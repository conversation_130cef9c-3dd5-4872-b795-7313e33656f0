<template>
    <main>
        <div class="list-title dfa mb20">我的发票</div>
        <div class="box dfa">
            <!--  温馨提示  -->
            <div class="remind p20">
                <div class="dfa mb20">
                    <img src="@/assets/images/userCenter/ico_notice.png" alt="">
                    <span>温馨提示</span>
                </div>
                <div class="line">1、订单完成90天内，可支持向第三方卖家申请开具增值税普通发票</div>
                <div class="line">2、申请开具发票内容默认为商品明细，如需开具商品大类可通知第三方卖家备注</div>
                <div class="line">3、发票申请提交后如有内容更新，可及时联系第三方卖家进行变更</div>
                <div class="line">4、暂不支持发票换开申请</div>
            </div>
            <!--  表单  -->
            <div class="apply-form">
                <el-form ref="form" :model="form" :rules="rules" label-position="top" :inline="false">
                    <div class="row">
                        <!--  发票类型  -->
                        <div class="col">
                            <el-form-item label="发票类型：" prop="invoiceType">
                                <el-select v-model="form.invoiceType" placeholder="请选择发票类型">
                                    <el-option v-for="item in typeOptions" :value="item.value" :label="item.label"
                                               :key="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <!--  发票内容  -->
                        <div class="col">
                            <el-form-item label="发票编号：" prop="invoiceNo">
                                <el-input v-model="form.invoiceNo" placeholder="请输入发票内容"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <!--  发票抬头类型  -->
                        <div class="col">
                            <el-form-item label="抬头类型：" prop="riseType">
                                <el-select v-model="form.riseType" placeholder="请选择发票抬头类型">
                                    <el-option v-for="item in headerOptions" :value="item.value"
                                               :label="item.label" :key="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <!--  发票抬头名称  -->
                        <div class="col">
                            <el-form-item label="单位名称：" prop="company">
                                <el-input v-model="form.company" placeholder="请输入发票抬头名称"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <!--  收票人姓名  -->
                        <div class="col">
                            <el-form-item label="收票人姓名：" prop="userName">
                                <el-input v-model="form.userName" placeholder="请输入收票人姓名"></el-input>
                            </el-form-item>
                        </div>
                        <!--  收票人手机号  -->
                        <div class="col">
                            <el-form-item label="收票人手机号：" prop="userPhone">
                                <el-input v-model="form.userPhone" placeholder="请输入收票人手机号"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <!--  收票人地区  -->
                        <div class="col">
                            <el-form-item class="address" label="收票人地区：" prop="userAddress">
                              <el-input v-model="form.userAddress" placeholder="请输入收票人地区"></el-input>
<!--                                <div class="df">-->
<!--                                    <el-select ref="selectLabel1" class="province" v-model="form.province"-->
<!--                                               placeholder="省份" @change="(code) => getSubDistrict(code, 1)">-->
<!--                                        <el-option v-for="item in addressOptions.province" :key="item.value"-->
<!--                                                   :label="item.districtName" :value="item.districtCode">-->
<!--                                        </el-option>-->
<!--                                    </el-select>-->
<!--                                    <el-select ref="selectLabel2" class="city" v-model="form.city" placeholder="市级"-->
<!--                                               @change="(code) => getSubDistrict(code, 2)">-->
<!--                                        <el-option v-for="item in addressOptions.city" :key="item.value"-->
<!--                                                   :label="item.districtName" :value="item.districtCode">-->
<!--                                        </el-option>-->
<!--                                    </el-select>-->
<!--                                    <el-select ref="selectLabel3" class="district" v-model="form.district"-->
<!--                                               placeholder="区、县">-->
<!--                                        <el-option v-for="item in addressOptions.district" :key="item.value"-->
<!--                                                   :label="item.districtName" :value="item.districtCode">-->
<!--                                        </el-option>-->
<!--                                    </el-select>-->
<!--                                </div>-->
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <!--  详细地址  -->
                        <div class="col">
                            <el-form-item label="详细地址：" prop="detailAddr">
                                <el-input type="textarea" :auto-resize="false" v-model="form.detailAddr"
                                          placeholder="请输入详细地址"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
            </div>
            <div class="btns df">
                <div @click="$router.go(-1)">返回</div>
            </div>
        </div>
    </main>
</template>

<script>
import { getCascaderOptions } from '@/api/platform/common/components'
import  { getDate } from '@/api/frontStage/invoice'
export default {
    name: 'apply',
    data () {
        return {
            // 校验规则
            rules: {
                type: { required: true, message: '请选择发票类型', trigger: 'blur' },
                invoiceNo: { required: true, message: '请输入发票内容', trigger: 'blur' },
                header: { required: true, message: '请选择发票抬头类型', trigger: 'blur' },
                headerName: { required: true, message: '请输入发票抬头名称', trigger: 'blur' },
                receiver: { required: true, message: '请输入收票人姓名', trigger: 'blur' },
                receiverTel: { required: true, min: 11, max: 11, message: '请输入正确的收票人手机号', trigger: 'blur' },
                receiverAddr: { required: true, message: '请选择收票人地区', trigger: 'blur' },
                detailAddr: { required: true, message: '请输入详细地址', trigger: 'blur' },
            },
            // 表单数据
            form: {
                invoiceType: 0,
                invoiceNo: '',
                riseType: '',
                company: '',
                dutyParagraph: '',
                registerAddress: '',
                registerPhone: '',
                bank: '',
                bankAccount: '',
                userName: '',
                userPhone: '',
                userAddress: '',
                detailAddr: '',
                province: '',
                city: '',
                district: '',
                orderSn: '',
                invoiceState: 0
            },
            typeOptions: [
                { label: '普通发票', value: 0 },
            ],
            headerOptions: [
                { label: '单位', value: 1 },
                { label: '个人', value: 2 },
            ],
            addressOptions: {
                province: [],
                city: [],
                district: []
            },

        }
    },
    created () {
        this.form.orderSn = this.$route.params.row
        this.getInvoice(this.form.orderItemId)
        this.getAddressPickerOptions()
    },
    methods: {
        // 获取发票
        getInvoice (orderItemId) {
            getDate({ orderItemId: orderItemId }).then(res=>{
                this.form = res
            })
        },
        // 提交申请
        handleSubmit () {
            this.form.province = this.$refs.selectLabel1.selectedLabel
            this.form.city = this.$refs.selectLabel2.selectedLabel
            this.form.district = this.$refs.selectLabel3.selectedLabel
            this.$refs['form'].validate(valid => {
                if (!valid) return
            })
        },
        // 获取地址选择器数据
        async getAddressPickerOptions () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.addressOptions.province = res
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            this.form.district = ''
            if (layer === 1) {
                this.form.city = ''
            }
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1) {
                    return this.addressOptions.city = res
                }
                this.addressOptions.district = res
            })
        },
    }
}
</script>

<style lang="scss" scoped>
main > div {
    border: 1px solid rgba(229, 229, 229, 1);
}

.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);
    position: relative;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.box {
    padding: 30px;
    flex-direction: column;

    .remind, .apply-form {
        width: 630px;
    }

    // 温馨提示
    .remind {
        height: 165px;
        margin-bottom: 30px;
        font-size: 12px;
        background: rgba(255, 253, 238, 0.5);

        .dfa {
            color: #333;

            img {
                width: 12px;
                height: 12px;
                margin-right: 5px;
            }
        }

        .line {
            margin-bottom: 15px;
            color: #999;
        }
    }

    // 表单
    .apply-form {

    }
}

.row {
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;

    .col {
        width: 300px;
    }
}

/deep/ .el-form {
    .el-form-item {
        margin-bottom: 0;
    }

    .el-form-item__label {
        padding-bottom: 0;
        color: #999;
    }

    .el-input, .el-input__inner {
        width: 300px;
        height: 35px;
    }

    .el-input__inner {
        border-radius: 0;
        border: 1px solid rgba(217, 217, 217, 1);
    }

    .address {
        .el-input, .el-input__inner {
            width: 80px;
        }

        .el-select {
            width: 80px !important;
            margin-right: 10px;

            &:last-of-type {
                .el-input, .el-input__inner {
                    width: 120px;
                }

                width: 120px !important;
                margin-right: 0;
            }
        }
    }

    .row:last-of-type {
        margin-bottom: 40px;

        .el-input, .el-input__inner {
            height: 60px;
        }

        .el-textarea__inner {
            height: 70px !important;
            padding: 11px 10px;
            border-radius: 0;
            resize: none;
        }
    }
}

.btns {
    width: 190px;

    div {
        width: 80px;
        height: 40px;
        font-size: 16px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
        user-select: none;

        &:first-of-type {
            margin-right: 30px;
            border: 1px solid #216EC6;
            color: #216EC6;
        }

        &:last-of-type {
            color: #fff;
            background-color: #216EC6;
        }
    }
}
</style>