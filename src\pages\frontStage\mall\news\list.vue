<template>
    <div class="root">
        <div class="box center">
            <div class="list-item df" v-for="item in newsList" :key="item.id" >
                <div class="date">{{item.gmtRelease}}</div>
                <div class="content">
                    <h3 @click="handleView(item)">{{item.title}}</h3>
                    <p>{{item.summary}}</p>
                </div>
            </div>
            <el-empty v-if="newsList.length === 0" :image="require('@/assets/images/ico_kong.png')" style="height: 400px"></el-empty>
            <!-- <div class="pagination dfa p20">
                <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page.sync="currentPage"
                    :page-size="pageSize"
                    layout="prev, pager, next"
                    :total="totalNum"
                    :pager-count="7">
                </el-pagination>
                <div class="page-btn">
                    共<span>{{totalNum / 9}}</span>页
                    到第<input v-model="destination" type="text">页
                    <button>确定</button>
                </div>
            </div> -->
            <pagination
                :currentPage.sync="currentPage"
                :destination="destination"
                :pageSize="pageSize"
                :total="totalNum"
                :totalPage="Math.ceil(totalNum / pageSize)"
                @currentChange="currentChange"
                @sizeChange="sizeChange">
        </pagination>
        </div>
    </div>
</template>
<script>
import pagination from '../../components/pagination.vue'
import { getWebInfo } from '@/api/frontStage/webInfo'
import { showLoading, hideLoading } from '@/utils/common'

export default {
    components: { pagination },
    data () {
        return {
            newsList: [],
            pageSize: 5,
            currPage: 1,
            totalNum: null,
            destination: '',
            mallType: 0,
            orderBy: 4,
            state: 1,
            programaKey: 'aboutUs',
        }
    },
    computed: {
        currentPage () {
            return 1
        }
    },
    created () {
        showLoading()
        let params = {
            limit: this.pageSize,
            page: this.currPage,
            mallType: this.mallType,
            programaKey: this.programaKey,
            orderBy: this.orderBy,
            state: this.state,
        }
        getWebInfo(params).then(res => {
            this.totalNum = res.totalCount
            this.newsList = res.list
            for(let i = 0 ; i < res.list.length ; i++) {
                this.newsList[i].gmtRelease = this.newsList[i].gmtRelease.slice(0, 10)
            }
            hideLoading()
        })
    },
    methods: {
        sizeChange () {
        },
        currentChange (page) {
            showLoading()
            this.currPage = page
            let params = {
                limit: this.pageSize,
                page: this.currPage,
                mallType: this.mallType,
                programaKey: this.programaKey,
                orderBy: this.orderBy,
                state: this.state,
            }
            getWebInfo(params).then(res => {
                this.pages = res
                this.newsList = res.list
                for(let i = 0 ; i < res.list.length ; i++) {
                    this.newsList[i].gmtRelease = this.newsList[i].gmtRelease.slice(0, 10)
                }
                hideLoading()
            })
        },
        handleView (item) {
            this.openWindowTab({ path: '/mFront/newsDetail', query: { id: item.contentId } })
        },
    },
}
</script>
<style scoped lang="scss">
@import "../../../../assets/css/news.scss";
</style>