<template>
    <div class="base-page">
        <div class="e-form">
            <BillTop @cancel="handleClose"></BillTop>
            <div class="tabs warningTabs" style="padding-top: 70px;">
                <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                    <el-tab-pane label="对账单详情" name="sheetDetail" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="物资清单" name="materialList" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <div id="tabs-content">
                        <!-- 基本信息 -->
                        <div id="columnInfoCon" class="con">
                            <div class="tabs-title" id="sheetDetail">对账单详情</div>
                            <div class="e-form" style="padding: 0 10px 10px;">
                                <el-form ref="formEdit" :model="formData" label-width="150px">
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="供应商名称：" prop="supplier">
                                                <span>{{ formData.supplier }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="状态：" prop="status">
                                                <span>{{ formData.status ? '启用' : '关闭' }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="开始时间：" prop="startTime">
                                                <span>{{ formData.startTime }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="结束时间：" prop="endTime">
                                                <span>{{ formData.endTime }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="对账时间：" prop="reconciliationTime">
                                                <span>{{ formData.reconciliationTime }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </div>
                        </div>
                        <!--计划清单-->
                        <div id="floorInfoCon" class="con">
                            <div class="tabs-title" id="materialList">物资清单</div>
                            <div class="e-table" style="overflow: auto;">
                                <el-table border :data="splitList" :header-cell-style="{ background: '#f7f7f7' }"
                                          v-loading="fileLoading">
                                    <el-table-column type="index" label="序号"></el-table-column>
                                    <el-table-column label="操作" width="90">
                                        <template v-slot="scope">
                                            <div class="action">
                                                <span @click="handleSplit()">拆单</span>
                                                <span style="color: #e9513e;" @click="handleDelSplit(scope.row.rowId)">删除</span>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        v-for="column in tableColumns"
                                        :key="column.prop"
                                        :prop="column.prop"
                                        :label="column.label"
                                        :width="column.width"
                                    >
                                        <template v-slot="scope">
                                            <template v-if="column.template.hasInput">
                                                <el-input
                                                    v-model="scope.row[column.prop]"
                                                    :type="column.template.type"
                                                    :min="column.template.type === 'number' ? column.template.min : null"
                                                    :placeholder="column.template.placeholder"
                                                />
                                            </template>
                                            <template v-else>
                                                {{ scope.row[column.prop] }}
                                            </template>
                                        </template>
                                    </el-table-column>

                                </el-table>
                            </div>
                            <!--<ComPagination
                                :total="pages.totalCount"
                                :limit="50"
                                :pageSize.sync="pages.pageSize"
                                :currentPage.sync="pages.currPage"
                                @currentChange="currentChange"
                                @sizeChange="sizeChange"
                            />-->
                        </div>
                    </div>
                </el-tabs>
            </div>
            <div class="buttons" v-if="!isview">
                <el-button type="primary" @click="updateSheet">确定</el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'

// import ComPagination from '@/components/pagination/pagination.vue'
import { getUuid, throttle } from '@/utils/common'

export default {
    components: {
        // ComPagination
    },
    data () {
        return {
            winEvent: {},
            tabsName: 'sheetDetail',
            planTableDtl: [
                {
                    gmtCreate: '2022-12-12 00:00:23',
                    gmtModified: '2022-12-12 00:00:23',
                    planDate: '2022-12-12 00:00:23',
                    billNo: '23232',
                    amount: '23232.003',
                    taxAmount: '43%',
                    orgName: '四川路桥',
                }
            ],
            pages: {
                currPage: 1,
                pageSize: 20,
                totalCount: 20
            },
            isLoading: false,
            fileLoading: false,
            splitList: [],
            keywords: '',
            // 表格数据
            formData: {},
            // 高级查询数据对象
            filterData: {
                materialClassName: '',
                spec: '',
                texture: '',
                materialName: '',
                tallAtotalAmount: '',
                lowAtotalAmount: '',
                lowquantity: '',
                tallquantity: '',
            },
            queryVisible: false,
            //选中数据
            selectedRows: [],
            formLabelWidth: '195px',
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            disabled: false, //是否禁止
            isview: false, //是否隐藏底部按钮
            formDate: {},
        }
    },
    created () {
        //接收上一个页面传递的参数
        let { row } = this.$route.params
        this.formData = row
        this.splitList = [{ rowId: getUuid(), ...row }]
        this.getTableDtlData()
    },
    async mounted () {
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'receive']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    computed: {
        // 结合computed实现拆单列表的监听，否则watch中无法拿到更改前列表的值
        newSplitList () {
            let list = JSON.parse(JSON.stringify(this.splitList))
            list.forEach(item => {
                for (let key in item) {
                    if (key === 'status') return
                    if (typeof item[key] === 'number') item[key] = String(item[key]) // 数字转换成字符串类型
                }
            })
            return list
        },
        tableColumns () {
            let columns = [
                { prop: 'sheetNum', label: '收料单号', width: '', template: {}, },
                { prop: 'materialName', label: '物资名称', width: '', template: {}, },
                { prop: 'model', label: '规格型号', width: '70', template: {}, },
                { prop: 'unit', label: '单位', width: '60', template: {}, },
                {
                    prop: 'count',
                    label: '本期数量',
                    width: '',
                    template: { hasInput: true, type: 'number', placeholder: '请输入数量' },
                },
                {
                    prop: 'onlinePrice',
                    label: '到货网价',
                    width: '',
                    template: { hasInput: true, type: 'number', placeholder: '请输入价格' },
                },
                {
                    prop: 'fixedPrice',
                    label: '固定费用',
                    width: '',
                    template: { hasInput: true, type: 'number', placeholder: '请输入价格' },
                },
                {
                    prop: 'priceWithTax',
                    label: '到场含税单价（元）',
                    width: '',
                    template: { hasInput: true, type: 'number', placeholder: '请输入价格' },
                },
                { prop: 'totalAmountWithTax', label: '含税金额（元）', width: '', template: {}, },
                {
                    prop: 'content',
                    label: '备注',
                    width: '140',
                    template: { hasInput: true, type: 'text', placeholder: '请输入备注' },
                },
                {
                    prop: 'settledAmount',
                    label: '已结算金额',
                    width: '120',
                    template: {}
                }
            ]
            if (this.formData.status !== 1) columns.splice(5, 2)
            return columns
        },
    },
    watch: {
        newSplitList: {
            handler (newVal, oldVal) {
                if(oldVal.length === 0 || newVal.length !== oldVal.length) return
                for(let i = 0; i < newVal.length; i++) {
                    let newObj = newVal[i]
                    let oldObj = oldVal[i]
                    if(JSON.stringify(newObj) === JSON.stringify(oldObj)) continue
                    for(let key in newObj) { // 循环已修改对象的属性
                        if(newObj[key] === oldObj[key]) continue
                        if(newObj[key] === '') return this.splitList[i][key] = 0 // 如果某个属性为空，则设为0

                        let { priceWithTax, onlinePrice, fixedPrice, count } = this.digitizeValue(newObj)
                        let isOpZero = onlinePrice === 0 && fixedPrice > 0 && priceWithTax > fixedPrice
                        let isFpZero = fixedPrice === 0 && onlinePrice > 0 && priceWithTax > onlinePrice

                        // toFixed用于对js计算产生的精度误差进行修正，this.spliceFloat用于将用户输入的超过2位小数的值进行向下舍去
                        switch (key) { // 根据发生改变的对象属性对关联的值重新赋值
                        // 修改本期数量
                        case 'count':
                            this.splitList[i].totalAmountWithTax = (count * priceWithTax).toFixed(2)
                            break
                            // 修改到场含税单价
                        case 'priceWithTax':
                            this.splitList[i].totalAmountWithTax = (count * priceWithTax).toFixed(2)
                            this.splitList.forEach(item => item.priceWithTax = this.spliceFloat(priceWithTax))
                            this.$nextTick(() => {
                                if(isOpZero) {
                                    this.$set(this.splitList[i], 'onlinePrice', (priceWithTax - fixedPrice).toFixed(2))
                                }
                                if(isFpZero) {
                                    this.$set(this.splitList[i], 'fixedPrice', (priceWithTax - onlinePrice).toFixed(2))
                                }
                            })
                            break
                            // 修改到货网价和固定费用
                        case 'onlinePrice':
                        case 'fixedPrice':
                            this.splitList.forEach(item => {
                                item.onlinePrice = this.spliceFloat(onlinePrice)
                                item.fixedPrice = this.spliceFloat(fixedPrice)
                                item.priceWithTax = (onlinePrice + fixedPrice).toFixed(2)
                            })
                            break
                        default:
                            break
                        }
                    }
                }
            },
            deep: true
        }
    },
    methods: {
        // 数字化参数
        digitizeValue (obj) {
            obj.count = parseInt(obj.count)
            obj.priceWithTax = parseFloat(obj.priceWithTax)
            obj.fixedPrice = parseFloat(obj.fixedPrice)
            obj.onlinePrice = parseFloat(obj.onlinePrice)
            return obj
        },
        // 数字转为小数位数为2的字符串
        spliceFloat (number) {
            const numberString = number.toString()
            const decimalIndex = numberString.indexOf('.')

            if (decimalIndex !== -1) {
                return numberString.substring(0, decimalIndex + 3)
            }

            return number.toString()
        },
        // 新增拆单
        handleSplit () {
            if(this.splitList.length === parseInt(this.formData.count)) return
            this.splitList.push({
                ...this.formData,
                rowId: getUuid(),
                count: 0,
                totalAmountWithTax: 0,
            })
        },
        // 删除本行的拆单
        handleDelSplit (id) {
            if(this.splitList.length === 1) return this.$message({ message: '不可删除最后一条拆单', type: 'error' })
            this.splitList.forEach((item, i, arr) => {
                if(item.rowId === id) arr.splice(i, 1)
            })
        },
        updateSheet () {
            let isPriceError = false
            let hasZeroCount = false
            let totalCount = this.splitList.reduce((total, item) => total + parseInt(item.count), 0) // 获取所有拆单已填的数量

            this.splitList.forEach(item => {
                let { onlinePrice, fixedPrice, priceWithTax, count } = this.digitizeValue(item)
                if(count === 0) hasZeroCount = true // 判断是否有拆单数量为0
                if(onlinePrice + fixedPrice !== priceWithTax) isPriceError = true // 判断网价和固定价格的和和含税单价是否相等
            })

            if(hasZeroCount) return this.$message({ message: '数量不能为0', type: 'error' })
            if(totalCount < parseInt(this.formData.count)) return this.$message({ message: '总数量不足', type: 'error' })
            if(totalCount > parseInt(this.formData.count)) return this.$message({ message: '总数量大于最大数量', type: 'error' })
            if(isPriceError) return this.$message({ message: '到货网价加固定费用之和与到场含税单价不符', type: 'error' })
            this.$message({ message: '校验成功', type: 'success' })
            // TODO 更新对账单
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 获取列表数据
        async getTableDtlData () {
            // TODO 获取对账单列表
        },
        currentChange (value) {
            this.pages.currPage = value
            this.getTableDtlData()
        },
        sizeChange (value) {
            this.pages.pageSize = value
            this.getTableDtlData()
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
    }
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    min-height: 760px;
    &::-webkit-scrollbar {
        width: 0;
    }
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
</style>

