<template>
    <main>
        <div v-loading="contractDetailLoading">
            <div class="list-title dfa mb20">计划信息</div>
            <div class="detailBox">
                <div class="row">
                    <div class="col">
                        <div class="name">
                            计划编号：
                        </div>
                        {{ resultDetail.billNo }}
                    </div>
                    <div class="col">
                        <div class="name">
                            计划含税总金额：
                        </div>
                        {{ resultDetail.totalAmount }}
                    </div>

                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            收货单位名称：
                        </div>
                        {{ resultDetail.orgName }}
                    </div>
                    <div class="col">
                        <div class="name">
                            供货单位名称：
                        </div>
                        {{ resultDetail.supplierName }}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            单据类型：
                        </div>
                        <el-tag v-if="resultDetail.billType == 1">浮动价格</el-tag>
                        <el-tag v-if="resultDetail.billType == 2">固定价格</el-tag>
                    </div>
                    <div class="col">
                        <div class="name">
                            创建日期：
                        </div>
                        {{ resultDetail.planDate }}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            备注：
                        </div>
                        {{ resultDetail.remarks }}
                    </div>
                </div>
            </div>
            <div class="list-title dfa mb20">计划明细<el-button :disabled="disableElement" v-show="true" type="primary" style="position: absolute; right: 40px" @click="submitOrderByPlan">生成订单</el-button></div>
            <el-table
                ref="msgTable"
                :data="resultDetail.details"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="productSn" label="商品编号" width=""/>
                <el-table-column prop="productName" label="商品名称名称" width=""/>
                <el-table-column prop="materialName" label="物资名称" width=""/>
                <el-table-column prop="spec" label="规格型号" width=""/>
                <el-table-column prop="classNamePath" label="分类路径" width=""/>
                <el-table-column prop="texture" label="材质" width=""/>
                <el-table-column prop="brandName" label="品牌名称" width=""/>
                <el-table-column prop="qty" label="数量" width=""/>
                <el-table-column prop="unit" label="计量单位" width=""/>
<!--                <el-table-column prop="isTwoUnit" label="是否有副级单位" width="">-->
<!--                    <template v-slot="scope">-->
<!--                        <el-tag type="success" v-if="scope.row.isTwoUnit === 1">是</el-tag>-->
<!--                        <el-tag type="info" v-if="scope.row.isTwoUnit === 0">否</el-tag>-->
<!--                    </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column prop="twoUnitNum" label="副级数量" width="">-->
<!--                    <template v-slot="scope">-->
<!--                        <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnitNum}}</span>-->
<!--                        <span v-else>/</span>-->
<!--                    </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column prop="twoUnit" label="副级单位" width="">-->
<!--                    <template v-slot="scope">-->
<!--                        <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnit}}</span>-->
<!--                        <span v-else>/</span>-->
<!--                    </template>-->
<!--                </el-table-column>-->
                <el-table-column prop="receivedQuantity" label="已下单数量" width="100"/>
                <el-table-column v-if="resultDetail.billType === 1" prop="netPrice" label="网价" width="100">
                </el-table-column>
                <el-table-column v-if="resultDetail.billType === 1" prop="fixationPrice" label="固定费" width="100">
                </el-table-column>
                <el-table-column v-if="resultDetail.billType === 2" prop="outFactoryPrice" label="出厂价" width="100">
                </el-table-column>
                <el-table-column v-if="resultDetail.billType === 2" prop="transportPrice" label="运杂费" width="100">
                </el-table-column>
                <el-table-column prop="synthesizePrice" label="含税单价" width="100"/>
                <el-table-column prop="synthesizeAmount" label="含税金额" width="100"/>
<!--                <el-table-column prop="consumeAmount" label="已消耗金额" width="100"/>-->
            </el-table>
            <el-dialog v-loading="submitMonthPlanOrderLoading" v-dialogDrag :visible.sync="showOrderDialog"
                       width="80%" :close-on-click-modal="false">
                <div class="list-title dfa mb20">
                    生成订单
                </div>
                <el-table
                    max-height="472px"
                    border
                    :data="addOrderForm.details"
                    style="min-height: 272px"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="productSn" label="物资编号" width=""/>
                    <el-table-column prop="productName" label="商品名称名称" width=""/>
                    <el-table-column prop="materialName" label="物资名称" width=""/>
                    <el-table-column prop="spec" label="规格型号" width=""/>
                    <el-table-column prop="classNamePath" label="分类路径" width=""/>
                    <el-table-column prop="texture" label="材质" width=""/>
                    <el-table-column prop="brandName" label="品牌名称" width=""/>
                    <el-table-column prop="qty" label="数量" width=""/>
                    <el-table-column prop="unit" label="计量单位" width=""/>
<!--                    <el-table-column prop="isTwoUnit" label="是否有副级单位" width="">-->
<!--                        <template v-slot="scope">-->
<!--                            <el-tag type="success" v-if="scope.row.isTwoUnit === 1">是</el-tag>-->
<!--                            <el-tag type="info" v-if="scope.row.isTwoUnit === 0">否</el-tag>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
<!--                    <el-table-column prop="twoUnitNum" label="副级数量" width="">-->
<!--                        <template v-slot="scope">-->
<!--                            <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnitNum}}</span>-->
<!--                            <span v-else>/</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
<!--                    <el-table-column prop="twoUnit" label="副级单位" width="">-->
<!--                        <template v-slot="scope">-->
<!--                            <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnit}}</span>-->
<!--                            <span v-else>/</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column prop="receivedQuantity" label="已下单数量" width="100"/>
                    <el-table-column v-if="resultDetail.billType === 1" prop="netPrice" label="网价" width="100">
                    </el-table-column>
                    <el-table-column v-if="resultDetail.billType === 1" prop="fixationPrice" label="固定费" width="100">
                    </el-table-column>
                    <el-table-column v-if="resultDetail.billType === 2" prop="outFactoryPrice" label="出厂价" width="100">
                    </el-table-column>
                    <el-table-column v-if="resultDetail.billType === 2" prop="transportPrice" label="运杂费" width="100">
                    </el-table-column>
                    <el-table-column prop="synthesizePrice" label="含税单价" width="100"/>
                    <el-table-column prop="synthesizeAmount" label="含税金额" width="100"/>
<!--                    <el-table-column prop="consumeAmount" label="已消耗金额" width="120"/>-->
                    <el-table-column prop="selectQty" label="选择数量" width="160">
                        <template slot-scope="scope">
                            <el-input-number v-if="scope.row.receivedQuantity != scope.row.qty" size="mini" v-model="scope.row.selectQty"
                                             :min="0" :precision="4" :step="0.1" :max="scope.row.qty - scope.row.receivedQuantity"   @change="changeSelectQtyM(scope.row)">
                            </el-input-number>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="select-box mt30">
                    <div>
                    </div>
                    <div>
                        <span>其他要求</span><input type="text" placeholder="填写要求"/>
                    </div>
                </div>
                <div class="addr-info center">
                    <div class="list-title" style="margin-left: -20px">
                        <span>收货信息</span>
                        <span style="position: absolute; right: 40px" @click="checkedAddressM">切换收货地址</span>
                    </div>
                    <div class="addr-content">
                        <p class="mb20">收货地址：{{receiver.addr}}（{{receiver.name}}收） </p>
                        <p>收货人：{{receiver.name}}（{{receiver.tel}}）</p>
                    </div>
                </div>
                <div class="dfa" style="margin-left: 80%">
                    <el-button type="primary" @click="submitOrderM">提交订单</el-button>
                    <el-button  @click="showOrderDialog = false">取消</el-button>
                </div>
            </el-dialog>
            <el-dialog v-dialogDrag class="front" title="" :visible.sync="addrDialogVisible">
                <div class="dialog-header">
                    <div class="dialog-header-top search_bar">
                        <div class="dialog-title search_bar">
                            <div></div>
                            <div>选择收货地址</div>
                        </div>
                        <div class="dialog-close" @click="addrDialogVisible = false"><img src="@/assets/images/close.png" alt="" /></div>
                    </div>
                    <div></div>
                </div>
                <el-table :data="addrList">
                    <el-table-column label="收货地址" label-width="560" prop="addr"></el-table-column>
                    <el-table-column label="联系人" label-width="152" prop="name"></el-table-column>
                    <el-table-column label="联系电话" label-width="110" prop="tel"></el-table-column>
                    <el-table-column label="操作" label-width="">
                        <template v-slot="scope">
                            <span @click="handleEditAddr(scope.row)" style="color: rgba(34, 111, 199, 1);cursor: pointer;">编辑</span>
                            <span @click="handleCurrentInventoryClick(scope.row)" style="color: rgba(34, 111, 199, 1);cursor: pointer;margin-left: 20px">选择</span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="add pointer" @click="createAddress">+ 新增</div>
            </el-dialog>
            <el-dialog class="front" :visible.sync="addDetailDialog" top="8vh">
                <div class="dialog-header">
                    <div class="dialog-header-top search_bar">
                        <div class="dialog-title search_bar">
                            <div></div>
                            <div>选择收货地址</div>
                        </div>
                        <div class="dialog-close" @click="addDetailDialog = false"><img src="@/assets/images/close.png" alt="" /></div>
                    </div>
                    <div></div>
                </div>
                <!-- 弹框内容 -->
                <div class="dialog-body center">
                    <el-form :model="userAddressForm" ref="addAddressRef" :rules="userAddressFormRules" label-width="80px" :inline="false" label-position="top">
                        <el-form-item label="收货人：" prop="receiverName">
                            <el-input v-model="userAddressForm.receiverName" placeholder="请输入收货人姓名"></el-input>
                        </el-form-item>
                        <el-form-item class="tel" label="手机号码：" prop="receiverMobile">
                            <span>+86</span><el-input v-model="userAddressForm.receiverMobile" placeholder="请输入手机号码"></el-input>
                        </el-form-item>
                        <el-form-item label="选择地址：" prop="detailAddress">
                            <el-cascader
                                size="large"
                                :options="addressData"
                                v-model="selectAddressOptions"
                                @change="handleAddressChange">
                            </el-cascader>
                        </el-form-item>
                        <el-form-item class="address" label="详细地址：" prop="detailAddress">
                            <el-input v-model="userAddressForm.detailAddress" placeholder="请输入详细收货地址"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer">
                <button class="butSub" @click="createAddressM">保存</button>
            </span>
                </div>
            </el-dialog>
        </div>
    </main>
</template>

<script>

import { mapState } from 'vuex'
import { submitSynthesizeTemporaryOrder, getSynthesizeTemporaryPlanDtial } from '@/api/plan/plan'
import { create, getDefaultAddress, getList } from '@/api/frontStage/shippingAddr'
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import { UserPermission } from '@/utils/permissions'

export default {
    filters: {
        dateStr (dateStr) {
            if(dateStr == null) {
                return ''
            }
            return dateStr.split('T')[0]
        }
    },
    name: 'detail',
    data () {
        return {
            submitMonthPlanOrderLoading: false,
            showOrderDialog: false,
            selectAddressOptions: [], // 地址选择
            addDetailDialog: false,
            contractDetailLoading: false,
            addrDialogVisible: false,
            addressData: regionData, // 地址数据
            resultDetail: {},
            userAddressForm: {},
            addrList: [],
            addOrderForm: {},
            userAddressFormRules: {
                receiverName: [
                    { required: true, message: '请输入收件人', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                receiverMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
            },
            receiver: {},
            // 数据权限
            userPermission: new UserPermission('物资下单权限'),
            disableElement: false
        }
    },
    created () {
        this.getPlantDetailM()
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        // 地址选择
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.userAddressForm.province = province
            this.userAddressForm.city = city
            this.userAddressForm.county = county
            this.userAddressForm.detailAddress = province + city + county
        },
        // 创建编辑地址统一接口
        createAddressM () {
            this.$refs.addAddressRef.validate(valid => {
                if (valid) {
                    create(this.userAddressForm).then(res => {
                        if(res.code == 200) {
                            this.$message({
                                message: res.message,
                                type: 'success'
                            })
                            this.getAddRess()
                            this.addDetailDialog = false
                        }
                    })
                }
            })
        },
        // 创建
        createAddress () {
            this.userAddressForm = {
                detailAddress: null,
            },
            this.selectAddressOptions = []
            this.addDetailDialog = true
        },
        // 地址表格点击
        handleCurrentInventoryClick (row) {
            this.receiver = row
            this.addrDialogVisible = false
        },
        // 编辑地址
        handleEditAddr (row) {
            let obj = {
                addressId: row.addressId,
                detailAddress: row.addr,
                receiverName: row.name,
                receiverMobile: row.tel,
            }
            this.userAddressForm = obj
            //地址选择器回显
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
            this.addDetailDialog = true
        },
        // 切换地址
        checkedAddressM () {
            this.getAddRess()
            this.addrDialogVisible = true
        },
        // 获取地址
        getAddRess () {
            // 获取收货地址
            this.addressListLoading = true
            getList({ page: 1, limit: 30 }).then(res => {
                if(!res.list[0]) return
                let address = []
                // 显示默认地址
                res.list.forEach(item => {
                    let obj = {
                        addressId: item.addressId,
                        checked: false,
                        addr: item.detailAddress,
                        name: item.receiverName,
                        tel: item.receiverMobile,
                        province: item.province,
                        city: item.city,
                        county: item.county,
                    }
                    address.push(obj)
                })
                this.addrList = address
            }).finally(() => {
                this.addressListLoading = false
            })
        },
        submitOrderM () {
            let arr = []
            for (let i = 0; i < this.addOrderForm.details.length; i++) {
                let t = this.addOrderForm.details[i]
                if(t.selectQty != null && t.selectQty > 0) {
                    arr.push(t)
                }
            }
            if(arr.length == 0) {
                return this.$message.error('未选择数量！')
            }
            if(this.receiver.tel == null) {
                return this.$message.error('请选择收货地址！')
            }
            this.clientPop('info', '您确认要提交订单吗？', async () => {
                this.submitMonthPlanOrderLoading = true
                let params = this.resultDetail
                params.receiverName = this.receiver.name
                params.receiverMobile = this.receiver.tel
                params.subOrderAddress = this.receiver.addr
                params.orderRemark = this.remarks
                params.payWay = 2
                params.details = arr
                submitSynthesizeTemporaryOrder(params).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.clientPop('suc', '提交成功！', () => {
                            this.getPlantDetailM()
                            this.showOrderDialog = false
                        })
                    }
                }).finally(() => {
                    this.submitMonthPlanOrderLoading = false
                })
            })
        },
        submitOrderByPlan () {
            let arr = []
            for (let i = 0; i < this.resultDetail.details.length; i++) {
                let t = this.resultDetail.details[i]
                if(t.qty != t.receivedQuantity) {
                    t.selectQty = 0
                    arr.push(t)
                }
            }
            if(arr.length == 0) {
                return this.$message.error('都已下单完毕！')
            }
            this.addOrderForm.details = arr
            this.getDefaultAddressM()
            this.showOrderDialog = true
        },
        // 获取默认地址
        getDefaultAddressM () {
            this.submitMonthPlanOrderLoading = true
            getDefaultAddress().then(res => {
                if(res != null) {
                    this.receiver.name = res.receiverName
                    this.receiver.tel = res.receiverMobile
                    this.receiver.addr = res.detailAddress
                }
            }).finally(() => {
                this.submitMonthPlanOrderLoading = false
            })
        },
        getPlantDetailM () {
            this.contractDetailLoading = true
            getSynthesizeTemporaryPlanDtial({ id: this.$route.query.billId }).then(res => {
                if (res.orgId) {
                    this.userPermission.isSameOrgByEnterpriseId(res.orgId) ? '' : this.disableElement = true
                }
                this.resultDetail = res
            }).finally(() => {
                this.contractDetailLoading = false
            })
        },
        // 去下单
        submitOrder (row) {
            this.$router.push({
                path: '/user/submitOrderByPlan',
                query: { billId: this.$route.query.billId, dtlId: row.dtlId }
            })
        }
    }
}
</script>

<style scoped lang="scss">
main {
    min-height: 894px;
    padding: 0 20px;
    border: 1px solid rgba(229, 229, 229, 1);
}

.list-title {
    padding: 0;
}

.detailBox {
    margin: 70px;

    .row {
        margin-bottom: 22px;
        color: rgba(51, 51, 51, 1);

        &, .col {
            display: flex;
        }

        .col {
            width: 50%;
        }

        .name {
            width: 180px;
            text-align: right;

            span {
                color: rgba(255, 95, 95, 1);
            }
        }
    }
}
.addr-info {
    //width: 1226px;
    width: 100%;
    background-color: #fff;
    .list-title {
        margin-top: 20px;
        padding-left: 33px;
        position: relative;
        & span:last-child {
            padding-left: 123px;
            color: rgba(33, 110, 198, 1);
            cursor: pointer;
        }
        &::before {
            position: absolute;
            left: 20px;
        }
    }
    .addr-content {
        padding-top: 20px;
        p {
            //margin-left: 20px;
            font-size: 14px;
            color: rgba(51, 51, 51, 1);
        }
    }
}
.select-box {
    &>div {margin-bottom: 30px;}
    span {margin-right: 20px;}
    /deep/ .el-select {
        width: 300px;
        height: 30px;
        .el-input__inner {
            height: 30px;
            padding: 0 10px;
            color: rgba(51, 51, 51, 1);
        }
    }
    input {
        width: 600px;
        height: 30px;
        padding: 0 10px;
        border: 1px solid rgba(217, 217, 217, 1);
        &::-webkit-input-placeholder {
            color: rgba(51, 51, 51, 1);
        }
    }
}
.list-title {
    padding: 0;
    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}
.add {
    width: 80px;
    height: 30px;
    margin: 18px 0 40px 0;
    line-height: 30px;
    text-align: center;
    color: rgba(33, 110, 198, 1);
    border: 1px solid rgba(33, 110, 198, 1);
}
/deep/ .el-dialog {
    .el-dialog__header {
        height: 10px;
        padding: 0px;
    }
    .el-dialog__body {
        overflow-y: hidden !important;
    }
}

</style>