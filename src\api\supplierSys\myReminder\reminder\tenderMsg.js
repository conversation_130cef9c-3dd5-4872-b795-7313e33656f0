import service from '@/utils/request'

const { httpPost, httpGet } = service

const getTenderMsgList = params => {
    return httpPost({
        url: '/tender/supplierSys/tenderMessage/listByEntity',
        params,
    })
}
// 消息提醒数量
const getMsgNumber = params => {
    return httpPost({
        url: '/tender/supplierSys/tenderMessage/getMsgNumber',
        params,
    })
}
//查询中标信息
const del = params => {
    return httpGet({
        url: '/tender/supplierSys/tenderMessage/delete',
        params,
    })
}
const getMsgById = params => {
    return httpGet({
        url: '/tender/supplierSys/tenderMessage/findById',
        params,
    })
}
// const aaa = params => {
//     return httpPost({
//         url: '/ss/api/app/Bid1/By-Bid-No',
//         params,
//         headers: {
//             AppCode: '00000002'
//         }
//     })
// }
const updateMsgByMsgId = params => {
    return httpGet({
        url: '/tender/supplierSys/tenderMessage/updateIsReadById',
        params,
    })
}

export {

    del,
    getTenderMsgList,
    getMsgById,
    updateMsgByMsgId,
    getMsgNumber

}