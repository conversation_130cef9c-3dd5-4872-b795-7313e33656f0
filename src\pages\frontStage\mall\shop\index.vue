<template>
    <div>
        <main>
            <div class="shopBanner mb20 dfa">
                <div class="contentBox center dfa">
                    <img class="logo" :src="shop.shopImg ? imgUrlPrefixAdd + shop.shopImg : require('@/assets/images/img/queshen5.png')" alt="">
                    <div class="shopDetail">
                        <div class="title mb10 textOverflow1" style="max-width: 400px;">{{shop.shopName}}</div>
                        <div class="info">地址：{{shop.detailedAddress || '未设置'}}</div>
                        <div class="info mb10">主营：{{shop.mainBusiness || '未设置'}}</div>
                        <div class="info mb10 score_service" @mouseenter="handleMouseEnter"  @mouseleave="handleMouseLeave">服务评分：<span class="score_val">{{shop.commentServiceScore}} </span><el-rate :disabled='true' v-model="shop.commentServiceScore" :max="5" :colors="['#FF0000','#FF0000','#FF0000']"
                            size="36px"  id="rateDetail" /></div>
                      <!--  <div class="tags dfa">
                            <div>认证企业</div>
                            <div>信用：132</div>
                            <div>入驻：712天</div>//../../../../assets/images/shop/banner.png
                        </div>-->
                    </div>
                    <img class="banner" :src="shop.adImg ? imgUrlPrefixAdd + shop.adImg : require('@/assets/images/shop/banner.png')" alt="">
                    <div :v-show="isShowRating" class="rating-detail-card" id="rating-detail-card">
                        <div class="info mb10">商品品质：{{shop.commentLevel}}分
                            <el-rate :disabled='true' v-model="shop.commentLevel" :max="5" :colors="['#FF0000','#FF0000','#FF0000']"/>
                        </div>
                        <div class="info mb10">保供能力：{{shop.commentSupply}}分
                            <el-rate :disabled='true' v-model="shop.commentSupply" :max="5" :colors="['#FF0000','#FF0000','#FF0000']"/>
                        </div>
                        <div class="info mb10">诚信履约：{{shop.commentIntegrity}}分
                            <el-rate :disabled='true' v-model="shop.commentIntegrity" :max="5" :colors="['#FF0000','#FF0000','#FF0000']"/>
                        </div>
                        <div class="info mb10">服务水平：{{shop.commentService}}分
                            <el-rate :disabled='true' v-model="shop.commentService" :max="5" :colors="['#FF0000','#FF0000','#FF0000']"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="shopSection center">
                <div class="tabs dfa">
                    <div :style="{ background: currentSection === 1 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 1">店铺简介</div>
                    <div v-if="currentTab == 3" :style="{ background: currentSection === 2 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 2">合作商家</div>
                    <div :style="{ background: currentSection === 0 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 0">商品</div>
                    <!--<div :style="{ background: currentSection === 0 ? 'rgba(33, 110, 198, 1)' : '' }"  @click="goProductPage( )">商品</div>-->
                </div>
            </div>
            <KeepAlive>
                <!-- 店铺商品 -->
                <products v-if="currentSection === 0" :page="productPage"></products>
            </KeepAlive>
            <KeepAlive>
                <!-- 店铺信息 -->
                <shopInfo v-if="currentSection === 1 && Object.keys(this.shopInfoVo).length>0"  :shopInfoVo="this.shopInfoVo" ></shopInfo>
            </KeepAlive>
            <KeepAlive>
                <!-- 合作商家 -->
                <suppliers v-if="currentSection === 2" :page="productPage"></suppliers>
            </KeepAlive>
        </main>
    </div>

</template>

<script>
import products from './products.vue'
import shopInfo from './shopInfo.vue'
import suppliers from './suppliers.vue'
import { getShopInfo } from '@/api/frontStage/shop'
export default {
    components: { products, shopInfo, suppliers },
    // components: {  shopInfo },
    data () {
        return {
            currentSection: 1,
            shopId: '',
            shopInfoVo: {},
            shop: {},
            productPage: 1,
            isShowRating: false,
            position: {},
            currentTab: null,
        }
    },
    created () {
        this.currentTab = this.$route.query.currentTab
        let bRoute = sessionStorage.getItem('bRoute')
        if(bRoute && bRoute.includes('productDetail')) {
            this.currentSection = 0
            this.productPage = Number(bRoute.split('-')[1])
            sessionStorage.removeItem('bRoute')
        }
        this.shopId = this.$route.query.shopId
        if (this.currentTab == 3) {
            this.shopId = '1878734518961074177'
        }
        let params = {
            shopId: this.shopId,
        }
        getShopInfo(params).then(res => {
            this.shopInfoVo = res
            this.shop = res.shop ? res.shop : { shopImg: '', shopType: '' }
        })
    },
    methods: {
        goProductPage () {
            this.openWindowTab({
                //path后面跟跳转的路由地址
                path: '/mFront/shopProductList',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shopProductList',
                query: {
                    shopId: this.$route.query.shopId
                }
            })
        },
        handleMouseEnter () {
            const element = document.getElementById('rateDetail')
            const rect = element.getBoundingClientRect()
            let left = element.offsetLeft
            let top = element.offsetTop
            let currentElement = element
            // 累加所有父元素的偏移量
            while (currentElement.offsetParent) {
                currentElement = currentElement.offsetParent
                left += currentElement.offsetLeft
                top += currentElement.offsetTop
            }
            // 获取元素的宽度和高度
            const width = element.offsetWidth
            // 计算右下角坐标
            const right = left + width
            const element1 = document.getElementById('rating-detail-card')
            const rect1 = element1.getBoundingClientRect()
            const height1 = rect1.height - rect.height
            element1.style.setProperty('--x', right + 'px')
            element1.style.setProperty('--y', (top - height1) + 'px')
            this.isShowRating = true
        },
        handleMouseLeave () {
            this.isShowRating = false
        },
    },
}
</script>

<style scoped lang="scss">
main {
    width: 100%;
    padding-bottom: 20px;
    background-color: #f5f5f5;
}
.contentBox {
    width: 1326px;
    min-width: 1326px;
    height: 100%;
}
.shopBanner {
    width: 100%;
    height: 170px;
    background-color: #e0e9f3;
    .logo {
        width: 118px;
        height: 120px;
        margin-right: 18px;
    }
    .shopDetail {
        width: 410px;
        .title {font-size: 20px;}
        .info {
            width: 302px;
            font-size: 16px;
            color: rgba(102, 102, 102, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .info:first-child {margin-bottom: 4px;}
        .tags div {
            padding: 2.5px 12px;
            font-size: 14px;
            border: 1px solid;
            border-radius: 2px;
            margin-right: 10px;
            &:first-child {
                color: rgba(255, 141, 26, 1);
                border-color: rgba(255, 141, 26, 1);
            }

            &:nth-child(2) {
                color: rgba(42, 130, 228, 1);
                border-color: rgba(42, 130, 228, 1);
            }
            &:last-child {
                color: rgba(153, 153, 153, 1);
                border-color: rgba(153, 153, 153, 1);
            }
        }
    }
    .banner {width: 780px;height: 150px;object-fit: cover;}
}
.shopSection {
    width: 1326px;
    color: #fff;
    .tabs {
        width: 100%;
        height: 52px;
        background-color: #333333;
        user-select: none;
        div {
            width: 147px;
            height: 52px;
            line-height: 52px;
            text-align: center;
            font-size: 20px;
            font-weight: 400;
            cursor: pointer;
        }
    }
}
.score_service{
    font-family: "Arial Normal", "Arial", sans-serif;
    font-size: 14px !important;
    text-align: left;
}
.score_val{
    color: #FF0000;
}
.el-rate{
    width: 100px;
    display: inline;
    font-size: 28px;
}
:deep(.el-rate__item .el-rate__icon) {
  font-size: 28px;
}
.rating-detail-card {
  position: absolute;
  left: var(--x);
  top: var(--y);
  width: 310px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  padding: 16px;
  z-index: 100;
  transition: opacity 0.2s ease;
  opacity: 0;
  &[v-show] {
    opacity: 1;
  }
}
</style>