import service from '@/utils/request'

const { httpPost, httpGet } = service

const treeByName = params => {
    return httpGet({
        url: '/materialMall/productZone/listByClassName',
        params
    })
}
const outputZoneExcel = params => {
    return httpPost({
        url: '/materialMall/productZone/outputZoneExcel',
        params,
        responseType: 'blob'
    })
}
const platforUploadMaterialExcelFile = params => {
    return httpPost({
        url: '/materialMall/productZone/platforUploadMaterialExcelFile',
        params,
    })
}
const edit = params => {
    return httpPost({
        url: '/materialMall/productZone/update',
        params
    })
}

const editBatch = params => {
    return httpPost({
        url: '/materialMall/productZone/update/sate',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/productZone/create',
        params
    })
}

const del = params => {
    return httpPost({
        url: '/materialMall/productZone/deleteBatch',
        params
    })
}

export {
    edit,
    create,
    del,
    treeByName,
    platforUploadMaterialExcelFile,
    editBatch,
    outputZoneExcel
}