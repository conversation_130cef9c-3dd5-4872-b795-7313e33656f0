$color-primary: #d3e9f4;


.el-menu{border: none;}
.el-tree-node{
  &>.el-tree-node__children{overflow: visible;}
}
.el-table{
  .sort-caret{
    &.ascending{top: 10px;}
    &.descending{display: none;}
  }
}
.el-form{
  &.inline_form{
    .el-form-item{margin: 0 30px 10px 0;}
  }
}
.el-input-group--append{
  .el-input__inner{height: 30px; line-height: 30px;}
}
.el-input-group__append{
  .el-button{padding: 0 10px; height: 30px; line-height: 30px;}
}
.el-button{padding: 0 20px; height: 30px; line-height: 30px;
  span{display: inline-block; vertical-align: text-bottom;}
}
.el-button-group{
  &>.el-button{padding: 0 20px; height: 30px; line-height: 30px;}
}
.el-menu--horizontal{
  .el-menu{
    .el-menu-item{height: 30px; line-height: 30px; background: #30417d; text-align: center; color: #fff; border-bottom: 1px #1d5abc solid;
      &:not(.is-disabled){
        &:hover{color: #3287e1; background: #1c2c64;}
      }
      &:last-child{border: none;}
      &.is-active{color: #fff;}
    }
  }
  &>.el-submenu {
    .el-submenu__title{
      &:focus{background: none;}
    }
  }
  .el-menu--popup{padding: 0 0 5px; min-width: 126px;}
  .el-menu--popup-bottom-start{margin: 0;}
  &.index_submenu{
    .el-menu{
      .el-menu-item{background: #306e7e; color: $color-primary; border: none;
        &:not(.is-disabled){
          &:hover{background: #245467; color: #fefefe;}
        }
      }
    }
    .el-menu--popup{margin: 0 0 0 15px; padding: 0; min-width: 100px;}
  }
}

.el-table{
  th{padding: 0;}
  td{padding: 5px 0;}
}
.el-table-column--selection{
//   .cell{padding: 0 10px;}
}

.search_bar{display: flex; justify-content: space-between; align-items: center;
  .el-tabs--top .el-tabs__item.is-top:last-child,
  .el-tabs--top .el-tabs__item.is-bottom:last-child,
  .el-tabs--bottom .el-tabs__item.is-top:last-child,
  .el-tabs--bottom .el-tabs__item.is-bottom:last-child,
  .el-tabs--top .el-tabs__item.is-top:nth-child(2),
  .el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
  .el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2){
    padding: inherit;
  }
  .el-tabs__item{color: $color-primary;
    .tab_title{display: block; padding: 0 15px;}
    &:hover{color: #409EFF;}
  }
}
.right_bar{display: flex; justify-content: flex-end; align-items: center;
  .el-radio-group{margin: 0 10px 0 0;}
  .el-radio{margin: 0 10px 0 0;}
  .el-radio__label{color: $color-primary;}
}

.todo{
  .ls{
    li{float: left; display: flex; flex-direction: column; margin: 0 10px 10px; width: calc(33% - 20px); height: 200px; border: 1px #51B6C2 solid; color: $color-primary;
      .ls_title{display: flex; justify-content: space-between; align-items: center; padding: 0 10px; height: 40px; border-bottom: 1px #51B6C2 solid;
        .txt{color: $color-primary;}
        .status{display: block; padding: 5px 15px; color: #fff; background: #FF7000; border-radius: 5px;}
      }
      .desc{flex: 1; padding: 10px; border-bottom: 1px #51B6C2 solid;}
      .ls_bottom{display: flex; justify-content: flex-end; align-items: center; height: 40px;
        .date{color: #999;}
        .btn_box{margin: 0 10px;}
      }
    }
  }
  .table{padding: 0 10px; background: none; color: $color-primary;
    &:before{content: none;}
    thead{color: $color-primary;}
    tr{background: #123964;
      &.el-table__row--striped{background: #132d51;
        td{background: none;}
      }
    }
    td{font-size: 14px; border: none;}
    .cell{height: 32px; line-height: 32px;}
    .el-table__header{
      tr, th{background: none;}
      th{font-size: 16px; border: none;}
    }
    .el-table__body{
      tr{
        &:hover{
          &>td{background: #0a315b;}
        }
      }
    }
    .el-table__fixed::before, .el-table__fixed-right::before,
    .el-table__body tr.hover-row > td{background: none;}
  }
}

.login{display: flex; flex-direction: column; height: 100%;
  .top{flex: 1; display: flex; justify-content: center; align-items: center;
    .logo{margin: 0 20px 0 0;}
    .txt{color: #182a52;
      .cn{height: 45px; overflow: hidden; font-size: 30px; text-align: justify;
        span{display:inline-block; width:100%;}
      }
      .en{font-size: 20px; color: #525f7d;}
    }
  }
  .bottom{flex: 1; background: url(../images/bg_03.png) center center no-repeat; background-size: cover;}
  .box{display: flex; flex-direction: column; justify-content: center; align-items: center; height: 500px; background: url(../images/bg_01.png) center no-repeat; background-size: cover;
    .form{padding:40px 70px 0 520px; width: 212px; height: 360px; background: url(../images/bg_02.png) center no-repeat; background-size: contain;
      .el-input__inner{padding: 0; font-size: 20px; border: none; border-bottom: 2px #DBDBDB solid;}
      .title{margin: 0 0 30px;
        .el-form-item__label{font-size: 22px;}
      }
      .txt{
        .el-form-item__label{display: block; width: 100%; text-align: center; font-size: 12px;}
      }
      .btn{margin: 50px 0 5px;
        .el-button{width: 100%; font-size: 16px; height: 40px; line-height: 40px; background: #1987E0; box-shadow: 0 2px 0 #D4D2D3;}
      }
    }
  }
}
.container{padding: 70px 0 0; min-height: calc(100% - 70px);
  .top{position: fixed; left: 0; top: 0; z-index: 10; width: 100%; height: 70px; background: url(../images/bg_2.png) center 0 no-repeat; background-size: cover;
    .posi_rela{position: relative; height: 100%; display: flex; flex-direction: column; justify-content: flex-end;}
    .up{position: absolute; left: 0; top: 0; width: 100%; z-index: 3; display: flex; justify-content: space-between;
      .center_txt{flex: 1; align-self: center; font-size: 14px; color: #fff;}
    }
    .logo{flex: 1; display: flex; align-items: center; margin: 2px 0 3px 10px;
      .img{margin: 0 5px 0 0;}
      .txt{color: #e9ebf1;
        .cn{height: 22px; overflow: hidden; font-size: 15px; text-align: justify;
          span{display:inline-block; width:100%;}
        }
        .en{font-size: 12px; text-transform : uppercase}
      }
    }
    .right{display: flex; padding: 0 22px 0 0; height: 35px;
      .search{
        .el-form-item{margin: 0;
          .el-form-item__content{line-height: 35px;}
          .el-input-group{display: flex; border: 1px #e9e3e5 solid; border-radius: 5px;}
          .el-input__inner, .el-input-group__append{background: none; border: none;}
          .el-input__inner{padding: 0 5px 0 15px; width: 240px; height: 35px; line-height: 35px; font-size: 12px; color: #fff;}
          .el-input-group__append{display: flex; padding: 0; width: auto;}
          .el-button{margin: 0; padding: 5px 8px; color: #fff;}
        }
      }
      .icon_box{display: flex; justify-content: center; align-items: center;
        .user_name{font-size: 14px; color: #fff;
          span{margin: 0 10px;}
        }
        .icon{margin: 0 0 0 15px; width: 20px; cursor: pointer; color: #fff; font-size: 22px;}
      }
    }
    .down{z-index: 2; display: flex; justify-content: space-between; align-items: flex-end; min-width: 1200px;
      .el-menu{margin: 0 0 0 7px; background: none; border: none; box-sizing: content-box;
        &>.el-menu-item{padding: 40px 5px 0; width: 112px; height: 30px; line-height: 30px; text-align: center; color: #fff;
          border: none; box-sizing: content-box; background: url(../images/bg_0.png) center bottom no-repeat; background-size: contain;
          &.is-active{background-image: url(../images/bg_1.png);}
        }
      }
      .el-menu--horizontal{
        .el-menu-item{
          &:not(.is-disabled){
            &.is-active{
              &:hover{background-image: url(../images/bg_1.png);}
            }
          }
        }
        &>.el-submenu{padding: 40px 5px 0; height: 30px; line-height: 30px;
          background: url(../images/bg_0.png) center bottom no-repeat; background-size: contain;
          .el-submenu__title{height: 30px; line-height: 30px; color: #fff; border: none;
            i{color: #fff;}
            &:hover{background: none;}
          }
          &.is-active{background-image: url(../images/bg_1.png);}
        }
      }
      .user_name{margin: 0 15px 10px 0; color: #fff;}
    }
  }
  .return_top{position: fixed; right: 10px; bottom: 10px; z-index: 4; width: 35px; height: 35px; line-height: 35px; font-size: 0; text-align: center; color: #fff; background: url(../images/ic_top_off.png) center center no-repeat; cursor: pointer;
    &:hover{background-image: url(../images/ic_top_on.png);}
  }
}
.main{width: 100%; overflow: hidden;
  .loca_bar{padding: 0 0 0 15px; height: 25px; line-height: 25px; font-size: 14px; color: #fff; background: #1b7cea;
    i{font-weight: bold; font-style: normal;}
  }
  .tree_search{margin: 5px 0;}
  .left_menu{width: 100%; overflow: auto;
    .el-menu-item-group__title{font-size: 14px;}
    .el-menu-item, .el-submenu__title {height: 35px; line-height: 35px;}
  }
  .el-tree-node__expand-icon{position: relative;
    &:not(.is-leaf) {margin: 0 5px 0 0; padding: 0 15px 0 0;
      &:before{display: block; margin: 0 2px 0 0; width: 18px; height: 18px; line-height: 18px; font-size: 18px; transform: rotate(0deg); transition: transform 0.3s ease-in-out;}
      &:after{position: absolute; right: 0; top: 0; z-index: 3; content: ""; width: 18px; height: 18px; background: url(../images/ic_folder.png) center center no-repeat;}
      &.expanded{transform: none;
        &:before{transform: rotate(90deg);}
      }
    }
  }
  .drag_bar{width: 10px; cursor: ew-resize; background: #dcdfe7 url(../images/drag_bg.png) center center no-repeat;}
  .table_search{display: flex; justify-content: space-between; padding: 5px 10px; font-size: 12px; background: #f3f4f5;
    .table_radio{margin: 0 20px 0 0;
      .el-radio{line-height: 30px;}
    }
  }
  .el-main{padding: 0 10px 10px; width: 100%; background: #f3f4f5;}
  .el-footer{padding: 5px 0; background: #f3f4f5;}
  .table_box{padding: 0 10px 10px; border: 1px #dcdfe7 solid; border-radius: 3px; background: #fff;
    th{
      &>.cell{line-height: 34px; font-size: 15px; color: #575757;}
    }
  }
}
.index{
  .top{display: flex; flex-direction: column; justify-content: space-between; height: 78px; background: url(../images/style_science_tech/top_bg.png) no-repeat; background-size: 100% 100%;
    .up{display: flex; justify-content: space-between; align-items: center; width: 100%;}
    .logo{flex: 1; display: flex; align-items: center; margin: 2px 0 3px 10px;
      .img{margin: 0 5px 0 0;}
      .txt{color: #e9ebf1;
        .cn{height: 22px; overflow: hidden; font-size: 15px; text-align: justify;
          span{display:inline-block; width:100%;}
        }
        .en{font-size: 12px; text-transform : uppercase}
      }
    }
    .right{margin: 10px 0 0; padding: 0 22px 0 0;
      .icon_box{display: flex; justify-content: center; align-items: center; height: 25px;
        .icon{margin: 0 0 0 15px; width: 20px; cursor: pointer; color: #fff; font-size: 22px;}
      }
    }
    .down{z-index: 2; display: flex; justify-content: space-between; align-items: flex-end; min-width: 1200px;
      .el-menu{margin: 0 0 0 7px; background: none; border: none; box-sizing: content-box;
        &>.el-menu-item{padding: 0 30px 0 0; height: 32px; line-height: 32px; text-align: center; color: #fff; border: none; box-sizing: content-box; background: url(../images/style_science_tech/V1_04.png) right bottom no-repeat;
          .txt{display: block; padding: 0 0 0 30px; min-width: 50px; height: 32px; line-height: 32px; color: #fff; border: none; background: url(../images/style_science_tech/V1_03.png) 0 bottom no-repeat;
            &:hover{color: #d1d8e5;}
          }
          &.is-active{background-image: url(../images/style_science_tech/nav_sel_bg_r.png);
            .txt{background-image: url(../images/style_science_tech/nav_sel_bg_l.png);}
          }
        }
      }
      .el-menu--horizontal{
        .el-menu-item{
          &:not(.is-disabled){
            &.is-active{background-image: url(../images/style_science_tech/nav_sel_bg_r.png);
              .txt{background-image: url(../images/style_science_tech/nav_sel_bg_l.png);}
            }
          }
        }
        &>.el-submenu{padding: 0 30px 0 0; height: 32px; background: url(../images/style_science_tech/V1_04.png) right bottom no-repeat;
          .el-submenu__title{padding: 0 0 0 30px; height: 32px; line-height: 32px; color: #fff; border: none; background: url(../images/style_science_tech/V1_03.png) 0 bottom no-repeat;
            .el-submenu__icon-arrow{display: none;}
            &:hover{color: #d1d8e5;}
          }
          &.is-active{background-image: url(../images/style_science_tech/nav_sel_bg_r.png);
            .el-submenu__title{background-image: url(../images/style_science_tech/nav_sel_bg_l.png);}
          }
        }
      }
      .word{display: flex; align-items: center; margin: 0 15px 0 0; height: 30px; line-height: 30px; color: #fff;
        .split_line{margin: 0 10px; width: 1px; height: 18px; background: #b8b8bf;}
      }
    }
  }
  .con{display: flex; flex-direction: column; position: relative; background: url(../images/style_science_tech/bg_0.png); background-size: 100% 100%;
    .t_l, .t_r, .b_l, .b_r{position: absolute; z-index: 7; width: 64px; height: 64px;}
    .t_l{top: 5px; left: 5px; background: url(../images/style_science_tech/img_t_l.png) no-repeat;}
    .t_r{top: 5px; right: 5px; background: url(../images/style_science_tech/img_t_r.png) no-repeat;}
    .b_l{bottom: 5px; left: 5px; background: url(../images/style_science_tech/img_b_l.png) no-repeat;}
    .b_r{bottom: 5px; right: 5px; background: url(../images/style_science_tech/img_b_r.png) no-repeat;}
    .bg_t_l{position: absolute; z-index: 2; top: 0; left: 0; width: 100%; height: 100%; background: url(../images/style_science_tech/img_bg_t_l.png) 0 0 no-repeat;}
    .bg_t_r{position: absolute; z-index: 3; top: 0; right: 0; width: 100%; height: 100%; background: url(../images/style_science_tech/img_bg_t_r.png) right 0 no-repeat;}
    .bg_b{position: absolute; z-index: 4; top: 0; right: 0; width: 100%; height: 100%; background: url(../images/style_science_tech/img_star_b_l.png) 0 bottom no-repeat; background-size: contain;}
    .start{position: absolute; z-index: 5; top: -10px; right: 110px; width: 126px; height: 61px; background: url(../images/style_science_tech/img_star.png) no-repeat;}
    .box{position: relative; z-index: 6; margin: 21px; box-sizing: border-box; border: 1px #2f50a8 solid; box-shadow: -1px -1px 29px #284d8f inset; background: rgba(0, 0, 0, .1);
      .bar{display: flex; margin: 20px; height: 92px;
        .bar1{position: relative; flex: 1; display: flex; justify-content: space-between; align-items: center; margin: 0 20px 0 0; padding: 0 20px;
          &:before{content: ''; position: absolute; top: 0; left: 0; width: 39px; height: 100%; z-index: 2; background: url(../images/style_science_tech/bg_bar_l.png) 0 bottom no-repeat;}
          &:after{content: ''; position: absolute; top: 0; left: 39px; right: 0; height: 100%; z-index: 1; background: url(../images/style_science_tech/bg_bar_r.png) right bottom no-repeat;}
          .icon{position: relative; z-index: 5; width: 77px; height: 92px; background-position: center center; background-repeat: no-repeat;}
          .icon1{background-image: url(../images/style_science_tech/ic_0.png);}
          .icon2{background-image: url(../images/style_science_tech/ic_1.png);}
          .icon3{background-image: url(../images/style_science_tech/ic_2.png);}
          .icon4{background-image: url(../images/style_science_tech/ic_3.png);}
          .right{position: relative; z-index: 5; display: flex; flex-direction: column; justify-content: center; align-items: flex-end;
            .num{margin: 0 0 10px; font-size: 30px; font-weight: bold;}
            .num1{color: #05baff;}
            .num2{color: #8e95ff;}
            .num3{color: #7dfce2;}
            .num4{color: #dfdfdf;}
            .name{font-size: 16px; font-weight: bold; color: $color-primary;}
          }
          &:last-child{margin: 0;}
        }
      }
      .area{position: relative; margin: 20px; padding: 40px 20px 20px; min-height: 230px;
        &:before{content: ''; position: absolute; top: 0; left: 0; width: 21px; height: 130px; z-index: 4; background: url(../images/style_science_tech/bg_cahrt_l_01.png) 0 0 no-repeat;}
        &:after{content: ''; position: absolute; top: 0; left: 21px; right: 0; height: 130px; z-index: 3; background: url(../images/style_science_tech/bg_cahrt_r_01.png) right 0 no-repeat;}
        .bg1{position: absolute; top: 130px; bottom: 0; left: 0; width: 21px; z-index: 2; background: url(../images/style_science_tech/bg_cahrt_l_02.png) 0 bottom no-repeat;}
        .bg2{position: absolute; top: 130px; bottom: 0; left: 21px; right: 0; z-index: 2; background: url(../images/style_science_tech/bg_cahrt_r_02.png) right bottom no-repeat;}
        .title{position: absolute; top: 0; left: 50%; z-index: 5; padding: 5px 40px 0; border-top: 5px #61a3fc solid; font-weight: bold; font-size: 16px; color: #fff; transform: translateX(-50%);}
      }
      .area2{position: relative; margin: 20px; padding: 40px 20px 20px; min-height: 230px;
        &:before{content: ''; position: absolute; top: 0; left: 0; width: 21px; height: 200px; z-index: 4; background: url(../images/style_science_tech/bg_cahrt2_l_01.png) 0 0 no-repeat;}
        &:after{content: ''; position: absolute; top: 0; left: 21px; right: 0; height: 200px; z-index: 3; background: url(../images/style_science_tech/bg_cahrt2_r_01.png) right 0 no-repeat;}
        .bg1{position: absolute; top: 200px; bottom: 0; left: 0; width: 21px; z-index: 2; background: url(../images/style_science_tech/bg_cahrt2_l_02.png) 0 bottom no-repeat;}
        .bg2{position: absolute; top: 200px; bottom: 0; left: 21px; right: 0; z-index: 2; background: url(../images/style_science_tech/bg_cahrt2_r_02.png) right bottom no-repeat;}
        .title{position: absolute; top: 0; left: 50%; z-index: 5; padding: 5px 40px 0; border-top: 5px #61a3fc solid; font-weight: bold; font-size: 16px; color: #fff; transform: translateX(-50%);}
        .area2_box{display: flex; justify-content: space-between; position: relative; z-index: 10;
          .table{padding: 0 10px; border: 1px #bae7fb dashed;
            background: none; color: $color-primary;
            &:before{content: none;}
            thead{color: $color-primary;}
            tr{background: #123964;
              &.el-table__row--striped{background: #132d51;
                td{background: none;}
              }
            }
            td{font-size: 14px; border: none;}
            .cell{height: 32px; line-height: 32px;}
            .el-table__header{
              tr, th{background: none;}
              th{font-size: 16px; border: none;}
            }
            .el-table__body{
              tr{
                &:hover{
                  &>td{background: #0a315b;}
                }
              }
            }
          }
        }
      }
    }
  }
  .el-pagination{margin: 10px 10px 0; color: $color-primary;
    &.is-background {
      .btn-prev, .btn-next, .el-pager li, .el-input__inner{background: none; color: #add6e7;}
      .el-input__inner{border: none;}
      .el-pagination__total, .el-pagination__jump{color: #add6e7;}
      .btn-prev:disabled, .btn-next:disabled{color: #606266;}
    }
  }
  .el-button--primary{background-color: #2a5dab; border-color: #2a5dab;}
}