<template>
    <main class="userCenter">
        <div class="title">退货列表</div>
        <div class="content p20">
            <!-- 顶部选项和搜索框 -->
            <div class="tabs dfb">
                <div class="tab df">
                    <div :class="activeTab == 1 ? 'active' : ''" @click="activeTab = 1">退货记录</div>
                    <!--                    <div :class="activeTab == 2 ? 'active' : ''" @click="activeTab = 2">搜索结果</div>-->
                </div>
                <div class="search df">
                    <div class="box dfa">
                        <img src="@/assets/images/ico_search.png" alt="">
                        <input v-model="keyword" type="text" placeholder="商品名称/订单号">
                    </div>
                    <button @click="onSearch">搜索</button>
                </div>
            </div>
            <!--  列表  -->
            <div class="list">
                <div class="orderItem" v-for="(item, i) in list" :key="i">
                    <div class="orderNum">
                        <span style="margin-left: 4px">订单编号：{{ item.orderSn }}</span>
                        <span style="margin-left: 34px">订单退货编号：{{ item.orderReturnNo }}</span>
                    </div>
                    <div class="products">
                        <div class="titleBar">
                            <div>商品信息</div>
                            <div>购买数量</div>
                            <div>退货数量</div>
                            <div>品牌</div>
                            <div>下单时间</div>
                            <div>订单项状态</div>
                        </div>
                        <div class="inner">
                            <div class="item dfa" v-for="(item1, index) in item.orderReturnItems" :key="index">
                                <img @click="$router.push({ path: '/mFront/productDetail', query: { productId: item1.productId } })"
                                    :src="item1.productImg ? imgUrlPrefixAdd + item1.productImg : require('@/assets/images/img/queshen5.png')"
                                    alt="">
                                <div class="infoBox"
                                    @click="$router.push({ path: '/mFront/productDetail', query: { productId: item1.productId } })">
                                    <div class="textOverflow2">{{ item1.productName
                                    }}</div>
                                    <div> 规格：{{ item1.skuName }}</div>
                                </div>
                                <div class="brand">x &nbsp;{{ item1.buyCounts }}</div>
                                <div class="brand">x &nbsp;{{ item1.count }}</div>
                                <div class="brand">{{ item1.brandName }}</div>
                                <div class="time">
                                    <div>{{ item.gmtCreate.split(' ')[0] }}</div>
                                    <div>{{ item.gmtCreate.split(' ')[1] }}</div>
                                </div>
                                <div class="time">
                                  <div v-if="item1.state==1">已申请退货</div>
                                  <div v-else-if="item1.state==2">退货中</div>
                                  <div v-else-if="item1.state==3">退货成功</div>
                                  <div v-else-if="item1.state==4">退货失败</div>
                                  <div class="pointer" style="color: #226fc7;" @click="handleViewDetail(item1.orderReturnId)">订单详情</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <pagination :currentPage="page.currPage" :pageSize="page.pageSize" :destination="page.destination"
                :total="page.totalCount" :totalPage="page.totalPage" @currentChange="currentChange"></pagination>
        </div>
    </main>
</template>

<script>
import pagination from '@/pages/frontStage/components/pagination'
import { getOrderReturnPageList } from '@/api/frontStage/orderRetuen'
export default {
    components: { pagination },
    data () {
        return {
            activeTab: 0,
            keyword: '',
            page: {
                totalCount: 0,
                currPage: 1,
                pageSize: 3,
                totalPage: 0,
                destination: 2,
            },
            statesOptions: [
                { value: 1, label: '已申请' },
                { value: 2, label: '退货中' },
                { value: 3, label: '退货成功' },
                { value: 4, label: '退货失败' },
            ],
            list: [
            ]
        }
    },
    created () {
        this.getPageList()
    },
    mounted () {
    },
    methods: {
        handleViewDetail (orderReturnId) {
            this.$router.push(
                {
                    path: '/user/refund/detail',
                    name: 'refundDetail',
                    params: {
                        orderReturnId: orderReturnId
                    }
                })
        },

        getPageList () {
            let params = {
                page: this.page.currPage,
                limit: this.page.pageSize,
                state: this.state,
            }
            if (this.keyword != null) {
                params.keywords = this.keyword
            }
            getOrderReturnPageList(params).then(res => {
                this.list = res.list
                this.page = res
                this.page.totalPage = Math.ceil(this.page.totalCount / this.page.pageSize)
            })
        },

        // 搜索
        onSearch () {
            this.getPageList()
        },
        // 退货

        currentChange (page) {
            this.page.currPage = page
            this.getPageList()
        },
    },
}
</script>

<style scoped lang="scss">
main {
    border: 1px solid rgba(230, 230, 230, 1);
}

.content {
    min-height: 824px;

    .tabs {
        margin-bottom: 30px;

        .tab {
            font-size: 16px;
            color: rgba(102, 102, 102, 1);

            .active {
                color: rgba(0, 0, 0, 1);

                &::after {
                    content: '';
                    display: block;
                    width: 100%;
                    height: 2px;
                    margin-top: 4px;
                    background-color: rgba(34, 111, 199, 1);
                }
            }
        }

        .search {
            .box {
                width: 268px;
                height: 26px;
                border: 1px solid rgba(229, 229, 229, 1);
                border-right: 0;

                img {
                    width: 16px;
                    height: 16px;
                    margin: 0 4px 0 10px;
                }

                input {
                    width: 230px;
                }
            }

            button {
                width: 52px;
                height: 26px;
                font-size: 14px;
                color: #fff;
                background-color: rgba(212, 48, 48, 1);
            }
        }
    }

    .list {
        height: 700px;

        .orderItem {
            margin-bottom: 30px;

            .orderNum {
                margin-bottom: 15px;
                color: #333;
            }

            .products {
                border: 1px solid rgba(230, 230, 230, 1);

                .titleBar {
                    height: 40px;
                    padding: 0 30px;
                    color: #333;
                    background-color: #FAFAFA;
                    display: grid;
                    grid-template-rows: 40px;
                    grid-template-columns: 300px 150px 173px 173px 173px 130px;
                    align-items: center;

                    div:not(:first-child) {
                        text-align: center;
                    }

                    /*div:first-child {
                        width: 370px;
                    }

                    div:nth-child(2) {
                        width: 190px;
                    }

                    div:nth-child(3) {
                        width: 200px;
                    }
                    div:nth-child(4) {
                        width: 200px;
                    }
                    div:nth-child(5) {
                        width: 200px;
                    }

                    div:last-child {
                        width: 147px;
                    }*/
                }

                .titleBar, .item:not(:last-of-type) {
                    border-bottom: 1px solid rgba(230, 230, 230, 1);
                }

                .inner {
                    padding: 0 30px;

                    .item {
                        padding: 30px 0;
                        color: #333;
                        display: grid;
                        grid-template-rows: 78px;
                        grid-template-columns: 80px 220px 153px 173px 173px 173px 130px;
                        align-items: center;

                        img {
                            width: 80px;
                            height: 80px;
                            object-fit: cover;
                        }

                        .infoBox {
                            width: 200px;
                            padding-left: 20px;

                            div:first-child {
                                margin-bottom: 15px;
                            }

                            div:last-child {
                                color: #999;
                            }
                        }

                        .brand {
                            //width: 170px;
                            text-align: center;
                        }

                        .time {
                            div {
                                text-align: center;
                            }

                            div:first-child {
                                margin-bottom: 10px;
                            }
                        }

                        .apply {
                            width: 147px;
                            button {
                                width: 80px;
                                height: 30px;
                                color: #fff;
                                background-color: #216EC6;
                            }
                            div {
                                margin-right: 50px;
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>