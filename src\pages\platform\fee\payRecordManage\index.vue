<template>
    <div class="base-page">
        <el-tabs class="elTabsDiv_1" v-model="activeName" @tab-click="handleClick">
            <el-tab-pane class="elTabDiv_1" label="年度服务费审核" name="first">
                <YearPayRecord></YearPayRecord>
            </el-tab-pane>
            <el-tab-pane class="elTabDiv_1" label="交易服务费审核" name="second">
                <DealPayRecord></DealPayRecord>
            </el-tab-pane>
            <el-tab-pane class="elTabDiv_1" label="缴费管理" name="third">
                <PayRecordManageVue></PayRecordManageVue>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import PayRecordManageVue from './indexS.vue'
import YearPayRecord from '../yearPayRecord/index.vue'
import DealPayRecord from '../dealPayRecord/index.vue'
export default {
    components: {
        PayRecordManageVue,
        YearPayRecord,
        DealPayRecord
    },
    watch: {},
    computed: {},
    data () {
        return {
            activeName: 'first'
        }
    },
    mounted () {},
    // keepAlive使用了触发
    activated () {},
    created () {
    },
    methods: {
        handleClick (tab, event) {
            console.log(tab, event)
        },
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ .elTabsDiv_1 {
    width: 100%;height: 100%;position: relative;
    .el-tabs__active-bar {height: 4px;}
    .el-tabs__header {
        position: absolute;top: 10px;left: 20px;
    }
    .el-tabs__item {font-size: 16px;}
    .el-tabs__item:hover, .el-tabs__item.is-active {color: #333;font-weight: bold;}
    .el-tabs__content, .elTabDiv_1 {width: 100%;height: 100%;}
    .elTabDiv_1 .base-page .right .top {padding-top: 40px;height: 95px;}
}
</style>
