<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;"  v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="基础信息" name="baseInfo" :disabled="clickTabFlag">
              </el-tab-pane>
                <el-tab-pane label="对公账户 " name="" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="缴费信息" name="" :disabled="clickTabFlag">
              </el-tab-pane>
                <el-tab-pane label="审核记录" name="auditRecords" :disabled="clickTabFlag" />
                <div id="tabs-content">
                    <div id="baseInfo" class="con">
                        <div class="tabs-title" id="baseInfo">基础信息</div>
                        <el-form :model="formDtl" label-width="200px" :rules="formDtlRules" ref="formDtlRef" :disabled="false" class="demo-ruleForm">
<!--                            店铺名称、供应商名称-->
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="店铺名称：" prop="shopName">
                                        <span>{{formDtl.shopName}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="供应商名称：" prop="enterpriseName">
                                        <span>{{formDtl.enterpriseName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          年费截止日期、年费状态-->
                            <el-row>
                            <el-col :span="12">
                              <el-form-item label="上期年费有效期截止日期：" prop="lastPeriodDate">
                                <span>{{formDtl.lastPeriodDate}}</span>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label="上期年费状态：" prop="FeeStatus">
                                <el-tag v-show="FeeStatus">未到期</el-tag>
                                <el-tag type="danger" v-show="!FeeStatus">已到期</el-tag>
                              </el-form-item>
                            </el-col>
                          </el-row>
                        </el-form>
                    </div>
                    <div>
                        <div class="tabs-title" id="baseInfo">对公账户</div>
                      <el-form :model="formDtl" label-width="200px" :rules="formDtlRules" ref="formDtlRef" :disabled="false" class="demo-ruleForm">
<!--                            开户银行、银行户名-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="开户银行：" prop="bankName">
                              <span>{{formDtl.bankName}}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="银行户名：" prop="BankAccountName">
                              <span>{{formDtl.BankAccountName}}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
<!--                          银行账号-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="银行账号：" prop="BankAccountNumber">
                              <span>{{formDtl.BankAccountNumber}}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </div>
                    <div>
                        <div class="tabs-title" id="baseInfo">缴费信息</div>
                      <el-form :model="formDtl" label-width="200px" :rules="formDtlRules" ref="formDtlRef" :disabled="false" class="demo-ruleForm">
<!--                          缴费金额、审核状态-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="缴费金额：" prop="PaymentAmount">
                              <span>{{formDtl.PaymentAmount}}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="审核状态：" prop="AuditStatus">
                              <el-tag v-show="AuditStatus">未审核</el-tag>
                              <el-tag type="danger" v-show="!AuditStatus">已审核</el-tag>
                            </el-form-item>
                          </el-col>
                        </el-row>
<!--                            缴费凭证、提交时间-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="缴费凭证：" prop="PaymentVoucher">
                              <el-image style="width: 300px; height: 200px" :src="formDtl.PaymentVoucher"></el-image>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="提交时间：" prop="SubmissionTime">
                              <span>{{formDtl.SubmissionTime}}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </div>
                        <div id="auditRecords" class="con">
                            <div class="tabs-title" id="auditRecords">审核记录</div>
                            <div class="e-table"  style="background-color: #fff">
                                <el-table
                                    border
                                    style="width: 100%"
                                    :data="formDtl.auditRecords"
                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                    :row-style="{ fontSize: '14px', height: '48px' }"
                                >
                                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                                    <el-table-column prop="auditResult" label="审核意见" width="220">
                                    </el-table-column>
                                    <el-table-column prop="failReason" label="未通过原因" width="600">
                                    </el-table-column>
                                    <el-table-column prop="founderName" label="审核人" width="227">
                                    </el-table-column>
                                    <el-table-column prop="gmtCreate" label="审核时间" width="230">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                </div>
            </el-tabs>
            <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
        </div>
        <div class="buttons">
            <el-button type="primary"
                       v-if="formDtl.state === 1 "
                       @click="auditPlanM(1, '通过')">审核通过
            </el-button>
            <el-button type="primary" class="btn-delete"
                       v-if="formDtl.state === 1 "
                       @click="auditPlanM(0, '未通过')">审核未通过
            </el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
<!--        审核通过弹窗-->
      <el-dialog :visible.sync="Pass"
                 title="确认审核通过"
                 customClass="IspassDiv">
        <el-form :model="formDtl">
            <el-form-item label="上期年费有效期截止日期"><span>{{formDtl.lastPeriodDate}}</span></el-form-item>
<!--          已过期-->
          <div v-if="!IsDue">
            <p style="color: red">截止到今日店铺上期年费已到期，请确认本期年费有效期开始日期</p>
            <el-form-item label="本期年费缴费有效期开始日期">
              <el-date-picker
                v-model="thisAnnualFeeStartDate"
                type="date"
                @change="endDate"
                placeholder="选择日期">
            </el-date-picker></el-form-item>
            <el-form-item label="本期年费缴费有效期截止日期">
              <el-input
                  placeholder="根据开始日期自动计算"
                  v-model="thisAnnualFeeEndDate"
                  :disabled="true">
              </el-input>
            </el-form-item>
          </div>
<!--          未过期-->
          <div v-if="IsDue">
            <el-form-item label="本期年费缴费有效期开始日期"><span>{{formDtl.thisAnnualFeeStartDate}}</span></el-form-item>
            <el-form-item label="本期年费缴费有效期截止日期"><span>{{formDtl.thisAnnualFeeEndDate}}</span></el-form-item>
          </div>
        </el-form>
        <div style="text-align: right">
          <el-button class="IspassDivbtn" @click="passCancel">取消</el-button>
          <el-button class="IspassDivbtn" @click="passOk">确认</el-button>
        </div>
      </el-dialog>
<!--        审核未通过弹窗-->
      <el-dialog :visible.sync="noPass"
                 title="确认未审核通过"
                 customClass="IspassDiv">
        <div>
              <div style="width: 400px;height: 200px;margin: 0 auto">
                审核未通过原因
                <textarea  v-model="noPassReason" style="width: 400px;height: 150px;border: 1px solid red;margin: 0 auto;resize:none;"></textarea>
              </div>
        </div>
        <div style="text-align: right">
          <el-button class="IspassDivbtn" @click="nopassCancel">取消</el-button>
          <el-button class="IspassDivbtn" @click="nopassOk">确认</el-button>
        </div>
      </el-dialog>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { mapState } from 'vuex'
// import { findBySn, updateFee, deleteFee, auditFee } from '@/api/fee/feeApi'
import { findBySn, updateFee, deleteFee } from '@/api/fee/feeApi'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return  dateStr.split(' ')[0]
        }
    },
    components: {
    },
    data () {
        return {
            //审核是否通过
            AuditStatus: false,
            Pass: false,
            //是否到期
            FeeStatus: true,
            IsDue: true,
            //本期年费有效期开始截止日期
            thisAnnualFeeStartDate: '',
            thisAnnualFeeEndDate: '',
            //填写未通过原因
            noPassReason: '',
            noPass: false,
            fileList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            formDtlRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                reconciliationProductType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' },
                ],
            },
            uploadLoading: false,
            freeAmount: 2000,
            formDtl: {
                files: []
            },
            tableData: [
            ],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            formLoading: false,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    created () {
        this.getFormDtl()
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        //审核通过确认按钮
        passOk () {
            this.Pass = false
        },
        //审核通过取消按钮
        passCancel () {
            this.Pass = false
        },
        //审核未通过确认按钮
        nopassOk () {
            this.noPass = false
        },
        //审核未通过取消按钮
        nopassCancel () {
            this.noPass = false
        },
        //判断截止到今天是否到期
        determineIsDue () {
            const backendDate = this.formDtl.serveEndTime
            const backendDateObj = new Date(backendDate + 'T00:00:00Z')
            const backendUTCTime = Date.UTC(
                backendDateObj.getUTCFullYear(),
                backendDateObj.getUTCMonth(),
                backendDateObj.getUTCDate()
            )
            const now = new Date()
            const todayUTCTime = Date.UTC(
                now.getUTCFullYear(),
                now.getUTCMonth(),
                now.getUTCDate()
            )
            const isExpired = backendUTCTime < todayUTCTime
            this.FeeStatus = !isExpired
            this.IsDue = !isExpired
            //待删除
            this.IsDue = false
            console.log('isExpired', isExpired)
        },
        //点击审核通过、点击审核未通过
        auditPlanM (state, title) {
            if(title.includes('未通过')) {
                console.log('点击未通过')
                this.noPass = true
            }else {
                console.log('点击通过')
                this.determineIsDue()
                this.Pass = true
            }
            // this.clientPop('info', '您确定进行【' + title + '】操作吗？', async () => {
            //     if (state == 0) {
            //         this.$prompt('未通过原因', '提示', {
            //             confirmButtonText: '确定',
            //             cancelButtonText: '取消',
            //             type: 'error',
            //             inputType: 'textarea',
            //             inputPlaceholder: '请输入不通过原因',
            //             inputPattern: /^.+$/,
            //             inputErrorMessage: '请输入不通过原因'
            //         }).then(({ value }) => {
            //             let params = {
            //                 id: this.formDtl.paymentRecordId,
            //                 isOpen: 0,
            //                 auditResult: value,
            //             }
            //             this.formLoading = true
            //             auditFee(params).then(res => {
            //                 if (res.code != null && res.code == 200) {
            //                     this.$message.success('操作成功')
            //                     this.getFormDtl()
            //                 }
            //             }).finally(() => {
            //                 this.formLoading = false
            //             })
            //         })
            //     } else {
            //         let params = {
            //             id: this.formDtl.paymentRecordId,
            //             isOpen: 1,
            //         }
            //         this.formLoading = true
            //         auditFee(params).then(res => {
            //             if (res.code != null && res.code == 200) {
            //                 this.$message.success('操作成功')
            //                 this.getFormDtl()
            //             }
            //         }).finally(() => {
            //             this.formLoading = false
            //         })
            //     }
            // })
        },
        //计算本期年费有效期截止日期
        endDate () {
            try {
                const startDateStr = this.thisAnnualFeeStartDate
                if (!startDateStr || isNaN(new Date(startDateStr))) {
                    throw new Error('无效的起始日期')
                }
                const currentDate = new Date(startDateStr)
                const nextDate = new Date(currentDate)
                nextDate.setFullYear(currentDate.getFullYear() + 1)
                if (
                    currentDate.getMonth() === 1 &&
                    currentDate.getDate() === 29 &&
                    nextDate.getMonth() !== 1
                ) {
                    nextDate.setDate(0)
                }
                const year = nextDate.getFullYear()
                const month = String(nextDate.getMonth() + 1).padStart(2, '0')
                const day = String(nextDate.getDate()).padStart(2, '0')
                const formattedDate = `${year}-${month}-${day}`
                this.thisAnnualFeeEndDate = formattedDate
                console.log('截止日期:', formattedDate)
            } catch (error) {
                console.error('日期计算失败:', error.message)
                this.thisAnnualFeeEndDate = null
            }
        },
        checkInputQty () {
            const regex = /^-?\d+$/
            if (!regex.test(this.formDtl.paymentDuration)) {
                this.$message.error('请输入有效的数字')
                this.formDtl.payAmount = this.freeAmount
                return this.formDtl.paymentDuration = 1
            }
            if (this.formDtl.paymentDuration < 0 || this.formDtl.paymentDuration > 999) {
                this.$message.error('超过限制！')
                this.formDtl.payAmount = this.freeAmount
                return this.formDtl.paymentDuration = 1
            }
            this.formDtl.payAmount = this.formDtl.paymentDuration * this.freeAmount
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.formDtl.files = []
                    this.fileList = []
                }else {
                    this.formDtl.files = []
                    let resO = res[0]
                    this.formDtl.files.push({
                        name: resO.objectName,
                        relevanceType: 1,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.formDtl.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.formDtl.files = []
                this.fileList = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload (file) {
            this.uploadLoading = true
            let image = this.formDtl.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        deleteOneM () {
            this.clientPop('info', '您确定要删除操作吗？', async () => {
                this.formLoading = true
                deleteFee({ id: this.formDtl.paymentRecordId }).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        saveSheetM (num) {
            this.clientPop('info', '您确定要该操作吗？', async () => {
                this.formDtl.submitAud = num
                this.formLoading = true
                updateFee(this.formDtl).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        getFormDtl () {
            this.formLoading = true
            findBySn({ sn: this.$route.query.sn }).then(res => {
                this.formDtl = res
                console.log('this.formDtl', this.formDtl)
                let image = res.files[0]
                this.uploadLoading = true
                previewFile({ recordId: image.fileFarId }).then(res => {
                    const blob = new Blob([res])
                    const url = window.URL.createObjectURL(blob)
                    this.fileList = []
                    this.fileList.push({
                        name: image.name,
                        url: url
                    })
                }).finally(() => {
                    this.uploadLoading = false
                })
            }).finally(() => {
                this.formLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
.IspassDivbtn{
  width: 100px;
  margin-right: 20px;
}
</style>
<style>
.IspassDiv{
  padding: 0;
  width: 500px !important;
}
</style>

