import service from '@/utils/request'

const { httpPost, httpGet } = service

const getUserList = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/platform/user/listByParameters',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/platform/user/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/user/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/user/delete',
        params
    })
}

const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/user/deleteBatch',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/user/updateByPublish',
        params
    })
}

const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/user/updateNotPublish',
        params
    })
}

const getEnterpriseInfo = params => {
    return httpGet({
        url: '/materialMall/platform/user/findById',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/platform/user/updateBatchById',
        params
    })
}
// 获取用户店铺企业信息
const getUserShopEnterpriseInfo = params => {
    return httpGet({
        url: '/materialMall/platform/user/getUserShopEnterpriseInfo',
        params
    })
}
// 根据用户编号获取数据
const getUserShopEnterpriseInfoByUserSn = params => {
    params.mallType = 0
    return httpGet({
        url: '/materialMall/platform/user/getUserShopEnterpriseInfoByUserSn',
        params
    })
}
// 统计用户
const getUserCountList = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/platform/user/getUserCountList',
        params
    })
}
const updateBatchUserState = params => {
    return httpPost({
        url: '/materialMall/w/user/updateBatchUserState',
        params
    })
}

export {
    getUserList,
    create,
    edit,
    del,
    batchPublish,
    batchNotPublish,
    batchDelete,
    getEnterpriseInfo,
    changeSortValue,
    getUserShopEnterpriseInfo,
    getUserCountList,
    getUserShopEnterpriseInfoByUserSn,
    updateBatchUserState
}
