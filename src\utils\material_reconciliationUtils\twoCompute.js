import { calculateNotTarRateAmount, calculateNotTarRateAmountFour, toFixed } from '@/utils/common'

function  fixed2 (num) {
    return toFixed(num, 2)
}

export function  twoReconciliationAmountM  (tableData, taxRate) {

    let reconciliationAmount = 0
    let reconciliationNoRateAmount = 0
    let taxAmount = 0
    // 最终计算
    for (let i = 0; i < tableData.length; i++) {
        let t = tableData[i]
        t.totalAmount  = fixed2(Number(t.quantity) * Number(t.price))
        t.noRatePrice =  calculateNotTarRateAmount(t.price, taxRate)
        t.noRateAmount =  calculateNotTarRateAmount(t.totalAmount, taxRate)
        t.taxAmount =  fixed2(Number(calculateNotTarRateAmountFour(t.totalAmount, taxRate)) * Number(taxRate / 100))
        taxAmount = fixed2(Number(taxAmount) + Number(t.taxAmount))
        reconciliationAmount = fixed2(Number(reconciliationAmount) + Number(t.totalAmount))
        reconciliationNoRateAmount = fixed2(Number(reconciliationNoRateAmount) + Number(t.noRateAmount))
    }
    let  params = {
        tableData: tableData,
        reconciliationAmount: reconciliationAmount,
        reconciliationNoRateAmount: reconciliationNoRateAmount,
        taxAmount: taxAmount
    }
    return params
}

export function  twoReconciliationFloatAmountM  (tableData, taxRate) {

    let reconciliationAmount = 0
    let reconciliationNoRateAmount = 0
    let taxAmount = 0
    // 最终计算
    for (let i = 0; i < tableData.length; i++) {
        let t = tableData[i]
        t.price = fixed2(Number(t.transportPrice) + Number(t.outFactoryPrice))
        t.noRatePrice =  calculateNotTarRateAmount(t.price, taxRate)
        t.totalAmount  = fixed2(Number(t.quantity) * Number(t.price))
        t.noRateAmount =  calculateNotTarRateAmount(t.totalAmount, taxRate)
        t.taxAmount =  fixed2(Number(calculateNotTarRateAmountFour(t.totalAmount, taxRate)) * Number(taxRate / 100))
        taxAmount = fixed2(Number(taxAmount) + Number(t.taxAmount))
        reconciliationAmount = fixed2(Number(reconciliationAmount) + Number(t.totalAmount))
        reconciliationNoRateAmount = fixed2(Number(reconciliationNoRateAmount) + Number(t.noRateAmount))
    }
    let  params = {
        tableData: tableData,
        reconciliationAmount: reconciliationAmount,
        reconciliationNoRateAmount: reconciliationNoRateAmount,
        taxAmount: taxAmount
    }
    return params
}

export function  twoReconciliationFixAmountM  (tableData, taxRate) {

    let reconciliationAmount = 0
    let reconciliationNoRateAmount = 0
    let taxAmount = 0
    // 最终计算
    for (let i = 0; i < tableData.length; i++) {
        let t = tableData[i]
        t.price = this.fixed2(Number(t.fixationPrice) + Number(t.netPrice))
        t.totalAmount  = fixed2(Number(t.quantity) * Number(t.price))
        t.noRatePrice =  calculateNotTarRateAmount(t.price, taxRate)
        t.noRateAmount =  calculateNotTarRateAmount(t.totalAmount, taxRate)
        t.taxAmount =  fixed2(Number(calculateNotTarRateAmountFour(t.totalAmount, taxRate)) * Number(taxRate / 100))
        taxAmount = fixed2(Number(taxAmount) + Number(t.taxAmount))
        reconciliationAmount = fixed2(Number(reconciliationAmount) + Number(t.totalAmount))
        reconciliationNoRateAmount = fixed2(Number(reconciliationNoRateAmount) + Number(t.noRateAmount))
    }
    let  params = {
        tableData: tableData,
        reconciliationAmount: reconciliationAmount,
        reconciliationNoRateAmount: reconciliationNoRateAmount,
        taxAmount: taxAmount
    }
    return params
}