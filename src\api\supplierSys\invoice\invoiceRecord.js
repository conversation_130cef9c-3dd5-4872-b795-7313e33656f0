import service from '@/utils/request'

const { httpPost, httpGet } = service
//  列表查询
const getList = params => {
    return httpPost({
        url: '/materialMall/invoiceRecord/listByEntity',
        params
    })
}

const findById = params => {
    return httpGet({
        url: '/materialMall/invoiceRecord/findById',
        params
    })
}
const getInvoiceInfo = params => {
    return httpGet({
        url: '/materialMall/invoiceRecord/getData',
        params
    })
}
const getInvoiceRecordInfo = id => {
    return httpGet({
        url: '/materialMall/invoiceRecord/getInvoiceRecord/' + id,
    })
}
// 更新
const edit = params => {
    return httpPost({
        url: '/materialMall/receiptPerson/update',
        params
    })
}
// 新增
const create = params => {
    return httpPost({
        url: '/materialMall/invoiceRecord/create',
        params
    })
}
// 删除
const del = params => {
    return httpGet({
        url: '/materialMall/invoiceRecord/delete',
        params
    })
}

// 批量删除
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/invoiceRecord/deleteBatch',
        params
    })
}

const updateState = params => {
    return httpPost({
        url: '/materialMall/invoiceRecord/updateState',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/invoiceRecord/deleteBatch',
        params
    })
}
const batchPublish = params => {
    return httpPost({
        url: '/materialMall/batchPublish/updateByPublish',
        params
    })
}
//das
const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/batchNotPublish/updateNotPublish',
        params
    })
}
export {
    getInvoiceInfo,
    findById,
    getList,
    edit,
    create,
    del,
    batchDelete,
    changeSortValue,
    batchPublish,
    batchNotPublish,
    updateState,
    getInvoiceRecordInfo,
}
