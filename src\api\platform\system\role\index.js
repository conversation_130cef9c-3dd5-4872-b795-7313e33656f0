/* eslint-disable no-unused-vars */
import service from '@/utils/request'

const { httpPost, httpGet } = service

/**
 * 获取角色分页列表
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 角色名称（对应图片1搜索框）
 * @param {string} [params.categoryType] - 机构类型（1后台 2供应商 3履约）
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页条数
 */
const getRoleList = (params = {}) => {
    return httpPost({
        url: '/materialMall/bgmanage/system/role/list',
        params  // 使用params传递查询条件（与您封装的httpPost方式一致）
    })
}

/**
 * 新增角色
 * @param {Object} params - 角色数据
 * @param {string} params.name - 角色名称（必填）
 * @param {string} params.categoryType - 机构类型（必填）
 * @param {Array} [params.menuIds] - 菜单权限ID数组
 * @param {string} [params.remarks] - 备注
 */
const addRole = (params = {}) => {
    return httpPost({
        url: '/materialMall/bgmanage/system/role/add',
        params  // 使用params传递表单数据
    })
}

/**
 * 更新角色
 * @param {Object} params - 角色数据
 * @param {string} params.roleId - 角色ID（必填）
 * @param {string} params.name - 角色名称
 * @param {string} params.categoryType - 机构类型
 * @param {Array} [params.menuIds] - 菜单权限ID数组
 * @param {string} [params.remarks] - 备注
 */
const updateRole = (params = {}) => {
    return httpPost({
        url: '/materialMall/bgmanage/system/role/update',
        params  // 使用params传递表单数据
    })
}

/**
 * 删除角色
 * @param {string} roleId - 角色ID
 */
const deleteRole = roleId => {
    return httpPost({
        url: `/materialMall/bgmanage/system/role/delete/${roleId}`
    })
}

/**
 * 获取角色详情
 * @param {string} roleId - 角色ID
 */
const getRoleDetail = roleId => {
    return httpGet({
        url: `/materialMall/bgmanage/system/role/detail/${roleId}`
    })
}

const getAllRole = roleId => {
    return httpPost({
        url: '/materialMall/bgmanage/system/role/getAlllist'
    })
}

/**
 * 设置角色菜单权限
 * @param {string} roleId - 角色ID
 * @param {Array} menuIds - 菜单ID数组
 */
const setRoleMenus = (roleId, menuIds) => {
    return httpPost({
        url: '/materialMall/bgmanage/system/role/setRoleMenus',
        params: {  // 合并roleId和menuIds
            roleId,
            menuIds: JSON.stringify(menuIds) // 数组转为字符串传输
        }
    })
}

const getJgType = params => {
    return httpGet({
        url: '/materialMall/bgmanage/system/role/jglist',
        params
    })
}

export {
    getRoleList,
    addRole,
    updateRole,
    deleteRole,
    getRoleDetail,
    setRoleMenus,
    getJgType,
    getAllRole
}