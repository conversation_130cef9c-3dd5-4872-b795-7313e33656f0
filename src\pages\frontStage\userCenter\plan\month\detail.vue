<template>
    <div class="root" v-loading="showLoading">
        <div class="order-info box center">
            <div class="dfa mb20">
                <el-button type="primary" class="btn-greenYellow" v-if="addPlanForm.state == 0 || addPlanForm.state == 3 || addPlanForm.state == 1" @click="savePlanM">保存</el-button>
                <el-button type="primary" class="btn-greenYellow" v-if="addPlanForm.state == 0 || addPlanForm.state == 3 || addPlanForm.state == 1 " @click="savePlanM(1)">保存并提交</el-button>
                <el-button type="warning" class="" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="cancellationPlanM()">作废</el-button>
                <el-button type="primary" class="btn-greenYellow" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="auditPlanM(1, '通过')">通过</el-button>
                <el-button type="primary" class="btn-delete" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="auditPlanM(0, '未通过')">未通过</el-button>
                <el-button type="primary" v-if="addPlanForm.state == 2 " @click="createMonthOrderM()">生成订单</el-button>
<!--                <el-button type="warning" v-if="addPlanForm.state == 2" @click="alterationM()">变更</el-button>-->
            </div>
            <el-form  :inline="true" ref="addPlanFormRoteRef" :model="addPlanForm" :data="addPlanForm" :rules="addPlanFormRote">
                <el-row style="">
                    <el-col :span="8">
                        <el-form-item label="计划编号：" prop="planNo">
                            <el-input style="width: 300px" clearable disabled v-model="addPlanForm.planNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="单据状态：" prop="demandType">
                            <el-tag type="info" v-if="addPlanForm.state == 0">草稿</el-tag>
                            <el-tag type="" v-if="addPlanForm.state == 1">已提交</el-tag>
                            <el-tag type="success" v-if="addPlanForm.state == 2">通过</el-tag>
                            <el-tag type="danger" v-if="addPlanForm.state == 3">未通过</el-tag>
                            <el-tag type="warning" v-if="addPlanForm.state == 4">已作废</el-tag>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9" :offset="0">
                        <el-form-item label="单据机构：" prop="orgName">
                            <el-input disabled style="width: 300px" v-model="addPlanForm.orgName" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="合同编号：" prop="contractNo">
                            <el-input disabled style="width: 300px" v-model="addPlanForm.contractNo" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="计划月份：" prop="planDate">
                            <el-date-picker
                                value-format="yyyy-MM"
                                v-model="addPlanForm.planDate"
                                type="month"
                                align="right"
                                placeholder="选择月">
                            </el-date-picker>
                        <!--     :picker-options="pickerOptions"-->
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-form-item label="供应商：" prop="supplierName">
                            <el-input disabled style="width: 300px" v-model="addPlanForm.supplierName" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="">
                    <el-col :span="8">
                        <el-form-item label="变更通过次数：" prop="planNo">
                            {{addPlanForm.alterationCount}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input style="width: 1100px;" type="textarea" :auto-resize="false" v-model="addPlanForm.remarks"
                                      placeholder="请输入备注" maxlength="1000" show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-tabs v-model="activeName"  type="card" @tab-click="handleClick">
                <el-tab-pane label="明细列表" name="dtl">
                    <el-table
                        max-height="372px"
                        border
                        :data="addPlanForm.dtls"
                        style="min-height: 372px"
                        :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                        :row-style="{ fontSize: '14px', height: '48px' }"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="materialName" label="物资名称" width="">
                        </el-table-column>
                        <el-table-column prop="spec" label="规格型号" width="">
                        </el-table-column>
                        <el-table-column prop="unit" label="计量单位" width="100">
                        </el-table-column>
                        <el-table-column prop="sourceQty" label="总数量" width="100">
                        </el-table-column>
<!--                        <el-table-column prop="useQty" label="已消耗数量" width="130">-->
<!--                        </el-table-column>-->
                        <el-table-column prop="maxQty" label="剩余数量" width="130">
                        </el-table-column>
                        <el-table-column prop="thisPlanQty" label="本期数量" width="160">
                            <template slot-scope="scope">
                                <el-input-number v-if="scope.row.state != 2 && scope.row.state != 4"
                                                 size="mini" v-model="scope.row.thisPlanQty"
                                                 :min="1" :precision="4" :step="0.1" :max="scope.row.oldThisPlanQty + scope.row.maxQty"
                                                 @change="changePlanDtlRowM(scope.row)">
                                </el-input-number>
                                <span v-if="scope.row.state == 2 || scope.row.state == 4"> {{scope.row.thisPlanQty}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="addPlanForm.state == 2" prop="orderQty" label="已下单数量" width="120">
                        </el-table-column>
                    </el-table>
                    <!--                        <pagination :currentPage.sync="contractDtlObj.pagination2.currPage" :destination="contractDtlObj.pagination2.destination" :pageSize="contractDtlObj.pagination2.pageSize"-->
                    <!--                                    :total="contractDtlObj.pagination2.totalNum" :totalPage="contractDtlObj.pagination2.totalPage" @currentChange="currentChange3" @sizeChange="sizeChange3">-->
                    <!--                        </pagination>-->
                </el-tab-pane>
                <el-tab-pane label="审核历史" name="auditInfo">
                <el-table
                    max-height="372px"
                    border
                    :data="addPlanForm.auditList"
                    style="min-height: 372px"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="auditType" label="审核类型" width="160">
                        <template slot-scope="scope">
                            <span v-if="scope.row.auditType == 1">录入审核</span>
                            <span v-if="scope.row.auditType == 2">变更审核</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="founderName" label="审核人" width="200">
                    </el-table-column>
                    <el-table-column prop="gmtCreate" label="审核时间" width="160">
                    </el-table-column>
                    <el-table-column prop="auditResult" label="审核意见" width="">
                    </el-table-column>
                </el-table>
            </el-tab-pane>
                <el-tab-pane label="变更历史" name="planChanges">
                    <el-table
                        max-height="372px"
                        border
                        :data="addPlanForm.planChanges"
                        style="min-height: 372px"
                        :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                        :row-style="{ fontSize: '14px', height: '48px' }"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="planChangeNo" label="变更计划编号" width="200">
                        </el-table-column>
                        <el-table-column prop="planNo" label="计划编号" width="200">
                        </el-table-column>
                        <el-table-column prop="planDate" label="计划日期" width="100">
                            <template slot-scope="scope">
                                {{scope.row.planDate | dateStr}}
                            </template>
                        </el-table-column>
                        <el-table-column prop="contractNo" label="合同编号" width=""/>
                        <el-table-column prop="supplierName" label="供应商名称" width=""/>
                        <el-table-column prop="gmtCreate" label="创建时间" width=""/>
                        <el-table-column prop="state" label="变更状态" width="90">
                            <template slot-scope="scope">
                                <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                                <el-tag type="" v-if="scope.row.state == 1">已提交</el-tag>
                                <el-tag type="success" v-if="scope.row.state == 2">通过</el-tag>
                                <el-tag type="danger" v-if="scope.row.state == 3">未通过</el-tag>
                                <el-tag type="warning" v-if="scope.row.state == 4">已作废</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="80">
                            <template slot-scope="scope">
                                <div class="pointer" style="color: rgba(33, 110, 198, 1);"  @click="monthPlanDtl(scope.row)" >详情</div>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
            <el-dialog v-loading="submitMonthPlanOrderLoading" v-dialogDrag :visible.sync="showOrderDialog"
                       width="80%" :close-on-click-modal="false">
                <div class="list-title dfa mb20">
                    生成订单
                </div>
                <el-table
                    max-height="272px"
                    border
                    :data="addPlanForm.dtls"
                    style="min-height: 272px"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="materialName" label="物资名称" width="">
                    </el-table-column>
                    <el-table-column prop="spec" label="规格型号" width="">
                    </el-table-column>
                    <el-table-column prop="unit" label="计量单位" width="100">
                    </el-table-column>
<!--                    <el-table-column prop="sourceQty" label="数量" width="100">-->
<!--                    </el-table-column>-->
                    <!--                        <el-table-column prop="useQty" label="已消耗数量" width="130">-->
                    <!--                        </el-table-column>-->
<!--                    <el-table-column prop="maxQty" label="剩余数量" width="130"></el-table-column>-->
                    <el-table-column prop="thisPlanQty" label="本期数量" width="100">
                        <template slot-scope="scope">
                            <span> {{scope.row.thisPlanQty}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderQty" label="已下单数量" width="120">
                        <template slot-scope="scope">
                            <span> {{scope.row.orderQty}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="selectQty" label="选择数量" width="160">
                        <template slot-scope="scope">
                            <el-input-number size="mini" v-model="scope.row.selectQty"
                                             :min="0" :precision="4" :step="0.1" :max="scope.row.thisPlanQty - scope.row.orderQty"   @change="changeSelectQtyM(scope.row)">
                            </el-input-number>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="select-box mt30">
                    <div>
<!--                        <span>结算方式</span>-->
<!--                        <el-select v-model="payMethod" value-key="" placeholder="请选择支付方式">-->
<!--                            <el-option v-for="item in payMethods" :key="item.value" :label="item.label" :value="item.value">-->
<!--                            </el-option>-->
<!--                        </el-select>-->
                    </div>
                    <div>
                        <span>其他要求</span><input v-model="remarks" type="text" placeholder="填写要求"/>
                    </div>
                </div>
                <div class="addr-info center mb20">
                    <div class="list-title">
                        <span>收货信息</span>
                        <span style="margin-left: 800px" @click="checkedAddressM">切换收货地址</span>
                    </div>
                    <div class="addr-content">
                        <p class="mb20">收货地址：{{receiver.addr}}（{{receiver.name}}收） </p>
                        <p>收货人：{{receiver.name}}（{{receiver.tel}}）</p>
                    </div>
                </div>
                <div class="dfa" style="margin-left: 80%">
                    <el-button type="primary" @click="submitOrderM">提交订单</el-button>
                    <el-button  @click="showOrderDialog = false">取消</el-button>
                </div>
            </el-dialog>
            <el-dialog v-dialogDrag class="front" title="" :visible.sync="addrDialogVisible">
                <div class="dialog-header">
                    <div class="dialog-header-top search_bar">
                        <div class="dialog-title search_bar">
                            <div></div>
                            <div>选择收货地址</div>
                        </div>
                        <div class="dialog-close" @click="addrDialogVisible = false"><img src="@/assets/images/close.png" alt="" /></div>
                    </div>
                    <div></div>
                </div>
                <el-table :data="addrList">
                    <el-table-column label="收货地址" label-width="560" prop="addr"></el-table-column>
                    <el-table-column label="联系人" label-width="152" prop="name"></el-table-column>
                    <el-table-column label="联系电话" label-width="110" prop="tel"></el-table-column>
                    <el-table-column label="操作" label-width="">
                        <template v-slot="scope">
                            <span @click="handleEditAddr(scope.row)" style="color: rgba(34, 111, 199, 1);cursor: pointer;">编辑</span>
                            <span @click="handleCurrentInventoryClick(scope.row)" style="color: rgba(34, 111, 199, 1);cursor: pointer;margin-left: 20px">选择</span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="add pointer" @click="createAddress">+ 新增</div>
            </el-dialog>
            <el-dialog class="front" :visible.sync="addDetailDialog" top="8vh">
                <div class="dialog-header">
                    <div class="dialog-header-top search_bar">
                        <div class="dialog-title search_bar">
                            <div></div>
                            <div>选择收货地址</div>
                        </div>
                        <div class="dialog-close" @click="addDetailDialog = false"><img src="@/assets/images/close.png" alt="" /></div>
                    </div>
                    <div></div>
                </div>
                <!-- 弹框内容 -->
                <div class="dialog-body center">
                    <el-form :model="userAddressForm" ref="addAddressRef" :rules="userAddressFormRules" label-width="80px" :inline="false" label-position="top">
                        <el-form-item label="收货人：" prop="receiverName">
                            <el-input v-model="userAddressForm.receiverName" placeholder="请输入收货人姓名"></el-input>
                        </el-form-item>
                        <el-form-item class="tel" label="手机号码：" prop="receiverMobile">
                            <span>+86</span><el-input v-model="userAddressForm.receiverMobile" placeholder="请输入手机号码"></el-input>
                        </el-form-item>
                        <el-form-item label="选择地址：" prop="detailAddress">
                            <el-cascader
                                size="large"
                                :options="addressData"
                                v-model="selectAddressOptions"
                                @change="handleAddressChange">
                            </el-cascader>
                        </el-form-item>
                        <el-form-item class="address" label="详细地址：" prop="detailAddress">
                            <el-input v-model="userAddressForm.detailAddress" placeholder="请输入详细收货地址"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer">
                <button class="butSub" @click="createAddressM">保存</button>
            </span>
                </div>
            </el-dialog>
            <el-dialog v-loading="changePlanInfoLoading" v-dialogDrag :visible.sync="showChangePlanInfo"  width="80%" :close-on-click-modal="false">
                <div class="list-title dfa mb20">
                    变更详情
                </div>
                <el-form :inline="true" ref="addPlanFormRoteRef" :data="changePlanFormDate">
                    <el-row style="">
                        <el-col :span="8">
                            <el-form-item label="变更计划编号：" prop="planChangeNo">
                                {{changePlanFormDate.planChangeNo}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="当前计划月份：" prop="thisTruePlanDate">
                                {{changePlanFormDate.thisTruePlanDate}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="">
                        <el-col :span="8">
                            <el-form-item label="计划编号：" prop="planNo">
                                {{changePlanFormDate.planNo}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="变更单据状态：" prop="demandType">
                                <el-tag type="info" v-if="changePlanFormDate.state == 0">草稿</el-tag>
                                <el-tag type="" v-if="changePlanFormDate.state == 1">已提交</el-tag>
                                <el-tag type="success" v-if="changePlanFormDate.state == 2">通过</el-tag>
                                <el-tag type="danger" v-if="changePlanFormDate.state == 3">未通过</el-tag>
                                <el-tag type="warning" v-if="changePlanFormDate.state == 4">已作废</el-tag>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9" :offset="0">
                            <el-form-item label="单据机构：" prop="orgName">
                                {{changePlanFormDate.orgName}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="合同编号：" prop="contractNo">
                                {{changePlanFormDate.contractNo}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="变更计划月份：" prop="planDate">
                                {{changePlanFormDate.planDate}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item label="供应商：" prop="supplierName">
                                {{changePlanFormDate.supplierName}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注：" prop="remarks">
                                {{changePlanFormDate.remarks}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-tabs v-model="activeName2"  type="card" @tab-click="handleClick">
                    <el-tab-pane label="明细列表" name="dtl">
                        <el-table
                            max-height="372px"
                            border
                            :data="changePlanFormDate.dtls"
                            style="min-height: 372px"
                            :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                            :row-style="{ fontSize: '14px', height: '48px' }"
                        >
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column prop="materialName" label="物资名称" width="">
                            </el-table-column>
                            <el-table-column prop="spec" label="规格型号" width="">
                            </el-table-column>
                            <el-table-column prop="unit" label="计量单位" width="100">
                            </el-table-column>
                            <el-table-column prop="sourceQty" label="总数量" width="100">
                            </el-table-column>
                            <el-table-column prop="maxQty" label="剩余数量" width="100">
                            </el-table-column>
                            <el-table-column prop="thisTrueQty" label="当前数量" width="100">
                            </el-table-column>
                            <el-table-column prop="oldThisPlanQty" label="变更数量" width="100">
                            </el-table-column>
                            <el-table-column prop="orderQty" label="已下单数量" width="120">
                            </el-table-column>
                        </el-table>
                    </el-tab-pane><el-tab-pane label="审核历史" name="auditInfo">
                    <el-table
                        max-height="372px"
                        border
                        :data="changePlanFormDate.auditList"
                        style="min-height: 372px"
                        :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                        :row-style="{ fontSize: '14px', height: '48px' }"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="auditType" label="审核类型" width="160">
                            <template slot-scope="scope">
                                <span v-if="scope.row.auditType == 1">录入审核</span>
                                <span v-if="scope.row.auditType == 2">变更审核</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="founderName" label="审核人" width="200">
                        </el-table-column>
                        <el-table-column prop="gmtCreate" label="审核时间" width="160">
                        </el-table-column>
                        <el-table-column prop="auditResult" label="审核意见" width="">
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                </el-tabs>
                <el-button style="margin-left: 90%;margin-top: 20px" @click="showChangePlanInfo = false">取消</el-button>
            </el-dialog>
            <el-button style="margin-left: 90%;margin-top: 20px" @click="$router.push({path: '/user/monthPlan'})">返回</el-button>
        </div>
    </div>
</template>
<script>
import {
    updatePlanDtlByPlanId,
    submitMonthPlanOrder,
    getPlanDtlInfoByPlanNo,
    cancellationPlan,
    auditPlan,
    getPlanChangeDtlInfoByPlanNo
} from '@/api/plan/plan'
import { create, getDefaultAddress, getList } from '@/api/frontStage/shippingAddr'
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import { mapState } from 'vuex'

export default {
    data () {
        return {
            changePlanFormDate: {},
            changePlanInfoLoading: false,
            showChangePlanInfo: false,
            remarks: null, // 订单备注
            payMethod: 2,
            payMethods: [
                { label: '路桥结算', value: 2 }
            ],
            userAddressForm: { // 新增编辑地址表单
                detailAddress: null,
            },
            // 地址
            addressData: regionData, // 地址数据
            selectAddressOptions: [], // 地址选择
            addDetailDialog: false, // 地址编辑新增
            userAddressFormRules: {
                receiverName: [
                    { required: true, message: '请输入收件人', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                receiverMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
            },
            addrList: [],
            submitMonthPlanOrderLoading: false,
            addressListLoading: false,
            receiver: {
                name: null,
                tel: null,
                addr: null,
            },
            addrDialogVisible: false,
            showOrderDialog: false,
            changePlanDtlRowDate: [],
            changeSelectQtyRowDate: [],
            activeName: 'dtl',
            activeName2: 'dtl',
            addPlanFormRote: {
                planNo: [
                    { required: true, message: '请输入计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                contractNo: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                planDate: [
                    { required: true, message: '请选择计划日期', trigger: 'blur' },
                ],
                supplierName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            showLoading: false,
            addPlanForm: {},
            // pickerOptions: {
            //     disabledDate (time) {
            //         return time.getTime() < Date.now()
            //     },
            // },
        }
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        // 月供计划明细
        monthPlanDtl (row) {
            this.showChangePlanInfo = true
            this.changePlanInfoLoading = true
            getPlanChangeDtlInfoByPlanNo({ planChangeNo: row.planChangeNo }).then(res => {
                this.changePlanFormDate = res
            }).finally(() => {
                this.changePlanInfoLoading = false
            })
        },
        submitOrderM () {
            let newDtlArr =  this.changeSelectQtyRowDate.filter(t => {
                return t.selectQty != 0
            })
            if(newDtlArr.length == 0) {
                return this.$message.error('未选择数量！')
            }
            if(this.receiver.tel == null) {
                return this.$message.error('请选择收货地址！')
            }
            newDtlArr.forEach(t => {
                t.receiverName = this.receiver.name
                t.receiverMobile = this.receiver.tel
                t.receiverAddress = this.receiver.addr
                t.orderRemark = this.remarks
                t.payWay = this.payMethod
                t.planId = this.addPlanForm.planId
                t.contractNo = this.addPlanForm.contractNo
                t.contractId = this.addPlanForm.contractId
            })
            this.clientPop('info', '您确认要提交订单吗？', async () => {
                let params = {
                    dtos: newDtlArr
                }
                this.submitMonthPlanOrderLoading = true
                submitMonthPlanOrder(params).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.clientPop('suc', '提交成功！', () => {
                            this.getPlanDtlM()
                            this.showOrderDialog = false
                        })
                    }
                }).finally(() => {
                    this.submitMonthPlanOrderLoading = false
                })
            })
        },
        // 选择数量变化
        changeSelectQtyM (row) {
            if(row.selectQty == null) {
                row.selectQty = 0
            }
            if (this.changeSelectQtyRowDate.length == 0) {
                this.changeSelectQtyRowDate.push({
                    planDtlId: row.planDtlId,
                    materialName: row.materialName,
                    planNo: row.planNo,
                    spec: row.spec,
                    contractDtlId: row.contractDtlId,
                    unit: row.unit,
                    selectQty: row.selectQty
                })
                return
            }
            let flag = false
            this.changeSelectQtyRowDate.forEach(t => {
                if (t.planDtlId == row.planDtlId) {
                    t.selectQty = row.selectQty
                    flag = true
                }
            })
            if (!flag) {
                this.changeSelectQtyRowDate.push({
                    planDtlId: row.planDtlId,
                    materialName: row.materialName,
                    planNo: row.planNo,
                    spec: row.spec,
                    contractDtlId: row.contractDtlId,
                    unit: row.unit,
                    selectQty: row.selectQty
                })
            }
        },
        // 创建
        createAddress () {
            this.userAddressForm = {
                detailAddress: null,
            },
            this.selectAddressOptions = []
            this.addDetailDialog = true
        },
        // 地址选择
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.userAddressForm.province = province
            this.userAddressForm.city = city
            this.userAddressForm.county = county
            this.userAddressForm.detailAddress = province + city + county
        },
        // 创建编辑地址统一接口
        createAddressM () {
            this.$refs.addAddressRef.validate(valid => {
                if (valid) {
                    create(this.userAddressForm).then(res => {
                        if(res.code == 200) {
                            this.$message({
                                message: res.message,
                                type: 'success'
                            })
                            this.getAddRess()
                            this.addDetailDialog = false
                        }
                    })
                }
            })
        },
        // 编辑地址
        handleEditAddr (row) {
            let obj = {
                addressId: row.addressId,
                detailAddress: row.addr,
                receiverName: row.name,
                receiverMobile: row.tel,
            }
            this.userAddressForm = obj
            //地址选择器回显
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
            this.addDetailDialog = true
        },
        // 地址表格点击
        handleCurrentInventoryClick (row) {
            this.receiver = row
            this.addrDialogVisible = false
        },
        // 切换地址
        checkedAddressM () {
            this.getAddRess()
            this.addrDialogVisible = true
        },
        // 获取地址
        getAddRess () {
            // 获取收货地址
            this.addressListLoading = true
            getList({ page: 1, limit: 30 }).then(res => {
                if(!res.list[0]) return
                let address = []
                // 显示默认地址
                res.list.forEach(item => {
                    let obj = {
                        addressId: item.addressId,
                        checked: false,
                        addr: item.detailAddress,
                        name: item.receiverName,
                        tel: item.receiverMobile,
                        province: item.province,
                        city: item.city,
                        county: item.county,
                    }
                    address.push(obj)
                })
                this.addrList = address
            }).finally(() => {
                this.addressListLoading = false
            })
        },
        // 获取默认地址
        getDefaultAddressM () {
            this.submitMonthPlanOrderLoading = true
            getDefaultAddress().then(res => {
                if(res != null) {
                    this.receiver.name = res.receiverName
                    this.receiver.tel = res.receiverMobile
                    this.receiver.addr = res.detailAddress
                }
                this.submitMonthPlanOrderLoading = false
            }).catch(() => {
                this.submitMonthPlanOrderLoading = false
            })
        },
        // 生成订单
        createMonthOrderM () {
            let isSubmitFlag = this.userInfo.isSubmitOrder
            let isInterior = this.userInfo.isInterior
            if(isInterior == 1) {
                if(isSubmitFlag == null || isSubmitFlag == 0) {
                    return this.$message.error('没有下单权限，请联系管理员！')
                }
            }
            this.getDefaultAddressM()
            this.showOrderDialog = true
        },
        // 变更方法
        alterationM () {
        },
        getPlanDtlM () {
            this.showLoading = true
            getPlanDtlInfoByPlanNo({ planNo: this.$route.query.planNo }).then(res => {
                this.addPlanForm = res
            }).finally(() => {
                this.showLoading = false
            })
        },
        changePlanDtlRowM (row) {
            // 如果把数量增大了
            // if(row.thisPlanQty > row.oldThisPlanQty) {
            //     // 判断大于的数量是否超过剩余数量
            //     let num = row.thisPlanQty - row.oldThisPlanQty
            //     if(num > row.maxQty) {
            //         row.thisPlanQty = row.oldThisPlanQty + row.maxQty
            //     }
            // }
            if(row.thisPlanQty == null) {
                row.thisPlanQty = 1
            }
            if (this.changePlanDtlRowDate.length == 0) {
                this.changePlanDtlRowDate.push({
                    planDtlId: row.planDtlId,
                    thisPlanQty: row.thisPlanQty,
                })
                return
            }
            let flag = false
            this.changePlanDtlRowDate.forEach(t => {
                if (t.planDtlId == row.planDtlId) {
                    t.thisPlanQty = row.thisPlanQty
                    flag = true
                }
            })
            if (!flag) {
                this.changePlanDtlRowDate.push({
                    planDtlId: row.planDtlId,
                    thisPlanQty: row.thisPlanQty
                })
            }
        },
        // 标签点击暂时无用
        // eslint-disable-next-line no-unused-vars
        handleClick (tab, event) {
        },
        // 审核计划
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗！', async () => {
                if(state == 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            planId: this.addPlanForm.planId,
                            isOpen: 0,
                            auditResult: value,
                        }
                        auditPlan(params).then(res => {
                            if(res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.getPlanDtlM()
                            }
                        })
                    }).catch(() => {
                    })
                }else {
                    let params = {
                        planId: this.addPlanForm.planId,
                        isOpen: 1,
                    }
                    auditPlan(params).then(res => {
                        if(res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.getPlanDtlM()
                        }
                    })
                }
            })
        },
        // 作废计划
        cancellationPlanM () {
            this.clientPop('info', '您确定要废进行作废操作吗？', async () => {
                this.showLoading = true
                cancellationPlan([this.addPlanForm.planId]).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getPlanDtlM()
                    }
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        // 保存计划
        savePlanM (isSubmit) {
            this.$refs.addPlanFormRoteRef.validate(valid => {
                if(valid) {
                    // 保存并提交
                    let params = {
                        planId: this.addPlanForm.planId,
                        planDate: this.addPlanForm.planDate,
                        remarks: this.addPlanForm.remarks,
                        dtls: this.changePlanDtlRowDate,
                    }
                    if(isSubmit != null && isSubmit == 1) {
                        params.isSubmit = 1
                    }else {
                        params.isSubmit = 0
                    }
                    this.showLoading = true
                    updatePlanDtlByPlanId(params).then(res => {
                        if(res.code != 200) {
                            this.clientPop('error', res.message, () => {
                                // 查询计划
                                this.getPlanDtlM()
                            })
                        }else {
                            this.clientPop('suc', '操作成功', () => {
                                // 查询计划
                                this.getPlanDtlM()
                            })
                        }
                    }).finally(() => {
                        this.showLoading = false
                    })
                }else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
    },
    created () {
        this.getPlanDtlM()
    }
}
</script>
<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);

.root {
    height: 100%;
    padding-top: 20px;
    background-color: #f5f5f5;

    .box {
        padding: 20px;
        width: 1326px;
        background-color: #fff;
    }
}
.list-title {
    padding: 0;
    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}
.search {
    .box {
        width: 268px;
        height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);
        border-right: 0;
        img {
            width: 16px;
            height: 16px;
            margin: 0 4px 0 10px;
        }
        input {width: 230px;}
    }
    button {
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
    }
}
.list {
    .item {
        height: 52px;
        padding: 0 20px;
        font-size: 14px;
        border: $border;
        color: rgba(102, 102, 102, 1);
        position: relative;

        & > div:not(.pointer) {
            margin-right: 60px;
        }

        .pointer {
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 20px;
        }

        span {
            color: rgba(51, 51, 51, 1);
        }
    }
}/deep/ .el-dialog {
     .el-dialog__header {
         height: 10px;
         padding: 0px;
     }
     .el-dialog__body {
         overflow-y: hidden !important;
     }
 }

.el-form-item {
    display: flex;
    align-items: flex-start;
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}

.addr-info {
    //width: 1226px;
    width: 100%;
    background-color: #fff;
    .list-title {
        margin-top: 20px;
        padding-left: 33px;
        position: relative;
        & span:last-child {
            padding-left: 123px;
            color: rgba(33, 110, 198, 1);
            cursor: pointer;
        }
        &::before {
            position: absolute;
            left: 20px;
        }
    }
    .addr-content {
        padding-top: 20px;
        p {
            //margin-left: 20px;
            font-size: 14px;
            color: rgba(51, 51, 51, 1);
        }
    }
}
.add {
    width: 80px;
    height: 30px;
    margin: 18px 0 40px 0;
    line-height: 30px;
    text-align: center;
    color: rgba(33, 110, 198, 1);
    border: 1px solid rgba(33, 110, 198, 1);
}
.dialog-body {
    width: 500px;
    padding-top: 30px;

    .el-form-item {
        margin-bottom: 14px;
        &:last-of-type {margin-bottom: 20px;}
    }

    .el-form-item__label {
        // height: 14px;
        // margin-bottom: 20px;
        padding-bottom: 0;
        color: #999;
    }

    .el-input__inner {
        width: 300px;
        height: 35px;
        border: 1px solid rgba(217, 217, 217, 1);
        border-radius: 0;
    }
    .address .el-input__inner {width: 500px;}
    .tel {
        .el-form-item__content {
            display: flex;
            span {
                margin-right: 10px;
                color: #333;
            }
        }
        .el-input, .el-input__inner {width: 266px;}
    }
}
.butSub {
    width: 80px;
    height: 40px;
    font-size: 16px;
    color: #fff;
    background-color: #216EC6;
    margin-left: 100px;
}
.select-box {
    &>div {margin-bottom: 30px;}
    span {margin-right: 20px;}
    /deep/ .el-select {
        width: 300px;
        height: 30px;
        .el-input__inner {
            height: 30px;
            padding: 0 10px;
            color: rgba(51, 51, 51, 1);
        }
    }
    input {
        width: 600px;
        height: 30px;
        padding: 0 10px;
        border: 1px solid rgba(217, 217, 217, 1);
        &::-webkit-input-placeholder {
            color: rgba(51, 51, 51, 1);
        }
    }
}
</style>