<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                          <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">批量启用
                          </el-button>
                          <el-button type="primary" @click="changePublishState(0)" class="btn-delete">批量停用
                          </el-button>
                            <!--                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">批量启用</el-button>-->
                            <!--                            <el-button type="primary" @click="changePublishState(2)" class="btn-greenYellow">批量禁用</el-button>-->
                            <!--                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>-->
                            <!--                            <el-button type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改排序值</el-button>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <!--                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>-->
                        <el-input clearable type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png"
                                                                                                                                            slot="suffix" @click="onSearch" /></el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" v-loading="isLoading" :style="{ width: '100%' }">
                <el-table @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                          @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="用户编号" width="220">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row)">{{scope.row.userNumber}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="昵称" width="">
                        <template slot-scope="scope">
                            {{scope.row.nickName}}
                        </template>
                    </el-table-column>
                    <el-table-column label="账号" width="">
                        <template slot-scope="scope">
                            {{scope.row.account}}
                        </template>
                    </el-table-column>
                    <el-table-column label="手机号码" width="">
                        <template slot-scope="scope">
                            {{scope.row.userMobile}}
                        </template>
                    </el-table-column>
                    <el-table-column label="真实姓名" width="">
                        <template slot-scope="scope">
                            {{scope.row.realName}}
                        </template>
                    </el-table-column>
                    <el-table-column label="是否内部用户" width="">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.isInternalUser==1" type="success">是</el-tag>
                            <el-tag v-if="scope.row.isInternalUser==0" type="danger">否</el-tag>
                        </template>
                    </el-table-column>
                  <el-table-column label="状态" width="">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.materialState==1" type="success">启用</el-tag>
                      <el-tag v-if="scope.row.materialState==0" type="danger">停用</el-tag>
                    </template>
                  </el-table-column>
<!--                    <el-table-column  label="邮箱" width="">-->
<!--                        <template slot-scope="scope">-->
<!--                            {{scope.row.email}}-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column  label="上次登录时间" width="">
                        <template slot-scope="scope">
                            {{scope.row.gmtLogin}}
                        </template>
                    </el-table-column>
                    <el-table-column  label="账号创建时间" width="" >
                        <template slot-scope="scope" >
                            {{ scope.row.gmtCreate}}
<!--                            {{ scope.row.gmtCreate | fmtdate}}-->
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column  label="账号状态" width="">-->
                    <!--                        <template slot-scope="scope">-->
                    <!--                            <span v-if="scope.row.state == '0'">初始</span>-->
                    <!--                            <span v-else-if="scope.row.state == '1'">启用</span>-->
                    <!--                            <span v-else-if="scope.row.state == '2'">禁用</span>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                           @currentChange="currentChange" @sizeChange="sizeChange" />
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="true" :before-close="closeDialog">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item width="150px" label="账号：" prop="account">
                            <el-input v-model="filterData.account" placeholder="请输入账号（模糊查询）" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="真实姓名：" prop="realName">
                            <el-input v-model="filterData.realName" placeholder="真实姓名（模糊查询）" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="手机号：" prop="userMobile">
                            <el-input v-model="filterData.userMobile" placeholder="请输入手机号" clearable></el-input>
                        </el-form-item>
                    </el-col>
<!--                    <el-col :span="12" :offset="0">-->
<!--                        <el-form-item label="邮箱：" prop="email">-->
<!--                            <el-input v-model="filterData.email" placeholder="请输入邮箱" clearable></el-input>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
                    <el-col :span="12" :offset="0">
                        <el-form-item label="昵称：" prop="nickName">
                            <el-input v-model="filterData.nickName" placeholder="昵称（模糊查询）" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">返回</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { mapActions } from 'vuex'
import { updateBatchUserState, getUserList, edit, create, del, batchDelete, changeSortValue } from '@/api/platform/user/userInquire'
import { debounce, showLoading, hideLoading } from '@/utils/common'
export default {
    components: {
        ComPagination
    },
    // filters: {
    //     dateStr (dateStr) {
    //         let newDateSr = dateStr.split('-')
    //         return newDateSr[0] + '-' + newDateSr[1] + '-' + newDateSr[2]
    //     }
    // },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getUserList(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertName: '账号',
            queryVisible: false,
            action: '编辑',
            keywords: '',
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            isLoading: false,
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                account: '',
                realName: '',
                nickName: '',
                userMobile: '',
                email: '',
                state: null,
                orderBy: 2,
            },
            tableData: [],
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入链接名', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
            },
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            userStatusFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '初始' },
                { value: 1, label: '启用' },
                { value: 2, label: '禁用' },
            ]
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()

    },
    activated () {
        this.getTableData()
    },
    created () {
        // this.isLoading = true
        // let params = {
        //     limit: this.pages.pageSize,
        //     page: this.pages.currPage,
        //     mallType: this.filterData.mallType,
        //     orderBy: this.filterData.orderBy,
        //     auditStatus: this.filterData.auditStatus
        // }
        // getUserList(params).then(res => {
        //     this.pages = res
        //     this.isLoading = false
        //     this.tableData = res.list
        // })
        // this.getParams()
    },
    methods: {
        ...mapActions(['setUnitMeasur']),
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                shopName: '',
                city: '',
                shopType: null,
                state: null,
                isBusiness: null,
                isSupplier: null,
                isInternalShop: null,
                auditStatus: 1,
                orderBy: 1,
                mallType: 0,
            },
            done()
        },
        //高级查询返回
        hideDialog () {
            this.filterData = {
                shopName: '',
                city: '',
                shopType: null,
                state: null,
                isBusiness: null,
                isSupplier: null,
                isInternalShop: null,
                auditStatus: 1,
                orderBy: 1,
                mallType: 0,
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            for (let key in this.filterData) {
                if (this.filterData[key] === '') {
                    this.filterData[key] = null
                }
            }
            this.getParams()
            // 查询请求传参
            getUserList(this.requestParams).then(res => {
                this.isLoading = true
                if (res.list) {
                    this.queryVisible = false
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                this.isLoading = false
                this.pages = res
            })
        },

        // 发布/取消
        changePublishState (state) {
            if(this.selectedRows.length === 0) {
                return this.$message('未选中数据')
            }
            let params = {
                ids: this.selectedRows.map(item => item.userId),
                mallType: 0,
                state: state,
            }
            this.clientPop('info', '您确定要批量启用/停用这些数据吗！', async () => {
                this.isLoading = true
                let res = await updateBatchUserState(params)
                if(res.code === 200) {
                    this.$message.success('修改成功')
                    await this.getTableData()
                    this.selectedRows = []
                }
                this.isLoading = false
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${this.alertName}吗？`, async () => {
                showLoading()
                let res = await del({ id: scope.row.userId })
                if (res.message === '操作成功') {
                    this.clientPop('suc', '删除成功', () => {
                        this.getTableData()
                    })
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if(!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {})
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => item.userId)
                let res = await batchDelete(arr)
                if(res.message === '操作成功') {
                    this.clientPop('suc', '删除成功', () => {
                        this.getTableData()
                    })
                }
                hideLoading()
            })
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        handleClose () { },
        handleView (row) {
            this.action = '编辑'
            this.$router.push({
                path: '/platform/user/userInquireDetail',
                name: 'userInquireDetail',
                query: { userNumber: row.userNumber }
            })
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if(!this.changedRow[0]) {
                return this.changedRow.push({ userId: row.userId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if(item.userId === row.userId) {
                    return i
                }
            })
            if(arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ userId: row.userId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            // changeSortValue(this.changedRow).then(res => {
            //     this.getTableData()
            // })
            if (!this.changedRow[0]) {
                let msg = '当前没有排序值被修改！'
                return this.clientPop('warn', msg, () => { })
            }
            let warnMsg = '您确定要修改排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                let res = await changeSortValue(this.changedRow)
                if (res.message === '操作成功') {
                    this.clientPop('suc', '修改成功', () => {
                        this.getTableData()
                    })
                }
            })
        },
        // 获取列表数据
        async getTableData () {
            this.isLoading = true
            this.getParams()
            getUserList(this.requestParams).then(res => {
                this.pages = res
                this.isLoading = false
                this.tableData = res.list
            })
            // getUserList(this.requestParams)
            // this.tableData = res.list || []
            // this.pages = res
            // this.isLoading = false
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getParams()
            getUserList(this.requestParams).then(res => {
                this.tableData = res.list || []
                if(!res.list) this.clientPop('warn', res.message, () => {})
            })
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (!valid) return
                if (this.action === '编辑') {
                    return this.handleEditData()
                }
                this.handleCreateData()
            })
        },
        // 修改数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message !== '操作成功') return
                this.clientPop('suc', '保存成功', () => {
                    this.getTableData()
                })
            })
        },
        // 保存数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message !== '操作成功') return
                this.clientPop('suc', '保存成功', () => {
                    this.getTableData()
                })
            })
        },
        // 获取屏幕大小
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}
.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}
</style>
