.el-menu-vertical-demo.el-menu{
    height: 100%;
    .el-submenu__title, .el-menu-item{
        width: 100px;
        height: 50px;
        margin: 0 auto 4px;
        line-height: 50px;
        font-family: "SourceHanSansCN-Regular", serif;
        font-size: 17px;
        border-radius: 6px;
        img{width: 22px; height: 22px; margin-top: -4px;}
        span{line-height: 50px;}
        &:hover{
            background-color: #08305E !important;
            .bar{background-color: #ffd41c;}
        }
    }

    .el-submenu{
        i.el-icon-arrow-down::before{
            background: url(../images/tragle828.png) no-repeat 1px 4px;
            color: unset;
        }
        &.is-opened{
            .el-submenu__title{
                width: 100px;
                height: 50px;
                margin: 0 auto 4px;
                color: #FFD41C !important;
                background-color: #08305E !important;
                .bar{background-color: #ffd41c;}
            }
        }
    }
    .menu-item-box{
        padding: 10px 0;
        li.el-menu-item{
            width: 100px;
            height: 46px;
            margin-bottom: 6px;
            font-size: 15px;
            font-weight: 400 !important;
            line-height: 46px;
            color: red;
            position: relative;
            img{width: unset; height: unset; margin: 0 14px 0 -10px;}
            &:hover{background-color: unset !important;}
        }
        .el-menu-item{
            box-sizing: content-box;
            //&:first-child{padding-top: 10px;}
            //&:last-child{ margin-bottom: 0;}
        }
    }
}
// 个人中心菜单
.el-menu-vertical-user.el-menu{
    .el-submenu__title, .el-menu-item{
        height: 50px;
        margin: 0 auto;
        font-size: 16px;
        line-height: 50px;
        font-family: "SourceHanSansCN-Regular", serif;
        color: #333;
        position: relative;
        display: flex;
        align-items: center;
        img{min-width: 16px;height: 16px;margin-right: 8px;}
        .arrow{position: absolute;right: 22px;}
        &.is-active{background-color: #216EC6;color: #fff;}
        &.highlight{color: #FFD41C;}
        .arrow {background: url(../images/userCenter/go.png);}
    }
    .el-menu-item-group__title{padding: 0;}
    .el-menu-item-group {
        .el-menu-item {
            font-size: 14px;
            color: #666;
            .dot{width: 4px;height: 4px;border-radius: 50%;background: #D8D8D8;position: absolute; left: 20px;}
            &.is-active{color: #226FC7;background-color: #fff;}
            &.highlight{color: #FFD41C;}
        }
    }
    .el-submenu__icon-arrow {
        width: 16px;
        height: 16px;
        margin-right: 12px;
        background-image: url(../images/userCenter/close1.png);
        &::before{display: none;}
    }
    // 子菜单展开
    .el-submenu.is-opened{
        .el-submenu__title {
            background-color: #216EC6 !important;
            span{color: #fff;}}
        .el-submenu__icon-arrow{background-image: url(../images/userCenter/close2.png);}
    }
}