<template>
    <div class="box">
        <h3>{{title}}</h3>
        <span>店铺名称：</span>
        <el-input class="inp" v-model="form.shopName" placeholder="" clearable ></el-input>
        <span>店铺简介：</span>
        <el-input class="inp" v-model="form.shopDescrible" placeholder="" clearable ></el-input>
        <!-- <span>分类层级：</span>
        <el-select v-model="form.classLevel" class="inp" placeholder="请选择">
            <el-option v-for="item in selectLevel" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <span>分类类型：</span>
        <el-select v-model="form.classType" class="inp" placeholder="请选择">
            <el-option v-for="item in selectType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select> -->
        <button @click="handleSubmit">提交</button>
    </div>
</template>
<script>
import axios from 'axios'
export default {
    data () {
        return {
            title: '新增',
            form: {
                founderId: '',
                founderName: '',
                gmtCreate: '',
                gmtModified: '',
                isBusiness: 0,
                isDelete: 0,
                latitude: 0,
                longitude: 0,
                shopAddress: '',
                shopBalance: 0,
                shopDescrible: 'a new shop',
                shopFreezeMoney: 0,
                shopId: '',
                shopImg: '',
                shopName: 'New Shop',
                shopType: 0,
                state: 0,
                userId: ''
            },
            selectLevel: [
                {
                    value: 1,
                    label: '一级分类'
                },
                {
                    value: 2,
                    label: '二级分类'
                },
                {
                    value: 3,
                    label: '三级分类'
                },
            ],
            selectType: [
                {
                    value: 0,
                    label: '物资'
                },
                {
                    value: 1,
                    label: '装备'
                },
                {
                    value: 2,
                    label: '周材（物资）'
                },
                {
                    value: 3,
                    label: '周材（装备）'
                },
                {
                    value: 4,
                    label: '二手装备'
                },
                {
                    value: 5,
                    label: '租赁装备'
                },
            ]
        }
    },
    methods: {
        handleSubmit () {
            console.log(this.form)
            let url
            if(this.title === '修改') {
                url = '/productCategoryTest/shop/update'
            }else{
                url = '/productCategoryTest/shop/create'
            }
            axios({
                method: 'post',
                url,
                data: this.form
            }).then(res => {
                console.log(res)
                this.$router.back()
            })
        }
    },
    mounted () {
        if(this.$route.query.data) {
            this.form = JSON.parse(this.$route.query.data)
            console.log(this.form)
            this.title = '修改'
        }
    }
}
</script>
<style scoped lang="scss">
.box{
    display: flex;
    flex-direction: column;
}
.inp{
    width: 250px;
    margin-bottom: 30px;
}
button{
    width: 100px;
    height: 35px;
    background-color: #216ec6;
    color: #fff;
}
</style>