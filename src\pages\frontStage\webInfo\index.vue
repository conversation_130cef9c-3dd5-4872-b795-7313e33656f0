<template>
    <main>
        <div class="content center df">
            <div class="userMenu">
                <el-menu :default-active="activeItem" :unique-opened="true" :router="true" class="el-menu-vertical-user">
                        <el-submenu index="1">
                            <template slot="title">
                                <!-- <img class="icon" src="" alt=""> -->
                                <span>商家服务</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item :route="{ path: '/mFront/simpleDetail', query: { key: 'becomeAM' } }" index="1-1">
                                    <template slot="title">
                                        <div class="dot"></div> 我要开店
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                        <el-submenu index="2">
                            <template slot="title">
                                <!-- <img class="icon" src="" alt=""> -->
                                <span>关于我们</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item  :route="{ path: '/mFront/simpleDetail', query: { key: 'companyProfile' } }" index="2-1">
                                    <template slot="title">
                                        <div class="dot"></div> 公司介绍
                                    </template>
                                </el-menu-item>
                                <el-menu-item :route="{ path: '/mFront/simpleDetail', query: { key: 'contactUs' } }" index="2-2">
                                    <template slot="title">
                                        <div class="dot"></div> 联系我们
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                        <el-submenu index="3">
                            <template slot="title">
                                <!-- <img class="icon" src="" alt=""> -->
                                <span>平台规则</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item :route="{ path: '/mFront/webInfoList', query: { key: 'userAgreement' } }" index="3-1">
                                    <template slot="title">
                                        <div class="dot"></div> 用户协议
                                    </template>
                                </el-menu-item>
                                <!-- 待修改 -->
                                <el-menu-item :route="{ path: '/mFront/webInfoList', query: { key: 'thePSR' } }" index="3-2">
                                    <template slot="title">
                                        <div class="dot"></div> 服务规则
                                    </template>
                                </el-menu-item>
                                <el-menu-item :route="{ path: '/mFront/webInfoList', query: { key: 'serviceGuarantee' } }" index="3-3">
                                    <template slot="title">
                                        <div class="dot"></div> 服务保障
                                    </template>
                                </el-menu-item>
                                <el-menu-item :route="{ path: '/mFront/webInfoList', query: { key: 'sincerityManagement' } }" index="3-4">
                                    <template slot="title">
                                        <div class="dot"></div>诚信管理
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                        <el-menu-item :route="{ path: '/mFront/webInfoList', query: { key: 'commonProblem' } }" index="4">
                            <template slot="title">
                                <!-- <img class="icon" src="" alt=""> -->
                                <span>常见问题</span>
                            </template>
                        </el-menu-item>
                        <el-submenu index="5">
                            <template slot="title">
                                <!-- <img class="icon" src="" alt=""> -->
                                <span>新手服务</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item :route="{ path: '/mFront/simpleDetail', query: { key: 'userRegisteration' } }" index="5-1">
                                    <template slot="title">
                                        <div class="dot"></div> 用户注册
                                    </template>
                                </el-menu-item>
                                <el-menu-item :route="{ path: '/mFront/simpleDetail', query: { key: 'retrievePassword' } }" index="5-2">
                                    <template slot="title">
                                        <div class="dot"></div> 找回密码
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                        <el-menu-item :route="{ path: '/mFront/simpleDetail', query: { key: 'marketCooperation' } }" index="6">
                            <template slot="title">
                                <!-- <img class="icon" src="" alt=""> -->
                                <span>市场合作</span>
                            </template>
                        </el-menu-item>
                    </el-menu>
            </div>
            <div class="content-box">
                <div class="title">{{title}}</div>
                <router-view></router-view>
            </div>
        </div>
    </main>
</template>
<script>
export default {
    data () {
        return {
            title: '',
            activeItem: ''
        }
    },
    watch: {
        $route (route) {
            this.getCurrent(route)
        }
    },
    created () {
        this.getCurrent(this.$route)
    },
    methods: {
        loadPage (url) {
            this.$router.push(url)
        },
        getCurrent (route) {
            switch (route.query.key) {
            case 'becomeAM':
                this.title = '商家服务 > 我要开店'
                this.activeItem = '1-1'
                break
            case 'companyProfile':
                this.title = '关于我们 > 公司介绍'
                this.activeItem = '2-1'
                break
            case 'contactUs':
                this.title = '关于我们 > 联系我们'
                this.activeItem = '2-2'
                break
            case 'userAgreement':
                this.title = '平台规则 > 用户协议'
                this.activeItem = '3-1'
                break
            case 'thePSR':
                this.title = '平台规则 > 服务规则'
                this.activeItem = '3-2'
                break
            case 'serviceGuarantee':
                this.title = '平台规则 > 服务保障'
                this.activeItem = '3-3'
                break
            case 'sincerityManagement':
                this.title = '平台规则 > 诚信管理'
                this.activeItem = '3-4'
                break
            case 'commonProblem':
                this.title = '常见问题'
                this.activeItem = '4'
                break
            case 'userRegisteration':
                this.title = '新手服务 > 用户注册'
                this.activeItem = '5-1'
                break
            case 'retrievePassword':
                this.title = '新手服务 > 找回密码'
                this.activeItem = '5-2'
                break
            case 'marketCooperation':
                this.title = '市场合作'
                this.activeItem = '6'
                break
            }
        }
    },
}
</script>
<style scoped lang="scss">
@import '../../../assets/css/menuStyle.css';
main {width: 100%;padding: 20px 0 36px;}
.content {
    width: 1326px;
    justify-content: space-between;
}
.userMenu {
    width: 200px;
    border: 1px solid rgba(229, 229, 229, 1);
    /deep/ .el-menu-vertical-user.el-menu .el-submenu__icon-arrow {
        margin-right: 0;
    }
}
.content-box {
    width: 1106px;
    border: 1px solid rgba(229, 229, 229, 1);
    .title {
        height: 40px;
        padding: 0 20px;
        font-size: 12px;
        line-height: 40px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        color: rgba(51, 51, 51, 1);
        background: rgba(250, 250, 250, 1);
    }
}
</style>