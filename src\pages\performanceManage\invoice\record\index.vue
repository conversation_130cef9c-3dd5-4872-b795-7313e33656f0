<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-select @change="stateTopOptionsClick(stateOptionTitle)" v-model="stateOptionTitle"
                                       placeholder="请选择状态">
                                <el-option v-for="item in stateOptions" :key="item.value" :label="item.label"
                                           :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div>
                            <!--                            <el-button @click="handleDelete" class="btn-delete ml10">批量删除</el-button>-->
                        </div>
                    </div>
                    <!-- -搜索栏开始----------------------------搜索栏 -->
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-input
                            type="text" clearable placeholder="请输入关键字搜索" @keyup.enter.native="handleInputSearch"
                            v-model="keywords"
                        >
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                    <!-- 高级搜索弹出框开始 -->
                    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%">
                        <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="发票状态：">
                                        <el-select v-model="filterData.invoiceState" clearable>
                                            <el-option
                                                v-for="item in stateOptions" :key="item.value" :label="item.label"
                                                :value="item.value"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="发票类型：">
                                        <el-select v-model="filterData.state" clearable>
                                            <el-option
                                                v-for="item in typeOptions" :key="item.value" :label="item.label"
                                                :value="item.value"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="发票种类：">
                                        <el-select v-model="filterData.invoiceCategory" clearable>
                                            <el-option
                                                v-for="item in cateGORYOptions" :key="item.value" :label="item.label"
                                                :value="item.value"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="单位名称：">
                                        <el-input
                                            clearable v-model="filterData.company" maxlength="11"
                                            placeholder="输入单位名称"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <span slot="footer">
                            <el-button type="primary" @click="confirmSearch">查询</el-button>
                            <el-button @click="resetSearchConditions">清空</el-button>
                            <el-button @click="queryVisible = false">取消</el-button>
                        </span>
                    </el-dialog>
                    <!-- 高级搜索弹出框 -->
                    <!-- -搜索栏结束----------------------------搜索栏 -->
                </div>
            </div>
            <div class="e-table">
                <el-table
                    class="table"
                    :height="rightTableHeight"
                    v-loading="tableLoading"
                    :data="tableData" border highlight-current-row
                    @row-click="handleCurrentInventoryClick"
                    ref="eltableCurrentRow" @current-change="handleCurrentChange"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <!-- 收料人ID -->
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="320px">
                        <template slot-scope="scope">
                            <!--<el-button v-show="scope.row.invoiceState===1||scope.row.invoiceState===3"-->
                            <!--    size="mini"-->
                            <!--    type="danger"-->
                            <!--    @click="onDel(scope)"-->
                            <!--&gt;删除-->
                            <!--</el-button>-->
                            <el-button v-show="scope.row.invoiceState===5"
                                       size="mini"
                                       type="success"
                                       @click="updateStateM(scope.row,6)"
                            >通过作废
                            </el-button>
                            <el-button v-show="scope.row.invoiceState===5"
                                       size="mini"
                                       type="danger"
                                       @click="updateStateM(scope.row,7)"
                            >不通过作废
                            </el-button>

                            <el-button v-show="scope.row.invoiceState===9"
                                       size="mini"
                                       type="success"
                                       @click="updateStateM(scope.row,10)"
                            >通过红字申请
                            </el-button>
                            <el-button v-show="scope.row.invoiceState===9"
                                       size="mini"
                                       type="danger"
                                       @click="updateStateM(scope.row,11)"
                            >不通过红字申请
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="采购机构" width="150px">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row.invoiceId)">
                                {{ scope.row.enterpriseName }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="供应商" width="150px">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row.invoiceId)">
                                {{ scope.row.supplierName }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="单位名称" width="150px">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row.invoiceId)">
                                {{ scope.row.company }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="单位税号" width="150px">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row.invoiceId)">
                                {{ scope.row.dutyParagraph }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="发票状态" width="150px">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.invoiceState === 1" type="info">申请开票中</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 2" type="success">已开票</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 3" type="danger">开票被拒</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 4" type="info">采购员申请作废中</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 5" type="info">供应商申请作废中</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 6" type="danger">已作废</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 7" type="success">作废被拒</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 8" type="info">采购员申请红字中</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 9" type="info">供应商申请红字中</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 10" type="danger">已冲红</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 11" type="success">红字被拒</el-tag>
                            <el-tag v-if="scope.row.invoiceState === 12" type="success">红字发票</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="发票类型" width="150px">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.invoiceType === 0" type="success">增值税专用发票</el-tag>
                            <el-tag v-if="scope.row.invoiceType === 1" type="primary">增值税普通发票</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="发票种类" width="150px" prop="invoiceCategory">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.invoiceCategory === 1" type="success">红字发票</el-tag>
                            <el-tag v-if="scope.row.invoiceCategory === 0" type="primary">普通发票</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="抬头类型" width="">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.riseType === 0" type="success">个人</el-tag>
                            <el-tag v-if="scope.row.riseType === 1" type="success">单位</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="开户银行" width="200" prop="bank"></el-table-column>
                    <el-table-column label="银行账号" width="200" prop="bankAccount"></el-table-column>
                    <el-table-column label="收票人姓名" width="200" prop="userName"></el-table-column>
                    <el-table-column label="收票人联系电话" width="200" prop="userPhone"></el-table-column>
                    <el-table-column label="含税总金额" width="200" prop="rateAmount"></el-table-column>
                    <el-table-column label="关联发票" width="200" prop="relevanceId">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row.relevanceId)">
                                {{ scope.row.relevanceId }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" width="200" prop="gmtCreate"></el-table-column>
                    <el-table-column label="备注" width="200" prop="remarks"></el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.total" :limit="20" :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <el-dialog id="addreconciliationId" v-dialogDrag :close-on-click-modal="false"
                   :visible.sync="showReconciliationForm"
                   style="" title="默认开票抬头" width="30%">
            <el-form ref="bidingFormRef" :disabled="false" :model="state" class="demo-ruleForm" label-width="300px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="默认开票抬头：" prop="state">
                            <el-radio v-model="state" :label="1">是</el-radio>
                            <el-radio v-model="state" :label="0">否</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="buttons" style="position: absolute; right: 20px; bottom: 20px;">
                <el-button class="btn-blue" @click="addAffirmM">确认</el-button>
                <el-button @click="showReconciliationForm = false">返回</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { batchDelete, create, del, edit, getList, updateInvoiceState } from '@/api/performance/invoice'
import { debounce, hideLoading, showLoading } from '@/utils/common'

export default {
    components: {
        ComPagination
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return this.screenHeight - 21 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return this.screenWidth - 302 + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.pages.total > 0) {
                return this.screenHeight - 244
            }
            return this.screenHeight - 291
        }
    },
    data () {
        return {
            state: 1,
            showReconciliationForm: false,
            tableLoading: false,
            screenWidth: 0,
            alertName: '收料员',
            screenHeight: 0,
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            queryVisible: false, // 是否显示高级搜索：true 显示，false不显示

            keywords: '',
            tableData: [],
            requestParams: {},

            pages: {
                total: 0,
                currPage: 1,
                pageSize: 20
            },
            // 高级查询-启停用状态
            stateFilter: [
                { label: '单位', value: 1 },
                { label: '个人', value: 0 },
            ],
            invoiceState: [
                { label: '默认', value: 1 },
                { label: '其他', value: 0 },
                { label: '全部', value: null },
            ],
            typeOptions: [
                { label: '全部', value: null },
                { label: '增值税专用发票', value: 1 },
                { label: '增值税普通发票', value: 0 },
            ],
            cateGORYOptions: [
                { label: '全部', value: null },
                { label: '红字发票', value: 1 },
                { label: '普通发票', value: 0 },
            ],
            stateOptionTitle: null, // 选中的状态标题
            stateOptions: [
                {
                    value: null,
                    label: '全部'
                }, {
                    value: 1,
                    label: '申请开票中'
                }, {
                    value: 2,
                    label: '已开票'
                }, {
                    value: 3,
                    label: '开票被拒'
                },
                {
                    value: 4,
                    label: '申请作废中'
                },
                {
                    value: 6,
                    label: '已作废'
                },
                {
                    value: 7,
                    label: '作废被拒'
                },
                {
                    value: 8,
                    label: '申请红字中'
                },
                {
                    value: 10,
                    label: '已冲红'
                },
                {
                    value: 11,
                    label: '红字被拒'
                },
                {
                    value: 12,
                    label: '红字发票'
                }
            ],
            // 查询对象
            filterData: {
                orderBy: 1,
                state: null,
                invoiceState: null,
                invoiceType: null,
                userName: null,
                userAddress: null,
                userPhone: null,
                company: null,
                registerAddress: null,
                bank: null,
                bankAccount: null,
                invoiceCategory: null

            },
            action: '编辑',
            //
            currentClass: null,
            currentRow: null,
            changedRow: [],
            selectedRows: [],

        }
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },

    created () {
        this.getTableData()
    },
    methods: {
        // 获取列表数据
        async getTableData () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                invoiceClass: 1
            }
            if (this.filterData.invoiceState != null && this.filterData.invoiceState != '') {
                if (this.filterData.invoiceState === 1) {
                    params.invoiceState = [1]
                }
                if (this.filterData.invoiceState === 2) {
                    params.invoiceState = [2]
                }
                if (this.filterData.invoiceState === 3) {
                    params.invoiceState = [3]
                }
                if (this.filterData.invoiceState === 4) {
                    params.invoiceState = [4, 5]
                }
                if (this.filterData.invoiceState === 6) {
                    params.invoiceState = [6]
                }
                if (this.filterData.invoiceState === 7) {
                    params.invoiceState = [7]
                }
                if (this.filterData.invoiceState === 8) {
                    params.invoiceState = [8, 9]
                }
                if (this.filterData.invoiceState === 10) {
                    params.invoiceState = [10]
                }
                if (this.filterData.invoiceState === 11) {
                    params.invoiceState = [11]
                }
                if (this.filterData.invoiceState === 12) {
                    params.invoiceState = [12]
                }
            }
            if (this.filterData.state != null  && this.filterData.invoiceState != '') {
                params.state = this.filterData.state
            }
            if (this.filterData.invoiceType != null   && this.filterData.invoiceType != '') {
                params.invoiceType = this.filterData.invoiceType
            }
            if (this.filterData.invoiceCategory != null   && this.filterData.invoiceCategory != '') {
                params.invoiceCategory = this.filterData.invoiceCategory
            }
            if (this.filterData.riseType != null   && this.filterData.riseType != '') {
                params.riseType = this.filterData.riseType
            }
            if (this.filterData.userName != null   && this.filterData.name != '') {
                params.userName = this.filterData.name
            }
            if (this.filterData.userAddress != null   && this.filterData.userAddress != '') {
                params.userAddress = this.filterData.userAddress
            }
            if (this.filterData.userPhone != null   && this.filterData.userPhone != '') {
                params.userPhone = this.filterData.userPhone
            }
            if (this.filterData.company != null  && this.filterData.company != '') {
                params.company = this.filterData.company
            }
            if (this.filterData.dutyParagraph != null  && this.filterData.dutyParagraph != '') {
                params.dutyParagraph = this.filterData.dutyParagraph
            }
            if (this.filterData.bank != null  && this.filterData.bank != '') {
                params.bank = this.filterData.bank
            }
            if (this.filterData.bankAccount != null  && this.filterData.bankAccount != '') {
                params.bankAccount = this.filterData.bankAccount
            }
            if (this.filterData.orderBy != null  && this.filterData.orderBy != '') {
                params.orderBy = this.filterData.orderBy
            }
            if (this.keywords != null || this.keywords != '') {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            getList(params).then(res => {
                this.tableData = res.list
                this.pages.total = res.totalCount
                this.pages.pageSize = res.pageSize
                this.pages.currentPage = res.currPage
                // console.log(this.pages)
                // this.pages = res
            })
            this.tableLoading = false
            this.viewList = true
        },
        handleNew () {
            this.showReconciliationForm = true
        },
        // 新增表单数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        // 编辑表单数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message === '操作成功') {
                    this.$message.success('操作成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },

        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        addAffirmM () {
            this.$router.push({
                path: 'performance/invoice/invoiceRiseApply',
                name: 'invoiceRiseApply',
                params: {
                    viewType: 'add',
                    state: this.state

                }
            })
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        resetSearchConditions () {
            this.filterData.state = null,
            this.filterData.invoiceType = null,
            this.filterData.invoiceCategory = null,
            this.filterData.invoiceState = null,
            this.filterData.company = null

        },
        // 高级查询
        confirmSearch () {
            this.keywords = null
            this.getTableData()
            this.queryVisible = false
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        handleView (invoiceId) {
            this.$router.push({
                path: '/performance/invoice/invoiceDetail',
                query: {
                    invoiceId: invoiceId
                }
            })
        },

        updateStateM (row, state) {
            let params = {
                id: row.invoiceId,
                state: state
            }
            if (state === 7 || state === 11) {
                this.$prompt('不通过原因', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    inputType: 'textarea',
                    inputPlaceholder: '请输入不通过原因',
                    inputPattern: /^.+$/,
                    inputErrorMessage: '请输入不通过原因'
                }).then(({ value }) => {
                    params.failReason = value
                    this.tableLoading = true
                    updateInvoiceState(params).then(res => {
                        if (res.code == 200) {
                            this.$message.success('操作成功')
                            this.getTableData()
                        }
                    }).finally(() => {
                        this.tableLoading = false
                    })
                })
            } else {
                this.tableLoading = true
                updateInvoiceState(params).then(res => {
                    if (res.code == 200) {
                        this.$message.success('操作成功')
                        this.getTableData()
                    }
                }).finally(() => {
                    this.tableLoading = false
                })
            }
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该' + scope.row.company + '的发票抬头吗？', async () => {
                showLoading()
                del({ id: scope.row.invoiceId }).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    } else {
                        this.clientPop('warn', res.message, () => {
                            this.$message.warning('删除失败')
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        stateTopOptionsClick (stateOptionTitle) {
            this.filterData.invoiceState = stateOptionTitle
            this.getTableData()
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                this.$message.info('未选择数据')
                return
            }
            let arr = ids.map(item => {
                return item.invoiceId
            })
            let ids = this.selectedRows.filter(t => {
                return t.invoiceState === 1 || t.invoiceState === 3
            })
            if (ids.length == 0) {
                this.$message.info('请选择申请中或者已申请被拒的发票数据')
                return
            }
            this.clientPop('info', '您确定要删除选中的发票抬头吗？', async () => {
                showLoading()

                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        // this.setUnitMeasur()
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-form-item.uploader {
    height: 200px;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload
/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 100px;
        margin-top: 0px;
    }
}

/deep/ .el-dialog {
    //height: 500px !important;
    padding: 0;

    .el-dialog__header {
        margin-bottom: 20px;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        background: red url(../../../../assets/test.png) no-repeat;

        .el-dialog__title {
            color: #fff;
        }
    }

    .el-dialog__body {
        height: 70%;
        margin-top: 20px;
    }

    .el-dialog__close.el-icon.el-icon-close::before {
        width: 44px;
        height: 44px;
    }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>
