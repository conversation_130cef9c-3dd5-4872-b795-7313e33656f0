<template>
    <div class="table-container">
        <div class="func-area">
            <div class="btns-group">
                <button @click="addGear">新增</button>
                <button @click="uploadItem">上架</button>
                <button @click="takeDownItems">下架</button>
                <button @click="handle">删除</button>
            </div>
            <div class="search-group">
                <div class="select-box">
                    <span>筛选：</span>
                    <el-select v-model="selectValue" placeholder="请选择">
                        <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                </div>
                <div class="search-box">
                    <input type="text" />
                </div>
            </div>
        </div>
        <el-table
            ref="multipleTable"
            :data="data"
            :cell-style="cellStyle"
            :header-cell-style="headerRowStyle"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"> </el-table-column>
            <template v-for="item in rentService.props">
                <template v-if="item.prop">
                    <el-table-column :key="item.label" :prop="item.prop" :label="item.label" :width="item.width"></el-table-column>
                </template>
                <template v-else>
                    <el-table-column :key="item.label" :label="item.label" :width="item.width">
                        <template slot-scope="scope">
                            <span
                                style="color: #5d9eff; cursor: pointer; margin-right: 6px"
                                v-for="event in item.events"
                                :key="event.name"
                                @click="event.func(scope)"
                                >{{ event.label }}</span
                            >
                        </template>
                    </el-table-column>
                </template>
            </template>
        </el-table>
        <el-pagination
            @current-change="handlePageChange"
            @prev-click="toPrevPage"
            @next-click="toNextPage"
            background
            layout="prev, total, pager, next, jumper, sizes"
            :total="50"
        ></el-pagination>
    </div>
</template>

<script>
import axios from 'axios'
import { tableHeaderStyle, tableCellStyle, rentService } from '../../assets/tableStyle.js'
export default {
    data () {
        return {
            rentService,
            selectValue: '数量',
            selectOptions: [
                {
                    value: '1',
                    labe: 'apple',
                },
            ],
            data: [],
        }
    },
    methods: {
        // 编辑表格数据
        handleEditTable (scope) {
            console.log(scope.row)
        },
        // 上架
        addGear () {
            this.$router.push({ path: '/supplierSys/editShop', })
        },
        // 下架
        takeDownItems () {},
        handleSelectionChange () {},
        handlePageSwitch (action, i) {
            console.log(action, i)
        },
        // 表格样式
        headerRowStyle () {
            return tableHeaderStyle
        },
        cellStyle () {
            return tableCellStyle
        },
        handlePageChange (i) {
            console.log(i)
        },
        toPrevPage (i) {
            i
        },
        toNextPage (i) {
            i
        },
        getGearList () {
            axios({
                method: 'post',
                url: '/productCategoryTest/shop/listByEntity',
                data: { limit: 10, page: 1 }
            }).then(res => {
                console.log(res)
            })
        }
    },
    created () {},
    mounted () {
        this.getGearList()
    },
}
</script>
<style scoped lang="scss">
.table-container {
    // height: 99%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .func-area {
        width: 100%;
        height: 86px;
        padding-left: 16px;
        background-color: #f9f9f9;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .btns-group {
            button {
                width: 78px;
                height: 38px;
                margin-right: 8px;
                border-radius: 6px;
                font-size: 17px;
                color: #fff;
                background-color: #8bcdea;
                &:last-child {
                    background-color: #ffb381;
                }
            }
        }
        .search-group {
            display: flex;
            align-items: center;
            .select-box {
                margin-right: 12px;
                color: #adadad;
            }
            .search-box {
                input {
                    width: 160px;
                    height: 30px;
                    border: 1px solid #f4f4f4;
                }
            }
        }
    }
    // /deep/ .el-table{border-bottom: 1px solid blue;}
    /deep/ .el-pagination {
        margin: 10px 0 50px;
    }
}
</style>
