<template>
    <div class="page">
        <div class="full" style="padding-right: 11px;">
            <div class="menu full" v-show="showMenu">
                <div class="title">
                    <img class="logo" src="../../assets/images/wuzi_inspection.png" alt=""/>
                </div>
                <el-menu
                    :default-active="defaultActive"
                    class="el-menu-vertical-demo"
                    mode="vertical"
                    background-color="#ffffff00"
                    text-color="#fff"
                    active-text-color="#FFD41C"
                    :unique-opened="true"
                    menu-trigger="click"
                    :router="true"
                    @open="handleSubOpen"
                >
                    <template v-for="item in visibleMenu">
                        <!-- 包含子菜单 -->
                        <template v-if="item.children">
                            <el-submenu
                                :key="item.menuId"
                                :index="item.menuId.toString()"
                                @click="changePath(1, item.menuName)"
                            >
                                <template slot="title">
                                    <div class="bar"></div>
                                    <img :src="images.gear2" style="margin-right: 10px" alt=""/>
                                    <span>{{ item.menuName }}</span>
                                </template>
                                <div class="menu-item-box">
                                    <el-menu-item
                                        v-for="subItem in item.children"
                                        @click="changePath(2, subItem.menuName)"
                                        :key="subItem.menuId"
                                        :index="subItem.menuId"
                                        :route="subItem.route"
                                    >
                                        <template slot="title">
                                            <img :src="images.dot" alt=""/>
                                            <span>{{ subItem.menuName }}</span>
                                        </template>
                                    </el-menu-item>
                                </div>
                            </el-submenu>
                        </template>
                        <!-- 不包含子菜单 -->
                        <template v-else>
                            <el-menu-item
                                :key="item.menuId"
                                :index="item.menuId"
                                :route="item.route"
                                @click="changePath(0, item.menuName)"
                            >
                                <div class="bar"></div>
                                <img :src="images.gear2" style="margin-right: 10px" alt=""/>
                                <span slot="title">{{ item.menuName }}</span>
                            </el-menu-item>
                        </template>
                    </template>
                </el-menu>
            </div>
            <div id="fold-btn" @click="showMenu = !showMenu"></div>
        </div>
        <div class="table-box">
            <div class="history">
                <top-btn-bar></top-btn-bar>
            </div>
            <div style="background-color: #fff;">
                <div class="router-box">
                    <top-step :stepInfo="steps" v-show="showSteps"/>
                    <keep-alive>
                        <router-view v-if="$route.meta.keepAlive"></router-view>
                    </keep-alive>
                    <router-view v-if="!$route.meta.keepAlive"></router-view>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import topStep from '../../components/topstep/topstep'
import topBtnBar from '../../components/topButtonBar'
import foldBtn from '../../assets/menu_close.png'
import openBtn from '../../assets/menu_open.png'
import { mapState, mapActions } from 'vuex'

export default {
    components: { topStep, topBtnBar },
    data () {
        return {
            defaultActive: '1',
            showMenu: true,
            images: {
                gearActive: require('@/assets/images/zbgl2.png'),
                gear: require('@/assets/images/zbgl.png'),
                gear2: require('@/assets/images/ershou.png'),
                gear2Active: require('@/assets/images/zbgl.png'),
                rental: require('@/assets/images/zlgl.png'),
                rentalActive: require('@/assets/images/zlgl2.png'),
                repair: require('@/assets/images/wxbyfw.png'),
                repairActive: require('@/assets/images/wxbyfw2.png'),
                order: require('@/assets/images/ddgl.png'),
                orderActive: require('@/assets/images/ddgl2.png'),
                backTitle: require('@/assets/images/logodpgl.png'),
                dot: require('@/assets/images/dot219.png'),
                arrow: require('@/assets/images/tragle829.png')
            },
            currentSteps: [
                { description: '', },
                { description: '', },
            ],
            submenuName: '招标管理',
            menu: [
                {
                    menuName: '招标管理',
                    menuId: '1',
                    show: true,
                    children: [
                        { show: true, menuName: '公开招标', menuId: '1-1', route: { path: '/inspection/biddingManage/openTendering', } },
                        { show: true, menuName: '邀请招标', menuId: '1-2', route: { path: '/inspection/biddingManage/invitebiding', } },
                        { show: true, menuName: '询价', menuId: '1-3', route: { path: '/inspection/biddingManage/enquirybiding', } },
                        { show: true, menuName: '竞争性谈判', menuId: '1-4', route: { path: '/inspection/biddingManage/competebiding', } },
                        { show: true, menuName: '单一性来源', menuId: '1-5', route: { path: '/inspection/biddingManage/oneTender', } },
                    ],
                },
                /*{
                    menuName: '商品管理',
                    menuId: '2',
                    children: [
                        { show: true, menuName: '商品分类管理', menuId: '2-1', route: { path: '/shopManage/productCategory', } },
                        { show: true, menuName: '商品基础库管理', menuId: '2-2', route: { path: '/shopManage/rentService', } },
                        { show: true, menuName: '店铺商品管理', menuId: '2-3', route: { path: '/shopManage/rentService', } },
                    ],
                },
                {
                    menuName: '商铺管理',
                    menuId: '3',
                    children: [
                        { show: true, menuName: '商铺审核', menuId: '3-2', route: { path: '/shopManage/rentService', } },
                        { show: true, menuName: '商铺管理', menuId: '3-1', route: { path: '/shopManage/rentService', } },
                    ],
                },
                {
                    menuName: '订单管理',
                    menuId: '4',
                    children: [
                        { show: true, menuName: '订单查询', menuId: '4-1', route: { path: '/shopManage/rentService', } }

                    ],
                },
                {
                    menuName: '客户管理',
                    menuId: '5',
                    children: [
                        { show: true, menuName: '客户查询', menuId: '5-1', route: { path: '/shopManage/rentService', } }

                    ],
                }, {
                    menuName: '个人信息',
                    menuId: '6',
                    children: [
                        { show: true, menuName: '个人信息', menuId: '6-1', route: { path: '/shopManage/rentService', } },
                        { show: true, menuName: '密码修改', menuId: '6-1', route: { path: '/shopManage/rentService', } }
                    ],
                }*/
            ],
        }
    },
    watch: {
        showMenu: {
            handler (newVal) {
                let btnImg = newVal ? `url(${foldBtn})` : `url(${openBtn})`
                $('#fold-btn').css('background-image', btnImg)
            }
        },
        $route: {
            handler () {
                this.setDefaultActive()
            },
            deep: true,
            immediate: true
        },
    },
    computed: {
        ...mapState(['userInfo', 'steps']),
        showSteps () {
            return !this.$route.path.includes('etail')
        },
        // 最终渲染的菜单
        visibleMenu () {
            let visibleMenu = this.menu.filter(item => (item.show && this.hasPermission(item.role)))
            visibleMenu.forEach(item => {
                if(item?.children.length > 0) {
                    item.children = item.children.filter(child => (child.show && this.hasPermission(child.role)))
                }
            })
            return visibleMenu
        },
    },
    methods: {
        ...mapActions(['changeSteps']),
        // 传入权限字段，判断用户是否有此权限
        hasPermission (permission) {
            if(!permission || permission.length === 0) return true
            let { roles } = this.userInfo
            let hasPermission = true
            permission.forEach(item => {
                if(!hasPermission) return
                hasPermission = (roles && roles.includes(item)) || this.userInfo[item] === 1
            })
            return hasPermission
        },
        // 设置默认高亮的菜单项
        setDefaultActive () {
            let path = this.$route.path
            this.visibleMenu.forEach(item => {
                if(item.route && item.route.path === path) {
                    this.currentSteps = [{ description: item.menuName }]
                    this.defaultActive = item.menuId
                }
                if(!item.children) return
                item.children.forEach(subItem => {
                    if(subItem.route.path !== path) return
                    this.defaultActive = subItem.menuId
                    this.submenuName = item.menuName
                    this.currentSteps[0].description = item.menuName
                    this.currentSteps[1].description = subItem.menuName
                })
            })
        },
        // 展开子菜单
        handleSubOpen (index) {
            this.menu.forEach(item => {
                if (item.menuId === index) this.submenuName = item.menuName
            })
        },
        // 修改路径显示
        changePath (num, name) {
            if (this.steps.length === 1) {
                num !== 0 ? this.steps.push({ description: name }) : this.steps[0].description = name
            } else {
                if (num === 0) {
                    this.steps.pop()
                    this.steps[0].description = name
                } else {
                    this.steps[0].description = this.submenuName
                    this.steps[1].description = name
                }
            }
            this.$store.dispatch('changeSteps', this.steps)
        },
    },
    created () {
    },
}
</script>
<style scoped lang="scss">
@import '../../assets/css/backStage.css';

/deep/ .el-dialog__header {
    background: url(../../assets/test.png);
}

.page {
    display: flex;
    font-family: 'SourceHanSansCN-Regular';
    height: 100%;
}

.table-box {
    // min-width: 700px;
    // height: 100;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    box-sizing: border-box;
    background-color: #eff2f6;

    &::-webkit-scrollbar {
        display: none;
    }

    .history {
        font-weight: bold;
        font-size: 17px;

        & > div {
            line-height: 84px;

            span {
                color: gray;
            }
        }
    }

    .router-box {
        height: 100%;
        display: flex;
        flex-direction: column;

        & > *:last-child {
            flex-grow: 1;
        }
    }
}
</style>
