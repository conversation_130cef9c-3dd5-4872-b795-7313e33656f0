<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="showLoading">
            <el-tabs v-model="tabsName" tab-position="left" @tab-click="onChangeTab">
                <el-tab-pane :disabled="clickTabFlag" label="发票信息" name="baseInfo"></el-tab-pane>
                <el-tab-pane :disabled="clickTabFlag" label="发票项信息" name="reconciliation"></el-tab-pane>
                <el-tab-pane :disabled="clickTabFlag" label="发票电子版信息" name="filesInfo">
                    <el-tab-pane label="审核历史" name="auditInfo" :disabled="clickTabFlag"/>
                </el-tab-pane>
                <div id="tabs-content">
                    <div id="baseInfo" class="con">
                        <div id="baseInfo" class="tabs-title">发票信息</div>
                        <!--新增-->
                        <div class="form">
                            <el-form
                                ref="formData" :model="formData" :rules="rules"
                                class="demo-ruleForm" label-width="200px"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发票类型：" prop="invoiceType">
                                            <el-select disabled
                                                       v-model="formData.invoiceType"
                                                       @change="changRiseType(formData.invoiceType)"
                                            >
                                                <el-option
                                                    v-for="item in typeOptions" :value="item.value"
                                                    :label="item.label" :key="item.value"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="12">
                                        <el-form-item label="抬头类型：" prop="riseType">
                                            <el-select disabled v-model="formData.riseType"
                                            >
                                                <el-option
                                                    v-for="item in headerOptions" :value="item.value"
                                                    :label="item.label" :key="item.value"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="单位名称：" prop="company"
                                        >
                                            <el-input disabled
                                                      v-model="formData.company"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item disabled
                                                      label="单位税号：" prop="dutyParagraph"
                                        >
                                            <el-input
                                                v-model="formData.dutyParagraph"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册地址：" prop="province">
                                            <el-cascader disabled
                                                         style="width: 300px"
                                                         size="large"
                                                         :options="addressData"
                                                         v-model="selectAddressOptions"
                                                         @change="handleAddressChange"
                                            >
                                            </el-cascader>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item disabled
                                                      label="详细地址" prop="registerAddress"
                                                      :rules="formData.invoiceType==0? rules.registerAddress:[{
                                  required:false,
                                  message:'请输入详细地址'
                                }]"
                                        >
                                            <el-input disabled
                                                      clearable
                                                      v-model="formData.registerAddress"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item disabled
                                                      label="注册电话" prop="registerPhone"
                                                      :rules="formData.invoiceType==0? rules.registerPhone:[{
                                  required:false,
                                  message:'请输入注册电话'
                                }]"
                                        >
                                            <el-input disabled
                                                      clearable v-model="formData.registerPhone"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="邮箱" prop="email">
                                            <el-input disabled
                                                      clearable v-model="formData.email"
                                            ></el-input>
                                        </el-form-item>

                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item disabled
                                                      label="开户银行：" prop="bank" :rules="formData.invoiceType==0? rules.bank:[{
                                  required:false,
                                  message:'请输入开户银行'
                                }]"
                                        >
                                            <el-input disabled
                                                      v-model="formData.bank"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item disabled
                                                      label="银行账号：" prop="bankAccount"
                                                      :rules="formData.invoiceType==0? rules.bankAccount:[{
                                  required:false,
                                  message:'请输入银行账号'
                                }]"
                                        >
                                            <el-input disabled
                                                      v-model="formData.bankAccount"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <div class="col">
                                            <el-form-item label="收票人姓名：" prop="userName">
                                                <el-input disabled
                                                          v-model="formData.userName"
                                                ></el-input>
                                            </el-form-item>
                                        </div>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收票人手机号：" prop="userPhone">
                                            <el-input disabled
                                                      v-model="formData.userPhone"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="详细地址" prop="userAddress">
                                            <el-input disabled
                                                      clearable v-model="formData.userAddress"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="发票状态：" prop="rateAmount">
                                            <el-tag v-if="formData.invoiceState === 1" type="info">申请开票中</el-tag>
                                            <el-tag v-if="formData.invoiceState === 2" type="success">已开票</el-tag>
                                            <el-tag v-if="formData.invoiceState === 3" type="danger">开票被拒</el-tag>
                                            <el-tag v-if="formData.invoiceState === 4" type="info">采购员申请作废中
                                            </el-tag>
                                            <el-tag v-if="formData.invoiceState === 5" type="info">供应商申请作废中
                                            </el-tag>
                                            <el-tag v-if="formData.invoiceState === 6" type="danger">已作废</el-tag>
                                            <el-tag v-if="formData.invoiceState === 7" type="success">作废被拒</el-tag>
                                            <el-tag v-if="formData.invoiceState === 8" type="info">采购员申请红字中
                                            </el-tag>
                                            <el-tag v-if="formData.invoiceState === 9" type="info">供应商申请红字中
                                            </el-tag>
                                            <el-tag v-if="formData.invoiceState === 10" type="danger">已冲红</el-tag>
                                            <el-tag v-if="formData.invoiceState === 11" type="success">红字被拒</el-tag>
                                            <el-tag v-if="formData.invoiceState === 12" type="success">红字发票</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发票种类：" prop="invoiceCategory">
                                            <el-tag v-if="formData.invoiceCategory === 1" type="danger">红字发票</el-tag>
                                            <el-tag v-if="formData.invoiceCategory === 0" type="primary">普通发票</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税金额：" prop="rateAmount">
                                            <span>{{ formData.rateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row v-if="formData.invoiceState === 3">
                                    <el-col :span="24">
                                        <el-form-item label="不通过原因：" prop="failReason">
                                            <el-input type="textarea" v-model="formData.failReason"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="抬头备注：" prop="productDescribe">
                                            <el-input type="textarea" v-model="formData.remarks"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div id="reconciliation" class="con">
                        <div class="tabs-title" id="reconciliation">发票项信息</div>
                        <div class="e-table" style="background-color: #fff">
                            <el-table
                                ref="tableRef"
                                border
                                :data="formData.dtls"
                                class="table"
                            >
                                <el-table-column label="对账单编号" width="190" prop="reconciliationNo">
                                </el-table-column>
                                <el-table-column label="商品名称" width="230" prop="materialName"/>
                                <el-table-column label="商品规格" width="230" prop="spec"/>
                                <el-table-column label="单位" prop="unit" width="130"/>
                                <el-table-column label="数量" prop="quantity" width="150"/>
                                <el-table-column label="单价" width="" prop="price"/>
                                <el-table-column label="税额" width="" prop="taxRate"/>
                                <el-table-column label="价税合计金额" width="160" prop="acceptanceAmount"/>
                                <!--                                <el-table-column prop="remarks" label="备注" width="200"></el-table-column>-->
                            </el-table>
                        </div>
                    </div>
                    <div id="filesInfo" class="con" v-loading="openShopLoading">
                        <div class="tabs-title" id="filesInfo">发票电子版信息</div>
                        <div class="e-table" style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-button type="primary" @click="batchDownloadFile">批量下载
                                    </el-button>
                                    <el-button type="primary" @click="batchDownloadFilePackage">批量下载并打包
                                    </el-button>
                                    <el-upload
                                        multiple action="fakeaction"
                                        :limit="20"
                                        accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                        :before-upload="beforeOneOfFilesUpload"
                                        :on-exceed="uploadOneOfFiles"
                                        :show-file-list="false"
                                        :http-request="uploadOneOfFiles"
                                        :on-remove="handleRemove"
                                        style="margin-left: 10px"
                                    >
                                        <el-button type="primary">上传发票电子版</el-button>
                                    </el-upload>
                                </div>
                            </div>
                            <el-table ref="fileTableRef"
                                      border
                                      style="width: 100%"
                                      :data="fileList"
                                      class="table"
                                      @row-click="handleCurrentInventoryClick"
                                      @selection-change="selectionChangeHandle"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column type="selection" header-align="center" align="center"
                                                 width="50"></el-table-column>
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="name" label="附件名称" width=""></el-table-column>
                                <el-table-column label="操作" width="200">
                                    <template v-slot="scope">
                                        <el-button type="primary" class="btn-greenYellow"
                                                   @click="openShopDow(scope.row)">下载
                                        </el-button>
                                        <el-button type="primary" class="btn-greenYellow"
                                                   @click="handleRemove(scope.row)">删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <div id="auditInfo" class="con">
                        <div class="tabs-title" id="">审核历史</div>
                        <div class="e-table" style="background-color: #fff">
                            <el-table ref="fileTableRef"
                                      border
                                      style="width: 100%"
                                      :data="formData.auditList"
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="auditType" label="审核类型" width="160">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.auditType == 7">作废审核</span>
                                        <span v-if="scope.row.auditType == 8">红字审核</span>
                                        <span v-if="scope.row.auditType == 9">开票审核</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200"/>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160"/>
                                <el-table-column prop="auditResult" label="审核意见"/>
                            </el-table>
                        </div>
                    </div>
                </div>

            </el-tabs>
            <div class="buttons">
                <el-button type="success" v-if="formData.invoiceState === 2" @click="saveFilesM()">保存发票电子版
                </el-button>
                <el-button type="success" v-if="formData.invoiceState === 1" @click="changInvoiceStateM(2)">通过
                </el-button>
                <el-button type="danger" v-if="formData.invoiceState === 1" @click="changInvoiceStateM(3)">不通过
                </el-button>
                <el-button type="danger" v-if="formData.invoiceState === 2||formData.invoiceState === 7"
                           @click="updateStateM(5)">申请作废
                </el-button>
                <el-button type="danger" v-if="formData.invoiceState === 2||formData.invoiceState === 11"
                           @click="updateStateM(9)">申请红字
                </el-button>
                <el-button type="success" v-if="formData.invoiceState === 4" @click="updateStateM(6)">作废通过
                </el-button>
                <el-button type="danger" v-if="formData.invoiceState === 4" @click="updateStateM(7)">作废不通过
                </el-button>
                <el-button type="danger" v-if="formData.invoiceState === 7" @click="updateStateM(9)">申请红字
                </el-button>
                <el-button type="success" v-if="formData.invoiceState === 8" @click="updateStateM(10)">红字通过
                </el-button>
                <el-button type="danger" v-if="formData.invoiceState === 8" @click="updateStateM(11)">红字不通过
                </el-button>
                <el-button type="danger" v-if="formData.invoiceState === 11" @click="updateStateM(5)">申请作废
                </el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import { changInvoiceState, findById, saveFiles } from '@/api/shopManage/invoice/invoiceApply'
import '@/utils/jquery.scrollTo.min'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import { createFileRecordDelete } from '@/api/supplierSys/bidManage/myBidding/myBidManage'
import { updateInvoiceState } from '@/api/performance/invoice'

export default {
    data () {
        return {
            dataListSelections: [], //选中的数据
            showLoading: false,
            fileSelectList: [],
            addressData: regionData, // 地址数据
            selectAddressOptions: [], // 地址选择
            // 数据加载
            rules: {
                invoiceType: { required: true, message: '请选择发票类型', trigger: 'blur' },
                riseType: { required: true, message: '请选择抬头类型', trigger: 'blur' },
                registerPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                userPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                company: { required: true, message: '请选择单位名称', trigger: 'blur' },
                dutyParagraph: { required: true, message: '请选择单位税号', trigger: 'blur' },
                registerAddress: { validator: true, message: '请选择注册地址', trigger: 'blur' },
                bank: { required: true, message: '请选择开户银行', trigger: 'blur' },
                bankAccount: { required: true, message: '请选择银行账号', trigger: 'blur' },
            },
            selectAddressUserOptions: [], // 地址选择
            fileLoading: false,
            fileList: [],
            //表单数据
            formData: {
                rateAmount: '',
                invoiceId: '',
                state: 0,
                invoiceType: 0,
                riseType: 1,
                company: '',
                dutyParagraph: '',
                registerAddress: '',
                registerPhone: '',
                bank: '',
                bankAccount: '',
                userName: '',
                userPhone: '',
                userAddress: '',
                email: '',
                province: '',
                city: '',
                county: '',
                userProvince: '',
                userCity: '',
                userCounty: '',
                district: '',
                files: [],

            },
            typeOptions: [
                { label: '全部', value: null },
                { label: '增值税专用发票', value: 0 },
                { label: '增值税普通发票', value: 1 },
            ],
            cateGORYOptions: [
                { label: '全部', value: null },
                { label: '红字发票', value: 1 },
                { label: '普通发票', value: 0 },

            ],
            headerOptions: [
                { label: '单位', value: 1 },
                { label: '个人', value: 2 },
            ],
            addressOptions: {
                province: [],
                city: [],
                district: []
            },

            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            openShopLoading: false,
            winEvent: {},
            topHeight: 120,
            originalFileList: [],
        }
    },
    created () {
        this.getByIdM(true)
    },
    mounted () {
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'reconciliation', 'filesInfo', 'auditInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        invoiceChanged () {
            if (this.originalFileList.length !== this.fileList.length) return true
            let newFileIdList = this.fileList.map(item => item.fileId)
            let isChanged = false
            this.originalFileList.forEach(item => {
                if (!newFileIdList.includes(item)) isChanged = true
            })
            return isChanged
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        openShopDow (fileRow) {
            this.openShopLoading = true
            previewFile({ recordId: fileRow.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = fileRow.name
                a.click()
                window.URL.revokeObjectURL(url)
                this.openShopLoading = false
            }).catch(() => {
                this.openShopLoading = false
            })
        },

        batchDownloadFile () {
            if (this.fileSelectList.length == 0) {
                this.$message.info('未选择文件')
                return
            }
            this.fileSelectList.forEach(t => {
                this.openShopDow(t)
            })
            this.fileSelectList = []
        },
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.fileTableRef.toggleRowSelection(row, row.flag)
        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if (size > 500) {
                this.$message.error('文件大小不能超过500M')
                return false
            }
            return true
        },
        handleRemove (file) {
            let recordId = null
            let newFiles = this.fileList.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.fileList = newFiles
                }
            })
        },
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'device')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if (uploadRes.code != null && uploadRes.code !== 200) {
                this.fileList.push(file)
                this.fileList.pop()
            } else {
                this.$message.success('上传成功')
                this.fileList.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 16,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
            }
        },
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        async batchDownloadFilePackage () {
            this.openShopLoading = true
            let files = this.formData.files
            if (files == null || files.length == 0) {
                this.$message.info('附件为空！')
                return
            }
            const zip = new JSZip()
            for (let i = 0; i < files.length; i++) {
                let res = await previewFile({ recordId: files[i].fileFarId })
                zip.file(files[i].name, res)
            }
            let content = await zip.generateAsync({ type: 'blob' })
            FileSaver.saveAs(content, this.formData.company + '发票电子版.zip')
            this.openShopLoading = false
        },
        saveFilesM () {
            if (this.fileList.length === 0) {
                return this.$message.error('请上传文件')
            }
            let params = {
                invoiceId: this.formData.invoiceId,
                files: this.fileList
            }
            saveFiles(params).then(res => {
                if (res.code == 200) {
                    this.$message.success('文件保存成功')
                    this.originalFileList = this.fileList.map(item => item.fileId)
                }
            })
        },
        updateStateM (state) {
            let params = {
                id: this.formData.invoiceId,
                state: state
            }
            if (state === 7 || state === 11) {
                this.$prompt('不通过原因', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    inputType: 'textarea',
                    inputPlaceholder: '请输入不通过原因',
                    inputPattern: /^.+$/,
                    inputErrorMessage: '请输入不通过原因'
                }).then(({ value }) => {
                    params.failReason = value
                    updateInvoiceState(params).then(res => {
                        this.showLoading = true
                        if (res.code == 200) {
                            this.showLoading = false
                            this.$message.success('保存成功')
                            this.$router.go(-1)
                        }
                    })
                })
            } else {
                updateInvoiceState(params).then(res => {
                    this.showLoading = true
                    if (res.code == 200) {
                        this.showLoading = false
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                })
            }

        },
        changInvoiceStateM (state) {
            if (state == 3) {
                this.$prompt('', '请输入不通过失败原因', {
                    customClass: 'winClass', //弹窗样式,
                    inputType: 'textarea',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(({ value }) => {
                    let params =
                        {
                            invoiceId: this.formData.invoiceId,
                            invoiceState: state,
                            failReason: value,
                            files: this.fileList
                        }
                    changInvoiceState(params).then(res => {
                        this.handleClose()
                        this.message(res)
                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '取消输入'
                    })
                })
            } else {
                let params = {
                    invoiceId: this.formData.invoiceId,
                    invoiceState: state,
                    files: this.fileList
                }
                changInvoiceState(params).then(res => {
                    if (res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                })
            }
        },
        getByIdM (initial) {
            this.showLoading = true
            findById({ id: this.$route.query.invoiceId }).then(res => {
                if (res.province != null)
                    this.addressFormatShow(res)
                if (res.userProvince != null) {
                    this.addUserRessFormatShow(res)
                }
                this.formData = res
                this.fileList = this.formData.files
                if (initial) this.originalFileList = this.formData.files.map(file => file.fileId)
            }).finally(
                this.showLoading = false
            )
        },
        // 地址回显
        addressFormatShow (row) {
            if (row.province == null || row.province == '') {
                return
            }
            if (row.city == null || row.city == '') {
                return
            }
            if (row.county == null || row.county == '') {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 地址回显
        addUserRessFormatShow (row) {
            if (row.userProvince == null || row.userProvince == '') {
                return
            }
            if (row.userCity == null || row.userCity == '') {
                return
            }
            if (row.userCounty == null || row.UserCounty == '') {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.userProvince][row.userCity][row.userCounty].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressUserOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        handleAddressUserChange () {
            let addArr = this.selectAddressUserOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.formData.userProvince = province
            this.formData.userCity = city
            this.formData.userCounty = county
            this.formData.userAddress = province + city + county
        },
        changRiseType (state) {
            if (state == 0) {
                this.formData.riseType = 1
            }
        },

        // 地区
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.formData.province = province
            this.formData.city = city
            this.formData.county = county
            this.formData.registerAddress = province + city + county
        },
        //取消
        handleClose () {
            if (this.invoiceChanged) return this.$message.error('发票文件更改请先保存发票！')
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

.e-table {
    min-height: auto;
    background: #fff;
}

#tabs-content {
    padding-bottom: 70px !important;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
    display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 300px;
        margin-top: 0px;
    }
}

</style>
