<!--<template>-->
<!--  <div class="e-form">-->
<!--    <BillTop @cancel="handleClose"></BillTop>-->
<!--    <div class="tabs warningTabs" style="padding-top: 70px;">-->
<!--      <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">-->
<!--        <el-tab-pane label="招标信息" name="baseInfo" :disabled="clickTabFlag">-->
<!--        </el-tab-pane>-->
<!--        <el-tab-pane label="包件信息" name="packageList" :disabled="clickTabFlag">-->
<!--        </el-tab-pane>-->
<!--        <el-tab-pane label="招标清单" name="tenderDtl" :disabled="clickTabFlag" >-->
<!--        </el-tab-pane>-->
<!--        <div id="tabs-content">-->
<!--          <div id="baseInfCon" class="con">-->
<!--            <div class="tabs-title" id="baseInfo">招标信息</div>-->
<!--            <div style="width: 100%" class="form">-->
<!--              <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">-->
<!--                <el-row>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="项目名称：" prop="tenderName">-->
<!--                      <span>{{ formData.tenderName }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="采购人：" prop="tenderUser">-->
<!--                      <span >{{formData.tenderUser}}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="采购方式：" prop="gmtCreate">-->
<!--                      <span v-if="formData.tenderForm === '0'" >公开招标</span>-->
<!--                      <span v-if="formData.tenderForm === '1'" >邀请招标</span>-->
<!--                      <span v-if="formData.tenderForm === '2'" >询价</span>-->
<!--                      <span v-if="formData.tenderForm === '3'" >竞争性谈判</span>-->
<!--                      <span v-if="formData.tenderForm === '4'" >单一性来源</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="招标机构：" prop="applyOrgName">-->
<!--                      <span>{{ formData.applyOrgName }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="申请日期：" prop="applyTime">-->
<!--                      <span>{{ formData.applyTime }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="投标截止时间：" prop="tenderEndTime">-->
<!--                      <span>{{ formData.tenderEndTime  }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="拟招标金额：" prop="applyTime">-->
<!--                      <span>{{ formData.tenderAmount }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="税额：" prop="tenderEndTime">-->
<!--                      <span>{{ formData.taxAmount  }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="币种：" prop="currency">-->
<!--                      <span>{{ formData.currency}}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--              </el-form>-->
<!--            </div>-->
<!--          </div>-->

<!--          &lt;!&ndash;                    分包信息&ndash;&gt;-->
<!--          <div id="packageList" class="con" >-->
<!--            <div class="tabs-title" id="packageList">包件信息</div>-->
<!--            <div class="e-table"  style="background-color: #fff">-->
<!--              <el-table ref="tableRef"-->
<!--                        border-->
<!--                        style="width: 100%"-->
<!--                        :data="packageList"-->
<!--                        class="table"-->
<!--              >-->
<!--                <el-table-column label="序号" type="index" width="60"></el-table-column>-->
<!--                <el-table-column prop="itemNo" label="包件编号" width="200px"></el-table-column>-->
<!--                <el-table-column prop="name" label="包件名称" width="130">-->
<!--                </el-table-column>-->
<!--                <el-table-column prop="priceLimit" label="采购最高限价" width=""></el-table-column>-->
<!--                <el-table-column prop="taxRate" label="清单税率" width=""></el-table-column>-->
<!--                <el-table-column prop="taxAmount" label="税额" width=""></el-table-column>-->
<!--                <el-table-column prop="gmtCreate" label="创建时间" width=""></el-table-column>-->
<!--                <el-table-column prop="freightRate" label="运费税率" width=""></el-table-column>-->
<!--                <el-table-column prop="freightTax" label="运费税额" width=""></el-table-column>-->
<!--                <el-table-column prop="changeReason" label="补遗信息" width="200"></el-table-column>-->
<!--              </el-table>-->
<!--            </div>-->
<!--            &lt;!&ndash;            分页&ndash;&gt;-->
<!--          </div>-->

<!--          &lt;!&ndash;                    招标清单&ndash;&gt;-->
<!--          <div id="tenderDtl" class="con" >-->
<!--            <div class="tabs-title" id="tenderDtl">招标清单信息</div>-->
<!--&lt;!&ndash;            <div  v-if="TenderdtlVo.list.length>0">&ndash;&gt;-->
<!--              <div v-for="item in TenderdtlVo " :key="item" >-->
<!--                <div class="lineBox">-->
<!--                  <div class="leftDiv">包件编号: <span>{{item.itemNo}}</span></div>-->
<!--                  <div class="rightDiv">包件名称: <span>{{item.name}}</span></div>-->
<!--                </div>-->
<!--                <div v-if="item.list.length>0" class="e-table"  style="background-color: #fff">-->
<!--                  <el-table ref="tableRef"-->
<!--                            border-->
<!--                            style="width: 100%"-->
<!--                            :data="item.list"-->
<!--                            class="table"-->
<!--                  >-->
<!--                    <el-table-column label="序号" type="index" width="60"></el-table-column>-->
<!--                    <el-table-column prop="name" label="物资/装备名称" width="200px"></el-table-column>-->
<!--                    <el-table-column prop="type" label="物资/装备类别" width="130">-->
<!--                    </el-table-column>-->
<!--                    <el-table-column prop="specs" label="规格型号" width="">-->
<!--                    </el-table-column>-->
<!--                    <el-table-column prop="num" label="数量" width=""></el-table-column>-->
<!--                    <el-table-column prop="limitPrice" label="申报限价" width=""></el-table-column>-->
<!--                    <el-table-column prop="freightPrice" label="运输申报限价" width=""></el-table-column>-->
<!--                    <el-table-column prop="unitPrice" label="综合单价" width=""></el-table-column>-->
<!--                    <el-table-column prop="texture" label="材质" width=""></el-table-column>-->
<!--                    <el-table-column prop="changeReason" label="补遗信息" width="200"></el-table-column>-->
<!--                  </el-table>-->
<!--&lt;!&ndash;                </div>&ndash;&gt;-->
<!--&lt;!&ndash;                &lt;!&ndash;            分页&ndash;&gt;&ndash;&gt;-->

<!--              </div>-->
<!--            </div>-->
<!--          </div>-->

<!--        </div>-->
<!--      </el-tabs>-->
<!--    <div class="buttons">-->
<!--      <el-button @click="handleClose">返回</el-button>-->
<!--    </div>-->
<!--  </div>-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--import '@/utils/jquery.scrollTo.min'-->
<!--// eslint-disable-next-line no-unused-vars-->
<!--import { mapState, mapMutations } from 'vuex'-->
<!--import $ from 'jquery'-->
<!--import { throttle } from '@/utils/common'-->
<!--import { getAttendPackageList, getTenderPackagedtlList } from '@/api/supplierSys/bidManage/attendBiding/attendBiding'-->

<!--export default {-->

<!--    data () {-->
<!--        return {-->
<!--            individualHousehold: false,-->
<!--            package: false,-->
<!--            TenderPackageVo: [],-->
<!--            TenderdtlVo: [],-->
<!--            packageList: [],-->
<!--            packageFifter: {-->
<!--                keywords: null-->
<!--            },-->
<!--            packagePage: {-->
<!--                total: 200,-->
<!--                pageSize: 20,-->
<!--                currentPage: 1,-->
<!--            },-->

<!--            packageData: {},-->
<!--            //基本信息表单数据-->
<!--            formData: {},-->
<!--            tabsName: 'baseInfo',-->
<!--            screenWidth: 0,-->
<!--            screenHeight: 0,-->
<!--            lastConHeight: 0,-->
<!--            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑-->
<!--            winEvent: {},-->
<!--            topHeight: 120,-->
<!--            changedInfo: [],-->
<!--            dtlPackages: []-->
<!--        }-->
<!--    },-->
<!--    components: {-->
<!--        ...mapState(['userInfo'])-->
<!--    },-->
<!--    created () {-->
<!--        this.formData = this.$route.params.row-->
<!--        this.getTableData()-->
<!--        this.getTenderPackageSubcontractor()-->
<!--        this.getTenderDtl()-->
<!--    },-->
<!--    mounted () {-->
<!--    // 获取数据-->
<!--    // 获取最后一个内容区域的高度，计算底部空白-->
<!--        this.getLastConHeight()-->
<!--        // 保存所有tabName-->
<!--        const arr = ['baseInfo', 'packageInfo', 'adminInfo']-->
<!--        this.tabArr = arr-->
<!--        let $idsTop = []-->
<!--        const onScroll = () => {-->
<!--            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑-->
<!--            if (this.clickTabFlag) {-->
<!--                return-->
<!--            }-->
<!--            if (!$idsTop[$idsTop.length - 1]) {-->
<!--                $idsTop = arr.map(item => {-->
<!--                    const $item = document.getElementById(item)-->
<!--                    let itemTop = null-->
<!--                    if ($item) {-->
<!--                        itemTop = $item.offsetTop-->
<!--                    }-->
<!--                    return itemTop-->
<!--                })-->
<!--            }-->
<!--            const scrollTop = $('#tabs-content')[0].scrollTop-->
<!--            // 倒序查找-->
<!--            let curLocal = 0-->
<!--            for (let i = $idsTop.length - 1; i >= 0; i&#45;&#45;) {-->
<!--                let item = $idsTop[i]-->
<!--                if (scrollTop + 1 >= item) {-->
<!--                    curLocal = i-->
<!--                    break-->
<!--                }-->
<!--            }-->
<!--            // 设置对应tabName-->
<!--            this.tabsName = arr[curLocal]-->
<!--        }-->
<!--        this.winEvent.onScroll = onScroll-->
<!--        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)-->
<!--        this.screenWidth = document.documentElement.clientWidth - this.topHeight-->
<!--        this.screenHeight = document.documentElement.clientHeight - this.topHeight-->
<!--        const onResize = () => {-->
<!--            this.screenWidth = document.documentElement.clientWidth - this.topHeight-->
<!--            this.screenHeight = document.documentElement.clientHeight - this.topHeight-->
<!--            $idsTop = arr.map(item => {-->
<!--                const itemTop = document.getElementById(item).offsetTop-->
<!--                return itemTop-->
<!--            })-->
<!--        }-->
<!--        this.winEvent.onResize = throttle(onResize, 500)-->
<!--        window.addEventListener('resize', this.winEvent.onResize)-->
<!--        if (this.$route.query.name) {-->
<!--            setTimeout(() => {-->
<!--                this.onChangeTab({-->
<!--                    name: this.$route.query.name-->
<!--                })-->
<!--                this.tabsName = this.$route.query.name-->
<!--            }, 200)-->
<!--        }-->
<!--    },-->
<!--    beforeDestroy () {-->
<!--        window.removeEventListener('scroll', this.winEvent.onScroll)-->
<!--        window.removeEventListener('resize', this.winEvent.onResize)-->
<!--    },-->
<!--    computed: {-->
<!--        ...mapState({-->
<!--            options: state => state.contract.ctClassify,-->
<!--            userInfo: state => state.userInfo,-->
<!--        }),-->
<!--        // tab内容高度-->
<!--        tabsContentHeight () {-->
<!--            return this.screenHeight - 140 + 'px'-->
<!--        },-->
<!--        // 填补底部空白，以使高度够滚动-->
<!--        seatHeight () {-->
<!--            return this.screenHeight - 72 - this.lastConHeight-->
<!--        },-->
<!--    },-->
<!--    watch: {-->
<!--        screenHeight: {-->
<!--            handler (newVal) {-->
<!--                $('#tabs-content').height(newVal - 71)-->
<!--            }-->
<!--        },-->
<!--    },-->
<!--    methods: {-->
<!--        // 获取分包信息-->
<!--        getTableData () {-->
<!--            let params = {-->
<!--                page: this.packagePage.currentPage,-->
<!--                limit: this.packagePage.pageSize,-->
<!--                billId: this.formData.billId-->
<!--            }-->
<!--            getAttendPackageList(params).then(res => {-->
<!--                this.packageList = res.list-->
<!--                this.package = res-->

<!--                if ( this.packageList != null && this.packageList.length > 0) {-->
<!--                    this.dtlPackages = this.packageList.map(item=>{-->
<!--                        return item.dtlId-->
<!--                    })-->
<!--                    this.getTenderDtl()-->
<!--                }-->
<!--            })-->

<!--        },-->
<!--        getTenderDtl () {-->
<!--            let params = {-->
<!--                packageIds: this.dtlPackages,-->
<!--                billId: this.formData.billId-->
<!--            }-->
<!--            getTenderPackagedtlList(params).then(res=>{-->
<!--                this.TenderdtlVo = res-->
<!--            })-->
<!--        },-->
<!--        //取消-->
<!--        handleClose () {-->
<!--            this.$router.go(-1)-->
<!--        },-->
<!--        onChangeTab (e) {-->
<!--            const height = $('#' + e.name).offset().top-->
<!--            try{-->
<!--                $('#tabs-content').scrollTo(height - this.topHeight, 500)-->
<!--            }catch (err) {-->
<!--                console.log(err)-->
<!--            }-->
<!--            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑-->
<!--            this.clickTabFlag = true-->
<!--            // 动画结束后，恢复状态-->
<!--            setTimeout(() => {-->
<!--                this.clickTabFlag = false-->
<!--            }, 600)-->
<!--        },-->
<!--        // 获取最后一个内容区域的高度，计算底部空白-->
<!--        getLastConHeight () {-->
<!--            let si = setInterval(() => {-->
<!--                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度-->
<!--                if (document.getElementById('terminationLog')) {-->
<!--                    const lastConHeight =-->
<!--              document.getElementById('terminationLog').offsetHeight-->
<!--                    this.lastConHeight = lastConHeight-->
<!--                    clearInterval(si)-->
<!--                    si = null-->
<!--                }-->
<!--            }, 100)-->
<!--        },-->
<!--        // 消息提示-->
<!--        message (res) {-->
<!--            if(res.code == 200) {-->
<!--                this.$message({-->
<!--                    message: res.message,-->
<!--                    type: 'success'-->
<!--                })-->
<!--            }else {-->
<!--                this.$message({-->
<!--                    message: res.message,-->
<!--                    type: 'error'-->
<!--                })-->
<!--            }-->
<!--        },-->
<!--    }-->
<!--}-->
<!--</script>-->

<!--<style lang='scss' scoped>-->
<!--.mainTitle {-->
<!--  box-sizing: border-box;-->
<!--  width: 100%;-->
<!--  height: 40px;-->
<!--  line-height: 40px;-->
<!--  background: rgb(246, 246, 246);-->
<!--  border: 1px solid rgb(236, 236, 236);-->
<!--  margin: auto;-->
<!--  margin-bottom: 15px;-->
<!--  padding-left: 10px;-->
<!--}-->

<!--.separ {-->
<!--  width: 30px;-->
<!--  height: 40px;-->
<!--  line-height: 18px;-->
<!--  text-align: center;-->
<!--}-->

<!--.e-table {-->
<!--  min-height: auto;-->
<!--  background: #fff;-->
<!--}-->

<!--.upload {-->
<!--  margin: 20px auto;-->
<!--  display: flex;-->
<!--  justify-content: center;-->
<!--  text-align: center;-->
<!--}-->

<!--.upload-demo {-->
<!--  display: flex;-->
<!--  justify-content: center;-->
<!--  align-items: center;-->
<!--}-->

<!--/deep/.el-input&#45;&#45;suffix .el-input__inner {-->
<!--  padding-right: 5px;-->
<!--}-->

<!--/deep/ .el-tabs__content {-->
<!--  // overflow: hidden;-->
<!--  &::-webkit-scrollbar {width: 0;}-->
<!--}-->
<!--</style>-->
