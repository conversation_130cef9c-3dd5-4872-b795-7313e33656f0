import service from '@/utils/request'
// const service = require('@/utils/request')
// console.log('auth', service)
import qs from 'qs'

// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

const request = {
    // 登录接口
    identityAuthSignin (params) {
        // console.log(qs.stringify(params))
        return httpPost({
            url: '/identity/auth/signin',
            params: qs.stringify(params),
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    // 验证并刷新token
    refreshToken (params) {
        return httpPost({
            url: '/identity/auth/verifyToken',
            params: qs.stringify(params),
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    // router/index只认这个文件，很奇怪
    // 获取用户所在机构
    hrOrgGetOrgByUserId (params) {
        return httpGet({
            url: '/hr/org/getOrgByUserId',
            params
        })
    },
    // 获取本机构及其以下机构
    hrOrgGetChildrenOrg (params) {
        return httpGet({
            url: '/hr/org/getChildrenOrg',
            params
        })
    },
    configKvGetDicValue (params) {
        return httpGet({
            url: '/config/kv/getDicValue',
            params
        })
    },
    //获取系统级字典值
    configKvGetDicValue1 (params) {
        return httpGet({
            url: '/config/kv/getDicValue1',
            params
        })
    },
    // 获取工程概况
    profileGetProfile (params) {
        return httpGet({
            url: '/project/profile/getProfile',
            params,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    // 获取机构设置的本位币
    getBaseCyByOrgId (params) {
        return httpGet({
            url: '/config/cy/getBaseCyByOrgId',
            params
        })
    },
    // 获取机构下某币种的人民币汇率
    getRmbRate (params) {
        return httpGet({
            url: '/config/cy/getRmbRate',
            params
        })
    },
    //通过机构Id查找机构
    getOrgById (params) {
        return httpGet({
            url: '/hr/org/getOrgById',
            params
        })
    }
    // 获取本位币信息
    // getBaseCy (params) {
    //     return httpGet({
    //         url: '/config/cy/getBaseCy',
    //         params
    //     })
    // },
}

export default request
