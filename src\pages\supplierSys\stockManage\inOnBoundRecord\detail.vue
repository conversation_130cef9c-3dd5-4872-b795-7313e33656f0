<template>
  <div class="e-form">
    <BillTop @cancel="handleClose"></BillTop>
    <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
      <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
        <el-tab-pane label="入库详情" name="baseInfo" :disabled="clickTabFlag">
        </el-tab-pane>
        <el-tab-pane label="入库明细" name="productInfo" :disabled="clickTabFlag">
        </el-tab-pane>
        <div id="tabs-content">
          <!-- 基本信息 -->
          <div id="baseInfCon" class="con">
            <div class="tabs-title" id="baseInfo">入库详情</div>
            <div style="width: 100%" class="form">
              <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="业务类型：">
                      <span v-if="formData.productType==0">低值易耗品</span>
                      <span v-if="formData.productType==1">大宗临购</span>
                      <span v-if="formData.productType==2">大宗临购清单</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="入库时间：">
                      <span>{{ formData.supplierName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="供货单位：">
                      <div style="width: 80%" class="textOverflow1">
                        {{ formData.supplierName }}
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="库房选择：">
                      <div>
                        <span v-if="formData.warehousedId==1">库房一</span>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="订单号：">
                      <span>{{ formData.orderSn }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="数量：">
                      <span>{{ formData.num }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="采购含税进价：">
                      <span>{{ formData.bidRateAmount }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="采购不含税进价：">
                      <span>{{ formData.noRateAmount }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="收件人：">
                      <span>{{ formData.operationUser }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="收件人手机号：">
                      <span>{{ formData.operationUserPhone }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="备注：">
                      <span>{{ formData.remarks }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <!-- 订单商品-->
          <div id="productInfo" class="con" v-loading="tableLoading">
            <div class="tabs-title" id="contractList">入库明细</div>
            <div class="e-table"  style="background-color: #fff">
              <el-table ref="masterOrderItemRef"
                        border
                        style="width: 100%"
                        :data="tableData"
                        class="table"
                        :max-height="$store.state.tableHeight"
              >
                <!--                                待完成选中物资-->
                <el-table-column type="selection" width="40"></el-table-column>
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="productName" label="商品名称" width="200px"></el-table-column>
                <el-table-column prop="skuName" label="规格型号" width="200px"></el-table-column>
                <el-table-column prop="unit" label="计量单位" width="200px"></el-table-column>
                <el-table-column prop="shipNum" label="数量" width=""/>
                <el-table-column  prop="shipCounts" label="采购含税单价" width="160px"></el-table-column>
                <el-table-column  prop="confirmCounts" label="采购含税金额" width="160px"></el-table-column>
                <el-table-column  prop="returnCounts" label="采购不含税金额" width="100"></el-table-column>
                <el-table-column  prop="pcwpReturn" label="备注" width="100"></el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tabs>
      <div class="buttons">
        <el-button @click="handleClose">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'

export default {
    data () {
        return {
            formLoading: false,
            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedRow: [],
            changedRowPrice: [],
            tableLoading: false,
            thisCurrentOrderId: null,
        }
    },
    created () {
        this.formData = this.$route.params.row
        this.tableData = JSON.parse(this.formData.settlementInfo)
    },
    mounted () {
    // 获取数据
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        //取消
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
  box-sizing: border-box;
  width: 100%;
  height: 40px;
  line-height: 40px;
  background: rgb(246, 246, 246);
  border: 1px solid rgb(236, 236, 236);
  margin: auto auto 15px;
  padding-left: 10px;
}

.e-table {
  min-height: auto;
  background: #fff;
}

/deep/ .el-form-item__content {
  max-width: 80% !important;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
/deep/ .el-tabs__content {
  // overflow: hidden;
  &::-webkit-scrollbar {width: 0;}
}
/deep/ .el-dialog {
  .el-dialog__body {
    height: 780px;
    margin-top: 0px;
  }
}

/deep/ #supplierDialog {
  .el-dialog__body {
    height: 580px;
    margin-top: 0px;
  }
}
/deep/ #supplierDialog2 {
  .el-dialog__body {
    height: 480px;
    margin-top: 0px;
  }
}
</style>
