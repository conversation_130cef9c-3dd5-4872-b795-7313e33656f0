<template>
    <main>
        <div class="list-title dfa">收货地址
            <div class="msg">
                您已创建了 <span>{{ list.length }}</span> 个收货地址，最多可创建 <span>25</span> 个收货地址
            </div>
            <div class="add-addr dfa pointer" @click="handleNew">+ 新增收货地址</div>
        </div>
        <div class="list-box">
            <div class="item mb20" v-for="(item, i) in list" :key="i">
                <div class="name dfa">
                    <span>{{ item.receiverName }}</span>
                    <img src="@/assets/images/userCenter/ico_bianji.png" @click="openEdit(item)" alt="">
                    <div class="default dfa" v-if="item.isDefaultAddress == '1'">
                        <img src="@/assets/images/userCenter/ico_mrdz.png" alt="">默认地址
                    </div>
                    <div class="setDefault" @click="setDefault(item)" v-else>设为默认</div>
                </div>
                <div class="info p20">
                    <div class="mb20 df">
                        <span>收货人：</span><span>{{ item.receiverName }}</span>
                    </div>
                    <div class="mb20 df">
                        <span>所在地区：</span><span>{{ item.province +item.city+item.county}}</span>
                    </div>
                    <div class="mb20 df">
                        <span>地址：</span><span>{{ item.detailAddress }}</span>
                    </div>
                    <div class="mb20 df">
                        <span>手机号：</span><span>{{ item.receiverMobile }}</span>
                    </div>
<!--                    <div class="mb20 df">-->
<!--                        <span>固定电话：</span><span>{{ item.fixedTelephone }}</span>-->
<!--                    </div>-->
<!--                    <div class="mb20 df">-->
<!--                        <span>电子邮箱：</span><span>{{ item.email }}</span>-->
<!--                    </div>-->
                    <div class="actions">
                        <span @click="openEdit(item)">编辑</span> | <span @click="handleDelete(item.addressId)">删除</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- 弹框 -->
        <el-dialog class="front" :visible.sync="dialogVisible" top="8vh">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>{{ dialogTitle }}</div>
                    </div>
                    <div class="dialog-close" @click="dialogVisible = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <!-- 弹框内容 -->
            <div class="dialog-body center">
                <el-form :model="form" ref="form" :rules="rules" :inline="false" label-position="top">
                    <el-form-item label="收货人：" prop="receiverName">
                        <el-input v-model="form.receiverName" placeholder="请输入收货人姓名"></el-input>
                    </el-form-item>
                  <el-form-item label="所在地区：" prop="area">
                    <el-cascader
                        size="large"
                        :options="addressData"
                        v-model="form.area"
                        @change="handleAddressChange">
                    </el-cascader>
                  </el-form-item>
                    <el-form-item class="address" label="地址：" prop="detailAddress">
                        <el-input v-model="form.detailAddress" placeholder="请输入详细收货地址"></el-input>
                    </el-form-item>
                    <el-form-item class="tel" label="手机号码：" prop="receiverMobile">
                        <span>+86</span><el-input v-model="form.receiverMobile" placeholder="请输入手机号码"></el-input>
                    </el-form-item>
<!--                    <el-form-item label="固定电话：" prop="fixedTelephone">
                        <el-input v-model="form.fixedTelephone" placeholder="请输入固定电话号码"></el-input>
                    </el-form-item>
                    <el-form-item label="电子邮箱：" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱地址"></el-input>
                    </el-form-item>
                    <el-form-item label="地址别名：" prop="name">
                        <el-input v-model="form.name" placeholder="请输入地址别名"></el-input>
                    </el-form-item>-->
                </el-form>
                <button @click="onSave">保存</button>
            </div>
        </el-dialog>
    </main>
</template>
<script>
import { regionData, CodeToText, TextToCode } from 'element-china-area-data'
// eslint-disable-next-line
import { getList, update, create, del, setDefaultAddress } from '@/api/frontStage/shippingAddr'
export default {
    data () {
        return {
            dialogVisible: false,
            dialogTitle: '新增收货地址',
            list: [
            ],
            addressData: regionData,
            form: {
                receiverName: '',
                area: [],
                province: '',
                city: '',
                county: '',
                detailAddress: null,
                fixedTelephone: '',
                receiverMobile: '',
                email: '',
                default: false,
                aliasAddress: '',
            },
            // 验证规则待完善
            rules: {
                receiverName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                area: { required: true, message: '请选择所在地区', trigger: 'blur' },
                detailAddress: [
                    { required: true, message: '请填写详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                receiverMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                fixedTelephone: { required: false, },
                // email: { required: false, },
                // name: { required: false, },
            },
        }
    },
    created () {
        let userId = localStorage.getItem('userId')
        getList({ userId, }).then(res => {
            res.list.forEach(item => {
                item.area = [item.province, item.city, item.county]
            })
            this.list = res.list
        })
    },
    methods: {
        closeForm () {
            this.form = {
                receiverName: '',
                area: [],
                province: '',
                city: '',
                county: '',
                detailAddress: null,
                fixedTelephone: '',
                receiverMobile: '',
                email: '',
                default: false,
                aliasAddress: '',
            }
        },
        // 新增
        handleNew () {
            this.dialogTitle = '新增收货地址'
            this.closeForm()
            this.dialogVisible = true
        },
        // 点击编辑按钮
        openEdit (item) {
            this.dialogTitle = '编辑收货地址'
            this.dialogVisible = true // 打开弹框
            this.form = item
            //地址回显
            let selected = TextToCode[this.form.province][this.form.city][this.form.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.form.area = arr
        },
        handleAddressChange () {
            if(this.form.area != null && this.form.area.length != 0) {
                let addArr = this.form.area
                let province = CodeToText[addArr[0]]
                let city = CodeToText[addArr[1]]
                let county = CodeToText[addArr[2]]
                this.form.province = province
                this.form.city = city
                this.form.county = county
                this.form.detailAddress = province + city + county
            }
        },
        setDefault (form) {
            setDefaultAddress({ addressId: form.addressId }).then(res => {
                if (res === '设置默认地址成功' || res === '操作成功') {
                    this.$message.success('设置成功')
                    this.getTableData()
                    this.dialogVisible = false
                }
            })
        },
        // 获取列表数据
        async getTableData () {
            getList().then(res => {
                this.list = res.list
            })

        },
        // 新增收货地址
        handleCreate () {
            create(this.form).then(res => {
                if (res.message == '操作成功' || res.message == '添加地址成功！') {
                    this.$message.success('新增成功')
                    this.getTableData()
                    this.closeForm()
                    this.dialogVisible = false
                }
            })
        },
        // 编辑收货地址
        handleEdit () {
            update(this.form).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('操作成功')
                    this.getTableData()
                    this.closeForm()
                    this.dialogVisible = false
                }
            })
        },
        // 删除地址
        handleDelete (id) {
            this.clientPop('info', '您确定要删除该信息吗？', async () => {
                del({ id }).then(res => {
                    if(res.code == 200) {
                        this.$message.success('操作成功')
                    }
                    this.getTableData()
                })
            })
        },
        // 保存
        onSave () {
            this.$refs['form'].validate(valid => {
                if(valid) {
                    if (this.dialogTitle === '编辑收货地址') {
                        this.handleEdit()
                    }else {
                        this.handleCreate()
                    }
                }
            })
        },
    },
}
</script>
<style scoped lang="scss">
main {
    border: 1px solid rgba(229, 229, 229, 1);
}
.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);
    position: relative;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }

    .msg {
        margin-left: 20px;
        font-size: 12px;
        color: #808080;

        span {
            color: #216EC6;
        }
    }

    .add-addr {
        width: 120px;
        height: 30px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(33, 110, 198, 1);
        // border: 1px solid rgba(33, 110, 198, 1);
        justify-content: center;
        position: absolute;
        top: 10px;
        right: 19px;
        user-select: none;

        &:active {
            color: #fff;
            background-color: rgba(33, 110, 198, 1);
        }
    }
}

.list-box {
    height: 638px;
    padding: 30px 29px 0 30px;
    overflow: auto;

    &::-webkit-scrollbar {
        display: none;
    }

    .item {
        height: 269px;
        border: 1px solid rgba(230, 230, 230, 1);

        .name {
            height: 45px;
            padding-left: 20px;
            border-bottom: 1px solid rgba(230, 230, 230, 1);
            background: #FAFAFA;
            position: relative;

            span {
                font-size: 18px;
                color: #333;
            }

            img {
                width: 16px;
                height: 16px;
                margin-left: 10px;
                cursor: pointer;
            }

            .default,
            .setDefault {
                height: 24px;
                line-height: 24px;
                position: absolute;
                top: 11px;
                right: 20px;
                cursor: pointer;
            }

            .default {
                width: 95px;
                color: #fff;
                background-color: #EF841C;
                justify-content: center;

                img {
                    width: 14px;
                    height: 14px;
                    margin: 0 5px 0 0;
                }
            }

            .setDefault {
                color: #216EC6;
            }
        }

        .info {
            position: relative;

            .mb20>span:first-child {
                width: 70px;
                text-align: right;
                color: #999;
                display: block;
            }

            .actions {
                color: #d8d8d8;
                position: absolute;
                right: 20px;
                bottom: 36px;

                span {
                    color: #216EC6;
                    cursor: pointer;
                }
            }
        }
    }
}

// 弹框
/deep/ .el-dialog {
    width: 800px;
    height: 744px;

    .dialog-body {
        width: 500px;
        padding-top: 30px;

        .el-form-item {
            margin-bottom: 14px;
            &:last-of-type {margin-bottom: 20px;}
        }

        .el-form-item__label {
            // height: 14px;
            // margin-bottom: 20px;
            padding-bottom: 0;
            color: #999;
        }

        .el-input__inner {
            width: 300px;
            height: 35px;
            border: 1px solid rgba(217, 217, 217, 1);
            border-radius: 0;
        }
        .address .el-input__inner {width: 500px;}
        .tel {
            .el-form-item__content {
                display: flex;
                span {
                    margin-right: 10px;
                    color: #333;
                }
            }
            .el-input, .el-input__inner {width: 266px;}
        }
    }
    button {
        width: 80px;
        height: 40px;
        font-size: 16px;
        color: #fff;
        background-color: #216EC6;
    }
}
</style>