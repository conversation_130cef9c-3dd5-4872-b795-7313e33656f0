<template>
    <div class="toolBox p20 mt20">
        <div class="toolList dfb mt30">
            <div class="toolItem pointer dfc" style="flex-direction: column" v-for="(item, i) in toolList" :key="i" @click="redirectPage(item.path)">
                <img :src="item.img" alt="" />
                <div class="mt10">{{ item.name }}</div>
            </div>
        </div>
    </div>
</template>
<script>
import { materialControlLink } from '../../../assets/links'
export default {
    data () {
        return {
            toolList: [
                {
                    name: '物资采购平台',
                    img: require('../../../assets/images/img/ico_htgl.png'),
                    path: materialControlLink
                },
                {
                    name: '后台管理平台',
                    img: require('../../../assets/images/img/ico_ds.png'),
                    path: '/platform'
                },
                {
                    name: '店铺管理平台',
                    img: require('../../../assets/images/img/ico_grzx.png'),
                    path: '/shopManage'
                },
                {
                    name: '供应商系统',
                    img: require('../../../assets/images/img/ico_pcwp.png'),
                    path: '/supplierSys'
                },
                {
                    name: '个人中心',
                    img: require('../../../assets/images/img/ico_4.png'),
                    path: '/user'
                },
                // {
                //     name: '其他栏目',
                //     img: require('../../../assets/images/img/ico_3.png'),
                // },
                // {
                //     name: '其他栏目',
                //     img: require('../../../assets/images/img/ico_x3.png'),
                // },
                // {
                //     name: '其他栏目',
                //     img: require('../../../assets/images/img/ico_x4.png'),
                // },
            ],
        }
    },
    mounted () {
    },
    methods: {
        redirectPage (path) {
            window.open(path, '_blank')
        },
    }
}
</script>
<style scoped lang="scss">
.toolBox {
    width: 100%;
    height: 174px;
    background: #fff;

    .toolList {
        padding: 0 98px;

        .toolItem {
            img {
                background: #ff565a;
                border-radius: 10px;
                width: 50px;
                height: 50px;
            }

            div {
                font-size: 14px;
                color: #333333;
                text-align: center;
                font-weight: 400;
            }
        }
    }
}
</style>