import service from '@/utils/request'

const { httpPost, httpGet } = service
const sendMessageApi = params => {
    return httpPost({
        url: '/materialMall/stationMessage/createBatch',
        params
    })
}
const getList = params => {
    return httpPost({
        url: '/materialMall/stationMessageReceive/listByEntity',
        params
    })
}
const del = params => {
    return httpGet({
        url: '/materialMall/stationMessage/delete',
        params
    })
}
const getMessageList = params => {
    return httpPost({
        url: '/materialMall/platform/stationMessage/listByEntity',
        params
    })
}
const receivePageList = params => {
    return httpPost({
        url: '/materialMall/stationMessageReceive/listReceiveByMgId',
        params
    })
}
export {
    sendMessageApi,
    getList,
    del,
    getMessageList,
    receivePageList
}