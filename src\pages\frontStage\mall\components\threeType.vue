<template>
  <div class="pageHeader">
    <!-- 分类 -->
    <div class="flBox" :class="{ 'flBox--expanded': showFl_right }">
      <div class="flContent" @mouseleave="close">
        <div class="flContent_left" v-show="showFl_left">
          <div
            @click="onTreeClick(item)"
            @mouseover="switch1(i)"
            :style="{
              background:
                flBox_left_current === i ? 'rgba(232, 240, 249, 1)' : '',
            }"
            class="itemDiv dfb"
            v-for="(item, i) in flBox_left_list"
            :key="i"
          >
            <div :style="{ color: typeObj.classPath2.indexOf(item.classPath) > -1 ? '#226fc7' : ''}">
              {{ item.className }}
            </div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
        <div
          class="flContent_right"
          v-if="showFl_left && showFl_right && flBox_right_list.length > 0"
        >
          <div class="item df" v-for="(item, i) in flBox_right_list" :key="i">
            <div
              style="cursor: pointer"
              @click="onTreeClick(item)"
              class="title" :style="{ color: typeObj.classPath2.indexOf(item.classPath) > -1 ? '#226fc7' : ''}"
            >
              {{ item.className }}
            </div>
            <img src="@/assets/images/img/a9.png" alt="" />
            <div class="taglist">
              <div
                @click="onTreeClick(item1)"
                v-for="(item1, j) in item.arr"
                :key="j" :style="{ color: typeObj.classPath2.indexOf(item1.classPath) > -1 ? '#226fc7' : ''}"
              >
                {{ item1.className }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCategoryTree } from '@/api/frontStage/productCategory'
import { getCategoryColumns } from '@/api/frontStage/floor'
// import { getPublishList } from '@/api/platform/content/adImg'
export default {
    name: 'pageHeader',
    props: {
        typeObj: {
            type: Object,
            // eslint-disable-next-line vue/require-valid-default-prop
            default: {
                classId: '', className: '', classPath: '', classPath2: ''
            },
        }
    },
    data () {
        return {
            activeColor: '#F6353B',
            backUrl: null,
            menuList: [],
            showFl_left: true,
            showFl_right: false,
            flBox_left_current: -1,
            // 分类
            flBox_left_list: [],
            flBox_right_list: [],
            init: {
                productType: 0,
                mallType: 0,
            },
        }
    },
    watch: {},
    computed: {},
    created () {
        // 获取分类
        this.getProductCategory()
        // 获取栏目
        this.getCategoryColumnsM()
    },
    methods: {
        // 树点击
        onTreeClick ({ classId, classPath }) {
            let typeObj = { 'classId': classId, 'classPath': classPath.replaceAll('/', ' > '), 'classPath2': classPath }
            this.$emit('onTabChange', typeObj)
            this.close()
            // let routeData = this.$router.resolve({
            //     path: '/mFront/productList',
            //     query: { classId, classPath: classPath.replaceAll('/', ' > ') },
            // })
            // window.open(routeData.href, '_blank')
        },
        // 获取栏目
        getCategoryColumnsM () {
            getCategoryColumns({ size: 0 }).then(res => (this.menuList = res))
        },
        // 获取分类
        getProductCategory () {
            let parses = {
                productType: this.init.productType,
                mallType: this.init.mallType,
            }
            getCategoryTree(parses).then(res => {
                this.flBox_left_list = res
                this.flBox_right_list = []
            })
        },
        // 分类悬浮
        switch1 (i) {
            this.flBox_left_current = i
            this.getRightCateGory() // 渲染分类右边
            this.showFl_right = this.flBox_right_list.length !== 0
        },
        // 获取3级分类
        getRightCateGory () {
            this.flBox_right_list = []
            let arr = this.flBox_left_list[this.flBox_left_current]
            if (arr.children != null) {
                arr.children.forEach(t2 => {
                    let obj = {}
                    obj.classId = t2.classId
                    obj.className = t2.className
                    obj.classPath = t2.classPath
                    if (t2.children == null) {
                        obj.arr = []
                    } else {
                        obj.arr = []
                        t2.children.forEach(t => {
                            let t3 = {}
                            t3.classId = t.classId
                            t3.className = t.className
                            t3.classPath = t.classPath
                            obj.arr.push(t3)
                        })
                    }
                    this.flBox_right_list.push(obj)
                })
            }
        },
        close () {
            this.showFl_right = false
            this.flBox_left_current = -1
        },
    },
}
</script>

<style scoped lang="scss">
.pageHeader {
  width: 100%;z-index: 9;
  position: relative;
  .flBox {
    width: 100%;
    &.flBox--expanded {
      z-index: 10000;
    }
    .flContent {
      position: relative;
      margin: 0 auto;
      .flContent_left {
        overflow: auto;
        &::-webkit-scrollbar {
          display: none;
        }
        .itemDiv {
          cursor: pointer;color: #333;
          height: 47px;display: inline-flex;align-items: center;
          padding: 0 20px;
          .dfa {
            div {
              color: #fff;
              font-size: 18px;
            }
          }
          i {
            color: #333;margin-left: 5px;
          }
        }
      }
      .flContent_right {
        padding: 30px;
        background: #fff;
        overflow: auto;max-height: 400px;
        position: absolute;top: 47px;left: -108px;width: calc(100% + 108px);
        border: 1px solid rgba(33, 110, 198, 1);
        .item {
          width: 100%;
          margin-bottom: 16px;
          img {
            width: 14px;
            height: 14px;
            margin: 4px 15px 0;
            flex-shrink: 0;
          }
          .title {
            font-size: 14px;
            flex-shrink: 0;
          }
          .taglist {
            width: 100%;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            // padding-bottom: 16px;
            border-bottom: 1px rgba(204, 204, 204, 1) dashed;
            div {
              font-size: 14px;
              font-weight: 400;
              color: rgba(51, 51, 51, 1);
              padding: 0 10px;
              line-height: 18px;
              border-left: solid 1px rgba(204, 204, 204, 1);
              margin-bottom: 12px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}
</style>

<style scoped>
.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}
.el-carousel :deep(.el-carousel__arrow) {
  display: block !important;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  z-index: 999;
}

.dropdown-content {
  left: 0;
  top: 60px;
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 172px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 10002;
}

.dropdown-content a {
  color: black;
  /* padding:-6px 16px; */
  text-decoration: none;
  display: block;
}
.dropdown-content a:hover {
  background-color: #edf0f3;
}
</style>