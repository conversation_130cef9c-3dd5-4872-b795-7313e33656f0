<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn" style="min-width: 490px;">
                            <el-button :class="activeTab == -1 ? 'active' : ''" @click="checkActiveTab(-1)">全部订单
                            </el-button>
                            <el-button :class="activeTab == 6 ? 'active' : ''" @click="checkActiveTab(6)">待发货
                            </el-button>
                            <el-button :class="activeTab == 8 ? 'active' : ''" @click="checkActiveTab(8)">已发货
                            </el-button>
                            <!--                            <el-dropdown style="margin-left: 10px" trigger="click" @command="btnClick">-->
                            <!--                                <el-button type="primary">-->
                            <!--                                    更多操作<i class="el-icon-arrow-down el-icon&#45;&#45;right"/>-->
                            <!--                                </el-button>-->
                            <!--                                <el-dropdown-menu slot="dropdown">-->
                            <!--                                    <el-dropdown-item command="shipping">待收货</el-dropdown-item>-->
                            <!--                                    <el-dropdown-item command="finish">已完成</el-dropdown-item>-->
                            <!--                                </el-dropdown-menu>-->
                            <!--                            </el-dropdown>-->
<!--                            <el-select v-model="selectedVal" value-key="" @change="handleFilter"-->
<!--                                       style="margin-left: 3px">-->
<!--                                <el-option v-for="item in selectOptions"-->
<!--                                           :key="item.value"-->
<!--                                           :label="item.label"-->
<!--                                           :value="item.value">-->
<!--                                </el-option>-->
<!--                            </el-select>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-input type="text" placeholder="订单项/商店名称" v-model="keyword" @change="onSearch"><img
                            src="@/assets/search.png" slot="suffix" @click="onSearch"/></el-input>
                        <!--                        <div class="adverse">-->
                        <!--                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>-->
                        <!--                        </div>-->
                    </div>
                </div>
            </div>
                <!--            表格-->
                <div class="e-table" v-loading="isLoading" :style="{ width: '100%'} ">
                    <el-table  style="min-height: 670px;" class="table"
                               :height="rightTableHeight" :data="list"
                               border
                               highlight-current-row
                              @selection-change="selectionChangeHandle"
                             >
                        <el-table-column type="selection" header-align="center" align="center"
                                         width="50"></el-table-column>
                        <el-table-column label="序号" type="index" width=""></el-table-column>
                        <el-table-column label="订单号" width="" prop="orderNum">
                            <template slot-scope="scope">
                                <span class="action"
                                      @click="handleViewDetail(scope.row.orderNum)">{{ scope.row.orderNum }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="订单项" width="" prop="orderItemId"></el-table-column>
                        <el-table-column label="商品名称" width="" prop="title">
                        </el-table-column>
                        <el-table-column label="购买数量" width="" prop="quantity">
                        </el-table-column>
                        <el-table-column label="状态" width="" prop="status">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.status==0">已申请</el-tag>
                                <el-tag v-if="scope.row.status==1">已申请</el-tag>
                                <el-tag v-if="scope.row.status==2">已申请</el-tag>
                                <el-tag v-if="scope.row.status==3">已确认</el-tag>
                                <el-tag v-if="scope.row.status==4">待签订合</el-tag>
                                <el-tag v-if="scope.row.status==5">已签合同</el-tag>
                                <el-tag v-if="scope.row.status==6">待发货</el-tag>
                                <el-tag v-if="scope.row.status==7">已关闭</el-tag>
                                <el-tag v-if="scope.row.status==8">已发货</el-tag>
                                <el-tag v-if="scope.row.status==9">待收货</el-tag>
                                <el-tag v-if="scope.row.status==10">已完成</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="收货人" width="" prop="receiver"></el-table-column>

<!--                        <el-table-column label="操作" width="">-->
<!--                            <template slot-scope="scope">-->
<!--                                <div class="pointer" style="color: #226fc7;" v-if="scope.row.status == 10">-->
<!--                                    <span v-if="scope.row.invoiceState==1">已申请发票</span>-->
<!--                                    <span v-else @click="invoiceApply(scope.row)">-->
<!--                                    申请发票-->
<!--                                </span>-->
<!--                                </div>-->
<!--                                &lt;!&ndash;&lt;!&ndash;                           <span v-if="scope.row.status == 10||scope.row.status==6">&ndash;&gt;&ndash;&gt;-->
<!--                                &lt;!&ndash;&lt;!&ndash;                           <span v-if="(scope.row.returnState==null||scope.row.returnState==0)&&scope.row.productType!=10 " @click="orderReturn(scope.row)" >申请退货</span>&ndash;&gt;&ndash;&gt;-->
<!--                                &lt;!&ndash;&lt;!&ndash;                                <el-button type="primary" v-show="scope.row.status==6&&scope.row.productType==10&&(scope.row.returnState==null||scope.row.returnState==0)" @click="orderReturn(scope.row)" >申请退货</el-button>&ndash;&gt;&ndash;&gt;-->
<!--                                &lt;!&ndash;&lt;!&ndash;                                <el-button type="primary" v-show="scope.row.returnState=='1'" >审核中</el-button>&ndash;&gt;&ndash;&gt;-->
<!--                                &lt;!&ndash;&lt;!&ndash;                                <el-button type="primary" style="color: #d43030;" v-show="scope.row.returnState=='2'"   >退货中</el-button>&ndash;&gt;&ndash;&gt;-->
<!--                                &lt;!&ndash;&lt;!&ndash;                                <el-button type="primary" v-show="scope.row.returnState=='3'"  >退货失败</el-button>&ndash;&gt;&ndash;&gt;-->
<!--                                &lt;!&ndash;&lt;!&ndash;                                <el-button type="primary" style="color: limegreen;" v-show="scope.row.returnState=='4'"  >退货失败</el-button>&ndash;&gt;&ndash;&gt;-->
<!--                                &lt;!&ndash;&lt;!&ndash;                           </span>&ndash;&gt;&ndash;&gt;-->
<!--                            </template>-->
<!--                        </el-table-column>-->
                    </el-table>
                </div>
                <!-- 分页器 -->
                <ComPagination :total="pages.totalCount" :limit="20"
                               :pageSize.sync="pages.pageSize"
                               :currentPage.sync="pages.currPage"
                               @currentChange="currentChange"
                               @sizeChange="sizeChange"/>
                <!--            分页-->
                <!--            <Pagination v-show="list != null || list.length !== 0" :total="pagination.total"-->
                <!--                        :pageSize.sync="pagination.pageSize" :currentPage.sync="pagination.currentPage"-->
                <!--                        @currentChange="currentChange" @sizeChange="sizeChange" />-->
        </div>
    </div>
</template>

<script>
// import pagination from '@/pages/frontStage/components/pagination'
import ComPagination from '@/components/pagination/pagination.vue'
import { getUserOrderPageList } from '@/api/frontStage/order'
import $ from 'jquery'
import { throttle } from '@/utils/common'

export default {
    // components: { pagination },
    components: {
        ComPagination
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    data () {
        return {
            formLoading: false,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            viewList: true,
            dataListSelections: '',
            isLoading: false,
            showLoading: false,
            activeTab: -1,
            selectedVal: 0,
            keyword: null,
            state: null,
            // selectOptions: [
            //     { label: '近一个月订单', value: 0 },
            //     { label: '近三个月订单', value: 1 },
            //     { label: '近半年订单', value: 2 },
            //     { label: '全部订单', value: 3 },
            // ],
            list: [],
            pages: {
                currPage: 1, //当前页
                pageSize: 20, // 显示数量
                totalCount: 20
            },
            destination: 2,
        }
    },
    watch: {
        activeTab (num) {
            if (num == -1) {
                this.state = null
            }
            if (num == 0) {
                this.state = 0
            }
            if (num == 6) {
                this.state = 6
            }
            if (num == 7) {
                this.state = 7
            }
            if (num == 8) {
                this.state = 8
            }
            if (num == 9) {
                this.state = 9
            }
            if (num == 10) {
                this.state = 10
            }
            this.getUserOrderPageListM()
        },
    },
    created () {
        this.getUserOrderPageListM()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    methods: {

        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // btnClick (command) {
        //     let actions = {
        //         'stop': () => this.changePublishState(2),
        //         'del': () => this.handleDelete(),
        //         'changeSort': () => this.changeSortValue(),
        //     }
        //     actions[command]()
        // },
        // 标签点击
        checkActiveTab (num) {
            this.activeTab = num
        },
        invoiceApply (item) {
            this.$router.push(
                {
                    path: '/performanceManage/individualApply',
                    name: 'performanceManageIndividualApply',
                    params: {
                        row: item
                    }
                })
        },
        // orderReturn (item) {
        //     this.$router.push(
        //         { path: '/user/refund/unshipped',
        //             name: 'refundApply',
        //             params: {
        //                 row: item
        //             }
        //         })
        // },
        getLastMonth () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if (month - 1 <= 0) { //如果是1月，年数往前推一年<br>
                dateObj.last = (year - 1) + '-' + 12 + '-' + day
            } else {
                let lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate()
                if (lastMonthDay < day) {    // 1个月前所在月的总天数小于现在的天日期
                    if (day < nowMonthDay) {        //当前天日期小于当前月总天数
                        dateObj.last = year + '-' + (month - 1) + '-' + (lastMonthDay - (nowMonthDay - day))
                    } else {
                        dateObj.last = year + '-' + (month - 1) + '-' + lastMonthDay
                    }
                } else {
                    dateObj.last = year + '-' + (month - 1) + '-' + day
                }
            }
            return dateObj
        },
        getLast3Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if (month - 3 <= 0) { // 如果是1、2、3月，年数往前推一年
                var last3MonthDay1 = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate()    // 3个月前所在月的总天数
                if (last3MonthDay1 < day) {    // 3个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + last3MonthDay1
                } else {
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + day
                }
            } else {
                let last3MonthDay2 = new Date(year, (parseInt(month) - 3), 0).getDate()    //3个月前所在月的总天数
                if (last3MonthDay2 < day) {    //3个月前所在月的总天数小于现在的天日期
                    if (day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 3) + '-' + (last3MonthDay2 - (nowMonthDay - day))
                    } else {
                        dateObj.last = year + '-' + (month - 3) + '-' + last3MonthDay2
                    }
                } else {
                    dateObj.last = year + '-' + (month - 3) + '-' + day
                }
            }
            return dateObj
        },
        getLast6Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if (month - 6 <= 0) { // 年数往前推一年
                let last6MonthDay1 = new Date((year - 1), (12 - (6 - parseInt(month))), 0).getDate()    // 6个月前所在月的总天数
                if (last6MonthDay1 < day) {    // 6个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + last6MonthDay1
                } else {
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + day
                }
            } else {
                let last6MonthDay2 = new Date(year, (parseInt(month) - 6), 0).getDate()    //6个月前所在月的总天数
                if (last6MonthDay2 < day) {    //6个月前所在月的总天数小于现在的天日期
                    if (day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 6) + '-' + (last6MonthDay2 - (nowMonthDay - day))
                    } else {
                        dateObj.last = year + '-' + (month - 6) + '-' + last6MonthDay2
                    }
                } else {
                    dateObj.last = year + '-' + (month - 6) + '-' + day
                }
            }
            return dateObj
        },
        getUserOrderPageListM () {
            let params = {
                page: this.pages.currPage,
                limit: this.pages.pageSize,
                productType: 12
            }
            if (this.state >= 0) {
                params.state = this.state
            }
            if (this.selectedVal === 0) {
                let dateObj = this.getLastMonth()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if (this.selectedVal === 1) {
                let dateObj = this.getLast3Month()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if (this.selectedVal === 2) {
                let dateObj = this.getLast6Month()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if (this.keyword != null) {
                params.keywords = this.keyword
            }
            this.showLoading = true
            getUserOrderPageList(params).then(res => {
                this.list = []
                res.list.forEach(t => {
                    this.list.push({
                        productId: t.productId,
                        shipCounts: t.shipCounts,
                        shopId: t.shopId,
                        orderId: t.orderId,
                        orderNum: t.orderSn,
                        pictureUrl: t.productImg,
                        title: t.untitled,
                        returnCount: t.returnCount,
                        quantity: t.buyCounts,
                        receiver: t.receiverName,
                        price: t.actualAmount,
                        status: t.state,
                        productType: t.productType,
                        createTime: t.gmtCreate,
                        invoiceState: t.invoiceState,
                        returnState: t.returnState,
                        brand: t.brand,
                        orderItemId: t.orderItemId
                    })
                })

                this.pages.currPage = res.currPage
                this.pages.pageSize = res.pageSize
                this.pages.totalCount = res.totalCount
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        currentChange (index) {
            this.pages.currPage = index
            this.getUserOrderPageListM()
        },
        sizeChange (size) {
            this.pages.pageSize = size
            this.getUserOrderPageListM()
        },
        // 下拉筛选
        handleFilter () {
            this.getUserOrderPageListM()
        },
        // 搜索
        onSearch () {
            this.getUserOrderPageListM()
        },
        // 申请售后
        applyCustomerService (id) {
            console.log(id)
        },
        // 跳转订单详情页面
        handleViewDetail (id) {
            this.$router.push({ path: '/performanceManage/synthesisMaterialDetail', query: { orderSn: id } })
        },
        // 评价订单
        handleOrderReview (id) {
            console.log(id)
        },
        // 订单付款
        handleOrderSettlement (id) {
            console.log(id)
        },
        // 取消订单
        cancelOrder (id) {
            console.log(id)
        },
        // 再次购买
        handlePurchase (id) {
            console.log(id)
        },
        // 确认收货
        handleOrderReceive (id) {
            console.log(id)
        },
    },
}
</script>
<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}
.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}
</style>

