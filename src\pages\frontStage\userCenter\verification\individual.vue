<template>
    <main>
        <div class="list-title dfa mb20">个人认证</div>
        <div class="content">
            <el-form :model="form" ref="form" :rules="rules" label-width="140px" :inline="false">
                <el-row>
                    <el-col class="idCol" :span="11" :offset="0">
                        <el-form-item label="身份证人像面照：" prop="identityCardFace">
                            <el-upload class="identityUpload face" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1, 2)"
                                :show-file-list="false" :before-upload="handleBeforeUpload">
                                <img class="identityUpload" v-if="form.identityCardFace" :src=" form.identityCardFace" alt="">
                                <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col class="idCol" :span="11" :offset="2">
                        <el-form-item label="身份证国徽面照：" prop="identityCardBadge">
                            <el-upload class="identityUpload badge" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2, 2)"
                                :show-file-list="false" :before-upload="handleBeforeUpload">
                                <img class="identityUpload" v-if="form.identityCardBadge" :src=" form.identityCardBadge" alt="">
                                <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11">
                        <el-form-item label="手机号码：" prop="tel">
                            <el-input clearable v-model="form.tel" placeholder="请输入手机号码"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="2">
                        <el-form-item class="verification" label="验证码：" prop="verification">
                            <el-input clearable v-model="form.verification" placeholder="请输入短信验证码"></el-input>
                            <div class="getCode">获取短信验证码</div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11">
                        <el-form-item label="姓名：" prop="name">
                            <el-input clearable v-model="form.name" placeholder="请输入姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="2">
                        <el-form-item label="身份证号码：" prop="idCode">
                            <el-input clearable v-model="form.idCode" placeholder="请输入身份证号码"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="btns center dfb">
                <button @click="$router.go(-1)">返回</button>
                <button @click="onSubmit">提交</button>
            </div>
        </div>
    </main>
</template>
<script>
import { previewFile, uploadFile } from '@/api/platform/common/file'
export default {
    data () {
        return {
            form: {
                identityCardFace: '',
                identityCardBadge: '',
                tel: '',
                verification: '',
                name: '',
                idCode: '',
            },
            rules: {
                identityCardFace: { required: true, message: '请上传身份证人像面照片', trigger: 'blur' },
                identityCardBadge: { required: true, message: '请上传身份证国徽面照片', trigger: 'blur' },
                tel: { required: true, min: 11, max: 11, message: '请输入正确的电话号码', trigger: 'blur' },
                verification: { required: true, min: 6, max: 6, message: '请输入正确的验证码', trigger: 'blur' },
                name: { required: true, min: 2, message: '请输入正确的姓名', trigger: 'blur' },
                idCode: { required: true, min: 18, max: 18, message: '请输入正确的身份证号码', trigger: 'blur' }
            }
        }
    },
    created () {},
    mounted () {},
    methods: {
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < 10
            if(sizeOk > 10) {
                this.$message.error('上传的图片大小不能超过10MB!')
            }
            return sizeOk
        },
        // 上传身份证
        async uploadIdentity (params, num, type) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            if (num === 1) {
                uploadFile(form).then(res => {
                    if (type == 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.form.identityCardFace = url
                        })
                        this.form.identityCardFace = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.form.identityCardFaceId = res[0].recordId
                    }
                    if (type == 2) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.form.identityCardFace = url
                        })
                        this.form.identityCardFace = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.form.identityCardFaceId = res[0]
                    }
                })
            }
            if (num === 2) {
                uploadFile(form).then(res => {
                    if (type === 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.form.identityCardBadge = url
                        })
                        this.form.identityCardBadge = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.form.identityCardBadgeId = res[0]
                    }
                    if (type === 2) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.form.identityCardBadge = url
                        })
                        this.form.identityCardBadge = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.form.identityCardBadgeId = res[0].recordId
                    }
                })
            }
        },
        onSubmit () {
            this.$refs['form'].validate(valid => {
                if(valid) {
                    // 校验成功执行的代码
                }
            })
        },
    },
}
</script>
<style scoped lang="scss">
main>div{
    height: 100%;
    border: 1px solid rgba(230, 230, 230, 1);
}
.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
.content {
    height: 500px;
    padding: 30px 31px;
    /deep/ .el-input__inner {
        width: 350px;
        height: 50px;
        font-size: 16px;
        border: 1px solid rgba(204,204,204,1);
        border-radius: 0;
    }
    /deep/ .verification {
        .el-form-item__content {display: flex;}
        .el-input, .el-input__inner {
            width: 195px;
            margin-right: 15px;
        }
        .getCode {
            width: 140px;
            height: 50px;
            font-size: 16px;
            line-height: 50px;
            text-align: center;
            color: #216EC6;
            border: 1px solid rgba(33,110,198,1);
            cursor: pointer;
            user-select: none;
            &:active {
                color: #fff;
                background-color: #216EC6;
            }
        }
    }
    /deep/ .el-col-11 {
        width: 490px;
        &.el-col-offset-2 {
            margin-left: 60px;
        }
    }
    /deep/ .idCol {
        height: 100px;
        margin-bottom: 20px;
    }
    /deep/ .el-col:not(.idCol) {height: 75px;}
    /deep/ .el-form-item__label {
        line-height: 50px;
        padding-right: 0;
        font-size: 16px;
        color: #333;
    }
    .identityUpload {
        width: 160px;
        height: 100px;
    }
    .btns {
        width: 350px;
        margin-top: 25px;
        button {
            width: 160px;
            height: 50px;
            font-size: 22px;
        }
        button:first-child {
            color: rgba(33,110,198,1);;
            border: 1px solid rgba(33,110,198,1);
            background-color: #fff;
        }
        button:last-child {
            color: #fff;
            background-color: rgba(33,110,198,1);
        }
    }
}
</style>