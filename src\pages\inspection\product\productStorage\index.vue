<template>
    <div class="base-page" v-if="showSetOrg">
        <!-- <top-step :stepInfo="steps" /> -->
        <div class="left" :style="{ height: '100%' }">
            <select-material-class ref="materialClassRef" @addEvent="onAddMaterialClass" @modifyEvent="onModifyMaterialClass"
                @delEvent="onDelMaterialClass" :data="tableData" />
        </div>
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="onAddTopLevel" class="btn-greenYellow">添加顶级</el-button>
                            <el-button type="primary" @click="onAddMaterial"
                                :disabled="!currentClass || (currentClass && currentClass.isDetailed === 0)" class="btn-greenYellow">添加明细</el-button>
                            <!-- <span class="prompt" v-if="!currentClass">请先选择物资类别</span> -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="e-table" :style="{ width: '100%' }">
                <div class="detail-info" :style="{ height: rightTableHeight + 'px' }">
                    <div class="e-form">
                        <div class="tabs" style="margin-bottom:120px">
                            <div class="tabs-title">基本信息</div>
                            <div style="width: 100%" class="form">
                                <el-form :model="formData" label-width="" ref="rulesBase" class="demo-ruleForm">
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="分类名称：" prop="className">
                                                <el-input v-model="formData.className" autocomplete="off"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item width="150px" label="排序：">
                                                <el-input v-model="formData.sort" type="number" placeholder="" size="normal"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="备注信息：">
                                                <el-input type="textarea" v-model="formData.classDescribe" autocomplete="off"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </div>
                        </div>
                    </div>
                    <div class="buttons">
                        <el-button type="primary" size="small" style="background: #2e61d7" @click="onSave('编辑')">保存</el-button>
                        <el-button size="small" @click="handleClose">取消</el-button>
                    </div>
                </div>
            </div>
            <!-- <Pagination
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            /> -->
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- 分类 -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">{{ isAddTop ? (!formClass.billId ? '添加顶级' : '编辑顶级') : (!formClass.billId ? '添加子级' : '编辑类别') }}</div>
                <el-form ref="form" :model="formClass" label-width="150px">
                    <el-row>
                        <el-col :span="12" v-if="!isAddTop && formClass.parentClassId !== '0' && formClass.parentClassId !== ''">
                            <el-form-item label="上级类别名称" prop="parentClassName">
                                {{ formClass.parentClassName }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formClass.billId">
                            <el-form-item label="分类编号" prop="billNo">
                                {{ formClass.billNo }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="分类名称" prop="className">
                                <el-input v-model="formData.className" placeholder="请输入类别名称">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="类型" prop="classType">
                                <el-select v-model="formData.classType" placeholder="">
                                    <el-option v-for="item in levelForm" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="排序：">
                                <el-input v-model="formData.sort" type="number" placeholder="" size="normal"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input type="textarea" v-model="formData.classDescribe" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>

            <div class="footer">
                <div class="right-btn">
                    <el-button type="primary" size="mini" @click="onSave('新增')">保存</el-button>
                    <el-button type="primary" size="mini" class="btn-delete" @click="onDel">删除</el-button>
                    <el-button size="mini" @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置机构页面 -->
    <div class="base-page " v-else>
        <div class="right">
            <!-- <div class="e-form" style="padding: 0 10px 10px;">
                    <el-form ref="form" :model="formData" label-width="150px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="机构名称：">
                                    <el-input v-model="formData.orgName"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="状态：">
                                 <el-select v-model="formData.state" placeholder="请选择">
                                    <el-option
                                    v-for="item in options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                    </el-option>
                                </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div> -->
            <el-table :data="orgDataTable" max-height="100%" border ref="multipleTable" size="mini" style="width: 100%" row-key="orgId"
                :header-cell-style="{ background: '#fafafa' }">
                <!-- <el-table-column type="selection" width="55">
                        </el-table-column> -->
                <el-table-column type="selection" width="55">
                    <template slot-scope="{ row,$index}">
                        <el-checkbox @change="setRowIsSelect(row)" v-model="row.isCheck" v-if="$index >= 0"></el-checkbox>
                    </template>
                </el-table-column>

                <el-table-column key="orgName" prop="orgName" label="机构名称" width="700">

                </el-table-column>
                <el-table-column prop="orgType" label="机构类型" min-width="100" key="orgType">
                    <template slot-scope="scope">
                        <span>{{ ['', '集团', '分子公司', '', '经理部', '项目部', '股份', '事业部'][scope.row.orgType] }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="shortCode" label="机构编码" min-width="200" key="shortCode">
                </el-table-column>

            </el-table>
            <div class="footer">
                <div class="right-btn">
                    <el-button type="primary" size="mini" @click="onSave('unknown')">未知</el-button>
                    <el-button type="default" size="mini" @click="Close">取消</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios'
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '../../../components/selectMaterialClass'
// import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
import { getMaterialLs, getMaterialById, getMaterialDetail, addMaterial, modifyMaterial, delMaterial, addMaterialClass, modifyMaterialClass, delMaterialClass, saveOrUpdatePermission, getOrgClassId, getChildrenOrg } from '@/api/base/material'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapState, mapActions, mapMutations } from 'vuex'
// import { getOrgName } from '@/api/materials/materialsMarketPrice'
export default {
    components: {
        SelectMaterialClass,
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) {
                return this.screenHeight - 170
            }
            return this.screenHeight - 214
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            classType: '0',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            isAddTop: true, // 新增顶级
            showSetOrg: true, //设置使用机构
            searchKey: '',
            treeData: [],
            currentClass: null,
            currentRow: null,
            levelForm: [
                { value: 0, label: 'apple' }
            ],
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            classData: {},
            tableData: [],
            orgDataTable: [

            ],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            allData: [],
            mapObj: null,
            formClass: {
                'billId': '',
                'billNo': '',
                'className': '',
                'estimatedMaterialLock': 0,
                'founderId': '',
                'founderName': '',
                'gmtCreate': '',
                'gmtModified': '',
                'isCentralizedPurchasing': 0,
                'isDetailed': 0,
                'isEnable': 0,
                'levelPathId': '',
                'levelPathName': '',
                'materialType': 0,
                'parentClassId': '',
                'parentClassName': '',
                'unit': ''
            },
            steps: [
                { description: '合同申请', },
                { description: '合同签订', },
                { description: '合同变更申请', },
                { description: '合同变更签订', },
                { description: '合同终止', }
            ],
            formData: { parentId: null, className: '', classLevel: null, sort: 2, classDescribe: '', classType: 0 },
            currencyOptions: [],
            tabsName: 'baseInfo',
            tableData2: [],
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            idsTop: [],
            checkAllDetail: false,
            isIndeterminate: false,
            arr: [],
            Array: [],

        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        let data = JSON.parse(localStorage.getItem('vuex'))
        this.orgId = data.userInfo.orgInfo.orgId
        this.getTableData()
    },
    methods: {
        ...mapActions('equip', ['setUnitMeasur']),
        addSave () {},
        handleClose () {},
        Save () {
            this.showSetOrg = true
        },
        Close () {
            this.showSetOrg = true
        },
        // 展示分类信息
        classNodeClick (data) {
            this.formData = data
            console.log(data.parentId)
        },
        // 复选框选中事件
        selectFun (selection, row) {
            console.log('selection>>>', selection)
            this.setRowIsSelect(row)

        },
        // 复选框点击事件
        setRowIsSelect (row) {
            if (row.isCheck) {
                this.Array.push(row)
                this.Array.forEach(item => {
                    if (item.children) {
                        item.children.forEach(val => {
                            this.Array.push(val)
                        })
                    }
                })
            } else {
                const index = this.Array.findIndex(x => x.orgId === row.orgId)
                if (index !== -1) {
                    this.Array.splice(index, 1)
                }
            }
            //当点击父级点复选框时，当前的状态可能为未知状态，所以当前行状态设为false并选中，即可实现子级点全选效果
            // if (row.isCheck === null) {
            //     row.isCheck = false
            //     this.$refs.multipleTable.toggleRowSelection(row, true)
            // }
            let that = this
            // 选中父级时候，选中父级及全部子集
            function selectAllChildrens (data) {
                treeToList(data, item => {
                    item.isCheck = row.isCheck
                })
                // console.log('arr', arr)
                // arr.forEach(item=>{
                //     item.isCheck = row.isCheck
                // })
                // data.forEach(item => {
                //     item.isCheck = row.isCheck
                //     // that.$refs.multipleTable.toggleRowSelection(
                //     //     item,
                //     //     row.isCheck
                //     // )
                //     if (item.children && item.children.length) {
                //         selectAllChildrens(item.children)
                //     }
                // })
            }
            // 判断子集是否全部选中
            function getSelectStatus (selectStatuaArr, data) {
                data.forEach(childrenItem => {
                    selectStatuaArr.push(childrenItem.isCheck)
                    if (childrenItem.children && childrenItem.children.length) {
                        getSelectStatus(selectStatuaArr, childrenItem.children)
                    }
                })
                return selectStatuaArr
            }
            // 判断点击的是哪个节点
            function getLevelStatus (row) {
                //如果当前节点的parantId =0 并且有子节点，则为1
                //如果当前节点的parantId !=0 并且子节点没有子节点 则为3
                if (row.parentId == 0) {
                    if (row.children && row.children.length) {
                        return 1
                    } else {
                        return 4
                    }
                } else {
                    if (!row.children || !row.children.length) {
                        return 3
                    } else {
                        return 2
                    }
                }
            }
            let result = {}
            //获取明确的节点
            function getExplicitNode (data, parentId) {
                data.forEach(item => {
                    if (item.dtlId == parentId) {
                        result = item
                    }
                    if (item.children && item.children.length) {
                        getExplicitNode(item.children, parentId)
                    }
                })
                return result
            }
            // 选中的子集，来操作父节点
            function operateLastLeve (row) {
                //操作的是子节点  1、获取父节点  2、判断子节点选中个数，如果全部选中则父节点设为选中状态，如果都不选中，则为不选中状态，如果部分选择，则设为不明确状态
                let selectStatuaArr = []
                let item = getExplicitNode(
                    that.orgDataTable,
                    row.parentId
                )
                selectStatuaArr = getSelectStatus(
                    selectStatuaArr,
                    item.children
                )
                if (
                    // 全选
                    selectStatuaArr.every(selectItem => {
                        return true == selectItem
                    })
                ) {
                    item.isCheck = true
                    that.$refs.multipleTable.toggleRowSelection(item, true)
                } else if (
                    // 全不选
                    selectStatuaArr.every(selectItem => {
                        return false == selectItem
                    })
                ) {
                    item.isCheck = false
                    that.$refs.multipleTable.toggleRowSelection(item, false)
                } else {
                    item.isCheck = null
                }
                //则还有父级
                if (item.parentId != 0) {
                    operateLastLeve(item)
                }
            }
            //判断操作的是子级点复选框还是父级点复选框，如果是父级点，则控制子级点的全选和不全选

            //1、只是父级 2、既是子集，又是父级 3、只是子级
            let levelSataus = getLevelStatus(row)
            if (levelSataus == 1) {
                selectAllChildrens(row.children)
            } else if (levelSataus == 2) {
                selectAllChildrens(row.children)
                operateLastLeve(row)
            } else if (levelSataus == 3) {
                operateLastLeve(row)
            }
        },
        // 检测表格数据是否全选
        checkIsAllSelect () {
            this.oneProductIsSelect = []
            this.orgDataTable.forEach(item => {
                this.oneProductIsSelect.push(item.isCheck)
            })
            //判断一级产品是否是全选.如果一级产品全为true，则设置为取消全选，否则全选
            let isAllSelect = this.oneProductIsSelect.every(
                selectStatusItem => {
                    return true == selectStatusItem
                }
            )
            return isAllSelect
        },
        // 表格全选事件
        selectAllFun (selection) {
            let isAllSelect = this.checkIsAllSelect()
            this.orgDataTable.forEach(item => {
                item.isCheck = !isAllSelect
                this.selectFun(selection, item)
            })
        },
        //表格标题样式 当一级目录有为不明确状态时，添加样式，使其全选复选框为不明确状态样式
        headerRowClassName () {
            let oneProductIsSelect = []
            this.orgDataTable.forEach(item => {
                oneProductIsSelect.push(item.isCheck)
            })
            if (oneProductIsSelect.includes('')) {
                return 'indeterminates'
            }
            return ''
        },

        // //表格样式当当前行的状态为不明确状态时，添加样式，使其复选框为不明确状态样式
        rowClassNameFun ({ row }) {
            if (row.isCheck === null) {
                return 'indeterminates'
            }
        },
        ...mapMutations(['setSelectedInfo']),
        //显示设置机构页面
        setOrg () {
            showLoading()
            this.showSetOrg = false
            this.getOrgData()

            //先获取权限机构  如果有就回显
            if (this.formClass.billId == '') {
                console.log('第一次不回显')
            } else {
                let classId = this.formClass.billId
                getOrgClassId({ classId }).then(res => {
                    console.log('回显', res)
                    this.orgDataTable.forEach(item => {
                        if (!item.children) {
                            item.children = []
                        }
                        item.isCheck = true
                    })

                })
            }

        },
        //懒加载树形机构数据
        async getOrgData () {
            let orgId = this.orgId
            const data = await getChildrenOrg({ orgId })
            this.orgDataTable = parseList(data, 'orgId', 'parentId', '0')
            hideLoading()
        },

        //关闭设置机构页面
        setOrgClose () {
            this.showSetOrg = true
        },
        async getTableData () {
            axios({
                method: 'get',
                url: '/productCategoryTest/productCategory/listTree',
                params: { classType: this.classType }
            }).then(res => {
                console.log(res)
                if (res.data.code === 200) {
                    console.log(2)
                    this.tableData = res.data.data
                } else {
                    this.$message({
                        message: '获取分类列表失败，请重试！',
                        type: 'error',
                    })
                }
            })
            this.viewList = true
            // let data
            // if (params) {
            //     if (typeof params === 'object') {
            //         data = params.data
            //         if (params.mapObj) {
            //             this.mapObj = params.mapObj
            //         }
            //         console.log(data, this.mapObj)
            //     } else {
            //         data = params
            //     }
            //     this.currentClass = data
            // } else {
            //     data = this.currentClass
            // }

            // showLoading()
            // try {
            //     const resData = await getMaterialLs({
            //         classId: data ? data.billId : '0',
            //         materialName: this.searchKey,
            //         limit: this.paginationInfo.pageSize,
            //         page: this.paginationInfo.currentPage
            //     })
            //     // this.tableData = resData.list
            //     this.allData = JSON.parse(JSON.stringify(resData.list))
            //     this.paginationInfo.total = resData.totalCount
            //     this.currentRow = null
            //     hideLoading()
            // } catch (error) {
            //     hideLoading()
            // }
        },
        async queryTableData () {
            const resData = this.allData.filter(item => {
                return item.materialName.includes(this.searchKey)
            })
            this.tableData = resData
            this.paginationInfo.total = 0
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        onSearch (e) {
            let searchKey = e
            if (typeof e === 'object') {
                searchKey = this.searchKey
            }
            console.log('searchKey: ', searchKey)
            this.queryTableData()
        },
        onAdvanceSearch () {
            advanceSearch().then(res => {
                alert(JSON.stringify(res))
            })
        },
        goList () {
            this.viewList = true
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 添加顶级
        onAddTopLevel () {
            this.viewList = 'class'
            this.isAddTop = true
            this.emptyForm()
            this.formData.classLevel = 0
            this.formClass.isDetailed = 0
        },
        onAddLevel () {
            this.viewList = 'class'
            this.isAddTop = false
            // const levelPath = this.getLevelPath()
            let id = this.formData.classId
            console.log(id)
            console.log(this.formData.parentId)
            this.formClass.isLeaf = true
            // this.formClass.levelPathId = levelPath.id.join('/')
            // this.formClass.levelPathName = levelPath.name.join('/')
            this.formClass.parentClassId = this.currentClass.billId
            this.formClass.parentClassName = this.currentClass.className
        },
        // 新增物资明细
        onAddMaterial () {
            this.viewList = 'material'
            this.currentRow = null
            const levelPath = this.getLevelPath()
            for (let key in this.formData) {
                this.formData[key] = ''
            }
            this.formData.classId = this.currentClass.billId
            this.formData.className = this.currentClass.className
            this.formData.classIdPath = levelPath.id.join('/')
            this.formData.classNamePath = levelPath.name.join('/')
        },
        // 编辑物资明细
        async onEditMaterial (row) {
            this.currentRow = row
            this.viewList = 'material'
            const data = await getMaterialDetail({
                id: row.billId
            })
            this.formData = data
            console.log(row, data)
        },
        // 添加子级
        onAddMaterialClass (data) {
            // 设置 currentClass
            this.getMapObj(data)
            this.onAddLevel()
        },
        // 修改物资类别
        async onModifyMaterialClass (data) {
            const detail = await getMaterialById({
                id: data.billId
            })
            this.viewList = 'class'
            this.isAddTop = false
            this.formClass = detail
        },
        // 删除物资类别
        onDelMaterialClass (data) {
            this.viewList = true
            this.currentClass = data
            this.onDel()
        },
        // 保存
        onSave (str) {
            let url = '/productCategoryTest/productCategory/create'
            if(str === '编辑') {
                url = '/productCategoryTest/productCategory/update'
            }
            axios({
                method: 'post',
                url,
                data: this.formData
            }).then(res => {
                console.log(res.data)
                this.getTableData()
            })
            this.emptyForm()
            // if (this.viewList === 'class') {
            //     this.saveMaterialClass()
            // } else {
            //     this.saveMaterial()
            // }
        },
        async saveMaterialClass () {
            try {
                let str
                if (!this.formClass.billId) {
                    this.formClass.isDetailed = 1
                    const billId = await addMaterialClass(this.formClass)

                    if (this.isAddTop) {
                        this.formClass.parentClassId = '0'
                    }
                    this.formClass.billId = billId
                    str = 'add'
                    this.Array.forEach(item => {
                        const obj = {
                            'orgId': item.orgId,
                            'orgName': item.orgName,
                        }
                        this.arr.push(obj)
                    })
                    //保存或更新权限机构信息
                    saveOrUpdatePermission({
                        'classId': this.formClass.billId,
                        'className': this.formClass.className,
                        'orgList': this.arr
                    })

                } else {
                    await modifyMaterialClass(this.formClass)
                    str = 'modify'
                }
                hideLoading()
                this.clientPop('suc', '保存成功', () => {
                    // 刷新树
                    this.refreshTree(str)
                    this.getTableData()
                })
            } catch (err) {
                console.log(err.message)
                hideLoading()
            }
        },
        async saveMaterial () {
            try {
                if (!this.currentRow) {
                    await addMaterial(this.formData)
                } else {
                    await modifyMaterial(this.formData)
                }
                hideLoading()
                this.clientPop('suc', '保存成功', () => {
                    this.getTableData()
                })
            } catch (error) {
                hideLoading()
            }
        },
        onDel () {
            this.clientPop('info', `${this.viewList === 'class' ? '您确定要删除该类别吗？' : '您确定要删除该物资明细吗？'}数据删除后不可恢复，请谨慎操作！`, async () => {
                showLoading()
                try {
                    let id
                    if (this.viewList === 'class') {
                        // 点击编辑类时的删除按钮
                        id = this.formClass.billId
                        await delMaterialClass({
                            id
                        })
                    } else {
                        // 点击树删除图标
                        if (!this.currentRow) {
                            id = this.currentClass.billId
                            await delMaterialClass({
                                id
                            })
                        } else {
                            // 编辑物资明细删除按钮
                            id = this.currentRow.billId
                            await delMaterial({
                                id
                            })
                        }
                    }
                    hideLoading()
                    this.clientPop('suc', '删除成功', () => {
                        // 刷新树
                        if (this.viewList === 'class' || (this.viewList !== 'class' && !this.currentRow)) {
                            this.refreshTree('del')
                        }
                        this.getTableData()
                    })
                } catch (err) {
                    console.log(err.message)
                    hideLoading()
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        getLevelPath (id = [], name = [], curClass) {
            if (!curClass) {
                curClass = this.currentClass
            }
            id.unshift(curClass.billId)
            name.unshift(curClass.className)
            if (curClass.parentClassId !== '0' && curClass.parentClassId !== '') {
                return this.getLevelPath(id, name, this.mapObj[curClass.parentClassId])
            } else {
                return {
                    id,
                    name
                }
            }
        },
        getMapObj (data) {
            for (let key in this.formData) {
                this.formData[key] = null
            }
            this.formData.parentId = data.classId
            this.formData.classLevel = data.classLevel + 1
            if (!this.mapObj) {
                this.mapObj = this.$refs.materialClassRef.mapObj
            }
            if (!this.mapObj[data.billId]) {
                this.mapObj[data.billId] = data
            }
            this.currentClass = data
        },
        // 刷新树
        refreshTree (str) {
            const tree = this.$refs.materialClassRef
            // 点击编辑类别时的按钮
            if (this.viewList === 'class') {
                if (str === 'add') {
                    if (this.isAddTop) {
                        tree.refreshTreeAddTop(this.formClass)
                    } else {
                        tree.refreshTree(this.formClass, this.currentClass)
                    }
                } else if (str === 'modify') {
                    tree.refreshTreeModify(this.formClass, this.currentClass)
                } else if (str === 'del') {
                    tree.refreshTreeDel(this.formClass)
                }
            } else {
                // 直接点击树上的图标删除
                tree.refreshTreeDel(this.currentClass)
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-table__header-wrapper {
    .el-checkbox {
        display: none;
    }
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;
    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

.e-form {
    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}
</style>
