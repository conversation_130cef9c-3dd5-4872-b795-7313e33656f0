.el-menu {
  border: none;
}

.el-tree-node > .el-tree-node__children {
  overflow: visible;
}

.el-table .sort-caret.ascending {
  top: 10px;
}

.el-table .sort-caret.descending {
  display: none;
}

.el-form.inline_form .el-form-item {
  margin: 0 30px 10px 0;
}

.el-input-group--append .el-input__inner {
  height: 30px;
  line-height: 30px;
}

.el-input-group__append .el-button {
  padding: 0 10px;
  height: 30px;
  line-height: 30px;
}

.el-button {
  padding: 0 20px;
  height: 30px;
  line-height: 30px;
}

.el-button span {
  display: inline-block;
  vertical-align: text-bottom;
}

.el-button-group > .el-button {
  padding: 0 20px;
  height: 30px;
  line-height: 30px;
}

.el-menu--horizontal .el-menu .el-menu-item {
  height: 30px;
  line-height: 30px;
  background: #30417d;
  text-align: center;
  color: #fff;
  border-bottom: 1px #1d5abc solid;
}

.el-menu--horizontal .el-menu .el-menu-item:not(.is-disabled):hover {
  color: #3287e1;
  background: #1c2c64;
}

.el-menu--horizontal .el-menu .el-menu-item:last-child {
  border: none;
}

.el-menu--horizontal .el-menu .el-menu-item.is-active {
  color: #fff;
}

.el-menu--horizontal > .el-submenu .el-submenu__title:focus {
  background: none;
}

.el-menu--horizontal .el-menu--popup {
  padding: 0 0 5px;
  min-width: 126px;
}

.el-menu--horizontal .el-menu--popup-bottom-start {
  margin: 0;
}

.el-menu--horizontal.index_submenu .el-menu .el-menu-item {
  background: #306e7e;
  color: #d3e9f4;
  border: none;
}

.el-menu--horizontal.index_submenu .el-menu .el-menu-item:not(.is-disabled):hover {
  background: #245467;
  color: #fefefe;
}

.el-menu--horizontal.index_submenu .el-menu--popup {
  margin: 0 0 0 15px;
  padding: 0;
  min-width: 100px;
}

.el-table th {
  padding: 0;
}

.el-table td {
  padding: 5px 0;
}

.search_bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search_bar .el-tabs--top .el-tabs__item.is-top:last-child,
.search_bar .el-tabs--top .el-tabs__item.is-bottom:last-child,
.search_bar .el-tabs--bottom .el-tabs__item.is-top:last-child,
.search_bar .el-tabs--bottom .el-tabs__item.is-bottom:last-child,
.search_bar .el-tabs--top .el-tabs__item.is-top:nth-child(2),
.search_bar .el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
.search_bar .el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
.search_bar .el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2) {
  padding: inherit;
}

.search_bar .el-tabs__item {
  color: #d3e9f4;
}

.search_bar .el-tabs__item .tab_title {
  display: block;
  padding: 0 15px;
}

.search_bar .el-tabs__item:hover {
  color: #409EFF;
}

.right_bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.right_bar .el-radio-group {
  margin: 0 10px 0 0;
}

.right_bar .el-radio {
  margin: 0 10px 0 0;
}

.right_bar .el-radio__label {
  color: #d3e9f4;
}

.todo .ls li {
  float: left;
  display: flex;
  flex-direction: column;
  margin: 0 10px 10px;
  width: calc(33% - 20px);
  height: 200px;
  border: 1px #51B6C2 solid;
  color: #d3e9f4;
}

.todo .ls li .ls_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  height: 40px;
  border-bottom: 1px #51B6C2 solid;
}

.todo .ls li .ls_title .txt {
  color: #d3e9f4;
}

.todo .ls li .ls_title .status {
  display: block;
  padding: 5px 15px;
  color: #fff;
  background: #FF7000;
  border-radius: 5px;
}

.todo .ls li .desc {
  flex: 1;
  padding: 10px;
  border-bottom: 1px #51B6C2 solid;
}

.todo .ls li .ls_bottom {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 40px;
}

.todo .ls li .ls_bottom .date {
  color: #999;
}

.todo .ls li .ls_bottom .btn_box {
  margin: 0 10px;
}

.todo .table {
  padding: 0 10px;
  background: none;
  color: #d3e9f4;
}

.todo .table:before {
  content: none;
}

.todo .table thead {
  color: #d3e9f4;
}

.todo .table tr {
  background: #123964;
}

.todo .table tr.el-table__row--striped {
  background: #132d51;
}

.todo .table tr.el-table__row--striped td {
  background: none;
}

.todo .table td {
  font-size: 14px;
  border: none;
}

.todo .table .cell {
  height: 32px;
  line-height: 32px;
}

.todo .table .el-table__header tr, .todo .table .el-table__header th {
  background: none;
}

.todo .table .el-table__header th {
  font-size: 16px;
  border: none;
}

.todo .table .el-table__body tr:hover > td {
  background: #0a315b;
}

.todo .table .el-table__fixed::before, .todo .table .el-table__fixed-right::before,
.todo .table .el-table__body tr.hover-row > td {
  background: none;
}

.login {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.login .top {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login .top .logo {
  margin: 0 20px 0 0;
}

.login .top .txt {
  color: #182a52;
}

.login .top .txt .cn {
  height: 45px;
  overflow: hidden;
  font-size: 30px;
  text-align: justify;
}

.login .top .txt .cn span {
  display: inline-block;
  width: 100%;
}

.login .top .txt .en {
  font-size: 20px;
  color: #525f7d;
}

.login .bottom {
  flex: 1;
  background: url(../images/bg_03.png) center center no-repeat;
  background-size: cover;
}

.login .box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 500px;
  background: url(../images/bg_01.png) center no-repeat;
  background-size: cover;
}

.login .box .form {
  padding: 40px 70px 0 520px;
  width: 212px;
  height: 360px;
  background: url(../images/bg_02.png) center no-repeat;
  background-size: contain;
}

.login .box .form .el-input__inner {
  padding: 0;
  font-size: 20px;
  border: none;
  border-bottom: 2px #DBDBDB solid;
}

.login .box .form .title {
  margin: 0 0 30px;
}

.login .box .form .title .el-form-item__label {
  font-size: 22px;
}

.login .box .form .txt .el-form-item__label {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 12px;
}

.login .box .form .btn {
  margin: 50px 0 5px;
}

.login .box .form .btn .el-button {
  width: 100%;
  font-size: 16px;
  height: 40px;
  line-height: 40px;
  background: #1987E0;
  box-shadow: 0 2px 0 #D4D2D3;
}

.container {
  padding: 70px 0 0;
  min-height: calc(100% - 70px);
}

.container .top {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10;
  width: 100%;
  height: 70px;
  background: url(../images/bg_2.png) center 0 no-repeat;
  background-size: cover;
}

.container .top .posi_rela {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.container .top .up {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 3;
  display: flex;
  justify-content: space-between;
}

.container .top .up .center_txt {
  flex: 1;
  align-self: center;
  font-size: 14px;
  color: #fff;
}

.container .top .logo {
  flex: 1;
  display: flex;
  align-items: center;
  margin: 2px 0 3px 10px;
}

.container .top .logo .img {
  margin: 0 5px 0 0;
}

.container .top .logo .txt {
  color: #e9ebf1;
}

.container .top .logo .txt .cn {
  height: 22px;
  overflow: hidden;
  font-size: 15px;
  text-align: justify;
}

.container .top .logo .txt .cn span {
  display: inline-block;
  width: 100%;
}

.container .top .logo .txt .en {
  font-size: 12px;
  text-transform: uppercase;
}

.container .top .right {
  display: flex;
  padding: 0 22px 0 0;
  height: 35px;
}

.container .top .right .search .el-form-item {
  margin: 0;
}

.container .top .right .search .el-form-item .el-form-item__content {
  line-height: 35px;
}

.container .top .right .search .el-form-item .el-input-group {
  display: flex;
  border: 1px #e9e3e5 solid;
  border-radius: 5px;
}

.container .top .right .search .el-form-item .el-input__inner, .container .top .right .search .el-form-item .el-input-group__append {
  background: none;
  border: none;
}

.container .top .right .search .el-form-item .el-input__inner {
  padding: 0 5px 0 15px;
  width: 240px;
  height: 35px;
  line-height: 35px;
  font-size: 12px;
  color: #fff;
}

.container .top .right .search .el-form-item .el-input-group__append {
  display: flex;
  padding: 0;
  width: auto;
}

.container .top .right .search .el-form-item .el-button {
  margin: 0;
  padding: 5px 8px;
  color: #fff;
}

.container .top .right .icon_box {
  display: flex;
  justify-content: center;
  align-items: center;
}

.container .top .right .icon_box .user_name {
  font-size: 14px;
  color: #fff;
}

.container .top .right .icon_box .user_name span {
  margin: 0 10px;
}

.container .top .right .icon_box .icon {
  margin: 0 0 0 15px;
  width: 20px;
  cursor: pointer;
  color: #fff;
  font-size: 22px;
}

.container .top .down {
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  min-width: 1200px;
}

.container .top .down .el-menu {
  margin: 0 0 0 7px;
  background: none;
  border: none;
  box-sizing: content-box;
}

.container .top .down .el-menu > .el-menu-item {
  padding: 40px 5px 0;
  width: 112px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  border: none;
  box-sizing: content-box;
  background: url(../images/bg_0.png) center bottom no-repeat;
  background-size: contain;
}

.container .top .down .el-menu > .el-menu-item.is-active {
  background-image: url(../images/bg_1.png);
}

.container .top .down .el-menu--horizontal .el-menu-item:not(.is-disabled).is-active:hover {
  background-image: url(../images/bg_1.png);
}

.container .top .down .el-menu--horizontal > .el-submenu {
  padding: 40px 5px 0;
  height: 30px;
  line-height: 30px;
  background: url(../images/bg_0.png) center bottom no-repeat;
  background-size: contain;
}

.container .top .down .el-menu--horizontal > .el-submenu .el-submenu__title {
  height: 30px;
  line-height: 30px;
  color: #fff;
  border: none;
}

.container .top .down .el-menu--horizontal > .el-submenu .el-submenu__title i {
  color: #fff;
}

.container .top .down .el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  background: none;
}

.container .top .down .el-menu--horizontal > .el-submenu.is-active {
  background-image: url(../images/bg_1.png);
}

.container .top .down .user_name {
  margin: 0 15px 10px 0;
  color: #fff;
}

.container .return_top {
  position: fixed;
  right: 10px;
  bottom: 10px;
  z-index: 4;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 0;
  text-align: center;
  color: #fff;
  background: url(../images/ic_top_off.png) center center no-repeat;
  cursor: pointer;
}

.container .return_top:hover {
  background-image: url(../images/ic_top_on.png);
}

.main {
  width: 100%;
  overflow: hidden;
}

.main .loca_bar {
  padding: 0 0 0 15px;
  height: 25px;
  line-height: 25px;
  font-size: 14px;
  color: #fff;
  background: #1b7cea;
}

.main .loca_bar i {
  font-weight: bold;
  font-style: normal;
}

.main .tree_search {
  margin: 5px 0;
}

.main .left_menu {
  width: 100%;
  overflow: auto;
}

.main .left_menu .el-menu-item-group__title {
  font-size: 14px;
}

.main .left_menu .el-menu-item, .main .left_menu .el-submenu__title {
  height: 35px;
  line-height: 35px;
}

.main .el-tree-node__expand-icon {
  position: relative;
}

.main .el-tree-node__expand-icon:not(.is-leaf) {
  margin: 0 5px 0 0;
  padding: 0 15px 0 0;
}

.main .el-tree-node__expand-icon:not(.is-leaf):before {
  display: block;
  margin: 0 2px 0 0;
  width: 18px;
  height: 18px;
  line-height: 18px;
  font-size: 18px;
  transform: rotate(0deg);
  transition: transform 0.3s ease-in-out;
}

.main .el-tree-node__expand-icon:not(.is-leaf):after {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 3;
  content: "";
  width: 18px;
  height: 18px;
  background: url(../images/ic_folder.png) center center no-repeat;
}

.main .el-tree-node__expand-icon:not(.is-leaf).expanded {
  transform: none;
}

.main .el-tree-node__expand-icon:not(.is-leaf).expanded:before {
  transform: rotate(90deg);
}

.main .drag_bar {
  width: 10px;
  cursor: ew-resize;
  background: #dcdfe7 url(../images/drag_bg.png) center center no-repeat;
}

.main .table_search {
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
  font-size: 12px;
  background: #f3f4f5;
}

.main .table_search .table_radio {
  margin: 0 20px 0 0;
}

.main .table_search .table_radio .el-radio {
  line-height: 30px;
}

.main .el-main {
  padding: 0 10px 10px;
  width: 100%;
  background: #f3f4f5;
}

.main .el-footer {
  padding: 5px 0;
  background: #f3f4f5;
}

.main .table_box {
  padding: 0 10px 10px;
  border: 1px #dcdfe7 solid;
  border-radius: 3px;
  background: #fff;
}

.main .table_box th > .cell {
  line-height: 34px;
  font-size: 15px;
  color: #575757;
}

.index .top {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 78px;
  background: url(../images/style_science_tech/top_bg.png) no-repeat;
  background-size: 100% 100%;
}

.index .top .up {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.index .top .logo {
  flex: 1;
  display: flex;
  align-items: center;
  margin: 2px 0 3px 10px;
}

.index .top .logo .img {
  margin: 0 5px 0 0;
}

.index .top .logo .txt {
  color: #e9ebf1;
}

.index .top .logo .txt .cn {
  height: 22px;
  overflow: hidden;
  font-size: 15px;
  text-align: justify;
}

.index .top .logo .txt .cn span {
  display: inline-block;
  width: 100%;
}

.index .top .logo .txt .en {
  font-size: 12px;
  text-transform: uppercase;
}

.index .top .right {
  margin: 10px 0 0;
  padding: 0 22px 0 0;
}

.index .top .right .icon_box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 25px;
}

.index .top .right .icon_box .icon {
  margin: 0 0 0 15px;
  width: 20px;
  cursor: pointer;
  color: #fff;
  font-size: 22px;
}

.index .top .down {
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  min-width: 1200px;
}

.index .top .down .el-menu {
  margin: 0 0 0 7px;
  background: none;
  border: none;
  box-sizing: content-box;
}

.index .top .down .el-menu > .el-menu-item {
  padding: 0 30px 0 0;
  height: 32px;
  line-height: 32px;
  text-align: center;
  color: #fff;
  border: none;
  box-sizing: content-box;
  background: url(../images/style_science_tech/V1_04.png) right bottom no-repeat;
}

.index .top .down .el-menu > .el-menu-item .txt {
  display: block;
  padding: 0 0 0 30px;
  min-width: 50px;
  height: 32px;
  line-height: 32px;
  color: #fff;
  border: none;
  background: url(../images/style_science_tech/V1_03.png) 0 bottom no-repeat;
}

.index .top .down .el-menu > .el-menu-item .txt:hover {
  color: #d1d8e5;
}

.index .top .down .el-menu > .el-menu-item.is-active {
  background-image: url(../images/style_science_tech/nav_sel_bg_r.png);
}

.index .top .down .el-menu > .el-menu-item.is-active .txt {
  background-image: url(../images/style_science_tech/nav_sel_bg_l.png);
}

.index .top .down .el-menu--horizontal .el-menu-item:not(.is-disabled).is-active {
  background-image: url(../images/style_science_tech/nav_sel_bg_r.png);
}

.index .top .down .el-menu--horizontal .el-menu-item:not(.is-disabled).is-active .txt {
  background-image: url(../images/style_science_tech/nav_sel_bg_l.png);
}

.index .top .down .el-menu--horizontal > .el-submenu {
  padding: 0 30px 0 0;
  height: 32px;
  background: url(../images/style_science_tech/V1_04.png) right bottom no-repeat;
}

.index .top .down .el-menu--horizontal > .el-submenu .el-submenu__title {
  padding: 0 0 0 30px;
  height: 32px;
  line-height: 32px;
  color: #fff;
  border: none;
  background: url(../images/style_science_tech/V1_03.png) 0 bottom no-repeat;
}

.index .top .down .el-menu--horizontal > .el-submenu .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.index .top .down .el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  color: #d1d8e5;
}

.index .top .down .el-menu--horizontal > .el-submenu.is-active {
  background-image: url(../images/style_science_tech/nav_sel_bg_r.png);
}

.index .top .down .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {
  background-image: url(../images/style_science_tech/nav_sel_bg_l.png);
}

.index .top .down .word {
  display: flex;
  align-items: center;
  margin: 0 15px 0 0;
  height: 30px;
  line-height: 30px;
  color: #fff;
}

.index .top .down .word .split_line {
  margin: 0 10px;
  width: 1px;
  height: 18px;
  background: #b8b8bf;
}

.index .con {
  display: flex;
  flex-direction: column;
  position: relative;
  background: url(../images/style_science_tech/bg_0.png);
  background-size: 100% 100%;
}

.index .con .t_l, .index .con .t_r, .index .con .b_l, .index .con .b_r {
  position: absolute;
  z-index: 7;
  width: 64px;
  height: 64px;
}

.index .con .t_l {
  top: 5px;
  left: 5px;
  background: url(../images/style_science_tech/img_t_l.png) no-repeat;
}

.index .con .t_r {
  top: 5px;
  right: 5px;
  background: url(../images/style_science_tech/img_t_r.png) no-repeat;
}

.index .con .b_l {
  bottom: 5px;
  left: 5px;
  background: url(../images/style_science_tech/img_b_l.png) no-repeat;
}

.index .con .b_r {
  bottom: 5px;
  right: 5px;
  background: url(../images/style_science_tech/img_b_r.png) no-repeat;
}

.index .con .bg_t_l {
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(../images/style_science_tech/img_bg_t_l.png) 0 0 no-repeat;
}

.index .con .bg_t_r {
  position: absolute;
  z-index: 3;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: url(../images/style_science_tech/img_bg_t_r.png) right 0 no-repeat;
}

.index .con .bg_b {
  position: absolute;
  z-index: 4;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: url(../images/style_science_tech/img_star_b_l.png) 0 bottom no-repeat;
  background-size: contain;
}

.index .con .start {
  position: absolute;
  z-index: 5;
  top: -10px;
  right: 110px;
  width: 126px;
  height: 61px;
  background: url(../images/style_science_tech/img_star.png) no-repeat;
}

.index .con .box {
  position: relative;
  z-index: 6;
  margin: 21px;
  box-sizing: border-box;
  border: 1px #2f50a8 solid;
  box-shadow: -1px -1px 29px #284d8f inset;
  background: rgba(0, 0, 0, 0.1);
}

.index .con .box .bar {
  display: flex;
  margin: 20px;
  height: 92px;
}

.index .con .box .bar .bar1 {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 20px 0 0;
  padding: 0 20px;
}

.index .con .box .bar .bar1:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 39px;
  height: 100%;
  z-index: 2;
  background: url(../images/style_science_tech/bg_bar_l.png) 0 bottom no-repeat;
}

.index .con .box .bar .bar1:after {
  content: '';
  position: absolute;
  top: 0;
  left: 39px;
  right: 0;
  height: 100%;
  z-index: 1;
  background: url(../images/style_science_tech/bg_bar_r.png) right bottom no-repeat;
}

.index .con .box .bar .bar1 .icon {
  position: relative;
  z-index: 5;
  width: 77px;
  height: 92px;
  background-position: center center;
  background-repeat: no-repeat;
}

.index .con .box .bar .bar1 .icon1 {
  background-image: url(../images/style_science_tech/ic_0.png);
}

.index .con .box .bar .bar1 .icon2 {
  background-image: url(../images/style_science_tech/ic_1.png);
}

.index .con .box .bar .bar1 .icon3 {
  background-image: url(../images/style_science_tech/ic_2.png);
}

.index .con .box .bar .bar1 .icon4 {
  background-image: url(../images/style_science_tech/ic_3.png);
}

.index .con .box .bar .bar1 .right {
  position: relative;
  z-index: 5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

.index .con .box .bar .bar1 .right .num {
  margin: 0 0 10px;
  font-size: 30px;
  font-weight: bold;
}

.index .con .box .bar .bar1 .right .num1 {
  color: #05baff;
}

.index .con .box .bar .bar1 .right .num2 {
  color: #8e95ff;
}

.index .con .box .bar .bar1 .right .num3 {
  color: #7dfce2;
}

.index .con .box .bar .bar1 .right .num4 {
  color: #dfdfdf;
}

.index .con .box .bar .bar1 .right .name {
  font-size: 16px;
  font-weight: bold;
  color: #d3e9f4;
}

.index .con .box .bar .bar1:last-child {
  margin: 0;
}

.index .con .box .area {
  position: relative;
  margin: 20px;
  padding: 40px 20px 20px;
  min-height: 230px;
}

.index .con .box .area:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 21px;
  height: 130px;
  z-index: 4;
  background: url(../images/style_science_tech/bg_cahrt_l_01.png) 0 0 no-repeat;
}

.index .con .box .area:after {
  content: '';
  position: absolute;
  top: 0;
  left: 21px;
  right: 0;
  height: 130px;
  z-index: 3;
  background: url(../images/style_science_tech/bg_cahrt_r_01.png) right 0 no-repeat;
}

.index .con .box .area .bg1 {
  position: absolute;
  top: 130px;
  bottom: 0;
  left: 0;
  width: 21px;
  z-index: 2;
  background: url(../images/style_science_tech/bg_cahrt_l_02.png) 0 bottom no-repeat;
}

.index .con .box .area .bg2 {
  position: absolute;
  top: 130px;
  bottom: 0;
  left: 21px;
  right: 0;
  z-index: 2;
  background: url(../images/style_science_tech/bg_cahrt_r_02.png) right bottom no-repeat;
}

.index .con .box .area .title {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 5;
  padding: 5px 40px 0;
  border-top: 5px #61a3fc solid;
  font-weight: bold;
  font-size: 16px;
  color: #fff;
  transform: translateX(-50%);
}

.index .con .box .area2 {
  position: relative;
  margin: 20px;
  padding: 40px 20px 20px;
  min-height: 230px;
}

.index .con .box .area2:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 21px;
  height: 200px;
  z-index: 4;
  background: url(../images/style_science_tech/bg_cahrt2_l_01.png) 0 0 no-repeat;
}

.index .con .box .area2:after {
  content: '';
  position: absolute;
  top: 0;
  left: 21px;
  right: 0;
  height: 200px;
  z-index: 3;
  background: url(../images/style_science_tech/bg_cahrt2_r_01.png) right 0 no-repeat;
}

.index .con .box .area2 .bg1 {
  position: absolute;
  top: 200px;
  bottom: 0;
  left: 0;
  width: 21px;
  z-index: 2;
  background: url(../images/style_science_tech/bg_cahrt2_l_02.png) 0 bottom no-repeat;
}

.index .con .box .area2 .bg2 {
  position: absolute;
  top: 200px;
  bottom: 0;
  left: 21px;
  right: 0;
  z-index: 2;
  background: url(../images/style_science_tech/bg_cahrt2_r_02.png) right bottom no-repeat;
}

.index .con .box .area2 .title {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 5;
  padding: 5px 40px 0;
  border-top: 5px #61a3fc solid;
  font-weight: bold;
  font-size: 16px;
  color: #fff;
  transform: translateX(-50%);
}

.index .con .box .area2 .area2_box {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 10;
}

.index .con .box .area2 .area2_box .table {
  padding: 0 10px;
  border: 1px #bae7fb dashed;
  border-width: 0 1px;
  background: none;
  color: #d3e9f4;
}

.index .con .box .area2 .area2_box .table:before {
  content: none;
}

.index .con .box .area2 .area2_box .table thead {
  color: #d3e9f4;
}

.index .con .box .area2 .area2_box .table tr {
  background: #123964;
}

.index .con .box .area2 .area2_box .table tr.el-table__row--striped {
  background: #132d51;
}

.index .con .box .area2 .area2_box .table tr.el-table__row--striped td {
  background: none;
}

.index .con .box .area2 .area2_box .table td {
  font-size: 14px;
  border: none;
}

.index .con .box .area2 .area2_box .table .cell {
  height: 32px;
  line-height: 32px;
}

.index .con .box .area2 .area2_box .table .el-table__header tr, .index .con .box .area2 .area2_box .table .el-table__header th {
  background: none;
}

.index .con .box .area2 .area2_box .table .el-table__header th {
  font-size: 16px;
  border: none;
}

.index .con .box .area2 .area2_box .table .el-table__body tr:hover > td {
  background: #0a315b;
}

.index .el-pagination {
  margin: 10px 10px 0;
  color: #d3e9f4;
}

.index .el-pagination.is-background .btn-prev, .index .el-pagination.is-background .btn-next, .index .el-pagination.is-background .el-pager li, .index .el-pagination.is-background .el-input__inner {
  background: none;
  color: #add6e7;
}

.index .el-pagination.is-background .el-input__inner {
  border: none;
}

.index .el-pagination.is-background .el-pagination__total, .index .el-pagination.is-background .el-pagination__jump {
  color: #add6e7;
}

.index .el-pagination.is-background .btn-prev:disabled, .index .el-pagination.is-background .btn-next:disabled {
  color: #606266;
}

.index .el-button--primary {
  background-color: #2a5dab;
  border-color: #2a5dab;
}
