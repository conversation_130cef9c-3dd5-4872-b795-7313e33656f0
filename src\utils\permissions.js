import store from '../store/index'

let userInfo = store.state.userInfo
let { roles, orgAndSon, orgId, mallRoles, localOrgId } = userInfo

Object.keys(userInfo).forEach(key => {
    let isAuthKey = key.includes('is') && key[0] === 'i'
    if(isAuthKey && userInfo[key] === 1) roles.push(key)
})

class UserPermission {
    // 参数为权限名称
    constructor (orgSearchParam) {
        this.orgId = orgId
        this.orgSearch = this.getRoleByName(orgSearchParam) // 使用传入的参数来设置 orgSearch 的值
        this.roles = roles
        this.enterpriseId = localOrgId
        this.currentSubOrgId = []
        this.orgDisplayState = 1 // 1全部， 2本级， 3下级

        // 根据新的 orgSearch 值来过滤子集机构完整列表
        this.subOrg = this.orgSearch === 1  ? orgAndSon.filter(org => org.orgId !== this.orgId) : []
        this.subOrgIds = this.subOrg.map(org => org.orgId)
    }
    /**
     * @param {string} name - 权限名称（下单权限等）
     * @return {number} 返回值0代表无权限 1 代表有权限
     */
    getRoleByName (name) {
        if (Array.isArray(mallRoles)) {
            // 根据名称寻找
            let isContain = mallRoles.find(item=>item.name === name)
            return isContain ? isContain.orgSearch : 0
        }else {
            return 0
        }
    }
    // 传入字符串数组，判断用户是否有指定权限，包括userInfo的is***字段是否为1、roles权限数组
    canAccessAction (permission) {
        return permission.every(p => this.roles.includes(p))
    }
    // 判断传入机构id是否为用户机构id
    isSameOrg (orgId) {
        return this.orgId === orgId
    }
    // 判断传入的企业ID是否为用户的企业ID（本地）
    isSameOrgByEnterpriseId (enterpriseId) {
        return this.enterpriseId === enterpriseId
    }
    // 判断用户是否有下级机构（传入机构id判断用户是否拥有指定下级机构）
    hasSubOrg (orgId) {
        return orgId ? this.subOrgIds.includes(orgId) : this.subOrg.length > 0
    }
    // 根据显示机构状态渲染内容
    // 1全部， 2本级， 3下级
    showContentByOrgState (state) {
        return this.orgDisplayState === state
    }
    // 查看本机构数据
    getHostOrgData () {
        this.orgDisplayState = 2
        this.currentSubOrgId = []
        return [orgId]
    }
    // 查看下级机构数据
    getSubOrgData (orgId) {
        if(this.orgSearch === 0) return this.$message.error('用户无权查看下级机构数据')
        this.orgDisplayState = 3
        this.currentSubOrgId = orgId ? [orgId] : []
    }
    // 查看所有机构数据
    getAllOrgData () {
        this.orgDisplayState = 1
        this.currentSubOrgId = []
        return [this.orgId, ...this.subOrgIds]
    }
    // 根据orgID查询机构简码
    getShortCodeByOrgId (orgId) {
        let orgBean =  orgAndSon.find(item=>item.orgId === orgId)
        return orgBean.shortCode || 0
    }
    // 获取是否有权限
    getOrgSearch () {
        return this.orgSearch
    }
}

export {
    UserPermission
}