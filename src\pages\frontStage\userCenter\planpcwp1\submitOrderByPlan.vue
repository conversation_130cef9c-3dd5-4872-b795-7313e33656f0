<template>
    <div class="root" v-loading="submitPlanOrderLoading">
        <div class="box center p20">
            <el-page-header class="mb10 ml20" @back="goBack" content="">
            </el-page-header>
            <div class="list-title dfa mb20">计划明细</div>
            <el-table
                class="ml20"
                ref="msgTable"
                :data="resultDetail.details"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="MatterName" label="物资名称" width="100"/>
                <el-table-column prop="Spec" label="规格型号" width=""/>
                <el-table-column prop="ClassName" label="类别名称" width=""/>
                <el-table-column prop="Price" label="单价" width=""/>
                <el-table-column prop="Number" label="数量" width=""/>
                <el-table-column prop="Amount" label="总金额" width=""/>
                <el-table-column prop="ConsumeAmount" label="已下单金额" width=""/>
                <el-table-column prop="ConsumeNumber" label="已下单数量" width=""/>
                <el-table-column prop="" label="是否可下单" width="">
                    <template slot-scope="scope">
                        <el-tag type="success" v-if="scope.row.resCode == 200">是</el-tag>
                        <el-tag type="warning" v-if="scope.row.resCode == 50000">是</el-tag>
                        <el-tag type="danger" v-if="scope.row.resCode == 50010">否</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="" label="原因" width="">
                    <template slot-scope="scope">
                        <el-tag type="danger" v-if="scope.row.resCode == 50010">{{ scope.row.cause }}</el-tag>
                    </template>
                </el-table-column>
            </el-table>
            <div class="list-title dfa mb20">商品列表</div>
            <div class="addr-bar dfb">
                <div class="ml20">共计 {{ totalSkuCount }} 件</div>
            </div>
            <div class="title dfa">
                <el-checkbox v-model="selectAllProduct" label="全选" :indeterminate="false" @change="toggleSelectAll"></el-checkbox>
                <div>商品</div>
                <div>单价</div>
                <div>数量</div>
                <div>小计</div>
                <div>提示</div>
            </div>
            <div class="product">
                <div class="shop" v-for="(item, i) in cartList" :key="i">
                    <div class="shop-name dfa">
                        <el-checkbox v-model="item.checked" :label="item.shopName" :indeterminate="false"
                                     @change="selectAllInShop(i)"></el-checkbox>
                        <!-- <img :src="item.list[0].icon" alt=""> -->
                    </div>
                    <div class="product-item dfa" v-for="(product, index) in item.productInfo" :key="index">
                        <div class="checkbox">
                            <el-checkbox v-model="product.checked"></el-checkbox>
                        </div>
                        <img
                            @click="$router.push({ path: '/mFront/productDetail', query: { productId: product.productId } })"
                            :src="product.productMinImg ? imgUrlPrefixAdd + product.productMinImg : require('@/assets/images/img/queshen3.png')"
                            alt="">
                        <div class="title-box">
                            <!--                            <div class="dfa"><div class="tag">平台自营</div><div class="name">{{product.productName}}</div></div>-->
                            <div
                                @click="$router.push({ path: '/mFront/productDetail', query: { productId: product.productId } })">
                                {{ product.productName }}
                            </div>
                            <div style="margin-top: 8px">
                                规格型号：{{ product.skuName }}
                            </div>
                            <div style="margin-top: 8px">
                                剩余库存：{{ product.stock }}
                            </div>
                            <div style="margin-top: 8px">
                                计量单位：{{ product.unit }}
                            </div>
                        </div>
                        <div class="price">￥{{ product.sellPrice }}</div>
                        <div class="num-box">
                            <div class="numCalc df">
                                <div disabled @click="changeNum('minus', product)">-</div>
                                <input @change="cartNumChange(product)" v-model="product.cartNum" type="text">
                                <div @click="changeNum('plus', product)">+</div>
                            </div>
                        </div>
                        <div class="total" style="color: rgba(212, 48, 48, 1);">￥{{ product.numTotalPrice }}</div>
                        <el-link style="width: 100px" type="warning" v-if="product.resCode == 50000" :underline="false">{{ product.cause }}</el-link>
                        <el-link type="success" v-if="product.resCode == 200"  :underline="false">可下单</el-link>
                    </div>
                </div>
            </div>
            <div class="bottom-bar dfb">
                <div class="bar-left dfa">
<!--                    <el-checkbox v-model="selectAllProduct" label="全选" :indeterminate="false"-->
<!--                                 @change="toggleSelectAll"></el-checkbox>-->
                </div>
                <div class="bar-right df">
                    <div><span>已选择 {{ totalSelected }} 件商品</span><i class="el-icon-arrow-up"></i></div>
                    <div class="bar-right-price dfa"><span>总价：</span><span>￥{{ totalPrice }}</span><img
                        src="../../../../assets/images/userCenter/提示.png" alt=""></div>
                    <button @click="submitOrder">去结算</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { mapState } from 'vuex'
import { checkSubmitPlanProductCondition, getPCWP1Interface } from '@/api/plan/plan'
import BigNumber from 'bignumber.js'

export default {
    data () {
        return {
            resultDetail: {},
            submitPlanOrderLoading: false,
            totalPrice: '',
            selectAllProduct: false,
            cartList: [], // 购物车列表
            changedSku: null
        }
    },
    watch: {
        cartList: {
            handler (newVal) {
                let checkAll = true
                newVal.forEach(newItem => {
                    // 判断店铺全选
                    newItem.checked = newItem.productInfo.every(subItem => subItem.checked)
                    // 如果有店铺未全选则取消购物车全选状态
                    if (!newItem.checked) checkAll = false
                    newItem.productInfo.forEach(subItem => {
                        // 计算价格
                        if (isNaN(Number(subItem.cartNum)) || subItem.cartNum < 1) subItem.cartNum = 1
                        subItem.numTotalPrice = (Number(subItem.sellPrice) * subItem.cartNum).toFixed(2)
                        // }
                    })
                })
                this.selectAllProduct = checkAll
                this.calcTotalPrice()
            },
            deep: true
        },
        // 监听用户修改sku的数量
        totalProductCount () {
            if (JSON.stringify(this.changedSku == '{}')) return
            this.handleNumChange(this.changedSku)
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 商品总数
        totalProductCount () {
            let num = 0
            this.cartList.forEach(item => {
                item.productInfo.forEach(item1 => {
                    num += item1.cartNum
                })
            })
            return num
        },
        // 商品sku总数量
        totalSkuCount () {
            let len = 0
            this.cartList.forEach(item => {
                len += item.productInfo.length
            })
            return len
        },
        // 全选的商品数量
        totalSelected () {
            let count = 0
            this.cartList.forEach(item => {
                if (item.checked) { // 店铺是否全选
                    return count += item.productInfo.length
                }
                item.productInfo.forEach(subItem => {
                    subItem.checked ? count += 1 : null
                })
            })
            return count
        },
    },
    methods: {
        // 返回上一页
        goBack () {
            window.history.back()
        },
        cartNumChange (item) {
            item.cartNum = parseFloat(item.cartNum).toFixed(4)
            if (item.cartNum >= item.cartMaxNum) {
                item.cartNum = item.cartMaxNum
            }
        },
        // 全选所有商品
        toggleSelectAll () {
            this.cartList.forEach(item => {
                item.productInfo.forEach(item1 => {
                    item1.checked = this.selectAllProduct
                })
            })
        },
        // 全选店铺下的商品
        selectAllInShop (i) {
            this.cartList[i].productInfo.forEach(item => item.checked = this.cartList[i].checked)
        },
        // 获取选中的所有商品
        getSelectedProduct () {
            let arr = []
            this.cartList.forEach(item => {
                if (item.checked) { // 店铺是否全选
                    return arr = arr.concat(item.productInfo)
                }
                item.productInfo.forEach(subItem => {
                    subItem.checked ? arr.push(subItem) : null
                })
            })
            return arr
        },
        // 更改sku对应的商品数量
        changeNum (action, item) {
            this.changedSku = item
            if (action === 'minus') {
                if (item.cartNum === 1) return
                item.cartNum--
            } else {
                if (item.cartNum >= item.cartMaxNum) {
                    item.cartNum = item.cartMaxNum
                } else {
                    item.cartNum++
                }
            }
            // this.handleNumChange(item)
        },
        // 计算订单总价
        calcTotalPrice () {
            let price = new BigNumber(0)
            this.cartList.forEach(item => {
                item.productInfo.forEach(item1 => {
                    if (item1.checked) {
                        price = price.plus(new BigNumber(item1.cartNum).times(item1.sellPrice))
                    }
                })
            })
            this.totalPrice = price.toFixed(2)
        },
        // 提交订单
        submitOrder () {
            let isSubmitFlag = this.userInfo.isSubmitOrder
            let isInterior = this.userInfo.isInterior
            if(isInterior == 1) {
                if(isSubmitFlag == null || isSubmitFlag == 0) {
                    this.$message.error('没有下单权限，请联系管理员！')
                    return
                }
            }
            let list = this.getSelectedProduct()
            if (list.length === 0) {
                this.$message({
                    message: '未选择商品',
                    type: 'info'
                })
                return
            }

            const result1 = list.reduce((result, student) => {
                const shopId = student.shopId
                const shopName = student.shopName
                if (!result[shopId]) {
                    result[shopId] = {
                        shopId: shopId,
                        shopName: shopName,
                        productInfo: []
                    }
                }
                result[shopId].productInfo.push(student)
                return result
            }, {})
            this.$router.push({
                path: '/user/submitOrderByPlanFull',
                name: 'submitOrderByPlanFull',
                params: {
                    row: {
                        productList: result1,
                        productSubmitList: list,
                        totalPrice: this.totalPrice
                    }
                }
            })
        },

        // 检查推送计划情况
        checkSubmitPlanProductConditionM (resultPlan) {
            checkSubmitPlanProductCondition(resultPlan).then(res => {
                this.resultDetail = res.planDetail
                if(res.buyProductList != null) {
                    this.cartList = res.buyProductList
                }
                this.submitPlanOrderLoading = false
            }).catch(() => {
                this.submitPlanOrderLoading = false
            })
        },
        getPlantDetailM () {
            let params = {
                'jsonrpc': '2.0',
                'method': 'Material.SporadicPlan.GetById',
                'params': {
                    'Id': this.$route.query.billId /*单据ID [String]*/
                },
                'id': 1,
                'tags': {
                    'userid': this.userInfo.farUserId,
                    'username': this.userInfo.originalUserName,
                    'orgid': this.userInfo.orgId,
                    'orgname': this.userInfo.orgName,
                    'companycode': '1000',
                    'auth_client_id': 'test',
                    'auth_token': 'test',
                    'platformid': '1'
                }
            }
            this.submitPlanOrderLoading = true
            getPCWP1Interface(params).then(res => {
                if(res.result == null) {
                    this.submitPlanOrderLoading = false
                    return
                }
                if(this.$route.query.DtlId != null) {
                    res.result.Details =  res.result.Details.filter(t => {
                        if(t.DtlId == this.$route.query.DtlId) {
                            return true
                        }else {
                            return false
                        }
                    })
                }
                this.checkSubmitPlanProductConditionM(res.result)
            }).catch(() => {
                this.submitPlanOrderLoading = false
            })
        },
    },
    async created () {
        // 获取购物车列表
        this.getPlantDetailM()
        this.calcTotalPrice()
    },
    mounted () {
    }
}
</script>
<style scoped lang="scss">
.root {
    height: 100%;
    border: 1px solid rgba(229, 229, 229, 1);

    .box {
        width: 100%;
        background-color: #fff;
    }
}

/deep/ .el-checkbox {
    color: rgba(51, 51, 51, 1);

    .el-checkbox__inner {
        border: 1px solid rgba(204, 204, 204, 1);
    }

    .el-checkbox__label {
        color: #666;
    }
}

.addr-bar {
    height: 30px;

    & > div:first-child {
        font-size: 20px;
        font-weight: 500;
        color: rgba(212, 48, 48, 1);
    }

    & > div:last-child {
        color: rgba(51, 51, 51, 1);
    }

    /deep/ .el-input__inner {
        width: 216px;
        height: 26px;
    }
}

.title {
    height: 52px;
    margin-bottom: 12px;
    padding-left: 20px;
    color: rgba(51, 51, 51, 1);
    background-color: rgba(250, 250, 250, 1);

    /deep/ .el-checkbox {
        width: 158px;
    }

    & > div:nth-child(2) {
        width: 450px;
    }

    & > div:nth-child(3) {
        width: 129px;
    }

    & > div:nth-child(4) {
        width: 109px;
    }

    & > div:nth-child(5) {
        width: 108px;
    }
}

.product {
    min-height: 600px;
    margin-bottom: 30px;

    .shop-name {
        padding: 22px 0 14px 20px;

        img {
            width: 22px;
            height: 22px;
            margin-left: 3px;
        }
    }

    & .product-item:not(:last-of-type) {
        margin-bottom: 10px;
    }

    .product-item {
        height: 144px;
        border: 1px solid rgba(230, 230, 230, 1);

        img {
            width: 100px;
            height: 100px;
            object-fit: cover;
        }

        & > div {
            height: 100px;
        }

        .checkbox {
            padding: 0 30px 0 20px;
        }

        .title-box {
            width: 400px;
            padding-left: 12px;

            .tag {
                width: 60px;
                height: 20px;
                margin: 0 10px 7px 0;
                font-size: 12px;
                line-height: 20px;
                text-align: center;
                color: #fff;
                background-color: rgba(255, 195, 0, 1);
            }
        }

        .price {
            width: 157px;
            text-align: center;
        }

        .num-box {
            width: 101px;

            .numCalc {
                width: 100%;
                border: 1px solid rgba(204, 204, 204, 1);

                div, input {
                    height: 26px;
                    text-align: center;
                }

                div {
                    width: 26px;
                    line-height: 26px;
                    background-color: rgba(230, 230, 230, 1);
                    cursor: pointer;
                    user-select: none;

                    &:first-child {
                        border-right: 1px solid rgba(204, 204, 204, 1);
                    }

                    &:last-child {
                        border-left: 1px solid rgba(204, 204, 204, 1);
                    }
                }

                input {
                    width: 49px;
                }
            }
        }

        .total {
            width: 134px;
            text-align: center;
        }

        .operate {
            font-size: 10px;
            color: rgba(102, 102, 102, 1);

            div {
                margin-bottom: 10px;
                cursor: pointer;
            }
        }
    }
}

.bottom-bar {
    height: 60px;
    padding-left: 20px;
    border: 1px solid rgba(230, 230, 230, 1);

    & > div {
        height: 100%;
    }

    /deep/ .el-checkbox {
        margin-right: 30px;
    }

    .bar-left {
        color: rgba(102, 102, 102, 1);

        span {
            margin-right: 20px;
            cursor: pointer;
        }
    }

    .bar-right {
        & > div:first-child {
            margin: 7px 9px 0 0;
            color: rgba(153, 153, 153, 1);
            cursor: pointer;

            span {
                margin-right: 9px;
            }
        }

        .bar-right-price {
            height: 27px;
            margin: 4px 22px 0 0;

            & span:first-child {
                color: rgba(153, 153, 153, 1);
            }

            & span:nth-child(2) {
                margin-right: 12px;
                font-size: 18px;
                font-weight: 700;
                color: rgba(212, 48, 48, 1);
            }

            img {
                width: 16px;
                height: 16px;
            }
        }

        button {
            width: 128px;
            height: 60px;
            font-size: 24px;
            font-weight: 400;
            text-align: center;
            line-height: auto;
            color: rgba(255, 255, 255, 1);
            background-color: rgba(212, 48, 48, 1);
        }
    }
}
</style>