<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <!--<el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>-->
                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">批量启用</el-button>
                            <el-button type="primary" @click="changePublishState(2)" class="btn-delete">批量停用</el-button>
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                            <el-button type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改排序值</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                        <el-input clearable type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字" v-model="filterData.keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" v-loading="isLoading">
                <el-table
                    @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="操作" width="120">
                        <template v-slot="scope">
                            <img @click="onDel(scope)" src="../../../../assets/btn/delete.png" alt="">
                        </template>
                    </el-table-column>
                    <el-table-column label="楼层主图">
                        <template v-slot="scope">
                            <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.imgUrl"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column label="楼层名称">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.floorName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="所属栏目" prop="columnName"/>
                    <el-table-column label="小标题" prop="floorNameText"/>
                    <!--                    <el-table-column  label="主图链接地址">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <a :href="scope.row.mainImgUrl" style="color: blue">{{scope.row.mainImgUrl}}</a>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <!--                    <el-table-column  label="商品类别">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            {{scope.row.floorProductType}}-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column label="楼层状态">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state==1" type="success">显示</el-tag>
                            <el-tag v-if="scope.row.state==0" type="danger">不显示</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="排序值" width="120">
                        <template v-slot="scope">
                            <el-input type="number" clearable v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.totalCount"
                :limit="50"
                :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange"
                @sizeChange="sizeChange"
            />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新增/编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item required class="uploader" label="图片地址：" prop="imgUrl">
                                <div>
                                    <el-upload
                                        class="avatar-uploader" action="fakeaction"
                                        :show-file-list="false"
                                        :before-upload="handleBeforeUpload" name="img"
                                        :auto-upload="true"
                                        :on-change="handleUploadChange"
                                        :http-request="uploadImg"
                                    >
                                        <img v-if="formData.imgUrl" :src="imgUrlPrefixAdd + formData.imgUrl" class="avatar" alt="">
                                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    </el-upload>
                                    <el-progress v-show="uploadInProgress" :percentage="uploadPercentage"></el-progress>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="楼层名称：" prop="floorName">
                                <el-input clearable v-model="formData.floorName" placeholder="请输入楼层名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item width="150px" label="小标题：" prop="floorNameText">
                                <el-input clearable v-model="formData.floorNameText" placeholder=""></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <!--                        <el-col :span="12">-->
                        <!--                            <el-form-item label="商品类别：" prop="floorProductType">-->
                        <!--                                <el-input clearable v-model="formData.floorProductType" placeholder="请输入商品类别" ></el-input>-->
                        <!--                            </el-form-item>-->
                        <!--                        </el-col>-->
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input clearable v-model="formData.sort" type="number" placeholder="填写排序值">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input clearable type="textarea" v-model="formData.remarks" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="35%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="24">
                        <el-form-item width="150px" label="楼层名称：" prop="floorName">
                            <el-input clearable v-model="filterData.floorName" placeholder="请输入楼层名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="栏目名称：" prop="columnName">
                            <el-input clearable v-model="filterData.columnName" placeholder="请输入栏目名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="小标题：" prop="floorNameText">
                            <el-input clearable v-model="filterData.floorNameText" placeholder="请输入小标题"></el-input>
                        </el-form-item>
                    </el-col>
                    <!--                    <el-col :span="12">-->
                    <!--                        <el-form-item label="商品类别：" prop="floorProductType">-->
                    <!--                            <el-input clearable v-model="filterData.floorProductType" placeholder="请输入商品类别名称" ></el-input>-->
                    <!--                        </el-form-item>-->
                    <!--                    </el-col>-->
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="楼层状态：">
                            <el-select v-model="filterData.state" placeholder="楼层状态">
                                <el-option
                                    v-for="item in stateFilter" :key="item.value" :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">返回</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { debounce, hideLoading, showLoading } from '@/utils/common'
import {
    batchDeleteFloor,
    batchNotPublishFloor,
    batchPublishFloor,
    changeSortValueFloor,
    createFloor,
    delFloor,
    editFloor,
    getFloorList
} from '@/api/platform/floor/floor'
import { uploadFile } from '@/api/platform/common/file'

export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getFloorList(this.requestParams).then(res => this.tableData = res.list)
            }
        }
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertName: '楼层',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            isLoading: false,
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                floorName: '',
                columnName: '',
                floorNameText: '',
                floorProductType: '',
                keywords: '',
                state: null,
                orderBy: 1,
                mallType: null,
            },
            tableData: [],
            // 表单校验规则
            formRules: {
                imgUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
                floorName: [{ required: true, message: '请输入楼层名', trigger: 'blur' }],
                floorNameText: [{ required: true, message: '请输入小标题名', trigger: 'blur' }],
                floorProductType: [{ required: true, message: '请输入商品类别', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            mapObj: null,
            // 新增编辑 表单数据
            formData: {},
            columnList: [],
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            stateFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '不显示' },
                { value: 1, label: '显示' },
            ],
            shopFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '慧采商城' },
                { value: 1, label: '装备商城' },
            ],
            uploadPercentage: 0,
            uploadInProgress: false
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        this.isLoading = true
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy,
            state: this.filterData.state
        }
        getFloorList(params).then(res => {
            this.pages = res
            this.tableData = res.list
            this.isLoading = false
        })
        this.getParams()
    },
    methods: {
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 上传图片
        async uploadImg (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            uploadFile(form).then(res => {
                this.formData.imgUrl = res[0].objectPath
                this.formData.imgUrlId = res[0].recordId
            })
        },
        handleUploadChange (file) {
            if (file.status === 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status === 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        resetSearchConditions () {
            this.filterData = {
                floorName: '',
                columnName: '',
                floorNameText: '',
                floorProductType: '',
                keywords: '',
                state: null,
                orderBy: 1,
                mallType: null,
            }
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.floorId
            })
            if (!this.selectedRows[0]) {
                let msg = num === 1 ? `请选择要启用的${this.alertName}` : `请选择要停用的${this.alertName}`
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = num === 1 ? `您确定要启用选中的${this.alertName}吗？` : `您确定要停用选中的${this.alertName}吗？`
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublishFloor(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '启用成功', () => {
                                getFloorList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublishFloor(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '停用成功', () => {
                                getFloorList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${this.alertName}吗？`, async () => {
                showLoading()
                delFloor(scope.row.floorId).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功！')
                        this.getTableData()
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.floorId
                })
                batchDeleteFloor(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        handleClose () {
        },
        handleView (scope) {
            // this.viewList = 'class'
            this.formData = JSON.parse(JSON.stringify(scope.row))
            this.action = '编辑'
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/floor/floorManagerDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'floorManagerDetail',
                params: {
                    row: scope.row
                }
            })
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            let sort = row.sort ? parseInt(row.sort) : 0
            if (!this.changedRow[0]) {
                return this.changedRow.push({ floorId: row.floorId, sort, })
            }
            let arr = this.changedRow.map((item, i) => {
                if (item.floorId === row.floorId) return i
            })
            if (arr[0]) {
                return this.changedRow[arr[0]].sort = sort
            }
            this.changedRow.push({ floorId: row.floorId, sort, })
        },
        // 修改排序值
        changeSortValue () {
            // changeSortValue(this.changedRow).then(res => {
            //     this.getTableData()
            // })
            if (!this.changedRow[0]) {
                let msg = '当前没有排序值被修改！'
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValueFloor(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '修改成功', () => {
                            this.getTableData()
                        })
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.isLoading = true
            this.getParams()
            getFloorList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.isLoading = false
                this.pages = res
            })
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getParams()
            getFloorList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
            })
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    // if (this.action === '编辑') {
                    //     return this.handleEditData()
                    // }
                    this.handleCreateData()
                }
            })
        },
        // 修改数据
        handleEditData () {
            editFloor(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 保存数据
        handleCreateData () {
            createFloor(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

/deep/ .el-form-item.uploader {
    height: 200px;
}

// upload
.avatar-uploader {
    /deep/ .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

.avatar-uploader {
    /deep/ .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/ .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/ .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>
