import service from '@/utils/request'

const { httpGet, httpPost } = service

const del = params => {
    return httpGet({
        url: '/materialMall/orderShip/delete',
        params
    })
}

const exportDataById = params => {
    return httpGet({
        url: '/materialMall/orderShip/exportDataById',
        params,
        responseType: 'blob',
    })
}
const deleteBatch = params => {
    return httpPost({
        url: '/materialMall/orderShip/deleteBatch',
        params
    })
}
//修改发货单状态
const updateShipType = params => {
    return httpPost({
        url: '/materialMall/orderShip/updateShipType',
        params
    })
}
//添加发货单运输联系人和电话
const update = params => {
    return httpPost({
        url: '/materialMall/orderShip/update',
        params
    })
}
/**
 * 导出发货单
 * @param params
 * @returns {*}
 */
const exportDataTwoById = params => {
    return httpGet({
        url: '/materialMall/orderShip/exportDataTwoById',
        params,
        responseType: 'blob',
    })
}
/**
 * 物资公司和二级供应商导出实物收料单
 * @param params
 * @returns {*}
 */
const materialShipExport = params => {
    return httpGet({
        url: '/materialMall/orderShip/materialShipExport',
        params,
        responseType: 'blob',
    })
}
const materialShipDzExport = params => {
    return httpGet({
        url: '/materialMall/orderShip/materialShipDzExport',
        params,
        responseType: 'blob',
    })
}
const exportDataPurchase = params => {
    return httpGet({
        url: '/materialMall/orderShip/exportDataPurchase',
        params,
        responseType: 'blob',
    })
}
export {
    del,
    update,
    deleteBatch,
    updateShipType,
    exportDataById,
    exportDataTwoById,
    materialShipExport,
    exportDataPurchase,
    materialShipDzExport
}
