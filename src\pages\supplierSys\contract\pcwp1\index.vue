<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" >
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn">
              <!--                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>-->
              <!--                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">启用</el-button>-->
              <!--                            <el-button type="primary" @click="changePublishState(2)" class="btn-greenYellow">停用</el-button>-->
              <!--                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>-->
            </div>
          </div>
          <div class="search_box">
            <el-input type="text" @keyup.enter.native="onSearch" placeholder="合同名称/公司名称" v-model="keyword"><img src="@/assets/search.png"
                                                                                                                      slot="suffix" @click="onSearch" /></el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" :style="{ width: '100%' }">
        <el-table @row-click="handleCurrentInventoryClick2" ref="eltableCurrentRow2" class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange" v-loading="flash">
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="合同名称"  >
            <template slot-scope="scope">
              <span class="action" @click="handleView(scope)">{{scope.row.Name}}</span>
            </template>
          </el-table-column>
          <el-table-column label="公司名称"  prop="OrgName">
          </el-table-column>
          <el-table-column label="合同金额" width="" prop="Amount">
          </el-table-column>
          <el-table-column label="结算金额" width="" prop="SettledAmount">
          </el-table-column>
          <el-table-column label="应交履约保证金" width="" prop="bondAmount">
          </el-table-column>
          <el-table-column label="签订日期" width="" prop="BillDate">
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                     @currentChange="currentChange" @sizeChange="sizeChange" />
    </div>
    <!-- ----------------查询弹框---------------- -->
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="60%" :close-on-click-modal="true" >
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
        <el-row>
          <el-col :span="12" >
            <el-form-item label="合同名称：">
              <el-input clearable  v-model="filterData.contractName" placeholder="请输入合同名称" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="签订日期：">
              <el-date-picker
                  value-format="yyyy-MM-dd"
                  v-model="filterData.signing"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { contractPcwp1List } from '@/api/supplierSys/contract/pcwp1Contract'
import { debounce } from '@/utils/common'
import { mapActions, mapState } from 'vuex'

export default {
    components: {
        ComPagination
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            flash: false,
            queryVisible: false,
            socialCode: '',
            keyword: '',
            currentRow: null,
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                orgName: '',
                contractName: '',
                contractStartAmount: '',
                contractEndAmount: '',
                signing: []

            },
            tableData: [
                // {
                //     orgName: '公司名称',
                //     contractAmount: '合同金额',
                //     settlementAmount: '结算金额',
                //     bondAmount: '应交履约保证金',
                //     signingDate: '22',
                //     contractName: '合同名小吃',
                // }
            ],

            // 表单校验规则

            mapObj: null,
            // 新增编辑 表单数据
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        this.getPcwp1DateList()

    },
    methods: {
        getPcwp1DateList () {
            console.log('aa')
            this.flash = true
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                type: '4',
            }

            if (this.filterData.signing.length != 0) {
                params.signingStartTime = this.filterData.signing[0]
                params.signingEndTime = this.filterData.signing[1]
            }

            if (this.filterData.contractName != '' && this.filterData.contractName != null) {
                params.name = this.filterData.name
            }
            if (this.keyword != '' && this.keyword != null) {
                params.keyword = this.keyword
            }
            contractPcwp1List(params).then(res=>{
                this.tableData = res.list
                this.pages = res
            })
            this.flash = false
        },
        // 关闭对话框（取消）

        hideDialog () {
            this.filterData = {
                orgName: '',
                contractName: '',
                contractStartAmount: '',
                contractEndAmount: '',
                signing: []
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keyword: this.keyword,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            this.keywords = ''
            this.getPcwp1DateList()
            this.queryVisible = false
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getDateList()
        },
        sizeChange () {
            this.getDateList()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () { },
        handleView (scope) {
            this.billId = scope.row.billId
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/contractDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'contractDetail',
                params: {
                    billId: scope.row.BillId
                }
            })
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取列表数据

        // 关键词搜索
        onSearch () {
            this.getPcwp1DateList()

        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单

        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}
.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}
</style>
