<template>
    <div>
        <div class="right" v-loading="showLoading">
            <!--            <el-date-picker-->
            <!--                :default-time="['00:00:00', '23:59:59']"-->
            <!--                @change="dateChange"-->
            <!--                value-format="yyyy-MM-dd HH:mm:ss"-->
            <!--                v-model="filterData.dateScope"-->
            <!--                type="datetimerange"-->
            <!--                range-separator="至"-->
            <!--                :picker-options="pickerOptions"-->
            <!--                start-placeholder="开始日期"-->
            <!--                end-placeholder="结束日期">-->
            <!--            </el-date-picker>-->
            <div style="width:100%;height:300px;" ref="chart"></div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <div style="height: 50px; line-height: 50px;margin-left: 10px">
                                <el-date-picker
                                    :default-time="['00:00:00', '23:59:59']"
                                    @change="dateChange"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-model="filterData.dateScope"
                                    type="datetimerange"
                                    range-separator="至"
                                    :picker-options="pickerOptions"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                </el-date-picker>
                            </div>
                        </div>
                        <div style="margin-left: 10px">
<!--                          物资自营店要求：物资子公司要看到物资分公司数据，1878734518961074177 测试 为物资子公司的店铺id-->
                          <!--                      物资自营店要求：物资子公司要看到物资分公司数据，1858777977810120705 正式 为物资子公司的店铺id&ndash;&gt;-->
                          <el-button type="primary" v-if="this.userInfo.shopId==this.businessShopID&&!changeShopView" @click="changShopIdM"   class="btn-greenYellow">查看物资分公司数据</el-button>
                          <el-button type="primary" v-if=this.changeShopView @click="returnShop()"  class="btn-greenYellow">返回子公司</el-button>
                        </div>
                    </div>
                </div>
                <el-table class="table" v-loading="tableLoading" :data="tableData"  border
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="订单号" width="240" prop="orderSn">
                    </el-table-column>
                    <el-table-column label="店铺名称" width="120" prop="shopName"></el-table-column>
                    <el-table-column label="二级供应商名称" width="200" prop="supplierName"></el-table-column>
                    <el-table-column label="商品名称" width="120" prop="productName"></el-table-column>
                    <el-table-column label="成本价" width="" prop="costPrice" />
                    <el-table-column label="商品价格" width="" prop="productPrice" />
                    <el-table-column label="购买数量" width="" prop="buyCounts"/>
                    <el-table-column label="总利润" width="" prop="profitPrice" />
                    <el-table-column label="总成本价" width="" prop="costAmount" />
                    <el-table-column label="总金额" width="" prop="totalAmount" />
                    <el-table-column label="完成时间" width="160" prop="flishTime" />
                    <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getPlatformOrdersItemCountM"
                @sizeChange="getPlatformOrdersItemCountM"
            />
        </div>

    </div>
</template>
<script>
//局部引用
import { getPlatformOrdersItemCount } from '@/api/platform/order/orders'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'

const echarts = require('echarts')
export default{
    components: {
        Pagination
    },
    computed: {
        ...mapState(['userInfo']),
    },
    data () {
        return {
            showLoading: false,
            changeShopView: false,
            shopId: '',
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableLoading: false,
            filterData: {
                dateScope: []
            },
            dataVOS: [],
            labelTitle: [], // 名称数组
            count: [], // 数量数组
        }
    },
    methods: {
        changShopIdM () {
            this.changeShopView = true
            // 物资分公司店铺名称
            this.shopId = '1645601878095495170'
            this.getPlatformOrdersItemCountM()
        },
        returnShop () {
            this.changeShopView = false
            this.shopId = null
            this.getPlatformOrdersItemCountM()
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
        getPlatformOrdersItemCountM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                isShop: 1,
            }
            if (this.shopId != null && this.shopId != '') {
                params.shopId = this.shopId
            }
            if(this.filterData.dateScope != null) {
                params.startDate = this.filterData.dateScope[0],
                params.endDate = this.filterData.dateScope[1]
            }
            this.showLoading = true
            getPlatformOrdersItemCount(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
                if(res.list.length != 0) {
                    this.labelTitle = res.list[0].labelTitle
                    this.count = res.list[0].count
                }else {
                    this.count = []
                }
                this.initCharts()
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        dateChange () {
            this.getPlatformOrdersItemCountM()
        },
        initCharts () {
            // 基于准备好的dom，初始化echarts实例
            let myChart = echarts.init(this.$refs.chart)
            // 绘制图表
            myChart.setOption({
                itemStyle: {
                    // 高亮时点的颜色
                    color: '#74A0F9'
                },
                tooltip: {},
                xAxis: {
                    data: this.labelTitle
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1,
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                series: [{
                    name: '数量',
                    type: 'bar',
                    data: this.count
                }]
            })
        }
    },
    //一加载页面就调用
    mounted () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope =  [this.dateStrM(start), this.dateStrM(end)]
        this.getPlatformOrdersItemCountM()
    }
}
</script>
<style scoped lang="scss">
</style>