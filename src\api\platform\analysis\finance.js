import service from '@/utils/request'

const { httpPost } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/financialProducts/listFinancialPage/classId',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/financialProducts/update',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/financialProducts/updatePublish',
        params
    })
}
//das
const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/financialProducts/updateNotPublish',
        params
    })
}

export {
    getList,
    batchPublish,
    batchNotPublish,
    edit
}