<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div class="tabs warningTabs" v-loading="formLoading">
            <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="店铺信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="附件资料" name="filesInfo" :disabled="clickTabFlag" >
                </el-tab-pane>
                <el-tab-pane label="企业信息" name="enterpriseInfo" :disabled="clickTabFlag" v-if="enterprise">
                </el-tab-pane>
                <el-tab-pane label="管理员信息" name="adminInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="个体户信息" name="individualHouseholdInfoCon" :disabled="clickTabFlag" v-if="individualHousehold">
                </el-tab-pane>
                <div id="tabs-content">
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">店铺信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="店铺名称：" prop="shopName">
                                            <span>{{ formData.shopName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="店铺类型：" prop="shopType">
                                            <span v-if="formData.shopType == '1'">企业店铺</span>
                                            <span v-if="formData.shopType == '0'">个体户店铺</span>
                                            <span v-if="formData.shopType == '2'">个人店铺</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="店铺所在地址：" prop="province">
                                            <span>{{ formData.province }}</span>
                                            <span>{{ formData.city }}</span>
                                            <span>{{ formData.county }}</span>
                                            <span>{{ formData.detailedAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="店铺创建时间：" prop="gmtCreate">
                                            <span>{{ formData.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!--                                <el-row>-->
                                <!--                                    <el-col :span="12">-->
                                <!--                                        <el-form-item label="经度：" prop="longitude">-->
                                <!--                                            <span>{{ formData.longitude }}</span>-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                    <el-col :span="12">-->
                                <!--                                        <el-form-item label="纬度：" prop="latitude">-->
                                <!--                                            <span>{{ formData.latitude }}</span>-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                </el-row>-->
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="店铺状态：" prop="state">
                                            <el-tag v-if="formData.state==1" type="success">启用</el-tag>
                                            <el-tag v-if="formData.state==0" type="danger">停用</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="是否自营：" prop="isBusiness">
                                            <span>{{ formData.isBusiness === 1 ? '自营' : '非自营' }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="是否内部店铺：" prop="isInternalShop">
                                            <span>{{ formData.isInternalShop === 1 ? '内部店铺' : '非内部店铺' }}</span>
                                        </el-form-item>
                                    </el-col>
<!--                                    <el-col :span="12">-->
<!--                                        <el-form-item label="是否支持内部结算：" prop="isInternalSettlement">-->
<!--                                            <span>{{ formData.isInternalSettlement === 1 ? '支持' : '不支持' }}</span>-->
<!--                                        </el-form-item>-->
<!--                                    </el-col>-->
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!--                    企业信息-->
                    <div id="enterpriseInfo" class="con" v-if="enterprise">
                        <div class="tabs-title" id="enterpriseInfo">企业信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="enterpriseData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="企业名称：" prop="enterpriseName">
                                            <span>{{ enterpriseData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                            <span>{{ enterpriseData.socialCreditCode }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商：" prop="socialCreditCode">
                                            {{ enterpriseData.isSupplier == 2 ? '是' : '否' }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="法定代表人：" prop="legalRepresentative">
                                            <span>{{ enterpriseData.legalRepresentative }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="注册资本：" prop="registeredCapital">
                                            <span>{{ enterpriseData.registeredCapital }}(万元)</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册地址：" prop="detailedAddress">
                                            <span>{{ enterpriseData.detailedAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="注册日期：" prop="creationTime">
                                            <span>{{ enterpriseData.creationTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <template v-if="enterpriseData.businessLicense">
                                    <el-row>
                                        <el-col :span="24" >
                                            <el-form-item label="营业执照：" prop="businessLicense">
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24" align="center"  >
                                            <el-image style="width: 900px; height: 500px" :src="enterpriseData.businessLicense"></el-image>
                                        </el-col>
                                    </el-row>
                                </template>
                            </el-form>
                        </div>
                    </div>
                    <!--                    附件信息-->
                    <div id="filesInfo" class="con" v-loading="openShopLoading">
                        <div class="tabs-title" id="baseInfo">附件资料</div>
                        <div class="e-table"  style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-button type="primary" @click="batchDownloadFile">批量下载
                                    </el-button>
                                    <el-button type="primary" @click="batchDownloadFilePackage">批量下载并打包
                                    </el-button>
                                </div>
                            </div>
                            <el-table ref="fileTableRef"
                                      border
                                      style="width: 100%"
                                      :data="enterpriseData.files"
                                      class="table"
                                      @row-click="handleCurrentInventoryClick"
                                      @selection-change="selectionChangeHandle"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="name" label="附件名称" width=""></el-table-column>
                                <el-table-column label="操作" width="100">
                                    <template slot-scope="scope">
                                        <el-button type="primary" class="btn-greenYellow" @click="openShopDow(scope.row)">下载
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <!--                    管理员信息-->
                    <div id="adminInfo" class="con">
                        <div class="tabs-title" id="adminInfo">管理员信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="enterpriseData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="姓名：" prop="adminName" >
                                            <span>{{ enterpriseData.adminName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="手机号码：" prop="adminPhone">
                                            <span>{{ enterpriseData.adminPhone }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="身份证号码：" prop="adminNumber">
                                            <span>{{ enterpriseData.adminNumber }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="身份证人像面："  prop="cardPortraitFace" >
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="身份证国徽面："  prop="cardPortraitNationalEmblem">
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12" align="center" >
                                        <el-image style="width: 360px; height: 200px" :src="enterpriseData.cardPortraitFace"></el-image>
                                    </el-col>
                                    <el-col :span="12" align="center">
                                        <el-image style="width: 360px; height: 200px" :src="enterpriseData.cardPortraitNationalEmblem"></el-image>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>

                    <div id="individualHouseholdInfoCon" class="con" v-if="individualHousehold">
                        <div class="tabs-title" id="individualHouseholdInfoCon">个体户信息</div>
                        <div style="width: 100%" class="form">
                            <el-form  label-width="200px" ref="rulesBase22" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="名称：" prop="enterpriseName">
                                            <span>{{ enterpriseData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                            <span>{{ enterpriseData.socialCreditCode }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商：" prop="socialCreditCode">
                                            {{ enterpriseData.isSupplier == 2 ? '是' : '否' }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="经营者姓名：" prop="operator">
                                            <span>{{ enterpriseData.operator }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="经营场所：" prop="placeOfBusiness">
                                            <span>{{ enterpriseData.placeOfBusiness }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册日期：" prop="creationTime">
                                            <span>{{ enterpriseData.creationTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="经营范围：" prop="mainBusiness">
                                            <span>{{ enterpriseData.mainBusiness }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" >
                                        <el-form-item label="营业执照：" prop="businessLicense">
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" align="center">
                                        <el-image style="width: 900px; height: 500px" :src="enterpriseData.businessLicense"></el-image>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import { getInfo } from '@/api/platform/shop/shopManager'
import { previewFile } from '@/api/platform/common/file'
import JSZip from 'jszip'
// eslint-disable-next-line no-unused-vars
import FileSaver from 'file-saver'

export default {

    data () {
        return {
            dataListSelections: [], //选中的数据
            openShopLoading: false,
            formLoading: false,
            individualHousehold: false,
            enterprise: false,
            // 企业信息
            enterpriseData: {},
            //基本信息表单数据
            formData: {},
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    components: {
    },
    created () {
        this.formData = this.$route.params.row
        this.getInfoM()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'enterpriseInfo', 'adminInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 70 + 'px !important'
        },
        // 填补底部空白，以使高度够滚动
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {

        //下载附件
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        openShopDow (fileRow) {
            this.openShopLoading = true
            previewFile({ recordId: fileRow.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = fileRow.name
                a.click()
                window.URL.revokeObjectURL(url)
                this.openShopLoading = false
            }).catch(() => {
                this.openShopLoading = false
            })
        },
        batchDownloadFile () {
            if(this.fileSelectList.length == 0) {
                this.$message.info('未选择文件')
                return
            }
            this.fileSelectList.forEach(t => {
                this.openShopDow(t)
            })
            this.fileSelectList = []
        },
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.fileTableRef.toggleRowSelection(row, row.flag)
        },
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        async batchDownloadFilePackage () {
            this.openShopLoading = true
            let files = this.enterpriseData.files
            if(files == null || files.length == 0) {
                this.$message.info('附件为空！')
                return
            }
            const zip = new JSZip()
            for (let i = 0; i < files.length; i++) {
                let res = await previewFile({ recordId: files[i].fileFarId })
                zip.file(files[i].name, res)
            }
            let content = await zip.generateAsync({ type: 'blob' })
            FileSaver.saveAs(content, this.formData.shopName + '开店附件资料.zip')
            this.openShopLoading = false
        },
        // 获取企业信息
        getInfoM () {
            let params = {
                enterpriseId: this.formData.enterpriseId
            }
            this.formLoading = true
            getInfo(params).then(res => {
                this.enterpriseData = res
                previewFile({ recordId: this.enterpriseData.cardPortraitFaceId }).then(res => {
                    this.enterpriseData.cardPortraitFace = window.URL.createObjectURL(res)
                })
                previewFile({ recordId: this.enterpriseData.cardPortraitNationalEmblemId }).then(res => {
                    this.enterpriseData.cardPortraitNationalEmblem = window.URL.createObjectURL(res)
                })
                previewFile({ recordId: this.enterpriseData.businessLicenseId }).then(res => {
                    this.enterpriseData.businessLicense = window.URL.createObjectURL(res)
                })
                let enterpriseType = this.enterpriseData.enterpriseType
                if(enterpriseType == 0) {
                    this.individualHousehold = true
                    this.enterprise = false
                }
                if(enterpriseType == 1) {
                    this.enterprise = true
                    this.individualHousehold = false
                }
                if(enterpriseType == 2) {
                    this.enterprise = false
                    this.individualHousehold = false
                }
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }else {
                this.$message({
                    message: res.message,
                    type: 'error'
                })
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.warningTabs {
    padding-top: 70px;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
</style>
