import service from '@/utils/request'

const { httpPost, httpGet } = service

const getMaterialInventoryPageList = params => {
    return httpPost({
        url: '/materialMall/productInventory/listPage',
        params
    })
}

const queryPageMaterialDtl = params => {
    params.versionId = '1942518269757186050'
    return httpPost({
        url: '/PCWP2/thirdapi/matarialpurchase/queryPageMaterialDtl',
        params,
        headers: {
            token: 'UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA'
        }
    })
}
const getVersionByOrgId = params => {
    return httpGet({
        url: '/PCWP2/thirdapi/matarialpurchase/getVersionByOrgId',
        params,
    })
}
export {
    getVersionByOrgId,
    queryPageMaterialDtl,
    getMaterialInventoryPageList,
}