import service from '@/utils/request'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const generateComparisonHistory = params => {
    return httpPost({
        url: '/materialMall/productCompareItem/createByProductIds',
        params,
    })
}

const getProductComparisonDetail = params => {
    return httpGet({
        url: '/materialMall/productCompare/getDetail',
        params,
    })
}

const getComparisonHistory = params => {
    return httpPost({
        url: '/materialMall/productCompare/listByEntity',
        params,
    })
}
const  reCompare = (params, id) =>{
    return httpPost({
        url: '/materialMall/productCompareItem/reCompare?id=' + id,
        params,
    })
}

const getComparisonDetail = params => {
    return httpGet({
        url: '/materialMall/productCompare/getDetail',
        params,
    })
}

const exportPdf = params => {
    return httpGet({
        url: '/materialMall/productCompare/exportPdf',
        params,
        responseType: 'blob'
    })
}

const exportMultiplePdf = params => {
    return httpPost({
        url: '/materialMall/productCompare/mergePdf',
        params,
        responseType: 'blob'
    })
}

const deleteBatch = params => {
    return httpPost({
        url: '/materialMall/productCompare/deleteBatch',
        params,
    })
}

export {
    generateComparisonHistory,
    getProductComparisonDetail,
    getComparisonHistory,
    getComparisonDetail,
    exportPdf,
    exportMultiplePdf,
    reCompare,
    deleteBatch
}