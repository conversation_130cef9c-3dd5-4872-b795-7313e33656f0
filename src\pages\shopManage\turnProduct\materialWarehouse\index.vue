<template>    <!-- 自营店新增、上架周材商品 列表 -->
    <div class="base-page" v-loading="showLoading">
        <div class="left" :style="{ height: '100%' }">
            <select-material-class  style="height: 100%" ref="materialClassRef" :productType="0" :is-lc="3"/>
        </div>
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn dfa" style="flex-wrap: wrap;">
                            <el-button type="primary" class="btn-greenYellow" @click="add">新增</el-button>
                            <el-button
                                type="primary" class="btn-greenYellow" @click="updateStateBatch(3)"
                            >批量上架
                            </el-button>
                            <el-upload
                                multiple action="fileUrl"
                                :limit="limitNum"
                                accept=".xls,.xlsx,csv"
                                :file-list="fileList"
                                :before-upload="beforeUpload"
                                :on-exceed="onExceed"
                                :show-file-list="true"
                                :http-request="uploadFile"
                                style="margin-left: 5px"
                            >
                                <el-button  type="primary">上传excel</el-button>
                            </el-upload>
                          <el-button style="margin-left: 5px"
                              type="primary" class="btn-greenYellow" @click="exportShelfLogs()"
                          >上架记录导出
                          </el-button>
                            <el-dropdown @command="btnClick" trigger="click" style="margin-left: 5px">
                                <el-button type="primary">
                                    更多操作<i class="el-icon-arrow-down el-icon--right"></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="onDownload">模板下载</el-dropdown-item>
                                    <el-dropdown-item command="updateStateBatch">批量下架</el-dropdown-item>
<!--                                    <el-dropdown-item command="outputExcelM">导出低值易耗品</el-dropdown-item>-->
                                    <el-dropdown-item command="changeSortValue">批量修改排序值</el-dropdown-item>
                                    <el-dropdown-item command="batchDelete">批量删除</el-dropdown-item>
                                    <el-dropdown-item command="batchDelete">批量删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                    </div>
                    <el-dropdown @command="handleChangeSort" trigger="click" placement="bottom">
                        <span class="pointer">
                            排序方式<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item :command="1" :style="{ color: init.orderBy == 1 ? '#2e61d7' : '' }">
                                排序值
                            </el-dropdown-item>
                            <el-dropdown-item :command="0" :style="{ color: init.orderBy == 0 ? '#2e61d7' : '' }">
                                上架时间
                            </el-dropdown-item>
                            <el-dropdown-item :command="2" :style="{ color: init.orderBy == 2 ? '#2e61d7' : '' }">
                                创建时间
                            </el-dropdown-item>
                            <el-dropdown-item :command="3" :style="{ color: init.orderBy == 3 ? '#2e61d7' : '' }">
                                修改时间
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <div class="search_box" style="width: 400px">
                        <el-input
                            clearable type="text" @blur="handleInputSearch" placeholder="输入搜索关键字"
                            v-model="init.keywords"
                        >
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    @row-click="handleCurrentInventoryClick2" ref="mainTable2" v-loading="tableLoading" class="table"
                    :height="rightTableHeight" :data="tableData" border
                    @selection-change="selectionChangeHandle"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="160">
                        <template v-slot="scope">
                            <el-button
                                v-if="scope.row.state===0"
                                style="padding:0px 8px 0px 8px;"
                                size="mini"
                                type="danger"
                                @click="deleteRow(scope.row)"
                            >删除
                            </el-button>
                            <el-button
                                style="padding:0px 8px 0px 8px;" v-if="scope.row.state===2 || scope.row.state===0"
                                size="mini"
                                type="success"
                                @click="updateState(scope.row,3,'上架')"
                            >上架
                            </el-button>
                            <el-button
                                style="padding:0px 8px 0px 8px;" v-if="scope.row.state===1"
                                size="mini"
                                type="danger"
                                @click="updateState(scope.row,2,'下架')"
                            >下架
                            </el-button>
                            <el-button
                                style="padding:0 8px;"
                                v-if="scope.row.state===4"
                                size="mini"
                                type="success"
                                @click="updateState(scope.row,3,'重新上架')"
                            >重新上架
                            </el-button>
                            <div v-if="scope.row.state===3">
                                <el-button
                                    v-if="scope.row.markUp != null"
                                    style="padding:0 8px;"
                                    size="mini"
                                    type="danger"
                                    @click="updateState(scope.row,2,'取消审核')"
                                >取消审核
                                </el-button>
                                <el-button
                                    v-if="scope.row.markUp == null"
                                    style="padding:0 8px;"
                                    size="mini"
                                    type="success"
                                    @click="handleView(scope.row)"
                                >设置加成率
                                </el-button>
                            </div>
                            <el-button
                                style="padding:0 8px;"
                                v-if="scope.row.state===1"
                                size="mini"
                                type="primary"
                                @click="updateInventory(scope.row)"
                            >修改库存
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="名称" width="200">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.productName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="物资分类" width="240" prop="classPathName">
                    </el-table-column>
                    <el-table-column label="编号" width="240">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.serialNum }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="图片" width="120" type="index">
                        <template v-slot="scope">
                            <el-image
                                style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg"
                                fit="fill"
                            ></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="state">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state==0">待上架</el-tag>
                            <el-tag v-if="scope.row.state==1" type="success">已上架</el-tag>
                            <el-tag v-if="scope.row.state==2" type="danger">已下架</el-tag>
                            <el-tag v-if="scope.row.state==3">审核中</el-tag>
                            <el-tag v-if="scope.row.state==4" type="danger">审核失败</el-tag>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="销售价格" prop="sellPrice"></el-table-column> -->
<!--                    <el-table-column label="原价" prop="originalPrice"/>
                    <el-table-column label="成本价" prop="costPrice"/>-->
                    <!-- <el-table-column label="差价" prop="profitPrice"/> -->
                    <el-table-column label="税率%" prop="taxRate"></el-table-column>
                    <el-table-column label="库存" prop="stock" min-width="100">
                      <template v-slot="scope">
                        <el-input
                            v-if="scope.row.editing"
                            clearable
                            v-model="scope.row.stock"
                            @change="updateInventoryNumber(scope.row)"
                        ></el-input>
                        <span v-else>{{ scope.row.stock }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="失败原因" prop="failReason"/>
                    <el-table-column label="供方名称" width="200" prop="supplierName"/>
                    <!--                   单位换算-->
                    <el-table-column label="材质" width="200" prop="productTexture"/>
<!--                    <el-table-column label="单位换算" width="200" prop="secondUnit"/>-->

                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input
                                clearable v-model="scope.row.shopSort" @change="getChangedRow(scope.row)"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="上架时间" width="160" prop="putawayDate"/>
                    <el-table-column label="创建时间" width="160" prop="gmtCreate"/>
                    <el-table-column label="修改时间" width="160" prop="gmtModified"/>
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--高级查询-->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品名称：">
                            <el-input
                                clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.productName"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品编号：">
                            <el-input
                                clearable maxlength="100" placeholder="请输入商品编号" v-model="filterData.serialNum"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="供应商名称：">
                            <el-input
                                clearable maxlength="100" placeholder="请输入供应商名称"
                                v-model="filterData.supplierName"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="价格以上：">
                            <el-input
                                clearable v-model="filterData.abovePrice" placeholder="请输入价格区间"
                                style="width: 200px"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="价格以下：">
                            <el-input
                                clearable type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间"
                                style="width: 200px"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.createDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="修改时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.modifiedDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="状态：">
                            <div style="display:flex">
                                <el-checkbox
                                    v-model="filterData.stateCheckAll" @change="stateAllSelect"
                                >全部
                                </el-checkbox>
                                <el-checkbox-group
                                    style="margin-left: 30px" v-model="filterData.state" @change="stateGroupChange"
                                >
                                    <el-checkbox
                                        :label="filterData.stateOptions[0].value"
                                    >{{ filterData.stateOptions[0].label }}
                                    </el-checkbox>
                                    <el-checkbox
                                        :label="filterData.stateOptions[1].value"
                                    >{{ filterData.stateOptions[1].label }}
                                    </el-checkbox>
                                    <el-checkbox
                                        :label="filterData.stateOptions[2].value"
                                    >{{ filterData.stateOptions[2].label }}
                                    </el-checkbox>
                                    <el-checkbox
                                        :label="filterData.stateOptions[3].value"
                                    >{{ filterData.stateOptions[3].label }}
                                    </el-checkbox>
                                    <el-checkbox
                                        :label="filterData.stateOptions[4].value"
                                    >{{ filterData.stateOptions[4].label }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="是否自有物资：">
                            <el-radio v-model="filterData.isOneself" :label="1">是</el-radio>
                            <el-radio v-model="filterData.isOneself" :label="0">否</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="排序：">
                            <el-radio v-model="init.orderBy" :label="1">按排序值排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="2">按创建时间排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="3">按修改时间排序</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
        </el-dialog>

        <el-dialog v-dialogDrag title="选择物资库" :visible.sync="showDeviceDialog" width="70%">
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            clearable type="text" @blur="getDeviceInventory" placeholder="输入搜索关键字"
                            v-model="inventory.keywords"
                        >
                            <img
                                :src="require('@/assets/search.png')" slot="suffix" @click="getDeviceInventory" alt=""
                            />
                        </el-input>
                    </div>
                </div>
                <el-table
                    v-loading='deviceInventoryLoading'
                    @selection-change="selectDeviceRow"
                    highlight-current-row
                    border
                    :data="inventory.tableData"
                    class="table"
                    @row-click="handleCurrentInventoryClick"
                    :max-height="$store.state.tableHeight"
                    ref="mainTable"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="relevanceId" label="编号" width="200"></el-table-column>
                    <el-table-column prop="productTitle" label="名称" width="200"></el-table-column>
                    <el-table-column prop="specTitle" label="规格型号"></el-table-column>
                    <!--                    <el-table-column prop="className" label="类别名称"></el-table-column>-->
                    <el-table-column prop="unit" label="计量单位"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="inventory.tableData && inventory.tableData.length > 0"
                    :total="inventory.paginationInfo.total"
                    :pageSize.sync="inventory.paginationInfo.pageSize"
                    :currentPage.sync="inventory.paginationInfo.currentPage"
                    @currentChange="getDeviceInventory"
                    @sizeChange="getDeviceInventory"
                />
                <div class="buttons" style="margin-top: 20px">
                    <el-button type="primary" class="btn-blue" @click="inventoryImportBatch">确定</el-button>
                    <el-button @click="showDeviceDialog = false">取消</el-button>
                </div>
            </span>

        </el-dialog>
        <!--        excel导入返回弹窗-->
        <el-dialog v-dialogDrag title="导入结果" :visible.sync="showImportExcelLoading" width="70%">
            <div class="e-table" style="background-color: #fff">
                <el-table
                    border
                    :data="excelResult"
                    class="table"
                    :max-height="$store.state.tableHeight"
                    ref="mainTable"
                >
                    <el-table-column prop="id" label="序号" width="60"></el-table-column>
                    <el-table-column prop="productName" label="商品名称"></el-table-column>
                    <el-table-column prop="state" label="状态" width="80">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state==1" type="success">成功</el-tag>
                            <el-tag v-if="scope.row.state==0" type="danger">失败</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="fail" label="失败原因"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <el-button class="mt20" @click="showImportExcelLoading = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog v-dialogDrag title="修改加成率" :visible.sync="showJcLoading" width="70%">
            <span>上架【{{productName}}】商品是二级供应商商品，请问是否更改该商品加成率数值？</span>
            <span slot="footer">
                <el-button class="mt20" @click="toDetail">确认修改</el-button>
            </span>
            <span slot="footer">
                <el-button class="mt20" @click="showJcLoading = false" style="margin-left: 10px;">保持不变</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import ImportExcel from '@/components/importExcel.vue'
import Pagination from '@/components/pagination/pagination'
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapMutations, mapState } from 'vuex'
import {
    batchDel,
    excelTemplate, exportShelfLog,
    getMaterialPageList,
    importBatchMaterial, shopUploadLcProductMallExcelFile, shopUploadProductMallZoneExcelFile,
    updateBatch,
    updateProductState,
} from '@/api/shopManage/product/materialManage'
import { getMaterialInventoryPageList } from '@/api/shopManage/product/prodcutInventory'
import { outputExcel, updateProductStock }  from '@/api/platform/product/materialManage'

export default {
    components: {
        // eslint-disable-next-line vue/no-unused-components
        SelectMaterialClass, Pagination, ImportExcel
    },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            fileUrl: '', //上传文件的域名地址
            limitNum: 1, //文件上传个数限制

            fileList: [], //文件列表
            showLoading: false,
            showImportExcelLoading: false,
            deviceInventoryLoading: false,
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                inventory: {
                    state: 1,
                    productType: 2,
                },
                // state: [0, 2],
                state: [0, 1, 2, 3, 4],
                productType: 2,
                orderBy: 2,
                classId: null,
                keywords: null,
                classPath: [],
            },
            // 商品库
            showDeviceDialog: false,
            inventory: {
                selectRow: [],
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            dataListSelections: [], //表格选中的数据
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                supplierName: null,
                productName: null,
                serialNum: null,
                stateCheckAll: false, // 选择全局
                state: [], // 状态
                isOneself: null,
                belowPrice: null,
                abovePrice: null,
                createDate: [], // 创建时间
                modifiedDate: [], // 修改时间
                stateOptions: [{
                    value: 0,
                    label: '待上架'
                }, {
                    value: 1,
                    label: '已上架'
                }, {
                    value: 2,
                    label: '已下架'
                }, {
                    value: 3,
                    label: '待审核'
                }, {
                    value: 4,
                    label: '审核失败'
                }],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            excelResult: [],
            productId: null,
            showJcLoading: false,
            productName: ''
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        // this.getTableData()
    },
    methods: {
        toDetail () {
            this.skipView( { productId: this.productId } )
            this.showJcLoading = false
        },
        //文件上传之前的钩子,可以做一些验证或限制
        beforeUpload (file) {
            let regExp = file.name.replace(/.+\./, '')
            let lower = regExp.toLowerCase() //把大写字符串全部转为小写字符串
            let suffix = ['xls', 'xlsx']
            if (suffix.indexOf(lower) === -1) {
                return this.$message.warning('请上传后缀名为 xls、xlsx 的附件 !')
            }
            let isLt2M = file.size / 1024 / 1024 < 5
            if (!isLt2M) {
                return this.$message.error('请上传文件大小不能超过 5MB 的附件 !')
            }
        },
        //文件超出个数限制时的钩子
        onExceed (files, fileList) {
            return this.$message.warning(`只能选择${this.limitNum}个文件,当前共选择了${files.length + fileList.length}个`)
        },
        uploadZoneFile (res) {
            let formData = new FormData()
            formData.append('file', res.file)
            formData.append('shopType', 2)
            this.tableLoading = true
            shopUploadProductMallZoneExcelFile(formData).then(res => {
                this.tableLoading = false
                this.showExcelResult(res)
                this.getTableData()
                this.fileList = []
            }).catch(() => {
                this.getTableData()
                this.fileList = []
            })
        },
        uploadFile (res) {
            console.log(res.file, 'res')
            let formData = new FormData()
            formData.append('file', res.file)
            this.tableLoading = true
            shopUploadLcProductMallExcelFile(formData).then(res => {
                this.tableLoading = false
                this.showExcelResult(res)
                this.getTableData()
                this.fileList = []
            }).catch(() => {
                this.getTableData()
                this.fileList = []
            })
        },
        btnClick (command) {
            console.log(command)
            let actions = {
                'onDownload': () => this.onDownload(),
                'updateStateBatch': () => this.updateStateBatch(2),
                'outputExcelM': () => this.outputExcelM(),
                'changeSortValue': () => this.changeSortValue(),
                'batchDelete': () => this.batchDelete()
            }
            actions[command]()
        },
        showExcelResult (result) {
            this.excelResult = result
            this.showImportExcelLoading = true
        },
        outputExcelM () {
            this.$confirm('确认导出低值易耗品文件吗？', '提示', {}).then(() => {
                let params = {
                    classId: '1',
                    state: 1,
                }
                this.tableLoading = true
                outputExcel(params).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '低值易耗品数据.xlsx'
                    a.click()
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        onDownload () {
            showLoading()
            excelTemplate({ productType: this.init.inventory.productType }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '临购物资模板.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                hideLoading()
            })
        },
        handleChangeSort (value) {
            this.init.orderBy = value
        },
        // 开始导入
        inventoryImportBatch () {
            if (this.inventory.selectRow.length === 0) {
                return this.$message('请勾选要导入的数据！')
            }
            this.clientPop('info', '您确定要批量导入入这些数据吗！', async () => {
                this.inventory.selectRow.forEach(t => {
                    t.productType = this.init.productType
                    t.classId = this.init.classId
                })
                this.deviceInventoryLoading = true
                importBatchMaterial(this.inventory.selectRow).then(res => {
                    this.message(res)
                    this.inventory.selectRow = []
                    this.showDeviceDialog = false
                    this.getTableData()
                    this.deviceInventoryLoading = false
                }).catch(() => {
                    this.deviceInventoryLoading = false
                })
            })
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        // 选择
        selectDeviceRow (value) {
            this.inventory.selectRow = value
        },
        // 批量导入
        importBatchClick () {
            if (this.init.classId == null) {
                return this.$message('请先选择分类再导入！')
            }
            this.showDeviceDialog = true
            this.getDeviceInventory()
        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                state: this.init.inventory.state,
                productType: this.init.productType,
                page: this.inventory.paginationInfo.currentPage,
                limit: this.inventory.paginationInfo.pageSize,
            }
            if (this.inventory.keywords != null) {
                params.keyword = this.inventory.keywords
            }
            this.deviceInventoryLoading = true
            getMaterialInventoryPageList(params).then(res => {
                this.inventory.tableData = res.list
                this.inventory.paginationInfo.total = res.totalCount
                this.inventory.paginationInfo.pageSize = res.pageSize
                this.inventory.paginationInfo.currentPage = res.currPage
                this.deviceInventoryLoading = false
            }).catch(() => {
                this.deviceInventoryLoading = false
            })
        },
        // 批量修改排序
        changeSortValue () {
            if (this.changedRow.length === 0) {
                return this.$message('未修改列表当中的排序值！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                updateBatch(this.changedRow).then(res => {
                    this.message(res)
                    this.changedRow = []
                    this.getTableData()
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if (row.shopSort <= 0) {
                row.shopSort = 0
            }
            if (this.changedRow.length === 0) {
                this.changedRow.push({ productId: row.productId, shopSort: row.shopSort })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.productId === row.productId) {
                    t.shopSort = row.shopSort
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push({ productId: row.productId, shopSort: row.shopSort })
            }
            flag = true
        },
        // 修改库存数量
        updateInventoryNumber (row) {
            const newStock = parseInt(row.stock, 10)
            if (isNaN(newStock) || newStock < 0) {
                this.$message.error('请输入有效的库存数量')
                return
            }
            let params = {
                productId: row.productId,
                state: row.state,
                stock: newStock
            }
            updateProductStock(params).then(res =>{
                row.editing = false
                this.message(res)
            }).catch(() => {
                row.editing = false
            })
        },
        // 修改库存
        updateInventory (row) {
            // 先将所有行的 editing 设为 false
            this.tableData.forEach(item => this.$set(item, 'editing', false))
            // 设置当前行 editing 为 true
            row.editing = true
        },
        skipView (data) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/product/turnMaterialWarehouseDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'turnMaterialWarehouseDetail',
                params: {
                    row: data
                }
            })
        },
        //新增
        add () {
            let data = {}
            data.viewType = 'add'
            data.classPath = this.classPath
            this.skipView(data)
        },
        // 物资详情
        handleView (row) {
            row.classPath = this.classPath
            this.skipView(row)
        },
        resetSearchConditions () {
            this.filterData.supplierName = ''
            this.filterData.productName = ''
            this.filterData.serialNum = ''
            this.filterData.createDate = []
            this.filterData.modifiedDate = []
            this.filterData.belowPrice = ''
            this.filterData.abovePrice = ''
            this.filterData.isOneself = null
            this.filterData.state = []
            this.filterData.stateCheckAll = false
        },
        // 高级搜索确认
        confirmSearch () {
            this.init.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateOptions.length
        },
        // 状态全选
        stateAllSelect (value) {
            if (value) {
                this.filterData.state = this.filterData.stateOptions.map(t => {
                    return t.value
                })
            } else {
                this.filterData.state = []
            }
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.mainTable2.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 单个上架
        updateState (row, state, title) {
            if (state == '3' && this.userInfo.isBusiness == 1 && row.markUp == null) {//上架
                //弹窗引导他设置加成率
                this.productId = row.productId
                this.productName = row.relevanceName
                this.showJcLoading = true
                return
            }
            let productName = row.relevanceName
            let params = {
                productIds: [row.productId],
                state: state
            }
            this.clientPop('info', '您确定要对物资【' + productName + '】进行【' + title + '】操作吗?', async () => {
                updateProductState(params).then(res => {
                    this.getTableData()
                    this.message(res)
                })
            })
        },
        // 单个删除
        deleteRow (row) {
            this.clientPop('info', '您确定要对物资【' + row.productName + '】进行【删除】操作吗？', async () => {
                batchDel([row.productId]).then(res => {
                    this.message(res)
                    this.getTableData()
                })
            })
        },
        exportShelfLogs () {
            if (this.dataListSelections.length === 0) {
                return this.$message('请勾选要导出记录的商品！')
            }
            let ids = this.dataListSelections.map(item => {
                return item.productId
            })
            this.tableLoading = false
            exportShelfLog(ids).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '上架记录.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                this.currentQuery.ids = []
                this.dataListSelections = []
                this.$message.success('操作成功')
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        // 批量删除
        batchDelete () {
            if (this.dataListSelections.length === 0) {
                return this.$message('请勾选要删除的数据！')
            }
            let ids = this.dataListSelections.map(item => {
                return item.productId
            })
            this.clientPop('info', '您确定要批量删除这些数据吗！', async () => {
                this.tableLoading = true
                batchDel(ids).then(res => {
                    this.message(res)
                    this.dataListSelections = []
                    this.getTableData()
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        // 批量修改上下架状态
        updateStateBatch (state) {
            if (this.dataListSelections.length === 0) return this.$message('请勾选要修改的数据！')
            let params = {
                productIds: this.dataListSelections.map(item => {
                    return item.productId
                }),
                state: state
            }
            this.clientPop('info', '您确定要批量操作这些物资吗！', async () => {
                this.tableLoading = true
                updateProductState(params).then(res => {
                    this.message(res)
                    this.dataListSelections = []
                    this.getTableData()
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.classPath = nodePath
            this.getTableData()
        },
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                state: this.init.state,
                shopId: this.init.shopId,
                productType: this.init.productType,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.serialNum != null) {
                params.serialNum = this.filterData.serialNum
            }
            if (this.filterData.state.length !== 0) {
                params.state = this.filterData.state
            }
            if (this.init.classId != null) {
                params.classId = this.init.classId
            }
            if (this.init.keywords != null) {

                params.keywords = this.init.keywords
            }
            if (this.filterData.modifiedDate != null) {
                params.startModifiedDate = this.filterData.modifiedDate[0]
                params.endModifiedDate = this.filterData.modifiedDate[1]
            }
            if (this.filterData.createDate != null) {
                params.startCreateDate = this.filterData.createDate[0]
                params.endCreateDate = this.filterData.createDate[1]
            }
            if (this.init.orderBy != null) {
                params.orderBy = this.init.orderBy
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.isOneself != null) {
                params.isOneself = this.filterData.isOneself
            }
            this.tableLoading = true
            getMaterialPageList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

.el-dialog__body {
    margin: 220px;
}

.base-page .left {
    min-width: 200px;
    height: 100%;
    padding: 0;
    overflow: auto;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .el-dropdown {
    min-width: 75px;
    margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}
</style>
