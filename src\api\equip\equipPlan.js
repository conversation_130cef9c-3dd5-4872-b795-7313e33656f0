import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet, httpGetFile } = service

//装备管理 - 装备计划模块 接口
const request = {
    //高级分页查询配置计划
    configuration (params) {
        return httpPost({
            url: '/facilityplan/list/advanced',
            params
        })
    },
    //配置计划列表高级搜索
    configPlanAdvanced (params) {
        return httpPost({
            url: '/facilityplan/list/advanced',
            params
        })
    },
    //配置计划新增
    configPlanAdd (params) {
        return httpPost({
            url: '/facilityplan/add',
            params
        })
    },
    //配置计划新增
    updateConfigBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/update',
            params
        })
    },
    //配置计划作废
    configPlanCancel (params) {
        return httpPost({
            url: '/facilityplan/cancel',
            params
        })
    },
    //配置计划变更
    configPlanChange (params) {
        return httpPost({
            url: '/facilityplan/change/plan/configuration',
            params
        })
    },
    //配置计划提交
    configPlanCommit (params) {
        return httpPostForm({
            url: '/facilityplan/commit/plan/configuration',
            params
        })
    },
    //根据主键删除配置计划
    configPlanDelete (params) {
        return httpPostForm({
            url: '/facilityplan/delete',
            params: {
                planConfigurationIdList: params
            }
        })
    },
    //删除或取消删除配置计划详情明细
    configPlanListDelete (params) {
        return httpPost({
            url: '/facilityplan/change/delete',
            params
        })
    },
    //获取配置计划基础信息
    getConfigBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/get/project/details',
            params
        })
    },
    //获取初始化配置计划基础信息
    getInitConfigBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/init/project/details',
            params
        })
    },
    //获取装备计划明细
    getConfiPlanInfo (params) {
        return httpPost({
            url: '/facilityplan/list/equiomente/plan/details',
            params
        })
    },
    //(配置计划明细)获取装备计划明细
    getconfigurationInfo (params) {
        return httpPost({
            url: '/facilityplan/list/equiomente/plan/configuration',
            params
        })
    },
    //新增\更新\删除计划明细  操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateDetailInfo (params) {
        return httpPost({
            url: '/facilityplan/update/equiomente/plan/del',
            params: params
        })
    },
    //获取所有装备分类及分类下的装备
    getEquipmentCategory (params) {
        return httpGet({
            url: '/facilityplan/list/category/equipment',
            params
        })
    },
    //获取装备分类下的装备
    getEquipment (params) {
        return httpPost({
            url: '/facilityplan/equipment/find',
            params
        })
    },
    //获取配置计划变更历史
    getConfigChangeHistory (params) {
        return httpPost({
            url: '/facilityplan/list/change/history',
            params
        })
    },
    //配置计划生成计划编号
    getPlanBillNo (params) {
        return httpPostForm({
            url: '/facilityplan/bill/configuration',
            params
        })
    },
    //配置计划变更基础信息查询
    getConfigBaseInfoChange (billId) {
        return httpGet({
            url: '/facilityplan/get/change/project/details',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //配置计划变更基础新增
    baseInfoChangeAdd (params) {
        return httpPost({
            url: '/facilityplan/add/change/plan/configuration',
            params
        })
    },
    //更新配置计划变更基本信息
    updateBaseInfoChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/configuration',
            params
        })
    },
    //变更明细查询
    getChangeDetails (billId) {
        return httpGet({
            url: '/facilityplan/list/configuration/change/plan/details',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //更新变更计划明细
    updateDetailInfoChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/configuration/dtl',
            params
        })
    },
    //删除配置计划变更历史
    deleteConfigChangeHistoy (params) {
        return httpPostForm({
            url: '/facilityplan/delete/change',
            params: {
                planConfigurationIdList: params
            }
        })
    },
    //===============================================================================================租赁计划接口
    //租凭计划明细汇总显示
    leaseDtlCollect (params) {
        return httpPost({
            url: '/facilityplan/get/project/lease/summary',
            params
        })
    },
    //租凭计划明细汇总导出
    leaseDtlImport () {
        return httpGetFile({
            url: '/facilityplan/summary/export'
        })
    },
    //高级分页查询添加租凭计划
    leaseList (params) {
        return httpPost({
            url: '/facilityplan/advanced',
            params
        })
    },
    //添加租凭计划
    leaseListAdd (params) {
        return httpPost({
            url: '/facilityplan/add/plan/lease',
            params
        })
    },
    //租赁计划生成计划编号
    getLeaseBillNo (params) {
        return httpPostForm({
            url: '/facilityplan/bill/lease',
            params
        })
    },
    //获取租凭计划基础信息
    getLeaseBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/get/project/lease/details',
            params
        })
    },
    //获取初始化租凭计划基础信息
    getInitLeaseBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/init/project/lease/details',
            params
        })
    },
    //获取租凭计划明细
    getLeasePlanInfo (params) {
        return httpPost({
            url: '/facilityplan/list/lease/plan/details',
            params
        })
    },
    //修改租凭计划基础信息
    updateLease (params) {
        return httpPost({
            url: '/facilityplan/update/plan/lease',
            params
        })
    },
    //提交租凭计划
    leaseCommit (params) {
        return httpPostForm({
            url: '/facilityplan/commit/plan/lease',
            params
        })
    },
    //根据主键删除租赁计划
    leaseDelete (params) {
        return httpPostForm({
            url: '/facilityplan/delete/plan/lease',
            params: {
                planLeaseIdList: params
            }
        })
    },
    //租赁计划变更基础信息查询
    getLeaseBaseInfoChange (billId) {
        return httpGet({
            url: '/facilityplan/get/change/project/lease/details',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //获取租凭计划明细
    getPlanDetail (params) {
        return httpPost({
            url: '/facilityplan/list/lease/plan/details',
            params
        })
    },
    //租赁计划 新增\更新\删除计划明细  操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateLeaseDetailInfo (params) {
        return httpPost({
            url: '/facilityplan/update/plan/lease/dtl',
            params: params
        })
    },
    //新增\更新\删除 推荐租赁商 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateBusiness (params) {
        return httpPost({
            url: '/facilityplan/update/rental/business',
            params: params
        })
    },
    //获取租凭计划  推荐租赁商
    getBusiness (params) {
        return httpPost({
            url: '/facilityplan/list/rental/business',
            params
        })
    },
    //获取租赁计划变更历史
    getleaseChangeHistory (params) {
        return httpPost({
            url: '/facilityplan/list/history/plan/lease',
            params
        })
    },
    //变更基本信息新增
    baseLeaseChangeAdd (params) {
        return httpPost({
            url: '/facilityplan/add/change/plan/lease',
            params
        })
    },
    //变更计划明细查询
    getleaseDetailsChange (id) {
        return httpGet({
            url: '/facilityplan/list/lease/change/plan/details',
            params: {
                id: id,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //变更租赁计划基本信息修改
    updateleaseBaseInfoChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/lease',
            params
        })
    },
    //变更租赁计划 计划明细修改
    updateleasePlanChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/lease/dtl',
            params
        })
    },
    //删除租赁计划变更历史
    deleteLeaseChangeHistoy (params) {
        return httpPostForm({
            url: '/facilityplan/change/delete/plan/lease',
            params: {
                planLeaseIdList: params
            }
        })
    },
    //===============================================================================================年度采购预算登记接口

    //高级查询年度采购预算登记
    procurementPlanList (params) {
        return httpPost({
            url: '/facilityplan/list/plan/year',
            params
        })
    },
    //添加租凭计划
    procurementPlanAdd (params) {
        return httpPost({
            url: '/facilityplan/add/plan/year/budget',
            params
        })
    },
    //删除年度采购预算登记
    procurementPlanDelete (params) {
        return httpPostForm({
            url: '/facilityplan/delete/plan/year/budget',
            params: {
                planYearBudgetIdList: params
            }
        })
    },
    //提交年度采购预算登记
    procurementPlanCommit (params) {
        return httpPostForm({
            url: '/facilityplan/commit/plan/year/budget',
            params
        })
    },
    //获取年度采购预算登记基础信息
    getProcurementBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/get/plan/year/budget',
            params
        })
    },
    //获取初始化年度采购预算登记基础信息
    getInitProcurementBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/init/plan/year/budget',
            params
        })
    },
    //获取年度采购预算登记计划明细
    getProcurementPlanInfo (params) {
        return httpPost({
            url: '/facilityplan/list/year/plan/details',
            params
        })
    },
    //年度采购登记生成计划编号
    getProcurementBillNo (params) {
        return httpPostForm({
            url: '/facilityplan/bill/year/budget',
            params
        })
    },
    //年度采购预算变更基础信息查询
    getProcureBaseInfoChange (billId) {
        return httpGet({
            url: '/facilityplan/get/change/plan/year/budget',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //修改年度采购预算登记基本信息
    updateProcureBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/update/plan/year/budget',
            params
        })
    },
    //修改年度采购预算登记 预算明细
    updateBudgetDtl (params) {
        return httpPost({
            url: '/facilityplan/update/plan/year/budget/dtl',
            params
        })
    },
    //年度采购预算登记 变更历史查询
    getBudgetChangeHistory (params) {
        return httpPost({
            url: '/facilityplan/list/history/plan/year',
            params
        })
    },
    //获取年度采购预算登记计划明细变更
    getProcurementPlanInfoChange (id) {
        return httpGet({
            url: '/facilityplan/list/year/change/plan/details',
            params: {
                id: id,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //年度采购预算登记  变更 基本信息新增
    ProcureBaseInfoChangeAdd (params) {
        return httpPost({
            url: '/facilityplan/add/change/plan/year/budget',
            params
        })
    },
    //年度采购预算登记  变更 基本信息更新
    updateProcureBaseInfoChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/year/budget',
            params
        })
    },
    //修改年度采购预算登记 预算明细
    updateBudgetDtlChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/year/budget/dtl',
            params
        })
    },
    //是否做过年度
    isJudge (params) {
        return httpGet({
            url: '/facilityplan/judge',
            params: {
                time: params
            }
        })
    },
    //删除年度预算登记变更历史
    deleteBudgetChangeHistoy (params) {
        return httpPostForm({
            url: '/facilityplan/delete/change/plan/year/budget',
            params: {
                planYearBudgetIdList: params
            }
        })
    },

    //===============================================================================================采购计划列表接口

    //高级查询采购计划列表
    purchasePlanList (params) {
        return httpPost({
            url: '/facilityplan/list/advanced/plan/buy',
            params
        })
    },
    //获取采购计划列表基础信息
    purchaseBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/get/plan/buy',
            params
        })
    },
    //添加采购计划基本信息
    purchasePlanAdd (params) {
        return httpPost({
            url: '/facilityplan/add/plan/buy',
            params
        })
    },
    //更新采购计划基本信息
    updatePurchasePlanBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/update/plan/buy',
            params
        })
    },
    //删除采购计划列表
    purchasePlanDelete (params) {
        return httpPost({
            url: '/facilityplan/delete/plan/buy',
            params
        })
    },
    //提交采购计划列表
    purchasePlanCommit (params) {
        return httpPostForm({
            url: '/facilityplan/commit/plan/buy',
            params
        })
    },
    //采购计划列表生成计划编号
    purchaseBillNo (params) {
        return httpPostForm({
            url: '/facilityplan/bill/buy',
            params
        })
    },
    //采购计划变更基础信息查询
    getPurchaseBaseInfoChange (billId) {
        return httpGet({
            url: '/facilityplan/get/change/plan/buy',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //获取初始化采购计划基础信息查询
    getInitPurchaseBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/init/plan/buy',
            params
        })
    },
    //获取采购计划列表明细
    purchasePlanInfo (params) {
        return httpPost({
            url: '/facilityplan/list/plan/buy/dtl',
            params
        })
    },
    //新增\更新\删除 采购计划 计划明细 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updatePurchasePlanInfo (params) {
        return httpPost({
            url: '/facilityplan/update/plan/buy/dtl',
            params
        })
    },
    //获取采购计划变更历史
    getPurchasePlanChangeHistory (params) {
        return httpPost({
            url: '/facilityplan/list/plan/buy/change/history',
            params
        })
    },
    //采购计划变更基础新增
    purchaseBaseInfoChangeAdd (params) {
        return httpPost({
            url: '/facilityplan/add/change/plan/buy',
            params
        })
    },
    //更新计划变更基本信息
    updatePurchaseBaseInfoChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/buy',
            params
        })
    },
    //变更计划明细查询
    getChangePurchaseDetails (billId) {
        return httpGet({
            url: '/facilityplan/list/buy/change/plan/details',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //更新变更计划明细
    updatePurchaseDetailChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/buy/dtl',
            params
        })
    },
    //删除采购计划变更历史
    deletePurchaseChangeHistoy (params) {
        return httpPostForm({
            url: '/facilityplan/change/delete/plan/buy',
            params: {
                id: params
            }
        })
    },
    //通过采购计划单据时间获取年度采购预算登记装备
    getYearBudgetEquip (params) {
        return httpPostForm({
            url: '/facilityplan/year/budget/equip',
            params: {
                time: params
            }
        })
    },
    //===============================================================================================大修计划接口

    //高级查询大修计划
    majorPlanList (params) {
        return httpPost({
            url: '/facilityplan/list/advanced/plan/repair',
            params
        })
    },
    //添加大修计划
    majorPlanAdd (params) {
        return httpPost({
            url: '/facilityplan/add/plan/repair',
            params
        })
    },
    //删除大修计划
    majorPlanDelete (params) {
        return httpPostForm({
            url: '/facilityplan/delete/plan/repair',
            params: {
                planRepairIdList: params
            }
        })
    },
    //提交大修计划
    majorPlanCommit (params) {
        return httpPostForm({
            url: '/facilityplan/commit/plan/repair',
            params
        })
    },
    //获取大修计划基础信息
    majorBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/get/plan/repair',
            params
        })
    },
    //获取初始化大修计划基础信息
    getInitmajorBaseInfo (params) {
        return httpPost({
            url: '/facilityplan/init/plan/repair',
            params
        })
    },
    //获取大修计划明细
    majorPlanInfo (params) {
        return httpPost({
            url: '/facilityplan/list/plan/repair/dtl',
            params
        })
    },
    //大修计划生成计划编号
    majorBillNo (params) {
        return httpPostForm({
            url: '/facilityplan/bill/repair',
            params
        })
    },
    //修改大修计划
    updatemajorPlan (params) {
        return httpPost({
            url: '/facilityplan/update/plan/repair',
            params
        })
    },
    //获取台账装备
    getActEquipment (params) {
        return httpPost({
            url: '/facilityplan/list/account/equipment',
            params
        })
    },
    //获取大修计划变更历史
    getMajorChangeHistory (params) {
        return httpPost({
            url: '/facilityplan/list/change/plan/repair',
            params
        })
    },
    //新增\更新\删除 大修计划 计划明细 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateDtl (params) {
        return httpPost({
            url: '/facilityplan/update/plan/repair/dtl',
            params
        })
    },
    //大修变更基础信息查询
    getMajorBaseInfoChange (billId) {
        return httpGet({
            url: '/facilityplan/get/change/plan/repair',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //大修计划变更基础新增
    MajorBaseInfoChangeAdd (params) {
        return httpPost({
            url: '/facilityplan/add/change/plan/repair',
            params
        })
    },
    //更新大修计划变更基本信息
    updateMajorBaseInfoChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/repair',
            params
        })
    },
    //变更计划明细查询
    getChangeMajorDetails (billId) {
        return httpGet({
            url: '/facilityplan/list/repair/change/plan/details',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //更新变更计划明细 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updatMajorDetailChange (params) {
        return httpPost({
            url: '/facilityplan/update/change/plan/repair/dtl',
            params
        })
    },
    //删除大修计划变更历史
    deleteMajorChangeHistoy (params) {
        return httpPostForm({
            url: '/facilityplan/delete/change/plan/repair',
            params: {
                planRepairIdList: params
            }
        })
    },

    //===============================================================================================保养提醒计划接口

    //高级查询保养提醒计划
    maintenancePlanList (params) {
        return httpPost({
            url: '/facilityplan/list/advanced/plan/maintenance/reminder',
            params
        })
    },
    //添加保养提醒计划
    maintenancePlanAdd (params) {
        return httpPost({
            url: '/facilityplan/add/plan/maintenance/reminder',
            params
        })
    },
    //修改保养提醒计划
    updateMaintenancePlan (params) {
        return httpPost({
            url: '/facilityplan/update/plan/maintenance/reminder',
            params
        })
    },
    //获取所有装备分类
    getCategory (params) {
        return httpGet({
            url: '/facilityplan/list/category',
            params
        })
    },
    //获取保养提醒计划明细
    maintenancePlanInfo (params) {
        return httpGet({
            url: '/facilityplan/get',
            params
        })
    },

}

export default request
