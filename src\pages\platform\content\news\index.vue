<template>
    <div class="base-page" v-if="showSetOrg">
        <!-- 列表 -->
        <div class="right" v-if="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">发布
                            </el-button>
                            <el-button type="primary" @click="changePublishState(2)" class="btn-delete">取消发布
                            </el-button>
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                            <el-button type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改排序值
                            </el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                        <!--   <el-radio v-model="filterData.orderBy" :label="2">按创建时间排序</el-radio>-->
                        <el-radio v-model="filterData.orderBy" :label="3">按修改时间排序</el-radio>
                        <!--        <el-radio v-model="filterData.orderBy" :label="4">按发布时间排序</el-radio>-->
                        <el-input type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!--搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table">
                <el-table
                    @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" v-loading="isLoading"
                    class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="80">
                        <template v-slot="scope">
                            <span class="action" @click="onDel(scope)"><img
                                src="../../../../assets/btn/delete.png"
                                alt=""
                            ></span>
                        </template>
                    </el-table-column>
                    <!-- 标题 -->
                    <el-table-column label="标题">
                        <template v-slot="scope">
                          <span class="action" @click="handleView(scope)">
                            {{ scope.row.title }}
                          </span>
                        </template>
                    </el-table-column>
                    <!-- 图片 -->
                    <el-table-column label="图片" width="120" v-if="programaKey!= 'LcPriceAnnouncement'" type="index">
                        <template v-slot="scope">
                            <el-image v-if="scope.row.bannerImg!==''" style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.bannerImg"></el-image>
                        </template>
                    </el-table-column>
                    <!-- 信息发布状态 -->
                    <el-table-column label="发布状态" width="120">
                        <template v-slot="scope">
                            {{ scope.row.state == 1 ? '是' : '否' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="首页显示" width="120">
                        <template v-slot="scope">
                            {{ scope.row.home == '1' ? '是' : '否' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="置顶显示" width="120">
                        <template v-slot="scope">
                            {{ scope.row.top == '2' ? '是' : '否' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="发布时间">
                        <template v-slot="scope">
                            {{ scope.row.gmtRelease }}
                        </template>
                    </el-table-column>
                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input type="number" v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <div class="right" v-if="viewList !== true">
            <!-- ---------------------新增编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-if="viewList === 'class'" :style="{ height: tabsContentHeight }">
                <div class="tabs-title">{{ dialogTitle }}</div>
                <el-form :rules="formRules" ref="editForm" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="标题：" prop="title">
                                <el-input v-model="formData.title" placeholder="请输入标题名称" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input v-model="formData.sort" type="number" placeholder="填写排序值">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row  v-if="programaKey!= 'LcPriceAnnouncement'">
                        <el-col :span="12" >
                            <el-form-item width="120px" label="是否主页显示：" prop="home">
                                <template>
                                    <el-radio v-model="formData.home" label="1">是</el-radio>
                                    <el-radio v-model="formData.home" label="2">否</el-radio>
                                </template>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" >
                            <el-form-item width="120px" label="是否顶部显示：" prop="top">
                                <template>
                                    <el-radio v-model="formData.top" label="2">是</el-radio>
                                    <el-radio v-model="formData.top" label="1">否</el-radio>
                                </template>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row v-if="formData.home==1">
                        <el-col :span="20">
                            <el-form-item class="uploader" label="首页图片：" prop="bannerImg">
                                <el-upload
                                    class="avatar-uploader"
                                    drag
                                    :limit="1"
                                    action="fakeaction"
                                    :before-upload="beforePicFileUpload"
                                    :http-request="uploadPicFile"
                                    :on-change="handleUploadChange"
                                    accept="image/*"
                                    :show-file-list="false"
                                    list-type="picture"
                                >
                                    <el-image
                                        v-if="formData.bannerImg"
                                        :src="imgUrlPrefixAdd + formData.bannerImg"
                                        fit="contain"
                                    ></el-image>
                                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    <div slot="tip" class="el-upload__tip">仅支持上传图片文件，建议图片比例为5：4</div>
                                </el-upload>
                                <el-progress v-show="uploadInProgress" :percentage="uploadPercentage"></el-progress>

                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="120px" label="发布时间：" prop="gmtReleaseTime">
                                <el-date-picker v-model="formData.gmtRelease" type="datetime" placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="内容管理：" prop="content">
                                <el-col :span="24">
                                    <editor v-model="formData.content"></editor>
                                </el-col>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="margin-top: 20px;">
                        <el-col :span="20">
                            <el-form-item class="upload-item" label="附件资料：">
                                <el-upload
                                    action="fakeaction"
                                    multiple
                                    :limit="20"
                                    :show-file-list="true"
                                    :file-list="fileList"
                                    :before-upload="beforeOneOfFilesUpload"
                                    :http-request="uploadOneOfFiles"
                                    :on-remove="handleRemove"
                                    accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                >
                                    <el-button size="small" type="primary">点击上传</el-button>
                                    <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="30%">
            <el-form :model="queryForm" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="24" width="60">
                        <el-form-item label="标题名称：">
                            <el-input v-model="filterData.title" placeholder="请输入标题名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="发布状态：">
                            <el-select v-model="filterData.state" clearable placeholder="发布状态">
                                <el-option v-for="item in stateFilter" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="首页显示：">
                            <el-select v-model="filterData.home" clearable placeholder="首页显示">
                                <el-option v-for="item in homeFilter" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="顶部显示：">
                            <el-select v-model="filterData.top" clearable placeholder="顶部显示">
                                <el-option v-for="item in topFilter" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import {
    batchDelete,
    batchNotPublish,
    batchPublish,
    changeSortValue,
    create,
    del,
    edit,
    getList
} from '@/api/platform/content/richContent'
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapMutations, mapState } from 'vuex'
import { uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import editor from '@/components/quillEditor'

export default {
    components: {
        ComPagination, editor
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        },
        // 监听路由全路径
        '$route.fullPath': {
            handler () {
                this.getTableData()
            }
        },
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState({
            numUnitOptions: state => state.selectOptions.numUnitOptions
        }),
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) return this.screenHeight - 244
            return this.screenHeight - 291
        },
        tabsContentHeight () {
            return this.screenHeight - 190 + 'px !important'
        }
    },
    data () {
        return {
            fileList: [],
            uploadInProgress: false,
            uploadPercentage: 0,

            bannerImg: null, // 图片地址
            alertName: '信息',
            queryVisible: false,
            action: '编辑',
            dialogTitle: '基本信息',
            classType: '0',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            showSetOrg: true, //设置使用机构
            keywords: '',
            currentClass: null,
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            programaKey: 'aboutUs',
            queryForm: {
                one: ''
            },
            pages: {
                currPage: 1,
                pageSize: 20,
                totalCount: 1,
            },
            stateFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '发布' },
                { value: 2, label: '未发布' },
            ],
            homeFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '显示' },
                { value: 0, label: '隐藏' },
            ],
            topFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '显示' },
                { value: 0, label: '隐藏' },
            ],
            filterData: {
                title: '',
                state: null,
                home: null,
                top: null,
                orderBy: 3,
                limit: 20,
                page: 1,
                mallType: '0'
            },
            // 表单校验规则
            formRules: {
                title: { required: true, message: '请输入标题名称', trigger: 'blur' },
                content: { required: true, validator: this.validateContent, trigger: 'blur' },
                sort: { required: true, message: '请填写排序值', trigger: 'blur' },
            },
            mallType: [
                { value: null, label: '全部' },
                { value: 0, label: '物资' },
                { value: 1, label: '装备' }
            ],
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            classData: {},
            tableData: [],
            orgDataTable: [],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            allData: [],
            mapObj: null,
            formData: {
                title: '',
                home: 2,
                top: 2,
                mallType: 0, // 商城类型
                content: '',
                gmtRelease: '',
                files: [],
                bannerImg: null
            },
            currencyOptions: [],
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            Array: [],
            requestParams: {},
            defaultPicturePath: '',
            bigPicturePath: '',
            smallPicturePath: '',
            tinyPicturePath: '',
            requestKey: '',
            isLoading: false,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        // showLoading()
        // getDicValue({ dicName: '计量单位', isEnable: true }).then(res => {
        //     this.$store.commit('setNumUnitOptions', res)
        // })
        this.isLoading = true
        if (this.$route.query.programaKey == null) {
            this.programaKey = 'aboutUs'
        } else {
            this.programaKey = this.$route.query.programaKey
            if (this.programaKey === 'LcPriceAnnouncement') {
                this.formData.home = 2
            }
        }
        console.log(this.formData)
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy,
            programaKey: this.programaKey,
            files: this.fileList
        }
        getList(params).then(res => {
            this.isLoading = false
            this.pages = res
            this.tableData = res.list
        })
        // hideLoading()
        this.getParams()
    },
    methods: {
        validateContent (rule, value, callback) {
            if (value === null) {
                return callback(new Error('请填写新闻内容'))
            }
            value = value.slice(3, -4).trim()
            if (value === '' || value === '<p></p>') {
                callback(new Error('请填写新闻内容'))
            }
            callback()
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData,
                programaKey: this.$route.query.programaKey
            }
            this.requestParams.page = this.pages.currPage
        },
        resetSearchConditions () {
            this.filterData = {
                title: '',
                state: null,
                home: null,
                top: null,
                orderBy: 3,
                limit: 20,
                page: 1,
                mallType: '0'
            }
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },

        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if (size > 100) {
                this.$message.error('文件大小不能超过100M')
                return false
            }
            return true
        },
        beforePicFileUpload (file) {
            let size = file.size / 1024 / 1024
            if (size > 10) {
                this.$message.error('文件大小不能超过10M')
                return false
            }
            return true
        },
        // 上传显示进度条
        handleUploadChange (file) {
            if (file.status === 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status === 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        // 图片上传
        async uploadPicFile (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'material')
            form.append('fileType', 1)
            form.append('isResetName', 0)
            let uploadRes = await uploadFile(form)

            if (uploadRes.code != null && uploadRes.code !== 200) {
                this.fileList.push(file)
                this.fileList.pop()
            } else {
                // this.formData.bannerImg =http://118.122.115.195:9022
                // console.log(uploadRes[0])
                // console.log(url)
                this.formData.bannerImg = uploadRes[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                // console.log(this.formData.bannerImg)
                this.$message.success('上传成功')
            }
        },
        // 多文件上传
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            let uploadRes = await uploadFile(form)

            if (uploadRes.code != null && uploadRes.code != 200) {
                this.fileList.push(file)
                this.fileList.pop()
            } else {
                this.$message.success('上传成功')
                this.formData.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 5,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
            }
        },
        handleExceed () {
            this.$message.error('文件个数不能超出20个')
            return false
        },
        handleRemove (file) {
            let files = this.formData.files
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.formData.files = newFiles
                }
            })
        },

        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.contentId
            })
            if (!this.selectedRows[0]) {
                let msg = num === 1 ? '请选择要发布的' + this.alertName : '请选择要取消发布的' + this.alertName
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = num === 1 ? '您确定要发布选中的' + this.alertName + '吗？' : '您确定要取消发布选中的' + this.alertName + '吗？'
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '发布成功', () => {
                                getList(this.requestParams).then(res => {
                                    this.tableData = res.list || []
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '取消发布成功', () => {
                                getList(this.requestParams).then(res => {
                                    this.tableData = res.list || []
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该信息吗？', async () => {
                showLoading()
                del({ id: scope.row.contentId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                }).finally(() => hideLoading())
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.contentId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                }).finally(() => hideLoading())
            })
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (!this.changedRow[0]) {
                return this.changedRow.push({ contentId: row.contentId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if (item.contentId === row.contentId) {
                    return i
                }
            })
            if (arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ contentId: row.contentId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                let msg = '当前没有排序值被修改！'
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '修改成功', () => {
                            this.changedRow = []
                            this.getTableData()
                        })
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            this.isLoading = true
            getList(this.requestParams).then(res => {
                this.tableData = res.list || []
                this.isLoading = false
                this.pages = res
            })
            this.viewList = true
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange (size) {
            this.pages.pageSize = size
            this.getTableData()
        },
        // 上传默认图片信息
        handleDefaultPictureUploadSuccess (res, file) {
            this.defaultPicturePath = URL.createObjectURL(file.raw)
            this.formData.defaultPicturePath = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        // 上传大图片信息
        handleBigPictureUploadSuccess (res, file) {
            this.bigPicturePath = URL.createObjectURL(file.raw)
            this.formData.bigPicturePath = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        // 上传小图片信息
        handleSmallPictureUploadSuccess (res, file) {
            this.smallPicturePath = URL.createObjectURL(file.raw)
            this.formData.smallPicturePath = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        // 上传微小图片信息
        handleTinyPictureUploadSuccess (res, file) {
            this.tinyPicturePath = URL.createObjectURL(file.raw)
            this.formData.tinyPicturePath = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        handleView ({ row }) {
            this.viewList = 'class'
            this.dialogTitle = '基本信息'
            this.formData = JSON.parse(JSON.stringify(row))
            this.fileList = row.files
            this.action = '编辑'
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 复选框选中事件
        selectFun (selection, row) {
            this.setRowIsSelect(row)
        },
        // 复选框点击事件
        setRowIsSelect (row) {
            if (row.isCheck) {
                this.Array.push(row)
                this.Array.forEach(item => {
                    if (!item.children) return
                    item.children.forEach(val => this.Array.push(val))
                })
            } else {
                const index = this.Array.findIndex(x => x.orgId === row.orgId)
                if (index !== -1) {
                    this.Array.splice(index, 1)
                }
            }
        },
        // 检测表格数据是否全选
        checkIsAllSelect () {
            this.oneProductIsSelect = []
            this.orgDataTable.forEach(item => {
                this.oneProductIsSelect.push(item.isCheck)
            })
            //判断一级产品是否是全选.如果一级产品全为true，则设置为取消全选，否则全选
            return this.oneProductIsSelect.every(
                selectStatusItem => {
                    return true === selectStatusItem
                }
            )
        },
        // 表格全选事件
        selectAllFun (selection) {
            let isAllSelect = this.checkIsAllSelect()
            this.orgDataTable.forEach(item => {
                item.isCheck = !isAllSelect
                this.selectFun(selection, item)
            })
        },

        ...mapMutations(['setSelectedInfo']),
        cellClsNm ({ column }) {
            if (column.label === '') return 'fixed-left'
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            this.formData.bannerImg = null

            for (let key in this.formData) {
                if (key === 'files') return this.formData[key]
                //
                if(this.programaKey == 'LcPriceAnnouncement' ) {
                    if (key === 'home' || key === 'top') {
                        continue
                    }
                }
                this.formData[key] = null
            }
        },
        // 保存
        onSave () {
            this.$refs.editForm.validate(valid => {
                console.log(this.formData)
                if (this.formData.home == 2) {
                    this.formData.bannerImg = ''
                }
                if (this.formData.home == 1 && this.formData.bannerImg === '' || this.formData.bannerImg == null) {
                    this.$message.error('请上传首页图片')
                    return
                }
                if (valid) {
                    if (this.action === '编辑') {
                        this.handleEdit()
                    } else {
                        this.handleCreate()
                    }
                }
            })
        },
        // 修改信息
        handleEdit () {
            edit(this.formData).then(res => {
                if (res.message === '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 新增信息
        handleCreate () {
            create({ ...this.formData, programaKey: this.$route.query.programaKey }).then(res => {
                if (res.message !== '操作成功') return
                this.clientPop('suc', '新增成功', () => {
                    this.emptyForm()
                    this.getTableData()
                    this.viewList = true
                })
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>
<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .el-form-item.upload-item {
    display: block;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;

    .el-col.el-col-24 {
        height: unset;
    }

    .el-col.el-col-20 {
        height: 50px;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload
.avatar-uploader {
    /deep/ .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/ .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/ .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

/deep/ .el-dialog {
    height: 500px !important;
    padding: 0;

    .el-dialog__header {
        margin-bottom: 20px;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        background: red url(../../../../assets/test.png) no-repeat;

        .el-dialog__title {
            color: #fff;
        }
    }

    .el-dialog__body {
        height: 280px;
        margin-top: 100px;
    }

    .el-dialog__close.el-icon.el-icon-close::before {
        width: 44px;
        height: 44px;
    }
}

/deep/ .el-form-item.el-form-item--small {
    //height: 500px;
}

/deep/ .multi-file-uploader {
    height: unset;
    min-height: 30px;
    padding-top: 10px;

    .el-upload__tip {
        height: 16px;
        margin-top: 0;
        line-height: 16px;
    }
}

/deep/ .el-upload-list__item {
    display: flex;
    align-items: center;
    height: 25px;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
