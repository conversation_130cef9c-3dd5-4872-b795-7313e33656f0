<template>
    <div class="base-page">

        <div class="right">
            <!-- ---------------------新增编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="发布状态：">
                                {{ formData.state == 1 ? '发布' : '未发布' }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="发布时间：">{{ formData.gmtRelease }}</el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="内容管理：">
                                <editor v-model="formData.content" :height="400" :max-length="0"></editor>
                            </el-form-item>
                        </el-col>
                    </el-row>
                  <el-row v-if="!showPcwpFile" style="margin-top: 20px;">
                      <el-col :span="20">
                          <el-form-item class="upload-item" label="附件资料：" prop="">
                              <el-upload
                                  action="fakeaction"
                                  multiple
                                  :limit="20"
                                  :show-file-list="true"
                                  :file-list="this.formData.files"
                                  :before-upload="beforeOneOfFilesUpload"
                                  :http-request="uploadOneOfFiles"
                                  :on-remove="handleRemove"
                                  accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                              >
                                  <el-button size="small" type="primary">点击上传</el-button>
                                  <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                              </el-upload>
                          </el-form-item>
                      </el-col>
                  </el-row>
                    <el-row  v-if="showPcwpFile" style="margin-top: 20px;">
                        <el-col :span="20">
                            <el-form-item class="upload-item" label="入库必填附件资料：" prop="">
                                <el-upload
                                    action="fakeaction"
                                    multiple
                                    :limit="20"
                                    :show-file-list="true"
                                    :file-list="this.pcwpFile"
                                    :before-upload="beforeOneOfFilesUpload"
                                    :http-request="pcwpUploadOneOfFiles"
                                    :on-remove="handleRemovepcwp"
                                    accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                >
                                    <el-button size="small" type="primary">点击上传</el-button>
                                    <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="showPcwpFile" style="margin-top: 20px;">
                        <el-col :span="20">
                            <el-form-item class="upload-item" label="未入库必填附件资料：" prop="">
                                <el-upload
                                    action="fakeaction"
                                    multiple
                                    :limit="20"
                                    :show-file-list="true"
                                    :file-list="this.unPcwpFile"
                                    :before-upload="beforeOneOfFilesUpload"
                                    :http-request="unPCWPuploadOneOfFiles"
                                    :on-remove="handleRemoveunpcwp"
                                    accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                >
                                    <el-button size="small" type="primary">点击上传</el-button>
                                    <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>

            <div class="footer">
                <div class="right-btn">
                    <el-button class="btn-delete" native-type="button" type="primary" v-if="formData.state == 2"
                        @click="handlePublish()">发布</el-button>
                    <el-button native-type="button" type="primary" v-else @click="handleNotPublish()">取消发布</el-button>
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
// eslint-disable-next-line
import { mc, getList, update, getInfo } from '@/api/platform/content/aboutUs'
import { batchPublish, batchNotPublish } from '../../../../api/platform/content/richContent'
import editor from '../../../../components/quillEditor'
import { uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
// import { hideLoading, showLoading } from '../../../../utils/common'
export default {
    components: {
        editor
    },
    data () {
        return {
            //是否展示pcwp入库上传资料
            showPcwpFile: false,
            alertName: '友情链接',
            picTypeFilter: [
                { value: 0, label: '选项1' }
            ],
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入链接名', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            // 新增编辑 表单数据
            formData: {
                name: '',
                address: '',
                tel: '',
                remarks: '',
                state: 1,
                content: '',
                gmtModified: '',
                files: []
            },
            allList: [],
            uploadlist: [],
            unuploadList: [],
            pcwpFile: [],
            unPcwpFile: [],
            noPcwpFile: [],
            arr: [],
            requestParams: {},
            programaKey: '',
            storageState: false,
        }
    },
    watch: {
        $route: {
            handler (val) {
                this.programaKey = val.query.programaKey
                this.getData()
            }
        }
    },
    methods: {

        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if (size > 100) {
                this.$message.error('文件大小不能超过100M')
                return false
            }
            return true
        },
        // 多文件上传
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            let uploadRes = await uploadFile(form)
            if (uploadRes.code != null && uploadRes.code != 200) {
                this.formData.files.push(file)
                this.formData.files.pop()
            } else {
                this.$message.success('上传成功')
                this.formData.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 5,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
            }

        },
        handleExceed () {
            this.$message.error('文件个数不能超出20个')
            return false
        },
        // 在pcwp用户上传资料
        async pcwpUploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            let uploadRes = await uploadFile(form)
            if (uploadRes.code != null && uploadRes.code != 200) {
                this.uploadlist.push(file)
                this.uploadlist.pop()
            } else {
                this.$message.success('上传成功')
                this.uploadlist.push({
                    programaKeyTwo: 'isPcwpSupplier',
                    name: uploadRes[0].objectName,
                    relevanceType: 5,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
            }

        },
        // 不在pcwp用户上传资料
        async unPCWPuploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            let uploadRes = await uploadFile(form)
            if (uploadRes.code != null && uploadRes.code != 200) {
                this.unuploadList.push(file)
                this.unuploadList.pop()
            } else {
                this.$message.success('上传成功')
                this.unuploadList.push({
                    programaKeyTwo: 'unPcwpSupplier',
                    name: uploadRes[0].objectName,
                    relevanceType: 5,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
            }

        },
        handleRemove (file) {
            let files = this.formData.files
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.formData.files = newFiles
                }
            })
        },
        handleRemovepcwp (file) {
            let files = this.formData.files
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.formData.files = newFiles
                }
            })
        },
        handleRemoveunpcwp (file) {
            let files = this.formData.files
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.formData.files = newFiles
                }
            })
        },
        // 保存/取消保存
        onSave () {
            this.clientPop('info', '您确定要保存吗', () => {
                if (this.showPcwpFile) {
                    this.allList = this.formData.files
                    this.formData.files = [...this.formData.files, ...this.unuploadList, ...this.uploadlist]
                    update({ ...this.formData, programaKey: this.$route.query.programaKey }).then(res => {
                        console.log(res)
                        if (res.code === 200) {
                            this.storageState = false
                            this.getData()
                            return this.clientPop('suc', '保存成功',)
                        }
                    })
                }else {
                    update({ ...this.formData, programaKey: this.$route.query.programaKey }).then(res => {
                        console.log(res)
                        if (res.code === 200) {
                            this.storageState = false

                            this.getData()
                            return this.clientPop('suc', '保存成功',)
                        }
                    })
                }

            })
        },
        // 发布/取消发布
        handleNotPublish () {
            this.clientPop('info', '您确定要取消发布吗？', () => {
                // 发布代码
                batchNotPublish([this.formData.contentId]).then(res => {
                    console.log(res)
                    if (res.code === 200) {
                        this.getData()
                        return this.clientPop('suc', '取消发布成功')
                    }
                })
            })
            this.storageState = false
        },
        publishData () {
            batchPublish([this.formData.contentId]).then(res => {
                if (res.code == 200) {
                    this.getData()
                    this.storageState = false
                    return this.clientPop('suc', '发布成功')
                }
            })
        },
        // 发布
        handlePublish () {
            if (!this.storageState) {
                return this.clientPop('info', '您还没有保存数据，确定继续发布吗？', () => {
                    this.publishData()
                })
            }
        },
        getData () {
            this.programaKey = this.$route.query.programaKey

            if ( this.programaKey == 'companyRegistration' || this.programaKey == 'individualRegistration') {
                this.showPcwpFile = true
            }else {
                this.showPcwpFile = false
            }
            getInfo({ programaKey: this.programaKey }).then(res => {
                this.formData = res
                if (this.showPcwpFile) {
                    this.formData.files = res.files
                    this.pcwpFile = res.files
                    this.unPcwpFile = res.files

                    this.formData.files = this.formData.files.filter(item => (item.programaKeyTwo == null))
                    this.pcwpFile =  this.pcwpFile.filter(item => (item.programaKeyTwo == 'isPcwpSupplier' ))
                    this.unPcwpFile = this.unPcwpFile.filter(item =>(item.programaKeyTwo == 'unPcwpSupplier'))

                }
            })
        },
    },
    created () {

        this.getData()

    },
    mounted () { },
}
</script>
<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-form {}

.right-btn {
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.avatar-uploader {
    /deep/.el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>