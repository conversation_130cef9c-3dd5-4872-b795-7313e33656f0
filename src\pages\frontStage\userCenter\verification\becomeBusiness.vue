<template>
    <main v-loading="formLoading">
        <div class="list-title df mb20">成为个体户</div>
        <div class="content" v-if="!(succeedPage || failPage)">
            <el-form class="businessForm" :model="businessForm" ref="businessForm" :rules="businessFormRules" label-width="168px" :inline="false">
                <div class="df">
                    <el-form-item class="licenseUploader" label="营业执照图片：" prop="businessLicense">
                        <el-upload class="avatar-uploader" action="fakeaction" :before-upload="handleBeforeUpload" name="img" :auto-upload="true"
                            :show-file-list="false" :on-change="handleUploadChange" :http-request="uploadLicenseBusiness">
                            <img v-if="businessForm.businessLicense" :src="businessForm.businessLicense" class="avatar">
                            <div v-else class="licenseUploader">
                                <img src="@/assets/images/userCenter/upload_yyzz.png" />
                            </div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item class="licenseUploader">
                        <div class="uploadDemo dfa">
                            <img src="@/assets/images/userCenter/yyzz_demo.png" alt="">
                            <div><span>示例图</span><i class="el-icon-zoom-in"></i></div>
                        </div>
                    </el-form-item>
                    <div class="uploadTip">请上传10MB以内的PNG，JPG，GIF，BMP格式图片</div>
                </div>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="名称：" prop="enterpriseName">
                            <el-input clearable v-model="businessForm.enterpriseName" placeholder="请填写50字以内的企业名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="1">
                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                            <el-input clearable v-model="businessForm.socialCreditCode"  v-on:blur="selectIsPcwpUser(businessForm.socialCreditCode,'individualRegistration')" placeholder="请输入统一社会信用代码"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11">
                        <el-form-item label="经营者" prop="operator">
                            <el-input clearable v-model="businessForm.operator" placeholder="请填写经营者姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="1">
                        <el-form-item class="licenseValidTime" prop="creationTime" label="注册日期：">
                            <el-date-picker v-model="businessForm.creationTime" value-format="yyyy-MM-dd HH:mm:ss" align="right" type="date"
                                placeholder="请选择注册日期" :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="税率%：" prop="taxRate">
                            <el-input v-model="businessForm.taxRate" style="width: 110px;" :step="0.01" type="number" placeholder="填写税率" @change="writeTaxRate"/>%
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="经营场所：" prop="placeOfBusiness">
                            <el-input clearable v-model="businessForm.placeOfBusiness" placeholder="请填写与营业执照一致的经营场所"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="经营范围：" prop="mainBusiness">
                            <el-input clearable v-model="businessForm.mainBusiness" placeholder="请填写与营业执照一致的经营范围"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="separ center"></div>
                <div class="subtitle">设置管理员</div>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="身份证人像面照：" prop="cardPortraitFace">
                            <el-upload class="identityUpload face" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1,2)"
                                       :show-file-list="false">
                                <img class="identityUpload" v-if="businessForm.cardPortraitFace" :src="businessForm.cardPortraitFace"
                                     alt="">
                                <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="身份证国徽面照：" prop="cardPortraitNationalEmblem">
                            <el-upload class="identityUpload badge" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2,2)"
                                       :show-file-list="false">
                                <img class="identityUpload" v-if="businessForm.cardPortraitNationalEmblem" :src="businessForm.cardPortraitNationalEmblem"
                                     alt="">
                                <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="手机号码：" prop="adminPhone">
                            <el-input clearable v-model="businessForm.adminPhone" disabled placeholder="请输入手机号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="姓名：" prop="adminName">
                            <el-input clearable v-model="businessForm.adminName"  placeholder="请输入姓名"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="身份证号码：" prop="adminNumber">
                            <el-input clearable v-model="businessForm.adminNumber"  placeholder="请输入身份证号码"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--                            附件资料-->
                <el-row v-show="registerPcwpFiles.isPcwoSupplier=='unPcwpSupplier'">
                    <el-col :span="24" style="height: unset;" v-loading="fileLoading2">
                        <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                            <el-upload
                                ref="multi-upload"
                                class="multi-file-uploader"
                                action="fakeaction"
                                accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                :on-remove="handleRemoveIsPcwp"
                                multiple
                                :limit="10"
                                :before-upload="beforeOneOfFilesUpload"
                                :http-request="uploadOneOfFilesD"
                                :on-exceed="handleExceed"
                                :file-list="businessFormFileList"
                            >
                                <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip"><span>请上传以下资料</span>
                                    <div class="file dfa pointer" v-for="file in registerPcwpFiles.files" :key="file.url">
                                        <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                    </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-show="registerPcwpFiles.isPcwoSupplier=='isPcwpSupplier'"></el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="agreeTerm">
                            <el-checkbox v-model="agreeTerm" :indeterminate="false">
                                您确认阅读并接受<span @click="openAgreemen">《物资采购平台企业认证协议》</span>
                            </el-checkbox>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="btns center dfb">
                <button @click="$router.go(-1)">返回</button>
                <button @click="onSubmit">提交</button>
            </div>
            <!-- 平台协议 -->
            <el-dialog title="物资采购平台企业认证协议" :visible.sync="showTerm">
                <span v-html="content"></span>
                <span slot="footer">
                        <el-button @click="showTerm = false">Cancel</el-button>
                        <el-button type="primary" @click="showTerm = false">OK</el-button>
                    </span>
            </el-dialog>
        </div>
        <div class="tabBox" v-if="succeedPage">
            <enterpriseReg :title="'个体户'"></enterpriseReg>
        </div>
        <div class="tabBox" v-if="failPage">
            <failReg></failReg>
        </div>
    </main>
</template>
<script>
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { getEnterpriseAuthInfo, becomeEnterprise } from '@/api/frontStage/verification'
import enterpriseReg from './enterpriseReg.vue'
import failReg from './fail.vue'
import { findByProgramaKey } from '@/api/w/richContent'
import { createWFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { selectIsPcwpUserByCode } from '@/api/frontStage/register'
export default {
    components: { enterpriseReg, failReg },
    data () {
        return {
            registerPcwpFiles: {
                isPcwoSupplier: '',
                files: []
            },
            //个体户pcwp入库附件资料
            businessFileList: [],
            fileLoading2: false,
            fileLoading: false,
            businessFormFileList: [],
            fileList: [],
            formLoading: false,
            content: '',
            succeedPage: false,
            failPage: false,
            uploadImgSize: 4,
            dateFormat: 'yyyy-MM-dd HH:mm:ss',
            showTerm: false,
            longTerm: false,
            businessForm: {},
            agreeTerm: false,
            //个体户表单验证
            businessFormRules: {
                businessLicense: { required: true, message: '请上传营业执照！', trigger: 'blur' },
                enterpriseName: [
                    { required: true, message: '请填写企业名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '请填写50字以内的企业名称', trigger: 'blur' }
                ],
                socialCreditCode: [
                    { required: true, message: '请输入统一信用代码', trigger: 'blur' },
                    { min: 18, max: 18, message: '请输入18位统一信用代码', trigger: 'blur' }
                ],
                operator: [
                    { required: true, message: '请输入经营者姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                creationTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                placeOfBusiness: [
                    { required: true, message: '请填写与营业执照一致的经营场所', trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' }
                ],
                mainBusiness: [
                    { required: true, message: '请输入经营范围', trigger: 'blur' },
                    { min: 1, max: 1000, message: '超过限制', trigger: 'blur' }
                ],
                taxRate: [
                    { required: true, message: '请输入税率', trigger: 'blur' },
                    { min: 0, max: 3, message: '超过限制', trigger: 'blur' }
                ],
                cardPortraitFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                cardPortraitNationalEmblem: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                adminName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                adminNumber: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                adminPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
            },
            // 日期选择器选项
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() > Date.now()
                },
                shortcuts: [{
                    text: '今天',
                    onClick (picker) {
                        picker.$emit('pick', new Date())
                    }
                }, {
                    text: '昨天',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24)
                        picker.$emit('pick', date)
                    }
                }, {
                    text: '一周前',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
                        picker.$emit('pick', date)
                    }
                }]
            },
        }
    },
    created () {
        this.getEnterpriseAuthInfoM()
        this.getRegisterAgreeUser()
    },
    mounted () { },
    methods: {
        writeTaxRate () {
            if (this.businessForm.taxRate != null) {
                if (!(0 <= this.businessForm.taxRate && this.businessForm.taxRate <= 100)) {
                    this.$message.error('税率不能小于0或大于100')
                    this.businessForm.taxRate = 0
                }
            }else {
                this.$message.error('税率不能为空')
                this.businessForm.taxRate = 0
            }
        },
        handleRemoveIsPcwp (file) {
            this.fileLoading = true
            let files = this.businessFormFileList
            let recordId = null
            let newFiles = files.filter(t =>{
                if(file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                }else {
                    return true
                }
            })
            createWFileRecordDelete({ recordId: recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.businessFormFileList = newFiles
                }
                this.fileLoading = false
            }).catch(() => {
                this.fileLoading = false
            })
        },
        async uploadOneOfFilesD (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading2 = true
            let uploadRes = await uploadFile(form)
            this.fileLoading2 = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.businessFormFileList.push(file)
                this.businessFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                this.businessFormFileList.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    programaKeyTwo: this.registerPcwpFiles.isPcwoSupplier,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,
                })
            }
        },
        selectIsPcwpUser (socialCreditCode, programaKey) {
            if (socialCreditCode.length == 18) {
                selectIsPcwpUserByCode({ socialCreditCode: socialCreditCode, programaKey: programaKey }).then(res=>{
                    this.registerPcwpFiles = res
                })
            }

        },
        openAgreemen () {
            this.showTerm = true
        },
        handleExceed () {
            this.$message.error('文件个数不能超出10个')
            return false
        },
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading2 = true
            let uploadRes = await uploadFile(form)
            this.fileLoading2 = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.businessFormFileList.push(file)
                this.businessFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                this.businessForm.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,
                })
            }
        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if(size > 100) {
                this.$message.error('文件大小不能超过100M')
                return false
            }
            return true
        },
        handleRemove (file) {
            this.fileLoading = true
            let files = this.enterpriseForm.files
            let recordId = null
            let newFiles = files.filter(t =>{
                if(file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                }else {
                    return true
                }
            })
            createWFileRecordDelete({ recordId: recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.enterpriseForm.files = newFiles
                }
                this.fileLoading = false
            }).catch(() => {
                this.fileLoading = false
            })
        },
        getRegisterAgreeUser () {
            findByProgramaKey({ programaKey: 'individualRegistration' }).then(res => {
                this.fileList = res.files
                this.content = res.content
            })
        },
        async uploadIdentity (params, num, type) {
            if(num === 1 ) {
                let file = params.file
                const form = new FormData()
                form.append('files', file)
                form.append('bucketName', 'mall-private') //存储桶名称
                form.append('directory', 'material') // 商城类型
                form.append('isChangeObjectName', true) // 是否修改文件名称
                form.append('isTemplate', false)  //是否是模板
                form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
                form.append('relationId', '990116') // 关联ID
                uploadFile(form).then(res => {
                    if(type == 2) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.businessForm.cardPortraitFace = url
                        })
                        this.businessForm.cardPortraitFace = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.businessForm.cardPortraitFaceId = res[0].recordId
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                        let fileSuffix = res[0].objectName.substr(res[0].objectName.lastIndexOf('.'))
                        this.businessForm.files.push({
                            name: '身份证正面.' + fileSuffix,
                            relevanceType: 8,
                            url: res[0].nonIpObjectPath,
                            fileType: 3,
                            fileFarId: res[0].recordId
                        })

                    }
                })
            }
            if(num === 2 ) {
                let file = params.file
                const form = new FormData()
                form.append('files', file)
                form.append('bucketName', 'mall-private') //存储桶名称
                form.append('directory', 'material') // 商城类型
                form.append('isChangeObjectName', true) // 是否修改文件名称
                form.append('isTemplate', false)  //是否是模板
                form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
                form.append('relationId', '990116') // 关联ID
                uploadFile(form).then(res => {
                    if(type === 2 ) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.businessForm.cardPortraitNationalEmblem = url
                        })
                        this.businessForm.cardPortraitNationalEmblem = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.businessForm.cardPortraitNationalEmblemId = res[0].recordId
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                        let fileSuffix = res[0].objectName.substr(res[0].objectName.lastIndexOf('.'))
                        this.businessForm.files.push({
                            name: '身份证反面.' + fileSuffix,
                            relevanceType: 8,
                            url: res[0].nonIpObjectPath,
                            fileType: 3,
                            fileFarId: res[0].recordId,

                        })
                    }
                })
            }
        },
        // 获取企业信息
        getEnterpriseAuthInfoM () {
            this.formLoading = true
            getEnterpriseAuthInfo({}).then(res => {
                this.businessForm = res
                if(this.businessForm.cardPortraitFaceId != null) {
                    previewFile({ recordId: this.businessForm.cardPortraitFaceId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.cardPortraitFace = url
                    })
                }
                if(this.businessForm.cardPortraitNationalEmblemId != null) {
                    previewFile({ recordId: this.businessForm.cardPortraitNationalEmblemId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.cardPortraitNationalEmblem = url
                    })
                }
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        handleUploadChange (file) {
            if (file.status == 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status == 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        // 个体户上传营业执照
        async uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            uploadFile(form).then(res => {
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.businessLicense = url
                })
                this.businessForm.businessLicense = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                this.businessForm.businessLicenseId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                let fileSuffix = res[0].objectName.substr(res[0].objectName.lastIndexOf('.'))
                this.businessForm.files.push({
                    name: '营业执照.' + fileSuffix,
                    relevanceType: 8,
                    url: res[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: res[0].recordId
                })
            })
        },
        //时间验证
        validateDate (rule, value, callback) {
            if (value == null || value == '') {
                return callback(new Error('请选择时间！'))
            }
            callback()
        },
        validateAddress (rule, value, callback) {
            if ( this.enterpriseForm.provinces == null || this.enterpriseForm.provinces == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.enterpriseForm.city == null || this.enterpriseForm.city == '' ) {
                return callback(new Error('请选择市级！'))
            }
            callback()
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.succeedPage = true
                setTimeout(() => {
                    localStorage.removeItem('token')
                    this.$store.commit('setUserInfo', {})
                    window.location.href = '/login'
                }, 1000)
            } else {
                this.failPage = true
            }
        },
        getRegisterAgree () {
            findByProgramaKey({ programaKey: 'userRegistration' }).then(res => {
                this.content = res.content
                this.showTerm = true
            })
        },
        onSubmit () {
            this.$refs['businessForm'].validate(valid => {
                if (valid) {
                    if(this.agreeTerm) {
                        this.businessForm.enterpriseType = 0
                        this.businessForm.mallType = 0
                        this.formLoading = true
                        this.businessForm.files = [...this.businessForm.files, ...this.businessFormFileList]
                        this.clientPop('info', '提交后请加QQ：840469283，便于审查部分未上传的资料并邮寄原件。', async () => {
                            becomeEnterprise(this.businessForm).then(res => {
                                this.message(res)
                                this.formLoading = false
                            })
                        }).catch(() => {
                            this.formLoading = false
                        })

                    }else {
                        this.$message({
                            message: '请查看协议后勾选',
                            type: 'error'
                        })
                    }
                }
            })
        },
    },
}
</script>
<style scoped lang="scss">
$font: 'Source Han Sans CN';
main>div {
    height: 100%;
    border: 1px solid rgba(230, 230, 230, 1);
}

.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.content { padding-bottom: 30px; }

/deep/ .el-form-item {
    margin-bottom: 25px;

    .el-form-item__label {
        height: 100%;
        padding-right: 10px;
        line-height: 50px;
        font-size: 16px;
        color: #333;
    }

    .el-input__inner {
        height: 50px;
        font-size: 16px;
        border-radius: 0;
        border: 1px solid rgba(204, 204, 204, 1);
    }
}

/deep/ .el-checkbox {
    display: flex;
    align-items: center;

    .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(33, 110, 198, 1);
    }

    .el-checkbox__label {
        font-size: 16px;
        color: #333;

        span {
            color: #216EC6;
        }
    }
}

/deep/.avatar-uploader .el-upload {
    width: 138px;
    height: 138px;
    border: 1px dashed rgba(217, 217, 217, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

/deep/ .businessForm {
    width: 1066px;
    margin-top: 30px;

    .el-col:not(.el-col-24) {
        width: 48.4%;

        .el-input,
        .el-input__inner {
            width: 350px;
        }

        &.el-col-offset-1 {
            margin-left: 20px;
        }
    }

    .el-col-24 .el-input__inner,
    .el-input {
        width: 893px;
    }

    .el-input.is-disabled {
        background-color: #e6e6e6;
        color: #999;
    }

    .el-form-item__error {
        width: 80%;
        margin-top: -10px;
    }

    .licenseUploader {
        font-size: 40px;
        color: #8c939d;
        width: 138px;
        height: 138px;
        //margin-right: 20px;
        line-height: 140px;
        display: inline;

        .el-form-item__error {
            width: 500px;
        }
    }

    .avatar {
        width: 140px;
        height: 140px;
        display: block;
    }

    .uploadDemo {
        width: 138px;
        height: 138px;
        padding: 5px;
        margin-left: 40px;
        font-size: 16px;
        color: #999;
        border: 1px dashed rgba(217, 217, 217, 1);
        flex-direction: column;

        img {
            width: 130px;
            height: 95px;
        }

        span {
            margin-right: 5px;
        }
    }

    .uploadTip {
        font-size: 14px;
        margin-top: 155px;
        margin-bottom: 20px;
        margin-left: -108px;
        width: 380px;
        color: #808080;
    }

    .el-select {
        width: 100%;
    }

    .licenseValidTime {
        .el-form-item__content {
            display: flex;
        }

        .el-date-editor.el-input {
            width: 350px;

            // margin-right: 32px;
            .el-input__inner {
                width: 350px;
            }
        }

        .el-checkbox {
            height: 50px;
            margin-bottom: 0;
        }

        .el-checkbox__inner {
            border: 1px solid rgba(217, 217, 217, 1);
        }
    }

    .registerAddress {
        .el-select {
            width: 100px;
            margin-right: 10px;

            &:last-child {
                margin-right: 0;
            }

            .el-input,
            .el-input__inner {
                width: 100px;
            }
        }

        .el-form-item__error {
            margin-left: 0px;
            //margin-top: -50px;
        }
    }

    .separ {
        width: 1066px;
        height: 1px;
        margin-left: 20px;
        margin-bottom: 30px;
        border-top: 1px dashed rgba(204, 204, 204, 1);
    }

    .subtitle {
        margin-left: 60px;
        margin-bottom: 30px;
        font-size: 20px;
        font-weight: 500;
        color: #216EC6;
    }

    .identityUpload {
        width: 160px;
        height: 100px;
    }

    .verifyBox .el-button {
        width: 140px;
        height: 50px;
        padding: 0;
        font-size: 16px;
        line-height: 50px;
        color: #216EC6;
        background: #FFFFFF;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 0;
    }

    .verifyBox {

        .el-input,
        .el-input__inner {
            width: 195px !important;
            margin-right: 15px;
        }
    }

    .el-checkbox {
        margin-top: -3px;
        margin-bottom: 50px;

        .is-checked .el-checkbox__inner {
            background-color: #216ec6;
        }
    }
}

.btns {
    width: 350px;
    margin-top: -25px;

    button {
        width: 160px;
        height: 50px;
        font-size: 22px;
    }

    button:first-child {
        color: rgba(33, 110, 198, 1);
        ;
        border: 1px solid rgba(33, 110, 198, 1);
        background-color: #fff;
    }

    button:last-child {
        color: #fff;
        background-color: rgba(33, 110, 198, 1);
    }
}
.tabBox {
    height: 100%;
    min-height: 622px;
    font-family: $font;
    position: relative;
}
</style>