!<template>
      <div class="billTop">
        <div class="billTop-title">
            <span class="title">{{ topTitle }}</span>
            <i class="el-icon-circle-close" title="关闭页面" @click="cancel"></i>
        </div>
    </div>
</template>

<script>
export default {
    props: ['title'],
    data () {
        return {
            topTitle: ''
        }
    },
    mounted () {
        if(this.title) {
            this.topTitle = this.title
        }else{
            this.topTitle = this.$route.meta.title
        }
    },
    methods: {
        cancel () {
            this.$emit('cancel')
        },
    },
}
</script>

<style>

</style>