<template>
    <div class="base-page" v-if="showSetOrg">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">发布</el-button>
                            <el-button type="primary" @click="changePublishState(2)" class="btn-greenYellow">取消发布</el-button>
                            <el-button type="primary" @click="handleDelete" class="btn-greenYellow">批量删除</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-input type="text" placeholder="输入搜索关键字" v-model="searchKey"><img src="@/assets/search.png" slot="suffix"
                                @click="onSearch" /></el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="handleFilter">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @current-change="handleCurrentChange" @selection-change="handleSeletionChange">
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="120">
                        <template slot-scope="scope">
                            <span class="action" @click="onDel(scope)"><img src="../../../assets/btn/delete.png" alt=""></span>
                        </template>
                    </el-table-column>
                    <!-- 图片 -->
                    <el-table-column label="图片" width="120" type="index">
                        <template slot-scope="scope">
                            <el-image style="width: 90px; height: 60px" :src="scope.row.pictureUrl"></el-image>
                        </template>
                    </el-table-column>
                    <!-- 图片链接类型 -->
                    <el-table-column label="链接类型" width="">
                        <template slot-scope="scope">
                            <span v-if="scope.row.pictureType == '0'">无</span>
                            <span v-else-if="scope.row.pictureType == '1'">内部链接</span>
                            <span v-else-if="scope.row.pictureType == '2'">外部链接</span>
                        </template>
                    </el-table-column>
                    <!-- 图片显示位置 -->
                    <el-table-column label="显示位置" width="">
                        <template slot-scope="scope">
                            <span v-if="scope.row.useType == '0'">homepage</span>
                            <span v-else-if="scope.row.useType == '1'">homepage</span>
                            <span v-else-if="scope.row.useType == '2'">homepage</span>
                        </template>
                    </el-table-column>
                    <!-- 图片发布状态 -->
                    <el-table-column label="发布状态" width="">
                        <template slot-scope="scope">
                            {{ scope.row.state == 1 ? '已发布' : '未发布' }}
                        </template>
                    </el-table-column>
                    <!-- 图片链接地址 -->
                    <el-table-column label="链接地址" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.pictureLinkAddress }}
                            </span>
                        </template>
                    </el-table-column>
                    <!-- 图片排序值 -->
                    <el-table-column label="排序值" width="" prop="orderValue" />

                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange" />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新增编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">{{ dialogTitle }}</div>
                <el-form :rules="formRules" ref="formPic" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item required class="uploader" label="图片地址：" prop="pictureUrl">
                                <el-upload class="avatar-uploader" action="http://api_devss.wanxikeji.cn/api/savePic" :show-file-list="false"
                                    :on-success="handleUploadSuccess" name="img">
                                    <img v-if="imageUrl" :src="imageUrl" class="avatar">
                                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="图片链接类型：" prop="pictureType">
                                <el-select v-model="formData.pictureType" placeholder="图片链接类型">
                                    <el-option v-for="item in picTypeFilter" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item width="150px" label="图片显示位置：" prop="useType">
                                <el-select v-model="formData.useType" placeholder="图片显示位置">
                                    <el-option v-for="item in useTypeFiter" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="图片链接地址：" prop="pictureLinkAddress">
                                <el-input v-model="formData.pictureLinkAddress" :disabled="formData.pictureType == 1" placeholder="填写图片链接地址">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input type="textarea" v-model="formData.remark" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="30%" :close-on-click-modal="true" :before-close="closeDialog">
            <el-form :model="queryForm" ref="form" label-width="120px" :inline="false" size="normal">

                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="发布状态：">
                            <el-select v-model="filterData.state" clearable placeholder="图片发布状态">
                                <el-option v-for="item in stateFilter" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="显示位置：">
                            <el-select v-model="filterData.useType" clearable placeholder="图片显示位置">
                                <el-option v-for="item in useTypeFiter" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="链接类型：">
                            <el-select v-model="filterData.pictureType" clearable placeholder="图片链接类型">
                                <el-option v-for="item in picTypeFilter" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>

            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
        </el-dialog>

    </div>
</template>

<script>
import axios from 'axios'
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { getList, edit, createPic, del, batchPublish, batchDelete } from '@/api/platform/content/adImg'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapState, mapActions, mapMutations } from 'vuex'
// import { getOrgName } from '@/api/materials/materialsMarketPrice'
export default {
    components: {
        ComPagination
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) {
                return this.screenHeight - 244
            }
            return this.screenHeight - 291
        },
        picUrlActive () {
            return this.formData.pictureType != 1
        }
    },
    data () {
        return {
            queryVisible: false,
            dialogTitle: '新增图片',
            classType: '0',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            showSetOrg: true, //设置使用机构
            searchKey: '',
            currentClass: null,
            currentRow: null,
            selectedRows: [],
            queryForm: {
                one: ''
            },
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            picTypeFilter: [
                //{ value: 0, label: '' },
                { value: 1, label: '无链接' },
                { value: 2, label: '内部链接' },
                { value: 3, label: '外部链接' },
            ],
            mallFilter: [
                { value: 2, label: '全部' },
                { value: 2, label: '全部' },
                { value: 1, label: '慧采商城' },
                { value: 0, label: '装备商城' },
            ],
            stateFilter: [
                { value: 0, label: '全部' },
                { value: 1, label: '发布' },
                { value: 2, label: '未发布' },
            ],
            useTypeFiter: [
                // { value: 0, label: '全部' },
                { value: 1, label: 'two' },
                { value: 2, label: 'three' },
            ],
            filterData: {
                pictureType: '',
                state: '',
                useType: '',
                limit: 10,
                page: 1,
                mallType: ''
            },
            // 表单校验规则
            formRules: {
                // pictureUrl: [{ required: true, validator: this.validatePic, }], // 图片验证样式有问题
                pictureUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
                useType: [
                    { required: true, message: '请选择图片显示位置', trigger: 'blur' },
                ],
                mallType: [{ required: true, message: '请选择商城类型', trigger: 'blur' }],
                pictureType: [{ required: true, message: '请选择图片链接类型', trigger: 'blur' }],
                pictureLinkAddress: [{ validator: this.validateUrl, type: 'url', trigger: 'blur' }]
            },
            mallType: [
                { value: 2, label: '全部' },
                { value: 0, label: '物资' },
                { value: 1, label: '装备' }
            ],
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            classData: {},
            tableData: [
                { useType: 1, state: 0, pictureType: 1, pictureUrl: 'address' },
                { useType: 1, state: 0, pictureType: 1, pictureUrl: 'address' },
            ],
            orgDataTable: [],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            allData: [],
            mapObj: null,
            formData: {
                'orderValue': 0, // 图片排序用
                'pictureId': '',
                'pictureLinkAddress': '', // 图片点击跳转的地址
                'pictureType': '1',
                'pictureUrl': '', // 服务器图片地址
                'remark': '测试', // 备注
                'state': 1, // 发布状态 1使用中 2未使用
                'useType': 0, // 图片展示位置
                'mallType': 0 // 商城类型
            },
            imageUrl: '',
            currencyOptions: [],
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            Array: [],
            pagination: {
                limit: 10,
                page: 1,
                total: 20,
                key: '',
            }
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        let data = JSON.parse(localStorage.getItem('vuex'))
        this.orgId = data.userInfo.orgInfo.orgId
        this.getTableData()
    },
    methods: {
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                pictureType: '',
                state: '',
                useType: '',
                limit: 10,
                page: 1,
                mallType: ''
            },
            done()
        },
        hideDialog () {
            this.filterData = {
                pictureType: '',
                state: '',
                useType: '',
                limit: 10,
                page: 1,
                mallType: ''
            },
            this.queryVisible = false
        },
        // 校验函数
        validateUrl (rule, value, callback) {
            if(this.formData.pictureType === 1 || this.formData.pictureLinkAddress != '') {
                callback()
            }else{
                callback(new Error('请输入正确的地址格式!'))
            }
        },
        // 高级查询
        advancedQuery () {
            console.log(this.filterData)
            let { pictureType, mallType, state, useType } = this.filterData
            let { pageSize, currPage } = this.pages
            getList({ pictureType, state, useType, limit: pageSize, page: currPage, orderBy: 0, mallType }).then(res => {
                this.pages = res
                this.tableData = res.list
                this.viewList = true
            })
        },
        // 发布/取消
        changePublishState (num) {
            this.selectedRows.forEach(item => {
                item.state = num
            })
            batchPublish(this.selectedRows).then(res => {
                console.log(res)
                this.getTableData()
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该图片吗？', async () => {
                showLoading()
                try {
                    del({ id: scope.row.pictureId })
                    hideLoading()
                    this.clientPop('suc', '删除成功', () => {
                        this.getTableData()
                    })
                } catch (err) {
                    hideLoading()
                }
            })
        },
        // 批量删除
        handleDelete () {
            this.clientPop('info', '您确定要删除选中图片吗？', async () => {
                showLoading()
                try{
                    let arr = this.selectedRows.map(item => {
                        return item.pictureId
                    })
                    batchDelete(arr).then(res => {
                        console.log(res)
                    })
                    hideLoading()
                    this.clientPop('suc', '删除成功', () => {
                        this.getTableData()
                    })
                } catch (err) {
                    hideLoading()
                }
            })
        },
        // 表格勾选
        handleSeletionChange (seletion) {
            this.selectedRows = seletion
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        // 上传图片
        handleUploadSuccess (res, file) {
            this.imageUrl = URL.createObjectURL(file.raw)
            this.formData.pictureUrl = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        handlePicRemove () { },
        handleClose () { },
        handleFilter () {
            this.queryVisible = true
        },
        Save () {
            this.showSetOrg = true
        },
        Close () {
            this.showSetOrg = true
        },
        // 提交图片
        handleSubmit () {
            axios({
                method: ''
            })
        },
        handleView (scope) {
            this.viewList = 'class'
            this.dialogTitle = '查看图片'
            this.formData = scope.row
            this.imgUrl = scope.row.pictureUrl
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.dialogTitle = '新增图片'
        },
        // 复选框选中事件
        selectFun (selection, row) {
            console.log('selection>>>', selection)
            this.setRowIsSelect(row)

        },
        // 复选框点击事件
        setRowIsSelect (row) {
            if (row.isCheck) {
                this.Array.push(row)
                this.Array.forEach(item => {
                    if (item.children) {
                        item.children.forEach(val => {
                            this.Array.push(val)
                        })
                    }
                })
            } else {
                const index = this.Array.findIndex(x => x.orgId === row.orgId)
                if (index !== -1) {
                    this.Array.splice(index, 1)
                }
            }
        },
        // 检测表格数据是否全选
        checkIsAllSelect () {
            this.oneProductIsSelect = []
            this.orgDataTable.forEach(item => {
                this.oneProductIsSelect.push(item.isCheck)
            })
            //判断一级产品是否是全选.如果一级产品全为true，则设置为取消全选，否则全选
            let isAllSelect = this.oneProductIsSelect.every(
                selectStatusItem => {
                    return true == selectStatusItem
                }
            )
            return isAllSelect
        },
        // 表格全选事件
        selectAllFun (selection) {
            let isAllSelect = this.checkIsAllSelect()
            this.orgDataTable.forEach(item => {
                item.isCheck = !isAllSelect
                this.selectFun(selection, item)
            })
        },

        ...mapMutations(['setSelectedInfo']),

        async getTableData () {
            let { pageSize, currPage } = this.pages
            getList({ pictureType: 0, state: 0, useType: 0, limit: pageSize, page: currPage, orderBy: 0, mallType: 3 }).then(res => {
                console.log(res)
                this.pages = res
                this.tableData = res.list
                // this.$message({
                //     message: '获取图片列表失败，请重试！',
                //     type: 'error',
                // })
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        onSearch () { },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存
        onSave () {
            this.$refs.formPic.validate(valid => {
                if (valid) {
                    if (this.dialogTitle === '编辑图片') {
                        this.handleEditPic()
                    } else {
                        this.handleCreatePic()
                    }
                }
            })
        },
        // 修改图片
        handleEditPic () {
            edit(this.formData).then(res => {
                console.log(res.data)
                this.viewList = true
                this.getTableData()
            })
        },
        // 新增图片
        handleCreatePic () {
            createPic(this.formData).then(res => {
                console.log(res.data)
                this.viewList = true
                this.getTableData()
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/.el-form-item.uploader {
    height: 200px;
}

/deep/ .el-table__header-wrapper {
    .el-checkbox {
        display: none;
    }
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload
.avatar-uploader {
    /deep/.el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

/deep/ .el-dialog {
    height: 500px !important;
    padding: 0;

    .el-dialog__header {
        margin-bottom: 20px;
        padding: 10px;
        text-align: center;
        background-color: red;
        font-weight: bold;
        background: url(../../../assets/test.png) no-repeat;

        .el-dialog__title {
            color: #fff;

        }
    }

    .el-dialog__body {
        height: 280px;
        margin-top: 100px;
    }

    .el-dialog__close.el-icon.el-icon-close::before {
        width: 44px;
        height: 44px;
    }
}
</style>
