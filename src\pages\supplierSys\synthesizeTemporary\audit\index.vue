<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按确认时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="3">按提交时间排序</el-radio>
                        <el-input clearable style="width: 300px" type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table">
                <el-table
                    class="table" :height="rightTableHeight" v-loading="tableLoading" :data="tableData" border
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="编号" width="240" prop="synthesizeTemporarySn">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.synthesizeTemporarySn }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="收货单位名称" width="400" prop="orgName"/>
                    <el-table-column label="供货单位名称" width="400" prop="supplierOrgName"/>
                    <el-table-column label="项目收货地址" width="" prop="receiverAddress"/>
                    <el-table-column label="含税总金额（元）" width="130" prop="synthesizeSumAmount"/>
                    <el-table-column label="清单类型" width="100" prop="清单类型">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.billType == 1">浮动价格</el-tag>
                            <el-tag v-if="scope.row.billType == 2">固定价格</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="state" label="状态" width="150">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state == 2">待审核</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 3">审核通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 4">审核未通过</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 6">已推送大宗临购计划</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orgIsDelete" label="采购单位是否删除" width="">
                        <template v-slot="scope">
                            <el-tag type="success" v-if="scope.row.orgIsDelete == 0">否</el-tag>
                            <el-tag type="danger" v-if="scope.row.orgIsDelete == 1">是</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="paymentWeek" label="货款支付周期（单位月）" width="">
                        <template v-slot="scope">
                            {{scope.row.paymentWeek}}月
                        </template>
                    </el-table-column>
                    <el-table-column prop="paymentWeek" label="超期垫资利息（%）" width="">
                        <template v-slot="scope">
                            {{scope.row.outPhaseInterest}}%
                        </template>
                    </el-table-column>
                    <el-table-column label="确认时间" width="160" prop="auditTime"/>
                    <el-table-column label="提交时间" width="160" prop="submitTime"/>
                    <el-table-column label="创建时间" width="160" prop="gmtCreate"/>
                    </el-table>
            </div>
            <!--分页-->
            <Pagination
                v-show="tableData || tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="状态：">
                            <el-select v-model="filterData.selectStateValue" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="清单类型：">
                            <el-select v-model="filterData.selectBillTypeValue" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.billTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="编号：">
                            <el-input clearable maxlength="100" placeholder="请输入订单号" v-model="filterData.synthesizeTemporarySn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="采购单位名称：">
                            <el-input clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.orgName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.createTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="提交时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.submitTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="确认时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.auditTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { platformListByEntityST } from '@/api/platform/order/orders'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            tableLoading: false,
            // 表格数据
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                selectBillTypeValue: null,  // 选中的值
                selectStateValue: null,  // 选中的值
                orgName: null,
                synthesizeTemporarySn: null,
                createTime: [],
                submitTime: [],
                auditTime: [],
                orderBy: 1,
                billTypeOptions: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: 1,
                        label: '浮动价格'
                    }
                    , {
                        value: 2,
                        label: '固定价格'
                    }],
                stateOptions: [
                    { value: null, label: '全部' },
                    { value: 2, label: '待审核' },
                    { value: 3, label: '审核通过' },
                    { value: 4, label: '审核未通过' },
                ],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        // 详情
        handleView (row) {
            this.$router.push({
                path: '/supplierSys/synthesizeTemporary/auditDetail',
                name: 'synthesizeTemporaryAuditDetail',
                query: {
                    sn: row.synthesizeTemporarySn
                }
            })
        },
        resetSearchConditions () {
            this.filterData.synthesizeTemporarySn = null
            this.filterData.orgName = null
            this.filterData.selectBillTypeValue = null
            this.filterData.selectStateValue = null
            this.filterData.createTime = []
            this.filterData.submitTime = []
            this.filterData.auditTime = []
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = null
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                states: [2, 3, 4],
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if(this.filterData.selectBillTypeValue != null) {
                params.billType = this.filterData.selectBillTypeValue
            }
            if (this.filterData.synthesizeTemporarySn != null) {
                params.synthesizeTemporarySn = this.filterData.synthesizeTemporarySn
            }
            if (this.filterData.orgName != null) {
                params.orgName = this.filterData.orgName
            }
            if(this.filterData.selectStateValue != null) {
                params.state = this.filterData.selectStateValue
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.createTime != null) {
                params.startCreateTime = this.filterData.createTime[0]
                params.endCreateTime = this.filterData.createTime[1]
            }
            if (this.filterData.submitTime != null) {
                params.startSubmitTime = this.filterData.submitTime[0]
                params.endSubmitTime = this.filterData.submitTime[1]
            }
            if (this.filterData.auditTime != null) {
                params.startAuditTime = this.filterData.auditTime[0]
                params.endAuditTime = this.filterData.auditTime[1]
            }
            this.tableLoading = true
            platformListByEntityST(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list || []
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {
    min-height: auto;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
    -moz-appearance: textfield !important;
}
</style>
