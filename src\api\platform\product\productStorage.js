import service from '@/utils/request'

const { httpPost } = service

const getProductInventoryList = params => {
    return httpPost({
        url: '/materialMall/productInventory/listPage',
        params
    })
}

const updateProductInventoryState = params => {
    return httpPost({
        url: '/materialMall/platform/productInventory/updateState',
        params
    })
}

const update = params => {
    return httpPost({
        url: '/materialMall/platform/productInventory/update',
        params
    })
}
const importBatch = params => {
    return httpPost({
        url: '/materialMall/platform/productInventory/importBatch',
        params
    })
}
const create = params => {
    return httpPost({
        url: '/materialMall/platform/productInventory/create',
        params
    })
}
const importBatchMaterial = params => {
    return httpPost({
        url: '/materialMall/platform/productInventory/importBatch',
        params
    })
}
const updateBatch = params => {
    return httpPost({
        url: '/materialMall/platform/productInventory/updateBatch',
        params
    })
}
export {
    updateBatch,
    importBatchMaterial,
    getProductInventoryList,
    updateProductInventoryState,
    update,
    importBatch,
    create,
}