<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                          <el-button style="margin-top:10px" type="primary" @click="outputAll" class="">导出数据</el-button>
                          <importShudaoExcel v-show="true"></importShudaoExcel>&nbsp;
                          <el-button type="primary2" @click="onDownload" class="btn-blue">下载模版</el-button>
                        </div>
                    </div>
                    <div class="search_box">
<!--                        <el-select style="margin-right: 10px;" v-model="formData.code" placeholder="请选择">-->
<!--                            <el-option-->
<!--                                v-for="item in cords"-->
<!--                                :key="item.value"-->
<!--                                :label="item.label"-->
<!--                                :value="item.value">-->
<!--                            </el-option>-->
<!--                        </el-select>-->
                        <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字"
                                  v-model="keywords"><img src="@/assets/search.png" slot="suffix" @click="onSearch"/>
                        </el-input>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table">
                <el-table @row-click="handleCurrentInventoryClick2" ref="eltableCurrentRow2" class="table"
                          :height="rightTableHeight" :data="tableData" border highlight-current-row
                          @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="80">
                        <template v-slot="scope">
                            <span class="action" @click="onDel(scope)">
                                <img src="../../../../assets/btn/delete.png" alt="">
                            </span>
                        </template>
                    </el-table-column>
                    <!-- 名称 -->
                    <el-table-column label="单位名称" width="">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.enterpriseName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" width="380" type="index">
                        <template v-slot="scope">
                          <span class="" >{{ scope.row.remarks }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.totalCount"
                :limit="20"
                :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange"
                @sizeChange="sizeChange"
            />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新增编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'" >
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px" >
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="单位名称：" prop="keyValue">
                                <el-input v-model="formData.enterpriseName" placeholder="请输入单位名称（单位名称不能重复）" clearable></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="备注：" prop="remarks">
                                <el-input v-model="formData.remarks" placeholder=""  clearable  type="textarea"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>

    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import {
    create,
    getListSystemInitVos,
    updateByBatch
} from '@/api/platform/system/systemParam'
// eslint-disable-next-line no-unused-vars
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapActions } from 'vuex'
import {
    createShudaoEnterprise,
    dele, deleteBatch,
    inquireShudaoEnterprise, supplyOutputExcel, excelTemplate
} from '@/api/platform/product/shudaoEnterprise'
import importShudaoExcel from '@/components/importShudaoExcel.vue'
// import { sendRespond } from '@/api/platform/mail/outbox'

export default {
    components: {
        importShudaoExcel,
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler (val) {
                console.log(val)
                this.getParams()
                inquireShudaoEnterprise(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }

        },
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            showLoading: false,

            fileUrl: '', //上传文件的域名地址
            limitNum: 1, //文件上传个数限制
            fileList: [], //文件列表
            showFileList: false, //文件列表是否显示,默认不显示
            currentQuery: {
                ids: []
            },
            // 当前查询
            tableLoading: false,
            // 表格数据
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            dataListSelections: [],
            // 高级搜索
            shudaoEnterpriseId: '',
            alertname: '字典',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            tableData: [],
            formRules: {
                // keyValue: [{ required: true, message: '', trigger: 'blur' }],
                // code: [{ required: true, message: '请选择字典名称', trigger: 'blur' }],
                //企业名字不重复
                enterpriseName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],

            },
            formData: {
                enterpriseName: '',
                remarks: '',
                name: '',
                code: '',

                keyValue: '',
                keyValue2: '',
                sort: '',
            },
            initSysParamFilter: [],
            cords: [],
            mapObj: null,
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            filterData: {
                name: '',
                orderBy: 3,
                type: 1,
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        this.getParams()
        let params = this.requestParams
        inquireShudaoEnterprise(params).then(res => {
            let arr = []
            res.list.forEach(item => {
                if (item.type == 1) arr.push(item)
            })
            this.pages = res
            this.tableData = res.list
        })
        getListSystemInitVos(params).then(res => {
            this.initSysParamFilter = res
        })

        getListSystemInitVos(params).then(res => {
            this.cords = res
        })
    },
    methods: {
        showLoadingM (state) {
            if(state == 1) {
                this.showLoading = true
            }else {
                this.showLoading = false
            }
        },
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                Name: '',
                title: '',
            },
            done()
        },
        hideDialog () {
            this.filterData = {
                name: '',
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData,
                type: 1
            }
            this.requestParams.page = this.pages.currPage
        },

        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
            this.dataListSelections = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () {
        },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = scope.row
            this.action = '编辑'
            this.formRules.keyValue.message = ''
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
            this.formRules.keyValue.message = ''
        },
        onDownload () {
            showLoading()
            excelTemplate().then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' })// 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob)// 3.创建一个临时的url指向blob对象
                const a = document.createElement('a')// 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '蜀道企业导入模板.xlsx'
                a.click()
                window.URL.revokeObjectURL(url)// 5.释放这个临时的对象url
            }).finally(()=>{
                hideLoading()
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该信息吗？', async () => {
                dele({ id: scope.row.shudaoEnterpriseId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                })
            })
        },
        // 全部导出
        outputAll () {
            if(this.tableData.length == 0) {
                return  this.$message.error('数据为空！')
            }
            showLoading()
            if(this.dataListSelections.length != 0) {
                let ids = this.dataListSelections.map(item => {
                    return item.shudaoEnterpriseId
                })
                console.log(ids)
                this.currentQuery.ids = this.currentQuery.ids.concat(ids)
                console.log(this.currentQuery.ids)
                this.tableLoading = true
                supplyOutputExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '蜀道集团所属企业信息.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.currentQuery.ids = []
                    this.dataListSelections = []
                    this.$message.success('操作成功')
                    this.tableLoading = false
                    hideLoading()

                }).catch(() => {
                    this.tableLoading = false
                })
            }else {
                this.tableLoading = true
                console.log(this.currentQuery)
                supplyOutputExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '蜀道集团所属企业信息.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.$message.success('操作成功')
                    this.tableLoading = false
                    hideLoading()

                }).catch(() => {
                    this.tableLoading = false
                    hideLoading()
                })
            }
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                let arr = this.selectedRows.map(item => {
                    return item.shudaoEnterpriseId
                })
                deleteBatch(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
            })
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (this.changedRow.length == 0) {
                this.changedRow.push(row)
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.systemId == row.systemId) {
                    t = row
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push(row)
            }
            flag = true
            // if(!this.changedRow[0]) {
            //     return this.changedRow.push({ systemId: row.systemId, keyValue: parseInt(row.keyValue), keyValue2: parseInt(row.keyValue2), sort: parseInt(row.sort), remarks: parseInt(row.remarks) })
            // }
            // let sortArr = this.changedRow.map((item, i) => {
            //     if(item.systemId === row.systemId) return i
            // })
            // let keyValueArr = this.changedRow.map((item, i) => {
            //     if(item.keyValue === row.keyValue) return i
            // })
            // let keyValue2Arr = this.changedRow.map((item, i) => {
            //     if(item.keyValue2 === row.keyValue2) return i
            // })
            // let remarksArr = this.changedRow.map((item, i) => {
            //     if(item.remarks === row.remarks) return i
            // })
            // if(sortArr[0]) {
            //     return this.changedRow[sortArr[0]].sort = row.sort
            // } else if(keyValueArr[0]) {
            //     return this.changedRow[keyValueArr[0]].keyValue = row.keyValue
            // }else if(keyValue2Arr[0]) {
            //     return this.changedRow[keyValue2Arr[0]].keyValue2 = row.keyValue2
            // }else if(remarksArr[0]) {
            //     return this.changedRow[remarksArr[0]].remarks = row.remarks
            // }
            // return this.changedRow.push({ systemId: row.systemId, keyValue: parseInt(row.keyValue), keyValue2: parseInt(row.keyValue2), sort: parseInt(row.sort), remarks: parseInt(row.remarks) })
        },
        // 修改
        changeSortValue () {
            if (!this.changedRow[0]) {
                let msg = '当前没有值被修改！'
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = '您确定要修改‘这些’的值吗？'
            this.clientPop('info', warnMsg, async () => {
                updateByBatch(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '修改成功', () => {
                            this.changedRow = []
                            this.getTableData()
                        })
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            this.currentQuery = this.requestParams
            inquireShudaoEnterprise(this.requestParams).then(res => {
                let arr = []
                res.list.forEach(item => {
                    if (item.type == 1) arr.push(item)
                })
                this.pages = res
                this.tableData = res.list
            })
            // inquireShudaoEnterprise(this.requestParams).then(res => {
            //     console.log(res.list)
            //     if (res.list) {
            //         this.tableData = res.list
            //     } else {
            //         this.clientPop('warn', res.message, () => { })
            //     }
            //     console.log(res)
            //     this.pages = res
            // })
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getParams()
            inquireShudaoEnterprise(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
            })
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },

        // 保存新增/修改
        onSave () {
            let params = {
                enterpriseName: this.formData.enterpriseName,
                remarks: this.formData.remarks
            }
            // let par = {
            //     checkEnterprise: this.formData.enterpriseName
            // }
            // if ( params.enterpriseName != this.tableData.enterpriseName) {
            createShudaoEnterprise(params).then(res => {
                if (res.code == 200) {
                    this.$message({
                        message: '保存成功',
                        type: 'success'
                    })
                    this.viewList = true
                }
                this.formData = {}
                this.getsendList()
            } )
            // }
            // else{
            //     this.$message({
            //         message: '保存失败,单位名称不能重复',
            //         type: 'error'
            //     })
            // }
        },
        // 修改数据
        handleEditData () {
            createShudaoEnterprise(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
                //   this.clientPop('errDt', 'msg', this.callback, null, this.cancelCallBack, 'title') // 错误详情弹框，msg为错误详情，title为错误标题
                // setError (name) {
                //   if (!this.errorList.find(x => x === name)) {
                //     this.errorList.push(name)
                //   }
                // },
            })
        },
        // 保存数据
        handleCreateData () {
            this.formData.name = this.$refs.selectLabel1.selectedLabel
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.getTableData()
            this.viewList = true
            this.$refs['formEdit'].clearValidate()
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.right .top {
    padding-right: 10px
}

.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__explain {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
