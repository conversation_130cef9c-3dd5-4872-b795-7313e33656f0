<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <div class="top">
          <div class="left">
            <div class="left-btn"  style=" display: flex; justify-content: space-between">
              <el-button style="margin-top:10px" type="primary" @click="outputAll" class="">数据导出</el-button>
<!--              <el-button  style="margin-top:10px" type="primary" v-if="this.userInfo.shopId==this.businessShopID&&!changeShopView" @click="changShopIdM"   class="btn-greenYellow">查看物资分公司数据</el-button>
              <el-button style="margin-top:10px" type="primary" v-if=this.changeShopView @click="returnShop()"  class="btn-greenYellow">返回子公司</el-button>-->
              <div style="height: 50px; line-height: 50px;margin-left: 10px">
                <el-date-picker
                    :default-time="['00:00:00', '23:59:59']"
                    @change="getTableData"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    v-model="filterData.dateScope"
                    type="datetimerange"
                    range-separator="至"
                    :picker-options="pickerOptions"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
              </div>
            </div>
            <div v-if="tableData && tableData.length > 0" class="ml10">含税：<span style="color: red">{{ tableData[0].countAmount }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;不含税：<span style="color: red">{{ tableData[0].countNoRateAmount }}</span>
            </div>
            <div v-else class="ml10">含税：<span style="color: red">0</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;不含税：<span style="color: red">0</span></div>
          </div>
          <div class="search_box">
            <el-input clearable style="width: 200px" type="text" @blur="getTableData" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png" slot="suffix" @click="getTableData" /></el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
      </div>
      <!--            表格-->
      <div class="e-table" :style="{ width: '100%' }">
        <el-table  ref="eltableCurrentRow2" @row-click="handleCurrentInventoryClick2"  @selection-change="selectionChangeHandle" class="table" :height="rightTableHeight" v-loading="tableLoading" :data="tableData"  border
        >
          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column label="序号" type="index" width="60" :index="indexMethod"/>
            <el-table-column label="客户" width="200" prop="bugOrgName" />
            <el-table-column label="供应商名称" width="200" prop="reconciliationAmount"/>
            <el-table-column label="结算单号" width="200" prop="amount"/>
            <el-table-column label="结算类型" width="160" prop="settlementType">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.settlementType == 0">零星采购结算</el-tag>
                <el-tag v-if="scope.row.settlementType == 1">大宗临购结算</el-tag>
                <el-tag v-if="scope.row.settlementType == 2">周转材料结算</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="不含税结算金额" width="" prop=""/>
            <el-table-column label="含税结算金额" width="" prop=""/>
            <el-table-column label="结算时间" width="160" prop="finishDate"/>
            <!--<el-table-column label="物资名称" width="200" prop="productName"/>-->
            <!--<el-table-column label="结算数量" width="" prop="number"/>-->

            <el-table-column label="已支付金额" width="" prop=""/>
            <el-table-column label="未支付金额" width="" prop=""/>
        </el-table>
      </div>
      <!--            分页-->
      <Pagination
          v-show="tableData !== null || tableData.length !== 0"
          :total="paginationInfo.total"
          :pageSize.sync="paginationInfo.pageSize"
          :currentPage.sync="paginationInfo.currentPage"
          @currentChange="getTableData"
          @sizeChange="getTableData"
      />
    </div>
    <!--        高级查询-->
    <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="false" >
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
        <!--                <el-row>-->
        <!--                    <el-col :span="12" :offset="0">-->
        <!--                        <el-form-item label="订单号：" >-->
        <!--                            <el-input clearable maxlength="100" placeholder="请输入订单号" v-model="filterData.orderSn"></el-input>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->
        <!--                </el-row>-->
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="客户名称：" >
              <el-input clearable maxlength="100" placeholder="请输入客户名称" v-model="filterData.productName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="供应商名称：" >
              <el-input clearable maxlength="100" placeholder="请输入供应商名称" v-model="filterData.productName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="结算类型：" >
              <el-select v-model="filterData.settlementType" placeholder="请选择结算类型" style="width: 100%;">
                <el-option
                  v-for="item in filterData.settlementTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="结算时间：">
              <el-date-picker
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="filterData.dateScope"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
            <el-button type="primary" @click = "expertSearch">查询</el-button>
            <el-button @click="resetSearchConditions">清空</el-button>
            <el-button @click="queryVisible = false">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { listByEntity, outputExcel } from '@/api/platform/order/orders'
import { mapState } from 'vuex'
export default {
    components: {
        Pagination
    },
    watch: {
    },
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        },
        ...mapState(['userInfo'])
    },
    data () {
        return {
            currentQuery: null,
            changeShopView: false,
            shopId: '',
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                },
                    //     {
                    //     text: '最近一年',
                    //     onClick (picker) {
                    //         const end = new Date()
                    //         const start = new Date()
                    //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                    //         start.setHours('00', '00', '00')
                    //         // end.setHours('23', '59', '59')
                    //         picker.$emit('pick', [start, end])
                    //     }
                    // }, {
                    //     text: '最近二年',
                    //     onClick (picker) {
                    //         const end = new Date()
                    //         const start = new Date()
                    //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                    //         start.setHours('00', '00', '00')
                    //         // end.setHours('23', '59', '59')
                    //         picker.$emit('pick', [start, end])
                    //     }
                    // }, {
                    //     text: '全部',
                    //     onClick (picker) {
                    //         picker.$emit('pick', [])
                    //     }
                    // }
                ]
            },
            // 当前查询
            tableLoading: false,
            // 表格数据
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            dataListSelections: [],
            // 高级搜索
            filterData: {
                supplierName: null,
                shopName: null,
                productName: null,
                dateScope: [],
                settlementType: null,
                belowPrice: null,
                abovePrice: null,
                dateValue: [], // 开始时间和结束时间
                settlementTypeOptions: [
                    {
                        value: 0,
                        label: '零星采购结算'
                    }
                    , {
                        value: 1,
                        label: '大宗临购结算'
                    }
                    , {
                        value: 2,
                        label: '周转材料结算'
                    }
                ],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope =  [this.dateStrM(start), this.dateStrM(end)]
        this.getTableData()
    },
    methods: {
        changShopIdM () {
            this.changeShopView = true
            // 物资分公司店铺名称
            this.shopId = '1645601878095495170'
            this.getTableData()
        },
        returnShop () {
            this.changeShopView = false
            this.shopId = null
            this.getTableData()
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
        // 全部导出
        outputAll () {
            if(this.tableData.length == 0) {
                return  this.$message.error('数据为空！')
            }
            if(this.dataListSelections.length != 0) {
                let ids = this.dataListSelections.map(item => {
                    return item.dealOrderInfoId
                })
                this.currentQuery.ids = ids
                if (this.shopId != null && this.shopId != '') {
                    this.currentQuery.shopId = this.shopId
                }
                this.tableLoading = true
                outputExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '供应商物资结算统计.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.currentQuery.ids = []
                    this.dataListSelections = []
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            }else {
                this.tableLoading = true
                outputExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '供应商物资结算统计.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            }
        },
        // 高级搜索
        expertSearch () {
            this.keywords = null
            this.getTableData()
            //重置数据
            this.filterData.belowPrice = null // 以下价格
            this.filterData.abovePrice = null // 以上价格
            this.filterData.orderSn = null
            this.filterData.productName = null
            this.filterData.shopName = null
            this.filterData.supplierName = null
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.queryVisible = false
        },
        resetSearchConditions () {
            this.filterData.supplierName = ''
            this.filterData.shopName = ''
            this.filterData.productName = ''
            this.filterData.settlementType = ''
            this.filterData.dateScope = []
            this.filterData.dateValue = []
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        indexMethod (index) {
            return this.paginationInfo.pageSize * (this.paginationInfo.currentPage - 1) + index + 1
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if (this.shopId != null && this.shopId != '') {
                this.currentQuery.shopId = this.shopId
            }
            if(this.filterData.dateValue.length != 0 && this.filterData.dateValue != null) {
                params.startFinishDate = this.filterData.dateValue[0]
                params.endFinishDate = this.filterData.dateValue[1]
            }
            if(this.filterData.dateScope.length != 0 && this.filterData.dateScope != null) {
                params.startFinishDate = this.filterData.dateScope[0]
                params.endFinishDate = this.filterData.dateScope[1]
            }
            if(this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if(this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if(this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if(this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if(this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if(this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            this.currentQuery = params
            this.tableLoading = true
            listByEntity(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {min-width: 200px; padding: 0;}
.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;
  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
    // bottom: 100px;
  }
}
/deep/ .el-dialog {
  .el-dialog__body {
    height: 380px;
    margin-top: 0px;
  }
}

.e-form {
  padding: 0 20px;

  .tabs-title::before {
    content: '';
    height: 22px;
    width: 8px;
    border-radius: 40px;
    background-color: #2e61d7;
    display: block;
    position: absolute;
    left: 20px;
    margin-right: 20px;
  }
}

.e-table {min-height: auto;}

.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
}
/deep/ .el-dialog__body {
  margin-top: 0;
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
  -moz-appearance: textfield !important;
}
</style>

