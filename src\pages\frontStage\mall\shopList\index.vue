<template>
    <main>
        <div class="contentBox center">
            <div class="contentBox front">
                <searchBox :form="filter" :list.sync="checkedList" @checkChange="handleFilter"></searchBox>
                <div class="dfa filterBox_top" v-if="this.currentTab != 3">
                    <el-radio-group v-model="checkedRadio" @change="checkboxChange">
                        <el-radio v-for="item in checkboxList" :label="item.value" :value="item.value" :key="item.value">
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                    <div :class="['sortBtn','pointer', sortObj.tag === 4 ? 'selected' : '']"
                        @click="selectSort(4)">
                    <span>综合评价</span><img :src="ComprehensiveEvaluation" alt="">
                </div>
                </div>
                <div class="row dfa filterBox_bottom">
                    <div class="searchInput df">
                        <div class="dfa">
                            <img src="@/assets/images/search1.png" alt="">
                            <input type="text" placeholder="搜索店铺" v-model="keywords" @keyup.enter="onSearch" />
                        </div>
                        <button @click="onSearch">搜索</button>
                    </div>
                </div>
            </div>
            <div class="shopList contentBox center">
                <div class="shopItem dfa p20" v-for="item in shopList" @click="$router.push('/mFront/shopIndex?shopId=' + item.shopId + '&currentTab=' + currentTab)"
                    :key="item.shopId">
                    <img :src="item.shopImg ? imgUrlPrefixAdd + item.shopImg : require('@/assets/images/img/queshen5.png')" alt="">
                    <div class="shopInfo">
                        <div class="title">{{ item.shopName }}</div>
                        <div class="detail"><span>开店时间：</span><span>{{ simpleFormatDate(item.openDate) }}</span></div>
                        <div class="detail"><span>所在区域：</span><span>{{ item.city || '未设置' }}</span></div>
                        <div class="detail df"><span>主营业务：</span><div class="textOverflow1" style="max-width: 370px;">{{ item.mainBusiness || '未设置' }}</div></div>
                    </div>
                </div>
            </div>
<!--            <div v-if="shopList.length == totalCount && totalCount != 0" class="nomore">没有更多了~~</div>-->
            <div class="p20">
                <Pagination
                    :total="pages.totalCount"
                    :current-page="pages.currPage"
                    :page-size="pages.pageSize"
                    :page-sizes="[10, 20]"
                    @currentChange="handlePageChange"
                    @sizeChange="handleSizeChange"
                />
            </div>
        </div>
        <publicity></publicity>
    </main>
</template>

<script>
import searchBox from '../../components/searchBox'
import Pagination from '@/pages/frontStage/components/simplePagination'
import publicity from '../../components/publicity.vue'
import arrow from '@/assets/images/arrow.png'
import arrow_up from '@/assets/images/arrow_up.png'
import arrow2 from '@/assets/images/arrow2.png'
import arrow2_up from '@/assets/images/arrow2_up.png'

// import { getByCity } from '@/api/frontStage/systemParam'
import { listShopInfo } from '@/api/frontStage/shop'
import { getIndexSupplierList } from '@/api/w/indexShop'
import { simpleFormatDate } from '@/utils/common.js'
import { getCityMU } from '@/utils/common'
import { mapState } from 'vuex'
export default {
    computed: {
        ...mapState(['materialCity']),
    },
    components: {
        searchBox, publicity, Pagination
    },
    data () {
        return {
            arrow,
            arrow_up,
            arrow2,
            arrow2_up,
            priceArrow: arrow,
            timeArrow: arrow,
            ComprehensiveEvaluation: arrow,
            shopList: [],
            flag: true,
            checkedList: [],
            filter: [],
            checkboxList: [
                { label: '全部', value: null },
                { label: '平台自营', value: 1 },
                { label: '路桥内部店', value: 2 },
                { label: '其它', value: 3 },
            ],
            pages: {
                currPage: 1,
                pageSize: 10,
                totalCount: 0,
            },
            checkedRadio: null,
            destination: '',
            keywords: this.$route.query.keywords ? this.$route.query.keywords : '',
            sortObj: {
                tag: 1,
                descend: true,
            },
            tagFilterStr: '0-desc',
            area: '',
            initial: 0,
            currentTab: null,
        }
    },
    watch: {
        $route: function () {
            this.keywords = this.$route.query.keywords
            this.queryData()
        },
        'sortObj.tag': {
            handler (newVal, oldVal) {
                if (oldVal == 2) {
                    if (this.sortObj.descend) {
                        return this.timeArrow = this.arrow
                    }
                    this.timeArrow = this.arrow_up
                } else if (oldVal == 1) {
                    if (this.sortObj.descend) {
                        return this.priceArrow = this.arrow
                    }
                    this.priceArrow = this.arrow_up
                }else if (oldVal == 4) {
                    if (this.sortObj.descend) {
                        return this.ComprehensiveEvaluation = this.arrow
                    }
                    this.ComprehensiveEvaluation = this.arrow_up
                }
            }

        },
        tagFilterStr (newVal) {
            switch (newVal) {
            case '1-asc':
                this.priceArrow = this.arrow2_up
                break
            case '1-desc':
                this.priceArrow = this.arrow2
                break
            case '2-asc':
                this.timeArrow = this.arrow2_up
                break
            case '2-desc':
                this.timeArrow = this.arrow2
                break
            case '4-asc':
                this.ComprehensiveEvaluation = this.arrow2_up
                break
            case '4-desc':
                this.ComprehensiveEvaluation = this.arrow2
                break
            }
        },

    },
    async mounted () {
        let i = 0
        let alphabets = { name: '首字母', options: [{ label: '不限', value: 0 }], type: 'initials' }
        'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('').forEach(item => {
            i++
            alphabets.options.push({ label: item, value: i, type: 'initials' })
        })
        // 获取城市
        let cities = await this.getByCityM()
        this.filter = [ cities, alphabets ]
    },
    created () {
        this.currentTab = this.$route.query.currentTab
        this.queryData()
    },
    methods: {
        // 排序
        selectSort (tag) {
            if (this.sortObj.tag == tag) {
                this.sortObj.descend = !this.sortObj.descend
            }
            this.sortObj.tag = tag
            this.getFilterStr(this.sortObj)
            this.queryData()
        },
        // 获取城市
        async getByCityM () {
            // let params = {
            //     code: 'addressc',
            //     size: 20
            // }
            // let res = await getByCity(params)
            return getCityMU(this.materialCity)
        },
        simpleFormatDate,
        handleFilter (obj) {
            this.pages.currPage = 1
            this.area = obj.city ? obj.city.value : null
            this.initial = obj.initials ? obj.initials.value : null
            this.queryData()
        },
        // 懒加载请求列表
        lazyLoad () {
            listShopInfo({}).then(res => this.shopList = this.shopList.concat(res.list))
        },
        // 按钮点击
        checkboxChange () {
            this.pages.currPage = 1
            this.shopList = []
            this.queryData()
        },
        onSearch () {
            this.pages.currPage = 1
            this.shopList = []
            this.queryData()
        },
        getFilterStr (obj) {
            this.tagFilterStr = obj.descend ? `${obj.tag}-desc` : `${obj.tag}-asc`
        },
        queryData () {
            if (this.currentTab != 3) {
                let params = {
                    limit: this.pages.pageSize,
                    page: this.pages.currPage,
                    keywords: this.keywords,
                    type: this.checkedRadio,
                    area: this.area,
                    initial: this.initial,
                    orderBy: this.tagFilterStr,
                }
                listShopInfo(params).then(res => {
                    let { totalCount, currPage, pageSize, list } = res
                    this.pages.totalCount = totalCount
                    this.pages.currPage = currPage
                    this.pages.pageSize = pageSize
                    this.shopList = list
                })
            } else {
                let params = {
                    limit: this.pages.pageSize,
                    page: this.pages.currPage,
                    keywords: this.keywords,
                    area: this.area,
                    initial: this.initial,
                    orderBy: this.tagFilterStr,
                }
                getIndexSupplierList(params).then(res => {
                    let { totalCount, currPage, pageSize, list } = res
                    this.pages.totalCount = totalCount
                    this.pages.currPage = currPage
                    this.pages.pageSize = pageSize
                    this.shopList = list
                })
            }
        },
        handlePageChange (page) {
            this.pages.currPage = page
            this.queryData()
        },
        handleSizeChange (size) {
            this.pages.pageSize = size
            this.queryData()
        }
    },
}
</script>

<style scoped lang="scss">

main {
    width: 100%;
    padding-top: 20px;
    background-color: #f5f5f5;

    .contentBox {
        width: 1326px;
    }

    .searchBar {
        height: 52px;
        padding-left: 20px;
        background-color: #fff;

        .sort {
            width: 88px;
            height: 26px;
            padding: 0 9px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(212, 48, 48, 1);
            border: 1px solid rgba(212, 48, 48, 1);

            span {
                margin-right: 3px;
            }
        }

        .searchInput {
            margin-left: 10px;

            &>div:first-child {
                width: 196px;
                height: 26px;
                padding-left: 8px;
                border: 1px solid rgba(229, 229, 229, 1);

                input {
                    width: 104px;
                    height: 21px;
                    margin-left: 4px;

                    &::placeholder {
                        color: rgba(204, 204, 204, 1);
                    }
                }
            }

            button {
                width: 52px;
                height: 26px;
                font-size: 14px;
                font-weight: 400;
                color: rgba(255, 255, 255, 1);
                background: rgba(212, 48, 48, 1);
            }
        }
    }

    .shopList {
        min-height: 380px;
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;

        .shopItem {
            width: 653px;
            height: 170px;
            margin-bottom: 19px;
            background-color: #fff;

            &:not(:nth-of-type(2n)) {
                margin-right: 20px;
            }

            img {
                width: 130px;
                height: 130px;
                margin-right: 20px;
            }

            .title {
                margin-bottom: 14px;
                font-size: 18px;
                font-weight: 500;
                letter-spacing: -1px;
            }

            .detail {
                margin-bottom: 10px;
                font-size: 14px;

                span:first-child {
                    color: rgba(102, 102, 102, 1);
                }

                span:last-child {
                    color: rgba(51, 51, 51, 1);
                }
            }
        }
    }

}
.front .filterBox_top .sortBtn {
  margin-left: 20px;
}
.pointer {
    font-size: 14px;
}
</style>