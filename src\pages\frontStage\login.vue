<template>
    <main>
        <div class="login-content">
            <el-tabs v-model="activeName" stretch>
                <el-tab-pane label="账号登录" name="first">
                    <el-form :model="userInfo" ref="accountLogin" :rules="userRules" :inline="false">
                        <el-form-item prop="account">
                            <!-- 账号输入框 -->
                            <el-input v-model="userInfo.account" placeholder="请输入账号">
                                <i slot="prefix">
                                    <img class="input-icon" src="@/assets/images/ico_id.png" alt=""/>
                                </i>
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="password">
                            <!-- 密码输入框 -->
                            <el-input class="password" v-model="userInfo.password" :type="pwType" placeholder="请输入登录密码">
                                <i slot="prefix">
                                    <img class="input-icon" src="@/assets/images/ico_passw.png" alt=""/>
                                </i>
                                <i slot="suffix">
                                    <img class="input-icon pointer" :src="hidePass" @click="toggleShowPass" alt=""/>
                                </i>
                            </el-input>
                        </el-form-item>
                    </el-form>
                    <div class="reset">
                        <div>
                            <el-checkbox v-model="isRemember" label="记住登录状态" :indeterminate="false"></el-checkbox>
                        </div>
                        <div>
                            <span @click="$router.push('/resetAccount')" style="color: #377CCB;cursor: pointer;">找回密码</span>
                        </div>
                    </div>
                    <button @click="onLogin">登录</button>
                    <div class="reg">
                        <span @click="$router.push('/mFront/register')">注册新用户</span>
                        <!--<span>忘记密码？</span>-->
                    </div>
                </el-tab-pane>
                <el-tab-pane label="短信登录" name="second">
                    <el-form :model="authCode" ref="phoneLogin" :rules="authCodeRules" :inline="false">
                        <el-form-item prop="phone">
                            <!-- 手机号输入框 -->
                            <el-input v-model="authCode.phone" placeholder="请输入手机号">
                                <i slot="prefix">
                                    <img class="input-icon" src="@/assets/images/ico_id.png" alt=""/>
                                </i>
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="code">
                            <!-- 验证码输入框 -->
                            <el-input class="verification" v-model="authCode.code" placeholder="请输入验证码">
                                <i slot="prefix">
                                    <img class="input-icon" src="@/assets/images/ico_passw.png"/>
                                </i>
                                <i slot="suffix">
                                    <span @click="verifyPhone">{{ verifyText }}</span>
                                </i>
                            </el-input>
                        </el-form-item>
                    </el-form>
                    <button @click="onLogin">登录</button>
                    <div class="newReg" @click="$router.push('/mFront/register')">注册新用户</div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <Dialog title="图形验证码" :close-on-click-modal="false" width="40%" top="30vh" :visible.sync="codeDialogVisible" @open="getCodeImg">
            <div class="verifyBox dfc">
                <el-input v-model="verification.verifyInput" placeholder="请输入图形验证码"/>
                <img class="pointer" :src="verification.verifyImg" @click="getCodeImg" alt="">
            </div>
            <span slot="footer">
                <el-button
                    class="codeDialogBtn" style="margin-right: 30px;" @click="codeDialogVisible = false"
                >取消</el-button>
                <el-button class="codeDialogBtn" type="primary" @click="checkCode">确定</el-button>
            </span>
        </Dialog>
        <Dialog title="提示" :visible.sync="alertDialogVisible">
            当前密码输入错误次数已到达<span style="color: orangered">{{diaData.count}}</span>次，超过<span style="color: orangered">8</span>次账号将被锁定<span style="color: orangered">1</span>天。
            <span slot="footer">
                <el-button @click="alertDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleLoginDia">确定</el-button>
            </span>
        </Dialog>
    </main>
</template>

<script>
// import md5 from 'js-md5'
import { encrypt } from '@/utils/common.js'
import { mapActions } from 'vuex'
import ico_hide from '@/assets/images/ico_hide.png'
import ico_show from '@/assets/images/ico_show.png'
import { loginSendCode, getPrivateKeyId, checkSendCodeVerify, getSendCodeImg, getUserPwdInfoByPhone } from '@/api/frontStage/login'
import Dialog from '@/pages/frontStage/components/dialog.vue'

const Base64 = require('js-base64').Base64

export default {
    props: ['user'],
    components: {
        Dialog
    },
    computed: {},
    data () {
        return {
            alertDialogVisible: false,
            diaData: {
                count: 0,
                newAcc: {}
            },
            ico_hide,
            ico_show,
            hidePass: ico_hide,
            loading: false,
            verifyText: '获取验证码',
            pwType: 'password',
            activeName: 'first',
            verification: { verifyImg: '', verifyId: '', verifyInput: '' },
            codeDialogVisible: false,
            userInfo: {
                account: '',
                password: '',
            },
            userRules: {
                account: [
                    { required: true, message: '请输入账号', trigger: 'blur' },
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                ],
            },
            authCode: {
                phone: '',
                code: '',
            },
            authCodeRules: {
                phone: [
                    { required: true, message: '请输入手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                code: [
                    { required: true, message: '请输入验证码', trigger: 'blur' },

                ],
            },
            isRemember: false,
            timer: null,
            visible: true,
            loginAfterPath: null, // 登录成功后跳转地址
            newPassword: null,
            loginFailureTime: { count: 0, countdown: 0 },
            res: []
        }
    },
    watch: {
        activeName (name) {
            // 清除验证
            if (name === 'first') {
                return this.$refs['phoneLogin'].clearValidate()
            }
            this.$refs['accountLogin'].clearValidate()
        },
    },
    created () {
        this.loginAfterPath = this.$route.params.row
        // 在页面加载时从cookie获取登录信息
        let account = this.getCookie('account')
        let password = Base64.decode(this.getCookie('password'))
        // 如果存在赋值给表单，并且将记住密码勾选
        if (account) {
            this.userInfo.account = account
            this.userInfo.password = password
            this.isRemember = true
        }
        this.enterLogin()
    },
    methods: {
        // 弹窗后确认登录
        handleLoginDia () {
            this.alertDialogVisible = false
            this.getToken(this.diaData.newAcc).then(async res => {
                if(res.code != null) {
                    return
                }
                if(res.code) this.writeFailCount()
                this.setUserInfo()
                // 登录后跳转开店
                if(this.userInfo.isInterior === 0 && localStorage.getItem('actionAfter') === 'openShop') {
                    localStorage.removeItem('actionAfter')
                    if(this.userInfo.shopId) {
                        this.$message({ message: '您已拥有店铺，无需开店', type: 'success' })
                    }else{
                        return this.$router.push('/front/becomeSeller')
                    }
                }
                this.goToHomePage()
            }).catch(err => {
                console.log('🚀 ~ err', err)
            })

        },
        ...mapActions(['getToken', 'getToken2']),
        getCodeImg () {
            this.verification.verifyInput = ''
            getSendCodeImg().then(res => {
                let blob = new Blob([res.data], { type: 'image/jpeg' })
                this.verification.verifyImg = window.URL.createObjectURL(blob)
                this.verification.verifyId = res.headers.verifyid
            })
        },
        checkCode () {
            let { verifyId, verifyInput } = this.verification
            checkSendCodeVerify({ id: verifyId, verifyInput }).then(async res => {
                if(res.code !== 200) return
                this.codeDialogVisible = false
                let privateKeyId = await this.getPrivateKey(this.authCode.phone)
                if(!privateKeyId) return
                this.loginLoading = true
                loginSendCode({ phone: this.authCode.phone, privateKeyId }).then(res => {
                    if (res.code !== 200) return
                    this.$message.success('发送成功')
                    this.handleCountdown()
                }).finally(() => {
                    this.loginLoading = false
                })
            })
        },
        verifyPhone () {
            if (this.verifyText !== '获取验证码' || this.activeName !== 'second') return
            this.$refs['phoneLogin'].validateField('phone', phoneError => {
                if (phoneError) return
                this.codeDialogVisible = true
            })
        },
        // 是否显示密码
        toggleShowPass () {
            this.hidePass = this.hidePass === this.ico_hide ? this.ico_show : this.ico_hide
            this.pwType = this.pwType === 'text' ? 'password' : 'text'
        },
        async getPrivateKey (phone) {
            let res = await getPrivateKeyId({ phone })
            if(typeof res !== 'string' || !res.length > 0) return ''
            return encrypt(res.verification)
        },
        // 获取验证码
        getAuthCode () {
            if (this.verifyText !== '获取验证码' || this.activeName !== 'second') return
            this.$refs['phoneLogin'].validateField('phone', async phoneError => {
                // 这里写获取短信验证码代码
                if (phoneError) return
                let privateKeyId = await this.getPrivateKey(this.authCode.phone)
                if(!privateKeyId) return
                this.loginLoading = true
                loginSendCode({ phone: this.authCode.phone, privateKeyId }).then(res => {
                    if (res.code !== 200) return
                    this.$message({ message: '发送成功', type: 'success' })
                    this.handleCountdown()
                }).finally(() => {
                    this.loginLoading = false
                })
            })
        },
        handleCountdown () {
            let countdown = 60
            let timer = setInterval(() => {
                if (countdown === 0) {
                    this.verifyText = '获取验证码'
                    return clearInterval(timer)
                }
                this.verifyText = `倒计时 ${countdown}`
                countdown -= 1
            }, 1000)
        },
        goIndex () {
            this.$router.replace('/')
        },
        goList () {
            this.$router.replace('/list')
        },
        radioChange (e) {
            // 当点击已经选中的把 isRemember 置空，就是取消选中，并返回
            if (this.isRemember === e) {
                this.isRemember = false
                return
            }
            // 不是选中，选中当前点击 Radio
            this.isRemember = e
            // 选中操作
        },
        onLogin () {
            this.userInfo.account = this.userInfo.account.trim()
            let { countdown } = this.loginFailureTime
            if (countdown !== 0) {
                return this.$message({ message: `请在${countdown}秒后重试`, type: 'warning' })
            }
            if (this.activeName === 'first') {
                this.$refs['accountLogin'].validate(async valid => {
                    if (valid) {
                        this.newPassword = this.userInfo.password
                        let newAcc = {
                            account: this.userInfo.account,
                            password: encrypt(this.userInfo.password)
                        }
                        let res = await  getUserPwdInfoByPhone({ phone: newAcc.account })
                        if (res) {
                            if( res.state === 1) {
                                // this.$message.warning('您的密码错误次数到' + res.count + '次，超过8次将锁定')
                                this.$message({ message: res.cause + '，请联系系统管理员或使用验证码登录', type: 'error', duration: 5000 })
                                return
                            }
                            if(res.count >= 5) {
                                this.diaData.count = res.count
                                this.diaData.newAcc = newAcc
                                this.alertDialogVisible = true
                                return
                                // this.$message.warning('您的密码错误次数到' + res.count + '次，超过8次将锁定')
                                // this.$message({ message: '您的密码错误次数到' + res.count + '次，超过8次将锁定', type: 'warning', duration: 5000 })
                            }
                            if (res.state === 1 && res.cause === '密码错误次数超过最大次数') {
                                this.$message.error('密码错误次数超过最大次数，请联系管理员')
                                return
                            }
                        }
                        this.getToken(newAcc).then(res => {
                            if (res.code != null) {
                                if(res.code == 500113) {
                                    return this.clientPop('warning', '尊敬的用户，由于您账号的密码强不够，为了安全考虑，平台已修改您的密码，原密码不能登录，请联系管理员。谢谢！', async () => {
                                    })
                                }else {
                                    return
                                }
                            }
                            if (res.code) this.writeFailCount()
                            this.setUserInfo()
                            if(res.firstLogin === 1) localStorage.setItem('firstLogin', 'true')
                            if(res.diaLogMap != null) localStorage.setItem('diaLogMap',  JSON.stringify(res.diaLogMap))
                            this.goToHomePage()
                        }).catch(err => {
                            console.log('error has occurred as below:')
                            console.log(err)
                        })
                    }
                })
            } else {
                // 手机号登陆
                this.$refs['phoneLogin'].validate(valid => {
                    if (valid) {
                        this.getToken2(this.authCode).then(res => {
                            if (res.code != null) return
                            if (res.code) this.writeFailCount()
                            if(res.firstLogin === 1) localStorage.setItem('firstLogin', 'true')
                            if(res.diaLogMap != null) localStorage.setItem('diaLogMap',  JSON.stringify(res.diaLogMap))
                            this.goToHomePage()
                        }).catch(err => {
                            console.log('error has occurred as below:')
                            console.log(err)
                        })
                    }
                })
            }
        },
        enterLogin () {
            document.onkeydown = e => {
                e = window.event || e
                if (this.$route.path === '/login' && (e.key === 'Enter' || e.key === 'enter')) {
                    //调用登录事件方法
                    if (!this.timer) {
                        this.onLogin()
                        this.timer = setTimeout(() => {
                            clearTimeout(this.timer)
                            this.timer = null
                        }, 3000)
                    }
                }
            }
        },
        // 储存表单信息
        setUserInfo: function () {
            // 判断用户是否勾选记住密码，如果勾选，向cookie中储存登录信息，如果没有勾选，储存的信息为空
            let account = this.isRemember ? this.userInfo.account : ''
            let password = this.isRemember ? Base64.encode(this.newPassword) : ''
            this.setCookie('account', account)
            this.setCookie('password', password)
        },
        // 获取cookie
        getCookie: function (key) {
            if (document.cookie.length <= 0) return ''

            let start = document.cookie.indexOf(key + '=')
            if (start !== -1) {
                start = start + key.length + 1
                let end = document.cookie.indexOf(';', start)
                if (end === -1) end = document.cookie.length
                return unescape(document.cookie.substring(start, end))
            }
        },
        // 保存cookie
        setCookie: function (cName, value, expiredays) {
            let exdate = new Date()
            exdate.setDate(exdate.getDate() + expiredays)
            document.cookie = cName + '=' + decodeURIComponent(value) +
                ((expiredays == null) ? '' : ';expires=' + exdate.toGMTString())
        },
        changePass (value) {
            this.visible = !(value === 'show') //切换密码框的显示
            this.$nextTick(function () {//对焦密码框
                this.$refs['password'].focus()
            })
        },
        goToHomePage () {
            if (this.loginAfterPath != null) {
                if (this.loginAfterPath.indexOf('/register') !== -1) {
                    window.open('/', '_self')
                } else {
                    location.href = this.loginAfterPath
                }
            } else if (document.referrer.indexOf('register') !== -1 || document.referrer.indexOf('login') != -1 || document.location.href.indexOf('/login') != -1) {
                window.open('/', '_self')
            } else {
                window.location.replace(document.referrer)
            }
        },
        // 登录失败超过6次进入倒计时，计时结束后才能再次登录
        writeFailCount () {
            if(this.res.count >= 5 && this.res.count <= 8) {
                // this.$message.warning('您的密码错误次数到' + res.count + '次，超过8次将锁定')
                this.$message({ message: '您的密码错误次数到' + this.res.count + '次，超过8次将锁定', type: 'warning', duration: 5000 })
            }

            this.loginFailureTime.count++

            if (this.loginFailureTime.count !== 6) return
            this.loginFailureTime.countdown = 60
            let timer = setInterval(() => {
                this.loginFailureTime.countdown--
                if (this.loginFailureTime.countdown === 0) {
                    clearInterval(timer)
                    this.loginFailureTime.count = 0
                }
            }, 1000)
        },
    }
}
</script>
<style lang="scss" scoped>
main {
    height: 700px;
    background-image: url(../../assets/images/BG.png);
    position: relative;
    background-size: cover;
}

/deep/ .el-button.codeDialogBtn {
    width: 90px;
    line-height: 40px;
    font-size: 20px;
    height: 40px;
    border-radius: 0;
}

.verifyBox {
    width: 100%;

    /deep/ .el-input {
        width: 250px;
        height: 50px;
        border-radius: 0;

        .el-input__inner {
            width: 250px;
            height: 50px;
            border-radius: 0;
        }
    }

    img {
        width: 140px;
        height: 50px;
        border: 1px solid lightgray;
    }

    /deep/ .el-button {
        padding: 0 0;
        text-align: center;
        font-size: 16px;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 0;
        color: rgba(33, 110, 198, 1);
        background-color: #fff;
    }
}

.login-content {
    width: 380px;
    height: 368px;
    padding: 40px;
    background-color: #fff;
    position: absolute;
    top: 129px;
    right: 377px;

    /deep/ .el-tabs {
        .el-tabs__nav-scroll {
            width: 300px;
        }

        .el-tabs__nav,
        .el-tabs__item {
            font-size: 18px;
            color: #333;
            border: none;
        }

        .el-tabs__nav {
            border-bottom: 1px solid #D8D8D8;
        }

        .el-tabs__item {
            width: 150px;
            padding: 0;
            text-align: center;
        }

        .el-tabs__active-bar {
            width: 150px;
            height: 3px;
            background-color: #216EC6;
        }

        .el-tabs__content {
            padding-top: 15px;
        }

        .el-form-item {
            height: 65px;
            margin-bottom: 0;
        }

        .el-input {

            .el-input__prefix,
            .el-input__suffix {
                width: 40px;
                height: 45px;
            }

            i {
                width: 40px;
                height: 45px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .el-input__validateIcon {
                display: none;
            }
        }

        .password .el-input__inner {
            padding-right: 40px;
        }

        .verification {
            .el-input__inner {
                padding-right: 100px;
            }

            .el-input__suffix, .el-input__suffix-inner {
                width: 100px;

                i {
                    font-style: normal;
                    width: 100px;

                    span {
                        color: #226FC7;
                        font-size: 14px;
                        cursor: pointer;
                        user-select: none;
                    }
                }
            }
        }

        .el-input__inner {
            width: 300px;
            height: 45px;
            padding-left: 40px;
            font-size: 16px;
            border: 1px solid #D9D9D9;
            border-radius: 0;

            ::-webkit-input-placeholder {
                color: #D9D9D9;
            }
        }

        button {
            width: 300px;
            height: 45px;
            margin-bottom: 20px;
            font-size: 16px;
            color: #fff;
            background-color: #216EC6;
        }

        .reg {
            color: #377CCB;
            text-align: center;
            margin-top: -2%;
            span {
              cursor: pointer;
            }

            /*span:first-child {
              margin-right: 15px;
            }*/
        }

        .newReg {
            text-align: center;
            color: #377CCB;
            cursor: pointer;
        }
    }
}
.reset {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6%;

  .is-checked .el-checkbox__inner {
    background-color: rgba(33, 110, 198, 1);
  }

  .el-checkbox__inner {
    border: 1px solid rgba(33, 110, 198, 1);
    border-radius: 4px;
  }

  .el-checkbox__label {
    color: #999;
  }
}
// --------------------------
</style>
