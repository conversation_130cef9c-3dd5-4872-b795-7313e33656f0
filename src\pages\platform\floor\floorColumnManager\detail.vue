<template>
    <div class="base-page">
        <div class="e-form" v-if="show">
            <BillTop @cancel="handleClose"></BillTop>
            <div class="tabs warningTabs" style="padding-top: 70px;">
                <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                    <el-tab-pane label="栏目信息" name="columnInfo" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="楼层信息" name="floorInfo" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <div id="tabs-content">
                        <!-- 栏目信息 -->
                        <div id="columnInfoCon" class="con">
                            <div class="tabs-title" id="columnInfo">栏目信息</div>
                            <div style="width: 100%" class="form">
                                <el-form
                                    :model="columnData" :label-width="formLabelWidth" ref="formEdit2" :rules="formRules2"
                                    class="demo-ruleForm"
                                >
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="栏目编号：" prop="columnNumber">
                                                <span>{{ columnData.columnNumber }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="栏目名称：" prop="columnName">
                                                <el-input clearable @blur="updateName" v-model="columnData.columnName"></el-input>
                                                <!--                                            <span>{{ columnData.columnName }}</span>-->
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <!--                                    <el-col :span="12">-->
                                        <!--                                        <el-form-item label="创建商城：" prop="mallType">-->
                                        <!--                                            <span v-if="columnData.mallType == '1'">装备商城</span>-->
                                        <!--                                            <span v-else-if="columnData.mallType == '0'">慧采商城</span>-->
                                        <!--                                        </el-form-item>-->
                                        <!--                                    </el-col>-->
                                        <el-col :span="12">
                                            <el-form-item label="栏目状态：" prop="state">
                                                <el-tag v-if="columnData.state==1" type="success">启用</el-tag>
                                                <el-tag v-if="columnData.state==0" type="danger">停用</el-tag>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <!--                                <el-row>-->
                                    <!--                                    <el-col :span="24">-->
                                    <!--                                        <el-form-item label="备注信息：" prop="remarks">-->
                                    <!--                                            <span>{{ columnData.remarks }}</span>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                    <!--                                </el-row>-->
                                </el-form>
                            </div>
                        </div>
                        <!--楼层信息-->
                        <div id="floorInfoCon" class="con">
                            <div class="tabs-title" id="floorInfo">栏目楼层清单</div>
                            <div class="e-table" style="background-color: #ffffff">
                                <div class="top">
                                    <div class="btns-box">
                                        <el-button size="small" class="btn-greenYellow" plain @click="addData">新增</el-button>
                                        <el-button size="small" @click="changePublishState(1)" class="btn-greenYellow">批量启用</el-button>
                                        <el-button size="small" @click="changePublishState(0)" class="btn-delete">批量停用</el-button>
                                        <el-button size="small" @click="changeSortValue" class="btn-greenYellow">批量修改排序值</el-button>
                                        <el-button size="small" type="danger" class="btn-delete" plain @click="deleteData">批量删除</el-button>
                                    </div>
                                </div>
                                <div>
                                    <div class="errorMsg" v-if="false">
                                        <span></span>
                                    </div>
                                    <el-table
                                        @row-click="handleCurrentInventoryClick" ref="mainTable" v-loading="isLoading" border :data="floorData" class="table" :max-height="$store.state.tableHeight"
                                        @selection-change="handleSelectionChange"
                                    >
                                        <el-table-column type="selection" width="40"></el-table-column>
                                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                                        <el-table-column label="操作" width="">
                                            <template v-slot="scope">
                                                <span class="action" @click="onDel(scope)"><img src="@/assets/btn/delete.png" alt=""></span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="楼层名称" width="200">
                                            <template v-slot="scope">
                                                <span class="action" @click="handleView(scope)">{{ scope.row.floorName }}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="小标题" width="200">
                                            <template v-slot="scope">
                                                {{ scope.row.floorNameText }}
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="楼层主图" width="">
                                            <template v-slot="scope">
                                                <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.imgUrl"></el-image>
                                            </template>
                                        </el-table-column>
                                        <!--                                    <el-table-column  label="主图链接地址" width="200">-->
                                        <!--                                        <template v-slot="scope">-->
                                        <!--                                            <a :href="scope.row.mainImgUrl" style="color: blue">{{scope.row.mainImgUrl}}</a>-->
                                        <!--                                        </template>-->
                                        <!--                                    </el-table-column>-->
                                        <!--                                    <el-table-column  label="商品类别" width="">-->
                                        <!--                                        <template v-slot="scope">-->
                                        <!--                                            {{scope.row.floorProductType}}-->
                                        <!--                                        </template>-->
                                        <!--                                    </el-table-column>-->
                                        <el-table-column label="楼层状态" width="">
                                            <template v-slot="scope">
                                                <el-tag v-if="scope.row.state==1" type="success">显示</el-tag>
                                                <el-tag v-if="scope.row.state==0" type="danger">不显示</el-tag>
                                            </template>
                                        </el-table-column>
                                        <!--                                    <el-table-column  label="商城类型" width="">-->
                                        <!--                                        <template v-slot="scope">-->
                                        <!--                                            <span v-if="scope.row.mallType == '0'">慧采商城</span>-->
                                        <!--                                            <span v-else-if="scope.row.mallType == '1'">装备商城</span>-->
                                        <!--                                        </template>-->
                                        <!--                                    </el-table-column>-->
                                        <el-table-column label="排序值" width="">
                                            <template v-slot="scope">
                                                <el-input type="number" clearable v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tabs>
            </div>
            <div class="buttons" v-if="!isview">
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
        <div class="right" v-show="add">
            <!-- ---------------------新增/编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="floorFormRules" ref="floorFormEdit" :model="addFloor" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item required class="uploader" label="图片地址（推荐：208x170）：" prop="imgUrl">
                                <div v-loading="imgLoading">
                                    <el-upload
                                        class="avatar-uploader" action="fakeaction"
                                        :show-file-list="false"
                                        :before-upload="handleBeforeUpload" name="img"
                                        :auto-upload="true"
                                        :on-change="handleUploadChange"
                                        :http-request="uploadImg"
                                    >
                                        <img v-if="addFloor.imgUrl" :src="imgUrlPrefixAdd + addFloor.imgUrl" class="avatar" alt="">
                                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    </el-upload>
                                    <el-progress v-show="uploadInProgress" :percentage="uploadPercentage"></el-progress>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item required class="uploader" label="楼层背景（推荐：250x620）：" prop="backgroundUrl">
                                <el-upload
                                    :class="backImgLen === 1 ? 'hide_box_admin' : ''"
                                    action="fakeaction"
                                    :file-list="backImg"
                                    :limit="1"
                                    list-type="picture-card"
                                    :before-upload="handleBeforeUpload"
                                    :auto-upload="false"
                                    :on-remove="removeBackImg"
                                    :on-change="(file, fileList) => { validateFileSize(file, fileList, 1) }"
                                >
                                    <i class="el-icon-plus"></i>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!--                    <el-row>-->
                    <!--                        <el-col :span="12">-->
                    <!--                            <el-form-item width="150px" label="楼层名称：" prop="floorName">-->
                    <!--                                <el-input clearable v-model="addFloor.floorName" placeholder="请输入楼层名称" ></el-input>-->
                    <!--                            </el-form-item>-->
                    <!--                        </el-col>-->
                    <!--                        <el-col :span="12">-->
                    <!--                            <el-form-item width="150px" label="小标题：" prop="floorNameText">-->
                    <!--                                <el-input clearable v-model="addFloor.floorNameText" placeholder="" ></el-input>-->
                    <!--                            </el-form-item>-->
                    <!--                        </el-col>-->
                    <!--                    </el-row>-->
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="楼层分类：" prop="floorName">
                                <!--<el-input clearable v-model="floorData.floorName"></el-input>-->
                                <category-cascader
                                    :classPath.sync="addFloor.classPaths"
                                    :classId.sync="addFloor.classId"
                                    :catelogPath="addFloor.classPaths"
                                    @change="handleCategoryChange"
                                ></category-cascader>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <!--<el-col :span="12">
                            <el-form-item label="小标题：" prop="floorNameText">
                                <el-input clearable v-model="floorData.floorNameText"></el-input>
                            </el-form-item>
                        </el-col>-->

                        <!--                        <el-col :span="12">-->
                        <!--                            <el-form-item label="商品类别：" prop="floorProductType">-->
                        <!--                                <el-input clearable v-model="addFloor.floorProductType" placeholder="请输入商品类别" ></el-input>-->
                        <!--                            </el-form-item>-->
                        <!--                        </el-col>-->
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input clearable v-model="addFloor.sort" type="number" placeholder="填写排序值">
                                </el-input>
                            </el-form-item>
                        </el-col>

                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input clearable type="textarea" v-model="addFloor.remarks" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="handleCreateData()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
import { hideLoading, showLoading, throttle } from '@/utils/common'
import {
    batchDeleteFloor,
    batchNotPublishFloor,
    batchPublishFloor,
    changeSortValueFloor,
    createFloor,
    delFloor,
    getFloorList
} from '@/api/platform/floor/floor'
import { uploadFile } from '@/api/platform/common/file'
import { editColumn } from '@/api/platform/floor/column'
import CategoryCascader from '@/components/category-cascader.vue'

export default {
    components: { CategoryCascader },
    data () {
        return {
            imgLoading: false,
            floorFormRules: {
                imgUrl: [
                    { required: true, message: '请上传主图', trigger: 'blur' },
                ],
                floorName: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                ],
                floorNameText: [
                    { required: true, message: '请输入小标题', trigger: 'blur' },
                ],
            },
            formRules2: {
                columnName: [
                    { required: true, message: '请输入栏目名称', trigger: 'blur' },
                ],
            },
            uploadPercentage: 0,
            uploadInProgress: false,
            add: false,
            show: true,
            isLoading: false,
            alertName: '楼层',
            dialogVisible: false,
            topHeight: 120,
            //基本信息表单数据
            columnData: {
                state: null,
                gmtCreate: '',
                gmtModified: '',
                founderId: '',
                founderName: '',
                remarks: '',
                sort: null,
                mallType: 0,
            },
            changedRow: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            floorData: [],
            addFloor: {},
            // 表格数据
            // 高级查询数据对象
            filterData: {
                columnId: '',
                state: null,
                orderBy: 1,
                mallType: 0,
                isDelete: 0,
            },
            //选中数据
            selectedRows: [],
            formLabelWidth: '195px',
            tabsName: 'columnInfo',
            screenWidth: 0,
            screenHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            disabled: false, //是否禁止
            auditParams: {
                'billId': this.$route.query.billid,
                'billType': 7024
            },
            isview: false, //是否隐藏底部按钮
            formData: {},
            // 表单校验规则
            formRules: {
                imgUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
                floorName: [{ required: true, message: '请输入楼层名', trigger: 'blur' }],
                backgroundUrl: { required: true, message: '请上传楼层背景图', trigger: 'blur' },
                floorNameText: [{ required: true, message: '请输入小标题名', trigger: 'blur' }],
                floorProductType: [{ required: true, message: '请输入商品类别', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            backImgLen: 0,
            backImg: [],

        }
    },
    created () {
        //接收上一个页面传递的参数
        this.columnData = this.$route.params.row
        this.filterData.columnId = this.columnData.columnId
        this.setImg()
    },
    async mounted () {
        // if (this.$route.query.isview === 'true' || this.$route.query.isview === 'false') {
        //     this.isview = JSON.parse(this.$route.query.isview)
        // }
        //接收上一个页面传递的参数
        // this.columnData = JSON.parse(this.$route.query.columnInfo)
        // this.filterData.columnId = this.columnData.columnId
        let res = await getFloorList({ ...this.filterData })
        this.floorData = res.list
        // this.floorData.forEach(item => {
        //     if(item.gmtRelease !== null && item.gmtRelease.length === 19) {
        //         item.gmtRelease = item.gmtRelease.slice(0, 10)
        //     }
        //     if(item.gmtModified !== null && item.gmtModified.length === 19) {
        //         item.gmtModified = item.gmtModified.slice(0, 10)
        //     }
        // })
        // 保存所有tabName
        let arr = ['columnInfo', 'floorInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            state: state => state.equip.equipData.state, //通用流程状态
        }),
        tabsContentHeight () {
            return this.screenHeight - 60 + 'px !important'
        }
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        }
    },
    methods: {
        // 主图上传
        validateFileSize (file, fileList, type) {
            if (!(file.size / 1024 / 1024 < this.uploadImgSize)) {
                fileList.pop()
                return this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            fileList.pop()
            let arr = [this.updateMainImg, this.updateBackImg]
            arr[type](file, fileList)
        },
        uploadFileInfo (params) {
            const form = new FormData()
            form.append('files', params.raw)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        updateBackImg (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.backImgLen = 1
            this.floorLoading = true
            uploadFile(form).then(res => {
                this.addFloor.backgroundUrl = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                fileList.push(params)
                this.$message({ message: '上传成功', type: 'success' })
            }).finally(() => this.floorLoading = false)
        },
        setImg () {
            // "http://192.168.100.100:9000/mall/material/SRBC/20221212/b96a0422dd4d6794f8bf915cdcc0261.png"
            if (this.addFloor.backgroundUrl) {
                let backImgName = this.addFloor.backgroundUrl.split('/')
                this.backImg = [{
                    name: backImgName.reverse()[0],
                    url: this.imgUrlPrefixAdd + this.addFloor.backgroundUrl
                }]
                this.backImgLen = 1
            }
        },
        removeBackImg () {
            this.addFloor.backgroundUrl = null
            this.backImgLen = 0
        },
        handleCategoryChange (value) {
            let name = value.map(item => item.className)
            this.addFloor.className = name.join(' > ')
            this.addFloor.floorName = name[0]
            this.addFloor.floorNameText = name.reverse()[0]
            this.addFloor.classId = value.reverse()[0].classId
        },
        handleView (scope) {
            // this.viewList = 'class'
            this.formData = JSON.parse(JSON.stringify(scope.row))
            this.action = '编辑'
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/floor/floorManagerDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'floorManagerDetail',
                params: {
                    row: scope.row
                }
            })
        },
        // 修改名称
        updateName () {
            this.$refs.formEdit2.validate(valid => {
                if (valid) {
                    let params = {
                        columnId: this.columnData.columnId,
                        columnName: this.columnData.columnName
                    }
                    editColumn(params).then(res => {
                        this.message(res)
                    })
                }
            })
        },
        // 消息提示
        message (res) {
            if (res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            } else {
                this.$message({
                    message: res.message,
                    type: 'error'
                })
            }
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 上传图片
        async uploadImg (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            this.imgLoading = true
            uploadFile(form).then(res => {
                this.addFloor.imgUrl = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                this.addFloor.imgUrlId = res[0].recordId
                this.$message.success('上传成功')
                this.imgLoading = false
                // this.addFloor.mainImgUrl = res[0].objectPath
            }).catch(() => {
                this.imgLoading = false
            })
        },
        handleUploadChange (file) {
            if (file.status === 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status === 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        // 保存数据
        handleCreateData () {
            this.$refs.floorFormEdit.validate(valid => {
                if (valid) {
                    this.addFloor.imgUrl = this.addFloor.imgUrl.replace(this.imgUrlPrefixDelete, '')
                    this.addFloor.columnId = this.columnData.columnId
                    this.addFloor.columnName = this.columnData.columnName
                    createFloor(this.addFloor).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('保存成功')
                            this.getTableData()
                            this.viewList = true
                        }
                    })
                    this.show = true
                    this.add = false
                }
            })
        },
        onCancel () {
            this.show = true
            this.add = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (!this.changedRow[0]) {
                return this.changedRow.push({ floorId: row.floorId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if (item.floorId === row.floorId) {
                    return i
                }
            })
            if (arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ floorId: row.floorId, sort: parseInt(row.sort) })
        },
        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.floorId
            })
            if (!this.selectedRows[0]) {
                let msg = num == 1 ? `请选择要启用的${this.alertName}` : `请选择要停用的${this.alertName}`
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = num === 1 ? `您确定要启用选中的${this.alertName}吗？` : `您确定要停用选中的${this.alertName}吗？`
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublishFloor(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('启用成功')
                            this.getTableData()
                        }
                    })
                    break
                case 0:
                    batchNotPublishFloor(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('停用成功')
                            this.getTableData()
                        }
                    })
                }
            })
        },
        // 修改排序值
        changeSortValue () {
            // changeSortValue(this.changedRow).then(res => {
            //     console.log(res)
            //     this.getTableData()
            // })
            if (!this.changedRow[0]) {
                let msg = '当前没有排序值被修改！'
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                this.isLoading = true
                changeSortValueFloor(this.changedRow).then(res => {
                    this.isLoading = false
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '修改成功', () => {
                            this.getTableData()
                        })
                    }
                }).catch(() => {
                    this.isLoading = false
                })
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${this.alertName}吗？`, async () => {
                showLoading()
                delFloor(scope.row.floorId).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                        getFloorList(this.requestParams).then(res => {
                            if (res.list) {
                                this.tableData = res.list
                            } else {
                                this.clientPop('warn', res.message, () => {
                                })
                            }
                            this.pages = res
                        })
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        addData () {
            this.emptyForm()
            this.viewList = 'class'
            this.add = true
            this.show = false
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 获取列表数据
        async getTableData () {
            this.isLoading = true
            this.getParams()
            getFloorList({ ...this.filterData }).then(res => {
                if (res.list) {
                    this.floorData = res.list
                    // for(var i = 0 ; i < this.tableData.length ; i++) {
                    //     if (this.floorData[i].gmtRelease != null) {
                    //         this.floorData[i].gmtRelease = this.tableData[i].gmtRelease.slice(0, 10)
                    //     }
                    //     if(this.floorData[i].gmtCreate != null) {
                    //         this.floorData[i].gmtCreate = this.tableData[i].gmtCreate.slice(0, 10)
                    //     }
                    //     if(this.floorData[i].gmtModified != null) {
                    //         this.floorData[i].gmtModified = this.tableData[i].gmtModified.slice(0, 10)
                    //     }
                    // }
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.isLoading = false
                this.pages = res
            }).catch(() => {
                this.isLoading = false
            })
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        deleteData () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.floorId
                })
                batchDeleteFloor(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                            getFloorList(this.requestParams).then(res => {
                                if (res.list) {
                                    this.floorData = res.list
                                } else {
                                    this.clientPop('warn', res.message, () => {
                                    })
                                }
                                this.pages = res
                            })
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        //取消
        handleClose () {
            hideLoading()
            this.$router.replace('/platform/floor/floorColumn')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            // console.log($('.tabs-content'))
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        showBusinessLicenseHidden (info) {
            if (info == null) {
                this.businessLicenseHidden = false
            }
        },
    }
}
</script>

<style lang='scss' scoped>

.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

// upload
.avatar-uploader {
    /deep/ .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

.avatar-uploader {
    /deep/ .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/ .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/ .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.btns-box {
    padding: 6px 4px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>
