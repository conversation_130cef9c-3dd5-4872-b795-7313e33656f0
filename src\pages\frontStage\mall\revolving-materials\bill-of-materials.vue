<template>
  <div class="synthesize-detail">
    <el-dialog
      v-loading="synthesizeLoading"
      v-dialogDrag
      :visible.sync="showSynthesizeMonad"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="list-title dfa mb20" style="margin-top: -20px">
        周转材料清单
      </div>
      <el-form
        :inline="true"
        ref="synthesizeRef"
        :model="synthesizeFormData"
        style="margin-left: 20px"
        :rules="synthesizeRoles"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="供应商单位：" prop="supplierOrgName">
              {{ synthesizeFormData.supplierOrgName }}
              <!--                            <el-input  style="width: 300px" disabled v-model="synthesizeFormData.supplierOrgName"></el-input>-->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购单位：" prop="orgName">
              {{ synthesizeFormData.orgName }}
              <!--                            <el-input  style="width: 300px" disabled v-model="synthesizeFormData.orgName"></el-input>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="清单类型：" prop="billType">
              <el-radio v-model="synthesizeFormData.billType" :label="1"
                >浮动价格</el-radio
              >
              <el-radio v-model="synthesizeFormData.billType" :label="2"
                >固定价格</el-radio
              >
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货款支付周期（月）：" prop="paymentWeek">
              <el-select
                v-model="synthesizeFormData.paymentWeek"
                clearable
                placeholder="请选择货款支付周期"
              >
                <el-option
                  v-for="item in payWeekList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目收货地址选择：" prop="province">
              <el-cascader
                clearable
                style="width: 300px"
                size="large"
                :options="addressData"
                v-model="selectAddressOptions"
                @change="handleAddressChange"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目收货详细地址：" prop="receiverAddress">
              <el-input
                clearable
                v-model="synthesizeFormData.receiverAddress"
                style="width: 300px"
                placeholder="请输入项目收货地址"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单据状态：">
              <el-tag type="info" v-if="synthesizeFormData.state == 0"
                >草稿</el-tag
              >
              <el-tag v-if="synthesizeFormData.state == 1">已提交</el-tag>
              <el-tag type="success" v-if="synthesizeFormData.state == 2"
                >供应商已确认</el-tag
              >
              <el-tag type="success" v-if="synthesizeFormData.state == 3"
                >已推送周转材料计划</el-tag
              >
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参考总金额（元）：">
              <span style="color: red">{{
                synthesizeFormData.referenceSumAmount
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注：" prop="remarks">
              <el-input
                style="width: 600px"
                type="textarea"
                :auto-resize="false"
                v-model="synthesizeFormData.remarks"
                placeholder="请填写详细需求描述（材质、每单位长度、长度、宽度、高度/厚度、直径、是否镀锌等）"
                maxlength="1000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="list-title dfa mb20">清单明细</div>
      <el-table
        max-height="372px"
        border
        :data="synthesizeFormData.dtls"
        style="min-height: 372px; margin-left: 20px"
        :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
        :row-style="{ fontSize: '14px', height: '48px' }"
      >
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column prop="productSn" label="物资编号" width="" />
        <el-table-column prop="productName" label="商品名称" width="" />
        <el-table-column prop="materialName" label="物资名称" width="" />
        <el-table-column prop="spec" label="规格型号" width="" />
        <el-table-column prop="classNamePath" label="分类路径" width="" />
        <el-table-column prop="texture" label="材质" width="" />
        <el-table-column prop="brandName" label="品牌名称" width="" />
        <el-table-column prop="qty" label="数量" width="">
          <template v-slot="scope">
            <el-input
              type="number"
              v-model="scope.row.qty"
              @change="getChangedRow(scope.row)"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="计量单位" width="" />
        <el-table-column prop="zonePath" label="配送区域" width="" />
        <el-table-column prop="referencePrice" label="参考单价" width="" />
        <el-table-column prop="referenceAmount" label="参考金额" width="" />
      </el-table>
      <div class="list-title dfa mb20">附件信息</div>
      <el-form style="margin-left: 20px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="" prop="">
              <el-upload
                accept=".pdf,.jpeg,.jpg,.png"
                action="fakeaction"
                v-loading="uploading"
                :file-list="[]"
                :before-upload="handleBeforeUpload"
                :auto-upload="true"
                :http-request="uploadStFile"
                list-type="picture-card"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        max-height="372px"
        border
        :data="files"
        style="min-height: 372px; margin-left: 20px"
        :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
        :row-style="{ fontSize: '14px', height: '48px' }"
      >
        <el-table-column label="操作">
          <template slot-scope="scope">
            <i
              class="el-icon-download"
              @click="() => onDownloadFile(scope.row)"
            />
            <i
              class="el-icon-delete"
              @click="() => onDeleteFile(scope.row.fileFarId)"
            />
            <!-- <i class="el-icon-view" @click="()=>onDeleteFile(scope.row.fileFarId)" /> -->
          </template>
        </el-table-column>
        <el-table-column prop="" label="缩略图" width="">
          <template slot-scope="scope">
            <el-image :src="scope.row.thumbnail">
              <div slot="placeholder" class="image-slot">
                加载中<span class="dot">...</span>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="文件名称" width="" />
        <el-table-column prop="fileSize" label="文件大小" width="" />
        <el-table-column prop="uploadDate" label="上传时间" width="" />
        <el-table-column prop="founderName" label="上传人" width="" />
      </el-table>
      <span slot="footer">
        <el-button
          type="primary"
          class="btn-greenYellow"
          @click="saveSynthesizeTemporaryM"
          >保存</el-button
        >
        <el-button
          type="primary"
          class="btn-greenYellow"
          @click="saveSynthesizeTemporaryM(1)"
          >保存并提交</el-button
        >
        <el-button @click="clickCloneM">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { createSynthesizeTemporary } from '@/api/frontStage/userCenter'
import {
    previewFile,
    uploadFile,
    thumbnailImage,
} from '@/api/platform/common/file'
import { debounce } from '@/utils/common'
import { toFixed } from '@/utils/common'
import { CodeToText, regionData } from 'element-china-area-data'
import moment from 'moment'
export default {
    data () {
        return {
            // 地址
            addressData: regionData, // 地址数据
            showSynthesizeMonad: false,
            tipVisible: false,
            synthesizeLoading: false,
            synthesizeFormData: {
                receiverAddress: null,
            },
            tipFormData: {
                value: undefined,
            },
            auditorOptions: [],
            // 配送区域选项
            deliveryAreaOptions: [
                { label: '四川省成都市', value: 'chengdu' },
                { label: '四川省自贡市', value: 'zigong' },
                { label: '四川省攀枝花市', value: 'panzhihua' },
                { label: '四川省泸州市', value: 'luzhou' },
                { label: '四川省德阳市', value: 'deyang' },
                { label: '四川省绵阳市', value: 'mianyang' },
                { label: '四川省广元市', value: 'guangyuan' },
                { label: '四川省遂宁市', value: 'suining' },
                { label: '四川省内江市', value: 'neijiang' },
                { label: '四川省乐山市', value: 'leshan' },
                { label: '四川省南充市', value: 'nanchong' },
                { label: '四川省眉山市', value: 'meishan' },
                { label: '四川省宜宾市', value: 'yibin' },
                { label: '四川省广安市', value: 'guangan' },
                { label: '四川省达州市', value: 'dazhou' },
                { label: '四川省雅安市', value: 'yaan' },
                { label: '四川省巴中市', value: 'bazhong' },
                { label: '四川省资阳市', value: 'ziyang' },
                { label: '四川省阿坝藏族羌族自治州', value: 'aba' },
                { label: '四川省甘孜藏族自治州', value: 'ganzi' },
                { label: '四川省凉山彝族自治州', value: 'liangshan' },
            ],
            synthesizeRoles: {
                receiverAddress: [
                    { required: true, message: '请输入地址', trigger: 'blur' },
                ],
                billType: [
                    {
                        required: true,
                        message: '请选择周转材料清单类型',
                        trigger: 'blur',
                    },
                ],
                paymentWeek: [
                    { required: true, message: '请选择货款支付周期', trigger: 'blur' },
                ],
                province: [
                    { required: true, message: '请选择地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' },
                ],
            },
            cartLoading: false,
            disableBtn: false,
            selectedAddr: null,
            totalPrice: '',
            noTaxTotalPrice: '',
            addrList: [{ id: 0, addr: '四川成都市成华区保和街道' }],
            payWeekList: [
                { label: '0月', value: 0 },
                { label: '1月', value: 1 },
                { label: '2月', value: 2 },
                { label: '3月', value: 3 },
                { label: '4月', value: 4 },
                { label: '5月', value: 5 },
                { label: '6月', value: 6 },
            ],
            selectAddressOptions: [],
            selectAllProduct: false,
            // 购物车列表
            cartList: [],
            changedSku: null,
            paymentPeriodOptions: [
                { label: '1个月账期', value: 1 },
                { label: '2个月账期', value: 2 },
                { label: '3个月账期', value: 3 },
                { label: '4个月账期', value: 4 },
                { label: '5个月账期', value: 5 },
                { label: '6个月账期', value: 6 },
            ],
            uploading: false,
            files: [],
            planSubmitVisible: false,
            planAttachs: [],
            attachVisible: false,
        }
    },
    methods: {
        clickCloneM () {
            this.synthesizeFormData = { receiverAddress: null }
            this.selectAddressOptions = []
            this.showSynthesizeMonad = false
        },
        saveSynthesizeTemporaryM (num) {
            for (let i = 0; i < this.synthesizeFormData.dtls.length; i++) {
                let t = this.synthesizeFormData.dtls[i]
                if (t.qty == null || Number(t.qty) == 0) {
                    return this.$message.error(
                        '商品为【' + t.productName + '】数量不能为0！'
                    )
                }
            }
            this.$refs.synthesizeRef.validate(valid => {
                if (valid) {
                    let str = ''
                    if (num != null && num == 1) {
                        str = '您确定要保存并提交吗？'
                    } else {
                        str = '您确定要保存吗？'
                    }
                    this.clientPop('info', str, async () => {
                        this.synthesizeLoading = true
                        if (num != null && num == 1) {
                            this.synthesizeFormData.isSubmit = 1
                        }
                        // 这里区分大宗和周材
                        let stType
                        if (this.commitType == 2) {
                            stType = 0 // 大宗清单
                        }
                        if (this.commitType == 4) {
                            stType = 1 // 周材清单
                        }
                        createSynthesizeTemporary({
                            ...this.synthesizeFormData,
                            attachments: this.files,
                            stType,
                        })
                            .then(res => {
                                if (res.code == 200) {
                                    this.$message.success('操作成功！')
                                    this.delCardM()
                                    this.synthesizeFormData = {
                                        receiverAddress: null,
                                    }
                                    this.selectAddressOptions = []
                                    this.showSynthesizeMonad = false
                                }
                            })
                            .finally(() => {
                                this.synthesizeLoading = false
                            })
                    })
                } else {
                    return this.$message.error('请检查非空项！')
                }
            })
        },
        // 地址选择
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.synthesizeFormData.province = province
            this.synthesizeFormData.city = city
            this.synthesizeFormData.county = county
            if (province == null && city == null && county == null) {
                this.synthesizeFormData.receiverAddress = null
            } else {
                this.synthesizeFormData.receiverAddress = province + city + county
            }
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 获取修改后的行
        getChangedRow (row) {
            if (row.qty < 0) {
                row.qty = 0
            }
            if (row.qty > 999999) {
                row.qty = 999999
            }
            row.qty = this.fixed4(row.qty)
            if (row.isTwoUnit === 1) {
                row.twoUnitNum = this.fixed4(row.qty * row.secondUnitNum)
            }
            // }
            let sumAmount = 0
            for (let i = 0; i < this.synthesizeFormData.dtls.length; i++) {
                let t = this.synthesizeFormData.dtls[i]
                let amount = this.fixed2(Number(t.qty) * Number(t.referencePrice))
                t.referenceAmount = amount
                t.synthesizeAmount = amount
                sumAmount = this.fixed2(Number(sumAmount) + Number(amount))
            }
            this.synthesizeFormData.referenceSumAmount = sumAmount
            this.synthesizeFormData.synthesizeSumAmount = sumAmount
        },
        uploadStFile ({ file }) {
            const fileType = file.type == 'application/pdf' ? 3 : 1
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', fileType)
            form.append('isResetName', 1)
            this.uploading = true
            uploadFile(form)
                .then(res => {
                    if (res.code != null && res.code != 200) {
                        this.$message.error(res.msg)
                    } else {
                        let resO = res[0]
                        const url = URL.createObjectURL(file)
                        const fileData = {
                            name: resO.objectName,
                            url,
                            thumbnail: undefined,
                            fileSize: file.size,
                            fileFarId: resO.recordId,
                            fileType,
                            founderName: this.userInfo.userName,
                            founderId: this.userInfo.userId,
                            uploadDate: moment().format('YYYY-MM-DD'),
                        }
                        thumbnailImage({ recordId: resO.recordId }).then(res => {
                            const blob = new Blob([res])
                            const thumbnail = window.URL.createObjectURL(blob)
                            fileData.thumbnail = thumbnail
                        })
                        this.files.push(fileData)
                    }
                })
                .finally(() => {
                    this.uploading = false
                })
        },
        handleBeforeUpload (file) {
            return this.handleBeforeUploadFunc(
                ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
                200
            )(file)
        },
        handleBeforeUploadAttach (file) {
            return this.handleBeforeUploadFunc([], 200)(file)
        },
        handleBeforeUploadFunc (allowedTypes, maxSize) {
            return file => {
                if (file.size > maxSize * 1024 * 1024) {
                    this.$message.error(`上传的图片大小不能超过 ${maxSize}MB!`)
                    return false
                }
                if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
                    this.$message.error('仅支持上传图片（.jpg, .jpeg, .png）和PDF格式')
                    return false
                }
                return true
            }
        },
        onDeleteFile (fileFarId) {
            this.files = this.files.filter(file => file.fileFarId !== fileFarId)
        },
        onDeleteAttachFile (fileFarId) {
            this.planAttachs = this.planAttachs.filter(
                file => file.fileFarId !== fileFarId
            )
        },
        onDownloadFile ({ fileFarId, name }) {
            this.uploading = true
            previewFile({ recordId: fileFarId })
                .then(res => {
                    const blob = new Blob([res])
                    const url = window.URL.createObjectURL(blob)
                    const link = document.createElement('a')
                    link.href = url
                    link.download = name
                    document.body.appendChild(link)
                    link.click()

                    // 清理
                    document.body.removeChild(link)
                    URL.revokeObjectURL(url)
                })
                .finally(() => {
                    this.uploading = false
                })
        },
    },
    async created () {
        this.cartLoading = true
        this.handleNumChange = debounce(this.updateCartNum)
        this.cartLoading = false
    },
}
</script>
<style scoped lang="scss">
@import "./index.scss";
</style>
