<template>
    <div>
        <el-dialog
            v-dialogDrag custom-class="dlg" @close='closeDialog' width="80%" title="商品选择"
            :visible.sync="showDialog"
        >
            <div>
                <div class="box-left">
                    <select-material-class @classNodeClick="classNodeClick" ref="materialClassRef" :productType="0"/>
                </div>
            </div>
            <div class="box-right" v-loading="formLoading">
                <div class="e-table">
                    <div class="top" style="height: 50px; padding-left: 10px;">
                        <div class="left">
                            <el-input type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="table.keywords">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="handleInputSearch" alt=""/>
                            </el-input>
                            <el-button style="margin-left: 30px" type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>

                    <el-table
                        @selection-change="selectMaterialRow" @row-click="handleCurrentInventoryClick" highlight-current-row border
                        :data="table.tableData" class="table" ref="eltableCurrentRow" :max-height="$store.state.tableHeight"
                    >
                        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column label="编码" width="220" prop="serialNum"></el-table-column>
                        <el-table-column label="名称" width="200" prop="productName"></el-table-column>
                        <el-table-column label="店铺名称" prop="shopName"></el-table-column>
                        <el-table-column label="商品图片" width="160" prop="productMinImg">
                            <template v-slot="scope">
                                <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg"></el-image>
                            </template>
                        </el-table-column>
                        <el-table-column label="规格型号" prop="skuName"></el-table-column>
                        <el-table-column label="原价" prop="originalPrice"></el-table-column>
                        <el-table-column label="销售价格" prop="sellPrice"></el-table-column>
                        <el-table-column label="库存" prop="stock"></el-table-column>
                        <el-table-column label="计量单位" prop="unit"></el-table-column>
                        <el-table-column label="上架时间" width="160" prop="putawayDate"></el-table-column>
                    </el-table>
                </div>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="table.tableData && table.tableData.length > 0" :total="table.paginationInfo.total"
                    :pageSize.sync="table.paginationInfo.pageSize" :currentPage.sync="table.paginationInfo.currentPage" @currentChange="getTable"
                    @sizeChange="getTable"
                />
                <el-button type="primary" @click="dialogSubmit">确定</el-button>
                <el-button @click="closeDialog">取消</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
            </span>
        </el-dialog>
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品名称：">
                            <el-input maxlength="100" placeholder="请输入商品名称" v-model="filterData.productName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺名称：">
                            <el-input maxlength="100" placeholder="请输入店铺名称" v-model="filterData.shopName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="型号规格：">
                            <el-input maxlength="100" placeholder="请输入型号规格" v-model="filterData.skuName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="价格以上：">
                            <el-input type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 246px"></el-input>
                        </el-form-item>

                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="价格以下：">
                            <el-input type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 246px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="上架时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.putawayDate" type="datetimerange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--                <el-row>-->
                <!--                    <el-col :span="24">-->
                <!--                        <el-form-item label="创建时间：">-->
                <!--                            <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.createDate" type="datetimerange"-->
                <!--                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">-->
                <!--                            </el-date-picker>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
                <!--                <el-row>-->
                <!--                    <el-col :span="24">-->
                <!--                        <el-form-item label="修改时间：">-->
                <!--                            <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.modifiedDate" type="datetimerange"-->
                <!--                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">-->
                <!--                            </el-date-picker>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="排序：">
                            <el-radio v-model="filterData.orderBy" :label="0">按上架时间排序</el-radio>
                            <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                            <el-radio v-model="filterData.orderBy" :label="2">按创建时间排序</el-radio>
                            <el-radio v-model="filterData.orderBy" :label="3">按修改时间排序</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import SelectMaterialClass from '@/components/classTree'
import { listProductFullInfoVOPage } from '@/api/platform/product/Product'
import Pagination from '@/components/pagination/pagination'

export default {
    props: ['productType', 'showDialog'],
    data () {
        return {
            formLoading: true,
            queryVisible: false,
            filterData: {},
            table: {
                init: {
                    state: [1]
                },
                className: null,
                classId: null,
                keywords: null,
                tableData: [],
                selectRow: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            }
        }
    },
    components: {
        SelectMaterialClass, Pagination
    },
    created () {
        this.getTable()
    },
    methods: {
        resetSearchConditions () {
            this.filterData = {}
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTable()
        },
        // 高级查询
        confirmSearch () {
            this.formLoading = true
            this.table.keywords = ''
            this.getTable()
            this.queryVisible = false
        },
        // 分类点击
        classNodeClick (data) {
            this.formLoading = true
            this.table.classId = data.classId
            this.getTable()
        },
        // 确定
        dialogSubmit () {
            this.$emit('batchCreate', this.table.selectRow)
        },
        // 行点击
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        // 关闭窗口
        closeDialog () {
            this.$parent.closeDialog()
        },
        // 选择当前行
        selectMaterialRow (value) {
            this.table.selectRow = value
        },
        getTable () {
            let params = {
                page: this.table.paginationInfo.currentPage,
                limit: this.table.paginationInfo.pageSize,
                state: this.table.init.state,
            }
            if (this.table.classId != null) {
                params.classId = this.table.classId
            }
            if (this.table.keywords != null) {
                params.keywords = this.table.keywords
            }
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            if (this.filterData.skuName != null) {
                params.skuName = this.filterData.skuName
            }
            // if(this.filterData.modifiedDate != null ) {
            //     params.startModifiedDate = this.filterData.modifiedDate[0],
            //     params.endModifiedDate = this.filterData.modifiedDate[1]
            // }
            // if(this.filterData.createDate != null ) {
            //     params.startCreateDate = this.filterData.createDate[0],
            //     params.endCreateDate = this.filterData.createDate[1]
            // }
            if (this.filterData.putawayDate != null) {
                params.startPutawayDate = this.filterData.putawayDate[0]
                params.endPutawayDate = this.filterData.putawayDate[1]
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            listProductFullInfoVOPage(params).then(res => {
                this.table.tableData = res.list
                this.table.paginationInfo.total = res.totalCount
                this.table.paginationInfo.pageSize = res.pageSize
                this.table.paginationInfo.currentPage = res.currPage
                this.formLoading = false
            })

        }
    }
}
</script>

<style lang="scss" scoped>

/deep/ .el-dialog.dlg {
    height: 600px;

    .el-dialog__header {
        margin-bottom: 0;
    }

    .el-dialog__body {
        height: 474px;
        margin: 10px;
        display: flex;

        & > div {
            .e-pagination {
                background-color: unset;
            }

            //height: 670px;
            .title {
                height: 22px;
                margin-bottom: 10px;
                padding-left: 26px;
                text-align: left;
                line-height: 22px;
                color: #2e61d7;
                font-weight: bold;
                position: relative;
                display: flex;

                &::before {
                    content: '';
                    display: block;
                    width: 10px;
                    height: inherit;
                    border-radius: 5px;
                    background-color: blue;
                    position: absolute;
                    left: 10px;
                    top: 0;
                }
            }
        }

        .el-input__inner {
            border: 1px solid blue;
            border-radius: 6px;
        }

        .el-input__suffix {
            width: 20px;
        }

        .e-table {
            flex-grow: 1;

            .table {
                height: 100%;
            }
        }

        .box-left {
            width: 370px;
            display: flex;
            flex-direction: column;

            .search {
                padding: 0 10px;
            }
        }

        .box-right {
            flex-grow: 1;
            display: flex;
            flex-direction: column;

            & > div {
                display: flex;
                flex-direction: column;
            }

            .top {
                height: 374px;
                margin: 0;
                border-radius: 0;
                box-shadow: unset;
            }

            .bottom {
                flex-grow: 1;
            }
        }
    }

    .el-dialog__footer {
        background-color: #eff2f6;
    }
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0;
    }
}
</style>