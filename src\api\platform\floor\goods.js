import service from '@/utils/request'

const { httpPost } = service

const getGoodsList = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/listByEntityByCondition',
        params
    })
}

const batchDeleteGoods = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/deleteBatch',
        params
    })
}

const batchPublishFloorGoods = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/updateByPublish',
        params
    })
}

const batchNotPublishFloorGoods = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/updateNotPublish',
        params
    })
}

const changeSortValueFloorGoods = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/updateBatchById',
        params
    })
}

const batchUpdate = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/batchUpdate',
        params
    })
}
const updateByPublish = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/updateByPublish',
        params
    })
}
const updateNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/updateNotPublish',
        params
    })
}
export {
    updateNotPublish,
    updateByPublish,
    batchUpdate,
    getGoodsList,
    batchDeleteGoods,
    batchPublishFloorGoods,
    batchNotPublishFloorGoods,
    changeSortValueFloorGoods
}