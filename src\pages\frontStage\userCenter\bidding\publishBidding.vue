<template>
    <main>
        <div class="list-title dfa mb20">发布竞价采购</div>
        <div class="publishBox">
            <el-form ref="addFormRule" :model="addForm" :data="addForm" :rules="addFormRule" label-position="left" label-width="100px">
                <div class="row dfb">
                    <div class="col">
                        <el-form-item label="竞价标题：" prop="name">
                            <el-input v-model="addForm.name" placeholder="请输入竞价标题"></el-input>
                        </el-form-item>
                    </div>
                </div>
                <div class="row dfb">
                    <div class="col">
                        <el-form-item label="竞价类型：" prop="demandType">
                            <el-select v-model="addForm.demandType"  placeholder="竞价类型">
                                <el-option v-for="item in demandTypeFilter" :key="item.value" :label="item.label"
                                           :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div class="row dfb" >
                    <div class="col">
                        <el-form-item label="开始时间：" prop="enterDate">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="addForm.enterDate"
                                type="datetime"
                                placeholder="请输入开始时间"
                                align="right"
                                :picker-options="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </div>
                    <div class="col">
                        <el-form-item label="结束时间：" prop="stopDate">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="addForm.stopDate"
                                type="datetime"
                                placeholder="请输入结束时间"
                                align="right"
                                :picker-options="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </div>
                </div>
                <div class="list-title dfa mb20">供应商列表
                    <span class="pointer" @click="$router.push('/user/publishDemand/publish')">+选择供应商</span>
                </div>
                <el-table
                    ref="msgTable"
                    :data="list"
                    :header-row-style="{ fontSize: '16px',color: '#216EC6' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column prop="sn" label="供应商编号" width="">
                    </el-table-column>
                    <el-table-column prop="title" label="供应商名称" width="">
                    </el-table-column>
                </el-table>
                <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"
                            :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange">
                </pagination>
                <div class="list-title dfa mb20">商品列表
                    <span class="pointer" @click="$router.push('/user/publishDemand/publish')">+新增商品</span>
                </div>
                <el-table
                    ref="msgTable"
                    :data="list"
                    :header-row-style="{ fontSize: '16px',color: '#216EC6' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column prop="sn" label="商品编码" width="">
                    </el-table-column>
                    <el-table-column prop="title" label="商品名称" width="">
                    </el-table-column>
                    <el-table-column prop="time" label="规格型号" width="">
                    </el-table-column>
                    <el-table-column prop="sn" label="计量单位" width="">
                    </el-table-column>
                    <el-table-column prop="sn" label="数量" width="">
                    </el-table-column>
                    <el-table-column prop="sn" label="操作" width="">
                    </el-table-column>
                </el-table>
            </el-form>
        </div>
        <el-dialog v-dialogDrag v-loading="brandTableLoading"  title="选择品牌" :visible.sync="showBrandDialog"  width="70%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div class="e-table"  style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input clearable type="text" @blur="getBrandTableData" placeholder="输入搜索关键字" v-model="brand.keywords">
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getBrandTableData" />
                        </el-input>
                    </div>
                </div>
                <el-table ref="tableRef"
                          highlight-current-row
                          border
                          style="width: 100%"
                          :data="brand.tableData"
                          class="table"
                          @row-click="handleCurrentClick"
                          :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="name" label="品牌名" width="200"></el-table-column>
                    <el-table-column prop="descript" label="介绍" width=""></el-table-column>
<!--                    <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>-->
<!--                    <el-table-column prop="gmtModified" label="更新时间" width="160"></el-table-column>-->
                </el-table>
            </div>
            <span slot="footer">
                <pagination
                    v-show="brand.tableData != null || brand.tableData.length != 0"
                    :total="brand.paginationInfo.total"
                    :pageSize.sync="brand.paginationInfo.pageSize"
                    :currentPage.sync="brand.paginationInfo.currentPage"
                    @currentChange="getBrandTableData"
                    @sizeChange="getBrandTableData"
                />
                <el-button style="margin-top: 20px" @click="showBrandDialog = false">取消</el-button>
            </span>

        </el-dialog>
        <div class="btn center" @click="onSubmit">提交</div>
    </main>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import { regionData, CodeToText, TextToCode } from 'element-china-area-data'
import pagination from '@/pages/frontStage/components/pagination.vue'
export default {
    components: { pagination, },
    name: 'detail',
    watch: {
        'addForm.demandType': {
            handler (newValue) {
                if(newValue == 2) {
                    this.isLease = false
                    // 二手
                    this.productType = 4
                }else {
                    this.isLease = true
                    this.productType = 5
                }
            },
        },
    },
    data () {
        return {
            pagination: {
                currPage: 1, //当前页
                destination: null,
                pageSize: 10, // 显示数量
                totalNum: 0,
                totalPage: 1,
            },
            isLease: false,
            addressData: regionData,
            pickerOptions: {
                disabledDate (v) {
                    return v.getTime() < new Date().getTime() - 86400000//  - 86400000是否包括当天
                }
            },
            numUnitOptions: [],
            showBrandDialog: false,
            // 品牌数据
            brand: {
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            brandTableLoading: false,
            productType: 4,
            demandTypeFilter: [
                { value: 2, label: '邀请竞价' },
            ],
            addForm: {
                name: null,
                spec: null,
                demandType: 2,
                brandName: null,
                brandId: null,
                classId: null,
                classPath: [],
                num: null,
                numUnit: null,
                duration: null,
                budgetAmount: null,
                linkman: null,
                linkmanPhone: null,
                enterDate: null,
                address: null,
                stopDate: null,
            },
            selectAddressOptions: [],
            addFormRule: {
                name: [
                    { required: true, message: '请输入名称', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                // spec: [
                //     { required: true, message: '请输入规格型号', trigger: 'blur' },
                //     { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                // ],
                demandType: [
                    { required: true, message: '请选择需求类型', trigger: 'blur' },
                ],
                // brandName: [
                //     { required: true, message: '请选择品牌', trigger: 'blur' },
                // ],
                classId: [
                    { required: true, message: '请选择分类', trigger: 'blur' },
                ],
                num: [
                    { required: true, message: '请输入数量', trigger: 'blur' },
                    { pattern: /^[1-9]\d*$/, message: '数据格式错误', trigger: 'blur' },
                ],
                numUnit: [
                    { required: true, message: '请选择计量单位', trigger: 'blur' },
                ],
                duration: [
                    { required: true, message: '请输入租赁时长', trigger: 'blur' },
                    { min: 1, max: 20, message: '超出范围', trigger: 'blur' }
                ],
                budgetAmount: [
                    { required: true, message: '请输入预算金额', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                linkman: [
                    { required: true, message: '请输入联系人', trigger: 'blur' },
                    { min: 1, max: 4, message: '超出范围', trigger: 'blur' }
                ],
                linkmanPhone: [
                    { required: true, message: '请输入联系电话', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                address: [
                    { required: true, message: '请选择工作地址', trigger: 'blur' },
                ],
                enterDate: [
                    { required: true, message: '请输入进场时间', trigger: 'blur' },
                ],
                stopDate: [
                    { required: true, message: '请输入结束时间', trigger: 'blur' },
                ],
            }
        }
    },
    created () {
        this.getParamsByCodeM()
    },
    methods: {
        getParamsByCodeM () {},
        // 品牌单选
        handleCurrentClick (row) {
            this.addForm.brandId  = row.brandId
            this.addForm.brandName = row.name
            this.showBrandDialog = false
        },
        // 地区更改触发
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.addForm.province = province
            this.addForm.city = city
            this.addForm.county = county
            this.addForm.address = province + city + county
        },
        // 计量单位选择
        numUnitChange (value) {
            this.addForm.numUnit = value
        },
        // 获取品牌表格
        getBrandTableData () {
            let params = {
                page: this.brand.paginationInfo.currentPage,
                limit: this.brand.paginationInfo.pageSize,
            }
            if(this.brand.keywords != null) {
                params.name = this.brand.keywords
            }
        },
        brandDialog () {
            this.showBrandDialog = true
            this.getBrandTableData()
        },
        // 初始化新增表单数据
        restartData () {
            this.addForm =  {
                name: null,
                spec: null,
                demandType: 2,
                brandName: null,
                brandId: null,
                classId: null,
                classPath: [],
                num: null,
                numUnit: null,
                duration: null,
                budgetAmount: null,
                linkman: null,
                linkmanPhone: null,
                enterDate: null,
                address: null,
                stopDate: null,
            }
        },
        onSubmit () {
            this.$refs['addFormRule'].validate(valid => {
                if(valid) {
                    let newClassPath = ''
                    this.addForm.classPath.forEach(t => {
                        newClassPath += t + '/'
                    })
                    this.addForm.classPath = newClassPath.substring(0, newClassPath.length - 1)
                }
            })
        },
    }
}
</script>

<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);
main {
    min-height: 894px;
    padding: 0 20px;
    border: $border;
}
.list-title {
  height: 50px;
  padding: 15px 19px 15px 21px;
  font-size: 20px;
  line-height: 20px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);
  position: relative;

  &::before {
    width: 3px;
    height: 20px;
    margin-right: 10px;
    content: '';
    display: block;
    background-color: rgba(33, 110, 198, 1);
  }
}
.list-title {
    padding: 0;
}

.publishBox {
    margin: 60px 60px 20px;

    .row {
        margin-bottom: 7px;
        color: rgba(51, 51, 51, 1);
    }
}

.btn {
    width: 160px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 22px;
    color: #fff;
    background-color: rgba(33, 110, 198, 1);
    user-select: none;
}

/deep/ .el-form {
    .el-form-item__label {
        height: 50px;
        line-height: 50px;
        text-align: right;
    }

    .el-input, .el-input__inner {
        width: 350px;
        height: 50px;
        border-radius: 0;
    }

    .el-input__inner {
        font-size: 16px;
        border: $border;
    }

    .long .el-input__inner {
        width: 853px;
    }
}
.pointer {
    font-size: 14px;
    color: rgba(33, 110, 198, 1);
    position: absolute;
    right: 20px;
}
</style>