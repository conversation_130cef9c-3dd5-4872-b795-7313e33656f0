<template>
    <div class="base-page">
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="goAdd">新增缴费</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="0">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按修改时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按审核时间排序</el-radio>
                        <el-input v-model="keywords" clearable placeholder="输入搜索关键字" style="width: 300px" type="text" @blur="handleInputSearch">
                            <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    ref="tableRef"
                    v-loading="tableLoading"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                    class="table"
                    @selection-change="tableSelectM"
                    @row-click="tableRowClickM"
                >
<!--                    <el-table-column type="selection" width="40"></el-table-column>-->
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="审核状态" class-name="" width="80">
                        <template v-slot="scope">
                            <span >{{ ["未缴费","待审核","审核通过","审核未通过）"][scope.row.state]}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="缴费金额（元）" prop="payAmount" width="">
                        <template v-slot="scope">
                            <span style="color: red">{{scope.row.payAmount}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="发票申请" width="">
                        <template>
                            <span >/</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="有效期开始日期" prop="serveStartTime" width="160"/>
                    <el-table-column label="续费截止日期（有效期截止日期）" prop="serveEndTime" width="160"/>
                    <el-table-column label="提交人" prop="founderName" class-name="" width="160" />
                    <el-table-column label="提交时间" prop="gmtCreate" class-name="" width="160" />
                    <el-table-column label="审核时间" prop="auditOpenTime" class-name="" width="160" />
                </el-table>
            </div>
            <Pagination
                v-show="tableData && tableData.length > 0"
                :currentPage.sync="paginationInfo.currentPage"
                :pageSize.sync="paginationInfo.pageSize"
                :total="paginationInfo.total"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="续费截止日期：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.serveEndTime"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>

        <el-dialog
            v-loading="showAddFeeLoading" class="addDia" top="5vh" title="新增缴费 " v-dialogDrag :visible.sync="showAddFee" width="70%">
            <el-form label-width="140px"  ref="showAddFeeRef"  :data="addFeeForm"  :rules="showAddFeeRoles">
                <el-row>
                    <el-col :span="24">
                        <span style="color: red">请用对公账户打款到四川路桥建设集团物资有限责任公司对公账号，并将缴费凭证截图在此处上传</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户名" prop="tot">
                            <span style="color: red">{{ platformFreeyhOrgName }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户开户行" prop="tot">
                            <span style="color: red">{{ platformFreeyhAddress }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户" prop="tot">
                            <span style="color: red">{{ platformFreeyhAccount }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收款金额" prop="tot">
                            <span style="color: red">{{ totalFee }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费方式：" prop="payType">
                            <el-radio v-model="addFeeForm.payType" :label="1" >线下</el-radio>
                            <el-radio v-model="addFeeForm.payType" disabled :label="2" >线上</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="缴费时长：">
                            <el-select class="ml10" v-model="addFeeForm.years">
                                <el-option :key="1" label="1年"
                                        :value="1">
                                </el-option>
                                <el-option :key="2" label="2年"
                                        :value="2">
                                </el-option>
                                <el-option :key="3" label="3年"
                                        :value="3">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item  label="缴费证明：" prop="file">
                            <el-upload
                                :class="addFeeForm.files.length === 1 ? 'hide_box_min' : ''"
                                v-loading="uploadLoading"
                                class="upload-demo"
                                action="fakeaction"
                                :limit="1"
                                :file-list="addFeeForm.files"
                                :before-upload="handleBeforeUpload"
                                :auto-upload="true"
                                :http-request="uploadPmtSlip"
                                list-type="picture-card">
                                <div slot="tip" class="el-upload__tip">只能上传图片文件</div>
                                <i slot="default" class="el-icon-plus"></i>
                                <div slot="file" slot-scope="{file}">
                                    <img
                                        class="el-upload-list__item-thumbnail"
                                        :src="file.url" alt="">
                                    <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="formDtlFileRemove(file)">
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </span>
                                </div>
                            </el-upload>
                            </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input
                                type="textarea"
                                :auto-resize="false"
                                v-model="addFeeForm.remarks"
                                placeholder="请输入备注" maxlength="1000"
                                show-word-limit
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button @click="showAddFee = false">暂不缴纳</el-button>
                <el-button type="primary" class="btn-greenYellow"  @click="save(1)">提交</el-button>
            </span>
        </el-dialog>
        <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { debounce, toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { myQueryListByEntity, supplierCreateOrUpdateFee, getRelevanceFile, supplierFetchSystemParams } from '@/api/fee/feeApi'
import { generatePdfThumbnailDataUrl } from '@/utils/pdfUtils'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { mapState } from 'vuex'
import moment from 'moment'
import Decimal from 'decimal.js'
import { toPreviewableFile } from '../dealPayRecord/index.vue'

const getSysValue = ( systemParams, key) => {
    if (!systemParams) {
        return '加载中'
    }
    const systemParam = systemParams.find(s => s.code == key)
    if (!systemParam) {
        return '加载失败'
    }
    return systemParam.keyValue
}

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState([ 'userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        platformFreeyhAddress () { // 平台收费银行开户行
            return getSysValue(this.systemParams, 'platformFreeyhAddress')
        },
        platformFreeyhAccount () { // 平台收费银行账号
            return getSysValue(this.systemParams, 'platformFreeyhAccount')
        },
        platformFreeyhOrgName () { // 平台收费公司名称
            return getSysValue(this.systemParams, 'platformFreeyhOrgName')
        },
        platformShopYearFeeAmount () { // 年费数额
            return getSysValue(this.systemParams, 'platformShopYearFeeAmount')
        },
        totalFee () {
            return new Decimal(this.platformShopYearFeeAmount).mul(new Decimal(this.addFeeForm.years)).toFixed(2)
        }
    },
    data () {
        return {
            dialogImageUrl: '',
            dialogVisible: false,
            freeAmount: 2000,
            showAddFee: false,
            showAddFeeLoading: false,
            addFeeForm: {
                payType: 1,
                years: 1,
                files: [],
                remarks: null,
            },
            showAddFeeRoles: {
                file: [
                    { required: true, message: '请输入证明', trigger: 'blur' },
                ],
            },
            tableSelectRow: [], // 多选框选择的数据
            tableLoading: false, // 加载
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            uploadImgSize: 10, // 上传文件大小
            tableData: [], // 表格数据
            filterData: { // 高级搜索
                serveEndTime: [],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            uploadLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            nextStartDate: undefined,
            systemParams: null,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        supplierFetchSystemParams().then( res => {
            this.systemParams = res
        })
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleDownload () {
            this.uploadLoading = true
            let image = this.addFeeForm.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        save () {
            if (this.addFeeForm.files.length == 0) {
                return this.$message.error('请上传缴费证明！')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.showAddFeeLoading = true
                // eslint-disable-next-line no-unused-vars
                const files = this.addFeeForm.files.map(({ url, ...rest }) => rest)
                const payload = {
                    paymentDuration: this.addFeeForm.years,
                    remarks: this.addFeeForm.remarks,
                    files
                }
                supplierCreateOrUpdateFee(payload).then(res => {
                    if (res.code == 200) {
                        this.$message.success('操作成功')
                        this.showAddFee = false
                        this.getTableData()
                    }
                }).finally(() => {
                    this.showAddFeeLoading = false
                })
            })
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        formDtlFileRemove () {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.addFeeForm.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.addFeeForm.files = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // 上传缴费凭证
        uploadPmtSlip (params) {
            const file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 3)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.addFeeForm.files = []
                }else {
                    let resO = res[0]
                    generatePdfThumbnailDataUrl(file).then(url => {
                        this.addFeeForm.files.push({
                            name: file.name,
                            relevanceType: 1,
                            url,
                            fileFarId: resO.recordId,
                            fileType: 1
                        })
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // 多选框
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        // 行点击
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        //重置数据
        resetSearchConditions () {
            this.filterData.serveEndTime = []
        },
        // 高级搜索
        confirmSearch () {
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            params.state = this.filterData.state
            if (this.filterData.serveEndTime != null) {
                params.startserveEndTime = this.filterData.serveEndTime[0]
                params.endserveEndTime = this.filterData.serveEndTime[1]
            }
            this.tableLoading = true
            myQueryListByEntity(params).then(res => {
                this.paginationInfo.total = res.total
                this.paginationInfo.pageSize = res.size
                this.paginationInfo.currentPage = res.current
                this.tableData = res.records.map(r=>{
                    return { ...r, file: [], fileLoading: true }
                })
                // TODO 图片预览和图片不存在的情形 详情时间预览 考虑改为前端分页
                this.tableData.forEach(r=>{
                    getRelevanceFile(1, r.paymentRecordId).then(async f=>{
                        const file = await toPreviewableFile(f.fileFarId, f.name)
                        r.file = { ...file }
                        r.fileLoading = false
                    })
                })
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
        resetAddFeeForm () {
            this.addFeeForm = {
                payType: 1,
                years: 1,
                files: [],
                remarks: null,
            }
        },
        goAdd () {
            this.showAddFee = true
            if (this.tableData == 0) {
                this.nextStartDate = moment()
                this.resetAddFeeForm()
                return
            }
            // TODO 考虑到这里有分页，应该从后端获取
            const latest = this.tableData[0]
            if (latest.state == 2) {
                this.nextStartDate = moment(latest.serveStartTime, 'YYYYMMDD').add(latest.serveStartTime.paymentDuration, 'years')
                this.addFeeForm.years = 1
                this.resetAddFeeForm()
            }else {
                this.nextStartDate = moment(latest.serveStartTime, 'YYYYMMDD')
                this.addFeeForm.years = latest.years
                this.addFeeForm = {
                    payType: 1,
                    years: latest.paymentDuration,
                    files: [ {
                        name: '',
                        relevanceType: 1,
                        url: latest.file.url,
                        fileFarId: latest.file.fileFarId,
                        fileType: 1
                    } ],
                    remarks: latest.remarks,
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
    appearance: textfield !important;
    -moz-appearance: textfield !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
/deep/ .addDia {
    .el-dialog__body {
        height: 600px;
    }
}

/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>
