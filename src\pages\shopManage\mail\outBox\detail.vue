<template>
  <div class="e-form">
    <BillTop @cancel="handleClose"></BillTop>
    <div class="tabs warningTabs" style="padding-top: 70px;">
      <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
        <el-tab-pane label="基本信息" name="baseInfo" :disabled="clickTabFlag">
        </el-tab-pane>
        <el-tab-pane label="收件人信息" name="receive" :disabled="clickTabFlag" v-if="formData.checkState != 2">
        </el-tab-pane>
        <div id="tabs-content">
          <div id="baseInfCon" class="con">
            <div class="tabs-title" id="baseInfo">基本信息</div>
            <div style="width: 100%" class="form">
              <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="发件人名称：">
                      <span>{{ formData.sendName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="发件人类型：">
                      <span v-show="formData.sendType==0">店铺</span>
                      <span v-show="formData.sendType==1">用户</span>
                      <span v-show="formData.sendType==2">平台</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="标题:">
                      <span>{{formData.title }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="消息类型：">
                      <span>{{ formData.messageType }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="发送时间：">
                      <span>{{ formData.sendDate }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="消息内容：" prop="productDescribe">
                      <editor  v-model="formData.content" :disabled ='true'></editor>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <div id="receiveCon" class="con" v-if="formData.checkState != 2">
            <div class="tabs-title" id="receive">收件人信息</div>
            <div class="e-table"  style="background-color: #fff">
              <div class="top" style="height: 50px; padding-left: 10px">
                <div class="left">
                  <div class="left-btn">
                    <el-select @change="getReceiveList" v-model="isRead" placeholder="请阅读状态">
                      <el-option v-for="item in isReads" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="search_box">
                  <el-input clearable type="text" @blur="getReceiveList" placeholder="输入搜索关键字" v-model="receive.keywords">
                    <img :src="require('@/assets/search.png')" slot="suffix" @click="getReceiveList" />
                  </el-input>
                </div>
              </div>
              <el-table ref="tableRef"
                        border
                        style="width: 100%"
                        :data="receive.tableData"
                        class="table"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="receiveName" label="收件人名称" width=""></el-table-column>
                <el-table-column prop="receiveCode" label="收件人编号" width=""></el-table-column>
                <el-table-column label="收件人类型" width="">-->
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.receiveType==0">店铺</el-tag>
                    <el-tag v-else-if="scope.row.receiveType==1" >用户</el-tag>
                    <el-tag v-else type="danger">已关闭</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="是否已读" width="">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.isRead==0">未读</el-tag>
                    <el-tag v-else-if="scope.row.isRead==1" >已读</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="gmtCreate" label="接收时间" width="160"></el-table-column>
                <!--                                <el-table-column prop="gmtModified" label="更新时间" width="160"></el-table-column>-->
              </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="receive.tableData != null || receive.tableData.length != 0"
                :total="receive.paginationInfo.totalCount"
                :pageSize.sync="receive.paginationInfo.pageSize"
                :currentPage.sync="receive.paginationInfo.currentPage"
                @currentChange="getReceiveList"
                @sizeChange="getReceiveList"
            />
          </div>
        </div>
      </el-tabs>
    </div>
    <div class="buttons">
      <el-button @click="handleClose">返回</el-button>
    </div>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { mapState, mapMutations } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
import editor from '../../../../components/quillEditor'
import { receivePageList } from '@/api/platform/mail/outbox'

export default {

    data () {
        return {
            //基本信息表单数据
            formData: {},
            // 表格数据
            isRead: null,
            receive: {
                type: 1,
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    totalCount: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            receiveTypes: [
                {
                    value: 1,
                    label: '用户'
                },
                {
                    value: 0,
                    label: '店铺'
                }
            ],
            isReads: [
                {
                    value: 0,
                    label: '未读'
                },
                {
                    value: 1,
                    label: '已读'
                }],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    components: {
        Pagination,
        editor
    },
    created () {
        this.formData = this.$route.params.row

        this.getReceiveList()
    },
    mounted () {
    // 获取数据
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'receive']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
    // 获取收件人信息
        getReceiveList () {
            let params = {
                page: this.receive.paginationInfo.currentPage,
                limit: this.receive.paginationInfo.pageSize,
                stationMessageId: this.formData.stationMessageId
            }
            if(this.receive.isReads != null) {
                params.isRead = this.isRead
            }
            if(this.receive.keywords != null) {
                params.keywords = this.receive.keywords
            }
            receivePageList(params).then(res => {
                this.receive.tableData = res.list
                this.receive.paginationInfo = res
            })
        },

        //取消
        handleClose () {
            this.$router.replace('/supplierSys/mail/outBox')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
              document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
  box-sizing: border-box;
  width: 100%;
  height: 40px;
  line-height: 40px;
  background: rgb(246, 246, 246);
  border: 1px solid rgb(236, 236, 236);
  margin: auto;
  margin-bottom: 15px;
  padding-left: 10px;
}

.separ {
  width: 30px;
  height: 40px;
  line-height: 18px;
  text-align: center;
}

.e-table {
  min-height: auto;
  background: #fff;
}

.upload {
  margin: 20px auto;
  display: flex;
  justify-content: center;
  text-align: center;
}

.upload-demo {
  display: flex;
  justify-content: center;
  align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-tabs__content {
  // overflow: hidden;
  &::-webkit-scrollbar {width: 0;}
}
</style>
