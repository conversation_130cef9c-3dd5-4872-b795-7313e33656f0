<template>
    <div v-if="showContent"><slot></slot></div>
</template>
<script>
import { mapState } from 'vuex'
export default {
    name: 'authComponent',
    props: {
        permission: {
            type: String,
            default: '',
        },
        orgName: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            showContent: true,
        }
    },
    computed: {
        ...mapState(['userInfo']),
        permissions () {
            let permissions = {}
            let permissionKeys = Object.keys(this.userInfo).filter(key => key[0] === 'i') // 获取权限字段属性名
            permissionKeys.forEach(key => permissions[key] = this.userInfo[key])
            return permissions // 返回用户信息的权限信息对象
        },
        isCorrectOrg () {
            return this.orgName === this.userInfo.orgName
        },
    },
    created () {
        let permissionKeys = Object.keys(this.permissions) // 获取权限字段属性名
        let { roles } = this.userInfo

        let hasPermission = permissionKeys.includes(this.permission) ? Boolean(this.permissions[this.permission]) : roles.includes(this.permission)
        if(this.permission && this.orgName) { // 传入权限字段和机构名称时
            this.showContent = hasPermission && this.isCorrectOrg
            return
        }

        if(this.permission && !this.orgName) { // 仅传入权限字段时
            this.showContent = hasPermission
            return
        }

        if(this.orgName && !this.permission) { // 仅传入机构名称时
            this.showContent = this.isCorrectOrg
        }

    },
}
</script>

<style scoped>

</style>