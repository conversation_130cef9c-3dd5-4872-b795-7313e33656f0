import service from '@/utils/request'

const { httpPost, httpGet } = service
//  列表查询
const getList = params => {
    return httpPost({
        url: '/materialMall/invoice/listByEntity',
        params
    })
}

const findById = params => {
    return httpGet({
        url: '/materialMall/invoice/findById',
        params
    })
}
const getInvoiceInfo = params => {
    return httpGet({
        url: '/materialMall/invoice/getData',
        params
    })
}
// 更新
const edit = params => {
    return httpPost({
        url: '/materialMall/invoice/update',
        params
    })
}
/**
 * 项目部申请票
 * @param params
 * @returns {*}
 */
// 新增
const create = params => {
    return httpPost({
        url: '/materialMall/invoice/create',
        params
    })
}

/**
 *
 * 供应商开票
 * @param params
 * @returns {*}
 */
const supplierCreate = params => {
    return httpPost({
        url: '/materialMall/invoice/supplier/create',
        params
    })
}

const twoEnterpriseCreate = params => {
    return httpPost({
        url: '/materialMall/invoice/twoEnterpriseCreate/create',
        params
    })
}

const twoSupplierCreate = params => {
    return httpPost({
        url: '/materialMall/invoice/twoSupplierCreate/create',
        params
    })
}
// 删除
const del = params => {
    return httpGet({
        url: '/materialMall/invoice/delete',
        params
    })
}

// 批量删除
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/invoice/deleteBatch',
        params
    })
}

const updateState = params => {
    return httpPost({
        url: '/materialMall/invoice/updateState',
        params
    })
}
const updateInvoiceState = params => {
    return httpPost({
        url: '/materialMall/invoice/changInvoiceState',
        params
    })
}
const update = params => {
    return httpPost({
        url: '/materialMall/invoice/update',
        params
    })
}
const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/invoice/deleteBatch',
        params
    })
}
const batchPublish = params => {
    return httpPost({
        url: '/materialMall/invoice/updateByPublish',
        params
    })
}
//das
const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/invoice/updateNotPublish',
        params
    })
}
/**
 * 根据二级对账单id查询可生成发票数据
 * @param params
 * @returns {*}
 */
const listBySupplierIdIds = params => {
    return httpPost({
        url: '/materialMall/invoice/listBySupplierIdIds',
        params
    })
}
export {
    getInvoiceInfo,
    listBySupplierIdIds,
    findById,
    getList,
    supplierCreate,
    twoEnterpriseCreate,
    twoSupplierCreate,
    edit,
    create,
    del,
    batchDelete,
    changeSortValue,
    batchPublish,
    batchNotPublish,
    updateState,
    update,
    updateInvoiceState
}
