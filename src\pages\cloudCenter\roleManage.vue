<template>
    <div class="content df">
        <section class="table-view p20">
            <div class="top-row mb10 p10 dfb">
                <div>
                    <el-button type="info"><i class="el-icon-refresh" @click="getList"></i></el-button>
                    <el-button type="primary" @click="openSave">新增</el-button>
                    <el-button type="primary" @click="changeSortValue">批量修改排序值</el-button>
                    <!--                    <el-button type="danger">批量删除</el-button>-->
                    <el-select class="ml10" @change="stateTopOptionsClick(stateOptionTitle)" v-model="stateOptionTitle"
                               placeholder="请选择平台">
                        <el-option v-for="item in stateOptions" :key="item.value" :label="item.label"
                                   :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div class="dfa">
                    <el-input clearable type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字"
                              v-model="keywords">
                        <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                    </el-input>
                    <div class="adverse">
                        <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                    </div>
                </div>
            </div>
            <el-table
                :data="tableData"
                row-key="id"
                v-loading="tableLoading"
                @row-click="handleRowClick"
                @selection-change="handleSelectionChange"
                :height="tableHeight"
                highlight-current-row
            >
                <el-table-column type="selection"></el-table-column>
                <el-table-column label="角色名称" prop="name"></el-table-column>
                <el-table-column label="角色编号" prop="code"></el-table-column>

                <el-table-column label="状态">
                    <template v-slot="scope">
                        <el-switch
                            v-model="scope.row.state"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            :active-value="1"
                            :inactive-value="0"
                            @change="updateStateM(scope.row)"/>
                    </template>
                </el-table-column>
                <el-table-column label="查看权限" prop="orgSearch">
                    <template v-slot="scope">
                        <el-tag v-if="scope.row.orgSearch==1">本机及子级</el-tag>
                        <el-tag v-if="scope.row.orgSearch==2">本级</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="排序值" width="120" type="index">
                    <template v-slot="scope">
                        <el-input type="number" v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="描述" prop="remarks"></el-table-column>
                <el-table-column label="创建时间" prop="gmtCreate"></el-table-column>
                <el-table-column label="修改时间" prop="gmtModified"></el-table-column>
                <el-table-column label="操作">
                    <template v-slot="scope">
                        <el-button type="primary" @click="editRow(scope.row)"><i class="el-icon-edit-outline"></i>
                        </el-button>
                        <el-popconfirm
                            class="ml10"
                            confirm-button-text="确定"
                            cancel-button-text="取消"
                            icon="el-icon-info"
                            icon-color="red"
                            title="确定删除角色吗？"
                            @confirm="delRow(scope.row)"
                        >
                            <el-button slot="reference" type="danger"><i class="el-icon-delete"></i></el-button>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                layout="prev, pager, next"
                :current-page.sync="paginationInfo.currentPage"
                :total="paginationInfo.total"
                :page-size="20"
                @currentChange="getList"
                @sizeChange="getList"
            />
        </section>
        <section class="tree-view p20" :style="{height:screenHeight-60+'px'}">
            <div class="mb20">功能权限（{{ roleName || '未选择角色' }}）</div>
            <div class="mb20">
                <el-button type="success" @click="getCheckedIds">保存</el-button>
                <el-button type="primary" @click="toggleSelectAll">全选/取消全选</el-button>
                <el-button class="ml10" @click="toggleTreeExpansion">展开/收起</el-button>
            </div>
<!--            <el-input-->
<!--                placeholder="输入关键字进行过滤"-->
<!--                v-model="filterNode">-->
<!--            </el-input>-->
            <!--                check-strictly-->
            <el-tree
                ref="tree"
                :props="treeProps"
                :data="treeData"
                node-key="menuId"
                default-expand-all
                show-checkbox
                :check-strictly="checkStrictly"
            />
            <!--                @check-change="handleCheckChange"-->
        </section>
        <el-dialog :title="title" top="10vh" :visible.sync="editDialogVisible">
            <el-form label-position="left" label-width="80px">
                <el-row>
                    <el-form-item label="角色名称：">
                        <el-input v-model="formData.name"></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="角色编号：">
                        <el-input v-model="formData.code"></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="状态：">
                        <el-switch
                            v-model="formData.state"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            :active-value='1'
                            :inactive-value='0'
                        ></el-switch>
                    </el-form-item>
                </el-row>
                <el-row >
                    <el-col>
                    <el-form-item label="查看权限：">
                            <el-radio v-model="formData.orgSearch" :label=1 border>本机及子级</el-radio>
                            <el-radio v-model="formData.orgSearch" :label=2 border>本级</el-radio>
                    </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-form-item label="排序：">
                        <el-input-number v-model="formData.sort"/>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="规则备注：">
                        <el-input type="textarea" v-model="formData.remarks"/>
                    </el-form-item>
                </el-row>
                <el-row>
                    <!--                    <el-form-item label="平台：">-->
                    <!--                        <el-checkbox-group v-model="formData.platform">-->
                    <!--                            <el-checkbox label="采购平台"></el-checkbox>-->
                    <!--                            <el-checkbox label="履约平台"></el-checkbox>-->
                    <!--                        </el-checkbox-group>-->
                    <!--                    </el-form-item>-->
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="saveAndupdateM()">确定</el-button>
                <el-button @click="editDialogVisible = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="false">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="角色名称：">
                            <el-input v-model="filterData.name" placeholder="请输入角色名称" clearable/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="角色编号：">
                            <el-input v-model="filterData.code" placeholder="请输入角色编号" clearable/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { save, update, getDateList, deleteById, updateState, saveMenuAndSysRole, getMenuAndSysRoleDateList, changeSortValue } from '@/api/cloudCenter/sysRole'
import { getMenuDateList } from '@/api/cloudCenter/menu'
export default {
    name: 'roleManage',
    data () {
        return {
            checkStrictly: true,
            tableLoading: false,
            editDialogVisible: false,
            stateOptionTitle: 1,
            checkedKeys: [],
            changedRow: [],
            roleId: null,
            roleName: null,
            queryVisible: false,
            stateOptions: [
                {
                    value: 1,
                    label: '后台管理平台'
                }, {
                    value: 2,
                    label: '供应商管理平台'
                }, {
                    value: 3,
                    label: '履约管理管理平台'
                }, {
                    value: 4,
                    label: '自营店供应商'

                }
            ],
            title: '',
            keywords: null,
            view: 'add',
            tableData: [],
            menus: [],
            menuIds: [],
            paginationInfo: { // 分页
                total: 20,
                pageSize: 20,
                currentPage: 1,
            },
            filterData: {
                name: '',
                code: '',
                categoryType: '',
            },
            formData: {
                name: '',
                code: '',
                categoryType: '',
                state: 1,
                orgSearch: 1
            },
            page: {
                currPage: 1,
                totalCount: 20
            },
            treeProps: {
                key: 'menuId',
                label: 'title',
                children: 'children',
            },
            treeData: [],
            selectedRows: [],
            screenHeight: 0,
            flatNodeObj: [],
        }
    },
    computed: {
        tableHeight () {
            return this.screenHeight - 220
        },
        nodesLen () {
            return this.getChildObj(this.treeData).length
        },
    },
    methods: {
        toggleSelectAll () {
            let checkedNodes = this.$refs['tree'].getCheckedNodes(false, true)
            let nodesToCheck = []
            if(checkedNodes.length !== this.nodesLen) {
                nodesToCheck = this.flatNodeObj
            }
            this.$refs['tree'].setCheckedNodes(nodesToCheck)
        },
        getChildObj (list) {
            let arr = []
            list.map(item => {
                arr.push(item)
                if(item.children && item.children.length > 0) {
                    arr.push(...item.children)
                    item.children.forEach(subItem => {
                        if(subItem.children && subItem.children.length > 0) arr.push(...subItem.children)
                    })
                }
            })
            return arr
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getList()
        },
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        saveAndupdateM () {
            this.formData.categoryType = this.stateOptionTitle
            if (this.view === 'add') {
                this.formData.roleId = ''
                save(this.formData).then(res => {
                    if (res.code == 200) {
                        this.$message.success('保存成功')
                        this.editDialogVisible = false

                    }
                }).finally(() => {
                    this.tableLoading = false
                    this.editDialogVisible = false
                    this.getList()
                }
                )
            } else {
                this.clientPop('info', '您确定要修改该菜单吗？', async () => {
                    update(this.formData).then(res => {
                        if (res.code === 200) {
                            this.$message.success('修改成功')
                        }
                    }).finally(() => {
                        this.editDialogVisible = false
                        this.getList()
                        this.tableLoading = false
                    }
                    )
                })

            }
        },
        getChangedRow (row) {
            if(!this.changedRow[0]) {
                return this.changedRow.push({ roleId: row.roleId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if(item.roleId === row.roleId) {
                    return i
                }
            })
            if(arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ roleId: row.roleId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                this.$message.info('当前没有排序值被修改')
                return
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('修改成功')
                        this.getList()
                    }
                })
            })
        },

        openSave () {
            this.editDialogVisible = true
            this.title = '新增角色'
            this.view = 'add'
        },
        confirmSearch () {
            this.keywords = null
            this.getList()
            this.queryVisible = false
        },
        resetSearchConditions () {
            this.filterData.name = null
            this.filterData.code = null
        },
        getList () {
            // this.tableLoading = true
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                categoryType: this.stateOptionTitle
            }
            if (this.filterData.name != null && this.filterData.name != '') {
                params.name = this.filterData.name
            }
            if (this.keywords != null && this.filterData.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.code != null && this.filterData.code != '') {
                params.code = this.filterData.code
            }
            if (this.filterData.state != null && this.filterData.state != '') {
                params.state = this.filterData.state
            }
            getDateList(params).then(res => {
                this.tableData = res.list
                this.tableLoading = false
            })
            // this.tableData = [
            //     { id: 1, name: 'Alex', code: '495884920', tel: '325232', gender: 0, active: true, changeTime: '2023-12-12 10:00:00',
            //     },
            //     { id: 2, name: 'Mary', code: '495884920', tel: '325232', gender: 1, active: false, changeTime: 'null' }
            // ]
            // setTimeout(() => { this.tableLoading = false }, 200)
        },
        getTreeData () {
            // this.treeData = [{ id: 1, label: '采购平台', children: [{ id: 11, label: '下单' }, { id: 12, label: '大宗' }] }]
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                classCode: this.stateOptionTitle
            }
            this.tableLoading = true
            getMenuDateList(params).then(res => {
                this.treeData = res
                this.flatNodeObj = this.getChildObj(res)
                this.tableLoading = false
            })
        },
        editRow (row) {
            this.editDialogVisible = true
            this.formData = row
            this.title = '编辑角色'
            this.view = 'update'
        },

        handleRowClick (row) {
            this.checkStrictly = true
            let checkedNodes = this.$refs['tree'].getCheckedNodes(false, true)
            checkedNodes.forEach(key => this.$refs['tree'].setChecked(key, false))
            getMenuAndSysRoleDateList({ roleId: row.roleId }).then(res => {
                this.menuIds = res
                this.$refs['tree'].setCheckedKeys(res)
                this.roleId = row.roleId
                this.roleName = row.name
                this.checkStrictly = false
            })
        },
        //修改订单状态
        updateStateM (row) {
            let params = {
                id: row.menuId,
                state: row.state
            }
            updateState(params).then(res => {
                if (res.code == 200) {
                    this.$message.success('修改状态成功')
                    this.getList()
                }
            })
        },
        delRow (row) {
            this.clientPop('info', `您确定要删除该${row.name}吗？`, async () => {
                this.tableLoading = true
                deleteById({ id: row.roleId }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('删除成功')
                    } else {
                        this.$message.warning('删除失败')
                    }

                }).finally(() => {
                    this.getList()
                    this.tableLoading = false
                })
            })
        },
        stateTopOptionsClick (stateOptionTitle) {
            this.filterData.categoryType = stateOptionTitle
            this.getList()
            this.getTreeData()

        },
        toggleTreeExpansion () {
            this.treeData.forEach(item => {
                this.$refs.tree.store.nodesMap[item.menuId].expanded = !this.$refs.tree.store.nodesMap[item.menuId].expanded
            })
        },
        getCheckedIds () {
            if (this.roleId) {
                let checkedKeys = this.$refs['tree'].getCheckedKeys()
                let halfCheckedKeys = this.$refs['tree'].getHalfCheckedKeys()
                let params = {
                    roleId: this.roleId,
                    menusIds: checkedKeys.concat(halfCheckedKeys)
                }
                saveMenuAndSysRole(params).then(res=>{
                    if (res.code === 200) {
                        this.$message.success('操作成功')
                    }
                })
            }else {
                this.$message.warning('请点击角色进行添加权限')
            }

        },
        // handleCheckChange (node) {
        //     if (this.selectedRows.length > 0) { // 判断条件，如果条件不满足，则取消勾选操作
        //         this.$nextTick(() => {
        //             node.checked = true // 将节点的选中状态设置为 false
        //         })
        //         return
        //     }else {
        //         this.getTreeData()
        //         this.$message.warning('请勾选角色进行添加权限')
        //     }
        // },
        handleCheck (node, nodeObj) {
            if (this.roleId == null || this.roleId == '') {
                // this.$message.warning('请选择左侧角色进行添加权限')
                nodeObj.checkedKeys = []
            }else {
                console.log('check changeupdate')
                this.menuIds = nodeObj.checkedKeys
            }
        },
        // filterNode (value, data) {
        //     if (!value) return true
        //     return data.label.indexOf(value) !== -1
        // },
        // eslint-disable-next-line
        handleTreeCheckChange(data, isSelected, hasSelectedChild) {
            // if (isSelected) {
            //     this.menuIds.push(data.menuId)
            // }else {
            //     this.menuIds = this.menuIds.filter(item=>{  item !== data.meunId})
            // }
            // console.log(this.menuIds, isSelected)
        },
        getScreenSize () {
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        },
    },
    created () {
        this.getList()
        this.getTreeData()
    },
    mounted () {
        this.getScreenSize()
        window.addEventListener('resize', () => this.getScreenSize())
    },
    beforeDestroy () {
        window.removeEventListener('resize', () => this.getScreenSize())
    },
}
</script>

<style scoped lang="scss">
.top-row {
    //height: 50px;
    padding: 10px;
    background-color: #fff;
}

.table-view {
    flex-grow: 1;
}

.tree-view {
    width: 400px;
    background-color: #fff;
    box-shadow: 0 0.7px 2.2px -15px rgba(0, 0, 0, 0.028),
    0 1.7px 5.3px -15px rgba(0, 0, 0, 0.04),
    0 3.3px 10px -15px rgba(0, 0, 0, 0.05),
    0 5.8px 17.9px -15px rgba(0, 0, 0, 0.06),
    0 10.9px 33.4px -15px rgba(0, 0, 0, 0.072),
    0 26px 80px -15px rgba(0, 0, 0, 0.1);
    overflow: auto;
}

/deep/ .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>