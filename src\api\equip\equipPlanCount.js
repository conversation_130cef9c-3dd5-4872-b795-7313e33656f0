const count = {
    //===================配置计划  计算==============================
    //计划明细  计算金额 = 需求数量*预计使用时间*单价  保留两位小数
    onAmount  (quantity, serviceTime, price) {
        let sum = 0
        sum = Number(quantity * serviceTime * price).toFixed(2)
        return parseFloat(sum)
    },
    //长租计划金额 = 明细获取金额向上汇总 保留两位小数
    onUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.amount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //变更后长租计划金额 = 明细获取金额(变)向上汇总 保留两位小数
    onChangeUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.state !== -1) {
                sum += +v.changeAmount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //总计划金额 = 金额的汇总数据+临租总金额
    onPlanAmount (tableData, tempLeaseAmount) {
        let sum = 0
        sum = +count.onUpCollect(tableData) + +tempLeaseAmount
        return parseFloat(Number(sum).toFixed(2))
    },
    //变更后总计划金额 = 金额的汇总数据+变更后临租总金额
    onChangePlanAmount (tableData, tempLeaseAmount) {
        let sum = 0
        sum = +count.onChangeUpCollect(tableData) + +tempLeaseAmount
        return parseFloat(Number(sum).toFixed(2))
    },
    //计划金额（本位币）=总计划金额+本位币汇率
    onScSumCost (planAmount, baseCurRate) {
        let sum = 0
        sum = Number(+planAmount * +baseCurRate).toFixed(2)
        return parseFloat(sum)
    },
    //===================租赁计划  计算==============================
    //计划明细 金额 = 数量*单价*预计使用时间+预计进退场费  保留两位小数
    onLeaseAmount  (quantity, price, serviceTime, enterExitCost) {
        if(enterExitCost == undefined) {
            enterExitCost = 0
        }
        let sum = 0
        sum = Number(+quantity * +serviceTime * +price + +enterExitCost).toFixed(2)
        return parseFloat(sum)
    },
    //费用合计 = 明细获取金额向上汇总 保留两位小数
    onLeaseUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.amount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //计划明细 变更后金额 = 变更后数量*变更后单价*变更后预计使用时间+变更后预计进退场费  保留两位小数
    onChangeLeaseAmount  (quantity, price, serviceTime, enterExitCost) {
        if(enterExitCost == undefined) {
            enterExitCost = 0
        }
        if(serviceTime == undefined) {
            serviceTime = 0
        }
        let sum = 0
        sum = Number(+quantity * +serviceTime * +price + +enterExitCost).toFixed(2)
        return parseFloat(sum)
    },
    //费用合计 = 明细获取金额向上汇总 保留两位小数
    onChangeLeaseUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.state !== -1) {
                sum += +v.changeAmount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //===================年度采购预算登记  计算==============================
    //计划明细 金额 = 数量*单价  保留两位小数
    onBudgetYearAmount  (quantity, price) {
        let sum = 0
        sum = Number(+quantity * +price ).toFixed(2)
        return parseFloat(sum)
    },
    //预算总金额 = 明细获取金额向上汇总 保留两位小数
    onBudgetYearUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.amount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //计划明细变更 金额 = 数量*单价  保留两位小数
    onChangeBudgetYearAmount  (quantity, price) {
        let sum = 0
        sum = Number(+quantity * +price ).toFixed(2)
        return parseFloat(sum)
    },
    //预算总金额变更 = 明细获取金额向上汇总 保留两位小数
    onChangeBudgetYearUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.state !== -1) {
                sum += +v.changeAmount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //===================采购计划  计算==============================
    //计划明细 金额 = 数量*单价  保留两位小数
    onPlanBuyAmount  (quantity, price) {
        let sum = 0
        sum = Number(+quantity * +price ).toFixed(2)
        return parseFloat(sum)
    },
    //费用合计 = 明细获取金额向上汇总 保留两位小数
    onPlanBuyUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.amount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //计划明细变更 金额 = 数量*单价  保留两位小数
    onChangePlanBuyAmount (quantity, price) {
        let sum = 0
        sum = Number(+quantity * +price ).toFixed(2)
        return parseFloat(sum)
    },
    //变更后费用合计 = 明细获取金额向上汇总 保留两位小数
    onChangePlanBuyUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.state !== -1) {
                sum += +v.changeAmount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //===================大修计划  计算==============================
    //计划维修费用 = 明细获取金额向上汇总 保留两位小数
    onMajorUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.repairCost
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //计划维修费用 = 明细获取金额向上汇总 保留两位小数
    onMajorUpCollectChange (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.state !== -1) {
                sum += +v.changeRepairCost
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
}
export default count
