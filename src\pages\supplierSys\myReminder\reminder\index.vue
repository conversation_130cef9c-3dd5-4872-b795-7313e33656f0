<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" >
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn">
              <el-select v-model="filterData.isRead"  @change="getDateList" placeholder="请选择消息状态">
                <el-option v-for="item in isReads" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
<!--              <el-select v-model="filterData.messageType"  @change="getDateList" placeholder="消息类型">-->
<!--                <el-option v-for="item in messageTypes" :key="item.value" :label="item.label" :value="item.value">-->
<!--                </el-option>-->
<!--              </el-select>-->
            </div>
          </div>
          <div class="search_box">
            <el-input type="text" @keyup.enter.native="getDateList" placeholder="标题" v-model="keywords"><img src="@/assets/search.png"
                                                                                                                          slot="suffix" @click="getDateList" /></el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" >
        <el-table @row-click="handleCurrentInventoryClick2" ref="eltableCurrentRow2" class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="" prop="sendTime">
            <template slot-scope="scope">
                            <span class="action" @click="delById(scope.row)"><img src="../../../../assets/btn/delete.png"
                                                                                  alt=""></span>
            </template>
          </el-table-column>
          <el-table-column label="标题" width="" prop="title">
            <template slot-scope="scope">
                            <span class="action" @click="toDetail(scope.row)">
                                {{ scope.row.title }}
                            </span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="" >
            <template slot-scope="scope">
              <span v-if="scope.row.isRead === 0" >未读</span>
              <span v-if="scope.row.isRead === 1" >已读</span>
            </template>
          </el-table-column>
          <el-table-column label="类型" width="" >
            <template slot-scope="scope">
              <span v-if="scope.row.messageType === 0" >中标通知</span>
              <span v-if="scope.row.messageType === 1" >其他</span>
            </template>
          </el-table-column>
          <el-table-column label="发送时间" width="" prop="sendTime">
          </el-table-column>

        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                     @currentChange="currentChange" @sizeChange="sizeChange" />
    </div>
    <!-- ----------------查询弹框---------------- -->
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="true" :before-close="closeDialog">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
        <el-row>
          <el-col :span="18" >
            <el-form-item label="消息状态：">
              <el-select v-model="filterData.isRead"  >
                <el-option v-for="item in isReads" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
<!--        <el-row>-->
<!--          <el-col :span="12" >-->
<!--            <el-form-item label="消息类型：">-->
<!--              <el-select v-model="filterData.messageType"  >-->
<!--                <el-option v-for="item in messageTypes" :key="item.value" :label="item.label" :value="item.value">-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
        <el-row>
          <el-col :span="12" >
            <el-form-item label="消息标题：" >
              <el-input clearable  v-model="filterData.messageType" placeholder="消息标题" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="发送时间：">
              <el-date-picker
                  value-format="yyyy-MM-dd"
                  v-model="filterData.sendTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getTenderMsgList, del } from '@/api/supplierSys/myReminder/reminder/tenderMsg'
import { debounce } from '@/utils/common'
import { mapActions, mapState } from 'vuex'

export default {
    components: {
        ComPagination
    },
    watch: {

    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            screenHeight: 0,
            screenWidth: 0,
            queryVisible: false,
            keywords: '',
            currentRow: null,
            selectedRows: [],
            pages: {
                totalCount: 10,
                currPage: 1,
                pageSize: 20,
            },
            isReads: [
                {
                    value: null,
                    label: '全部'
                },
                {
                    value: 0,
                    label: '未读'
                },
                {
                    value: 1,
                    label: '已读'
                },
            ],
            messageTypes: [
                {
                    value: null,
                    label: '全部'
                },
                {
                    value: 0,
                    label: '中标通知'
                },

            ],
            // 高级查询数据对象
            filterData: {
                messageType: null,
                isRead: null,
                sendTime: [],
                title: ''

            },
            tableData: [],
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        this.getDateList()
        this.getParams()
    },
    methods: {
        delById (from) {
            this.clientPop('info', '您确定要删除该' + from.title + '吗？', async () => {
                del({ id: from.messageId }).then(res => {
                    if (res.code === 200) {
                        this.$message.success('删除成功')
                        this.getDateList()
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                })})
        },
        toDetail (row) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/myReminder/reminderDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'reminderDetail',
                params: {
                    messageId: row.messageId
                }
            })
        },
        getDateList () {
            if (this.userInfo.isExternal === 1) {
                let params = {
                    limit: this.pages.pageSize,
                    page: this.pages.currPage,
                    mallType: 0
                }

                if( this.filterData.isRead != null) {
                    params.isRead = this.filterData.isRead
                }
                if (this.filterData.sendTime != 0) {
                    params.startTime = this.filterData.sendTime[0]
                    params.endTime = this.filterData.sendTime[1]
                }
                if (this.filterData.messageType != '' && this.filterData.messageType != null) {
                    params.messageType = this.filterData.messageType
                }

                if (this.filterData.title != '' && this.filterData.title != null) {
                    params.title = this.filterData.title
                }
                if (this.filterData.messageType != '' && this.filterData.messageType != null) {
                    params.messageType = this.filterData.messageType
                }
                if (this.keywords != '' && this.keywords != null) {
                    params.keywords = this.keywords
                }
                getTenderMsgList(params).then(res=>{

                    this.tableData = res.list
                    this.pages = res
                })
            }

        },
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                isRead: null,
                messageType: '',
                sendTime: [],
                title: ''
            },
            done()
        },
        hideDialog () {
            this.filterData = {
                isRead: null,
                messageType: '',

                sendTime: [],
                title: ''
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            this.keywords = null
            this.getDateList()
            this.queryVisible = false
            this.filterData = {
                isRead: null,
                messageType: '',
                title: '',
                sendTime: [],
            }
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleView (scope) {
            this.formData = scope.row
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/bidManage/bidManageDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'bidManageDetail',
                params: {
                    row: scope.row
                }
            })
        },

        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },

        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}
.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}

</style>
