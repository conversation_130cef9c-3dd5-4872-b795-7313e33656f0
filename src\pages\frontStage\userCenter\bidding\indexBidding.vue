<template>
    <main class="userCenter">
        <div class="list-title dfa mb20">竞价采购
            <span class="pointer" @click="$router.push('/user/publishBidding')">+发布竞价</span>
        </div>
        <el-table
                ref="msgTable"
                :data="list"
                style="min-height: 472px"
                :header-row-style="{ fontSize: '16px',color: '#216EC6' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
        >
            <el-table-column prop="sn" label="竞价编号" width="">
            </el-table-column>
            <el-table-column prop="title" label="竞价标题" width="">
            </el-table-column>
            <el-table-column prop="time" label="竞价截止时间" width="">
            </el-table-column>
            <el-table-column prop="type" label="竞价方式" width="">
            </el-table-column>
            <el-table-column prop="state" label="竞价状态" width="">
            </el-table-column>
            <el-table-column prop="" label="操作" width="">
                <template slot-scope="scope">
                    <div class="pointer" style="color: rgba(33, 110, 198, 1);"
                         @click="handleViewDetail(scope.row.demandId)">查看详情
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination"
                    :pageSize="pagination.pageSize"
                    :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange"
                    @sizeChange="sizeChange">
        </pagination>
    </main>
</template>

<script>
import pagination from '@/pages/frontStage/components/pagination.vue'

export default {
    components: { pagination },
    name: 'index',
    data () {
        return {
            pagination: {
                currPage: 1,
                destination: null,
                pageSize: 10,
                totalNum: 0,
                totalPage: 1,
            },
            list: [
                {
                    sn: '2342424n2o4h',
                    title: '五金材料采购',
                    time: '2023-6-20 00:00:00',
                    type: '公开竞价',
                    state: '进行中',
                }
            ],
        }
    },
    created () {
        // this.getUserDemandPageListM()
    },
    mounted () {
    },
    methods: {
        currentChange (index) {
            this.pagination.currPage = index
            this.getUserDemandPageListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getUserDemandPageListM()
        },
        getUserDemandPageListM () {
        },
        // 跳转详情
        handleViewDetail (id) {
            this.openWindowTab({ path: '/user/detailBidding', query: { id, } })
        },
    },
}
</script>

<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);
main {
  padding: 0 20px;
  border: $border;
}

.list-title {
  height: 50px;
  padding: 15px 19px 15px 21px;
  font-size: 20px;
  line-height: 20px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);
  position: relative;

  &::before {
    width: 3px;
    height: 20px;
    margin-right: 10px;
    content: '';
    display: block;
    background-color: rgba(33, 110, 198, 1);
  }
}

.list-title {
  padding: 0;

  .pointer {
    font-size: 14px;
    color: rgba(33, 110, 198, 1);
    position: absolute;
    right: 20px;
  }
}

.list {
  .item {
    height: 52px;
    padding: 0 20px;
    font-size: 14px;
    border: $border;
    color: rgba(102, 102, 102, 1);
    position: relative;

    & > div:not(.pointer) {
      margin-right: 60px;
    }

    .pointer {
      color: rgba(33, 110, 198, 1);
      position: absolute;
      right: 20px;
    }

    span {
      color: rgba(51, 51, 51, 1);
    }
  }
}
</style>