import service from '@/utils/request'

const { httpPost, httpGet } = service

const getShopList = params => {
    return httpPost({
        url: '/materialMall/platform/shop/findByConditionByPageNot',
        params
    })
}
const getList = params => {
    return httpPost({
        url: '/materialMall/platform/shop/findByConditionByPageNot',
        params
    })
}
const edit = params => {
    return httpPost({
        url: '/materialMall/platform/shop/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/shop/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/shop/delete',
        params
    })
}

const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/shop/deleteBatch',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateByPublish',
        params
    })
}

const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateNotPublish',
        params
    })
}
const getShopPassList = params => {
    return httpPost({
        url: '/materialMall/platform/shop/findByConditionByPage',
        params
    })
}

export {
    getList,
    getShopList,
    create,
    edit,
    del,
    batchPublish,
    batchNotPublish,
    batchDelete,
    getShopPassList
}
