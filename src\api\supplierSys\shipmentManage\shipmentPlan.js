import service from '@/utils/request'

const { httpPost, httpGet } = service

const monthList = params => {
    return httpPost({
        url: '/supplierSys/monthSendPlan/listByEntity',
        params
    })
}
const monthplanDtlList = params => {
    return httpPost({
        url: '/supplierSys/monthPlanDtl/listByEntity',
        params
    })
}
const saveMonthSendDtl = params => {
    return httpPost({
        url: '/supplierSys/monthSendPlanDtl/create',
        params
    })
}
const createBatch = params => {
    return httpGet({
        url: '/supplierSys/monthSendPlanDtl/createBatchByBillId',
        params
    })
}

export {
    monthList,
    monthplanDtlList,
    saveMonthSendDtl,
    createBatch

}