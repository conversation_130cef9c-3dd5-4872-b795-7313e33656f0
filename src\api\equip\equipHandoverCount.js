const count = {
    //===================装备调动  计算==============================
    //税额 = 租赁金额（不含税）*税率
    onTaxAmount (amount, tax) {
        let sum = 0
        sum = Number(amount * (tax / 100)).toFixed(2)
        return parseFloat(sum)
    },
    //租赁金额(含税) = 租赁金额（不含税）+税额
    onLeaseAmount (amount, taxAmount) {
        let sum = 0
        sum = Number(amount + taxAmount).toFixed(2)
        return parseFloat(sum)
    },
    //租赁金额（不含税） = 明细获取金额向上汇总 保留两位小数
    onUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.leaseAmount
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //调动明细  租赁金额 = 租赁时间*租金单价（元）+进场费（元）+退场费（元）+操作手工资（元） 保留两位小数
    onAmount (leaseTerm, leasePrice, enterPrice, exitPrice, operatingSalary) {
        let sum = 0
        sum = Number(leaseTerm * leasePrice + enterPrice + exitPrice + operatingSalary).toFixed(2)
        return parseFloat(sum)
    }
}
export default count
