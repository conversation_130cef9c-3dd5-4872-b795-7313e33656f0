import service from '@/utils/request'

const { httpPost, httpGet } = service

const getShopList = params => {
    return httpPost({
        url: '/materialMall/platform/shop/findByConditionByPage',
        params
    })
}
const findTwoSupplierShopByPage = params => {
    return httpPost({
        url: '/materialMall/platform/shop/findTwoSupplierShopByPage',
        params
    })
}
//下载导入店铺模板
const shopTemplate = params => {
    return httpGet({
        url: '/materialMall/platform/shop/excel/template',
        params,
        responseType: 'blob'
    })
}

const shopArrearageExcelFile = params => {
    return httpPost({
        url: '/materialMall/platform/shop/shopArrearageExcelFile',
        params,
    })
}

const stopShopStateExcelFile = params => {
    return httpPost({
        url: '/materialMall/platform/shop/stopShopStateExcelFile',
        params,
    })
}

const updateBatchArrearage = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateBatchArrearage',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/platform/shop/update',
        params
    })
}

const freezeShop = params => {
    return httpPost({
        url: '/materialMall/platform/shop/freezeShop',
        params
    })
}

const updateBatchIsOrderAuth = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateBatchIsOrderAuth',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/shop/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/shop/delete',
        params
    })
}

const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/shop/deleteBatch',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateByPublish',
        params
    })
}

const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateNotPublish',
        params
    })
}

const getEnterpriseInfo = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/findById',
        params
    })
}

const getEnterpriseInfoTaxRate = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/getEnterpriseInfoTaxRate',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateBatchById',
        params
    })
}
const changeIsOrderAuth = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateBatchById',
        params
    })
}
const changePayDays = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateBatchById',
        params
    })
}
const getInfo = params => {
    params.mallType = 0
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/getInfo',
        params
    })
}
const updateBatchAuditStatus = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateBatchAuditStatus',
        params
    })
}

export {
    getShopList,
    shopTemplate,
    updateBatchArrearage,
    getEnterpriseInfoTaxRate,
    create,
    edit,
    freezeShop,
    updateBatchIsOrderAuth,
    del,
    batchPublish,
    stopShopStateExcelFile,
    shopArrearageExcelFile,
    batchNotPublish,
    batchDelete,
    getEnterpriseInfo,
    changeSortValue,
    changeIsOrderAuth,
    changePayDays,
    getInfo,
    updateBatchAuditStatus,
    findTwoSupplierShopByPage
}
