<template>
    <main>
        <div class="list-title mb20 df">修改个人认证</div>
        <div class="content">
            <el-form :model="individualForm" ref="form" :rules="rules" label-width="128px" :inline="false">
                <!-- 身份证上传 -->
                <div class="row dfb">
                        <el-form-item label="身份证人像面照：" prop="cardPortraitFace">
                            <el-upload class="identityUpload face" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1)"
                                       :show-file-list="false">
                                <img class="identityUpload" v-if="individualForm.cardPortraitFace" :src="individualForm.cardPortraitFace"
                                     alt="">
                                <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">
                            </el-upload>
                        </el-form-item>
                    <el-form-item label="身份证国徽面照：" prop="cardPortraitNationalEmblem">
                        <el-upload class="identityUpload badge" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2)"
                                   :show-file-list="false">
                            <img class="identityUpload" v-if="individualForm.cardPortraitNationalEmblem" :src="individualForm.cardPortraitNationalEmblem"
                                 alt="">
                            <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                        </el-upload>
                    </el-form-item>
                </div>
                <div class="row dfb">
                    <el-col :span="11" :offset="0">
                        <el-form-item label="姓名：" prop="adminName">
                            <el-input clearable v-model="individualForm.adminName" placeholder="请输入姓名"></el-input>
                        </el-form-item>
                    </el-col>
                </div>
                <div class="row dfb">
                    <el-col :span="11" :offset="0">
                        <el-form-item label="身份证号码：" prop="adminNumber">
                            <el-input clearable v-model="individualForm.adminNumber" placeholder="请输入身份证号"></el-input>
                        </el-form-item>
                    </el-col>
                </div>
<!--                <div class="row dfb">-->
<!--                    <el-col :span="11" :offset="0">-->
<!--                        <el-form-item label="手机号码：" prop="adminPhone">-->
<!--                            <el-input clearable v-model="individualForm.adminPhone" placeholder="请输入手机号码"></el-input>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                </div>-->
            </el-form>
            <div class="btns center dfb">
                <button @click="$router.go(-1)">返回</button>
                <button @click="onSubmit">提交</button>
            </div>
        </div>
    </main>
</template>
<script>
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { getEnterpriseAuthInfo, updateEnterprise } from '@/api/frontStage/verification'

export default {
    data () {
        return {
            individualForm: {
                cardPortraitFace: null,
                cardPortraitNationalEmblem: null,
                adminName: null,
                adminNumber: null,
            },
            rules: {
                cardPortraitFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                cardPortraitNationalEmblem: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                // adminPhone: [
                //     { required: true, message: '请输入11位手机号', trigger: 'blur' },
                //     { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                // ],
                adminName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                adminNumber: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
            }
        }
    },
    created () {
        this.getEnterpriseAuthInfoM()
    },
    mounted () { },
    methods: {
        // 获取企业信息
        getEnterpriseAuthInfoM () {
            getEnterpriseAuthInfo({}).then(res => {
                this.individualForm = res
                if(this.individualForm.cardPortraitFaceId != null) {
                    previewFile({ recordId: this.individualForm.cardPortraitFaceId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.individualForm.cardPortraitFace = url
                    })
                }
                if(this.individualForm.cardPortraitNationalEmblemId != null) {
                    previewFile({ recordId: this.individualForm.cardPortraitNationalEmblemId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.individualForm.cardPortraitNationalEmblem = url
                    })
                }

            })
        },
        onSubmit () {
            this.$refs['form'].validate(valid => {
                if(valid) {
                    updateEnterprise(this.individualForm).then(res => {
                        if(res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            })
                            this.$router.push('/user/verification/detail?type=individual')
                        }else {
                            this.$message({
                                message: '修改失败',
                                type: 'error'
                            })
                        }
                    })
                }
            })
        },
        // 上传身份证
        uploadIdentity (params, num) {
            if(num === 1 ) {
                let file = params.file
                const form = new FormData()
                form.append('files', file)
                form.append('bucketName', 'mall-private') //存储桶名称
                form.append('directory', 'material') // 商城类型
                form.append('isChangeObjectName', true) // 是否修改文件名称
                form.append('isTemplate', false)  //是否是模板
                form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
                form.append('relationId', '990116') // 关联ID
                uploadFile(form).then(res => {
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.individualForm.cardPortraitFace = url
                    })
                    this.individualForm.cardPortraitFace = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                    this.individualForm.cardPortraitFaceId = res[0].recordId
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                })
            }
            if(num === 2 ) {
                let file = params.file
                const form = new FormData()
                form.append('files', file)
                form.append('bucketName', 'mall-private') //存储桶名称
                form.append('directory', 'material') // 商城类型
                form.append('isChangeObjectName', true) // 是否修改文件名称
                form.append('isTemplate', false)  //是否是模板
                form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
                form.append('relationId', '990116') // 关联ID
                uploadFile(form).then(res => {
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.individualForm.cardPortraitNationalEmblem = url
                    })
                    this.individualForm.cardPortraitNationalEmblem = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                    this.individualForm.cardPortraitNationalEmblemId = res[0].recordId
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                })
            }
        }
    },
}
</script>
<style scoped lang="scss">
.btns {
    width: 350px;
    margin-top: 25px;
    button {
        width: 160px;
        height: 50px;
        font-size: 22px;
    }
    button:first-child {
        color: rgba(33,110,198,1);;
        border: 1px solid rgba(33,110,198,1);
        background-color: #fff;
    }
    button:last-child {
        color: #fff;
        background-color: rgba(33,110,198,1);
    }
}
main {
    min-height: 570px;

    &>div {
        border: 1px solid rgba(230, 230, 230, 1);
    }
}

.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
.identityUpload {
    width: 160px;
    height: 100px;
}
.content {
    min-height: 530px;
    padding: 30px 31px;

    /deep/ .el-form {
        width: 610px;
        margin: 0 auto;
        .row {
            .el-form-item {margin-bottom: 25px;}
            .el-form-item__label {
                height: 50px;
                line-height: 50px;
                font-size: 16px;
                color: #333;
            }
            .el-input {
                font-size: 16px;
                width: 350px;

                .el-input__inner {
                    height: 50px;
                    width: 350px;
                }
            }
        }
        //.row:last-of-type {
        //    .el-input, .el-input__inner {width: 195px;margin-right: 15px;}
        //}
    }
}
</style>