<template>
    <div class="tree">
        <div class="search_box">
            <div class="name">类别名称</div>
            <el-input class="ipt" type="text"
                placeholder="输入搜索关键字"
                v-model="searchKey"
                @select="onSearch"
                @change="onSearch"
            ><img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
        </div>
        <el-tree
            class="scroll_bar"
            :style="{height: treeHeight}"
            :data="treeData"
            :props="defaultProps"
            :load="loadNode"
            @node-click="handleNodeClick"
            lazy
        >
            <template slot-scope="{ node }">
                <span :title="node.label">{{node.label}}</span>
            </template>
        </el-tree>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import { getEquipmentCategory } from '@/api/base/equip'
import { debounce } from '@/utils/common'
export default {
    data () {
        return {
            searchKey: '',
            treeData: [],
            defaultProps: {
                children: 'childList',
                isLeaf: 'isLeaf',
                label: 'categoryName'
            },
            screenWidth: 0,
            screenHeight: 0,
            params: {
                'categoryId': '00000',
                'keyword': ''
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        treeHeight () {
            return (this.screenHeight - 125) + 'px'
        },
    },
    created () {
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    methods: {
        loadNode (node, resolve) {
            const getTreeData = ()=>{
                getEquipmentCategory(this.params).then(data=>{
                    data.forEach(item=>item.isLeaf = !item.isLower)
                    resolve(data)
                })
            }
            if (node.level === 0) {
                getTreeData()
            }
            if (node.level >= 1) {
                this.params.categoryId = node.data.categoryId
                getTreeData()
            }
        },
        handleNodeClick (row) {
            this.$emit('select', row)
        },
        onSearch (e) {
            let searchKey = e
            if (typeof e === 'object') {
                searchKey = this.searchKey
            }
            console.log(searchKey)
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
    }
}
</script>

<style lang="scss" scoped>
    .tree{
        .search_box {
            position: fixed; left: 11px; top: 0; z-index: 2; padding: 10px 0 0; width: 246px; flex-direction: column; background: #fff;
        }
        .name {
            margin: 0 0 15px;
            width: 100%;
            height: 40px;
            line-height: 40px;
            text-align: center;
            font-weight: 700;
            background-color: rgb(224, 231, 249);
        }
        .el-tree{
            overflow-y: auto;
        }
        /deep/.el-tree-node>.el-tree-node__children {
            width: 800px;
            &::-webkit-scrollbar {
                height: 8px;
            }
            &:hover{
                &::-webkit-scrollbar-thumb {
                    background: rgba(204, 204, 204, 0.8);
                    &:hover{
                        background: rgba(204, 204, 204, 1);
                    }
                }
            }
        }
    }
</style>