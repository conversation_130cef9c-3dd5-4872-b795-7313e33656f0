<template>
    <div class="df">
        <div class="boxBottom">
            <div class="icon center"></div>
            <div class="msg1">提交成功</div>
            <div class="msg2">重新登陆即可以以{{title}}身份登录</div>
            <button class="center1" @click="$router.go(-1)">返回</button>
            <button class="center" @click="directTo('/login')">重新登陆</button>
        </div>
    </div>
</template>
<script>
export default {
    props: ['title'],
    data () {
        return {}
    },
    methods: {
        directTo (url) {
            localStorage.removeItem('token')
            this.$store.commit('setUserInfo', {})
            window.open(url)
        }
    }
}
</script>
<style scoped lang="scss">
.df {
    flex-direction: column;
}
.boxTop {
    height: 87px;
    border-bottom: 1px solid #D9D9D9;
    .title {
        width: 200px;
        height: 100%;
        font-size: 26px;
        font-weight: 500;
        line-height: 87px;
        text-align: center;
        border-bottom: 4px solid #216EC6;
        color: #333;
        user-select: none;
    }
}
.boxBottom {
    padding-bottom: 80px;
    text-align: center;
    flex-grow: 1;
    .icon {
        width: 100px;
        height: 100px;
        margin-top: 74px;
        background: url(../../../../assets/images/userCenter/zc_chenggong.png);
    }
    .msg1 {
        margin: 30px 0 15px 0;
        font-size: 22px;
        color: #333;
        text-align: center;
        font-weight: 400;
    }
    .msg2 {
        margin-bottom: 72px;
        font-size: 16px;
        text-align: center;
        color: #999;
    }
    .progress {
        width: 720px;
        margin-bottom: 80px;
        font-size: 14px;
        display: flex;
        .bar {margin-right: -4px;}
        .img {
            height: 30px;
            margin-bottom: 15px;
            font-size: 12px;
            line-height: 30px;
            color: #fff;
        }
        .bar:first-child {
            width: 244px;
            .img {background: url(../../../../assets/images/userCenter/step_1.png);}
            .text {color: #88B4E5;}
        }
        .bar:nth-child(2) {
            width: 240px;
            position: relative;
            .img {z-index: 1; background: url(../../../../assets/images/userCenter/step_2.png);}
            .text {color: #2A82E4;}
        }
        .bar:last-child {
            width: 240px;
            .img {background: url(../../../../assets/images/userCenter/step_3.png);}
            .text {color: #B3B3B3;}
        }
    }
    .center1{
        width: 150px;
        height: 50px;
        font-size: 22px;
        color: rgba(33,110,198,1);;
        border: 1px solid rgba(33,110,198,1);
        background-color: #fff;
        margin-right: 20px;
    }
    button {
        width: 150px;
        height: 50px;
        font-size: 22px;
        color: #fff;
        background-color: #216EC6;
        margin-left: 20px;
    }
}
</style>