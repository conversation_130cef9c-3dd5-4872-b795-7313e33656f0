<template>
    <div class="pagination-front dfa p20">
        <el-pagination
            @size-change="sizeChange"
            @current-change="currentChange"
            :current-page.sync="currPage"
            :page-size="pgSize"
            layout="prev, pager, next"
            :pager-count="pagerSize || 7"
            :total="dataLength">
        </el-pagination>
        <div class="page-btn">
            共<span>{{ totalPages }}</span>页
            到第<input v-model="destination" type="text">页
            <button @click="jumpToPage()">确定</button>
        </div>
    </div>
</template>
<script>
import { debounce } from '@/utils/common'

export default {
    props: ['pageSize', 'total', 'totalPage', 'destination', 'currentPage', 'pagerSize'],
    data () {
        return {
            pgSize: this.pageSize,
            dataLength: this.total,
            totalPages: Math.ceil(this.totalPage),
            currPage: this.currentPage,
            mark: this.destination
        }
    },
    watch: {
        pageSize (val) {
            this.pgSize = val
        },
        total (val) {
            this.dataLength = val
        },
        currentChange (val) {
            this.currPage = val
        },
        totalPage (val) {
            this.totalPages = Math.ceil(val)
        },
        destination (val) {
            this.mark = val
        }
    },
    computed: {},
    methods: {
        handler (event) {
            this.$emit(event)
        },
        currentChange (page) {
            this.currPage = parseInt(page)
            this.$emit('currentChange', parseInt(page))
            // this.fn('currentChange')
        },
        sizeChange (size) {
            this.pgSize = parseInt(size)
            this.$emit('sizeChange', parseInt(size))
        },
        jumpToPage () {
            this.currPage = this.mark
            this.currentChange(this.mark)
        },
    },
    created () {
        this.fn = debounce(this.handler)
    },
}
</script>
<style scoped lang="scss">
.pagination-front {
    height: 100px;
    justify-content: right;
    color: rgba(153, 153, 153, 1);
    font-size: 16px;

    span, button, input {
        font-size: 16px;
        color: rgba(0, 0, 0, 1);
    }

    input {
        width: 40px;
        height: 40px;
        margin: 0 5px;
        text-align: center;
        border: 1px solid rgba(230, 230, 230, 1);
    }

    button {
        width: 80px;
        height: 40px;
        font-size: 16px;
        border: 1px solid rgba(230, 230, 230, 1);
        background-color: #fff;
    }
}
</style>