
<template>
    <main class="userCenter" v-loading="planListLoading">
        <div class="list-title dfa mb20">
            {{$route.params.type | titleFilter}}
            <!-- <div class="filter-options">
                <el-radio-group v-model="filterState" @change="handleFilterChange">
                    <el-radio :label="'1'">待审核</el-radio>
                </el-radio-group>
            </div> -->
        </div>
        <div class="tabs mb20 dfb">
            <div class="tab df">
                <div :class="filterState == -100 ? 'active' : ''" @click="checkActiveTab(-100)">全部计划</div>
                <div :class="filterState == 0 ? 'active' : ''" @click="checkActiveTab(0)">待提交</div>
                <div :class="filterState == 1 ? 'active' : ''" @click="checkActiveTab(1)">待审核</div>
                <div :class="filterState == 2 ? 'active' : ''" @click="checkActiveTab(2)">已审核</div>
                <div :class="filterState == 4 ? 'active' : ''" @click="checkActiveTab(4)">审核不通过</div>
                <div :class="filterState == -1 ? 'active' : ''" @click="checkActiveTab(-1)">已作废</div>
            </div>
            <div class="search df" style="position: absolute;right: 0;">
                <div v-if="showDevFunc" class="mr30">
                    <!--                    数据权限-->
                    <el-select :value="dataSource" @change="dataSourceChange">
<!--                        <el-option label="本级及下级" :value="0"/>-->
                        <el-option label="本机构" :value="1"/>
                        <el-option label="本级及下级" :value="2"/>
<!--                        <el-option v-if="userPermission.hasSubOrg()" label="下级机构" :value="2"/>-->
                        <el-option
                            v-for="item in userPermission.subOrg"
                            :label="item.orgName" :value="item.orgId" :key="item.orgId"
                        />
                    </el-select>
                </div>
                <div class="box dfa">
                    <img src="@/assets/images/ico_search.png" alt="" />
                    <input v-model="keyword" type="text" placeholder="计划编号" />
                </div>
                <button @click="getPlanListM">搜索</button>
                <button @click="handleExportExcel">导出</button>
            </div>
        </div>
        <el-table border ref="msgTable" :data="list" style="min-height: 472px" :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
            :row-style="{ fontSize: '14px', height: '48px' }">
            <el-table-column label="序号" type="index" width="60" :index="indexMethod"></el-table-column>
            <el-table-column prop="billNo" label="计划编号" width="300"> </el-table-column>
            <el-table-column prop="year" label="计划年度" width="">
                <template v-slot="scope">
                    {{ getYearFromDate(scope.row.billDate) }}
                </template>
            </el-table-column>
            <el-table-column prop="month" label="计划月份" width="">
                <template v-slot="scope">
                    {{ getMonthFromDate(scope.row.billDate) }}
                </template>
            </el-table-column>
            <el-table-column prop="billDate" label="计划日期" width="">
                <template v-slot="scope">
                    {{ scope.row.billDate | dateStr }}
                </template>
            </el-table-column>
            <el-table-column prop="planAmount" label="计划金额" width=""> </el-table-column>
            <el-table-column v-if="showDevFunc" prop="orgName" label="机构信息" width=""> </el-table-column>
            <el-table-column label="计划状态" width="">
                <template v-slot="scope">
                    {{ scope.row.state | planStatusFilter }}
                </template>
            </el-table-column>
            <el-table-column prop="" label="操作" width="180">
                <template v-slot="scope">
                    <!-- 审核人操作 -->
                    <!-- 是审核人，是推送pcwp的计划，是本部门的经办人推送的计划 -->
                    <!-- TODO 这里审核人可能属于多个部门，这种场景下这个审核权限如何判定 -->
                    <div class="td_operate" v-if="isAuditor && !scope.row.pbillId && scope.row.orgId==userInfo.orgId">
                        <!-- 撤回的和作废的 可以被删除 -->
                        <!-- <div class="td_operateDiv"
                            v-if="scope.row.state === '0' || scope.row.state === '-1'"
                            @click="handleRemove(scope.row.billId)"
                        >删除</div> -->
                        <!-- 已提交的可以审核 -->
                        <div class="td_operateDiv"
                            v-if="scope.row.state === '1'"
                            @click="handleAutid(scope.row.billId)"
                        >通过</div>
                        <!-- <div class="td_operateDiv" v-if="planList_planReview" @click="handleAutid(scope.row.billId)">通过</div> -->
                        <!-- 已提交的可以审核 -->
                        <div class="td_operateDiv"
                            v-if="scope.row.state === '1'"
                            @click="goAutidFailed(scope.row.billId)"
                        >不通过</div>
                        <!-- <div class="td_operateDiv" v-if="planList_planReview" @click="goAutidFailed(scope.row.billId)">不通过</div> -->
                        <!-- 审核失败的可以作废 -->
                        <!-- <div class="td_operateDiv"
                            v-if="scope.row.state === '4'"
                            @click="handleCancel(scope.row.billId)"
                        >作废</div> -->
                    </div>
                    <!-- 经办人操作 -->
                    <!-- 是经办人，是推送pcwp的计划，是本人推送的计划 -->
                    <!-- <div class="td_operate" v-if="isHandler && !scope.row.pbillId && scope.row.founderId==userInfo.userId">
                        <div class="td_operateDiv"
                            v-if="scope.row.state === '0'"
                            @click="handleSubmit(scope.row.billId)"
                        >提交</div>
                        <div class="td_operateDiv"
                            v-if="scope.row.state === '1'"
                            @click="handleRevoke(scope.row.billId)"
                        >撤回</div>
                    </div> -->
                    <div class="td_operate">
                        <!-- <div class="td_operateDiv" @click="handleViewCheck(scope.row.billId)"
                        >审核</div> -->
                        <div class="td_operateDiv" @click="handleViewDetail(scope.row.billId)"
                        >查看详情</div>
                        <div class="td_operateDiv"
                            v-if="ordable(scope.row)"
                            @click="handleOneClickOrder(scope.row.billId)"
                        >一键下单</div> <!--审核通过的可以下单 -->
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"
            :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange">
        </pagination>
        <el-dialog
            width="30%"
            title="提示"
            :visible.sync="auditVisible"
            >
            <el-input
                type="textarea"
                autosize
                placeholder="请输入理由"
                v-model="reason">
            </el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="auditVisible = false">取消</el-button>
                <el-button type="primary" @click="handleAutidFailed">确认</el-button>
            </span>
        </el-dialog>
        <el-dialog
            width="30%"
            title="审核"
            :visible.sync="checkVisible"
            >
            <el-row style="margin-bottom: 20px;">
                <el-col :span="4">审核结果</el-col>
                <el-col :span="12">
                    <el-radio-group v-model="checkType">
                        <el-radio :label="1">通过</el-radio>
                        <el-radio :label="2">不通过</el-radio>
                        <el-radio :label="3">撤回</el-radio>
                    </el-radio-group>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="4">备注</el-col>
                <el-col :span="12">
                    <el-input class="textareaInput"
                        type="textarea"
                        placeholder="请输入理由"
                        v-model="checkReason">
                    </el-input>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="checkConfirm">确认</el-button>
                <el-button @click="checkCancel">取消</el-button>
            </span>
        </el-dialog>
    </main>
</template>

<script>
import pagination from '@/pages/frontStage/components/pagination.vue'

import { mapState } from 'vuex'
// import { getPCWP2Interface } from '@/api/plan/plan'
import { hasPermission } from '@/utils/permissionOperate'
import { getPlanList, deletePlanAndDetails, exportExcel, changePlanState } from '@/api/plan/plan'

import { UserPermission } from '@/utils/permissions'

const ordable = ( plan, userInfo) => {
    const isHandler = true
    // 非经办人 不能下单
    if (!isHandler) {
        return false
    }
    // 数据未加载完成 不能下单
    if (!plan.billId) {
        return false
    }
    // 非pcwp计划 不能下单
    if (!plan.pbillId) {
        return false
    }
    // 非本人上传的计划 不能下单
    if (plan.founderId !== userInfo.userId) {
        return false
    }
    // 是不已审核状态的 不能下单
    if (plan.state !== '2') {
        return false
    }
    return true
}
export { ordable }

function planStatusFilter (state) {
    // 将输入的状态转换为字符串，以确保与常量匹配
    const stateStr = String(state)

    switch (stateStr) {
    case '-1':
        return '已作废'
    case '0':
        return '待提交'
    case '1':
        return '待审核'
    case '2':
        return '已审核'
    case '3':
        return '已完成'
    case '4':
        return '审核不通过'
    default:
        return '未知状态' // 处理未知的状态码
    }
}
export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return dateStr.split(' ')[0]
        },
        planStatusFilter,
        titleFilter (type) {
            switch (type) {
            case '0':
                return '零星采购计划列表'
            case '1':
                return '大宗临购计划列表'
            case '2':
                return '周转材料计划列表'
            default:
                return ''
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        isHandler () {
            // TODO 还未实现的功能 暂时这样方便测试
            return true
        },
        isAuditor () {
            // TODO 还未实现的功能 暂时这样方便测试
            return true
        },
        type () {
            return this.$route.params.type
        }
        // isHandler () {
        //     return this.userInfo.roles.includes('计划-经办人')
        // },
        // isAuditor () {
        //     return this.userInfo.roles.includes('计划-审核人')
        // },
    },
    components: { pagination },
    name: 'index',
    data () {
        return {
            // perms权限
            planList_planReview: false,
            planList_planOrder: false,
            planList_planOneClickOrder: false,
            planList_planExport: false,
            planList_planDelete: false,
            planList_planRevoke: false,

            planListLoading: false,
            keyword: null,
            filterState: -100, // 修改初始值为false，表示未勾选
            pagination: {
                currPage: 1,
                destination: null,
                pageSize: 10,
                totalNum: 10,
                totalPage: 1,
            },
            list: [],
            userPermission: new UserPermission('物资下单权限'),
            dataSource: 1,
            showDevFunc: true, // 假设这个变量控制开发功能的显示
            auditVisible: false, // 审核框是否显示
            reason: '', // 审核不通过理由

            // 审核框数据
            checkVisible: false,
            checkReason: '',
            checkType: 1,
            checkBillId: null,
        }
    },
    created () {
        let type = this.$route.params.type
        let planObj = {
            '0': 'planSporadic',    //'零星采购计划列表'
            '1': 'planBulk',       //'大宗临购计划列表'
            '2': 'planTurnover',    //'周转材料计划列表'
        }
        if(type == '0' || type == '1' || type == '2') {
            this.planList_planReview = hasPermission('planList:' + planObj[type] + ':review', this.userInfo.perms || [])
            this.planList_planOrder = hasPermission('planList:' + planObj[type] + ':order', this.userInfo.perms || [])
            this.planList_planOneClickOrder = hasPermission('planList:' + planObj[type] + ':oneClickOrder', this.userInfo.perms || [])
            this.planList_planExport = hasPermission('planList:' + planObj[type] + ':export', this.userInfo.perms || [])
            this.planList_planDelete = hasPermission('planList:' + planObj[type] + ':delete', this.userInfo.perms || [])
            this.planList_planRevoke = hasPermission('planList:' + planObj[type] + ':revoke', this.userInfo.perms || [])
        }
        this.getPlanListM()
    },
    methods: {
        ordable (plan) {
            return ordable(plan, this.userInfo)
        },
        // 从日期字符串中提取年份
        getYearFromDate (dateStr) {
            if (!dateStr) return ''
            return new Date(dateStr).getFullYear()
        },
        // 从日期字符串中提取月份
        getMonthFromDate (dateStr) {
            if (!dateStr) return ''
            return new Date(dateStr).getMonth() + 1
        },
        dataSourceChange (state) {
            this.dataSource = state
            if (state === 0) {
                this.userPermission.getAllOrgData()
            } else if (state === 1) {
                this.userPermission.getHostOrgData()
            } else if (state === 2) {
                this.userPermission.getSubOrgData()
            } else if (state.length > 1) {
                this.userPermission.getSubOrgData(state)
            }
            this.getPlanListM()
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getPlanListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getPlanListM()
        },
        // handleFilterChange () {
        //     // 状态筛选变化时，重置页码为1
        //     this.pagination.currPage = 1
        //     this.getPlanListM()
        // },
        checkActiveTab (val) {
            this.filterState = val
            // 状态筛选变化时，重置页码为1
            this.pagination.currPage = 1
            this.getPlanListM()
        },
        getQueryConditions () {
            const params = {
                limit: this.pagination.pageSize,
                page: this.pagination.currPage,
                type: this.type, /*计划类型 0-零星采购计划 1-大宗临购 2-周转材料*/
            }
            // 如果选择了状态筛选，添加状态参数
            if (this.filterState != -100) {
                params.state = this.filterState
            }
            if (this.keyword != null) {
                params.billNo = this.keyword
            }
            return params

        },
        getPlanListM () {
            const params = this.getQueryConditions()
            this.planListLoading = true
            getPlanList(params)
                .then(res => {
                    this.list = res.list
                    this.pagination.totalPage = res.totalPage
                    this.pagination.totalNum = res.totalCount
                    this.pagination.currPage = res.currPage
                    // this.pagination.pageSize = res.pageSize
                    this.planListLoading = false
                })
                .catch(() => {
                    this.planListLoading = false
                })
        },
        // 审核
        handleViewCheck (billId) {
            this.checkVisible = true
            this.checkBillId = billId
        },
        // 审核-确认
        checkConfirm () {
            let billId = this.checkBillId
            if(this.checkType == 1) {
                this.clientPop('info', '您确定要通过此计划吗？', async () => {
                    changePlanState(billId, '2').then(res => {
                        if (res.code != 200) {
                            return
                        }
                        this.$message.success('审核成功')
                        this.checkVisible = false
                        this.getPlanListM()
                    })
                })
            }else if(this.checkType == 2) {
                if (!this.checkReason) {
                    this.$message.error('审核理由不能为空')
                    return
                }
                this.clientPop('info', '您确定要不通过此计划吗？', async () => {
                    const reason = this.checkReason
                    changePlanState(billId, '4', { reason }).then(res => {
                        if (res.code != 200) {
                            return
                        }
                        this.checkVisible = false
                        this.$message.success('驳回成功')
                        this.getPlanListM()
                    })
                })
            }else if(this.checkType == 3) {
                this.clientPop('info', '您确定要撤回此计划吗？', async () => {
                    changePlanState(billId, '0').then(res => {
                        if (res.code != 200) {
                            return
                        }
                        this.$message.success('撤回成功')
                        this.checkVisible = false
                        this.getPlanListM()
                    })
                })
            }
        },
        // 审核-取消
        checkCancel () {
            this.checkReason = ''
            this.checkBillId = null
            this.checkType = 1
            this.checkVisible = false
        },
        // 查看详情
        handleViewDetail (billId) {
            this.$router.push({ path: '/user/planDetail', query: { billId: billId } })
        },
        handleOneClickOrder (billId) {
            this.$router.push({ path: '/user/planDetail', query: { billId: billId, orderAll: true } })
        },
        handleRemove (billId) {
            this.clientPop('info', '您确定要删除此计划吗？', async () => {
                deletePlanAndDetails(billId).then(res=>{
                    if (res.code != 200) {
                        return
                    }
                    this.$message.success('删除成功')
                    this.getPlanListM()
                })
            })
        },
        async handleExportExcel () {
            const params = this.getQueryConditions()
            let fileRes = await exportExcel(params)
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = '采购计划列表.xlsx'
            a.click()
            window.URL.revokeObjectURL(url)
        },
        handleSubmit (billId) {
            changePlanState(billId, '1').then(res => {
                if (res.code != 200) {
                    return
                }
                this.$message.success('提交成功')
                this.getPlanListM()
            })
        },
        handleRevoke (billId) {
            this.clientPop('info', '您确定要撤回此计划吗？', async () => {
                changePlanState(billId, '0').then(res => {
                    if (res.code != 200) {
                        return
                    }
                    this.$message.success('撤回成功')
                    this.getPlanListM()
                })
            })
        },
        indexMethod (index) {
            return this.pagination.pageSize * (this.pagination.currPage - 1) + index + 1
        },
        handleAutid (billId) {
            this.clientPop('info', '您确定要通过此计划吗？', async () => {
                changePlanState(billId, '2').then(res => {
                    if (res.code != 200) {
                        return
                    }
                    this.$message.success('审核成功')
                    this.getPlanListM()
                })
            })
        },
        goAutidFailed (billId) {
            this.currentBillId = billId
            this.reason = ''
            this.auditVisible = true
        },
        handleAutidFailed () {
            if (!this.reason) {
                this.$message.error('审核理由不能为空')
                return
            }
            this.clientPop('info', '您确定要不通过此计划吗？', async () => {
                const reason = this.reason
                changePlanState(this.currentBillId, '4', { reason }).then(res => {
                    if (res.code != 200) {
                        return
                    }
                    this.auditVisible = false
                    this.$message.success('驳回成功')
                    this.getPlanListM()
                })
            })
        },
        handleCancel (billId) {
            changePlanState(billId, '-1').then(res => {
                if (res.code != 200) {
                    return
                }
                this.$message.success('作废成功')
                this.getPlanListM()
            })
        }
    },
}
</script>

<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);

main {
    padding: 0 20px;
    border: $border;
}

.list-title {
    padding: 0;
    position: relative;
    .filter-options {
        position: absolute;
        left: 180px; /* 增加距离，从150px调整到180px */
    }
    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}
.tabs {position: relative;}
.tab {
    font-size: 16px;
    color: rgba(102, 102, 102, 1);
    div {
    margin-right: 20px;
    cursor: pointer;
    }
    .active {
    color: rgba(0, 0, 0, 1);
    &::after {
        content: '';
        display: block;
        width: 100%;
        height: 2px;
        margin-top: 4px;
        background-color: rgba(34, 111, 199, 1);
    }
    }
}
.search {
    .box {
        width: 268px;
        height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);
        border-right: 0;

        img {
            width: 16px;
            height: 16px;
            margin: 0 4px 0 10px;
        }

        input {
            width: 230px;font-size: 13px;
        }
    }

    button {
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
    }
}

.list {
    .item {
        height: 52px;
        padding: 0 20px;
        font-size: 14px;
        border: $border;
        color: rgba(102, 102, 102, 1);
        position: relative;

        &>div:not(.pointer) {
            margin-right: 60px;
        }

        .pointer {
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 20px;
        }

        span {
            color: rgba(51, 51, 51, 1);
        }
    }
}
/deep/ .el-input--suffix .el-input__inner{
    height: 26px;
}

.action-buttons-column {
    display: flex;
    flex-direction: column;
    align-items: left;
    .el-button {
        margin: 5px 0;
        width: 100px; // 设置固定宽度使两个按钮宽度一致
    }
}
// .td_operate {
//     text-align: center;
//     /deep/ .el-button {width: 100px;}
//     /deep/ .el-button+.el-button {margin-left: 0;}
// }
.td_operateDiv {cursor: pointer;color: #216ec6;}
.textareaInput /deep/ .el-textarea__inner {
    width: 415px;height: 80px;
}
</style>
