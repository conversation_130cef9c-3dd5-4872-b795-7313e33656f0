<template>
    <div>
        <el-upload
            class="upload-demo center p20"
            action="https://jsonplaceholder.typicode.com/posts/"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :on-exceed="handleExceed"
            :on-success="handleSuccess"
            accept=".pdf, .doc, .docx"
            multiple
            :limit="3"
            :file-list="fileList">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">能上传文件，且不超过10MB</div>
        </el-upload>
        <div class="p20">
            <editor v-model="editorForm"></editor>
            <el-button type="primary" @click="editChange">confirm</el-button>
        </div>
        <addrCom v-model="addr" lazy showCountry ></addrCom>
    </div>
</template>
<script>
import { showLoading, hideLoading } from '../../../../utils/common'
import editor from '../../../../components/quillEditor'
import addrCom from '../../../../components/common/addrComp'
export default {
    components: {
        editor, addrCom
    },
    data () {
        return {
            fullscreenLoading: false,
            fileList: [],
            editorForm: '',
            addr: '',
        }
    },
    computed: {},
    methods: {
        editChange () {
            console.log(this.addr)
        },
        // upload
        handleRemove (file, fileList) {
            console.log(file, fileList)
        },
        handlePreview (file) {
            console.log(file)
        },
        handleExceed (files, fileList) {
            this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
        },
        beforeRemove (file, fileList) {
            console.log(fileList)
            return this.$confirm(`确定移除 ${file.name}？`, '', () => {
                alert('removed')
            })
        },
        handleSuccess (res, file) {
            console.log(res)
            console.log(file)
        },
    },
    mounted () {
        showLoading()
        setTimeout(() => {
            hideLoading()
        }, 500)
    },
}
</script>
<style scoped lang="scss">
.upload-demo {
    width: 300px;
}
// loading
/deep/ .el-loading-spinner .circular{
    width: 42px;
    height: 42px;
    animation: loading-rotate 2s linear infinite;
    display: none;
}
/deep/ .el-loading-spinner{
    /* 图片替换为您自定义的即可 */
    background: url('https://img-blog.csdnimg.cn/20200214201948358.jpg?x-oss-process=image/resize,m_fixed,h_64,w_64') no-repeat;
    background-size: 48px 48px;
    width: 100%;
    height: 100%;
    position: relative;
    top: 50%;
    left: 50%;
}
</style>
