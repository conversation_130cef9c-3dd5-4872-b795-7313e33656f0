<template>
    <div>
        <nav>
            <div class="topbar" v-loading="shopLoading">
                <div class="content-box center">
                    <div class="btns-user search_bar">
                        <div class="user-left">
                            <div @click="$router.push('/index')">
                                <img src="../../../assets/images/ico_home.png" alt=""/><span>首页</span>
                            </div>
                        </div>
                        <div class="user-right">
                            <div class="dfa" v-if="loginData.isInterior && loginData.userName">
                                当前企业：{{ selectedEnterprise }}
                                <el-dropdown class="enterprise" @command="dropChange" trigger="click" split-button
                                             v-model="selectedEnterprise">
                                    <el-dropdown-menu style="width: unset;max-width: unset;" slot="dropdown">
                                        <el-dropdown-item style="padding-right: 30px;"
                                                          @click.native="menuOrgClick(item)" :command="item.orgName"
                                                          v-for="item in dropdownList" :key="item.orgId">
                                            {{ item.orgName }} <img v-show="item.orgName == selectedEnterprise"
                                                                    src="@/assets/images/check.png" alt="">
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </div>
                            <span class="order-link" v-if="loginData.userName"
                                  @click="$router.push('/user/order')">订单中心</span>
                            <div class="cart dfa" v-if="loginData.userName" @click="$router.push('/user/cart')">
                                <span>购物车</span>
                                <img src="@/assets/images/gwc.png" alt="">
                                <div v-loading="cartNumLoading" class="mark" v-show="cartNum != null && cartNum != 0">
                                    <div>{{ cartNum }}</div>
                                </div>
                            </div>
                            <div class="greetings" v-if="loginData.userName">
                                <div class="pop">
                                    <span style="color: #333333;">您好！</span><span>{{ loginData.userName }}</span>
                                    <img src="../../../assets/images/ico_xl .png" alt="">
                                    <div class="pop-menu">
                                        <div class="popTop">
                                            <div class="user df">
                                                <img
                                                    :src="loginData.userImg ? imgUrlPrefixAdd + loginData.userImg : require('@/assets/images/userCenter/default_avatar.png')"
                                                    alt="">
                                                <div class="info">
                                                    <div class="nameTitle textOverflow1 pointer"
                                                         @click="$router.push('/user')">{{ loginData.userName }}
                                                    </div>
                                                    <div class="idTitle textOverflow1">ID: {{
                                                            loginData.userNumber
                                                        }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="btns df">
                                                <div class="dfa" @click="goMainSelf">个人中心 <i class="el-icon-arrow-right"></i></div>
                                                    <div class="dfa"
                                                         v-if="loginData.shopId == null && userInfo.enterpriseType != 2 && userInfo.isShpAuthority === 1"
                                                         @click="goCreatShop">我要开店 <i
                                                        class="el-icon-arrow-right"></i></div>
                                                    <div class="dfa"
                                                         v-if="loginData.shopId != null && loginData.auditStatus == 1 && userInfo.isShpAuthority === 1"
                                                         @click="goMainShop">店铺主页 <i
                                                        class="el-icon-arrow-right"></i></div>
                                                    <div class="dfa"
                                                         v-if="loginData.shopId != null && loginData.auditStatus == 3 && userInfo.isShpAuthority === 1"
                                                         @click="$router.push({path:'/user/statusFail',query:{shopId: loginData.shopId}})">
                                                        审核未通过 <i class="el-icon-arrow-right"></i></div>
                                                    <div class="dfa"
                                                         v-if="loginData.shopId != null && loginData.auditStatus == 2 && userInfo.isShpAuthority === 1">
                                                        审核中<i class="el-icon-arrow-right"></i></div>
                                            </div>
                                            <div class="exit" @click="handleLogout">退出</div>
                                        </div>
                                        <div class="popBottom">
                                            <div class="icons df">
                                                <i id="arrow-left" class="el-icon-arrow-left pointer"></i>
                                                <div id="iconBox" class="iconBox dfa">
                                                    <div class="items dfa pointer" @click="goPlatform" v-if="userInfo.isPlatformAdmin == 1">
                                                        <img src="@/assets/images/userCenter/backstage.png" alt="">
                                                        <div>后台管理平台</div>
                                                    </div>
                                                    <div class="items dfa pointer" @click="goPerformance" v-if="userInfo.roles && userInfo.roles.includes('物资采购平台履约系统')">
                                                        <img src="@/assets/images/userCenter/backstage.png" alt="">
                                                        <div>履约管理平台</div>
                                                    </div>
                                                    <!--<div class="items dfa pointer" @click="goShopManage" v-if="this.userInfo.isShpAuthority === 1 && this.userInfo.shopId != null && this.userInfo.auditStatus == 1">
                                                        <img src="@/assets/images/userCenter/shop.png" alt="">
                                                        <div>店铺管理平台</div>
                                                    </div>-->
                                                    <div class="items dfa pointer" @click="goSupplierSys"
                                                         v-if="userInfo.isSupplier == 1">
                                                        <img src="@/assets/images/userCenter/supplier.png" alt="">
                                                        <div>供应商平台</div>
                                                    </div>
                                                    <div class="items dfa pointer" @click="getCheck"
                                                         v-if="userInfo.isCheck == 1">
                                                        <img src="@/assets/images/userCenter/supplier.png" alt="">
                                                        <div>纪检平台</div>
                                                    </div>
                                                </div>
                                                <i id="arrow-right" class="el-icon-arrow-right pointer"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="login-btns" v-else>
                                <span @click="$router.push('/login')">【用户登录】</span>
                                <!--<span @click="showLoginForm('用户登录')">【用户登录】</span>-->
                            </div>
                            <div class="bell dfa" v-if="loginData.userName" @click="$router.push('/user/mail')">
                                <img src="@/assets/images/message.png" title="消息提醒" alt="">
                                <div class="mark" v-show="msgList.length > 0">
                                    <div>{{ msgList.length }}</div>
                                </div>
                            </div>
                            <div v-if="!loginData.userName">
                                <img src="../../../assets/images/ico_regist.png" alt=""/><span
                                @click="$router.push('/mFront/register')">新用户注册</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <el-dialog class="front" :visible.sync="dialogVisible" :close-on-click-modal="true">
                <div class="dialog-header">
                    <div class="dialog-header-top search_bar">
                        <div class="dialog-title search_bar">
                            <div></div>
                            <div>内部用户开店</div>
                        </div>
                        <div class="dialog-close" @click="dialogVisible = false"><img src="@/assets/images/close.png"
                                                                                      alt=""/></div>
                    </div>
                    <div></div>
                </div>
                <div class="dialog-body center">
                    <div class="company-info">
                        <div><span>企业名称：</span><span>{{ userInfo.orgName }}</span></div>
                    </div>
                    <el-form :model="form" ref="form" :rules="rules" label-position="top" :inline="false" size="normal">
                        <div class="row dfb">
                            <div class="col">
                                <el-form-item label="店铺名称：" prop="shopName">
                                    <el-input clearable v-model="form.shopName" placeholder="请输入店铺名称"></el-input>
                                </el-form-item>
                            </div>
                            <div class="col">
                                <el-form-item label="联系人：" prop="contact">
                                    <el-input clearable v-model="form.contact" placeholder="请输入联系人"></el-input>
                                </el-form-item>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <el-form-item label="联系电话：" prop="tel">
                                    <el-input clearable v-model="form.tel" placeholder="请输入联系电话"></el-input>
                                </el-form-item>
                            </div>
                        </div>
                        <div class="row">
                            <el-form-item label="详细地址：" prop="detailedAddress">
                                <el-input clearable type="textarea" :auto-resize="false" v-model="form.detailedAddress"
                                          placeholder="请输入详细地址"></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item prop="agreeTerm">
                            <el-checkbox v-model="form.agreeTerm" :indeterminate="false">
                                您确认阅读并接受<span style="color: seagreen"
                                                      @click="openAgreemen">《慧采商城开店协议》</span></el-checkbox>
                        </el-form-item>

                        <div class="btn center" @click="handleSubmit">提交</div>
                    </el-form>
                </div>
            </el-dialog>
            <div class="logoBox dfc">
                <div class="content-box center dfb">
                    <div class="content_left dfa">
                        <img style="width: 388px; height: 70px" src="../../../assets/images/logo_wz.png" alt=""
                             class="pointer"
                             @click="() => { $router.push('/index') }"/>
                        <div class="tablist dfa" v-if="$route.path !== '/index'">
                            <div class="tabItem" @click="() => { current = 0; $router.push('/mFront/mallIndex') }">
                                <span :style="{ color: current === 0 ? '#216EC6' : '' }">慧采商城</span>
                                <div v-if="current === 0"></div>
                            </div>
                            <div class="tabItem" @click="() => { current = 1; $router.push('/mFront/biddingIndex') }">
                                <span :style="{ color: current === 1 ? '#216EC6' : '' }">招采中心</span>
                                <div v-if="current === 1"></div>
                            </div>
                            <!-- <div class="tabItem" @click="() => { current = 2; $router.push('/user/groupIndex') }">
                                <span :style="{ color: current == 2 ? '#216EC6' : '' }">物资采购</span>
                                <div v-if="current == 2"></div>
                            </div>-->
                        </div>
                    </div>

                    <div class="content_right dfa" v-if="$route.path !== '/mFront/materialControl'">
                        <div class="dfa">
                            <div class="sx dfc" @click="searchType.show = !searchType.show">
                                {{ searchType.name }}<i
                                :class="searchType.show ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                            </div>
                            <div class="searchType" v-if="searchType.show" @mouseleave="searchType.show = false">
                                <div @click="changeSearchType(1)">商品</div>
                                <div @click="changeSearchType(2)">店铺</div>
                            </div>
                            <i class="el-icon-search"></i>
                            <input type="text" v-model="mainKeyWords" :placeholder="searchPlaceholder"/>
                        </div>
                        <button @click="handleSearch">搜索</button>
                    </div>
                </div>
            </div>
        </nav>
        <el-dialog title="慧采商城开店协议" :visible.sync="showTerm">
            <span v-html="content"></span>
            <span slot="footer">
                        <el-button @click="showTerm = false">取消</el-button>
                        <el-button type="primary" @click="showTerm = false">确定</el-button>
                    </span>
        </el-dialog>
    </div>

</template>
<script>
import { getShopStateByUserId, getUserNameInfo } from '@/api/frontStage/mallWebHeader'
import { createShopInside, cutOrg, getCartNum, getMsgNum, loginOut } from '@/api/frontStage/userCenter'
import { mapState } from 'vuex'
import { findByProgramaKey } from '@/api/w/richContent'

export default {
    data () {
        return {
            rules: {
                shopName: [
                    { required: true, message: '请输入店铺名称', trigger: 'blur' },
                    { min: 1, max: 100, message: '超出范围', trigger: 'blur' }
                ],
                contact: [
                    { required: true, message: '请输入联系人', trigger: 'blur' },
                    { min: 1, max: 20, message: '超出范围', trigger: 'blur' }
                ],
                tel: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailedAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
            },
            form: {
                shopName: '',
                contact: '',
                tel: '',
                detailedAddress: '',
                agreeTerm: false,

            },
            loginData: {},
            receiveType: 1,
            current: 0,
            showTerm: false,
            content: '',
            searchPlaceholder: '海量优质商品等您来选',
            searchType: {
                name: '商品',
                show: false,
            },
            msgList: [],
            cartNum: null,
            cartNumLoading: false,
            selectedEnterprise: '',
            dropdownList: [
                { label: '成都路桥', value: 1 },
                { label: '企业1', value: 2 },
                { label: '企业2', value: 3 },
                { label: '企业3', value: 4 },
            ],
            mainKeyWords: null,
            shopLoading: false,
            dialogVisible: false,
        }
    },
    watch: {
        $route () {
            let pathArr1 = ['/mFront/mallIndex']
            let pathArr2 = ['/mFront/biddingIndex', '/mFront/order', '/mFront/biddingDetail', '/mFront/biddingDisplayDetail']
            // let pathArr3 = ['/mFront/materialControl']
            if (pathArr1.includes(this.$route.path)) this.current = 0
            if (pathArr2.includes(this.$route.path)) this.current = 1
            // if (pathArr3.includes(this.$route.path)) this.current = 2
        },
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        getRegisterAgreeUser (programaKey) {
            findByProgramaKey({ programaKey: programaKey }).then(res => {
                this.fileList = res.files
                this.content = res.content
            })
        },
        openAgreemen () {
            this.showTerm = true
        },
        refreshHeaderVueM () {
            this.loginData = {}
        },
        menuOrgClick (item) {
            if (item.orgName == this.userInfo.enterpriseName) {
                return this.$message.info('已是当前企业无需切换！')
            }
            this.shopLoading = true
            cutOrg(item).then(res => {
                if (res != null && res.code == null) {
                    this.$store.commit('setUserInfo', res)
                    location.reload()
                } else {
                    this.shopLoading = false
                }
            }).finally(() => {
                this.shopLoading = false
            })
        },
        // 内部开店
        createShopInsideM () {
            createShopInside(this.form).then(res => {
                if (res.code === 200) {
                    this.$message({ message: '开店成功等待审核！', type: 'success' })
                    setTimeout(function () {
                        location.reload()
                    }, 1500)
                }
            })
        },
        // 提交
        handleSubmit () {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    this.createShopInsideM()
                }
            })
        },
        getMessageNum () {
            getMsgNum({ receiveType: this.receiveType }).then(res => {
                this.msgList = res
            })
        },
        getCartNumM () {
            getCartNum().then(res => {
                if (res.code != null && res.code == 401) {
                    this.loginData = {}
                } else {
                    if (res.code != null) {
                        this.cartNum == res.data
                    } else {
                        this.cartNum = res
                    }
                    this.getShopStateByUserIdM()
                    this.getMessageNum()
                }
            })
        },
        dropChange (name) {
            this.selectedEnterprise = name
        },
        changeSearchType (num) {
            if (num === 1) {
                this.searchPlaceholder = '海量优质商品等您来选'
                this.searchType.name = '商品'
            } else {
                this.searchPlaceholder = '海量优质商家等您来选'
                this.searchType.name = '店铺'
            }
            this.searchType.show = false
        },
        handleSearch () {
            this.current = 0
            let type = this.searchType.name
            if (type === '商品') {
                return this.openWindowTab({ path: '/mFront/productList', query: { keywords: this.mainKeyWords } })
            }
            if (this.mainKeyWords == null) {
                this.openWindowTab('/mFront/shopList')
            } else {
                this.openWindowTab('/mFront/shopList?keywords=' + this.mainKeyWords)
            }
            this.$parent.reverseKey()
        },
        // 退出登录
        handleLogout () {
            loginOut().then(() => {
                localStorage.removeItem('token')
                this.$store.commit('setUserInfo', {})
                window.location.href = '/'
            })
            // 状态保持清除后刷新页面
        },
        goPlatform () {
            this.openWindowTab('/platform')
        },
        goPerformance () {
            this.openWindowTab('/performance')
        },
        goShopManage () {
            this.openWindowTab('/shopManage')
        },
        goSupplierSys () {
            this.openWindowTab('/supplierSys')
        },
        getCheck () {
            this.openWindowTab('/inspection')
        },
        goMainSelf () {
            this.openWindowTab('/user')
        },
        goSecondHand () {
        },
        goCreatShop () {
            // 是否内部用户
            let isInterior = this.userInfo.isInterior == 1
            // 内部用户展示弹窗，否则跳转开店页面
            isInterior ? this.dialogVisible = true : this.openWindowTab('/mFront/becomeSeller')
        },
        goMainShop () {
            this.openWindowTab({ query: { shopId: this.userInfo.shopId }, path: '/mFront/shopIndex' })
        },
        async getShopStateByUserIdM () {
            this.shopLoading = true
            if (this.loginData.isShpAuthority === 1) {
                let res = await getShopStateByUserId()
                if (res != null) {
                    this.loginData.auditStatus = res.auditStatus
                    this.loginData.shopClass = res.shopClass
                    this.loginData.shopId = res.shopId
                    this.loginData.isBusiness = res.isBusiness
                    this.$store.commit('setUserInfo', this.loginData)
                }
            }
            let res2 = await getUserNameInfo()
            if (res2 != null) {
                this.loginData.userImg = res2.userImg
                this.loginData.userName = res2.nickName
                this.$store.commit('setUserInfo', this.loginData)
            }
            this.shopLoading = false
        },
    },
    created () {
        this.getRegisterAgreeUser('becomeSeller')
        let { userId, organizationVOS } = this.userInfo
        if (localStorage.getItem('token') && userId != null && this.$route.query.token == null) {
            this.loginData = this.userInfo
            this.selectedEnterprise = this.loginData.orgName
            // 查询店铺状态
            this.getCartNumM()
            if (!organizationVOS) return
            this.dropdownList = organizationVOS.filter(item => item.orgName.length > 0)
        }
    },
    mounted () {
        window.refreshHeaderVueM = this.refreshHeaderVueM
        // 监听事件总线
        this.$bus.$on('refreshCart', () => {
            getCartNum().then(res => this.cartNum = res.code == null ? res : res.data)
        })
        this.$bus.$on('refreshUserInfo', () => this.loginData = this.userInfo)
        // 图片选项切换
        let box = document.querySelector('#iconBox')
        let left = document.querySelector('#arrow-left')
        let right = document.querySelector('#arrow-right')
        if (box == null || box.childNodes == null) return
        let len = box.childNodes.length

        if (len < 4) {
            left.style.display = 'none'
            right.style.display = 'none'
        } else {
            left.addEventListener('click', () => {
                box.scrollLeft -= 105
                if (box.scrollLeft <= 0) box.scrollLeft = 0
            })
            right.addEventListener('click', () => {
                if (box.scrollLeft > (len - 4) * 100) return
                box.scrollLeft += 105
            })
        }
    },
}
</script>
<style scoped lang="scss">

nav {
    .content-box {
        width: 1326px;
        min-width: 1326px;

        & > div:last-child {
            padding: 32px 0;
        }

        .btns-user {
            height: 45px;
            font-size: 14px;

            img,
            span {
                cursor: pointer;
            }

            .user-left {
                height: 14px;
                display: flex;
                align-items: center;

                & > div:first-child {
                    height: 16px;
                    display: flex;
                    align-items: center;

                    img {
                        margin-right: 9px;
                    }

                    span {
                        height: 16px;
                        margin-right: 40px;
                        color: #333333;
                        letter-spacing: 0;
                        font-weight: 400;
                    }
                }

            }

            .user-right {
                height: 100%;
                display: flex;
                align-items: center;
                color: #216ec6;

                .dfa:first-of-type {
                    margin-right: 20px;
                    color: rgba(51, 51, 51, 1);
                }

                /deep/ .el-dropdown {
                    button:first-of-type {
                        display: none;
                    }

                    button:last-of-type {
                        padding: 0;
                        border: none;
                        color: rgba(33, 110, 198, 1);
                        background-color: transparent;

                        span {
                            display: none;
                        }

                        &:after {
                            content: '【切换】';
                        }
                    }
                }

                .order-link {
                    margin-right: 20px;
                    color: #333;
                }

                .bell,
                .cart {
                    position: relative;

                    img {
                        margin: 0;
                    }

                    .mark {
                        width: 12px;
                        height: 12px;
                        line-height: 12px;
                        text-align: center;
                        border-radius: 50%;
                        color: #fff;
                        background-color: #F73838;
                        position: absolute;
                        top: -4px;
                        right: -6px;

                        div {
                            transform: scale(0.7);
                        }
                    }
                }

                .cart {
                    margin-right: 20px;

                    span {
                        margin-right: 5px;
                        color: #333;
                    }
                }

                .greetings {
                    height: inherit;
                    margin-right: 20px;
                    display: flex;
                    justify-content: right;

                    .pop {
                        min-width: 117px;
                        padding-left: 10px;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        position: relative;
                        z-index: 10;

                        img {
                            margin-left: 5px;
                        }

                        &:hover {
                            background-color: #fff;

                            & .pop-menu {
                                display: block;
                            }
                        }

                        .pop-menu {
                            width: 385px;
                            height: 270px;
                            font-size: 14px;
                            border: 1px solid rgba(230, 230, 230, 1);
                            color: #333;
                            background-color: #fff;
                            position: absolute;
                            z-index: 100;
                            display: none;
                            top: 43px;
                            right: 0;

                            .popTop {
                                height: 137px;
                                padding: 15px 18px 0;
                                border-bottom: 1px solid rgba(230, 230, 230, 1);
                                position: relative;

                                .user {
                                    height: 60px;
                                    margin-bottom: 16px;

                                    img {
                                        width: 60px;
                                        height: 60px;
                                        margin-right: 12px;
                                        border-radius: 50%;
                                    }

                                    .info {
                                        padding-top: 6px;

                                        .nameTitle,
                                        .idTitle {
                                            text-align: left;
                                            max-width: 230px;
                                        }

                                        .nameTitle {
                                            line-height: 24px;
                                            margin-bottom: 4px;
                                            font-size: 16px;
                                            font-weight: 700;
                                            color: rgba(56, 56, 56, 1);
                                        }

                                        .idTitle {
                                            line-height: 21px;
                                            font-size: 14px;
                                            font-weight: 400;
                                            color: rgba(153, 153, 153, 1);
                                        }

                                    }
                                }

                                .exit {
                                    font-size: 14px;
                                    color: rgba(33, 110, 198, 1);
                                    position: absolute;
                                    top: 8px;
                                    right: 17px;
                                    cursor: pointer;
                                }

                                .btns {
                                    div {
                                        height: 30px;
                                        margin-right: 10px;
                                        padding: 0 8px 0 10px;
                                        border: 1px solid rgba(229, 229, 229, 1);
                                        color: rgba(153, 153, 153, 1);
                                        user-select: none;
                                        cursor: pointer;
                                    }
                                }
                            }

                            .popBottom {
                                padding: 16px 10px 0;

                                .icons {
                                    .iconBox {
                                        width: 293px;
                                        margin: 0 auto;
                                        overflow-x: scroll;

                                        &::-webkit-scrollbar {
                                            display: none;
                                        }

                                        .items {
                                            width: 84px !important;
                                            font-size: 14px;
                                            color: rgba(153, 153, 153, 1);
                                            flex-direction: column;

                                            div {
                                                width: 84px;
                                                height: 38px;
                                            }
                                        }

                                        & .items:not(:last-of-type) {
                                            margin-right: 21px;
                                        }
                                    }

                                    i {
                                        margin-top: 20px;
                                        color: rgba(153, 153, 153, 1);
                                    }

                                    img {
                                        width: 56px;
                                        height: 56px;
                                        margin-bottom: 5px;
                                        border-radius: 50%;
                                    }
                                }
                            }
                        }
                    }
                }

                .login-btns {
                    // width: 322px;
                    height: 14px;
                    margin-right: 10px;
                    line-height: 16px;
                    font-size: 14px;
                    letter-spacing: 0;
                    font-weight: 400;
                }

                img {
                    margin-right: 6px;
                }
            }
        }
    }

    .topbar {
        background-color: #f7f7f7;

        .content-box {
            & > div:last-child {
                padding: 0;
            }
        }
    }
}

.logoBox {
    background: #fff;
    height: 130px;
    width: 100%;

    .content_left {
        .tablist {
            margin-left: 75px;

            .tabItem {
                font-size: 18px;
                color: #000000;
                letter-spacing: 0;
                font-weight: 400;
                margin-right: 40px;
                height: 28px;
                cursor: pointer;

                div {
                    background: #226fc7;
                    width: 72px;
                    height: 3px;
                    margin-top: 5px;
                }
            }
        }
    }

    .content_right {
        .dfa {
            width: 350px;
            height: 40px;
            background: #fafafa;
            border: 1px solid rgba(222, 222, 222, 1);
            padding-left: 10px;
            position: relative;
            justify-content: flex-start;

            i {
                color: #999;
                margin: 0 10px 0 4px;
            }

            .sx {
                width: 68px;
                height: 30px;
                border-right: solid 1px #dedede;
                // background-color: beige;
                margin-right: 15px;
                font-size: 14px;
                color: #333333;
                letter-spacing: 0;
                font-weight: 500;
                cursor: pointer;
                // &:active .searchType{display: block;}
            }

            .searchType {
                width: 86px;
                height: 74px;
                position: absolute;
                border: 1px solid #dedede;
                left: 0;
                bottom: -74px;
                z-index: 3;
                cursor: pointer;

                div {
                    height: 36px;
                    line-height: 36px;
                    text-align: center;
                    background-color: #fff;
                    user-select: none;

                    &:hover {
                        color: rgb(33, 110, 198);
                    }
                }
            }
        }

        button {
            height: 40px;
            text-align: center;
            line-height: 40px;
            background: #216ec6;
            width: 80px;
            color: #fff;
        }

        input, button {
            //
        }
    }
}

.dialog-login {

    .login-box {
        width: 400px;
        margin-top: 80px;
        margin-bottom: 20px;

        .tabs {
            width: 270px;
            height: 36px;
            font-size: 20px;
            margin-bottom: 28px;
            color: #333333;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            font-weight: 400;
            position: relative;
            cursor: pointer;

            .first,
            .second {
                width: 135px;
            }

            .first {
                text-align: left;
            }

            .second {
                text-align: right;
            }

            .active-bar {
                width: 104px;
                height: 3px;
                background: #226FC7;
                position: absolute;
                left: 8px;
                bottom: 0;
                transition: .3s;
            }
        }

        .el-form-item.el-form-item--normal {
            margin: 0;
            position: relative;

            .msg {
                color: red;
                position: absolute;
                left: 3px;
                bottom: 0;
            }
        }

        .el-input.el-input--normal {
            width: 100%;
            border: none;
        }

        /deep/ .el-input--normal {
            width: 400px;
            height: 55px;
            margin-bottom: 30px;
        }

        /deep/ .el-input__inner {
            width: 400px;
            height: 55px;
            padding: 0 40px 0 50px;
            font-size: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 0;
            display: flex;

            // align-items: center;
            ::-webkit-input-placeholder {
                font-size: 20px;
                color: #d9d9d9;
            }
        }

        .input-icon {
            width: 15px;
            height: 20px;
            margin-right: 10px;

            &:last-child {
                width: unset;
                height: unset;
                margin: 0 0 0 10px;
            }
        }

        .input-box {
            width: 400px;
            height: 55px;
            margin-bottom: 30px;
            padding: 0 12px;
            border: 1px solid #d9d9d9;
            display: flex;
            align-items: center;

            input {
                height: 53px;
                flex-grow: 1;
            }

        }

        .verify-box {
            height: 55px;
            margin-bottom: 30px;

            /deep/ .el-input--normal {
                width: 270px;
                margin-bottom: 0;
            }

            /deep/ .el-input__inner {
                width: 270px;
                margin: 0 10px 0 0 !important;
                margin-bottom: unset;
            }

            button {
                width: 120px;
                font-size: 16px;
                border: 1px solid #216ec6;
                border-radius: 5px;
                color: #216ec6;
                background: #ffffff;

                &:active {
                    color: #fff;
                    background-color: #216ec6;
                }
            }
        }

        button {
            width: 400px;
            height: 55px;
            font-size: 20px;
            color: #fff;
            background-color: #216ec6;
        }
    }
}

/deep/ .el-dialog {
    width: 800px !important;
    height: 600px !important;
    margin-top: 161px !important;

    .dialog-body {
        width: 630px;
        padding: 50px 0 10px;

        .company-info {
            height: 45px;
            margin-bottom: 30px;
            padding: 15px;
            background: #F7F7F7;

            div {
                font-size: 14px;

                span:first-child {
                    color: #999;
                }

                span:last-child {
                    color: #226FC7;
                }
            }
        }

        .row {
            margin-bottom: 30px;

            .col {
                width: 300px;
            }

            .el-input__inner {
                height: 35px;
                border-radius: 0;
            }

            .el-textarea__inner {
                height: 70px !important;
                padding: 11px 10px;
                border-radius: 0;
                resize: none;
            }
        }

        .el-form-item {
            margin-bottom: 0;
        }

        .el-form-item__label {
            height: 24px;
            line-height: 14px;
            color: #999;
        }

        .btn {
            width: 80px;
            height: 40px;
            line-height: 40px;
            font-size: 16px;
            text-align: center;
            color: #fff;
            background-color: #216EC6;
            cursor: pointer;
            user-select: none;
        }
    }
}
</style>
<style lang="scss">
ul.el-dropdown-menu--small {
    width: 260px;
    margin-right: -100px;

    &, .el-dropdown-menu__item {
        padding: 0 10px;
    }

    .el-dropdown-menu__item {
        height: 45px;
        line-height: 45px;

        &:not(:last-of-type) {
            border-bottom: 1px solid rgba(230, 230, 230, 1);
        }

        position: relative;

        &:hover {
            background-color: #fff;
        }

        img {
            width: 16px;
            height: 16px;
            position: absolute;
            top: 14px;
            right: 4px;
        }
    }

    .popper__arrow {
        display: none !important;
    }
}
</style>