<template>
    <main class="userCenter" v-loading="planListLoading">
        <div class="list-title dfa mb20">
            周转材料清单列表
            <div class="search df" style="position: absolute; right: 40px">
                <div v-if="showDevFunc" class="mr30">
                    <!--                    数据权限-->
                    <el-select :value="dataSource" @change="dataSourceChange">
                        <el-option label="本级及下级" :value="0"/>
                        <el-option label="本机构" :value="1"/>
                        <el-option v-if="userPermission.hasSubOrg()" label="下级机构" :value="2"/>
                        <el-option
                            v-for="item in userPermission.subOrg"
                            :label="item.orgName" :value="item.orgId" :key="item.orgId"
                        />
                    </el-select>
                </div>
                <div class="box dfa">
                    <img src="@/assets/images/ico_search.png" alt=""/>
                    <input v-model="keyword" type="text" placeholder="单据编号、供应商名称"/>
                </div>
                <button @click="getTableData">搜索</button>
            </div>
        </div>
        <el-table
            border :data="list" height="500px" :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
            :row-style="{ fontSize: '14px', height: '48px' }"
        >
            <el-table-column label="序号" type="index" width="60" :index="indexMethod"></el-table-column>
            <el-table-column prop="synthesizeTemporarySn" label="单据编号" width="180"/>
            <el-table-column prop="supplierOrgName" label="供应商名称" width=""/>
            <el-table-column prop="receiverAddress" label="项目收货地址" width=""/>
            <el-table-column prop="synthesizeSumAmount" label="含税总金额（元）" width=""/>
            <el-table-column prop="state" label="状态" width="">
                <template v-slot="scope">
                    <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                    <el-tag v-if="scope.row.state == 1">已提交</el-tag>
                    <el-tag v-if="scope.row.state == 2">已提交</el-tag>
                    <el-tag type="success" v-if="scope.row.state == 3">供应商已确认</el-tag>
                    <el-tag v-if="scope.row.state == 4">已提交</el-tag>
                    <el-tag type="danger" v-if="scope.row.state == 5">已拒绝</el-tag>
                    <el-tag type="danger" v-if="scope.row.state == 11">审核不通过</el-tag>
                    <el-tag type="success" v-if="scope.row.state == 6">已推送大宗临购计划</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="清单类型" width="100" prop="清单类型">
                <template v-slot="scope">
                    <el-tag v-if="scope.row.billType == 1">浮动价格</el-tag>
                    <el-tag v-if="scope.row.billType == 2">固定价格</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="orgName" v-if="showDevFunc" :show-overflow-tooltip="true" label="机构名称"/>
            <el-table-column prop="gmtCreate" label="创建时间" width="160">
            </el-table-column>
            <el-table-column label="用户类型" width="100" prop="用户类型">
                <template v-slot="scope">
                    <el-tag v-if="scope.row.orgFarId">内部用户</el-tag>
                    <el-tag v-else>外部用户</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="" label="操作" width="">
                <template v-slot="scope">
                    <div
                        class="pointer" style="color: rgba(33, 110, 198, 1)"
                        @click="handleViewDetail(scope.row.synthesizeTemporarySn,scope.row.orgFarId)"
                    >查看详情
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <pagination
            :currentPage.sync="pagination.currPage" :destination="pagination.destination"
            :pageSize="pagination.pageSize"
            :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange"
            @sizeChange="sizeChange"
        >
        </pagination>
    </main>
</template>

<script>
import pagination from '@/pages/frontStage/components/pagination.vue'

import { mapState } from 'vuex'
import { synthesizeTemporaryList } from '@/api/frontStage/userCenter'
import { UserPermission } from '@/utils/permissions'
import { hasPermission } from '@/utils/permissionOperate'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return dateStr.split(' ')[0]
        },
    },
    computed: {
        ...mapState(['userInfo']),
    },
    components: { pagination },
    name: 'index',
    data () {
        return {
            // perms权限
            planList_listReview: false,
            planList_listOrder: false,
            planList_listOneClickOrder: false,
            planList_listExport: false,
            planList_listDelete: false,
            planList_listRevoke: false,

            planListLoading: false,
            keyword: null,
            pagination: {
                currPage: 1,
                destination: null,
                pageSize: 10,
                totalNum: 10,
                totalPage: 1,
            },
            list: [],
            userPermission: new UserPermission('物资下单权限'),
            dataSource: 0,
        }
    },
    created () {
        this.planList_listReview = hasPermission('planList:listTurnover:review', this.userInfo.perms || [])
        this.planList_listOrder = hasPermission('planList:listTurnover:order', this.userInfo.perms || [])
        this.planList_listOneClickOrder = hasPermission('planList:listTurnover:oneClickOrder', this.userInfo.perms || [])
        this.planList_listExport = hasPermission('planList:listTurnover:export', this.userInfo.perms || [])
        this.planList_listDelete = hasPermission('planList:listTurnover:delete', this.userInfo.perms || [])
        this.planList_listRevoke = hasPermission('planList:listTurnover:revoke', this.userInfo.perms || [])
        this.getTableData()
    },
    mounted () {
    },
    methods: {
        //数据权限
        dataSourceChange (state) {
            this.dataSource = state
            if (state === 0) {
                this.userPermission.getAllOrgData()
            } else if (state === 1) {
                this.userPermission.getHostOrgData()
            } else if (state === 2) {
                this.userPermission.getSubOrgData()
            } else if (state.length > 1) {
                this.userPermission.getSubOrgData(state)
            }
            this.getTableData()
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getTableData()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getTableData()
        },
        getTableData () {
            let params = {
                page: this.pagination.currPage,
                limit: this.pagination.pageSize,
                stType: 1, //周转材料清单
                // 数据权限
                dataSelect: this.userPermission.orgDisplayState, // （1本机及子级2只看本级3看指定子级）
                dataScope: this.userPermission.currentSubOrgId
            }
            if (this.keyword != null) {
                params.keywords = this.keyword
            }
            this.planListLoading = true
            synthesizeTemporaryList(params).then(res => {
                this.list = res.list
                this.pagination.currPage = res.currPage
                this.pagination.pageSize = res.pageSize
                this.pagination.totalNum = res.totalCount
                this.pagination.totalPage = res.totalPage
            }).finally(() => {
                this.planListLoading = false
            })
        },
        indexMethod (index) {
            return this.pagination.pageSize * (this.pagination.currPage - 1) + index + 1
        },
        // 跳转详情
        handleViewDetail (sn, orgId) {
            this.$router.push({ path: '/user/turnoverMaterialsDtl', query: { sn: sn, orgId: orgId } })
        },
    },
}
</script>

<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);

main {
    padding: 0 20px;
    border: $border;
}

.list-title {
    padding: 0;

    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}

.search {
    .box {
        width: 268px;
        height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);
        border-right: 0;

        img {
            width: 16px;
            height: 16px;
            margin: 0 4px 0 10px;
        }

        input {
            width: 230px;font-size: 13px;
        }
    }

    button {
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
    }
}

.list {
    .item {
        height: 52px;
        padding: 0 20px;
        font-size: 14px;
        border: $border;
        color: rgba(102, 102, 102, 1);
        position: relative;

        & > div:not(.pointer) {
            margin-right: 60px;
        }

        .pointer {
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 20px;
        }

        span {
            color: rgba(51, 51, 51, 1);
        }
    }
}
/deep/ .el-input--suffix .el-input__inner{
    height: 26px;
}
</style>
