export const routerObj = {
    frontStageUser: [
        { route: { path: '/user' }, menuName: '个人中心', menuId: '1', icon: 'personal_center' },
        { menuName: '计划列表', menuId: '2', icon: 'plan_list', children: [
            { route: { path: '/user/plan/0' }, menuName: '零星采购计划', menuId: '2-1' },
        ] },
        { menuName: '订单中心', menuId: '3', icon: 'ico1', children: [
            { route: { path: '/user/order' }, menuName: '零星采购订单', menuId: '3-1' },
            { route: { path: '/user/blockOrder' }, menuName: '大宗临购订单', menuId: '3-2' },
        ] },
    ],
    performance: [
        {
            menuName: '对账',
            menuId: '3',
            show: true,
            children: [
                { show: true, menuName: '对账单', menuId: '3-1', route: { path: '/performance/sheet/sheet' } },
                // { show: true, menuName: '大宗临购对账单', menuId: '3-2', route: { path: '/performance/sheet/synthesizeTemporary' } },
            ]
        },
        {
            menuName: '发票管理',
            menuId: '9',
            show: true,
            children: [
                { show: true, menuName: '发票管理', menuId: '9-1', route: { path: '/performance/invoice/record' } },
                {
                    show: true,
                    menuName: '发票抬头',
                    menuId: '9-2',
                    route: { path: '/performance/invoice/invoiceRise' },
                },
            ]
        },
        {
            menuName: '系统管理',
            menuId: '7',
            show: true,
            children: [
                {
                    show: true,
                    menuName: '收料员管理',
                    menuId: '7-1',
                    route: { path: '/performance/system/receiptPerson' },
                },

            ],
        },
    ],
    supplier: [
        {
            menuName: '消息管理',
            menuId: '1',
            authList: [0, 1],
            show: true,
            children: [
                { show: true, menuName: '收件箱', menuId: '1-1', route: { path: '/supplierSys/product/inBox', } },
            ],
        },
        {
            menuName: '内容管理',
            menuId: '2',
            authList: [0],
            show: true,
            children: [
                { show: true, menuName: '评价管理', menuId: '2-1', route: { path: '/supplierSys/comment/commentManage', } },
                { show: true, menuName: '供应商汇总评价', menuId: '2-2', route: { path: '/supplierSys/comment/supplierAggregateComment', } },
            ],
        },
        {
            menuName: '商品管理',
            menuId: '5',
            authList: [0],
            show: true,
            children: [
                { show: true, menuName: '零星采购商品', menuId: '5-2', route: { path: '/supplierSys/product/materialSupplierNotSubmit', } },
                { show: true, menuName: '大宗临购商品', menuId: '5-5', route: { path: '/supplierSys/product/lcMaterialSupplierNotSubmit', } },
                { show: true, menuName: '周转材料商品', menuId: '5-6', route: { path: '/supplierSys/product/turnMaterialSupplierNotSubmit', } },
            ],
        },
        {
            menuName: '店铺商品管理',
            menuId: '6',
            authList: [0, 1],
            show: true,
            children: [
                { show: true, menuName: '零星采购商品', menuId: '6-2', route: { path: '/supplierSys/product/materialWarehouse', } },
                { show: true, menuName: '待确认的零星采购商品', menuId: '6-4', route: { path: '/supplierSys/product/materialSupplierAffirm', } },
                { show: true, menuName: '大宗临购商品', menuId: '6-5', route: { path: '/supplierSys/product/lcMaterialWarehouse', } },
                { show: true, menuName: '待确认的大宗临购商品', menuId: '6-6', route: { path: '/supplierSys/product/lcMaterialSupplierAffirm', } },
                { show: true, menuName: '周转材料商品', menuId: '6-8', route: { path: '/supplierSys/product/turnMaterialWarehouse', } },
                { show: true, menuName: '待确认的周转材料商品', menuId: '6-9', route: { path: '/supplierSys/product/turnMaterialSupplierAffirm', } },
                { show: true, menuName: '一键铺货', menuId: '6-7', route: { path: '/supplierSys/product/oneClickStockUp', } },
            ],
        }
    ],
    platform: [
        { menuName: '收件箱', menuId: '7-2', route: { path: '/platform/mail/inBox', show: true,  } },
        {
            menuName: '我的消息',
            menuId: '7',
            show: true,
            children: [
                { menuName: '发件箱', menuId: '7-1', route: { path: '/platform/mail/outBox', show: true, } }
            ],
        },
        {
            menuName: '商品管理',
            menuId: '2',
            children: [
                { menuName: '商品分类管理', menuId: '2-1', route: { path: '/platform/product/productCategory', } },
                { menuName: '商品基础库管理', menuId: '2-2', route: { path: '/platform/product/productStorage', } },
                { menuName: '店铺商品管理', menuId: '2-3', route: { path: '/platform/product/shopMaterialManage', } },
                { menuName: '店铺审核商品管理', menuId: '2-4', route: { path: '/platform/product/shopCheckMaterial', } },
                { menuName: '品牌管理', menuId: '2-5', route: { path: '/platform/brand/brandLogo', } },
                { menuName: '店铺临购商品管理', menuId: '2-6', route: { path: '/platform/product/shopLcMaterialManage', } },
                { menuName: '店铺审核临购商品管理', menuId: '2-7', route: { path: '/platform/product/shopCheckLcMaterial', } },
                { menuName: '商品信息数据库', menuId: '2-8', route: { path: '/platform/product/productDatabase', } },

            ],
        },
        {
            menuName: '系统管理',
            menuId: '8',
            show: true,
            children: [
                { menuName: '菜单管理', menuId: '8-3', route: { path: '/platform/system/menuManage', query: { key: 'menu' } } },
                { menuName: '用户管理', menuId: '8-4', route: { path: '/platform/system/userManage', query: { key: 'user' } } },
                { menuName: '角色管理', menuId: '8-5', route: { path: '/platform/system/roleManage', query: { key: 'role' } } },
                { menuName: '参数管理', menuId: '8-1', route: { path: '/platform/system/systemParam', query: { key: 'params' } } },
                { menuName: '字典管理', menuId: '8-2', route: { path: '/platform/system/dictionaries', query: { key: 'dictionaries' } } },
            ],
        }
    ]
}