import service from '@/utils/request'

const { httpPost, httpGet } = service
//  列表查询
const getList = params => {
    return httpPost({
        url: '/materialMall/receiptPerson/listByEntity',
        params
    })
}
// 更新
const edit = params => {
    return httpPost({
        url: '/materialMall/receiptPerson/update',
        params
    })
}
// 新增
const create = params => {
    return httpPost({
        url: '/materialMall/receiptPerson/create',
        params
    })
}
// 删除
const del = params => {
    return httpGet({
        url: '/materialMall/receiptPerson/delete',
        params
    })
}

// 批量删除
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/receiptPerson/deleteBatch',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/receiptPerson/deleteBatch',
        params
    })
}
const batchPublish = params => {
    return httpPost({
        url: '/materialMall/receiptPerson/updateByPublish',
        params
    })
}
//das
const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/receiptPerson/updateNotPublish',
        params
    })
}
export {
    getList,
    edit,
    create,
    del,
    batchDelete,
    changeSortValue,
    batchPublish,
    batchNotPublish,
}
