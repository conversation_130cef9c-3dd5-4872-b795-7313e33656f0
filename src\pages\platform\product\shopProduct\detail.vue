<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="商品信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
<!--                <el-tab-pane label="规格信息" name="productInfo" :disabled="clickTabFlag">-->
<!--                </el-tab-pane>-->
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">商品信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="商品名称：">
                                            <span>{{
                                                    formData.productName
                                            }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="店铺名称：">
                                            <span>{{ formData.shopName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                            <el-form-item label="分类名称：">
                                                <span>{{ formData.className }}</span>
                                            </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="商品状态：">
                                            <el-tag v-if="formData.state==0">待上架</el-tag>
                                            <el-tag v-if="formData.state==1" type="success">已上架</el-tag>
                                            <el-tag v-if="formData.state==2" type="danger">已下架</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="商品关键字：">
                                            <span>{{ formData.productKeyword }}</span>
                                        </el-form-item>
                                    </el-col>
<!--                                    <el-col :span="12">-->
<!--                                        <el-form-item label="商品最低价：">-->
<!--                                            <span>{{ formData.productMinPrice }}</span>-->
<!--                                        </el-form-item>-->
<!--                                    </el-col>-->
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="规格：">
                                            <span>{{ tableData.skuName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="成本价：">
                                            <span>{{ tableData.costPrice }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="原价：">
                                            <span>{{ tableData.originalPrice }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="销售价格：">
                                            <span>{{ tableData.sellPrice }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="库存：">
                                            <span>{{ tableData.stock }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="单位：">
                                            <span>{{ tableData.unit }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="销量：">
                                            <span>{{ tableData.soldNum }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="库存：">
                                            <span>{{ tableData.stock }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="商品描述：">
                                            <el-input clearable type="textarea" disabled v-model="formData.productDescribe"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 商品信息-->
<!--                    <div id="productInfo" class="con">-->
<!--                        <div class="tabs-title" id="contractList">规格信息</div>-->
<!--                        <div class="e-table"  style="background-color: #fff">-->
<!--                                <div class="top" style="height: 50px; padding-left: 10px">-->
<!--                                    <div class="left">-->
<!--                                        <el-input clearable type="text" @blur="getTableData" placeholder="输入搜索关键字" v-model="keywords">-->
<!--                                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData" />-->
<!--                                        </el-input>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <el-table ref="tableRef"-->
<!--                                          border-->
<!--                                          style="width: 100%"-->
<!--                                          :data="tableData"-->
<!--                                          class="table"-->
<!--                                          :max-height="$store.state.tableHeight"-->
<!--                                >-->
<!--                                    <el-table-column label="序号" type="index" width="60"></el-table-column>-->
<!--                                    <el-table-column prop="skuName" label="规格" width="200px"></el-table-column>-->
<!--                                    <el-table-column prop="costPrice" label="成本价" width=""></el-table-column>-->
<!--                                    <el-table-column prop="originalPrice" label="原价" width=""></el-table-column>-->
<!--                                    <el-table-column prop="sellPrice" label="销售价格" width=""></el-table-column>-->
<!--                                    <el-table-column prop="stock" label="库存" width=""></el-table-column>-->
<!--                                    <el-table-column prop="unit" label="单位" width="100"></el-table-column>-->
<!--                                    <el-table-column prop="soldNum" label="销量" width=""></el-table-column>-->
<!--                                    <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>-->
<!--                                    <el-table-column prop="sort" label="排序" width=""></el-table-column>-->
<!--                                    <el-table-column prop="remarks" label="备注" width="200"></el-table-column>-->
<!--                                </el-table>-->
<!--                        </div>-->
<!--                        &lt;!&ndash;            分页&ndash;&gt;-->
<!--                        <Pagination-->
<!--                            v-show="tableData != null || tableData.length != 0"-->
<!--                            :total="paginationInfo.total"-->
<!--                            :pageSize.sync="paginationInfo.pageSize"-->
<!--                            :currentPage.sync="paginationInfo.currentPage"-->
<!--                            @currentChange="getTableData"-->
<!--                            @sizeChange="getTableData"-->
<!--                        />-->
<!--                    </div>-->

                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button @click="handleClose">取消</el-button>
        </div>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState, mapMutations } from 'vuex'
import { listSkuById } from '@/api/platform/product/shopProduct'
import { findById } from '@/api/platform/product/productCategory'
import $ from 'jquery'
// import { throttle } from '@/utils/common'
// import Pagination from '@/components/pagination/pagination'
export default {

    data () {
        return {
            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            // paginationInfo: { // 分页
            //     total: 200,
            //     pageSize: 20,
            //     currentPage: 1,
            // },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    components: {
        // Pagination
    },
    created () {
        // showLoading()
        this.formData = this.$route.params.row
        findById({ id: this.formData.classId }).then(res =>{
            this.formData.className = res.className
        })
        this.getTableData()
        // hideLoading()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        // let $idsTop = []
        // const onScroll = () => {
        //     // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
        //     if (this.clickTabFlag) {
        //         return
        //     }
        //     if (!$idsTop[$idsTop.length - 1]) {
        //         $idsTop = arr.map(item => {
        //             const $item = document.getElementById(item)
        //             let itemTop = null
        //             if ($item) {
        //                 itemTop = $item.offsetTop
        //             }
        //             return itemTop
        //         })
        //     }
        //     const scrollTop = $('#tabs-content')[0].scrollTop
        //     // 倒序查找
        //     let curLocal = 0
        //     for (let i = $idsTop.length - 1; i >= 0; i--) {
        //         let item = $idsTop[i]
        //         if (scrollTop + 1 >= item) {
        //             curLocal = i
        //             break
        //         }
        //     }
        //     // 设置对应tabName
        //     this.tabsName = arr[curLocal]
        // }
        // this.winEvent.onScroll = onScroll
        // $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        // this.screenWidth = document.documentElement.clientWidth - this.topHeight
        // this.screenHeight = document.documentElement.clientHeight - this.topHeight
        // const onResize = () => {
        //     this.screenWidth = document.documentElement.clientWidth - this.topHeight
        //     this.screenHeight = document.documentElement.clientHeight - this.topHeight
        //     $idsTop = arr.map(item => {
        //         const itemTop = document.getElementById(item).offsetTop
        //         return itemTop
        //     })
        // }
        // this.winEvent.onResize = throttle(onResize, 500)
        // window.addEventListener('resize', this.winEvent.onResize)
        // if (this.$route.query.name) {
        //     setTimeout(() => {
        //         this.onChangeTab({
        //             name: this.$route.query.name
        //         })
        //         this.tabsName = this.$route.query.name
        //     }, 200)
        // }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        // 获取表格数据
        getTableData () {
            let params = {
                productId: this.formData.productId,
                // page: this.paginationInfo.currentPage,
                // limit: this.paginationInfo.pageSize,
                page: 1,
                limit: 1
            }
            // if(this.keywords != null) {
            //     params.keywords = this.keywords
            // }
            listSkuById(params).then(res => {
                // this.tableData = res.list
                // this.paginationInfo.total = res.totalCount
                // this.paginationInfo.pageSize = res.pageSize
                // this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list[0]
            })
        },
        ...mapMutations(['setAuditParams']),
        ...mapMutations(['setSelectedInfo']),
        addData () {},
        deleteData () {},
        //取消
        handleClose () {
            this.$router.replace('/platform/product/shopProduct')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
</style>
