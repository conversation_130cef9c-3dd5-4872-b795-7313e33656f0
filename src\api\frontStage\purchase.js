import service from '@/utils/request'

const { httpPost, httpGet  } = service

/**
 * 初次登录弹框信息提交接口
 * @param {*} params
 * @returns
 */
const theFirstLogin = params => {
    return httpPost({
        url: '/materialMall/purchase/the-first-login',
        params,
    })
}

const changeOrganLogin = () => {
    return httpGet({
        url: '/materialMall/userAddress/isDefaultAddress',
    })
}

export {
    theFirstLogin,
    changeOrganLogin
}