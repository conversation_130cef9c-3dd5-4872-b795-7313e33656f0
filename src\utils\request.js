import axios from 'axios'
import { Message } from 'element-ui'
// import { showErrorDetailPop } from '@/utils/common'
import store from '@/store'
import vue from '../main.js'

export const ip = process.env.NODE_ENV === 'production' ? 'http://pcwp2-api.scrbg.com' : 'http://http://***************:15103/'

export const oss = '/oss' //nginx代理的文件下载前缀

const service = axios.create({
    // process.env.NODE_ENV === 'development' 来判断是否开发环境
    timeout: 240000,
    baseURL: '/api'
})

// 请求白名单，不需要携带token
const whiteList = ['/config/dist/getDist', '/oss/uploader1', '/oss/thumbnail', '/oss/downloader', '/config/kv/getDicValue', '/thirdapi/purchase/contract']
service.interceptors.request.use(
    config => {
        config.headers.mallType = 0
        let token = localStorage.getItem('token')
        let testToken = 'UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA'
        if (token) {
            config.headers.token = token
        }
        // 如果请求地址为指定接口或存在于白名单中则传入测试token
        if (config.url.includes('/thirdapi/purchase/contract') || config.url.includes('/thirdapi/matarialpurchase') || whiteList.includes(config.url)) {
            config.headers.token = testToken
        }
        // 云中心token
        if(config.url.includes('cloudCenter')) config.headers.cToken = store.state.cToken
        // 部署正式需要调整
        if(config.url.indexOf('/PCWP2/thirdapi/') != -1) {
            console.log('=====', config.url)
            // 测试token
            if (vue.showDevFunc) {
                config.headers.token = 'UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA'
                config.headers.sysCode = 'pcwp2'
            }else {
                // 正式token
                config.headers.token = 'RHI5YW44ek4sMTY4MTEzMDQ1OQ.lWswCMpG4b2Cr2Bz1er--a-joeFSmdJDw9rPIBB3Q2o8WvGwjAbhmg'
                config.headers.sysCode = 'pcwp2'
            }
        }
        // 若存在机构信息则传给接口
        if(store.state.userInfo.orgInfo) {
            const orgInfo = {
                orgId: store.state.userInfo.orgInfo.orgId,
                shortCode: store.state.userInfo.orgInfo.shortCode,
                orgName: store.state.userInfo.orgInfo.orgName
            }
            config.headers.org = encodeURIComponent(JSON.stringify(orgInfo))
        }
        if (config.method === 'post') {
            // if (config.headers['Content-Type']) {
            //     if (config.headers['Content-Type'].indexOf('multipart/form-data') === -1) {
            //         config.data = JSON.stringify(config.data)
            //     }
            // } else {
            //     config.data = JSON.stringify(config.data)
            // }
            config.headers['Content-Type'] =
                config.headers['Content-Type'] ||
                'application/json;charset=UTF-8'
        } else {
            config.headers['Content-Type'] =
                config.headers['Content-Type'] ||
                'application/json;charset=UTF-8'
            if (
                Object.prototype.toString.call(config.responseType) ===
                '[object Object]'
            ) {
                config.responseType['Content-Type'] =
                    'application/json;charset=UTF-8'
                config.data = true
            }
        }
        return config
    },
    () => {
        return Promise.reject('请求错误, 请联系管理员!')
    }
)

/**
 * 使用说明：
 * 成功：then回调，直接取用data即可（无需data.data）
 *      如果是async await方式 直接拿data即可
 * 失败：catch回调（前端调用异常 和 服务端业务异常）
 */
service.interceptors.response.use(
    response => {
        if(response.config.url.includes('/userCenter/user/loginOut')) return response.data || response
        if (!response) {
            return
        }
        // 文件流
        if (response.config.responseType === 'arraybuffer') {
            return response.data
        }
        // excel
        if (
            response.headers['content-type'] &&
            response.headers['content-type'].indexOf(
                'application/vnd.ms-excel'
            ) !== -1
        ) {
            return response.data
        }
        const {
            data: { code, data, message }
        } = response
        // 图片
        if (response.config.responseType == 'blob') {
            let showHeaderUrls = ['/w/user/getSendCodeVerify', '/w/user/getRegisterVerify']
            let showHeader = showHeaderUrls.some(url => response.config.url.includes(url))
            if(showHeader) return response
            return response.data
        }
        //采招平台接口
        if (response.config.url.search('purchasePlatform/api/app/Bid1/By-Bid-No') == 1) {

            if (response.status == 200) {
                const res = (response.data)
                return res
            }else {
                dealErr(404, '招标未发布')
            }
        }
        // 如果 response.data.data有值的话就返回response.data.data否则返回response.data
        // 这样其实对后端controller返回R<Void>的情况有误导，返回R<Void>的时候http请求回调里还是{code:200,msg:'msg}这种数据，而R<Other>这种则是直接返回data
        if (code == 200) {
            const res = (!data && (data !== 0 || data !== '0')) ? response.data : data
            return res
        } else if (response.data.status === '1') {
            return response.data.geocodes || response.data.regeocode
        } else {
            //业务 如果是需要忽略error处理
            try {
                if (
                    response &&
                    response.config &&
                    response.config.isIgnoreError
                ) {
                    //nothing to do
                    console.log('忽略业务error处理')
                } else {
                    dealErr(code, message, data)
                }
            } catch (e) {
                dealErr(code, message, data)
            }
            //返回服务器数据{code message data}
            return Promise.resolve(response.data)
        }
    },
    error => {
        //前端请求err
        //采招平台没有查询到招标
        if ( error.response.config.url.search('purchasePlatform/api/app/Bid1/By-Bid-No') == 1) {
            return
        }
        let errorInfo = error.response
        if (!(typeof errorInfo === undefined) && errorInfo) {
            const { statusText, status } = JSON.parse(
                JSON.stringify(error.response)
            )
            const data = { code: status, msg: statusText }
            dealErr(data.code, data.msg)
        } else {
            dealErr(400, '网络出现问题，请稍后再试')
        }
        //返回前端请求异常数据
        return Promise.reject(error)
    }
)

/**
 * 前端异常和服务器端业务异常处理
 */
let isOpenError = null
function dealErr (c, message, data) {
    switch (c) {
    case 500:
        Message.error(message)
        break
    case 500485:
        Message.error(message)
        break
    case 400:
        Message.error(message)
        break
    case 401:
        // 登录超时
        window.localStorage.clear()
        window.sessionStorage.clear()
        store.commit('setUserInfo', {})
        window.refreshHeaderVueM ? window.refreshHeaderVueM() : null
        Message.error(message)
        break
    case 4010:
        // Message.error(message)
        window.localStorage.clear()
        window.sessionStorage.clear()
        store.commit('setUserInfo', {})
        window.location.reload()
        break
    case 404:
        Message.error('网络请求不存在')
        break
        // 其他错误，直接抛出错误提示
    case 405:
        if(data != null) {
            for (const i in data) {
                Message.error(data[i])
            }
        }else {
            Message.error('请检查未填的非空项')
        }
        break
        // 其他错误，直接抛出错误提示
    default:
        if (message && !isOpenError) {
            Message.error(message)
            // showErrorDetailPop(message)
            // isOpenError = MessageBox({
            //     title: '错误提示',
            //     type: 'error',
            //     message,
            //     customClass: 'request_err_pop',
            //     callback () {
            //         isOpenError = null
            //         console.log('MessageBox callback: ', isOpenError)
            //     }
            // })
        }
    }
}

/**
 * post表单请求方式
 */
service.httpPostForm = function httpPostForm ({
    url,
    params = {},
    headers = { 'Content-Type': 'multipart/form-data' },
    timeout = 60000,
    isLoading = false,
    isIgnoreError = false
}) {
    const formdata = new FormData()
    for (const p in params) {
        formdata.append(p, params[p])
    }

    return service.post(url, formdata, {
        headers,
        timeout,
        withCredentials: true,
        isLoading,
        isIgnoreError
    })
}

/**
 * post请求方式
 */
service.httpPost = function httpPost ({
    url,
    params = {},
    headers = {},
    responseType = {},
    isLoading = false,
    isIgnoreError = false
}) {
    return service.post(url, params, {
        headers,
        withCredentials: true,
        isLoading,
        responseType,
        isIgnoreError
    })
}

/**
 * get表单请求方式
 */
service.httpGet = function httpGet ({
    url,
    params = {},
    headers = {},
    responseType = {},
    isLoading = false,
    isIgnoreError = false
}) {
    return service.get(url, {
        headers,
        params,
        withCredentials: true,
        responseType,
        isLoading,
        isIgnoreError
    })
}

/**
 * get表单请求方式
 */
const httpGetFile = function httpGet ({
    url,
    params = {},
    headers = {},
    responseType = 'arraybuffer',
    isLoading = false,
    isIgnoreError = false
}) {
    return service.get(url, {
        headers,
        params,
        withCredentials: true,
        responseType,
        isLoading,
        isIgnoreError,
        baseURL: ''
    })
}
service.httpGetFile = httpGetFile

export default service
export {
    httpGetFile
}
