<template>
    <div class="root">
        <div class="title center mb20">大宗临供各地区参考价信息栏</div>
        <div class="box center">
            <div class="list-item df" v-for="item in newsList" :key="item.id" >
                <div class="date">{{item.gmtRelease}}</div>
                <div class="content">
                    <h3 @click="handleView(item)">{{item.title}}</h3>
                    <p>{{item.summary}}</p>
                </div>
            </div>
            <el-empty v-if="newsList.length === 0" :image="require('@/assets/images/ico_kong.png')" style="height: 400px"></el-empty>
            <pagination
                :currentPage.sync="currPage"
                :destination="destination"
                :pageSize="pageSize"
                :total="totalNum"
                :totalPage="Math.ceil(totalNum / pageSize)"
                @currentChange="currentChange"
                @sizeChange="sizeChange">
            </pagination>
        </div>
    </div>
</template>

<script>
import { getWebInfo } from '@/api/frontStage/webInfo'
import { showLoading, hideLoading } from '@/utils/common'
import Pagination from '../../components/pagination.vue'
export default {
    name: 'topList',
    components: { Pagination },
    data () {
        return {
            newsList: [],
            pageSize: 5,
            currPage: 1,
            totalNum: 0,
            destination: '',
            mallType: 0,
            orderBy: 4,
            state: 1,
            programaKey: 'LcPriceAnnouncement',
        }
    },
    methods: {
        currentChange () {},
        sizeChange () {},
        handleView ({ contentId }) {
            this.openWindowTab({ path: '/mFront/newsDetail', query: { id: contentId } })
        },
        getList () {
            showLoading()
            let params = {
                limit: this.pageSize,
                page: this.currPage,
                mallType: this.mallType,
                programaKey: this.programaKey,
                orderBy: this.orderBy,
                state: this.state,
            }
            getWebInfo(params).then(res => {
                this.newsList = res.list
                this.totalNum = res.totalCount
            }).finally(() => hideLoading())
        },
    },
    created () {
        this.getList()
    },
}
</script>

<style scoped lang="scss">
@import "../../../../assets/css/news.scss";
.title {
    width: 1326px;
    font-size: 22px;
}
</style>