<template>
    <div>
        <main>
            <div class="contentBox center">
                <div class="cards">
                    <!-- <div class="item">
                        <div class="triangle"><img src="../../../../assets/images/img/ico_daiban .png" alt=""></div>
                        <div class="count"><span>代办事项</span><span>0</span></div>
                        <div class="tags">
                            <div>外包方 0</div><div>招标 0</div>
                            <div>合同 0</div><div>结算 0</div>
                            <div>材料 0</div><div>周材 0</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="triangle"><img src="../../../../assets/images/img/ico_yichang .png" alt=""></div>
                        <div class="count"><span>异常预警</span><span>0</span></div>
                        <div class="tags">
                            <div>外包方 0</div><div>招标 0</div>
                            <div>合同 0</div><div>结算 0</div>
                            <div>材料 0</div><div>周材 0</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="triangle"><img src="../../../../assets/images/img/ico_tongzhi .png" alt=""></div>
                        <div class="count"><span>系统通知</span><span>0</span></div>
                    </div> -->
                    <div class="item purchase">
                        <img src="../../../../assets/images/img/ico_zhaobiao.png" alt="">
                        <div>
                            <div class="title">我的招标</div>
                            <div class="statusCount"><span>14</span><span>报名中</span></div>
                        </div>
                    </div>
                    <div class="item order">
                        <img src="../../../../assets/images/img/ico_dingdan .png" alt="">
                        <div>
                            <div class="title">我的订单</div>
                            <div class="statusCount"><span>0</span>待收货<span></span></div>
                        </div>
                    </div>
                    <div class="item question">
                        <img src="../../../../assets/images/img/ico_xunjia .png" alt="">
                        <div>
                            <div class="title">我的询价</div>
                            <div class="statusCount"><span>2</span><span>报名中</span></div>
                        </div>
                    </div>
                </div>
                <iframe class="frame" frameborder="0" scrolling="no" onload="this.height=this.contentWindow.document.body.scrollHeight" src="http://***************/materialIndex?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VybnVtYmVyIjoiMDI0NjMxIiwiZXhwIjoxNjg0ODI1MzcyLCJ1c2VyaWQiOiJhYmI1MTg0ZDQzY2EtOTYwYy1iNjQwLWMzNmMtNDU0YTEwZjQiLCJ1c2VybmFtZSI6IlBDV1DmtYvor5UifQ.7UFvAthCSn4HvFK0vpAuYP0L7yecmLAVAsh7sUjqJ7M&orgId=ad551eff9d03-8efe-2148-9ed8-64781e1e"></iframe>
                <toolBox></toolBox>
            </div>
        </main>
    </div>
</template>
<script>
import toolBox from '../../components/toolBox.vue'
export default {
    components: { toolBox },
    data () {
        return {}
    },
    methods: {},
    created () {},
}
</script>
<style scoped lang="scss">
main{
    padding-top: 20px;
    font-family: 'Source Han Sans CN';
    background-color: #f5f5f5;
    .contentBox{
        width: 1326px;
        .cards{
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            .item{
                width: 428px;
                height: 150px;
                margin-bottom: 20px;
                padding-left: 80px;
                background-color: #fff;
                display: flex;
                align-items: center;
                position: relative;
                &>img{margin-right: 25px;}
                .triangle{
                    width: 0;
                    height: 0;
                    border: 25px solid;
                    position: absolute;
                    left: 0;
                    top: 0;
                    img{
                        position: absolute;
                        left: -20px;
                        top: -20px;
                    }
                }
                .count{
                    margin-right: 84px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    &>span:first-child{
                        margin-bottom: 13px;
                        font-size: 16px;
                    }
                    &>span:last-child{
                        font-size: 30px;
                        font-weight: 700;
                        color: #818181;
                    }
                }
                .tags{
                    width: 166px;
                    color: #fff;
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    div{
                        width: 80px;
                        height: 24px;
                        margin-bottom: 6px;
                        text-align: center;
                        line-height: 24px;
                        background-color: beige;
                    }
                }
                .title{
                    color: #333;
                    font-size: 24px;
                    font-weight: 500;
                }
                .statusCount{
                    &>span:first-child{margin-right: 5px;font-size: 50px;font-weight: 700;}
                    &>span:last-child{font-size: 18px;}
                }
                &:first-child{
                    .triangle{border-color: #1890FF transparent transparent #1890FF;}
                    .count{color: #269AFA;}
                    .tags>div{background-color: #269AFA;}
                }
                &:nth-child(2){
                    .triangle{border-color: #FA8A14 transparent transparent #FA8A14;}
                    .count{color: #FA8A14;}
                    .tags>div{background-color: #FA8A14;}
                }
                &:nth-child(3){
                    .triangle{border-color: #389E0D transparent transparent #389E0D;}
                    .count{color: #389E0D;}
                }
                &:nth-child(4){
                    .statusCount{color: #1890FF;}
                }
                &:nth-child(5){
                    .statusCount{color: #ED842A;}
                }
                &:nth-child(6){
                    .statusCount{color: #9751E8;}
                }

            }
            .purchase{
                background-image: linear-gradient(90deg, #DEEDFF 0%, #F2F9FF 100%);
            }
            .order{background-image: linear-gradient(90deg, #FFECDD 2%, #FFFAF7 99%, #FFFAF7 99%);}
            .question{background-image: linear-gradient(90deg, #EFE2FA 0%, #FBF8FE 100%);}
        }
        .frame {
            width: 100%;
            height: 2300px;
        }
    }
}
</style>