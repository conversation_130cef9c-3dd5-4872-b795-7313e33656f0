<template>
    <div class="root">
        <div class="main">
            <div class="goodList">
                <div class="titleBox dfa">
                    <div></div>
                    <span>商品列表</span>
                </div>
                <el-table border :data="goodData" style="width: 100%" :header-cell-style="{ background: '#f7f7f7' }">
                    <el-table-column prop="index" label="序号" width="80" align="center">
                        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                    </el-table-column>
                    <el-table-column prop="name" label="商品名称" align="center">
                    </el-table-column>
                    <el-table-column prop="total" label="数量" width="180" align="center">
                        <template slot-scope="scope">
                            <el-input-number v-model="scope.row.total" @change="handleChange" :min="1" :max="50" label="描述文字"></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column prop="unitPrice" label="单价" width="110" align="center">
                    </el-table-column>
                    <el-table-column prop="totalPrice" label="合计" width="110" align="center">
                    </el-table-column>
                    <el-table-column prop="yiNum" label="已选数量" align="center" width="100">
                    </el-table-column>
                    <el-table-column prop="planDan" label="已加入计划单明细" width="220" align="center">
                    </el-table-column>
                </el-table>
            </div>
            <!-- 计划单 -->
            <div class="planBox dfb">
                <div class="planBox_left">
                    <div class="titleBox dfa">
                        <div></div>
                        <span>计划单</span>
                    </div>
                    <div class="searchBox dfa mb20">
                        <el-input v-model="input" placeholder="请输入计划单编号"></el-input>
                        <el-button style="height: 32px; background: #d43030; color: #fff">查询</el-button>
                    </div>
                    <el-table @row-click="rowClick" border :data="planDan" style="width: 100%;cursor: pointer;" :header-cell-style="{ background: '#f7f7f7' }">
                        <el-table-column prop="sn" label="计划单编号" align="center">
                        </el-table-column>
                        <el-table-column prop="date" label="计划日期" align="center">
                        </el-table-column>
                    </el-table>
                    <div class="pageBox dfb">
                        <div class="left pointer">
                            <i class="el-icon-arrow-left"></i>
                            上一页
                        </div>
                        <div class="right pointer">
                            下一页
                            <i class="el-icon-arrow-right"></i>
                        </div>
                    </div>
                </div>

                <!-- ==================================== -->
                <div class="planBox_right">
                    <div class="titleBox dfa">
                        <div></div>
                        <span>采购计划明细</span>
                    </div>
                    <div class="number">计划单编号：{{ sn }}</div>
                    <el-table border :data="planList" style="width: 100%" :header-cell-style="{ background: '#f7f7f7' }">
                        <el-table-column prop="index" label="序号" width="80" align="center">
                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                        </el-table-column>
                        <el-table-column prop="name" label="商品名称" align="center">
                        </el-table-column>
                        <el-table-column prop="type" label="规格型号" width="180" align="center">
                        </el-table-column>
                        <el-table-column prop="planNum" label="计划数量" width="110" align="center">
                        </el-table-column>
                        <el-table-column prop="price" label="金额" width="110" align="center">
                        </el-table-column>
                        <el-table-column prop="yiNum" label="数量" align="center">
                            <template slot-scope="scope">
                                <el-input-number v-model="scope.row.yiNum" @change="handleChange" :min="1" :max="50" label="描述文字"></el-input-number>
                            </template>
                        </el-table-column>
                        <el-table-column prop="syNum" label="剩余数量" width="100" align="center">
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>

        <div class="btnBox dfc">
            <div class="left dfc" @click="goBack">
                <i class="el-icon-arrow-left"></i>
                上一步
            </div>
            <div class="right dfc" @click="goNext">
                下一步
                <i class="el-icon-arrow-right"></i>
            </div>
        </div>

        <div class="footer">
            <div class="content dfb">
                <div class="dfc">
                    <img src="../../../../../assets/images/img/z8.png" alt="" />
                </div>
                <div class="dfc">
                    <img style="height: 28px" src="../../../../../assets/images/img/z9.png" alt="" />
                </div>
                <div class="dfc">
                    <img style="height: 30px" src="../../../../../assets/images/img/z10.png" alt="" />
                </div>
                <div class="dfc">
                    <img src="../../../../../assets/images/img/z11.png" alt="" />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { goodData, planDan, planList } from './index'
export default {
    components: { },
    data () {
        return {
            input: '',
            goodData: [],
            planDan: [],
            planList: [],
            sn: 'P00001'
        }
    },
    mounted () {
        this.goodData = goodData
        this.planDan = planDan
        this.planList = planList
    },
    methods: {
        rowClick (row, column, event) {
            console.log(row, column, event)
            this.sn = row.sn
        },
        handleChange () {},
        goBack () {
            this.$router.go(-1)
        },
        goNext () {
            this.$router.push('/user/orderDetail')
        },
    },
}
</script>
<style>
.goodList .cell {
    font-size: 14px;
}

.planBox .cell {
    font-size: 14px;
}
</style>
<style scoped lang="scss">
div {
    //   line-height: 1;
    box-sizing: border-box;
}

.root {
    width: 100%;
    background: #f5f5f5;
    .main {
        width: 1326px;
        margin: 0 auto;

        .titleBox {
            width: 100%;
            height: 50px;
            border-bottom: solid 1px #d8d8d8;
            margin-bottom: 20px;

            div {
                width: 3px;
                height: 20px;
                background: #216ec6;
                margin-right: 11px;
            }

            span {
                font-size: 20px;
                color: #333333;
                font-weight: 500;
            }
        }

        .goodList {
            width: 100%;
            background: #fff;
            padding: 20px;
            margin: 20px 0;
            padding-top: 0;
        }

        .planBox {
            margin-top: 20px;
            .planBox_left {
                width: 250px;
                height: 423px;
                background: #fff;
                padding: 0 20px;

                .pageBox {
                    font-size: 14px;
                    color: #333333;
                    letter-spacing: 0;
                    text-align: center;
                    font-weight: 400;
                    margin-top: 20px;
                }
            }

            .planBox_right {
                width: 1056px;
                height: 423px;
                background: #fff;
                padding: 0 20px;

                .number {
                    font-size: 16px;
                    color: #333333;
                    line-height: 24px;
                    font-weight: 400;
                    margin-bottom: 20px;
                }
            }
        }
    }

    .footer {
        width: 100%;
        background: #fff;
        height: 80px;

        .content {
            width: 1326px;
            margin: 0 auto;
            height: 100%;

            div {
                width: 25%;

                img {
                    width: 166px;
                    height: 26px;
                }
            }
        }
    }
}

.btnBox {
    width: 100%;
    height: 70px;
    background: #fff;
    margin: 20px 0;

    .left {
        width: 140px;
        height: 40px;
        font-size: 18px;
        color: #ffffff;
        background: #216ec6;
        cursor: pointer;
    }

    .right {
        width: 140px;
        height: 40px;
        font-size: 18px;
        color: #ffffff;
        background: #216ec6;
        margin-left: 30px;
        cursor: pointer;
    }
}
</style>
