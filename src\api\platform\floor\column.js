import service from '@/utils/request'

const { httpPost, httpGet } = service

const getColumnList = params => {
    return httpPost({
        url: '/materialMall/platform/categoryColumns/listByEntity',
        params
    })
}

const getColumnById = params => {
    return httpPost({
        url: '/materialMall/platform/categoryColumns/findById',
        params
    })
}

const editColumn = params => {
    return httpPost({
        url: '/materialMall/platform/categoryColumns/update',
        params
    })
}

const createColumn = params => {
    return httpPost({
        url: '/materialMall/platform/categoryColumns/create',
        params
    })
}

const delColumn = params => {
    return httpGet({
        url: '/materialMall/platform/categoryColumns/delete',
        params
    })
}

const batchDeleteColumn = params => {
    return httpPost({
        url: '/materialMall/platform/categoryColumns/deleteBatch',
        params
    })
}

const batchPublishColumn = params => {
    return httpPost({
        url: '/materialMall/platform/categoryColumns/updateByPublish',
        params
    })
}

const batchNotPublishColumn = params => {
    return httpPost({
        url: '/materialMall/platform/categoryColumns/updateNotPublish',
        params
    })
}

const getEnterpriseInfoColumn = params => {
    return httpGet({
        url: '/materialMall/platform/categoryColumns/findById',
        params
    })
}

const changeSortValueColumn = params => {
    return httpPost({
        url: '/materialMall/platform/categoryColumns/updateBatchById',
        params
    })
}
export {
    getColumnList,
    getColumnById,
    createColumn,
    editColumn,
    delColumn,
    batchPublishColumn,
    batchNotPublishColumn,
    batchDeleteColumn,
    getEnterpriseInfoColumn,
    changeSortValueColumn,
}