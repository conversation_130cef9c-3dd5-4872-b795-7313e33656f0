import service from '@/utils/request'

const { httpPost, httpGet } = service

// 新增规则

const getDataList = params => {
    return httpPost({
        url: '/materialMall/productWarningDtl/listByEntity',
        params
    })
}
const findproductWarningDtlById = params => {
    return httpGet({
        url: '/materialMall/productWarningDtl/findAllByWaringId',
        params
    })
}
const dele = params => {
    return httpGet({
        url: '/materialMall/productWarningDtl/delete',
        params
    })
}
const deleteBatch = params => {
    return httpPost({
        url: '/materialMall/productWarningDtl/deleteBatch',
        params
    })
}
// 修改转台
const updateStateBatch = params => {
    return httpPost({
        url: '/materialMall/productWarningDtl/updateState',
        params,
    })
}
// 修改
const update = params => {
    return httpPost({
        url: '/materialMall/productWarningDtl/excel/update',
        params,
    })
}

//立即执行
const executeNowBatch = params => {
    return httpGet({
        url: '/materialMall/productWarningDtl/excel/executeNowBatch',
        params,
    })
}

// 导出excel
export {
    getDataList,
    findproductWarningDtlById,
    dele,
    deleteBatch,
    executeNowBatch,
    updateStateBatch,
    update
}