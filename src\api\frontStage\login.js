import service from '@/utils/request'
import qs from 'qs'

const { httpPost, httpGet } = service

// 登陆发送验证码
const loginSendCode = params => {
    return httpPost({
        url: '/materialMall/w/user/indexPutPhoneCode',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 注册发送手机验证码
const registerSendCode = params => {
    return httpPost({
        url: '/materialMall/w/user/registerSendCode',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 修改密码验证码
const updatePassSendCodeByNoLogin = params => {
    return httpPost({
        url: '/materialMall/w/user/updatePassSendCode',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 修改密码验证码
const updatePassSendCode = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/updatePassSendCode',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 修改手机验证码
const updatePhoneSendCode = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/updatePhoneSendCode',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 检验修改密码的验证码
const checkUpdatePassCodeByNoLogin = params => {
    return httpPost({
        url: '/materialMall/w/user/checkUpdatePassCode',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 修改密码
const updatePasswordByNoLogin = params => {
    return httpPost({
        url: '/materialMall/w/user/updatePassword',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 检验修改密码的验证码
const checkUpdatePassCode = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/checkUpdatePassCode',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 检验修改手机的验证码
const checkUpdatePhoneCode = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/checkUpdatePhoneCode',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
//修改密码
const updatePassword = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/updatePassword',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 修改手机号
const updatePhone = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/updatePhone',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}

const tokenLogin = params => {
    return httpGet({
        url: '/materialMall/w/user/tokenLogin',
        params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}

const getVerificationImg = params => {
    return httpGet({
        url: '/materialMall/w/user/getRegisterVerify',
        params,
        responseType: 'blob',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
}

const checkVerificationImg = params => {
    return httpGet({
        url: '/materialMall/w/user/checkVerify',
        params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}

const getPrivateKeyId = params => {
    return httpGet({
        url: '/materialMall/w/user/getPrivateKeyId',
        params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
// 当前外部用户密码是否过期
const getUserPwdInfoByPhone = params => {
    return httpGet({
        url: '/materialMall/w/user/checkPwdState',
        params
    })
}
const checkExpiring = params => {
    return httpGet({
        url: '/materialMall/w/user/checkExpiring',
        params
    })
}

const getSendCodeImg = params => {
    return httpGet({
        url: '/materialMall/w/user/getSendCodeVerify',
        params,
        responseType: 'blob',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}

const checkSendCodeVerify = params => {
    return httpGet({
        url: '/materialMall/w/user/checkSendCodeVerify',
        params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}

export {
    loginSendCode,
    registerSendCode,
    updatePassSendCode,
    checkUpdatePassCode,
    updatePassword,
    updatePhoneSendCode,
    checkUpdatePhoneCode,
    updatePhone,
    tokenLogin,
    getVerificationImg,
    checkVerificationImg,
    getPrivateKeyId,
    getSendCodeImg,
    checkSendCodeVerify,
    getUserPwdInfoByPhone,
    checkExpiring,
    updatePassSendCodeByNoLogin,
    checkUpdatePassCodeByNoLogin,
    updatePasswordByNoLogin
}