<template>
        <div class="e-form">
<!--            浮动-->
            <BillTop @cancel="handleClose"></BillTop>
            <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
                <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                    <el-tab-pane label="对账单详情" name="baseInfo" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="对账单明细" name="reconciliationDtl" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <div id="tabs-content">
                        <!-- 基本信息 -->
                        <div id="baseInfo" class="con">
                            <div class="tabs-title" id="baseInfo">对账单详情</div>
                            <el-form :model="reconciliationForm" :rules="reconciliationFormRules" label-width="200px" ref="reconciliationFormRef" :disabled="false" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="业务类型：" prop="reconciliationProductType">
                                            <el-radio v-model="reconciliationForm.reconciliationProductType" :label="13">大宗临购</el-radio>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="对账时间：" prop="startEndTme">
                                            <el-date-picker
                                                value-format="yyyy-MM-dd"
                                                v-model="reconciliationForm.startEndTme"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12" >
                                        <el-form-item label="收货单位：" prop="twoSupplierName">
                                            <el-input placeholder="请选择收货单位" disabled
                                                      v-model="reconciliationForm.purchasingOrgName"/>
                                            <el-button :disabled="reconciliationForm.reconciliationProductType == null || reconciliationForm.startEndTme == null ||  reconciliationForm.startEndTme.length == 0"
                                                       size="mini" type="primary" @click="selectedEnterprise">选择
                                            </el-button>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="选择物资：" prop="">
                                            <el-button size="mini"
                                                       :disabled="reconciliationForm.reconciliationProductType == null || reconciliationForm.startEndTme == null ||  reconciliationForm.startEndTme.length == 0"
                                                       type="primary"
                                                       @click="selectMaterialBtnClick">选择物资</el-button>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="供货单位：" prop="supplierName">
                                            <span>{{reconciliationForm.supplierName}}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收货单位：" prop="purchasingOrgName">
                                            <span>{{reconciliationForm.purchasingOrgName}}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额：" prop="reconciliationAmount">
                                            <span>{{reconciliationForm.reconciliationAmount}}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="不含税总金额：" prop="reconciliationNoRateAmount">
                                            <span>{{reconciliationForm.reconciliationNoRateAmount}}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="对账类型：" prop="type">
                                            <el-tag v-if="reconciliationForm.type == 1">浮动价格对账</el-tag>
                                            <el-tag v-if="reconciliationForm.type == 2">固定价格对账</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="税率（%）：" prop="taxRate">
                                            <el-input
                                                type="number"
                                                v-model="reconciliationForm.taxRate"
                                                @change="taxRateChange()">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="税额：" prop="taxAmount">
                                    <span>{{reconciliationForm.taxAmount}}</span>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                                <el-row>
                                    <el-col :span="12" >
                                        <el-form-item label="超期垫资利息（%）：" prop="outPhaseInterest">
                                            {{reconciliationForm.outPhaseInterest}}%
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" >
                                        <el-form-item label="货款支付周期（单位月）：" prop="paymentWeek">
                                            {{reconciliationForm.paymentWeek}}月
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注：" prop="remarks">
                                            <el-input style="width: 1000px;" type="textarea" :auto-resize="false" v-model="reconciliationForm.remarks"
                                                      placeholder="请输入备注" maxlength="1000" show-word-limit></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <!--计划清单-->
                        <div id="reconciliationDtl" class="con">
                            <div class="tabs-title" id="reconciliationDtl">对账单明细</div>
                            <div class="e-table table-container"  style="background-color: #fff" >
                                <el-table
                                    ref="tableRef"
                                    border
                                    style="width: 100%"
                                    :data="tableData"
                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                    :row-style="{ fontSize: '14px', height: '48px' }"
                                >
                                    <el-table-column label="序号" type="index" width="60"  fixed="left"></el-table-column>
                                    <el-table-column prop="receivingDate" label="收料日期" width="130">
                                        <template v-slot="scope">
                                            {{ scope.row.receivingDate | dateStr }}
                                        </template>
                                    </el-table-column>
<!--                                    <el-table-column label="操作" width="120" fixed="left" v-if="reconciliationForm.reconciliationProductType == 12">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            <span class="pointer" style="color: rgba(33, 110, 198, 1); margin-left: 20px" @click="dismantleM(scope.row)">拆单</span>-->
<!--                                            <span class="pointer" style="color: rgb(176,5,5); margin-left: 20px" @click="deleteM(scope.row)">删除</span>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
                                    <el-table-column prop="orderSn" label="订单号" width="220"/>
                                    <el-table-column prop="materialName" label="物资名称" width="200"/>
                                    <el-table-column prop="spec" label="规格型号" width=""/>
                                    <el-table-column prop="unit" label="单位" width=""/>
                                    <el-table-column prop="texture" label="材质" width=""/>
                                    <el-table-column prop="maxQuantity" label="可选数量" width="70"/>
                                    <el-table-column prop="quantity" label="已选数量" width="100" >
                                        <template v-slot="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.quantity"
                                                @change="getChangedRow(scope.row)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="freightPrice" label="网价" width="100" v-if="reconciliationForm.type === 1">
                                        <template v-slot="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.freightPrice"
                                                @change="priceChange(scope.row)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="fixationPrice" label="固定费用" width="100" v-if="reconciliationForm.type === 1">
                                        <template v-slot="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.fixationPrice"
                                                @change="priceChange(scope.row)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="outFactoryPrice" label="出厂价" width="100" v-if="reconciliationForm.type === 2">
                                        <template v-slot="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.outFactoryPrice"
                                                @change="gPriceChange(scope.row)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="transportPrice" label="运杂费" width="100" v-if="reconciliationForm.type === 2">
                                        <template v-slot="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.transportPrice"
                                                @change="gPriceChange(scope.row)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="price" label="含税单价" width=""/>
                                    <el-table-column prop="noRatePrice" label="不含税单价" width=""/>
                                    <el-table-column prop="acceptanceAmount" label="含税金额" width=""/>
                                    <el-table-column prop="acceptanceNoRateAmount" label="不含税金额" width=""/>
                                    <el-table-column prop="taxAmount" label="税额" width=""/>
                                    <el-table-column prop="remarks" label="备注" width="" >
                                        <template v-slot="scope">
                                            <el-input
                                                v-model="scope.row.remarks">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </el-tabs>
            </div>
            <div class="buttons">
                <el-button type="primary" @click="saveSheetM(0)">保存</el-button>
                <el-button type="primary" @click="saveSheetM(1)">保存并提交</el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
            <el-dialog
                v-dialogDrag custom-class="dlg" width="90%" title="物资选择"
                :visible.sync="selectMaterialDialogShow"
            >
                <div class="box-left">
                    <div class="e-table">
                        <div class="top">
                            <div style="width: 200px">
                                <el-input  type="text" @blur="getContractOrPlanListM" placeholder="输入搜索关键字" v-model="keywords">
                                    <img :src="require('@/assets/search.png')" slot="suffix" @click="getContractOrPlanListM" />
                                </el-input>
                            </div>
                        </div>
                        <el-table ref="selectContractOrPlanR"
                                  id="myTable"
                                  border
                                  :select-all="false"
                                  max-height="340px"
                                  @selection-change="selectContractOrPlanSelectM"
                                  @row-click="selectContractOrPlanRowClickM"
                                  :data="contractOrPlanTableDate"
                                  v-loading="selectContractOrPlanLoading"
                                  class="table"
                        >
                            <el-table-column type="selection" width="40"></el-table-column>
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column v-if="reconciliationForm.reconciliationProductType == 13" prop="billNo" label="计划编号" width=""></el-table-column>
                            <el-table-column prop="orderSn" label="订单编号" width=""></el-table-column>
                            <el-table-column prop="orgName" label="收货单位名称" width=""></el-table-column>
                            <el-table-column prop="gmtCreate" label="创建日期" width="">
                                <template v-slot="scope">
                                    {{ scope.row.gmtCreate | dateStr }}
                                </template>
                            </el-table-column>
                        </el-table>
                        <Pagination
                            v-if="contractOrPlanTableDate != null && contractOrPlanTableDate.length > 0"
                            :total="paginationInfo.total"
                            :pageSize.sync="paginationInfo.pageSize"
                            :currentPage.sync="paginationInfo.currentPage"
                            @currentChange="currentChangeUser"
                            @sizeChange="sizeChangeUser"
                        />
                    </div>
                </div>
                <div class="box-right">
                    <div class="e-table">
                        <div class="top" >
                            <div style="width: 200px">
                                <el-input type="text" @blur="searchSiteReceivingTableDate" placeholder="输入搜索关键字" v-model="keywords2">
                                    <img :src="require('@/assets/search.png')" slot="suffix" @click="searchSiteReceivingTableDate" />
                                </el-input>
                            </div>
                            <el-date-picker
                                :clearable="false"
                                style="margin-left: 20px"
                                @change="searchSiteReceivingTableDate"
                                value-format="yyyy-MM-dd"
                                v-model="reconciliationForm.startEndTme"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </div>
                        <el-table ref="siteReceivingTableRef"
                                  v-loading="siteReceivingLoading"
                                  border
                                  max-height="390px"
                                  @selection-change="siteReceivingTableSelectM"
                                  @row-click="siteReceivingTableRowClickM"
                                  :data="siteReceivingTableDate"
                                  class="table"
                        >
                            <el-table-column type="selection" width="40"></el-table-column>
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column prop="orderNo" label="订单编号" width=""></el-table-column>
                            <el-table-column prop="materialName" label="物资名称" width=""></el-table-column>
                            <el-table-column prop="spec" label="规格型号" width=""></el-table-column>
                            <el-table-column prop="texture" label="材质" width=""></el-table-column>
                            <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                            <el-table-column prop="quantity" label="剩余可对账数量" width=""></el-table-column>
<!--                            <el-table-column prop="totalQuantity" label="总数量" width=""></el-table-column>-->
                        </el-table>
                        <Pagination
                            v-show="siteReceivingTableDate != null && siteReceivingTableDate.length > 0"
                            :total="paginationInfo2.total"
                            :pageSize.sync="paginationInfo2.pageSize"
                            :currentPage.sync="paginationInfo2.currentPage"
                            @currentChange="currentChangeUser2"
                            @sizeChange="sizeChangeUser2"
                        />
                    </div>
                </div>
                <span slot="footer">
                <el-button type="primary" @click="siteReceivingTableDateSelectAffirmClick">确认选择</el-button>
                <el-button @click="selectMaterialDialogShow = false">取消</el-button>
            </span>
            </el-dialog>
            <el-dialog v-dialogDrag :title="selectContractOrPlanTableTitle" :visible.sync="showSelectContractOrPlan"  width="80%" style="margin-left: 10%;" :close-on-click-modal="false">
                <div class="e-table" style="background-color: #fff" v-loading="selectContractOrPlanLoading">
                    <div class="top dfa" style="height: 50px; padding-left: 10px">
                            <el-input style="width: 200px; " type="text" @blur="getContractOrPlanListM" placeholder="输入搜索关键字" v-model="keywords">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="getContractOrPlanListM" />
                            </el-input>
                    </div>
                    <el-table ref="selectContractOrPlanR"
                              border
                              style="width: 100%"
                              @selection-change="selectContractOrPlanSelectM"
                              @row-click="selectContractOrPlanRowClickM"
                              :data="contractOrPlanTableDate"
                              class="table"
                              :max-height="$store.state.tableHeight"
                    >
                        <el-table-column type="selection" width="40"></el-table-column>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column v-if="reconciliationForm.reconciliationProductType == 13" prop="billNo" label="计划编号" width=""></el-table-column>
                        <el-table-column prop="orderSn" label="订单编号" width=""></el-table-column>
                        <el-table-column prop="orgName" label="收货单位名称" width=""></el-table-column>
                        <el-table-column prop="gmtCreate" label="创建时间" width=""></el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <span slot="footer">
                    <Pagination
                        :total="paginationInfo.total"
                        :pageSize.sync="paginationInfo.pageSize"
                        :currentPage.sync="paginationInfo.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                </span>
                <div class="buttons">
                </div>
                <div class="buttons">
                    <el-button  type="primary" @click="selectContractOrPlanClickM">确认</el-button>
                    <el-button @click="showSelectContractOrPlan = false">关闭</el-button>
                </div>
            </el-dialog>
            <el-dialog v-dialogDrag title="可对账物资明细" :visible.sync="showSiteReceivingDia"  width="80%" style="margin-left: 10%;" :close-on-click-modal="false">
                <div class="e-table" style="background-color: #fff" v-loading="siteReceivingLoading">
                    <div class="top dfa" style="height: 50px; padding-left: 10px">
                        <el-input style="width: 200px; " type="text" @blur="searchSiteReceivingTableDate" placeholder="输入搜索关键字" v-model="keywords2">
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="searchSiteReceivingTableDate" />
                        </el-input>
                    </div>
                    <el-table ref="siteReceivingTableRef"
                              border
                              style="width: 100%"
                              @selection-change="siteReceivingTableSelectM"
                              @row-click="siteReceivingTableRowClickM"
                              :data="siteReceivingTableDate"
                              class="table"
                              :max-height="$store.state.tableHeight"
                    >
                        <el-table-column type="selection" width="40"></el-table-column>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="orderNo" label="订单编号" width=""></el-table-column>
                        <el-table-column prop="materialName" label="物资名称" width=""></el-table-column>
                        <el-table-column prop="spec" label="规格型号" width=""></el-table-column>
                        <el-table-column prop="texture" label="材质" width=""></el-table-column>
                        <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                        <el-table-column prop="quantity" label="剩余可对账数量" width=""></el-table-column>
<!--                        <el-table-column prop="totalAmount" label="金额" width=""></el-table-column>-->
<!--                        <el-table-column prop="totalQuantity" label="总数量" width=""></el-table-column>-->
<!--                        <el-table-column prop="supplierName" label="供应商名称" width=""></el-table-column>-->
<!--                        <el-table-column prop="sourceNumber" label="源单编号" width=""></el-table-column>-->
                    </el-table>
                </div>
                <!--分页-->
                <span slot="footer">
                    <Pagination
                        :total="paginationInfo2.total"
                        :pageSize.sync="paginationInfo2.pageSize"
                        :currentPage.sync="paginationInfo2.currentPage"
                        @currentChange="currentChangeUser2"
                        @sizeChange="sizeChangeUser2"
                    />
                </span>
                <div class="buttons">
                    <el-button  type="primary" @click="siteReceivingTableDateSelectAffirmClick">确认</el-button>
                    <el-button @click="showSiteReceivingDia = false">关闭</el-button>
                </div>
            </el-dialog>
            <el-dialog
                v-dialogDrag :title="selectPurchasingOrgNameTitle" :visible.sync="showPurchasingOrgNameView" width="80%"
                style="margin-left: 10%;" :close-on-click-modal="false"
            >
                <div class="e-table" v-loading="selectPurchasingOrgNameLoading">
                    <div class="top dfa" style="height: 50px; padding-left: 10px">
                        <el-input
                            style="width: 200px; " type="text" @blur="searchEnterprise"
                            placeholder="输入搜索关键字" v-model="enterpriseKeywords"
                        >
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="searchEnterprise"/>
                        </el-input>
                    </div>
                    <el-table
                        ref="selectTwoSupplierR"
                        border
                        @selection-change="selectOrgNameSelectM"
                        :data="purchasingOrgNameTable"
                        class="table"
                        :max-height="$store.state.tableHeight"
                    >
                        <!--<el-table-column type="selection" width="40"></el-table-column>-->
                        <el-table-column label="序号" type="index" width="60"/>
                        <el-table-column label="操作">
                            <template v-slot="scope">
                                <div
                                    class="pointer" style="color: rgba(33, 110, 198, 1);"
                                    @click="submitOrgName(scope.row)"
                                >选择收货单位
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="收货单位名称" prop="orgName"/>
                    </el-table>
                </div>
                <!--分页-->
                <span slot="footer">
                    <Pagination
                        :total="paginationInfo1.total"
                        :pageSize.sync="paginationInfo1.pageSize"
                        :currentPage.sync="paginationInfo1.currentPage"
                        @currentChange="currentChangeUser1"
                        @sizeChange="sizeChangeUser1"
                    />
                </span>
            </el-dialog>
        </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
import {
    reconciliationFixCountAmount
} from '@/utils/material_reconciliationUtils/compute'

// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
import {
    getCanUseSiteReceivingDtl,
    materialReconciliationSTSupplierCreate,
    materialReconciliationGetOrderById,
    getOrderItem4Price } from '@/api/reconciliation/reconciliation'
import { getReconcilableMaterialList, getReconciliablePlansBySupplier, getReconciliableEnterprisePageList } from '@/api/orderShipDtl/orderShipDtl'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return  dateStr.split(' ')[0]
        }
    },
    components: {
        Pagination
    },
    data () {
        return {
            //新增  添加选择收货单位
            selectPurchasingOrgNameTitle: null,
            selectPurchasingOrgNameLoading: false,
            enterpriseKeywords: '',
            showPurchasingOrgNameView: false,
            purchasingOrgNameTable: false,
            orgName: '',
            selectPurchasingOrgNameRowDate: [],
            paginationInfo1: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },

            formLoading: false,
            reconciliationFormRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                reconciliationProductType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' },
                ],
            },
            siteReceivingTableSelectRowData: [],
            selectContractOrPlanSelectRowData: [],
            siteReceivingTableDate: [],
            siteReceivingLoading: false,
            showSiteReceivingDia: false,
            keywords2: null,
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            selectContractOrPlanTableTitle: null,
            selectContractOrPlanLoading: false,
            showSelectContractOrPlan: false,
            selectMaterialDialogShow: false,
            contractOrPlanTableDate: [],
            keywords: null,
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            reconciliationForm: {
                type: null,
                orderId: null,
                orderSn: null,
                businessType: 6,
                reconciliationProductType: 13,
                sourceBillId: null,
                sourceBillNo: null,
                supplierId: null,
                supplierEnterpriseId: null,
                supplierName: null,
                purchaserId: null,
                purchaserLocalId: null,
                purchaserName: null,
                purchasingOrgId: null,
                purchasingLocalOrgId: null,
                purchasingOrgName: null,
                acceptanceName: null,
                reconciliationAmount: null,
                taxAmount: null,
                reconciliationNoRateAmount: null,
                settleAmount: null,
                startTime: null,
                endTime: null,
                startEndTme: [],
                createType: 1,
                // 内外供应商信息
                creditCode: null,
                orgShort: null,
                supplierOrgId: null,
                taxRate: null,
                dtl: [],

            },
            showReconciliationForm: false,
            reconciliationFormLoading: false,
            maxNum: *********,
            tableData: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableLoading: false,
            changedRow: [],
        }
    },
    created () {
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        //新增添加项目部选择
        selectedEnterprise () {
            this.selectPurchasingOrgNameTitle = '选择项目部'
            this.getOrderPlanByOrgNameListM()
            this.showPurchasingOrgNameView = true
        },
        // 搜索企业名称
        searchEnterprise () {
            // 搜索时重置到第一页
            this.paginationInfo1.currentPage = 1
            this.getOrderPlanByOrgNameListM()
        },
        //根据发货单位进行分组查询企业名称
        getOrderPlanByOrgNameListM () {
            let params = {
                page: this.paginationInfo1.currentPage,
                limit: this.paginationInfo1.pageSize,
            }
            if(this.enterpriseKeywords != null) {
                params.keywords = this.enterpriseKeywords
            }
            if(this.reconciliationForm.reconciliationProductType != null) {
                params.productType = this.reconciliationForm.reconciliationProductType
            }
            this.selectPurchasingOrgNameLoading = true
            getReconciliableEnterprisePageList(params).then(res => {
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                this.purchasingOrgNameTable = res.list
            }).finally(() => {
                this.selectPurchasingOrgNameLoading = false
            })
        },

        selectOrgNameSelectM (value) {
            this.selectPurchasingOrgNameRowDate = value
        },
        //选择发货单位
        submitOrgName (row) {
            this.reconciliationForm.purchasingOrgName = row.orgName
            this.showPurchasingOrgNameView = false
        },
        // 获取一级大宗临购可对账物资列表
        getReconcilableMaterialListByOrder (selectedRows) {
            // 确保分页参数有效
            if (!this.paginationInfo2.currentPage || this.paginationInfo2.currentPage <= 0) {
                this.paginationInfo2.currentPage = 1
            }
            if (!this.paginationInfo2.pageSize || this.paginationInfo2.pageSize <= 0) {
                this.paginationInfo2.pageSize = 20
            }

            // 处理多个选中的订单，将orderSn用逗号隔开
            let orderSnList = []
            if (Array.isArray(selectedRows)) {
                // 如果传入的是数组，提取所有orderSn
                orderSnList = selectedRows.map(row => row.orderSn).filter(orderSn => orderSn)
            } else if (selectedRows && selectedRows.orderSn) {
                // 如果传入的是单个对象，提取orderSn
                orderSnList = [selectedRows.orderSn]
            }

            if (orderSnList.length === 0) {
                this.$message.error('未找到有效的订单编号')
                return
            }

            let params = {
                orderSn: orderSnList.join(','), // 多个orderSn用逗号隔开
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
            }

            // 添加查询条件
            if (this.keywords2 != null) {
                params.keywords = this.keywords2
            }
            if (this.reconciliationForm.startEndTme && this.reconciliationForm.startEndTme.length > 0) {
                params.startTime = this.reconciliationForm.startEndTme[0]
                params.endTime = this.reconciliationForm.startEndTme[1]
            }

            this.siteReceivingLoading = true
            getReconcilableMaterialList(params).then(res => {
                console.log('getReconcilableMaterialList API返回数据:', res)
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage

                // 字段转换：将后台返回的字段映射到前端表格需要的字段
                // 确保包含getReconcilableMaterialList返回的所有字段
                this.siteReceivingTableDate = res.list.map(item => ({
                    ...item,  // 保留原始数据的所有字段
                    orderNo: item.orderSn,  // orderSn → orderNo (用于表格显示)
                    quantity: item.maxQuantity,  // maxQuantity → quantity (用于表格显示)
                    orderDtlId: item.orderItemId,  // orderItemId → orderDtlId (用于表格显示)
                    // 确保关键字段存在
                    orderSn: item.orderSn,  // 订单编号
                    materialName: item.materialName,  // 物资名称
                    spec: item.spec,  // 规格型号
                    texture: item.texture,  // 材质
                    unit: item.unit,  // 单位
                    maxQuantity: item.maxQuantity,  // 剩余可对账数量
                    orderItemId: item.orderItemId,  // 订单项ID
                    receiveDate: item.receiveDate,  // 收料日期
                    ratePrice: item.ratePrice,  // 含税单价
                    noRatePrice: item.noRatePrice,  // 不含税单价
                    remarks: item.remarks,  // 备注
                    type: item.type,  // 对账类型
                    // 新增字段
                    orderId: item.orderId,  // 订单ID
                    tradeId: item.tradeId,   // 贸易ID
                    // 供应商信息字段
                    supplierId: item.supplierId,  // 供应商ID
                    supplierName: item.supplierName,  // 供应商名称
                    // 采购员信息字段
                    purchaserId: item.purchaserId,  // 采购员ID
                    purchaserName: item.purchaserName  // 采购员名称
                }))
                console.log('转换后的siteReceivingTableDate（前3条）:', this.siteReceivingTableDate.slice(0, 3))
                console.log('检查供应商信息字段:', {
                    supplierId: this.siteReceivingTableDate[0]?.supplierId,
                    supplierName: this.siteReceivingTableDate[0]?.supplierName
                })
                console.log('检查采购员信息字段:', {
                    purchaserId: this.siteReceivingTableDate[0]?.purchaserId,
                    purchaserName: this.siteReceivingTableDate[0]?.purchaserName
                })
                console.log('检查新增字段 orderId 和 tradeId:', {
                    orderId: this.siteReceivingTableDate[0]?.orderId,
                    tradeId: this.siteReceivingTableDate[0]?.tradeId
                })

                // 等待DOM更新后，默认选中所有行
                this.$nextTick(() => {
                    if (this.$refs.siteReceivingTableRef && this.siteReceivingTableDate.length > 0) {
                        // 清空之前的选择
                        this.$refs.siteReceivingTableRef.clearSelection()
                        // 选中所有行
                        this.siteReceivingTableDate.forEach(row => {
                            this.$refs.siteReceivingTableRef.toggleRowSelection(row, true)
                        })
                    }
                })
            }).finally(() => {
                this.siteReceivingLoading = false
            })
        },

        // 搜索物资列表的统一方法
        searchSiteReceivingTableDate () {
            // 如果有选中的订单，使用新的API调用
            if (this.selectContractOrPlanSelectRowData.length > 0) {
                this.getReconcilableMaterialListByOrder(this.selectContractOrPlanSelectRowData)
            } else {
                this.getSiteReceivingTableDateM()
            }
        },

        selectMaterialBtnClick () {
            if (this.showDevFunc) {
                if (this.reconciliationForm.purchasingOrgName == null || this.reconciliationForm.purchasingOrgName == '') {
                    this.$message.warning('请先选择收货单位')
                    return
                }
            }
            // 重置右侧数据和分页信息
            this.siteReceivingTableDate = []
            this.paginationInfo2.currentPage = 1
            this.paginationInfo2.total = 0
            this.paginationInfo2.pageSize = 20

            this.getContractOrPlanListM()
            this.selectContractOrPlanSelectRowData = []
            this.selectMaterialDialogShow = true
        },
        selectContractOrPlanSelectM (value) {
            this.selectContractOrPlanSelectRowData = value
            this.siteReceivingTableDate = []
            // 清空右侧表格的选中状态
            this.siteReceivingTableSelectRowData = []
            if(value.length > 0) {
                // 重置右侧分页信息
                this.paginationInfo2.currentPage = 1
                this.paginationInfo2.total = 0

                let num = this.reconciliationForm.reconciliationProductType
                let row = this.selectContractOrPlanSelectRowData[0]
                // 计划
                if(num == 13) {
                    this.reconciliationForm.businessType = 6
                    this.reconciliationForm.sourceBillId = row.billId
                    this.reconciliationForm.sourceBillNo = row.billNo
                }
                this.productTypeDis = true
                this.tableData = []
                this.reconciliationForm.creditCode = row.creditCode
                this.reconciliationForm.orgShort = row.shortCode
                this.reconciliationForm.supplierOrgId = row.storageOrgId
                this.reconciliationForm.purchasingOrgId = row.orgId
                this.reconciliationForm.purchasingOrgName = row.orgName
                this.reconciliationForm.taxRate = row.taxRate
                this.reconciliationForm.supplierName = row.storageName
                this.reconciliationForm.type = row.type

                // 设置供应商ID
                this.reconciliationForm.supplierId = row.supplierId || row.storageId || row.storageOrgId

                // 设置采购员信息
                this.reconciliationForm.purchaserId = row.purchaserId
                this.reconciliationForm.purchaserName = row.purchaserName

                console.log('选择订单时设置供应商和采购员信息:', {
                    supplierName: row.storageName,
                    supplierId: this.reconciliationForm.supplierId,
                    storageOrgId: row.storageOrgId,
                    purchaserId: this.reconciliationForm.purchaserId,
                    purchaserName: this.reconciliationForm.purchaserName,
                    完整订单数据: row
                })

                // 如果billType为空或未定义，设置默认值
                if (this.reconciliationForm.type == null || this.reconciliationForm.type === undefined) {
                    // 默认设置为浮动价格对账
                    this.reconciliationForm.type = 1
                    console.log('billType为空，设置默认对账类型为浮动价格:', this.reconciliationForm.type)
                }
                // 获取支付周期等
                this.getOrderInfo(row.orderId)
                // this.showSelectContractOrPlan = false
                let newArr = this.selectContractOrPlanSelectRowData.filter(t => {
                    if (row.billNo != t.billNo) {
                        this.$message.error('请选择同计划的订单数据！')
                        this.$refs.selectContractOrPlanR.toggleRowSelection(t, false)
                        return false
                    } else {
                        this.$refs.selectContractOrPlanR.toggleRowSelection(t, true)
                        return true
                    }
                })
                this.selectContractOrPlanSelectRowData = newArr

                // 根据用户类型选择不同的API
                // if (this.userInfo.isInterior === 1) {
                //     // 内部用户，使用原来的请求方法
                //     this.getSiteReceivingTableDateM()
                // } else {
                //     // 非内部用户，使用新的请求方法
                //     this.getReconcilableMaterialListByOrder(this.selectContractOrPlanSelectRowData)
                // }

                this.getReconcilableMaterialListByOrder(this.selectContractOrPlanSelectRowData)
            }
        },
        selectContractOrPlanRowClickM (row) {
            row.flag = !row.flag
            this.$refs.selectContractOrPlanR.toggleRowSelection(row, row.flag)
        },
        // 获取订单信息
        getOrderInfo (orderId) {
            if(orderId == null || orderId == '') {
                return this.$message.error('订单id不存在！')
            }
            this.formLoading = true
            materialReconciliationGetOrderById({ orderId: orderId }).then(res => {
                this.reconciliationForm.paymentWeek = res.paymentWeek
                this.reconciliationForm.outPhaseInterest = res.outPhaseInterest
            }).finally(() => {
                this.formLoading = false
            })
        },
        siteReceivingTableSelectM (value) {
            this.siteReceivingTableSelectRowData = value
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        // 选择数据确认
        siteReceivingTableDateSelectAffirmClick () {
            if(this.siteReceivingTableSelectRowData.length == 0) {
                return this.$message.error('未选择数据')
            }

            // 保存原始选中的数据，包含getReconcilableMaterialList返回的完整字段
            const originalSelectedData = [...this.siteReceivingTableSelectRowData]

            this.formLoading = true
            getOrderItem4Price(this.siteReceivingTableSelectRowData).then(res => {
                let priceData = []

                // 确保返回的数据是数组格式
                if (Array.isArray(res)) {
                    priceData = res
                } else if (res && Array.isArray(res.list)) {
                    // 如果返回的是包含list属性的对象
                    priceData = res.list
                } else if (res && Array.isArray(res.data)) {
                    // 如果返回的是包含data属性的对象
                    priceData = res.data
                } else {
                    // 如果都不是，使用原始数据
                    console.warn('getOrderItem4Price返回的数据格式不正确:', res)
                    priceData = originalSelectedData
                }

                // 合并原始数据和价格数据，确保包含所有字段
                this.siteReceivingTableSelectRowData = originalSelectedData.map((originalItem, index) => {
                    const priceItem = priceData[index] || {}
                    return {
                        ...originalItem,  // 保留原始数据的所有字段（包括getReconcilableMaterialList返回的完整字段）
                        ...priceItem,     // 覆盖价格相关字段
                        // 确保getReconcilableMaterialList返回的关键字段不被覆盖为空值
                        orderSn: originalItem.orderSn || priceItem.orderSn,  // 订单编号
                        materialName: originalItem.materialName || priceItem.materialName,  // 物资名称
                        spec: originalItem.spec || priceItem.spec,  // 规格型号
                        texture: originalItem.texture || priceItem.texture,  // 材质
                        unit: originalItem.unit || priceItem.unit,  // 单位
                        maxQuantity: originalItem.maxQuantity || priceItem.maxQuantity,  // 剩余可对账数量
                        orderItemId: originalItem.orderItemId || priceItem.orderItemId,  // 订单项ID
                        receiveDate: originalItem.receiveDate || priceItem.receiveDate,  // 收料日期
                        ratePrice: originalItem.ratePrice || priceItem.ratePrice,  // 含税单价
                        noRatePrice: originalItem.noRatePrice || priceItem.noRatePrice,  // 不含税单价
                        remarks: originalItem.remarks || priceItem.remarks,  // 备注
                        // 新增字段
                        orderId: originalItem.orderId || priceItem.orderId,  // 订单ID
                        tradeId: originalItem.tradeId || priceItem.tradeId,   // 贸易ID
                        // 供应商信息字段
                        supplierId: originalItem.supplierId || priceItem.supplierId,  // 供应商ID
                        supplierName: originalItem.supplierName || priceItem.supplierName,  // 供应商名称
                        // 采购员信息字段
                        purchaserId: originalItem.purchaserId || priceItem.purchaserId,  // 采购员ID
                        purchaserName: originalItem.purchaserName || priceItem.purchaserName  // 采购员名称
                    }
                })

                this.submitAffirmM()
            }).finally(() => {
                this.formLoading = false
            })
        },
        submitAffirmM () {
            // 确保 siteReceivingTableSelectRowData 是数组
            if (!Array.isArray(this.siteReceivingTableSelectRowData)) {
                this.$message.error('选中的数据格式不正确')
                return
            }

            let newArr = []
            this.siteReceivingTableSelectRowData.forEach(row => {
                if(row.quantity == null || row.quantity <= 0) {
                    return this.$message.error('剩余数量为0不能选择！')
                }
                let dtl = {
                    receiptBillId: row.billId,
                    receiptBillSn: row.billNo,
                    receiptBillDtlId: row.dtlId,
                    warehouseId: row.warehouseId,
                    warehouseName: row.warehouseName,
                    receivingDate: row.receivingDate || row.receiveDate,  // 使用receiveDate字段
                    materialId: row.materialId,
                    materialClassId: row.materialClassId,
                    materialClassName: row.materialClassName,
                    materialMallId: row.materialMallId,
                    materialName: row.materialName,  // 来自getReconcilableMaterialList
                    spec: row.spec,  // 来自getReconcilableMaterialList
                    unit: row.unit,  // 来自getReconcilableMaterialList
                    texture: row.texture,  // 来自getReconcilableMaterialList
                    quantity: row.quantity,  // 来自maxQuantity字段映射
                    settledAmount: 0,
                    freightPrice: row.freightPrice,
                    fixationPrice: row.fixationPrice,
                    outFactoryPrice: row.outFactoryPrice,
                    transportPrice: row.transportPrice,
                    orderId: row.orderId,
                    orderSn: row.orderSn || row.orderNo,  // 来自getReconcilableMaterialList的orderSn字段
                    orderDtlId: row.orderDtlId,
                    // getReconcilableMaterialList返回的关键字段
                    orderItemId: row.orderItemId,  // 订单项ID
                    price: row.ratePrice || row.price,  // 含税单价，优先使用ratePrice
                    noRatePrice: row.noRatePrice,  // 不含税单价
                    remarks: row.remarks,  // 备注
                    uuid: getUuid(),
                    maxQuantity: row.maxQuantity || row.quantity,  // 剩余可对账数量
                    // 新增字段
                    tradeId: row.tradeId  // 商品ID
                }
                // 浮动价格对账：网价等于含税单价
                if(this.reconciliationForm.type == 1) {
                    // 优先使用ratePrice，如果没有则使用freightPrice（网价），因为网价等于含税单价
                    dtl.price = Number(row.ratePrice) || Number(row.freightPrice) || 0
                    // 固定费用单独处理，不加到含税单价中
                    dtl.freightPrice = dtl.price
                    dtl.fixationPrice = Number(row.fixationPrice) || 0
                }
                // 固定
                if(this.reconciliationForm.type == 2) {
                    dtl.price = Number(row.ratePrice) || (Number(row.outFactoryPrice) + Number(row.transportPrice))
                }

                // 计算含税金额
                dtl.acceptanceAmount = this.fixed2(dtl.price * dtl.quantity)

                // 计算不含税单价（根据税率）
                if (this.reconciliationForm.taxRate && this.reconciliationForm.taxRate > 0) {
                    dtl.noRatePrice = this.fixed2(dtl.price / (1 + this.reconciliationForm.taxRate / 100))
                } else {
                    dtl.noRatePrice = dtl.price
                }

                // 计算不含税金额
                dtl.acceptanceNoRateAmount = this.fixed2(dtl.noRatePrice * dtl.quantity)

                // 计算税额：税额 = 含税金额 - 不含税金额
                dtl.taxAmount = this.fixed2(dtl.acceptanceAmount - dtl.acceptanceNoRateAmount)

                newArr.push(dtl)
            })

            // 保留现有的对账单明细数据，追加新选择的数据
            // 检查是否有重复的数据（基于orderItemId）
            const existingOrderItemIds = this.tableData.map(item => item.orderItemId).filter(id => id)
            const filteredNewArr = newArr.filter(newItem => {
                return !existingOrderItemIds.includes(newItem.orderItemId)
            })

            // 追加新数据到现有表格数据
            this.tableData = [...this.tableData, ...filteredNewArr]

            // 先清空需要重新计算的对账单详情栏位
            this.reconciliationForm.reconciliationAmount = 0      // 含税总金额
            this.reconciliationForm.reconciliationNoRateAmount = 0  // 不含税总金额
            this.reconciliationForm.taxAmount = 0                 // 税额

            // 对账类型从选中的数据中获取
            if (this.siteReceivingTableSelectRowData[0] && this.siteReceivingTableSelectRowData[0].billType) {
                this.reconciliationForm.type = this.siteReceivingTableSelectRowData[0].billType
                console.log('从物资选择数据中设置对账类型:', this.siteReceivingTableSelectRowData[0].billType)
            } else {
                console.log('物资选择数据中没有billType字段:', this.siteReceivingTableSelectRowData[0])
            }

            console.log('确认选择后的表格数据:', this.tableData)
            console.log('对账类型:', this.reconciliationForm.type)
            console.log('税率:', this.reconciliationForm.taxRate)

            // 只在对应字段为空时才填充，避免覆盖用户已填写的数据
            if (!this.reconciliationForm.purchaserId && this.siteReceivingTableSelectRowData[0].purchaserId) {
                this.reconciliationForm.purchaserId = this.siteReceivingTableSelectRowData[0].purchaserId
            }
            if (!this.reconciliationForm.purchaserName && this.siteReceivingTableSelectRowData[0].purchaserName) {
                this.reconciliationForm.purchaserName = this.siteReceivingTableSelectRowData[0].purchaserName
            }
            if (!this.reconciliationForm.purchasingOrgId && this.siteReceivingTableSelectRowData[0].purchasingOrgId) {
                this.reconciliationForm.purchasingOrgId = this.siteReceivingTableSelectRowData[0].purchasingOrgId
            }
            if (!this.reconciliationForm.purchasingOrgName && this.siteReceivingTableSelectRowData[0].purchasingOrgName) {
                this.reconciliationForm.purchasingOrgName = this.siteReceivingTableSelectRowData[0].purchasingOrgName
            }
            if (!this.reconciliationForm.supplierId && this.siteReceivingTableSelectRowData[0].supplierId) {
                this.reconciliationForm.supplierId = this.siteReceivingTableSelectRowData[0].supplierId
            }
            if (!this.reconciliationForm.supplierName && this.siteReceivingTableSelectRowData[0].supplierName) {
                this.reconciliationForm.supplierName = this.siteReceivingTableSelectRowData[0].supplierName
            }

            // 使用 $nextTick 确保表格数据更新完成后再计算金额
            this.$nextTick(() => {
                console.log('开始计算金额，当前表格数据行数:', this.tableData.length)

                // 直接调用我们的计算方法，根据对账单明细来计算总额
                this.calculateTaxAmountByRule()

                console.log('计算完成后的金额:')
                console.log('含税总金额:', this.reconciliationForm.reconciliationAmount)
                console.log('不含税总金额:', this.reconciliationForm.reconciliationNoRateAmount)
                console.log('税额:', this.reconciliationForm.taxAmount)
            })
            this.showSiteReceivingDia = false
            this.selectMaterialDialogShow = false
        },
        getSiteReceivingTableDateM () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                orgId: this.reconciliationForm.purchasingOrgId,
                businessType: this.reconciliationForm.businessType,
            }
            let orderIds = this.selectContractOrPlanSelectRowData.map(t => t.orderId)
            if(orderIds.length == 0) {
                return this.$message.error('未选择订单')
            }
            params.orderIds = orderIds
            // 如果是内部店铺
            if(this.userInfo.isInterior == 1) {
                params.orgShort = this.userInfo.orgInfo.shortCode
            }else {
                params.creditCode = this.userInfo.socialCreditCode
            }
            if(this.keywords2 != null) {
                params.keyword = this.keywords2
            }
            if(this.reconciliationForm.sourceBillId != null) {
                params.sourceId = this.reconciliationForm.sourceBillId
            }
            this.siteReceivingLoading = true
            getCanUseSiteReceivingDtl(params).then(res => {
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
                this.siteReceivingTableDate = res.list
            }).finally(() => {
                this.siteReceivingLoading = false
            })
        },
        selectContractOrPlanClickM () {
            if(this.selectContractOrPlanSelectRowData.length == 0) {
                return this.$message.error('未选择数据！')
            }
            let num = this.reconciliationForm.reconciliationProductType
            let row = this.selectContractOrPlanSelectRowData[0]
            // 计划
            if(num == 13) {
                let flag = false
                for (let i = 0; i < this.selectContractOrPlanSelectRowData.length; i++) {
                    if(row.billNo != this.selectContractOrPlanSelectRowData[i].billNo) {
                        flag = true
                    }
                }
                if(flag) {
                    return this.$message.error('请选择同计划的订单数据！')
                }
                this.reconciliationForm.businessType = 6
                this.reconciliationForm.sourceBillId = row.billId
                this.reconciliationForm.sourceBillNo = row.billNo
            }
            this.productTypeDis = true
            this.tableData = []
            this.reconciliationForm.creditCode = row.creditCode
            this.reconciliationForm.orgShort = row.shortCode
            this.reconciliationForm.supplierOrgId = row.storageOrgId
            this.reconciliationForm.purchasingOrgId = row.orgId
            this.reconciliationForm.purchasingOrgName = row.orgName
            this.reconciliationForm.taxRate = row.taxRate
            this.reconciliationForm.supplierName = row.storageName
            this.reconciliationForm.type = row.billType

            // 设置供应商ID，可能的字段名包括 supplierId, storageId, storageOrgId 等
            this.reconciliationForm.supplierId = row.supplierId || row.storageId || row.storageOrgId

            // 设置采购员信息
            this.reconciliationForm.purchaserId = row.purchaserId
            this.reconciliationForm.purchaserName = row.purchaserName

            console.log('确认选择订单时设置供应商和采购员信息:', {
                supplierName: row.storageName,
                supplierId: this.reconciliationForm.supplierId,
                storageOrgId: row.storageOrgId,
                purchaserId: this.reconciliationForm.purchaserId,
                purchaserName: this.reconciliationForm.purchaserName,
                完整订单数据: row
            })

            // 如果billType为空或未定义，设置默认值
            if (this.reconciliationForm.type == null || this.reconciliationForm.type === undefined) {
                // 默认设置为浮动价格对账
                this.reconciliationForm.type = 1
                console.log('确认选择时billType为空，设置默认对账类型为浮动价格:', this.reconciliationForm.type)
            }
            // 获取支付周期等
            this.getOrderInfo(row.orderId)
            this.showSelectContractOrPlan = false
        },
        currentChangeUser2 (currPage) {
            this.paginationInfo2.currentPage = currPage
            // 如果有选中的订单，使用新的API调用
            if (this.selectContractOrPlanSelectRowData.length > 0) {
                this.getReconcilableMaterialListByOrder(this.selectContractOrPlanSelectRowData)
            } else {
                this.getSiteReceivingTableDateM()
            }
        },
        sizeChangeUser2 (pageSize) {
            this.paginationInfo2.pageSize = pageSize
            // 如果有选中的订单，使用新的API调用
            if (this.selectContractOrPlanSelectRowData.length > 0) {
                this.getReconcilableMaterialListByOrder(this.selectContractOrPlanSelectRowData)
            } else {
                this.getSiteReceivingTableDateM()
            }
        },
        currentChangeUser (currPage) {
            this.paginationInfo.currentPage = currPage
            this.getContractOrPlanListM()
        },
        sizeChangeUser (pageSize) {
            this.paginationInfo.pageSize = pageSize
            this.getContractOrPlanListM()
        },
        currentChangeUser1 (currPage) {
            this.paginationInfo1.currentPage = currPage
            this.getOrderPlanByOrgNameListM()
        },
        sizeChangeUser1 (pageSize) {
            this.paginationInfo1.pageSize = pageSize
            this.getOrderPlanByOrgNameListM()
        },
        // 获取合同或计划编号
        getContractOrPlanListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.keywords != null) {
                params.keywords = this.keywords
            }
            if(this.reconciliationForm.reconciliationProductType != null) {
                params.productType = this.reconciliationForm.reconciliationProductType
            }
            if(this.reconciliationForm.purchasingOrgName != null) {
                params.orgName = this.reconciliationForm.purchasingOrgName
            }
            this.selectContractOrPlanLoading = true
            getReconciliablePlansBySupplier(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.contractOrPlanTableDate = res.list
            }).finally(() => {
                this.selectContractOrPlanLoading = false
            })
        },
        // 获取明细
        getCanUseSiteReceivingDtlM () {
            if(this.reconciliationForm.startEndTme.length == 0) {
                return
            }
            this.getSiteReceivingTableDateM()
            this.showSiteReceivingDia = true
        },
        saveSheetM (num) {
            console.log('保存对账单时的type值:', this.reconciliationForm.type)
            console.log('保存对账单时的供应商信息:', {
                supplierId: this.reconciliationForm.supplierId,
                supplierName: this.reconciliationForm.supplierName,
                supplierOrgId: this.reconciliationForm.supplierOrgId
            })
            console.log('保存对账单时的采购员信息:', {
                purchaserId: this.reconciliationForm.purchaserId,
                purchaserName: this.reconciliationForm.purchaserName
            })
            console.log('完整的reconciliationForm:', this.reconciliationForm)

            // 最后检查：如果type仍然为空，设置默认值
            if (this.reconciliationForm.type == null || this.reconciliationForm.type === undefined) {
                this.reconciliationForm.type = 1  // 默认为浮动价格对账
                console.log('保存时type为空，设置默认值为浮动价格对账:', this.reconciliationForm.type)
            }

            // 最后检查：如果供应商信息为空，尝试从表格数据中获取
            if (!this.reconciliationForm.supplierId && this.tableData.length > 0) {
                // 从第一行数据中尝试获取供应商信息
                const firstRow = this.tableData[0]
                if (firstRow.supplierId) {
                    this.reconciliationForm.supplierId = firstRow.supplierId
                    console.log('从表格数据中获取供应商ID:', firstRow.supplierId)
                }
                if (firstRow.supplierName && !this.reconciliationForm.supplierName) {
                    this.reconciliationForm.supplierName = firstRow.supplierName
                    console.log('从表格数据中获取供应商名称:', firstRow.supplierName)
                }
            }

            // 最后检查：如果采购员信息为空，尝试从表格数据中获取
            if (!this.reconciliationForm.purchaserId && this.tableData.length > 0) {
                const firstRow = this.tableData[0]
                if (firstRow.purchaserId) {
                    this.reconciliationForm.purchaserId = firstRow.purchaserId
                    console.log('从表格数据中获取采购员ID:', firstRow.purchaserId)
                }
                if (firstRow.purchaserName && !this.reconciliationForm.purchaserName) {
                    this.reconciliationForm.purchaserName = firstRow.purchaserName
                    console.log('从表格数据中获取采购员名称:', firstRow.purchaserName)
                }
            }

            if(this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if(t.quantity <= 0) {
                    return this.$message.error('序号为：【' + (i + 1) + '】的数量需要大于0！')
                }
                if(this.reconciliationForm.type == 1) {
                    if(t.freightPrice <= 0) {
                        return this.$message.error('序号为：【' + (i + 1) + '】的网价需要大于0！')
                    }
                    if(t.fixationPrice == null || t.fixationPrice <= 0) {
                        t.fixationPrice = 0
                    }
                }
                if(this.reconciliationForm.type == 2) {
                    if(t.outFactoryPrice <= 0) {
                        return this.$message.error('序号为：【' + (i + 1) + '】的出厂价需要大于0！')
                    }
                    if(t.transportPrice == null || t.transportPrice <= 0) {
                        t.transportPrice = 0
                    }
                }
            }
            this.reconciliationForm.dtl = this.tableData
            this.$refs.reconciliationFormRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要操作吗？', async () => {
                        this.formLoading = true
                        // 处理日期
                        this.reconciliationForm.startTime = this.reconciliationForm.startEndTme[0]
                        this.reconciliationForm.endTime = this.reconciliationForm.startEndTme[1]
                        this.reconciliationForm.isSubmit = num
                        materialReconciliationSTSupplierCreate(this.reconciliationForm).then(res => {
                            if(res.code == null && res.message == null) {
                                this.$message.success('操作成功')
                                if (this.reconciliationForm.type == 1) {
                                    this.$router.push({
                                        path: '/supplierSys/sheet/synthesizeTemporaryFDetail',
                                        query: {
                                            sn: res
                                        }
                                    })
                                }
                                if (this.reconciliationForm.type == 2) {
                                    this.$router.push({
                                        path: '/supplierSys/sheet/synthesizeTemporaryGDetail',
                                        query: {
                                            sn: res
                                        }
                                    })
                                }
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                }
            })
        },
        // 选择计划
        selectPlanClick () {
            this.selectContractOrPlanTableTitle = '选择计划'
            this.getContractOrPlanListM()
            this.showSelectContractOrPlan = true
        },
        // 选择合同
        selectContractClick () {
            this.selectContractOrPlanTableTitle = '选择合同'
            this.getContractOrPlanListM()
            this.showSelectContractOrPlan = true
        },
        // 删除单
        deleteM (row) {
            this.tableData = this.tableData.filter(t => {
                if(t.uuid != row.uuid) {
                    return true
                }else{
                    return  false
                }
            })
            // 删除行后重新计算总额
            this.calculateTaxAmountByRule()
        },
        // 拆单
        dismantleM (row) {
            // 插入到当前点击的下一个节点
            let insertIndex = this.tableData.length
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if(t.uuid == row.uuid) {
                    insertIndex = i + 1
                }
            }
            let newRow = {
                ...row,
                acceptanceAmount: this.fixed2(0),
                acceptanceNoRateAmount: this.fixed2(0),
                taxAmount: this.fixed2(0),
                quantity: 0,
                uuid: getUuid()
            }
            this.tableData.splice(insertIndex, 0, newRow)
        },

        // 表单变化
        getChangedRow (row) {
            // 处理数量
            this.disposeQuantityM(row)
            if(this.reconciliationForm.type == 1) {
                // 计算金额
                this.countAmountM()
            }
            if(this.reconciliationForm.type == 2) {
                // 计算金额
                this.gCountAmountM()
            }
        },
        taxRateChange () {
            if(this.reconciliationForm.taxRate == null || this.reconciliationForm.taxRate < 0) {
                this.reconciliationForm.taxRate = this.fixed2(0)
            }else if(this.reconciliationForm.taxRate >= 100 ) {
                this.reconciliationForm.taxRate = 100
            }else {
                this.reconciliationForm.taxRate = this.fixed2(this.reconciliationForm.taxRate)
            }
            // 浮动价格
            if(this.reconciliationForm.type === 1) {
                this.countAmountM()
            }
            // 固定价格
            if(this.reconciliationForm.type === 2) {
                this.gCountAmountM()
            }
        },
        gPriceChange (row) {
            // 一旦变化则是1
            row.updateType = 1
            // 处理出厂价
            this.disposeOutFactoryPriceM(row)
            // 处理运杂费
            this.disposeTransportPriceM(row)
            // 计算金额
            this.gCountAmountM()
        },
        priceChange (row) {
            // 一旦变化则是1
            row.updateType = 1
            // 处理固定费用
            this.disposeFixationPriceM(row)
            // 处理到店网价
            this.disposeFreightPriceM(row)

            // 网价等于含税单价，所以当网价变化时，更新含税单价
            if (row.freightPrice != null && row.freightPrice > 0) {
                row.price = row.freightPrice
            }

            // 计算金额
            this.countAmountM()
        },
        countAmountM () {
            // 自定义浮动价格计算逻辑：网价等于含税单价
            this.customFloatCountAmount()
        },

        // 自定义浮动价格计算方法，网价等于含税单价
        customFloatCountAmount () {
            let reconciliationAmount = 0
            let reconciliationNoRateAmount = 0
            let taxAmount = 0

            // 遍历每一行数据进行计算
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]

                // 网价等于含税单价，固定费用单独处理
                if (t.freightPrice != null && t.freightPrice > 0) {
                    t.price = this.fixed2(t.freightPrice)  // 网价就是含税单价
                } else {
                    t.price = this.fixed2(0)
                }

                // 确保固定费用有值
                if (t.fixationPrice == null || t.fixationPrice < 0) {
                    t.fixationPrice = 0
                }

                // 计算不含税单价
                const taxRate = parseFloat(this.reconciliationForm.taxRate) || 0
                if (taxRate > 0) {
                    t.noRatePrice = this.fixed2(t.price / (1 + taxRate / 100))
                } else {
                    t.noRatePrice = t.price
                }

                // 计算含税金额
                t.acceptanceAmount = this.fixed2(Number(t.quantity) * Number(t.price))

                // 计算不含税金额
                t.acceptanceNoRateAmount = this.fixed2(Number(t.quantity) * Number(t.noRatePrice))

                // 计算税额：税额 = 含税金额 - 不含税金额
                t.taxAmount = this.fixed2(t.acceptanceAmount - t.acceptanceNoRateAmount)

                // 累加总额
                reconciliationAmount = this.fixed2(Number(reconciliationAmount) + Number(t.acceptanceAmount))
                reconciliationNoRateAmount = this.fixed2(Number(reconciliationNoRateAmount) + Number(t.acceptanceNoRateAmount))
                taxAmount = this.fixed2(Number(taxAmount) + Number(t.taxAmount))
            }

            // 设置总额
            this.reconciliationForm.reconciliationAmount = reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = reconciliationNoRateAmount
            this.reconciliationForm.taxAmount = taxAmount
        },
        gCountAmountM () {
            let params = reconciliationFixCountAmount(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = params.tableData
            this.reconciliationForm.taxAmount = params.taxAmount
            this.reconciliationForm.reconciliationAmount = params.reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = params.reconciliationNoRateAmount

            // 根据规则重新计算税额：税额 = 含税金额 - 不含税金额
            this.calculateTaxAmountByRule()
        },
        // 计算对账单总额：通过累加对账单明细所有行的对应字段
        calculateTaxAmountByRule () {
            console.log('开始计算总额，表格数据:', this.tableData)

            let totalTaxAmount = 0           // 税额总计
            let totalReconciliationAmount = 0    // 含税总金额
            let totalReconciliationNoRateAmount = 0  // 不含税总金额

            // 遍历对账单明细表格数据，计算每行的税额和不含税金额，并累加总额
            this.tableData.forEach((row, index) => {
                console.log(`处理第${index + 1}行数据:`, row)

                // 安全地获取含税金额，确保是有效数字
                let acceptanceAmount = parseFloat(row.acceptanceAmount)
                if (isNaN(acceptanceAmount) || acceptanceAmount < 0) {
                    acceptanceAmount = 0
                }
                console.log(`第${index + 1}行含税金额:`, acceptanceAmount)

                // 如果不含税金额为空或为0，根据税率重新计算
                let acceptanceNoRateAmount = parseFloat(row.acceptanceNoRateAmount)
                if (isNaN(acceptanceNoRateAmount) || acceptanceNoRateAmount <= 0) {
                    const taxRate = parseFloat(this.reconciliationForm.taxRate) || 0
                    if (taxRate > 0) {
                        // 不含税金额 = 含税金额 / (1 + 税率/100)
                        acceptanceNoRateAmount = acceptanceAmount / (1 + taxRate / 100)
                        row.acceptanceNoRateAmount = this.safeFixed2(acceptanceNoRateAmount)

                        // 同时更新不含税单价
                        const quantity = parseFloat(row.quantity) || 0
                        if (quantity > 0) {
                            row.noRatePrice = this.safeFixed2(acceptanceNoRateAmount / quantity)
                        }
                    } else {
                        acceptanceNoRateAmount = acceptanceAmount
                        row.acceptanceNoRateAmount = this.safeFixed2(acceptanceNoRateAmount)
                        row.noRatePrice = row.price || 0
                    }
                }
                console.log(`第${index + 1}行不含税金额:`, acceptanceNoRateAmount)

                // 税额 = 含税金额 - 不含税金额，保留2位小数点
                const taxAmount = acceptanceAmount - acceptanceNoRateAmount
                row.taxAmount = this.safeFixed2(taxAmount)
                console.log(`第${index + 1}行税额:`, row.taxAmount)

                // 累加所有行的金额到总额
                totalReconciliationAmount += acceptanceAmount
                totalReconciliationNoRateAmount += acceptanceNoRateAmount
                totalTaxAmount += taxAmount
            })

            // 安全地设置总额，避免NaN
            this.reconciliationForm.reconciliationAmount = this.safeFixed2(totalReconciliationAmount)  // 含税总金额
            this.reconciliationForm.reconciliationNoRateAmount = this.safeFixed2(totalReconciliationNoRateAmount)  // 不含税总金额
            this.reconciliationForm.taxAmount = this.safeFixed2(totalTaxAmount)  // 税额总计

            console.log('计算完成，最终结果:')
            console.log('含税总金额:', this.reconciliationForm.reconciliationAmount)
            console.log('不含税总金额:', this.reconciliationForm.reconciliationNoRateAmount)
            console.log('税额总计:', this.reconciliationForm.taxAmount)
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 安全的fixed2方法，处理NaN值
        safeFixed2 (num) {
            if (isNaN(num) || num === null || num === undefined) {
                return 0
            }
            return parseFloat(toFixed(num, 2))
        },
        disposeOutFactoryPriceM (row) {
            if(row.outFactoryPrice <= 0 || row.outFactoryPrice >= this.maxNum) {
                row.outFactoryPrice = this.fixed2(0)
            }else {
                row.outFactoryPrice = this.fixed2(row.outFactoryPrice)
            }
        },
        disposeTransportPriceM (row) {
            if(row.transportPrice <= 0 || row.transportPrice >= this.maxNum) {
                row.transportPrice = this.fixed2(0)
            }else {
                row.transportPrice = this.fixed2(row.transportPrice)
            }
        },
        // 处理固定费用
        disposeFixationPriceM (row) {
            if(row.fixationPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.fixationPrice = this.fixed2(0)
            }else {
                row.fixationPrice = this.fixed2(row.fixationPrice)
            }
        },
        // 处理到货网价
        disposeFreightPriceM (row) {
            if(row.freightPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.freightPrice = this.fixed2(0)
            }else {
                row.freightPrice = this.fixed2(row.freightPrice)
            }
        },
        // 处理数量
        disposeQuantityM (row) {
            if(row.quantity <= 0) {
                return row.quantity = this.fixed4(0)
            }
            // 计算最大值
            let maxNum = this.fixed4(row.maxQuantity)
            if(row.quantity >= maxNum) {
                row.quantity = this.fixed4(maxNum)
            }else {
                row.quantity = this.fixed4(row.quantity)
            }
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'reconciliationDtl']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
    appearance: textfield !important;
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 500px;
        margin-top: 0px;
    }
}
/deep/ .el-dialog.dlg {
    height: 600px;

    .el-dialog__header {
        margin-bottom: 0;
    }

    .el-dialog__body {
        height: 474px;
        margin: 10px;
        display: flex;

        & > div {
            .e-pagination {
                background-color: unset;
            }

            //height: 670px;
            .title {
                height: 22px;
                margin-bottom: 10px;
                padding-left: 26px;
                text-align: left;
                line-height: 22px;
                color: #2e61d7;
                font-weight: bold;
                position: relative;
                display: flex;

                &::before {
                    content: '';
                    display: block;
                    width: 10px;
                    height: inherit;
                    border-radius: 5px;
                    background-color: blue;
                    position: absolute;
                    left: 10px;
                    top: 0;
                }
            }
        }

        .el-input__inner {
            border: 1px solid blue;
            border-radius: 6px;
        }

        .el-input__suffix {
            width: 20px;
        }

        .e-table {
            flex-grow: 1;

            .table {
                height: 100%;
            }
        }

        .box-left {
            width: 660px;
            display: flex;
            flex-direction: column;
            .top {
                box-shadow: unset;
            }
        }

        .box-right {
            flex-grow: 1;
            display: flex;
            flex-direction: column;

            & > div {
                display: flex;
                flex-direction: column;
            }

            .top {
                justify-content: left;
                border-radius: 0;
                box-shadow: unset;
            }

            .bottom {
                flex-grow: 1;
            }
        }
    }

    .el-dialog__footer {
        background-color: #eff2f6;
    }
}
/deep/ #myTable .el-table__header-wrapper  .el-checkbox{
    display:none
}

/* 对账单明细表格水平滚动条支持 */
.table-container {
    overflow-x: auto;
    width: 100%;
}

.table-container .el-table {
    min-width: 1200px; /* 设置最小宽度，确保列数过多时可以水平滚动 */
}
</style>