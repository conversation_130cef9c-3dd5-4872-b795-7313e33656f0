<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs tab-position="left" @tab-click="onChangeTab">
                <el-tab-pane label="合同详情" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="物资采购合同清单" name="contractdtl" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!--         合同详情 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">合同详情</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="招标名称：">
                                            <span>{{ formData.applyName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="合同编号：">
                                            <span>{{ formData.billNo }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="合同名称：">
                                            <span>{{ formData.name }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="公司名称：">
                                            <span>{{ formData.aname }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="否需要缴纳履约保证金：">
                                            <span v-show="formData.performanceBond==1">是</span>
                                            <span v-show="formData.performanceBond==0">否</span>

                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="包件段名称：">
                                            <span>{{ formData.contractSectionName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="不含税金额：">
                                            <span>{{ formData.auditNoTaxAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="押金：">
                                            <span>{{ formData.deposit }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="税率%：">
                                            <span>{{ formData.tax }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="税额：">
                                            <span>{{ formData.taxAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="预付款：">
                                            <span>{{ formData.scprepaidAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="税额：">
                                            <span>{{ formData.taxAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="合同金额：">
                                            <span>{{ formData.amount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="签约人：">
                                            <span>{{ formData.signatory }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="签订地点：">
                                            <span>{{ formData.signPlace }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="签订日期：">
                                            <span>{{ formData.billDate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="合同有效期：">
                                            <span>{{ formData.termValidity }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="公证时间：">
                                            <span>{{ formData.notaryDate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!--合同明细-->
                    <div id="floorInfoCon" class="con">
                        <div class="tabs-title" id="contractdtl">合同服务明细</div>
                        <div class="e-table" style="background-color: #ffffff">
                            <div class="top">
                                <!-- 新增按钮 -->
                                <div class="left">
                                    <div class="left-btn">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <el-table @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow"
                                          v-loading="isLoading" border style="width: 100%"
                                          :data="formData.materialPurchaseList" class="accountStatementDtlTable"
                                          :max-height="$store.state.tableHeight"
                                          @selection-change="handleSelectionChange">
                                    <el-table-column type="selection" width="40"></el-table-column>
                                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                                    <el-table-column label="物资编号" width="100" prop="itemCode"></el-table-column>
                                    <el-table-column label="物资名称" width="100" prop="itemName"></el-table-column>
                                    <el-table-column label="顶级类别名称" width="100" prop="topClassName"></el-table-column>
                                    <el-table-column label="材料类型名称" width="" prop="materialTypeName"></el-table-column>
                                    <el-table-column label="材质" width="" prop="spec"></el-table-column>
                                    <el-table-column label="数量" width="100" prop="qty"></el-table-column>
                                    <el-table-column label="单价" width="" prop="price"></el-table-column>
                                    <el-table-column label="规格" width="100" prop="model"></el-table-column>
                                    <el-table-column label="单位" width="" prop="unit"></el-table-column>
                                    <el-table-column label="未收数量" width="100"
                                                     prop="noReceiveNumber"></el-table-column>
                                    <el-table-column label="已收数量" width="100"
                                                     prop="receiveNumber"></el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                    <!--&lt;!&ndash;         合同服务明细 &ndash;&gt;-->
                    <!--          <div id="floorInfoCon" class="con">-->
                    <!--            <div class="tabs-title" id="contractMaterialsDtl">合同服务明细</div>-->
                    <!--            <div class="e-table" style="background-color: #ffffff">-->
                    <!--              <div class="top">-->
                    <!--                &lt;!&ndash; 新增按钮 &ndash;&gt;-->
                    <!--                <div class="left">-->
                    <!--                  <div class="left-btn">-->
                    <!--                  </div>-->
                    <!--                </div>-->
                    <!--              </div>-->
                    <!--              <div>-->
                    <!--                <el-table @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" v-loading="isMaterialLoading" border style="width: 100%" :data="contractMaterialsDtlTable" class="accountStatementDtlTable" :max-height="$store.state.tableHeight"-->
                    <!--                          @selection-change="handleSelectionChange">-->
                    <!--                  <el-table-column type="selection" width="40"></el-table-column>-->
                    <!--                  <el-table-column label="序号" type="index" width="60"></el-table-column>-->
                    <!--                  <el-table-column label="物资名称" width="100" prop="materialName"></el-table-column>-->
                    <!--                  <el-table-column label="原数量" width="100" prop="oriQuantity"></el-table-column>-->
                    <!--                  <el-table-column label="原单价" width="100" prop="oriUnitPrice"></el-table-column>-->
                    <!--                  <el-table-column label="数量" width="100" prop="quantity"></el-table-column>-->
                    <!--                  <el-table-column label="服务类型名称" width="100" prop="typeName"></el-table-column>-->
                    <!--                  <el-table-column label="单价" width="100" prop="unitPrice"></el-table-column>-->
                    <!--                  <el-table-column label="状态" width="100" prop="changeState">-->
                    <!--                    <template scope="scope">-->
                    <!--                      <span v-show="scope.row.changeState==0">未变更</span>-->
                    <!--                      <span v-show="scope.row.changeState==1">新增</span>-->
                    <!--                      <span v-show="scope.row.changeState==2">编辑</span>-->
                    <!--                      <span v-show="scope.row.changeState==-1">删除</span>-->
                    <!--                    </template>-->
                    <!--                  </el-table-column>-->
                    <!--                </el-table>-->
                    <!--              </div>-->
                    <!--            </div>-->
                    <!--          </div>-->
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button type="primary" v-if="formData.auditStatus === 2" @click="changeAuditState(1)"
                       class="btn-greenYellow">审核通过
            </el-button>
            <el-button type="primary" v-if="formData.auditStatus === 2" @click="changeAuditState(2)" class="btn-delete">
                审核未通过
            </el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { mapState, mapMutations } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import { wzContractById } from '@/api/supplierSys/contract/pcwp1Contract'

export default {

    data () {
        return {
            contractdtlDtlTable: [], //合同明细
            contractMaterialsDtlTable: [],
            isLoading: false,
            isMaterialLoading: false,
            //基本信息表单数据
            formData: {},
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedInfo: [],

        }
    },
    components: {},
    created () {
        this.billId = this.$route.params.billId
        // var temp = $.parseJSON(this.formData.address)
        // this.getcontractMaterialsDtlTable(this.formData.contractId)
        // this.getTableDtlData(this.formData.contractId)
        // this.formData.address = temp.address + temp.detail
        this.getDataById()

    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'enterpriseInfo', 'adminInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        getDataById () {
            console.log(this.billId)
            wzContractById({ billId: this.billId }).then(res => {
                this.formData = res
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },

        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 消息提示

    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}
</style>
