<template>
  <div class="base-page">
    <div class="right" v-show="viewList === true">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn">
              <el-button type="primary" @click="onWriter" >写消息</el-button>
            </div>
            <div>
              <el-select  @change="getsendList" v-model="filterData.allRead"  >
                <el-option v-for="item in allReads" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>

          </div>
          <div class="search_box">
            <el-input type="text" @keyup.enter.native="getsendList" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png"
                                                                                                                         slot="suffix" @click="getsendList" /></el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>

      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" :style="{ width: '100%' }">
        <el-table class="table" :height="rightTableHeight" :data="sendMessageTable" border highlight-current-row
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""></span>
            </template>
          </el-table-column>
          <el-table-column label="标题" width="" prop="title">
            <template slot-scope="scope">
              <span class="action" @click="handleView(scope.row)">{{scope.row.title}}</span>
            </template>
          </el-table-column>
          <!--          <el-table-column label="收件人消息账号" width="" prop="receiveCode">-->
          <!--          </el-table-column>-->
          <el-table-column label="是否全读" width="120" >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.allRead==1" type="success">已读</el-tag>
              <el-tag v-else >未读</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="发送时间" width="" prop="sendDate"/>
        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                     :currentPage.sync="pages.currPage"
                     @currentChange="currentChangeUser" @sizeChange="sizeChangeUser"
      />
    </div>

    <!--      写消息-->
    <div class="right" v-show="viewList !== true"  >
      <div class="e-form" style="padding: 0 10px 10px;" v-if="viewList === 'class'">
        <div class="tabs-title">{{action}}</div>
        <el-form :rules="formRules" ref="writer" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="发送用户：" prop="receiveName">
                <el-input disabled v-model="formData.receiveUserListTitle"></el-input>
                <el-button size="mini"
                           type="primary"
                           @click="importDeviceSelect()">选择</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发送商铺：" prop="shopName">
                <el-input disabled v-model="formData.receiveShopListTitle"></el-input>
                <el-button size="mini"
                           type="primary"
                           @click="shopDialog">选择</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <!--          发送消息给用户-->
          <el-row >
            <el-col :span="24">
              <el-form-item label="消息标题：" prop="title">
                <el-input v-model="formData.title" placeholder="请输入标题"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row >
            <el-col class="editorCol" :span="24" >
              <el-form-item label="消息内容：" prop="content" >
                <editor v-model="formData.content"></editor>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="e-form" style="padding: 0 10px 10px;" v-if="viewList === 'watch'">
        <div class="tabs-title">{{action}}</div>
        <el-form  ref="writer" :model="messageInfo" label-width="150px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="收件人名称：" prop="sendDate">
                <span>{{ messageInfo.receiveName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标题：" prop="title" >
                <span >{{ messageInfo.title }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="收件人类型：" prop="sendDate">
                <span v-show="messageInfo.receiveName==0">店铺</span>
                <span v-show="messageInfo.receiveName==1">用户</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收件人消息账号：" prop="title">
                <span>{{ messageInfo.receiveCode }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否已读：" prop="sendDate">
                <span v-if="messageInfo.isRead==0">未读</span>
                <span v-else-if="messageInfo.isRead==1" >已读</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收件人消息账号：" prop="title">
                <span>{{ messageInfo.receiveCode }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row >
            <el-col class="editorCol" :span="24" >
              <el-form-item label="消息内容：" prop="content" >
                <span  >{{ messageInfo.content }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="footer">
        <div class="buttons">
          <el-button type="primary" @click="viewList=true">返回</el-button>
          <el-button v-if="viewList === 'class'" type="primary" @click="sendMessage">发送</el-button>
        </div>
      </div>
    </div>
    <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="false" :before-close="closeDialog">
      <el-form :model="receiveMessagefifter" ref="form" label-width="120px" :inline="false" size="normal">
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="是否全读：">
              <el-select v-model="filterData.allRead" >
                <el-option v-for="item in allReads" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" :offset="0">
            <el-form-item label="标题：">
              <el-input v-model="filterData.title" placeholder="请输入标题" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="发送时间：">
              <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.dateValue" type="datetimerange"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="expertSearch">确定</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
    </el-dialog>

    <!--        选择用户库-->
    <el-dialog title="选择收件人" :v-show="formData.receiveType==1" v-loading="inventoryTableLoading" v-dialogDrag  :visible.sync="receviceDialog"  width="70%" style="margin-left: 20%;" :close-on-click-modal="true">
      <div class="e-table"  style="background-color: #fff">
        <div class="top" style="height: 50px; padding-left: 10px">

          <div class="left-btn">
            <el-button type="primary" size="small" @click=" selectUserAll">全部选择</el-button>
          </div>

          <div class="search_box">
            <el-input type="text" @keyup.enter.native="getUserList" placeholder="输入搜索关键字" v-model="userList.keywords"><img src="@/assets/search.png" slot="suffix" @click="getUserList" /></el-input>
          </div>
        </div>
        <el-table ref="tableRef"
                  highlight-current-row
                  border
                  style="width: 100%"
                  :data="userList.tableData"
                  class="table"
                  @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column prop="userNumber" label="用户编号" width="200"></el-table-column>
          <el-table-column prop="realName" label="真实姓名" width="200"></el-table-column>
          <el-table-column prop="nickName" label="昵称" width=""></el-table-column>
          <el-table-column prop="userMobile" label="手机号码" width="200"></el-table-column>
          <el-table-column prop="state" label="用户状态" width="">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.state==0">初始</el-tag>
              <el-tag v-else-if="scope.row.state==1" >启用</el-tag>
              <el-tag v-else-if="scope.row.state==2" >禁用</el-tag>
              <el-tag v-else type="danger">已关闭</el-tag>
            </template>
          </el-table-column>
          <!--          <el-table-column prop="userImg" label="头像" width=""></el-table-column>-->

        </el-table>
      </div>
      <span slot="footer">
                    <Pagination
                        v-show="userList.tableData != null || userList.tableData.length != 0"
                        :total="userList.paginationInfo.total"
                        :pageSize.sync="userList.paginationInfo.pageSize"
                        :currentPage.sync="userList.paginationInfo.currentPage"
                        @currentChange="currentChangeUser" @sizeChange="sizeChangeUser"

                    />
                    <el-button style="margin-top: 20px" @click="receviceDialog = false">取消</el-button>
                    <el-button style="margin-top: 20px" @click="onUserList">确定</el-button>
                </span>
    </el-dialog>

    <!--   选择商铺发送-->
    <el-dialog v-dialogDrag title="选择商铺" :v-show="formData.receiveType==0" v-loading="inventoryTableLoading"  :visible.sync="showDeviceDialog"  width="70%" style="margin-left: 20%;" :close-on-click-modal="true">
      <div class="e-table"  style="background-color: #fff">
        <div class="top" style="height: 50px; padding-left: 10px">
          <div class="left">
            <div class="left-btn">
              <el-button type="primary" size="small" @click="selectShopAll">全部选择</el-button>
            </div>
          </div>
          <div class="search_box">
            <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
            <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
            <el-input type="text" @keyup.enter.native="getshopLists" placeholder="输入搜索关键字" v-model="userList.keywords"><img src="@/assets/search.png" slot="suffix" @click="getshopLists" /></el-input>
          </div>
        </div>

        <el-table ref="tableRef" highlight-current-row border
                  style="width: 100%"
                  :data="shopList.tableData"
                  class="table"
                  @currentChange="currentChangeUser" @sizeChange="sizeChangeUser"
                  @selection-change="handleSelectionChangeShop"

        >
          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column prop="serialNum" label="序列号" width="200"></el-table-column>
          <el-table-column prop="shopName" label="店铺名称" width="200"></el-table-column>
          <el-table-column prop="shopType" label="店铺类型" width="">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.shopType==0">个体户</el-tag>
              <el-tag v-else-if="scope.row.shopType==1" >企业</el-tag>
              <el-tag v-else-if="scope.row.shopType==2" >个人</el-tag>
              <el-tag v-else type="danger">已关闭</el-tag>
            </template>
          </el-table-column>
          <!--          <el-table-column prop="shopImg" label="店铺log" width="200"></el-table-column>-->
          <el-table-column prop="detailedAddress" label="详细地址" width=""></el-table-column>
          <el-table-column prop="state" label="店铺状态" width="">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.state==0">停用</el-tag>
              <el-tag v-else-if="scope.row.state==1" >启用</el-tag>
              <el-tag v-else type="danger">已关闭</el-tag>
            </template>
          </el-table-column>

        </el-table>
      </div>
      <span slot="footer">
                    <Pagination
                        v-show="shopList.tableData != null || shopList.tableData.length != 0"
                        :total="shopList.paginationInfo.total"
                        :pageSize.sync="shopList.paginationInfo.pageSize"
                        :currentPage.sync="shopList.paginationInfo.currentPage"
                        @currentChange="getShopList"
                        @sizeChange="getShopList"
                    />
                    <el-button style="margin-top: 20px" @click="showDeviceDialog = false">取消</el-button>
                    <el-button style="margin-top: 20px" @click="getShopList">确定</el-button>
                </span>
    </el-dialog>
  </div>

</template>
<script>
import { sendMessageApi, getList, del, getMessageList } from '@/api/platform/mail/outbox'
import Pagination from '@/components/pagination/pagination'
import { getUserList } from '@/api/platform/user/userInquire'
import { getShopPassList } from '@/api/platform/shop/shopAudit'
// import { showLoading } from '@/utils/common'
import ComPagination from '@/components/pagination/pagination.vue'
import editor from '../../../../components/quillEditor'
export default {
    components: {
        ComPagination,
        editor,
        Pagination
    },
    data () {
        return{
            allReads: [
                {
                    value: null,
                    label: '全部'
                },
                {
                    value: 0,
                    label: '未全读'
                },
                {
                    value: 1,
                    label: '已读'
                }],
            // 数据加载
            keywords: null,

            action: '',
            formLoading: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            filterData: {
                title: null,
                allRead: null,
                dateValue: [], // 开始时间和结束时间
                orderBy: 1,
            },
            receiveMessagefifter: {
                title: null,
            },
            queryVisible: false,
            messageInfo: {},
            tableData: [],
            sendMessageTable: [], //接收消息列表
            viewList: true,
            stationMessageInfo: {},
            receiveUserList: [], //发送用户id集合
            receiveShopList: [],  //发送商铺id集合
            formRules: {
                title: { required: true, message: '请输入标题', trigger: 'blur' },
                content: { required: true, message: '请输入发送消息', type: 'url', trigger: 'blur' },
            },

            showDeviceDialog: false, // 选择收件商铺弹窗,
            receviceDialog: false, // 选择收件用户弹窗库
            formData: {
                receiveType: '', // 收件人类型0店铺 1用户2平台
                receiveUserList: [], //收件人,
                receiveShopList: [], //收件人
                receiveUserListTitle: [], //用户展示字段收件人,
                receiveShopListTitle: [], //商铺展示字段收件人,
                title: null, //标题
                state: 1,
                content: null
            },
            //用户数据
            userList: {
                tableData: [
                ],
                keywords: null,
                paginationInfo: { // 分页
                    total: 20,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            pages: {
                currPage: 1,
                pageSize: 20,
                totalCount: 200
            },
            shopList: {
                tableData: [
                ],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },

        }
    },
    created () {
        this.getsendList()
    },
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    methods: {
        expertSearch () {
            this.keywords = null
            this.getsendList()
            //重置数据
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.title = null// 选中的标题
            this.filterData.allRead = null
            this.filterData.receiveName = null
            this.queryVisible = false
        },
        onWriter () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '编写消息'
        },
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },

        currentChangeUser (currPage) {
            this.userList.paginationInfo.currentPage = currPage
            this.getUserList()
        },
        sizeChangeUser (pageSize) {
            this.userList.paginationInfo.pageSize = pageSize
            this.getUserList()
        },
        currentChangeShop (currPage) {
            this.shopList.paginationInfo.currentPage = currPage
            this.getsendList()
        },
        sizeChangeshop (pageSize) {
            this.shopList.paginationInfo.pageSize = pageSize
            this.getUserList()
        },
        // 详情
        handleView (row) {
            console.log('详情', row)
            //利用$router.push进行跳转
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/mail/outBoxDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'outBoxDetail',
                params: {
                    row: row
                }
            })
        },
        //收件人页码跳转
        currentChange (currPage) {
            this.pages.currPage = currPage
            this.getsendList()
        },
        sizeChange (pageSize) {
            console.log('sizeChange', pageSize)
            this.pages.pageSize = pageSize
            this.getsendList()
        },

        //批量删除发件信息
        handleDelete () {
            console.log('bb')
        },
        //收件人关键字查询
        onSearch () {
            console.log('ss')
        },
        /**
     * 删除发送消息
     *
     */
        onDel (scope) {
            this.clientPop('info', `您确定要删除标题为：${scope.row.title}吗？`, async () => {
                // showLoading()
                del({ id: scope.row.stationMessageId }).then(res => {
                    this.message(res)
                    this.getsendList()
                })

                // showLoading()
            })
        },
        /**
     * 查询所有收件人列表
     *
     * 消息列表
     */
        getsendList () {
            console.log('33')
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                sendType: 2
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.filterData.allRead != null) {
                params.allRead = this.filterData.allRead
            }
            if (this.filterData.title != null && this.filterData.title != '') {
                params.title = this.filterData.title
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0],
                params.endDate = this.filterData.dateValue[1]
            }

            getMessageList(params).then(res=>{
                this.sendMessageTable = res.list
                this.pages = res
            })
        },
        /**
     * 确定发送商铺id集合
     */

        getShopList () {
            this.formData.receiveShopList = this.receiveShopList,
            this.showDeviceDialog = false
        },
        /**
     * 确定发送用户id集合
     */
        onUserList () {
            this.formData.receiveUserList = this.receiveUserList,
            this.receviceDialog = false
        },
        // 收件人信息高级查询
        advancedQuery () {
            this.keywords = null
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                orderBy: this.filterData.orderBy,
                title: this.receiveMessagefifter.title,
            }
            getList(params).then(res=>{
                this.message(res)
                this.sendMessageTable = res.list
                this.pages.total = res.totalCount
                this.pages.pageSize = res.pageSize
                this.pages.currentPage = res.currPage
                this.queryVisible = false
            })

        },
        //发送消息
        sendMessage () {
            let  params = {
                title: this.formData.title,
                content: this.formData.content,
                receiveList: this.formData.receiveUserList,
                shopIdList: this.formData.receiveShopList,
                sendType: 0
            }
            sendMessageApi(params).then(res=>{
                this.clientPop('suc', res.message, () => {
                    this.formData = {}
                    this.viewList = true
                    this.getsendList()
                })
            })
        },
        //获取用户信息 关键字搜索
        getUserList () {
            let params = {
                page: this.userList.paginationInfo.currentPage,
                limit: this.userList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            console.log('获取用户信息 关键字搜索', params, this.userList.keywords )
            if(this.userList.keywords != null) {
                params.keyword = this.userList.keywords
            }
            this.inventoryTableLoading = true
            getUserList(params).then(res=>{
                this.userList.tableData = res.list
                this.userList.paginationInfo.currentPage = res.currPage
                this.userList.paginationInfo.pageSize = res.pageSize
                this.userList.paginationInfo.total = res.totalCount

            })
            this.inventoryTableLoading = false
        },

        //获取商铺信息
        getshopLists () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            if(this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            this.inventoryTableLoading = true
            getShopPassList(params).then(res=>{
                this.shopList.tableData = res.list
                this.shopList.paginationInfo.currentPage = res.currPage
                this.shopList.paginationInfo.pageSize = res.pageSize
                this.shopList.paginationInfo.total = res.totalCount

            })
            this.inventoryTableLoading = false
        },
        //获取用户星系
        getDeviceInventory () {
            console.log('dddd')
        },
        //选择全部用户
        selectUserAll () {
            let params = {
                page: this.userList.paginationInfo.currentPage,
                limit: this.userList.paginationInfo.total,
                orderBy: this.filterData.orderBy,
            }
            console.log('//选择全部用户', params)
            if (this.userList.keywords != '' || this.userList.keywords != null) {
                params.keywords = this.userList.keywords
            }
            getUserList(params).then(res=>{
                this.handleSelectionChange(res.list)
            })

        },
        //选择全部商铺
        selectShopAll () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.total,
                orderBy: this.filterData.orderBy,
            }
            if(this.shopList.keywords != null) {
                params.keyword = this.shopList.keywords
            }
            getShopPassList(params).then(res=>{
                this.handleSelectionChangeShop(res.list)
            })
        },

        // 打开收件人弹窗
        importDeviceSelect () {
            this.receviceDialog = true
            this.formData.receiveType = 1
            this.getUserList()

        },
        // 打开商店弹窗
        shopDialog () {
            this.showDeviceDialog = true
            this.formData.receiveType = 0
            this.getshopLists()
        },
        // addUserList () {
        //     this.showDeviceDialog = false
        // },
        handleSelectionChange (list) {
            if (list.length > 0) {
                this.receiveUserList = list.map(item => {
                    return item.userId
                })
                this.formData.receiveUserListTitle = list.map(item => {
                    return item.userId + '[' + item.realName + ']'
                })

            }

        },
        /**
     * 表格勾选
     */

        handleCurrentChange (val) {
            console.log('handleCurrentChange', val)
            this.currentRow = val
        },
        //收集表格数据
        handleSelectionChangeShop (list) {
            if (list.length > 0) {
                this. formData.receiveShopList = list.map(item => {
                    return item.shopId
                })
                this.formData.receiveShopListTitle = list.map(item => {
                    return item.shopId + '[' + item.shopName + ']'
                })

            }

        },
        // 消息提示
        message (res) {
            if (res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            } else {
                this.$message({
                    message: res.message,
                    type: 'error'
                })
            }
        },
    }
}
</script>
<style lang="scss" scoped>
/deep/ .el-col.editorCol{
  .el-form-item__content {
    height: unset !important;
  }
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
</style>
