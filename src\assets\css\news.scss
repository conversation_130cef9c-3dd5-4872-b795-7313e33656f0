.root {
  height: 100%;
  padding: 20px 0;
  background-color: #f5f5f5;

  .box {
    width: 1326px;
    height: 100%;
    background-color: #fff;
  }
}

.list-item {
  height: 138px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);
  &>div {height: 100%;}
  .date {
    width: 126px;
    padding: 43px 0 0 30px;
    font-size: 14px;
    color: rgba(153, 153, 153, 1);
  }
  .content {
    padding-top: 40px;
    p{
      font-size: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      ////能够显示的行数，超出部分用...表示
      //-webkit-box-orient: vertical;
    }
  }
  h3 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 10px;
    cursor: pointer;
    &:hover {color: rgba(33, 110, 198, 1);}
  }
  p {
    width: 1030px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    color: rgba(153, 153, 153, 1);
  }
}
.pagination {
  height: 100px;
  justify-content: right;
  color: rgba(153, 153, 153, 1);
  font-size: 16px;
  span, button, input {color: rgba(0, 0, 0, 1);}
  input {
    width: 40px;
    height: 40px;
    text-align: center;
    border: 1px solid rgba(230, 230, 230, 1);
  }
}