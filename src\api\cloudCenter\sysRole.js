import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service
//删除菜单
const deleteById = params => {
    return httpGet({
        url: '/cloudCenter/sysRole/delete',
        params,
    })
}
//修改菜单状态
const updateState = params => {
    return httpPost({
        url: '/cloudCenter/sysRole/updateState',
        params,
    })
}
//修改菜单
const update = params => {
    return httpPost({
        url: '/cloudCenter/sysRole/update',
        params
    })
}

//登录时查看本机构招标信息
const getDateList = params => {
    return httpPost({
        url: '/cloudCenter/sysRole/listByEntity',
        params,
    })
}

//批量修改角色排序值
const changeSortValue = params => {
    return httpPost({
        url: '/cloudCenter/sysRole/changeSortValue',
        params,
    })
}

const getMenuAndSysRoleDateList = params => {
    return httpGet({
        url: '/cloudCenter/sysRole/getMenuAndSysRoleDateList',
        params,
    })
}

//根据招标id查询分包信息
const save = params => {
    return httpPost({
        url: '/cloudCenter/sysRole/create',
        params
    })
}
//查询系统当前节点所有父级订单
const saveMenuAndSysRole = params => {
    return httpPost({
        url: '/cloudCenter/sysRole/saveMenuAndSysRole',
        params
    })
}
export {
    update,
    save,
    getDateList,
    updateState,
    deleteById,
    saveMenuAndSysRole,
    changeSortValue,
    getMenuAndSysRoleDateList

}