export default [
    {
        path: '/shopManage',
        component: () => import('@/pages/shopManage/index'),
        redirect: '/shopManage/product/inBox',
        children: [
            // 店铺信息
            {
                path: '/shopManage/shop/shopInfoManage',
                // name: 'shopInfoManage',
                component: () => import('@/pages/shopManage/shop/shopInfoManage'),
                meta: {
                    title: '店铺信息'
                }
            },
            // 我的订单
            {
                path: '/shopManage/order/searchOrder',
                // name: 'searchOrder',
                component: () => import('@/pages/shopManage/order/searchOrder'),
                meta: {
                    keepAlive: true,
                    title: '店铺订单'
                }
            },
            {
                path: '/shopManage/order/searchOrderDetail',
                // name: 'shopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/detail'),
                meta: {
                    title: '店铺订单详情'
                }
            },
            {
                path: '/shopManage/order/ordDetailDetail',
                // name: 'shopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/ordDetail'),
                meta: {
                    title: '店铺订单详情'
                }
            },
            {
                path: '/shopManage/order/smallShipTwo/searchOrder',
                // name: 'searchOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/smallShip/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '零星采购店铺订单'
                }
            },
            // {
            //     path: '/shopManage/order/smallShipTwo/searchOrderDetail',
            //     // name: 'smallShipShopManageSearchOrderDetail',
            //     component: () => import('@/pages/shopManage/order/searchOrder/smallShipTwo/detail'),
            //     meta: {
            //         title: '零星采购订单详情'
            //     }
            // },
            {
                path: '/shopManage/order/shipedDtl/searchOrderDetail',
                // name: 'shipedDtlShopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/shiped/shipdtl.vue'),
                meta: {
                    title: '零星采购发货单项详情'
                }
            },
            {
                path: '/shopManage/order/shiped/searchOrder',
                // name: 'shipedSearchOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/shiped/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '店铺订单'
                }
            },

            {
                path: '/shopManage/order/shiped/searchOrderDetail',
                // name: 'shipedShopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/shiped/detail'),
                meta: {
                    title: '零星采购订单详情'
                }
            },
            {
                path: '/shopManage/mail/outBoxDetail',
                // name: 'outBoxDetail',
                component: () => import('@/pages/shopManage/mail/outBox/detail'),
                meta: {
                    title: '收件箱详情'
                }
            },      {
                path: '/shopManage/product/inBox',
                component: () => import('@/pages/shopManage/mail/inBox/index'),
                meta: {
                    keepAlive: true,
                    title: '收件箱'
                }
            },
            {
                path: '/shopManage/mail/outBox',
                component: () => import('@/pages/shopManage/mail/outBox/index'),
                meta: {
                    keepAlive: true,
                    title: '发件箱'
                }
            },

            {
                path: '/shopManage/product/materialManage',
                component: () => import('@/pages/shopManage/product/materialManage/index'),
                meta: {
                    keepAlive: true,
                    title: '出售中物资'
                }
            },
            {
                path: '/shopManage/product/materialManageDetail',
                // name: 'materialManageDetail',
                component: () => import('@/pages/shopManage/product/materialManage/detail'),
                meta: {
                    title: '物资详情'
                }
            },
            {
                path: '/shopManage/product/materialCheck',
                component: () => import('@/pages/shopManage/product/materialCheck/index'),
                meta: {
                    keepAlive: true,
                    title: '审核的物资'
                }
            },
            {
                path: '/shopManage/product/materialCheckDetail',
                // name: 'materialCheckDetail',
                component: () => import('@/pages/shopManage/product/materialCheck/detail'),
                meta: {
                    title: '审核物资详情'
                }
            },
            {
                path: '/shopManage/product/materialWarehouse',
                component: () => import('@/pages/shopManage/product/materialWarehouse/index'),
                meta: {
                    keepAlive: true,
                    title: '仓库中的商品'
                }
            },
            {
                path: '/shopManage/product/materialWarehouseDetail',
                // name: 'materialWarehouseDetail',
                component: () => import('@/pages/shopManage/product/materialWarehouse/detail'),
                meta: {
                    title: '物资详情'
                }
            },
            {
                path: '/shopManage/product/allProducts',
                component: () => import('@/pages/shopManage/product/allProducts/index'),
                meta: {
                    title: '店铺商品管理'
                }
            },
            {
                path: '/shopManage/order/paidOrder',
                component: () => import('@/pages/shopManage/order/paidOrder/index'),
                meta: {
                    title: '已支付订单'
                }
            },
            {
                path: '/shopManage/order/finishedOrder',
                component: () => import('@/pages/shopManage/order/finishedOrder/index'),
                meta: {
                    title: '结算订单'
                }
            },

            {
                path: '/shopManage/order/pendingOrder',
                component: () => import('@/pages/shopManage/order/pendingOrder/index'),
                meta: {
                    title: '待支付订单'
                }
            },
            {
                path: '/shopManage/returnGoods/invoice',
                component: () => import('@/pages/shopManage/returnGoods/apply/index'),
                meta: {
                    title: '退货申请'
                }
            },
            {
                path: '/shopManage/returnGoods/applyDetail',
                // name: 'applyDetail',
                component: () => import('@/pages/shopManage/returnGoods/apply/detail1.vue'),
                meta: {
                    title: '退货申请详情'
                }
            },
            {
                path: '/shopManage/returnGoods/record',
                component: () => import('@/pages/shopManage/returnGoods/record/index'),
                meta: {
                    title: '退货记录'
                }
            },
            {
                path: '/shopManage/refundDetail',
                // name: 'refundDetail',
                component: () => import('@/pages/shopManage/returnGoods/record/detail1.vue'),
                meta: {
                    title: '物资商铺管理-退货详情'
                }
            },
            {
                path: '/shopManage/invoice/invoice',
                component: () => import('@/pages/shopManage/invoice/invoice/index'),
                meta: {
                    title: '发票申请'
                }
            },
            {
                path: '/shopManage/invoice/record',
                component: () => import('@/pages/shopManage/invoice/record/index'),
                meta: {
                    title: '发票记录'
                }
            },
            {
                path: '/shopManage/analysis/order',
                component: () => import('@/pages/shopManage/analysis/order/index'),
                meta: {
                    title: '订单统计'
                }
            },
            {
                path: '/shopManage/analysis/product',
                component: () => import('@/pages/shopManage/analysis/product/index'),
                meta: {
                    title: '商品统计'
                }
            },
        ]
    },
    {
        path: '/productCategory',
        component: () => import('@/pages/shopManage/productCategory'),
    },
]