<template>
    <div class="searchBox">
        <div class="searchBox_top dfb">
            <div class="dfa">
                <div class="title">筛选条件：</div>
                <div class="checkItem" v-for="(item, i) in list" :key="i">
                    {{ item }} <i class="el-icon-close pointer" @click="deleteItem(i)"></i>
                </div>
                <span class="pointer" v-if="list.length > 0" @click="resetSearch">清空已选条件</span>
            </div>
            <div class="searchBox_top_right">收起筛选 <i class="el-icon-arrow-up"></i></div>
        </div>
        <div class="row dfb" v-for="(item, index) in form" :key="index">
            <div class="row_left dfa">
                <div class="title">{{item.name}}</div>
                <div class="item" @click="checkItem(index, i)" :style="{ color: currentRows[`row${index + 1}`] == i ? 'rgba(34, 111, 199, 1)' : '' }" v-for="(item1, i) in item.options"
                    :key="i">
                    {{ item1 }}
                </div>
                <div class="multi pointer" v-if="item.multiple">+ 多选</div>
            </div>
            <div class="row_right dfc">更多 <i class="el-icon-arrow-down"></i></div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'searchBox',
    data () {
        return {
            currentRows: {},
        }
    },
    props: ['form', 'list'],
    watch: {
        list: {
            handler (newVal) {
                this.$emit('checkChange', newVal)
            }
        }
    },
    methods: {
        // 删除某一筛选条件
        deleteItem (i) {
            this.list.splice(i, 1)
        },
        // 清空筛选条件列表
        resetSearch () {
            for(let key in this.currentRows) {
                this.currentRows[key] = 0
            }
            this.list = []
        },
        // 点击筛选条件
        checkItem (index, i) {
            for(let key in this.currentRows) {
                if(key == `row${index + 1}`) {
                    this.currentRows[key] = i
                }
            }
            this.getCheckList()
        },
        // 将筛选条件放入列表中
        getCheckList () {
            let arr = []
            this.form.forEach((item, i) => {
                let num = this.currentRows[`row${i + 1}`]
                if (num > 0) {
                    arr.push(item.options[num])
                }
            })
            this.list = arr
        },
    },
    mounted () {
        for(let i = 0; i < this.form.length; i++) {
            this.currentRows[`row${i + 1}`] = null
        }
    }
}
</script>
<style scoped lang="scss">
.searchBox {
    width: 100%;
    //height: 254px;
    background-color: #fff;
    .searchBox_top {
        height: 48px;

        .title {
            width: 108px;
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
        }

        span {
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
        }

        .checkItem {
            font-size: 14px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            padding: 6px 12px;
            background: rgba(250, 250, 250, 1);
            border: 1px solid rgba(229, 229, 229, 1);
            margin-right: 10px;
        }

        .searchBox_top_right {
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
            padding-right: 20px;
        }
    }

    .row {
        // height: 47px;
        border-top: 1px solid rgba(229, 229, 229, 1);
        position: relative;

        .title {
            width: 108px;
            height: 48px;
            opacity: 1;
            background: rgba(250, 250, 250, 1);
            text-align: center;
            line-height: 48px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            border-top: 1px solid rgba(229, 229, 229, 1);
        }

        .item {
            font-size: 14px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            margin: 0 15px;
            cursor: pointer;
        }
        .multi {
            width: 60px;
            height: 28px;
            font-size: 14px;
            text-align: center;
            line-height: 28px;
            border: 1px solid rgba(33, 110, 198, 1);
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 90px;
            user-select: none;
            &:active {color: #fff;background: rgba(33, 110, 198, 1);}
        }

        .row_right {
            width: 60px;
            height: 28px;
            opacity: 1;
            background: rgba(255, 255, 255, 1);
            margin-right: 20px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
        }
    }
}
</style>