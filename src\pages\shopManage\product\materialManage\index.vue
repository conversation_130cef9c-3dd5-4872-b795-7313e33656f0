<template>
    <div class="base-page">
        <div class="left" :style="{ height: '100%' }">
            <select-material-class style="height: 100%" ref="materialClassRef" :productType="0"/>
        </div>
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <!--<el-button type="primary" @click="updateStateBatch(1)" class="btn-greenYellow">批量上架</el-button>-->
                            <el-button type="primary" @click="updateStateBatch(2)" class="btn-delete">批量下架</el-button>
                            <el-button type="primary" @click="putawayProductExportM()" class="btn-greenYellow">导出商品</el-button>
                            <!--<el-button type="primary" class="btn-greenYellow" @click = "add">新增</el-button>-->
                            <el-button type="primary" class="btn-greenYellow" @click="changeSortValue">批量修改排序值</el-button>
                            <!--<el-button type="primary" class="btn-blue" @click = "importBatchClick">导入</el-button>-->
                        </div>
                    </div>
                    <el-dropdown @command="handleChangeSort" trigger="click" placement="bottom">
                        <span class="pointer">
                            排序方式<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item :command="1" :style="{ color: init.orderBy == 1 ? '#2e61d7' : '' }">
                                排序值
                            </el-dropdown-item>
                            <el-dropdown-item :command="0" :style="{ color: init.orderBy == 0 ? '#2e61d7' : '' }">
                                上架时间
                            </el-dropdown-item>
                            <el-dropdown-item :command="2" :style="{ color: init.orderBy == 2 ? '#2e61d7' : '' }">
                                创建时间
                            </el-dropdown-item>
                            <el-dropdown-item :command="3" :style="{ color: init.orderBy == 3 ? '#2e61d7' : '' }">
                                修改时间
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <div class="search_box" style="width: 400px">
                        <el-input clearable type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="init.keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" v-loading='tableLoading' class="table" border
                    :height="rightTableHeight" :data="tableData" @selection-change="selectionChangeHandle"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="130">
                        <template v-slot="scope">
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.state===2 || scope.row.state===0"
                                size="mini"
                                type="success"
                                @click="updateState(scope.row,3,'上架')"
                            >上架
                            </el-button>
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.state===1"
                                size="mini"
                                type="danger"
                                @click="updateState(scope.row,2,'下架')"
                            >下架
                            </el-button>
                        </template>
                    </el-table-column>
                    <!-- 物资名称 -->
                    <el-table-column label="名称" width="200">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.productName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品编码" width="240">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.serialNum }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="图片" width="120" type="index">
                        <template v-slot="scope">
                            <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg" fit="cover"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="state">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state==0">待上架</el-tag>
                            <el-tag v-if="scope.row.state==1" type="success">已上架</el-tag>
                            <el-tag v-if="scope.row.state==2" type="danger">已下架</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="销售价格" prop="sellPrice"></el-table-column>
                    <el-table-column label="原价" prop="originalPrice"/>
                    <el-table-column label="成本价" prop="costPrice"/>
                    <el-table-column label="差价" prop="profitPrice"/>
                    <el-table-column label="库存" prop="stock"/>
                    <el-table-column label="供方名称" width="200" prop="supplierName"/>
                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input type="number" clearable v-model="scope.row.shopSort" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="上架时间" prop="putawayDate"/>
                    <el-table-column label="创建时间" prop="gmtCreate"/>
                    <el-table-column label="修改时间" prop="gmtModified"/>
                </el-table>
            </div>
            <!--分页-->
            <Pagination
                v-show="tableData && tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品名称：">
                            <el-input clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.productName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品编码：">
                            <el-input clearable maxlength="100" placeholder="请输入商品编码" v-model="filterData.serialNum"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="供应商名称：">
                            <el-input clearable maxlength="100" placeholder="请输入供应商名称" v-model="filterData.supplierName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="销售价格以上：">
                            <el-input clearable type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                        <el-form-item label="销售价格以下：">
                            <el-input clearable type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="上架时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.putawayDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.createDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="修改时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.modifiedDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="是否自有物资：">
                            <el-radio v-model="filterData.isOneself" :label="1">是</el-radio>
                            <el-radio v-model="filterData.isOneself" :label="0">否</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="状态：">
                            <div style="display:flex">
                                <el-checkbox v-model="filterData.stateCheckAll" @change="stateAllSelect">全部</el-checkbox>
                                <el-checkbox-group style="margin-left: 30px" v-model="filterData.state" @change="stateGroupChange">
                                    <el-checkbox :label="filterData.stateOptions[0].value">{{ filterData.stateOptions[0].label }}</el-checkbox>
                                    <el-checkbox :label="filterData.stateOptions[1].value">{{ filterData.stateOptions[1].label }}</el-checkbox>
                                    <el-checkbox :label="filterData.stateOptions[2].value">{{ filterData.stateOptions[2].label }}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="排序：">
                            <el-radio v-model="init.orderBy" :label="0">按上架时间排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="1">按排序值排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="2">按创建时间排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="3">按修改时间排序</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
        </el-dialog>

        <!--        <el-dialog v-dialogDrag  title="选择物资库" :visible.sync="showDeviceDialog"  width="70%"  :close-on-click-modal="true">-->
        <!--            <div class="e-table"  style="background-color: #fff">-->
        <!--                <div class="top" style="height: 50px; padding-left: 10px">-->
        <!--                    <div class="left">-->
        <!--                        <el-input clearable type="text" @blur="getDeviceInventory" placeholder="输入搜索关键字" v-model="inventory.keywords">-->
        <!--                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getDeviceInventory" />-->
        <!--                        </el-input>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--                <el-table-->
        <!--                          v-loading = 'deviceInventoryLoading'-->
        <!--                          @selection-change="selectDeviceRow"-->
        <!--                          highlight-current-row-->
        <!--                          border-->
        <!--                          style="width: 100%"-->
        <!--                          :data="inventory.tableData"-->
        <!--                          class="table"-->
        <!--                          @row-click="handleCurrentInventoryClick2"-->
        <!--                          :max-height="$store.state.tableHeight"-->
        <!--                          ref="eltableCurrentRow2"-->
        <!--                >-->
        <!--                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>-->
        <!--                    <el-table-column label="序号" type="index" width="60"></el-table-column>-->
        <!--                    <el-table-column prop="relevanceId" label="	编号" width="200"></el-table-column>-->
        <!--                    <el-table-column prop="productTitle" label="名称" width="200"></el-table-column>-->
        <!--                    <el-table-column prop="specTitle" label="规格型号"></el-table-column>-->
        <!--                    <el-table-column prop="className" label="类别名称"></el-table-column>-->
        <!--                    <el-table-column prop="unit" label="计量单位"></el-table-column>-->
        <!--                </el-table>-->
        <!--            </div>-->
        <!--            <span slot="footer">-->
        <!--                <Pagination-->
        <!--                    v-show="inventory.tableData != null || inventory.tableData.length != 0"-->
        <!--                    :total="inventory.paginationInfo.total"-->
        <!--                    :pageSize.sync="inventory.paginationInfo.pageSize"-->
        <!--                    :currentPage.sync="inventory.paginationInfo.currentPage"-->
        <!--                    @currentChange="getDeviceInventory"-->
        <!--                    @sizeChange="getDeviceInventory"-->
        <!--                />-->
        <!--                <div class="buttons" style="margin-top: 20px">-->
        <!--                    <el-button type="primary" class="btn-blue" @click="inventoryImportBatch">确定</el-button>-->
        <!--                    <el-button  @click="showDeviceDialog = false">取消</el-button>-->
        <!--                </div>-->
        <!--            </span>-->

        <!--        </el-dialog>-->
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapMutations, mapState } from 'vuex'
import {
    getMaterialPageList,
    importBatchMaterial,
    updateBatch,
    updateProductState,
    putawayProductExport
} from '@/api/shopManage/product/materialManage'
import { getMaterialInventoryPageList } from '@/api/shopManage/product/prodcutInventory'

export default {
    components: {
        SelectMaterialClass, Pagination
    },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    data () {
        return {
            deviceInventoryLoading: false,
            tableLoading: false, // 表格加载
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
                state: [1],
                productType: 0,
                orderBy: 0,
                classId: null,
                keywords: null,
                classPath: [],
            },
            // 商品库
            showDeviceDialog: false,
            inventory: {
                selectRow: [],
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            dataListSelections: [], //表格选中的数据
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                supplierName: null,
                serialNum: null,
                productName: null,
                stateCheckAll: false, // 选择全局
                state: [], // 状态
                isOneself: null,
                belowPrice: null,
                abovePrice: null,
                putawayDate: [], // 上架时间
                createDate: [], // 创建时间
                modifiedDate: [], // 修改时间
                stateOptions: [{
                    value: 0,
                    label: '待上架'
                }, {
                    value: 1,
                    label: '已上架'
                }, {
                    value: 2,
                    label: '已下架'
                }],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.userInfo.orgInfo ? this.orgId = this.userInfo.orgInfo.orgId : null
        // this.getTableData()
    },
    methods: {
        handleChangeSort (value) {
            this.init.orderBy = value
        },
        // 批量导入
        inventoryImportBatch () {
            if (this.inventory.selectRow.length === 0) {
                return this.$message('请勾选要导入的数据！')
            }
            this.clientPop('info', '您确定要批量导入入这些数据吗！', async () => {
                this.inventory.selectRow.forEach(t => {
                    t.productType = this.init.productType
                    t.classId = this.init.classId
                })
                this.tableLoading = true
                importBatchMaterial(this.inventory.selectRow).then(res => {
                    this.message(res)
                    this.inventory.selectRow = []
                    this.showDeviceDialog = false
                    this.getTableData()
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        // 选择
        selectDeviceRow (value) {
            this.inventory.selectRow = value
        },
        // 批量导入
        importBatchClick () {
            if (this.init.classId == null) {
                return this.$message('请先选择分类再导入！')
            }
            this.showDeviceDialog = true
            this.getDeviceInventory()
        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                state: this.init.inventory.state,
                productType: this.init.productType,
                page: this.inventory.paginationInfo.currentPage,
                limit: this.inventory.paginationInfo.pageSize,
            }
            if (this.inventory.keywords != null) {
                params.keyword = this.inventory.keywords
            }
            this.deviceInventoryLoading = true
            getMaterialInventoryPageList(params).then(res => {
                this.inventory.tableData = res.list
                this.inventory.paginationInfo.total = res.totalCount
                this.inventory.paginationInfo.pageSize = res.pageSize
                this.inventory.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.deviceInventoryLoading = false
            })
        },
        // 批量修改排序
        changeSortValue () {
            if (this.changedRow.length === 0) {
                return this.$message('未修改列表当中的排序值！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                updateBatch(this.changedRow).then(res => {
                    this.message(res)
                    this.changedRow = []
                    this.getTableData()
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if (row.shopSort <= 0) {
                row.shopSort = 0
            }
            if (this.changedRow.length === 0) {
                this.changedRow.push({ productId: row.productId, shopSort: row.shopSort })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.productId === row.productId) {
                    t.shopSort = row.shopSort
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push({ productId: row.productId, shopSort: row.shopSort })
            }
            flag = true
        },
        skipView (data) {
            let path = '/supplierSys/product/materialManageDetail'
            this.$router.push({
                path,
                name: 'materialDetail',
                params: { row: data }
            })
        },
        //新增
        add () {
            let data = {}
            data.viewType = 'add'
            data.classPath = this.classPath
            this.skipView(data)
        },
        // 物资详情
        handleView (row) {
            row.classPath = this.classPath
            this.skipView(row)
        },
        // 清空高级搜索表单
        resetSearchConditions () {
            this.filterData.supplierName = ''
            this.filterData.serialNum = ''
            this.filterData.productName = ''
            this.filterData.createDate = []
            this.filterData.putawayDate = []
            this.filterData.modifiedDate = []
            this.filterData.belowPrice = ''
            this.filterData.abovePrice = ''
            this.filterData.isOneself = null
            this.filterData.state = []
            this.filterData.stateCheckAll = false
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 高级搜索确认
        confirmSearch () {
            this.init.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateOptions.length
        },
        // 状态全选
        stateAllSelect (value) {
            if (value) {
                this.filterData.state = this.filterData.stateOptions.map(t => {
                    return t.value
                })
            } else {
                this.filterData.state = []
            }
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 单个上架
        updateState (row, state, title) {
            let productName = row.productName
            let params = {
                productIds: [row.productId],
                state: state
            }
            this.clientPop('info', '您确定要对物资【' + productName + '】进行【' + title + '】操作吗?', async () => {
                updateProductState(params).then(res => {
                    this.getTableData()
                    this.message(res)
                })
            })
        },
        putawayProductExportM () {
            let params = {
                productIds: this.dataListSelections.map(item => {
                    return item.productId
                }),
                state: this.init.state,
                shopId: this.init.shopId,
                productType: this.init.productType,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.supplierName) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.serialNum) {
                params.serialNum = this.filterData.serialNum
            }
            if (this.filterData.productName) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.state.length > 0) {
                params.state = this.filterData.state
            }
            if (this.init.classId) {
                params.classId = this.init.classId
            }
            if (this.init.keywords) {
                params.keywords = this.init.keywords
            }
            if (this.filterData.modifiedDate) {
                params.startModifiedDate = this.filterData.modifiedDate[0]
                params.endModifiedDate = this.filterData.modifiedDate[1]
            }
            if (this.filterData.createDate) {
                params.startCreateDate = this.filterData.createDate[0]
                params.endCreateDate = this.filterData.createDate[1]
            }
            if (this.filterData.putawayDate) {
                params.startPutawayDate = this.filterData.putawayDate[0]
                params.endPutawayDate = this.filterData.putawayDate[1]
            }
            if (this.init.orderBy != null) {
                params.orderBy = this.init.orderBy
            }
            if (this.filterData.belowPrice) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.isOneself) {
                params.isOneself = this.filterData.isOneself
            }
            this.clientPop('info', '您确定要批量导出商品数据？', async () => {
                this.tableLoading = true
                putawayProductExport(params ).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '出售中的物资商品.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.currentQuery.ids = []
                    this.dataListSelections = []
                    this.$message.success('商品导出操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
                // putawayProductExport(params).then(res => {
                //     if (res.code == 200) {
                //         this.$message({
                //             message: '上架商品导出成功',
                //             type: 'success'
                //         })
                //     }else {
                //         this.$message({
                //             message: '上架商品导出失败',
                //             type: 'error'
                //         })
                //     }
                // }).finally(() => {
                //     this.tableLoading = false
                // })
            })

        },
        // 批量修改上下架状态
        updateStateBatch (state) {
            if (this.dataListSelections.length === 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let params = {
                productIds: this.dataListSelections.map(item => {
                    return item.productId
                }),
                state: state
            }
            this.clientPop('info', '您确定要批量下架这些物资吗！', async () => {
                let res = await updateProductState(params)
                this.message(res)
                this.dataListSelections = []
                this.getTableData()
            })
        },
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.classPath = nodePath
            this.getTableData()
        },
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                state: this.init.state,
                shopId: this.init.shopId,
                productType: this.init.productType,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.supplierName) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.serialNum) {
                params.serialNum = this.filterData.serialNum
            }
            if (this.filterData.productName) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.state.length > 0) {
                params.state = this.filterData.state
            }
            if (this.init.classId) {
                params.classId = this.init.classId
            }
            if (this.init.keywords) {
                params.keywords = this.init.keywords
            }
            if (this.filterData.modifiedDate) {
                params.startModifiedDate = this.filterData.modifiedDate[0]
                params.endModifiedDate = this.filterData.modifiedDate[1]
            }
            if (this.filterData.createDate) {
                params.startCreateDate = this.filterData.createDate[0]
                params.endCreateDate = this.filterData.createDate[1]
            }
            if (this.filterData.putawayDate) {
                params.startPutawayDate = this.filterData.putawayDate[0]
                params.endPutawayDate = this.filterData.putawayDate[1]
            }
            if (this.init.orderBy != null) {
                params.orderBy = this.init.orderBy
            }
            if (this.filterData.belowPrice) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.isOneself) {
                params.isOneself = this.filterData.isOneself
            }
            this.tableLoading = true
            getMaterialPageList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

.el-dialog__body {
    margin: 220px;
}

.base-page .left {
    min-width: 200px;
    height: 100%;
    padding: 0;
    overflow: scroll;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .el-dropdown {
    min-width: 75px;
    margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}
</style>
