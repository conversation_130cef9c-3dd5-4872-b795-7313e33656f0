<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" v-loading="showLoading">
            <el-tabs
                :style="{ height: tabsContentHeight + 'px !important' }" tab-position="left"
                v-model="tabsName" @tab-click="onChangeTab"
            >
                <el-tab-pane label="发票申请" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="对账单信息" name="reconciliation" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">发票申请</div>
                        <div class="form">
                            <el-form
                                ref="formData" :model="formData" :rules="rules"
                                class="demo-ruleForm" label-width="200px"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <div class="col">
                                            <el-form-item label="开票含税金额：" prop="rateAmount">
                                                <span>{{formData.rateAmount}}</span>
                                            </el-form-item>
                                        </div>
                                    </el-col>
                                    <el-col :span="12" >
                                        <el-form-item label="发票抬头：" prop="company">
                                            <el-input placeholder="发票抬头" clearable disabled v-model="formData.company"></el-input>
                                            <el-button size="mini"
                                                       type="primary"
                                                       @click="selectContractClick">选择</el-button>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发票类型：" prop="invoiceType">
                                            <el-select
                                                v-model="formData.invoiceType" placeholder="请选择发票类型"
                                                @change="changRiseType(formData.invoiceType)"
                                            >
                                                <el-option
                                                    v-for="item in typeOptions" :value="item.value"
                                                    :label="item.label" :key="item.value"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="12">
                                        <el-form-item label="抬头类型：" prop="riseType">
                                            <el-select
                                                disabled v-model="formData.riseType" placeholder="请选择抬头类型"
                                            >
                                                <el-option
                                                    v-for="item in headerOptions" :value="item.value"
                                                    :label="item.label" :key="item.value"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="单位名称：" prop="company"
                                        >
                                            <el-input
                                                v-model="formData.company" placeholder="请输入单位名称"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="单位税号：" prop="dutyParagraph"
                                        >
                                            <el-input
                                                v-model="formData.dutyParagraph" placeholder="请输入单位税号"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册地址：" prop="province">
                                            <el-cascader
                                                style="width: 300px"
                                                size="large"
                                                :options="addressData"
                                                v-model="selectAddressOptions"
                                                @change="handleAddressChange"
                                            >
                                            </el-cascader>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="详细地址" prop="registerAddress"
                                            :rules="formData.invoiceType==0? rules.registerAddress:[{
                                  required:false,
                                  message:'请输入详细地址'
                                }]"
                                        >
                                            <el-input
                                                placeholder="请输入详细地址" clearable
                                                v-model="formData.registerAddress"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="注册电话" prop="registerPhone"
                                            :rules="formData.invoiceType==0? rules.registerPhone:[{
                                  required:false,
                                  message:'请输入注册电话'
                                }]"
                                        >
                                            <el-input
                                                placeholder="请输入注册电话" clearable v-model="formData.registerPhone"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="邮箱" prop="email">
                                            <el-input
                                                placeholder="请输入邮箱" clearable v-model="formData.email"
                                            ></el-input>
                                        </el-form-item>

                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="开户银行：" prop="bank" :rules="formData.invoiceType==0? rules.bank:[{
                                  required:false,
                                  message:'请输入开户银行'
                                }]"
                                        >
                                            <el-input
                                                v-model="formData.bank" placeholder="请输入开户银行"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="银行账号：" prop="bankAccount"
                                            :rules="formData.invoiceType==0? rules.bankAccount:[{
                                  required:false,
                                  message:'请输入银行账号'
                                }]"
                                        >
                                            <el-input
                                                v-model="formData.bankAccount" placeholder="请输入银行账号"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <div class="col">
                                            <el-form-item label="收票人姓名：" prop="userName">
                                                <el-input
                                                    v-model="formData.userName" placeholder="请输入收票人姓名"
                                                ></el-input>
                                            </el-form-item>
                                        </div>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收票人手机号：" prop="userPhone">
                                            <el-input
                                                v-model="formData.userPhone" placeholder="请输入收票人手机号"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="收票人地址：" prop="userProvince">
                                            <el-cascader
                                                style="width: 300px"
                                                size="large"
                                                :options="addressData"
                                                v-model="selectAddressUserOptions"
                                                @change="handleAddressUserChange"
                                            >
                                            </el-cascader>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="详细地址" prop="userAddress">
                                            <el-input
                                                placeholder="请输入详细地址" clearable v-model="formData.userAddress"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                <el-col :span="12">
                                    <el-form-item label="税额" prop="taxRate">
                                        <span
                                        >{{taxRate}}</span>%
                                    </el-form-item>
                                </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="抬头备注：" prop="productDescribe">
                                            <el-input type="textarea" v-model="formData.remarks"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!--<el-row  style="margin-top: 20px;">-->
                                <!--    <el-col :span="20" v-loading="fileLoading">-->
                                <!--        <el-form-item class="upload-item" label="发票电子版" prop="">-->
                                <!--            <el-upload-->
                                <!--                action="fakeaction"-->
                                <!--                multiple-->
                                <!--                :limit="20"-->
                                <!--                :show-file-list="true"-->
                                <!--                :file-list="fileList"-->
                                <!--                :before-upload="beforeOneOfFilesUpload"-->
                                <!--                :http-request="uploadOneOfFiles"-->
                                <!--                :on-remove="handleRemove"-->
                                <!--                accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"-->
                                <!--            >-->
                                <!--                <el-button size="small" type="primary">点击上传</el-button>-->
                                <!--                <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>-->
                                <!--            </el-upload>-->
                                <!--        </el-form-item>-->
                                <!--    </el-col>-->
                                <!--</el-row>-->
                            </el-form>
                        </div>
                    </div>
                    <!-- 订单商品-->
                    <div id="reconciliation" class="con">
                        <div class="tabs-title" id="contractList">对账单商品</div>
                        <div class="e-table" style="background-color: #fff">
                            <!--                            <div class="top" style="height: 50px; padding-left: 10px">-->
                            <!--                                <div class="left">-->
                            <!--                                    <el-input type="text" @blur="getTableData" placeholder="输入搜索关键字"-->
                            <!--                                              v-model="keywords">-->
                            <!--                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData"/>-->
                            <!--                                    </el-input>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <el-table
                                ref="tableRef"
                                border
                                :data="tableList"
                                class="table"
                            >
                                <el-table-column label="对账单编号" width="190" prop="reconciliationNo">
                                </el-table-column>
                                <el-table-column label="商品名称" width="230" prop="materialName"/>
                                <el-table-column label="商品规格" width="230" prop="spec"/>
                                <el-table-column label="单位" prop="unit" width="130"/>
                                <el-table-column label="数量" prop="quantity" width="150"/>
                                <!--<el-table-column label="已结算金额" prop="settleAmount"/>-->
                                <el-table-column label="单价" width="" prop="price"/>
                                <el-table-column label="金额" width="" prop="acceptanceAmount"/>
                                <el-table-column label="价税合计金额" width="160" prop="acceptanceAmount"/>
                                <!--                                <el-table-column prop="remarks" label="备注" width="200"></el-table-column>-->
                            </el-table>
                        </div>
                        <!--            分页-->
                        <!--                        <Pagination-->
                        <!--                            v-show="tableData != null || tableData.length != 0"-->
                        <!--                            :total="paginationInfo.total"-->
                        <!--                            :pageSize.sync="paginationInfo.pageSize"-->
                        <!--                            :currentPage.sync="paginationInfo.currentPage"-->
                        <!--                            @currentChange="getTableData"-->
                        <!--                            @sizeChange="getTableData"-->
                        <!--                        />-->
                    </div>
                    <span slot="footer">
                    <Pagination
                        :total="paginationInfo1.total"
                        :pageSize.sync="paginationInfo1.pageSize"
                        :currentPage.sync="paginationInfo1.currentPage"
                        @currentChange="currentChange"
                        @sizeChange="sizeChange"
                    />
                </span>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button type="primary" @click="invoiceApplyM(1)">申请发票</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
        <el-dialog v-dialogDrag :title="selectContractOrPlanTableTitle" :visible.sync="showSelectContractOrPlan"  width="60%" style="margin-left: 10%;" :close-on-click-modal="false">
            <div class="e-table" style="background-color: #fff" v-loading="selectContractOrPlanLoading">
                <div class="top dfa" style="height: 50px; padding-left: 10px">
                    <el-input style="width: 200px; " type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                        <img :src="require('@/assets/search.png')" slot="suffix" @click="handleInputSearch" />
                    </el-input>
                </div>
                <el-table ref="bidingOrderItemRef"
                          border
                          :data="tableData"
                          class="table"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="">
                        <template slot-scope="scope">
                            <el-button type="primary" class="btn-greenYellow" @click="selectInvoiceClickM(scope.row)">选择发票抬头
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="发票类型" width="150px">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.invoiceType === 0" type="success">增值税专用发票</el-tag>
                            <el-tag v-if="scope.row.invoiceType === 1" type="primary">增值税普通发票</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="开户银行" width="200" prop="bank"></el-table-column>
                    <el-table-column label="银行账号" width="200" prop="bankAccount"></el-table-column>

                </el-table>
            </div>
            <!--分页-->
            <span slot="footer">
                    <Pagination
                        :total="paginationInfo1.total"
                        :pageSize.sync="paginationInfo.pageSize"
                        :currentPage.sync="paginationInfo.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                </span>
            <div class="buttons">
                <el-button @click="showSelectContractOrPlan = false">关闭</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
import { calculateNotTarRateAmount, throttle, toFixed } from '@/utils/common'
import { materialReconciliationIds, materialReconciliationDtlIds  } from '@/api/reconciliation/reconciliation'
import { getInvoiceInfo, getList } from '@/api/performance/invoiceRecord'
import { create } from '@/api/performance/invoice'
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import { uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/supplierSys/bidManage/myBidding/myBidManage'

export default {
    data () {
        return {
            paginationInfo: {
                total: 0,
                currPage: 1,
                pageSize: 20
            },
            paginationInfo1: {
                total: 0,
                currPage: 1,
                pageSize: 20
            },
            tableData: [],
            keywords: '',
            tableLoading: '',
            selectContractOrPlanLoading: false,
            selectContractOrPlanTableTitle: '',
            showSelectContractOrPlan: false,
            uploadMax: 10,
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
            },
            addressData: regionData, // 地址数据
            selectAddressOptions: [], // 地址选择
            // 数据加载
            formLoading: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            rowData: null, // 跳转过来的数据
            showForm: false,
            rules: {
                invoiceType: { required: true, message: '请选择发票类型', trigger: 'blur' },
                riseType: { required: true, message: '请选择抬头类型', trigger: 'blur' },
                registerPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                userPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                company: { required: true, message: '请选择单位名称', trigger: 'blur' },
                dutyParagraph: { required: true, message: '请选择单位税号', trigger: 'blur' },
                registerAddress: { validator: true, message: '请选择注册地址', trigger: 'blur' },
                bank: { required: true, message: '请选择开户银行', trigger: 'blur' },
                bankAccount: { required: true, message: '请选择银行账号', trigger: 'blur' },
            },
            selectAddressUserOptions: [], // 地址选择
            fileList: [],
            fileLoading: false,
            showLoading: false,
            taxRate: this.$route.params.row.taxRate,
            //表单数据
            formData: {
                taxRate: 0,
                supplierId: '',
                files: [],
                dtls: [],
                invoiceRecordId: '',
                state: 0,
                invoiceType: 0,
                riseType: 1,
                company: '',
                dutyParagraph: '',
                registerAddress: '',
                registerPhone: '',
                bank: '',
                bankAccount: '',
                userName: '',
                userPhone: '',
                userAddress: '',
                email: '',
                province: '',
                city: '',
                county: '',
                userProvince: '',
                userCity: '',
                userCounty: '',
                district: '',
                rateAmount: 0,
                noRateAmount: '',

            },
            rateAmount2: 0,
            typeOptions: [
                { label: '增值税专用发票', value: 0 },
                { label: '增值税普通发票', value: 1 },
            ],
            headerOptions: [
                { label: '单位', value: 1 },
                { label: '个人', value: 2 },
            ],
            addressOptions: {
                province: [],
                city: [],
                district: []
            },
            tableList: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    computed: {
        tabsContentHeight () {
            return this.screenHeight - 140
        },
    },
    mounted () {
        // 保存所有tabName
        const arr = ['baseInfo', 'reconciliation']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    methods: {
        selectInvoiceClickM (row) {
            this.formData = row
            this.showSelectContractOrPlan = false
        },
        currentChange (currPage) {
            this.paginationInfo1.currentPage = currPage
            this.getTableDataByReconciliationdtlM()
        },
        sizeChange (pageSize) {
            this.paginationInfo1.pageSize = pageSize
            this.getTableDataByReconciliationdtlM()
        },
        currentChangeUser (currPage) {
            this.paginationInfo.currentPage = currPage
            this.getTableData()
        },
        sizeChangeUser (pageSize) {
            this.paginationInfo.pageSize = pageSize
            this.getTableData()
        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if (size > 500) {
                this.$message.error('文件大小不能超过500M')
                return false
            }
            return true
        },
        // 多文件上传
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'device')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if (uploadRes.code != null && uploadRes.code !== 200) {
                this.fileList.push(file)
                this.fileList.pop()
            } else {
                console.log(uploadRes, 'rr')
                this.$message.success('上传成功')
                this.fileList.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 16,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
            }
        },
        handleRemove (file) {
            let files = this.fileList
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.fileList = newFiles
                }
            })
        },
        changRiseType (state) {
            if (state == 0) {
                this.formData.riseType = 1
            }
        },
        selectContractClick () {
            this.selectContractOrPlanTableTitle = '选择发票抬头'
            this.getTableData()
            this.showSelectContractOrPlan = true
        },
        getTableData () {
            let params = {
                limit: this.paginationInfo.pageSize,
                page: this.paginationInfo.currPage,
                userType: 0,
            }
            if (this.keywords != null || this.keywords != '') {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            getList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            })
            this.tableLoading = false
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        invoiceApplyM (state) {
            this.formData.invoiceState = state
            this.formData.dtls = this.tableList
            this.formData.taxRate = this.taxRate
            this.formData.supplierName = this.$route.params.row.supplierName,
            create(this.formData).then(res=>{
                if (res.code === 200) {
                    this.$message.success('申请成功！')
                    this.$router.push({  path: '/performance/invoice/record' })
                }

            })
            console.log(this.formData.files)
        },
        handleInputSearch () {
            this.getTableData()
        },
        getData () {
            getInvoiceInfo().then(res=>{
                if (res.province != null)
                    this.addressFormatShow(res)
                if (res.userProvince != null) {
                    this.addUserRessFormatShow(res)
                }
                this.formData = res
                this.formData.rateAmount = this.rateAmount2
            })
        },
        getTableDataByReconciliationM (reconciliationIds) {
            console.log(reconciliationIds, 'reconciliationIds')
            materialReconciliationIds( reconciliationIds ).then(res=>{
                this.tableList = res
            })
        },

        getTableDataByReconciliationdtlM (reconciliationIds) {
            let params = {
                page: this.paginationInfo1.currentPage,
                limit: this.paginationInfo1.pageSize,
                reconciliationIds: reconciliationIds,
            }
            materialReconciliationDtlIds( params ).then(res=>{
                this.tableList = res.list
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                let rateAmount = 0
                for (let i = 0; i < this.tableList.length; i++) {
                    let t = this.tableList[i]
                    t.acceptanceAmount  = this.fixed2(Number(t.quantity) * Number(t.price))
                    rateAmount = this.fixed2(Number(rateAmount) + Number(t.acceptanceAmount))
                }
                this.formData.rateAmount = rateAmount
                this.rateAmount2 = rateAmount
                this.formData.noRateAmount = calculateNotTarRateAmount(rateAmount, this.taxRate)
            })
        },
        addressFormatShow (row) {
            if (row.province == null || row.province == '') {
                return
            }
            if (row.city == null || row.city == '') {
                return
            }
            if (row.county == null || row.county == '') {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 地址回显
        addUserRessFormatShow (row) {
            if (row.userProvince == null || row.userProvince == '') {
                return
            }
            if (row.userCity == null || row.userCity == '') {
                return
            }
            if (row.userCounty == null || row.UserCounty == '') {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.userProvince][row.userCity][row.userCounty].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressUserOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        handleAddressUserChange () {
            let addArr = this.selectAddressUserOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.formData.userProvince = province
            this.formData.userCity = city
            this.formData.userCounty = county
            this.formData.userAddress = province + city + county
        },

        // 地区
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.formData.province = province
            this.formData.city = city
            this.formData.county = county
            this.formData.registerAddress = province + city + county
        },

    },
    created () {
        this.getData()
        this.formData.supplierName = this.$route.params.row.supplierName,
        this.formData.taxRate = this.$route.params.row.taxRate
        console.log(this.formData.supplierId, 'this.$route.params.row')
        this.getTableDataByReconciliationdtlM( this.$route.params.row.reconciliationIds)
    }
}
</script>

<style lang="scss" scoped>
.e-table {
    min-height: auto;
    background: #fff;
}
.warningTabs {
    padding-top: 70px;
}
/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    overflow: auto !important;
    height: unset !important;

    &::-webkit-scrollbar {
        width: 0;
    }
}
</style>
