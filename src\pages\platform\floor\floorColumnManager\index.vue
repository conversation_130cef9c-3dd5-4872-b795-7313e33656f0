<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button @click="changePublishState(1)" class="btn-greenYellow">批量启用</el-button>
                            <el-button @click="changePublishState(0)" class="btn-delete">批量停用</el-button>
                            <el-button @click="handleDelete" class="btn-delete">批量删除</el-button>
                            <el-button @click="changeSortValueColumn" class="btn-greenYellow">批量修改排序值</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="3">按修改时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                        <el-input clearable @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" v-loading="isLoading">
                <el-table
                    class="table" :height="rightTableHeight" :data="tableData" border
                    @row-click="handleCurrentInventoryClick" ref="mainTable" @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="120">
                        <template v-slot="scope">
                            <span class="action" @click="onDel(scope)">
                                <img src="@/assets/btn/delete.png" alt="" >
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="栏目编号" width="220">
                        <template v-slot="scope">
                            {{ scope.row.columnNumber }}
                        </template>
                    </el-table-column>
                    <el-table-column label="栏目名称">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.columnName }}</span>
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column label="发布时间">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            {{scope.row.gmtRelease}}-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <!--                    <el-table-column  label="备注">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            {{scope.row.remarks}}-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column label="栏目状态">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state==1" type="success">启用</el-tag>
                            <el-tag v-if="scope.row.state==0" type="danger">停用</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="固定状态">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.isFixed==1" type="success">固定</el-tag>
                            <el-tag v-if="scope.row.isFixed==0" type="success">默认</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="修改时间">
                        <template v-slot="scope">
                            {{ scope.row.gmtModified.slice(0, 10) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input type="number" clearable v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新增/编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="8">
                            <!--                            <el-col :span="12">-->
                            <!--                                <el-form-item width="150px" label="标题：" prop="title">-->
                            <!--                                    <el-input clearable v-model="formData.title" placeholder="请输入标题名称" ></el-input>-->
                            <!--                                </el-form-item>-->
                            <!--                            </el-col>-->
                            <el-form-item width="150px" label="栏目名称：" prop="columnName">
                                <el-input clearable v-model="formData.columnName" placeholder="请输入栏目名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="填写排序值：" prop="sort">
                                <el-input clearable v-model="formData.sort" type="number" placeholder="填写排序值">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="栏目状态：" prop="state">
                                <el-select v-model="formData.state" placeholder="栏目状态">
                                    <el-option
                                        v-for="item in addStatusFilter" :key="item.value" :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="固定状态：" prop="isFixed">
                                <el-select v-model="formData.isFixed" placeholder="固定状态">
                                    <el-option
                                        v-for="item in addIsFixedFilter" :key="item.value" :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col v-show="formData.isFixed==1">
                            <el-form-item label="编码：" prop="code">
                                <el-input clearable v-model="formData.code"  placeholder="编码">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input clearable type="textarea" v-model="formData.remarks" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="35%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="24">
                        <el-form-item width="150px" label="栏目名称：" prop="columnName">
                            <el-input clearable v-model="filterData.columnName" placeholder="请输入栏目名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注信息：" prop="remarks">
                            <el-input clearable v-model="filterData.remarks" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="栏目状态：">
                            <el-select v-model="filterData.state" placeholder="栏目状态">
                                <el-option
                                    v-for="item in statusFilter" :key="item.value" :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">返回</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { debounce, hideLoading, showLoading } from '@/utils/common'
import {
    batchDeleteColumn,
    batchNotPublishColumn,
    batchPublishColumn,
    changeSortValueColumn,
    createColumn,
    delColumn,
    editColumn,
    getColumnList
} from '@/api/platform/floor/column'

export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertName: '栏目',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            isLoading: false,
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                state: null,
                orderBy: 1,
                mallType: 0,
                columnName: '',
                remarks: ''
            },
            tableData: [],
            mapObj: null,
            // 新增编辑 表单数据
            formData: {},
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            statusFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '启用' },
                { value: 0, label: '停用' },
            ],
            addStatusFilter: [
                { value: 1, label: '启用' },
                { value: 0, label: '停用' },
            ],
            addIsFixedFilter: [
                { value: 1, label: '固定' },
                { value: 0, label: '默认' },
            ],
            mallTypeFilter: [
                { value: null, label: '全部' },
                // { value: 0, label: '初始' },
                { value: 0, label: '慧采商城' },
                { value: 1, label: '装备商城' },
            ],
            // 表单校验规则
            formRules: {
                columnName: [{ required: true, message: '请输入栏目名称', trigger: 'blur' }],
                name: [{ required: true, message: '请输入链接名', trigger: 'blur' }],
                state: [{ required: true, message: '请选择状态', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        this.isLoading = true
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy,
            state: this.filterData.state
        }
        getColumnList(params).then(res => {
            this.pages = res
            res.list.forEach(item => {
                typeof item.gmtRelease === 'string' ? item.gmtRelease = item.gmtRelease.slice(0, 10) : ''
                typeof item.gmtCreate === 'string' ? item.gmtCreate = item.gmtCreate.slice(0, 10) : ''
                typeof item.gmtModified === 'string' ? item.gmtModified = item.gmtModified.slice(0, 10) : ''
            })
            this.tableData = res.list
        }).finally(() => {
            this.isLoading = false
        })
        this.getParams()
    },
    methods: {
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        resetSearchConditions () {
            this.filterData.state = null
            this.filterData.columnName = ''
            this.filterData.remarks = ''
        },
        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        changePublishState (num) {
            let arr = this.selectedRows.map(item => item.columnId)
            if (!this.selectedRows[0]) {
                let msg = num == 1 ? `请选择要启用的${this.alertName}` : `请选择要停用的${this.alertName}`
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = num === 1 ? `您确定要启用选中的${this.alertName}吗？` : `您确定要停用选中的${this.alertName}吗？`
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublishColumn(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '启用成功', () => {
                                getColumnList(this.requestParams).then(res => {
                                    if (res.list) {
                                        res.list.forEach(item => {
                                            typeof item.gmtRelease === 'string' ? item.gmtRelease = item.gmtRelease.slice(0, 10) : ''
                                            typeof item.gmtCreate === 'string' ? item.gmtCreate = item.gmtCreate.slice(0, 10) : ''
                                            typeof item.gmtModified === 'string' ? item.gmtModified = item.gmtModified.slice(0, 10) : ''
                                        })
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 0:
                    batchNotPublishColumn(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '停用成功', () => {
                                getColumnList(this.requestParams).then(res => {
                                    if (res.list) {
                                        res.list.forEach(item => {
                                            typeof item.gmtRelease === 'string' ? item.gmtRelease = item.gmtRelease.slice(0, 10) : ''
                                            typeof item.gmtCreate === 'string' ? item.gmtCreate = item.gmtCreate.slice(0, 10) : ''
                                            typeof item.gmtModified === 'string' ? item.gmtModified = item.gmtModified.slice(0, 10) : ''
                                        })
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${this.alertName}吗？`, async () => {
                showLoading()
                delColumn({ id: scope.row.columnId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.columnId
                })
                batchDeleteColumn(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        handleView (scope) {
            // this.viewList = 'class'
            this.formData = JSON.parse(JSON.stringify(scope.row))
            // this.action = '编辑'
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/floor/floorColumnDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'floorColumnDetail',
                params: {
                    row: scope.row
                }
            })
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (!this.changedRow[0]) {
                return this.changedRow.push({ columnId: row.columnId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if (item.columnId === row.columnId) {
                    return i
                }
            })
            if (arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ columnId: row.columnId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValueColumn () {
            // changeSortValue(this.changedRow).then(res => {
            //     console.log(res)
            //     this.getTableData()
            // })
            if (!this.changedRow[0]) {
                let msg = '当前没有排序值被修改！'
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = '您确定要修改选中的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValueColumn(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '修改成功', () => {
                            this.getTableData()
                        })
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.isLoading = true
            this.getParams()
            getColumnList(this.requestParams).then(res => {
                if (res.list) {
                    res.list.forEach(item => {
                        typeof item.gmtRelease === 'string' ? item.gmtRelease = item.gmtRelease.slice(0, 10) : ''
                        typeof item.gmtCreate === 'string' ? item.gmtCreate = item.gmtCreate.slice(0, 10) : ''
                        typeof item.gmtModified === 'string' ? item.gmtModified = item.gmtModified.slice(0, 10) : ''
                    })
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.pages = res
            }).finally(() => {
                this.isLoading = false
            })
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    // if (this.action === '编辑') {
                    //     return this.handleEditData()
                    // }
                    this.handleCreateData()
                }
            })
        },
        // 修改数据
        handleEditData () {
            editColumn(this.formData).then(res => {
                if (res.message === '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 保存数据
        handleCreateData () {
            createColumn(this.formData).then(res => {
                if (res.message === '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
