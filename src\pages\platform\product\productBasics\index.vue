<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">启用
                            </el-button>
                            <el-button type="primary" @click="changePublishState(2)" class="btn-delete">停用
                            </el-button>
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                            <el-button type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改排序值</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                        <el-input type="text" @keyup.enter.native="onSearch" placeholder="品牌名称" v-model="keywords">
                            <img src="@/assets/search.png"
                                 slot="suffix" @click="onSearch"/></el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                          @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="120">
                        <template slot-scope="scope">
                            <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png"
                                                                            alt=""></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="品牌名称" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.name }}
                            </span>
                        </template>
                    </el-table-column>
                    <!-- 图片 -->
                    <el-table-column label="图片" width="120" type="index">
                        <template slot-scope="scope">
                            <el-image v-show="scope.row.logo != null" style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.logo"></el-image>
                        </template>
                    </el-table-column>
                    <!-- 介绍 -->
                    <el-table-column label="介绍" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.descript }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="首页显示状态" width="">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.state==1" type="success">启用</el-tag>
                            <el-tag v-if="scope.row.state==0" type="danger">停用</el-tag>
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column label="备注" width="">-->
                    <!--                        <template slot-scope="scope">-->
                    <!--                            <span class="action" @click="handleView(scope)">-->
                    <!--                                {{ scope.row.remarks }}-->
                    <!--                            </span>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <!-- 图片排序值 -->
                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input type="number" v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>

                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                           :currentPage.sync="pages.currPage"
                           @currentChange="currentChange" @sizeChange="sizeChange"/>
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item class="uploader" label="logo：" prop="">
                                <div>
                                    <el-upload class="avatar-uploader" action="fakeaction" v-loading="addLoading"
                                               :show-file-list="false" :before-upload="handleBeforeUpload" name="img"
                                               :auto-upload="true"
                                               :on-change="handleUploadChange"
                                               :http-request="uploadImg"
                                    >
                                        <img v-if="formData.logo" :src="imgUrlPrefixAdd + formData.logo" class="avatar" alt="">
                                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    </el-upload>
                                    <el-progress v-show="uploadInProgress" :percentage="uploadPercentage"></el-progress>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="品牌名称：" prop="name">
                                <el-input clearable v-model="formData.name">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="品牌介绍：" prop="descript">
                                <el-input clearable v-model="formData.descript">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input clearable v-model="formData.sort" type="number" placeholder="填写排序值">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input clearable type="textarea" v-model="formData.remarks" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%" :before-close="closeDialog"
                   :close-on-click-modal="true">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="品牌状态：">
                            <el-select v-model="filterData.state" clearable placeholder="logo启用状态">
                                <el-option v-for="item in stateFilter" :key="item.value" :label="item.label"
                                           :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="品牌名称：" >
                            <el-input clearable type="text" v-model="filterData.name"  placeholder="请输入品牌名称"/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getList, edit, create, del, batchPublish, changeSortValue, batchNotPublish, batchDelete } from '@/api/platform/brand/brandLogo'
import { uploadFile } from '@/api/platform/common/file'
// eslint-disable-next-line no-unused-vars
import { debounce, showLoading, hideLoading } from '@/utils/common'
export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getList(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) {
                return this.screenHeight - 244
            }
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            addLoading: false,
            alertName: '品牌',
            queryVisible: false,
            action: '编辑',
            classType: '0',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentClass: null,
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询选项
            mallFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '慧采商城' },
                { value: 0, label: '装备商城' },
            ],
            stateFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '启用' },
                { value: 0, label: '停用' },
            ],
            // 高级查询数据对象
            filterData: {
                logo: null,
                state: null,
                name: null,
                useType: null,
                mallType: 0,
                orderBy: 1
            },
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入品牌名称', trigger: 'blur' }],
            },
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [],
            orgDataTable: [],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            allData: [],
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
                //品牌ID
                brandId: '',
                // 品牌logo地址
                logo: '',
                //品牌介绍
                descript: '',
                // 备注
                remarks: '',
                // 启用状态 1启用 0停用
                state: 0,
                // 商城类型
                mallType: 0
            },
            // logo: '',
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            Array: [],
            requestParams: {},
            uploadPercentage: 0,
            uploadInProgress: false
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        // this.setUnitMeasur()
    },
    created () {
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy
        }
        getList(params).then(res => {
            this.pages = res
            this.tableData = res.list
        })
        this.getParams()
    },
    methods: {
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                logo: null,
                state: null,
                name: null,
                useType: null,
                mallType: 0,
                orderBy: 1
            }
            done()
        },
        hideDialog () {
            this.filterData = {
                logo: null,
                state: null,
                orderBy: 1
            }
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            this.keywords = ''
            for (let key in this.filterData) {
                if (this.filterData[key] === '') {
                    // return this.clientPop('warn', )
                    this.filterData[key] = null
                }
            }
            this.getParams()
            // 查询请求传参
            showLoading()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.queryVisible = false
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.pages = res
                hideLoading()
                // this.viewList = true
                this.closeDialog()
            })
        },
        // 启用/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.brandId
            })
            if (!this.selectedRows[0]) {
                this.$message.info('未选择数据')
                return
            }
            let warnMsg = num === 1 ? '您确定要启用选中的' + this.alertName + '吗？' : '您确定要停用选中的' + this.alertName + '吗？'
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('启用成功')
                            getList(this.requestParams).then(res => {
                                if (res.list) {
                                    this.tableData = res.list
                                } else {
                                    this.clientPop('warn', res.message, () => {})
                                }
                                this.pages = res
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('停用成功')
                            getList(this.requestParams).then(res => {
                                if (res.list) {
                                    this.tableData = res.list
                                } else {
                                    this.clientPop('warn', res.message, () => {})
                                }
                                this.pages = res
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该' + this.alertName + '吗？', async () => {
                showLoading()
                del({ id: scope.row.brandId }).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                this.$message.info('未选择数据')
                return
            }
            this.clientPop('info', '您确定要删除选中的' + this.alertName + '吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.brandId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 上传图片
        async uploadImg (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            this.addLoading = true
            uploadFile(form).then(res => {
                this.formData.logo =  res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                this.formData.logoId = res[0].recordI
                this.$message.success('上传成功')
                this.addLoading = false
            }).catch(() => {
                this.addLoading = false
            })
        },
        handleUploadChange (file) {
            if(file.status == 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if(this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if(file.status == 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        handleClose () {
        },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = scope.row
            this.action = '编辑'
            this.logo = scope.row.logo
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if(!this.changedRow[0]) {
                return this.changedRow.push({ brandId: row.brandId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if(item.brandId === row.brandId) {
                    return i
                }
            })
            if(arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ brandId: row.brandId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                this.$message.info('当前没有排序值被修改')
                return
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('修改成功')
                        this.getTableData()
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.pages = res
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
            })
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
            this.logo = ''
        },
        // 保存新增/删除
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.action === '编辑') {
                        return this.handleEditData()
                    }
                    this.handleCreateData()
                }
            })
        },
        // 修改数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message === '操作成功') {
                    this.$message.success('操作成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        // 保存数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-form-item.uploader {
    height: 200px;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload
.avatar-uploader {
    /deep/ .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/ .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}
/deep/ .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

/deep/ .el-dialog {
    height: 500px !important;
    padding: 0;

    .el-dialog__header {
        margin-bottom: 20px;
        padding: 10px;
        text-align: center;
        background-color: red;
        font-weight: bold;
        background: url(../../../../assets/test.png) no-repeat;

        .el-dialog__title {
            color: #fff;

        }
    }

    .el-dialog__body {
        height: 280px;
        margin-top: 100px;
    }

    .el-dialog__close.el-icon.el-icon-close::before {
        width: 44px;
        height: 44px;
    }
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
