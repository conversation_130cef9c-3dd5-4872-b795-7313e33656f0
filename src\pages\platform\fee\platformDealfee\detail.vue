<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;"  v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="服务交易费记录详情" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="服务交易费记录明细" name="baseInfoDtl" :disabled="clickTabFlag" />
                <el-tab-pane label="余额变动记录" name="balanceODtls" :disabled="clickTabFlag" />
                <div id="tabs-content">
                    <div id="baseInfo" class="con">
                        <div class="tabs-title" id="baseInfo">服务交易费记录详情</div>
                        <el-form :model="formDate" label-width="200px"  ref="formDateRef" :disabled="false" class="demo-ruleForm">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="平台计费编号：" prop="platformDealFeeNu">
                                        <span>{{formDate.platformDealFeeNu}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="企业名称：" prop="enterpriseName">
                                        <span>{{formDate.enterpriseName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="剩余余额：" prop="remainingSum" >
                                        <span style="color: red">{{formDate.remainingSum}}元</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="总服务费：" prop="dealAmount" >
                                        <span style="color: red">{{formDate.dealAmount}}元</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="总缴费金额：" prop="paymentAmount" >
                                        <span style="color: red">{{formDate.paymentAmount}}元</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="总待缴费金额：" prop="residuePayFee" >
                                        <span style="color: red">{{formDate.residuePayFee}}元</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="总交易金额：" prop="totalDeal" >
                                        <el-tooltip :content="formDate.totalDeal" placement="top">
                                            <span style="color: red">{{(formDate.totalDeal / 10000).toFixed(2)}}万元</span>
                                        </el-tooltip>
                                    </el-form-item>
                                </el-col>
<!--                                <el-col :span="12">-->
<!--                                    <el-form-item label="总交易金额：" prop="totalDeal" >-->
<!--                                        <span style="color: red">{{formDate.totalDeal}}元</span>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="免费交易额度：" prop="exemptTotalAmount" >
                                        <span style="color: red">{{formDate.exemptTotalAmount}}元</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="占用免费交易额度：" prop="thisTotalExempt" >
                                        <span style="color: red">{{formDate.thisTotalExempt}}元</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="信用额度：" prop="arrearage" >
                                        <span style="color: red">{{formDate.arrearage}}元</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="可欠费过期时间时长：" prop="arrearageDateNum" >
                                        {{formDate.arrearageDateNum == null?'无限制':formDate.arrearageDateNum}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="可欠费过期时间类型：" prop="arrearage" >
                                        <sapn v-if="formDate.arrearageDateType == 1">天</sapn>
                                        <sapn v-if="formDate.arrearageDateType == 2">月</sapn>
                                        <sapn v-if="formDate.arrearageDateType == 3">年</sapn>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="是否欠费：" prop="arrearageDateTime" >
                                        <el-tag type="success" v-if="formDate.arrearageDateTime == null">否</el-tag>
                                        <el-tag type="danger" v-if="formDate.arrearageDateTime != null">是</el-tag>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="欠费时间：" prop="arrearageDateTime" >
                                        {{formDate.arrearageDateTime== null?formDate.arrearageDateTime:formDate.arrearageDateTime.split(" ")[0]}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="是否停止交易：" prop="stopServe" >
                                        <el-tag type="success"  v-show="formDate.stopServe==0">否</el-tag>
                                        <el-tag type="danger"  v-show="formDate.stopServe==1">是</el-tag>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="创建时间：" prop="gmtCreate">
                                        {{formDate.gmtCreate}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <div id="baseInfoDtl" class="con" >
                        <div class="tabs-title" id="baseInfoDtl">服务交易费记录明细</div>
                        <div class="e-table"  style="background-color: #fff" v-loading="dtlTableListLoading">
                                                        <div class="top" style="height: 50px; padding-left: 10px">
                                                            <div class="left">
                                                                <div class="search_box" style="margin-left: 20px">
                                                                    <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords">
                                                                        <img src="@/assets/search.png" slot="suffix" @click="onSearch" />
                                                                    </el-input>
                                                                </div>
                                                            </div>
                                                        </div>
                            <el-table
                                border
                                style="width: 100%"
                                max-height="342px"
                                ref="tableListRef"
                                :data="feeDtls"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column  prop="relevanceNu" label="对账单编号"  width="160"/>
                                <el-table-column  prop="relevanceNu" label="服务类型"  width="160">
                                    <template v-slot="scope">
                                        <span v-show="scope.row.serveType==1">店铺交易服务费</span>
                                        <span v-show="scope.row.serveType==2">合同履约服务费用</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="projectEnterpriseName" label="采购单位企业" width="260"/>
                                <el-table-column prop="enterpriseName" label="供货单位" width="260"/>
                                <el-table-column prop="dealAmount" label="交易金额" width="100"/>
                                <el-table-column prop="useExemptFree" label="占用免费交易金额" width="100"/>
                                <el-table-column prop="exceedFree" label="需缴费交易金额" width="100"/>
                                <el-table-column prop="state" label="状态">
                                    <template v-slot="scope">
                                        <el-tag type="success"  v-show="scope.row.state==0">正常</el-tag>
                                        <el-tag type="danger"  v-show="scope.row.state==1">已作废</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="payType" label="缴费类型" width="100">
                                    <template v-slot="scope">
                                        <el-tag  v-show="scope.row.payType==1">正常缴费</el-tag>
                                        <el-tag  v-show="scope.row.payType==2">免费额度</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="feeRatio" label="收取比例（‰）" width="120"/>
                                <el-table-column prop="serveFee" label="服务费用" width="100"/>
                                <el-table-column prop="payFee" label="已缴费金额" width="120"/>
                                <el-table-column prop="finishPayFee" label="是否已完成缴费" width="120">
                                    <template v-slot="scope">
                                        <el-tag type="danger"  v-show="scope.row.finishPayFee==0">否</el-tag>
                                        <el-tag type="success"  v-show="scope.row.finishPayFee==1">是</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="residuePayFee" label="剩余未缴费金额" width="120"/>
<!--                                <el-table-column prop="dealFeeAmount" label="平台免费交易额度"/>-->
                                <el-table-column prop="useBalance" label="缴费使用余额金额" width="120"/>
<!--                                <el-table-column prop="dealFeeQuota" label="可信用额度"/>-->
<!--                                <el-table-column prop="feeEndDateNum" label="可欠费时长"/>-->
<!--                                <el-table-column prop="feeEndDateType" label="可欠费时长类型">-->
<!--                                    <template v-slot="scope">-->
<!--                                        <span v-show="scope.row.feeEndDateType==1">天</span>-->
<!--                                        <span v-show="scope.row.feeEndDateType==2">月</span>-->
<!--                                        <span v-show="scope.row.feeEndDateType==3">年</span>-->
<!--                                    </template>-->
<!--                                </el-table-column>-->
                                <el-table-column prop="gmtCreate" label="交易时间" width="160"/>
                            </el-table>
                            <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                                           @currentChange="currentChange" @sizeChange="sizeChange" />
                        </div>
                    </div>
                    <div id="balanceODtls" class="con" >
                        <div class="tabs-title" id="balanceODtls">余额变动记录</div>
                        <div class="e-table"  style="background-color: #fff" v-loading="dtlTableListLoading">
                            <!--                            <div class="top" style="height: 50px; padding-left: 10px">-->
                            <!--                                <div class="left">-->
                            <!--                                    <div class="search_box" style="margin-left: 20px">-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <el-table
                                border
                                style="width: 100%"
                                max-height="342px"
                                ref="tableListRef"
                                :data="formDate.balanceODtls"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column  prop="title" label="操作说明"  width="160"/>
                                <el-table-column  prop="amount" label="变动金额(元)"  width=""/>
                                <el-table-column  prop="beforeAmount" label="修改前余额(元)"  width=""/>
                                <el-table-column  prop="afterAmount" label="修改后余额(元)"  width=""/>
                                <el-table-column  prop="operateUn" label="关联编号"  width="200"/>
                                <el-table-column  prop="operateType" label="关联类型"  width="160">
                                    <template v-slot="scope">
                                        <span v-show="scope.row.operateType==1">服务交易缴费</span>
                                        <span v-show="scope.row.operateType==2">对账单操作</span>
                                        <span v-show="scope.row.operateType==3">对账单操作</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="操作时间" width="160"/>
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { mapState } from 'vuex'
import {
    findDealFeeDateBySn, getDealFeeDateList
} from '@/api/fee/feeApi'
import ComPagination from '@/components/pagination/pagination.vue'

export default {
    components: { ComPagination },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return  dateStr.split(' ')[0]
        }
    },
    data () {
        return {
            keywords: '',
            pages: {
                totalCount: null,
                pageSize: 20,
                currPage: 1,
            },
            dtlTableListLoading: false,
            formDate: {},
            feeDtls: [],
            selectTableList: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            formLoading: false,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    created () {
        this.getFormDtl()
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        onSearch () {
            this.getDtlListM()
        },
        currentChange (page) {
            this.pages.currentPage = page
            this.getDtlListM()
        },
        sizeChange (size) {
            this.pages.pageSize = size
            this.getDtlListM()
        },
        getDtlListM () {
            let params = {
                page: this.pages.currentPage,
                limit: this.pages.pageSize,
                platformDealFeeId: this.formDate.platformDealFeeId
            }
            if (this.keywords != '' && this.keywords != null) {
                params.keywords = this.keywords
            }
            getDealFeeDateList(params).then(res => {
                this.feeDtls = res.list
                this.pages = res
            })
        },
        getFormDtl () {
            this.formLoading = true
            findDealFeeDateBySn({ sn: this.$route.query.sn }).then(res => {
                this.formDate = res
                this.getDtlListM()
                this.formLoading = false
            }).finally(() => {
                this.formLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },

        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'baseInfoDtl']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

/deep/ .selectDealDia {
    .el-dialog__body {
        margin-top: 0px;
    }
}

</style>