<template>
    <div class="bar">
        <span v-if="orgName" style="margin-right: 40px;">当前公司：{{ orgName }}</span>
        <img src="../assets/images/ico_shouye .png" @click="toIndex" title="首页" alt="">
        <div class="msg df">
            <img  @click="$router.push('/supplierSys/product/inBox')" src="../assets/images/ico_xxtz .png" title="消息提醒" alt="">
            <div  class="dot" v-show="msgList.length > 0"><div>{{ msgList.length }}</div></div>
        </div>
        <img src="../assets/images/ico_grxx .png" @click="$router.push('/user/userCenter')" title="个人中心" alt="">
        <img @click="loginOutM" src="../assets/images/ico_tc.png" title="退出" alt="">
        <!-- <img src="../assets/images/ico_gengduo .png" title="更多" alt=""> -->
    </div>
</template>
<script>
import { loginOut } from '@/api/frontStage/userCenter'
import { getMessageNum } from '@/api/platform/mail/inbox'
import { mapState } from 'vuex'
import { showLoading, hideLoading } from '../utils/common'

export default {
    props: {
        orgName: {
            type: String,
            required: false,
            default: ''
        }
    },
    created () {
        this.getMessages()
    },
    computed: {
        ...mapState(['userInfo'])
    },
    data () {
        return {
            receiveType: 0,
            msgList: []
        }
    },
    methods: {
        async loginOutM () {
            showLoading()
            await loginOut()
            hideLoading()
            // 状态保持清除后刷新页面
            localStorage.removeItem('token')
            this.$store.commit('setUserInfo', {})
            window.location.href = '/'
        },
        getMessages () {
            getMessageNum({ receiveType: this.receiveType }).then(res=>{
                this.msgList = res
            })
        },
        toIndex () {
            this.$router.push('/index')
        }
    }
}
</script>
<style scoped lang="scss">
    .bar{
        width: 100%;
        height: 50px;
        background-color: #EFF2F6;
        display: flex;
        align-items: center;
        justify-content: right;
        .msg {
            width: 20px;
            height: 20px;
            margin-right: 32px;
            position: relative;
            img {margin-right: 0;}
            .dot {
                width: 12px;
                height: 12px;
                line-height: 12px;
                text-align: center;
                border-radius: 50%;
                color: #fff;
                background-color: #F73838;
                position: absolute;
                top: -4px;
                right: -5px;
                div {
                    transform: scale(0.7);
                }
            }
        }
        img{
            width: 20px;
            height: 20px;
            margin-right: 32px;
            cursor: pointer;
        }
    }
</style>