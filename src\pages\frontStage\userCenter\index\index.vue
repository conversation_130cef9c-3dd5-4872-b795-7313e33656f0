<template>
    <div class="root">
        <div class="main dfb mt20">
            <div class="main_left">
                <el-menu :default-active="activeItem" :unique-opened="true" class="el-menu-vertical-user" :router="true">
                        <!-- 首页 -->
                        <el-menu-item class="userCenter_invoice" :route="{ path: '/user' }" index="1">
                            <template slot="title">
                                <img class="icon" src="" alt="">
                                <span>个人中心</span>
                            </template>
                        </el-menu-item>
                        <el-submenu class="userCenter_order" index="2" v-if="showPlan">
                            <template slot="title">
                                <img class="icon" src="" alt="">
                                <span>采购计划</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item :route="{ path: '' }" index="2-1">
                                    <template slot="title">
                                        <div class="dot"></div> 零星采购计划
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                        <el-submenu class="userCenter_order" index="3">
                            <template slot="title">
                                <img class="icon" src="" alt="">
                                <span>订单中心</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item :route="{ path: '/user/order' }" index="3-1">
                                    <template slot="title">
                                        <div class="dot"></div> 我的订单
                                    </template>
                                </el-menu-item>
                                <el-menu-item :route="{ path: '/user/orderReview' }" index="3-2">
                                    <template slot="title">
                                        <div class="dot"></div> 评价晒单
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                        <el-submenu class="userCenter_like" index="4">
                            <template slot="title">
                                <img class="icon" src="" alt="">
                                <span>我的关注</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item :route="{ path: '/user/followingProduct' }" index="4-1">
                                    <template slot="title">
                                        <div class="dot"></div> 商品关注
                                    </template>
                                </el-menu-item>
                                <el-menu-item :route="{ path: '' }" index="4-2">
                                    <template slot="title">
                                        <div class="dot"></div> 店铺关注
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                        <!-- 我的发票 -->
                        <el-menu-item class="userCenter_invoice" :route="{ path: '' }" index="5">
                            <template slot="title">
                                <img class="icon" src="" alt="">
                                <span>我的发票</span>
                            </template>
                        </el-menu-item>
                        <!-- 我的购物车 -->
                        <el-menu-item class="userCenter_cart" :route="{ path: '' }" index="6">
                            <template slot="title">
                                <img class="icon" src="" alt="">
                                <span>我的购物车</span>
                            </template>
                        </el-menu-item>
                        <!-- 安全设置 -->
                        <el-submenu class="userCenter_security" index="7">
                            <template slot="title">
                                <img class="icon" src="" alt="">
                                <span>安全设置</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item :route="{ path: '' }" index="7-1">
                                    <template slot="title">
                                        <div class="dot"></div> 修改登录密码
                                    </template>
                                </el-menu-item>
                                <!-- 待修改 -->
                                <el-menu-item :route="{ path: '' }" index="7-2">
                                    <template slot="title">
                                        <div class="dot"></div> 修改手机号
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                        <!-- 个人资料 -->
                        <el-submenu class="userCenter_userInfo" index="8">
                            <template slot="title">
                                <img class="icon" src="" alt="">
                                <span>个人资料</span>
                            </template>
                            <el-menu-item-group>
                                <el-menu-item :route="{ path: '/user/shippingAddr' }" index="8-1">
                                    <template slot="title">
                                        <div class="dot"></div> 收货地址
                                    </template>
                                </el-menu-item>
                                <el-menu-item :route="{ path: '/user/changeAvatar' }" index="8-2">
                                    <template slot="title">
                                        <div class="dot"></div> 修改头像、昵称
                                    </template>
                                </el-menu-item>
                            </el-menu-item-group>
                        </el-submenu>
                    </el-menu>
            </div>
            <div class="main_right">
                <div class="right_top dfb">
                    <div class="left dfc">
                        <div class="left_1">
                            <img src="../../../../assets/images/img/touxiang.png" alt="" />
                            <div>ID：2158987</div>
                        </div>
                        <div class="left_2">
                            <span>Admin张三</span>
                            <div class="dfc">我要开店 <i class="el-icon-arrow-right"></i></div>
                        </div>
                    </div>
                    <div class="right dfb">
                        <div class="item dfc" v-for="(item, i) in iconList" :key="i">
                            <img :src="item.img" alt="" />
                            <span>{{ item.name }}</span>
                            <div v-if="item.num" class="num">{{ item.num }}</div>
                        </div>
                    </div>
                </div>

                <div class="logisticsBox dfb mt20">
                    <div class="left">
                        <div class="titleBox dfb">
                            <div class="dfa">
                                <div class="line"></div>
                                <div class="title">我的物流</div>
                            </div>
                        </div>
                        <div class="left_list">
                            <div class="left_item mt30 dfb" v-for="(item, i) in 3" :key="i">
                                <div class="item_1 dfa">
                                    <img src="../../../../assets/images/img/image.png.png" alt="" />
                                    <div class="item_1_right">
                                        <div>您的订单已由亲属代收。如有疑问您可以联系配送员【张华军…</div>
                                        <div class="mt10">普通快递 | 2022-10-22 09:36:3</div>
                                    </div>
                                </div>
                                <div class="item_2 dfa">
                                    <el-button style="padding: 0" type="text">查看详情</el-button>
                                    <span></span>
                                    <el-button type="text" style="padding: 0">查看发票</el-button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="right">
                        <div class="titleBox dfb">
                            <div class="dfa">
                                <div class="line"></div>
                                <div class="title">我的关注</div>
                            </div>
                        </div>
                        <div class="gzNum dfb">
                            <div class="dfc">
                                <div>16</div>
                                <span> 商品关注</span>
                            </div>
                            <div class="dfc">
                                <div>16</div>
                                <span> 店铺关注</span>
                            </div>
                        </div>
                        <div class="spguanTitle dfb">
                            <div>商品关注</div>
                            <div>更多 <i class="el-icon-arrow-right"></i></div>
                        </div>
                        <div class="right_list dfb">
                            <div class="item" v-for="(item, i) in 4" :key="i">
                                <img src="../../../../assets/images/img/image.png.png" alt="" />
                                <div>不锈钢沉头自攻螺丝…</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cnxh">
                    <div class="titleBox dfb" style="border: 0">
                        <div class="dfa">
                            <div class="line"></div>
                            <div class="title">我的物流</div>
                        </div>
                        <div class="dfa" style="font-size: 14px">
                            <i class="el-icon-refresh-right" style="margin-right: 5px; font-weight: 600; font-size: 16px"></i>换一组
                        </div>
                    </div>
                </div>
                <!-- 猜您喜欢列表 -->
                <div class="goodlist dfb">
                    <div class="goodItem dfc" v-for="(item, i) in 8" :key="i">
                        <img src="../../../../assets/images/img/1668320745440.jpg" alt="" />
                        <div class="title">多联 内丝弯头 PPR 20mm</div>
                        <div class="desc">卫浴五金及配件</div>
                        <div class="price">￥3875.00</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'personalCenter',
    data () {
        return {
            showPlan: true,
            activeItem: '1',
            loginData: {},
            iconList: [
                {
                    img: require('../../../../assets/images/img/ico_fukuan.png'),
                    name: '待付款',
                    num: 8,
                },
                {
                    img: require('../../../../assets/images/img/ico_shouhuo.png'),
                    name: '待收货',
                },
                {
                    img: require('../../../../assets/images/img/ico_pingjia.png'),
                    name: '待评价',
                    num: 3,
                },
                {
                    img: require('../../../../assets/images/img/ico_shouhou.png'),
                    name: '退还/售后',
                },
                {
                    img: require('../../../../assets/images/img/ico_dingdan.png'),
                    name: '全部订单',
                },
            ],
        }
    },
    created () {
    },
    mounted () {
        switch(this.$route.path) {
        case '/user':
            this.activeItem = '1'
            break
        case '/user/changeAvatar':
            this.activeItem = '1'
            break
        }
    },
    methods: {
        handleOpen (key, keyPath) {
            console.log(key, keyPath)
        },
        handleClose (key, keyPath) {
            console.log(key, keyPath)
        },
    },
}
</script>
<!-- <style>
.root .el-submenu__title {
    width: 200px !important;
    background: #fff !important;
    color: #333 !important;
    font-size: 16px !important;
    font-weight: 500 !important;
}
.root .el-menu-vertical-demo.el-menu .el-submenu.is-opened .el-submenu__title {
    background-color: #fff !important;
    color: #333 !important;
    font-size: 16px !important;
    font-weight: 500 !important;
}
.root .el-menu-vertical-demo.el-menu .el-submenu__title:hover,
.el-menu-vertical-demo.el-menu .el-menu-item:hover {
    background-color: #fff !important;
}
.root .el-menu-vertical-demo.el-menu .el-menu-item:hover {
    background-color: #fff !important;
}
.root .el-menu-item {
    width: 200px !important;
    font-size: 14px !important;
    color: #666;
}
.root .el-menu-item:hover {
    width: 200px !important;
    font-size: 14px !important;
}
.root .el-menu-vertical-demo.el-menu .el-submenu i.el-icon-arrow-down::before {
    background: 0 !important;
}
</style> -->
<style scoped lang="scss">
@import '../../../../assets/css/menuStyle.css';

div {
    line-height: 1;
}

.root {
    width: 100%;
    padding-top: 20px;
    background: #f5f5f5;
}

.main {
    width: 1326px;
    margin: 0 auto;
    min-height: 100vh;
    align-items: flex-start;

    .main_left {
        width: 200px;
        height: 1280px;
        background: #fff;

        i {color: #333;}

        /deep/.el-menu-item {
            &.is-active {
                .arrow {
                    background: url(../../../../assets/images/userCenter/go2.png);
                }
            }

            &.userCenter_invoice {
                .icon {
                    width: 18px;
                    margin-right: 6px;
                    background: url(../../../../assets/images/userCenter/ico3.png);
                }

                &.is-active {
                    .icon {
                        background: url(../../../../assets/images/userCenter/ico3w.png);
                    }
                }
            }

            &.userCenter_cart {
                .icon {
                    background: url(../../../../assets/images/userCenter/ico4.png);
                }

                &.is-active {
                    .icon {
                        background: url(../../../../assets/images/userCenter/ico4w.png);
                    }
                }
            }
        }

        /deep/.el-submenu {
            &.userCenter_order {
                .icon {background: url(../../../../assets/images/userCenter/ico1.png);}
                &.is-opened {
                    .icon {background: url(../../../../assets/images/userCenter/ico1w.png);}
                }
            }
            &.userCenter_like {
                .icon {background: url(../../../../assets/images/userCenter/ico2.png);}
                &.is-opened {
                    .icon {background: url(../../../../assets/images/userCenter/ico2w.png);}
                }
            }
            &.userCenter_security {
                .icon {background: url(../../../../assets/images/userCenter/ico5.png);}
                &.is-opened {
                    .icon {background: url(../../../../assets/images/userCenter/ico5w.png);}
                }
            }
            &.userCenter_userInfo {
                .icon {background: url(../../../../assets/images/userCenter/ico6.png);}
                &.is-opened {
                    .icon {background: url(../../../../assets/images/userCenter/ico6w.png);}
                }
            }

        }
    }

    .main_right {
        width: 1106px;

        .right_top {
            width: 100%;
            height: 200px;

            .left {
                background: #216ec6;
                box-shadow: 4px 4px 10px 0px rgba(0, 133, 255, 0.29);
                width: 300px;
                height: 200px;

                .left_1 {
                    img {
                        width: 100px;
                        height: 100px;
                    }

                    div {
                        margin-top: 15px;
                        font-size: 14px;
                        color: #ffffff;
                        font-weight: 400;
                    }
                }

                .left_2 {
                    margin-left: 20px;

                    span {
                        font-size: 16px;
                        color: #ffffff;
                        font-weight: 500;
                    }

                    div {
                        width: 100px;
                        height: 30px;
                        border: 1px solid rgba(255, 255, 255, 1);
                        font-size: 14px;
                        color: #ffffff;
                        font-weight: 500;
                        margin-top: 34px;
                    }
                }
            }

            .right {
                width: 806px;
                height: 180px;
                background: #fff;
                padding: 0 97px;

                .item {
                    flex-direction: column;
                    position: relative;

                    span {
                        font-size: 14px;
                        color: #333333;
                        font-weight: 400;
                        display: inline-block;
                        margin-top: 20px;
                    }

                    .num {
                        position: absolute;
                        top: -10px;
                        right: -8px;
                        color: #fff;
                        width: 20px;
                        height: 20px;
                        background: #e22022;
                        border-radius: 50%;
                        text-align: center;
                        line-height: 20px;
                    }
                }
            }
        }

        .logisticsBox {
            .left {
                background: #ffffff;
                width: 700px;
                height: 350px;

                .left_list {
                    padding: 0 20px;

                    .left_item {
                        img {
                            width: 60px;
                            height: 60px;
                            margin-right: 20px;
                        }

                        .item_1_right {
                            font-size: 14px;
                            color: #666666;
                            letter-spacing: 0;
                            font-weight: 400;
                        }

                        .item_2 {

                            // width: 120px;
                            span {
                                width: 1px;
                                height: 18px;
                                background: #d9d9d9;
                                margin: 0 10px;
                                margin-top: 3px;
                            }
                        }
                    }
                }
            }

            .right {
                background: #ffffff;
                width: 386px;
                height: 350px;

                .gzNum {
                    width: 100%;
                    height: 110px;
                    border-bottom: solid 1px #e6e6e6;
                    padding: 0 85px;

                    .dfc {
                        flex-direction: column;

                        div {
                            font-size: 30px;
                            color: #333333;
                            text-align: center;
                            font-weight: 700;
                            margin-bottom: 10px;
                        }

                        span {
                            font-size: 14px;
                            color: #808080;
                            text-align: center;
                            font-weight: 400;
                        }
                    }
                }

                .spguanTitle {
                    width: 100%;
                    height: 56px;
                    padding: 0 20px;
                }

                .spguanTitle div:nth-child(1) {
                    font-size: 16px;
                    color: #333333;
                    text-align: center;
                    font-weight: 400;
                }

                .spguanTitle div:nth-child(2) {
                    font-size: 14px;
                    color: #216ec6;
                    text-align: center;
                    font-weight: 400;
                }

                .right_list {
                    padding: 0 20px;
                    width: 100%;

                    img {
                        width: 60px;
                        height: 60px;
                    }

                    .item {
                        div {
                            font-size: 14px;
                            color: #333333;
                            line-height: 18px;
                            font-weight: 400;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 2;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            word-break: break-all;
                            width: 70px;
                            margin-top: 3px;
                        }
                    }
                }
            }
        }

        .titleBox {
            padding: 0 20px;
            width: 100%;
            border-bottom: solid 1px #e6e6e6;
            height: 50px;

            .line {
                background: #216ec6;
                width: 3px;
                height: 20px;
                margin-right: 10px;
            }

            .title {
                font-size: 20px;
                color: #333333;
                font-weight: 500;
            }
        }

        .cnxh {
            width: 100%;
            background: #fff;
            height: 50px;
            margin: 20px 0;
        }

        .goodlist {
            margin-top: 20px;
            flex-wrap: wrap;

            .goodItem {
                width: 260px;
                height: 300px;
                background: #fff;
                margin-bottom: 20px;
                flex-direction: column;

                img {
                    width: 160px;
                    height: 160px;
                }

                .title {
                    font-size: 14px;
                    color: #333333;
                    letter-spacing: 0;
                    text-align: center;
                    font-weight: 400;
                    margin-top: 20px;
                }

                .desc {
                    font-size: 14px;
                    color: #999999;
                    text-align: center;
                    font-weight: 400;
                    margin: 10px 0;
                }

                .price {
                    font-size: 16px;
                    color: #d43030;
                    text-align: center;
                    line-height: 23px;
                    font-weight: 400;
                }
            }
        }
    }
}
</style>
