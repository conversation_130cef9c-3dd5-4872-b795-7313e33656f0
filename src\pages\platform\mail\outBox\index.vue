<template>
    <div class="base-page">
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="onWriter">写消息</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-input clearable type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table">
                <el-table
                    class="table"
                    ref="receive"
                    v-loading="mainTableLoading"
                    :height="rightTableHeight"
                    :data="sendMessageTable"
                    border highlight-current-row
                    @row-click="handleCurrentInventoryClick"
                    @current-change="handleCurrentChange"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="操作" width="80">
                        <template v-slot="scope">
                            <span class="action" @click="onDel(scope)"><img src="@/assets/btn/delete.png" alt=""></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="标题" prop="title">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.title }}</span>
                        </template>
                    </el-table-column>
                    <!--          <el-table-column label="收件人消息账号" prop="receiveCode">-->
                    <!--          </el-table-column>-->
                    <!--          <el-table-column label="是否全读" width="120" >-->
                    <!--            <template v-slot="scope">-->
                    <!--              <el-tag v-if="scope.row.allRead==1" type="success">已读</el-tag>-->
                    <!--              <el-tag v-else >未读</el-tag>-->
                    <!--            </template>-->
                    <!--          </el-table-column>-->
                    <el-table-column label="发送时间" prop="sendDate"/>
                </el-table>
            </div>
            <!-- 分页器 -->
            <Pagination
                :total="pages.totalCount"
                :limit="20"
                :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange"
                @sizeChange="sizeChange"
            />
        </div>

        <!--写消息-->
        <div class="right" v-show="viewList !== true">
            <div class="e-form" style="padding: 0 10px 10px;" v-if="viewList === 'class'">
                <div class="tabs-title">写消息</div>
                <el-form :rules="formRules" ref="writer" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="发送用户：" prop="receiveName">
                                <el-input disabled v-model="formData.receiveUserListTitle"/>
                                <el-button size="mini" type="primary" @click="importDeviceSelect()">选择</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="发送供应商：" prop="shopName">
                                <el-input disabled v-model="formData.receiveShopListTitle"/>
                                <el-button size="mini" type="primary" @click="shopDialog()">选择</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!--发送消息给用户-->
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="消息标题：" prop="title">
                                <el-input clearable clean v-model="formData.title" placeholder="请输入标题"/>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="showDevFunc" :span="12">
                            <el-form-item label="是否提醒：" prop="remind">
                                <el-radio-group v-model="formData.remind">
                                    <el-radio :label="1">是</el-radio>
                                    <el-radio :label="0">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col class="editorCol" :span="24">
                            <el-form-item label="消息内容：" prop="content">
                                <editor v-model="formData.content"/>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row style="margin-top: 20px;">
                        <el-col :span="20" v-loading="fileLoading">
                            <el-form-item class="upload-item" label="附件资料：" prop="">
                                <el-upload
                                    action="fakeaction"
                                    multiple
                                    :limit="20"
                                    :show-file-list="true"
                                    :file-list="fileList"
                                    :before-upload="beforeOneOfFilesUpload"
                                    :http-request="uploadOneOfFiles"
                                    :on-remove="handleRemove"
                                    accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                >
                                    <el-button size="small" type="primary">点击上传</el-button>
                                    <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="buttons">
                    <el-button v-if="viewList === 'class'" type="primary" @click="sendMessage">发送</el-button>
                    <el-button @click="viewList=true">返回</el-button>
                </div>
            </div>
        </div>
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="receiveMessageFilter" ref="form" label-width="120px" :inline="false">
                <!--        <el-row>-->
                <!--          <el-col :span="12">-->
                <!--            <el-form-item label="是否全读：">-->
                <!--              <el-select v-model="filterData.allRead" >-->
                <!--                <el-option v-for="item in allReads" :key="item.value" :label="item.label" :value="item.value">-->
                <!--                </el-option>-->
                <!--              </el-select>-->
                <!--            </el-form-item>-->
                <!--          </el-col>-->
                <!--        </el-row>-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="标题：">
                            <el-input v-model="filterData.title" placeholder="请输入标题" clearable/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="发送时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
        </el-dialog>

        <!--选择用户库-->
        <el-dialog title="选择收件人" :v-show="formData.receiveType==1" v-loading="inventoryTableLoading" v-dialogDrag :visible.sync="receiveDialog" width="70%" style="margin-left: 20%;" :close-on-click-modal="true">
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left-btn1">
                        <el-checkbox v-model="checkAllUser" label="发送所有人" :indeterminate="false" @change="toggleSelectAllUser"/>
                    </div>
                    <div class="search_box">
                        <el-input clearable type="text" @keyup.enter.native="getUserList" placeholder="输入搜索关键字" v-model="userList.keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="getUserList" alt=""/>
                        </el-input>
                    </div>
                </div>
                <el-table
                    ref="tableUser"
                    highlight-current-row
                    border
                    :data="userList.tableData"
                    class="table"
                    @selection-change="handleSelectionChange"
                    @row-click="handleCurrentInventoryClickUser"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column prop="userNumber" label="用户编号" width="200"/>
                    <el-table-column prop="realName" label="真实姓名" width="200"/>
                    <el-table-column prop="nickName" label="昵称"/>
                    <el-table-column prop="userMobile" label="手机号码" width="200"/>
                </el-table>
            </div>
            <span slot="footer">
                    <Pagination
                        v-show="userList.tableData || userList.tableData.length > 0"
                        :total="userList.paginationInfo.total"
                        :pageSize.sync="userList.paginationInfo.pageSize"
                        :currentPage.sync="userList.paginationInfo.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                    <el-button type="primary" style="margin-top: 20px" @click="onUserList">确定</el-button>
                    <el-button style="margin-top: 20px" @click="receiveDialog = false">取消</el-button>
                </span>
        </el-dialog>
        <!--选择供应商发送-->
        <el-dialog v-dialogDrag title="选择供应商" :v-show="formData.receiveType==0" v-loading="inventoryTableLoading" :visible.sync="showDeviceDialog" width="70%" style="margin-left: 20%;">
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left-btn1">
                        <!--               Shop和supplier反着的       -->
                        <el-checkbox v-model="checkAllShop" label="发送所有供应商" :indeterminate="false" @change="toggleSelectAllShop"/>
                        <el-checkbox v-model="checkAllSupplierShops" label="发送所有商铺" :indeterminate="false" @change="toggleSelectAllSupplierShops"/>
                    </div>
                    <div class="search_box">
                        <el-input clearable type="text" @keyup.enter.native="getshopLists" placeholder="输入搜索关键字" v-model="shopList.keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="getshopLists" alt=""/>
                        </el-input>
                    </div>
                </div>

                <el-table
                    ref="tableShop"
                    highlight-current-row border
                    :data="shopList.tableData"
                    class="table"
                    @currentChange="currentChangeUser"
                    @sizeChange="sizeChangeUser"
                    @selection-change="handleSelectionChangeShop"
                    @row-click="handleCurrentInventoryClickShop"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <!--                    <el-table-column prop="serialNum" label="序列号" width="200"/>-->
                    <el-table-column prop="enterpriseName" label="企业名称"/>
                    <el-table-column prop="shopName" label="店铺名称" ref="tableHaveShop"/>

                    <!--                    <el-table-column prop="shopType" label="店铺类型">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <el-tag v-if="scope.row.shopType==0">个体户</el-tag>-->
                    <!--                            <el-tag v-else-if="scope.row.shopType==1" >企业</el-tag>-->
                    <!--                            <el-tag v-else-if="scope.row.shopType==2" >个人</el-tag>-->
                    <!--                            <el-tag v-else type="danger">已关闭</el-tag>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <!--          <el-table-column prop="shopImg" label="店铺log" width="200"/>-->
                    <!--                    <el-table-column prop="detailedAddress" label="详细地址"/>-->
                    <!--                    <el-table-column prop="state" label="店铺状态">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <el-tag v-if="scope.row.state==0">停用</el-tag>-->
                    <!--                            <el-tag v-else-if="scope.row.state==1" >启用</el-tag>-->
                    <!--                            <el-tag v-else type="danger">已关闭</el-tag>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->

                </el-table>
            </div>
            <Pagination
                v-show="shopList.tableData || shopList.tableData.length > 0"
                :total="shopList.paginationInfo.total"
                :pageSize.sync="shopList.paginationInfo.pageSize"
                :currentPage.sync="shopList.paginationInfo.currentPage"
                @currentChange="getShopList"
                @sizeChange="getShopList"
            />
            <span slot="footer">
                <el-button type="primary" style="margin-top: 20px" @click="getSupplierList">确定</el-button>
                <el-button style="margin-top: 20px" @click="showDeviceDialog = false">取消</el-button>
            </span>
        </el-dialog>
    </div>

</template>
<script>
import { del, getMessageList, sendMessageApi } from '@/api/platform/mail/outbox'
import Pagination from '@/components/pagination/pagination'
import { getUserList } from '@/api/platform/user/userInquire'
import { getShopPassList } from '@/api/platform/shop/shopAudit'
import editor from '@/components/quillEditor'
import { uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { getSupplierShopsList } from '@/api/platform/supplier/supplierAudit'
import { hideLoading, showLoading } from '@/utils/common'

export default {
    components: {
        editor,
        Pagination
    },
    data () {
        return {
            shopSelected: false,
            fileLoading: false,
            mainTableLoading: false,
            uploadData: {
                bucketName: 'mall',
                directory: 'material',
                fileType: 3,
                isResetName: 1,
            },
            fileList: [],
            checkAll: false, //全选控制
            checkAllUser: false, //全选控制
            checkAllShop: false, //全选控制
            checkAllSupplierShops: false, //全选控制
            keywords: null,
            inventoryTableLoading: false,
            filterData: {
                title: null,
                // allRead: null,
                dateValue: [], // 开始时间和结束时间
                orderBy: 1,
            },
            // 收件人高级查询
            receiveMessageFilter: {
                title: null,
                dateValue: [],
            },
            queryVisible: false,
            messageInfo: {},
            sendMessageTable: [], //接收消息列表
            viewList: true,
            stationMessageInfo: {},
            receiveUserList: [], //发送用户集合
            receiveShopList: [],  //发送商铺集合
            receiveSupplierList: [],  //发送供应商集合
            formRules: {
                title: { required: true, message: '请输入标题', trigger: 'blur' },
                content: { required: true, message: '请输入发送消息', trigger: 'blur' },
                remind: { required: true, message: '请选择是否提醒', trigger: 'blur' },
            },

            showDeviceDialog: false, // 选择收件商铺弹窗,
            receiveDialog: false, // 选择收件用户弹窗库
            formData: {
                receiveType: '', // 收件人类型0店铺 1用户2平台
                receiveUserList: [], //收件人id,
                receiveShopList: [], //商铺id
                receiveEnterpriseList: [], //企业id
                receiveUserListTitle: [], //用户展示字段收件人,
                receiveShopListTitle: [], //商铺展示字段收件人,
                receiveSupplierListTitle: [], //供应商展示字段收件人,
                title: null, //标题
                state: 1,
                content: null,
                files: [],
                remind: 0
            },
            //用户数据
            userList: {
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 20,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            pages: {
                currPage: 1,
                pageSize: 20,
                totalCount: 0
            },
            supplierlist: {
                tableData: [],
                keywords: null,
            },
            shopList: {
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            screenHeight: 0,
        }
    },
    created () {
        this.getSendList()
    },
    mounted () {
        this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    methods: {
        handlePreview (file) {
            console.log(file)
        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if (size > 500) {
                this.$message.error('文件大小不能超过500M')
                return false
            }
            return true
        },
        // 多文件上传
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if (uploadRes.code != null && uploadRes.code !== 200) {
                this.fileList.push(file)
                this.fileList.pop()
            } else {
                this.$message.success('上传成功')
                this.formData.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 7,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
            }
        },
        handleExceed () {
            this.$message.error('文件个数不能超出20个')
            return false
        },
        handleRemove (file) {
            let files = this.formData.files
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.formData.files = newFiles
                }
            })
        },

        selectUserAll () {
            let params = {
                page: this.userList.paginationInfo.currentPage,
                limit: this.userList.paginationInfo.total,
                orderBy: this.filterData.orderBy,
            }
            if (this.userList.keywords) {
                params.keywords = this.userList.keywords
            }
            getUserList(params).then(res => {
                this.handleSelectionChange(res.list)
            })

        },
        selectShopAll () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.total,
                orderBy: this.filterData.orderBy,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            getShopPassList(params).then(res => {
                this.handleSelectionChangeShop(res.list)
            })
        },
        toggleSelectAllUser () {
            if (!this.checkAllUser) {
                this.formData.receiveUserListTitle = []
                return this.$refs['tableUser'].clearSelection()
            }
            this.$refs['tableUser'].toggleAllSelection()
            this.selectUserAll()
        },
        toggleSelectAllShop () {
            if (!this.checkAllShop) {
                this.formData.receiveShopListTitle = []
                return this.$refs['tableShop'].clearSelection()
            }
            this.selectShopAll()
            this.$refs['tableShop'].toggleAllSelection()
        },
        toggleSelectAllSupplierShops () {
            this.formData.receiveSupplierListTitle = this.shopList.tableData.filter(item => item.shopId)
            this.formData.receiveSupplierListTitle.forEach(item => {
                this.$refs['tableShop'].toggleRowSelection(item, !this.shopSelected)
            })
            this.shopSelected = !this.shopSelected

        },
        //点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.receive.toggleRowSelection(row, row.flag)
        },
        //点击选中
        handleCurrentInventoryClickShop (row) {
            row.flag = !row.flag
            this.$refs.tableShop.toggleRowSelection(row, row.flag)
        },
        //点击选中发送用户
        handleCurrentInventoryClickUser (row) {
            row.flag = !row.flag
            this.$refs.tableUser.toggleRowSelection(row, row.flag)
        },
        resetSearchConditions () {
            this.filterData.title = ''
            this.filterData.dateValue = []
            this.filterData.orderBy = 1
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getSendList()
        },
        confirmSearch () {
            this.keywords = ''
            this.getSendList()
            this.queryVisible = false
        },
        onWriter () {
            // this.emptyForm()
            this.viewList = 'class'
        },
        emptyForm () {
            for (let key in this.formData) {
                if(!key.includes('file') || !key.includes('remind')) this.formData[key] = null
            }
        },
        currentChangeUser (currPage) {
            this.userList.paginationInfo.currentPage = currPage
            this.getUserList()
        },
        sizeChangeUser (pageSize) {
            this.userList.paginationInfo.pageSize = pageSize
            this.getUserList()
        },
        currentChangeShop (currPage) {
            this.shopList.paginationInfo.currentPage = currPage
            this.getSendList()
        },
        sizeChangeshop (pageSize) {
            this.shopList.paginationInfo.pageSize = pageSize
            this.getUserList()
        },
        // 详情
        handleView (row) {
            //利用$router.push进行跳转
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/mail/outBoxDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'OutBoxDetail',
                params: { row }
            })
        },
        // 信息列表页码跳转
        currentChange (currPage) {
            this.pages.currPage = currPage
            console.log('列表', currPage)
            this.getSendList()
        },
        sizeChange (pageSize) {
            this.pages.pageSize = pageSize
            this.getSendList()
        },
        /**
         * 删除发送消息
         */
        onDel (scope) {
            this.clientPop('info', `您确定要删除标题为：${scope.row.title}吗？`, async () => {
                showLoading()
                let res = await del({ id: scope.row.stationMessageId })
                this.message(res)
                await this.getSendList()
                hideLoading()
            })
        },
        /**
         * 查询所有收件人列表
         *
         * 消息列表
         */
        async getSendList () {
            this.mainTableLoading = true
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                sendType: 2
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.filterData.allRead != null) {
                params.allRead = this.filterData.allRead
            }
            if (this.filterData.title) {
                params.title = this.filterData.title
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            let res = await getMessageList(params)
            this.sendMessageTable = res.list || []
            this.pages = res
            this.mainTableLoading = false
        },
        /**
         * 确定发送企业id
         */
        getSupplierList () {
            this.formData.receiveShopList = this.receiveShopList.map(item => item.enterpriseId)
            this.formData.receiveShopListTitle = this.receiveShopList.map(item => item.enterpriseId + '[' + item.enterpriseName + ']')
            this.showDeviceDialog = false
        },
        getShopList () {
            // this.shopList.tableData = this.receiveShopList.map(item=>{
            //     this.shopList.tableData = item
            //     return item
            // })
            // getSupplierShopsList().then(res=>{
            //     this.shopList.tableData = res.list
            // })
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            this.inventoryTableLoading = true
            getSupplierShopsList(params).then(res => {
                this.shopList.tableData = res.list || []
                this.shopList.paginationInfo.currentPage = res.currPage
                this.shopList.paginationInfo.pageSize = res.pageSize
                this.shopList.paginationInfo.total = res.totalCount
            })
            this.inventoryTableLoading = false
            this.formData.receiveShopList = this.receiveShopList.map(item => item.enterpriseId)
            this.formData.receiveShopListTitle = this.receiveShopList.map(item => `${item.enterpriseId}[${item.enterpriseName}]`)
        },
        /**
         * 确定发送用户id集合
         */
        onUserList () {
            this.formData.receiveUserList = this.receiveUserList.map(item => item.userId)
            this.formData.receiveUserListTitle = this.receiveUserList.map(item => `${item.userId}[${item.realName}]`)
            this.receiveDialog = false
        },
        //发送消息
        sendMessage () {
            this.$refs.writer.validate(async valid => {
                if (!valid) return
                if (this.formData.receiveUserList != null || this.formData.receiveShopList != null) {
                    let params = {
                        title: this.formData.title,
                        content: this.formData.content,
                        receiveList: this.formData.receiveUserList,
                        enterpriseIdList: this.formData.receiveShopList,
                        files: this.formData.files,
                        sendType: 0,
                        remind: this.formData.remind
                    }
                    showLoading()
                    let res = await sendMessageApi(params)
                    hideLoading()
                    if (res.code !== 200) return this.$message({ message: res.message, type: 'warning' })
                    this.$message({ message: '发送成功', type: 'success' })
                    this.formData = {
                        remind: 0
                    }
                    this.viewList = true
                    showLoading()
                    await this.getSendList()
                    hideLoading()
                } else {
                    this.$message({ message: '至少选择一个用户和商铺', type: 'warning' })
                }
            })
        },

        //获取用户信息 关键字搜索
        async getUserList () {
            let params = {
                page: this.userList.paginationInfo.currentPage,
                limit: this.userList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            if (this.userList.keywords != null) {
                params.keywords = this.userList.keywords
            }
            this.inventoryTableLoading = true
            let res = await getUserList(params)
            this.userList.tableData = res.list || []
            this.userList.paginationInfo.currentPage = res.currPage
            this.userList.paginationInfo.pageSize = res.pageSize
            this.userList.paginationInfo.total = res.totalCount
            this.inventoryTableLoading = false
        },

        //获取商铺信息
        getshopLists () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            this.inventoryTableLoading = true
            getSupplierShopsList(params).then(res => {
                this.shopList.tableData = res.list || []
                this.shopList.paginationInfo.currentPage = res.currPage
                this.shopList.paginationInfo.pageSize = res.pageSize
                this.shopList.paginationInfo.total = res.totalCount
            })
            this.inventoryTableLoading = false
        },
        // 打开收件人弹窗
        importDeviceSelect () {
            this.checkAllUser = false
            this.receiveDialog = true
            this.receiveUserList = []
            this.formData.receiveType = 1
            this.getUserList()
        },
        // 打开供应商弹窗(查看商铺，供应商)
        shopDialog () {
            this.checkAllShop = false
            this.checkAllSupplierShops = false
            this.receiveShopList = []
            this.showDeviceDialog = true
            this.formData.receiveType = 0
            this.getshopLists()
        },
        // addUserList () {
        //     this.showDeviceDialog = false
        // },
        handleSelectionChange (list) {
            this.receiveUserList = list || []
            // this.formData.receiveUserListTitle = list.map(item => {
            //     return item.userId + '[' + item.realName + ']'
            // })
        },
        /**
         * 表格勾选
         */
        handleCurrentChange (val) {
            this.currentRow = val
        },
        //收集表格数据
        handleSelectionChangeShop (list) {
            this.receiveShopList = list || []
            // this. formData.receiveShopList = list.map(item => {
            //     return item.shopId
            // })
            // this.formData.receiveShopListTitle = list.map(item => {
            //     return item.shopId + '[' + item.shopName + ']'
            // })
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
    }
}
</script>
<style lang="scss" scoped>
/deep/ .el-col.editorCol {
    .el-form-item__content {
        height: unset !important;
    }
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

.left-btn1 {
    margin: auto 2px;
}

.buttons {
    padding-right: 20px;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0;
    }
}
</style>
