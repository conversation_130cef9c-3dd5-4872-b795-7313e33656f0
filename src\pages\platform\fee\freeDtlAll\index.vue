<template>
    <div class="base-page">
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <div style="display: flex;align-items: center;">
                                <el-button
                                    type="primary"
                                    style="margin-right: 10px"
                                    @click="outPutExcelM"
                                >导出</el-button>
                                <el-select  style="margin-right: 10px" v-model="freeQueryType" placeholder="请选择缴费记录类型">
                                    <el-option
                                        v-for="item in filterData.recordTypeSelect2"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                                <span v-if="freeQueryType == 3 || freeQueryType == 4"  style="width: 500px">
                                合计：
                                总费用金额（元）：<span style="color: red">{{tableData[0]==null?0:tableData[0].totalFree}}</span>&nbsp;&nbsp;&nbsp;
                                总欠费金额（元）：<span style="color: red">{{tableData[0]==null?0:tableData[0].paymentAmount}}</span>
                            </span>
                            </div>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-dropdown @command="handleChangeSort" trigger="click" placement="bottom">
                        <span class="pointer">
                            排序方式<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="0" :style="{ color: filterData.orderBy == 0 ? '#2e61d7' : '' }">
                                    按生成时间排序
                                </el-dropdown-item>
                                <el-dropdown-item :command="1" :style="{ color: filterData.orderBy == 1 ? '#2e61d7' : '' }">
                                    按修改时间排序
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                        <el-input v-model="keywords" clearable placeholder="输入搜索关键字" style="width: 200px;margin-left: 20px" type="text" @blur="handleInputSearch">
                            <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    v-if="freeQueryType == 1 || freeQueryType == 2"
                    ref="tableRef"
                    v-loading="tableLoading"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                    class="table"
                    @selection-change="tableSelectM"
                    @row-click="tableRowClickM"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"/>
<!--                    <el-table-column label="平台年费编号" prop="platformYearFeeNu" width="200">-->
<!--                        <template v-slot="scope">-->
<!--                            <span class="action" @click="handleView(scope.row)">{{ scope.row.platformYearFeeNu }}</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column label="企业名称" prop="enterpriseName" width="">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.enterpriseName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺名称" prop="shopName" width=""/>
                    <el-table-column label="是否过期" prop="outTime" width="130">
                        <template v-slot="scope">
                            <el-tag type="danger" v-if="scope.row.outTime == 0">是</el-tag>
                            <el-tag type="success" v-if="scope.row.outTime == 1">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="服务到期时间" prop="serveEndTime" width="120"/>
                    <el-table-column label="生成时间" prop="gmtCreate" width="160"/>
                    <el-table-column label="修改时间" prop="gmtModified" width="160"/>
                </el-table>
                <el-table
                    v-else
                    ref="tableRef"
                    v-loading="tableLoading"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                    class="table"
                    @selection-change="tableSelectM"
                    @row-click="tableRowClickM"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="enterpriseName" label="企业名称" width="260">
                        <template v-slot="scope">
                            {{scope.row.enterpriseName}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="relevanceNu" label="关联单据编号" width="240">
                    </el-table-column>
                    <el-table-column prop="" label="关联单据类型" width="">
                        对账单
                    </el-table-column>
                    <el-table-column prop="dealAmount" label="交易金额（元）" width="150">
                        <template v-slot="scope">
                            {{scope.row.dealAmount}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="useExemptFree" label="占用免费交易金额（元）" width="150">
                    </el-table-column>
                    <el-table-column prop="exceedFree" label="需缴费交易金额（元）" width="150">
                    </el-table-column>
                    <el-table-column prop="payType" label="缴费类型" width="100">
                        <template v-slot="scope">
                            <el-tag  v-show="scope.row.payType==1">正常缴费</el-tag>
                            <el-tag  v-show="scope.row.payType==2">免费额度</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="feeRatio" label="收取比例（‰）" width="120">
                    </el-table-column>
                    <el-table-column prop="serveFee" label="服务费用（元）" width="100">
                    </el-table-column>
                    <el-table-column prop="payFee" label="已缴费金额（元）" width="120">
                    </el-table-column>
                    <el-table-column prop="finishPayFee" label="是否欠费" width="120">
                        <template v-slot="scope">
                            <el-tag type="success"  v-show="scope.row.finishPayFee==1">否</el-tag>
                            <el-tag type="danger"  v-show="scope.row.finishPayFee==0">是</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="residuePayFee" label="剩余未缴费金额（元）" width="120">
                    </el-table-column>
                    <el-table-column prop="useBalance" label="缴费使用余额金额（元）" width="120">
                    </el-table-column>
                    <el-table-column label="生成时间" prop="gmtCreate" width="160"/>
                    <el-table-column label="修改时间" prop="gmtModified" width="160"/>
                </el-table>
            </div>
            <Pagination
                v-show="tableData && tableData.length > 0"
                :currentPage.sync="paginationInfo.currentPage"
                :pageSize.sync="paginationInfo.pageSize"
                :total="paginationInfo.total"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="企业名称：">
                            <el-input clearable maxlength="100" placeholder="请输入企业名称" v-model="filterData.enterpriseName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="freeQueryType ==1 || freeQueryType == 2">
                    <el-col :span="12">
                        <el-form-item label="店铺名称：">
                            <el-input clearable maxlength="100" placeholder="请输入店铺名称" v-model="filterData.shopName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="freeQueryType ==3 || freeQueryType == 4">
                    <el-col :span="12">
                        <el-form-item label="是否欠费：">
                            <el-select v-model="filterData.isArrearage" placeholder="请选择是否欠费">
                                <el-option
                                    v-for="item in filterData.isArrearageSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                <el-row v-if="freeQueryType ==3 || freeQueryType == 4">-->
<!--                    <el-col :span="12">-->
<!--                        <el-form-item label="是否超过免费额度：">-->
<!--                            <el-select v-model="filterData.isOutFree" placeholder="请选择是否超过免费额度">-->
<!--                                <el-option-->
<!--                                    v-for="item in filterData.isArrearageSelect"-->
<!--                                    :key="item.value"-->
<!--                                    :label="item.label"-->
<!--                                    :value="item.value">-->
<!--                                </el-option>-->
<!--                            </el-select>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                </el-row>-->
                <el-row v-if="freeQueryType ==1 || freeQueryType == 2">
                    <el-col :span="12">
                        <el-form-item label="服务到期时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd"
                                v-model="filterData.serveEndTime"
                                type="daterange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="freeQueryType ==1 || freeQueryType == 2">
                    <el-col :span="12">
                        <el-form-item label="服务是否过期：">
                            <el-select v-model="filterData.outTime" placeholder="请选择服务是否过期">
                                <el-option
                                    v-for="item in outTimeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="生成时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.gmtCreate"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="修改时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.gmtModified"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { debounce, toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { platformDealFeeRecordDtlListByEntity, platformYearFeeList, platformExportExcelFreeDtl, platformExportExcelYearFree } from '@/api/fee/feeApi'
import { mapState } from 'vuex'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        },
        freeQueryType: {
            handler () {
                this.getTableData()
                this.tableSelectRow = []
            }
        }
    },
    computed: {
        ...mapState([ 'userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    data () {
        return {
            freeQueryType: 1,
            tableSelectRow: [], // 多选框选择的数据
            tableLoading: false, // 加载
            keywords: null, // 关键字
            currentQuery: null,
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            uploadImgSize: 10, // 上传文件大小
            tableData: [], // 表格数据
            outTimeSelect: [
                { value: null, label: '全部' },
                { value: 0, label: '是' },
                { value: 1, label: '否' },
            ],
            filterData: { // 高级搜索
                shopName: null,
                outTime: null,
                enterpriseName: null,
                isArrearage: null,
                isArrearageSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '否' },
                    { value: 1, label: '是' }
                ],
                isOutFree: null,
                recordTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '店铺年度服务费' },
                    { value: 2, label: '电子招标年度服务费' },
                    { value: 3, label: '店铺交易服务费' },
                    { value: 4, label: '合同履约交易服务费' },
                ],
                recordTypeSelect2: [
                    { value: 1, label: '店铺年度服务费' },
                    { value: 2, label: '电子招标年度服务费' },
                    { value: 3, label: '店铺交易服务费' },
                    { value: 4, label: '合同履约交易服务费' },
                ],
                orderBy: 1,
                gmtCreate: [],
                serveEndTime: [],
                gmtModified: [],
                auditOpenTime: [],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            uploadLoading: false,
            fileList: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        outPutExcelM () {
            this.tableLoading = true
            if(this.freeQueryType == 1 || this.freeQueryType == 2) {
                if(this.tableSelectRow.length > 0) {
                    let ids = this.tableSelectRow.map(item => {
                        return item.platformYearFeeId
                    })
                    this.currentQuery.ids = ids
                }
                platformExportExcelYearFree(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                    const url = window.URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = '供应商管理年度费用明细模板.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url)
                    this.currentQuery.ids = []
                    // this.tableSelectRow = []
                    this.$message.success('操作成功')
                }).finally(() => {
                    this.tableLoading = false
                })
            }else {
                if(this.tableSelectRow.length > 0) {
                    let ids = this.tableSelectRow.map(item => {
                        return item.platformDealFeeDtlId
                    })
                    this.currentQuery.ids = ids
                }
                platformExportExcelFreeDtl(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                    const url = window.URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = '供应商管理交易费用明细.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url)
                    this.currentQuery.ids = []
                    // this.tableSelectRow = []
                    this.$message.success('操作成功')
                }).finally(() => {
                    this.tableLoading = false
                })
            }
        },
        handleView (row) {
            this.$router.push({
                path: '/platform/fee/yearDetail',
                name: 'platformYearDetail',
                query: {
                    sn: row.platformYearFeeNu
                }
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 多选框
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        // 行点击
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        //重置数据
        resetSearchConditions () {
            this.filterData.isArrearage = null
            this.filterData.isOutFree = null
            this.filterData.enterpriseName = null
            this.filterData.shopName = null
            this.filterData.outTime = null
            this.filterData.gmtCreate = []
            this.filterData.serveEndTime = []
            this.filterData.gmtModified = []
            this.filterData.auditOpenTime = []
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                freeQueryType: this.freeQueryType,
                isNotAbandon: 1
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.isArrearage != null) {
                params.isArrearage = this.filterData.isArrearage
            }
            if (this.filterData.isOutFree != null) {
                params.isOutFree = this.filterData.isOutFree
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            if (this.filterData.outTime != null) {
                params.outTime = this.filterData.outTime
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.gmtCreate != null) {
                params.startGmtCreate = this.filterData.gmtCreate[0]
                params.endGmtCreate = this.filterData.gmtCreate[1]
            }
            if (this.filterData.serveEndTime != null) {
                params.startServeEndTime = this.filterData.serveEndTime[0]
                params.endServeEndTime = this.filterData.serveEndTime[1]
            }
            if (this.filterData.gmtModified != null) {
                params.startGmtModified = this.filterData.gmtModified[0]
                params.endGmtModified = this.filterData.gmtModified[1]
            }
            if (this.filterData.auditOpenTime != null) {
                params.startAuditTime = this.filterData.auditOpenTime[0]
                params.endAuditTime = this.filterData.auditOpenTime[1]
            }
            this.tableLoading = true
            this.currentQuery = params
            if(this.freeQueryType == 1 || this.freeQueryType == 2) {
                platformYearFeeList(params).then(res => {
                    this.paginationInfo.total = res.totalCount
                    this.paginationInfo.pageSize = res.pageSize
                    this.paginationInfo.currentPage = res.currPage
                    this.tableData = res.list
                }).finally(() => {
                    this.tableLoading = false
                })
            }else {
                platformDealFeeRecordDtlListByEntity(params).then(res => {
                    this.paginationInfo.total = res.totalCount
                    this.paginationInfo.pageSize = res.pageSize
                    this.paginationInfo.currentPage = res.currPage
                    this.tableData = res.list
                }).finally(() => {
                    this.tableLoading = false
                })
            }

        },
        handleChangeSort (value) {
            this.filterData.orderBy = value
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
    appearance: textfield !important;
    -moz-appearance: textfield !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
/deep/ .addDia {
    .el-dialog__body {
        height: 600px;
    }
}

/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>
