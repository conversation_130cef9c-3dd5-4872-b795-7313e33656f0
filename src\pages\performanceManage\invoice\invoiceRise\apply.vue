<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div v-loading='formLoading' class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs v-model="tabsName" tab-position="left" @tab-click="onChangeTab">
                <el-tab-pane :disabled="clickTabFlag" label="发票抬头" name="baseInfo">
                </el-tab-pane>
                <div id="tabs-content">
                    <div id="baseInfCon" class="con">
                        <div id="baseInfo" class="tabs-title">发票抬头</div>
                        <!--新增-->
                        <div class="form">
                            <el-form
                                ref="formData" :model="formData" :rules="rules"
                                class="demo-ruleForm" label-width="200px"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发票类型：" prop="invoiceType">
                                            <el-select
                                                v-model="formData.invoiceType" placeholder="请选择发票类型"
                                                @change="changRiseType(formData.invoiceType)"
                                            >
                                                <el-option
                                                    v-for="item in typeOptions" :value="item.value"
                                                    :label="item.label" :key="item.value"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="12">
                                        <el-form-item label="抬头类型：" prop="riseType">
                                            <el-select
                                                disabled v-model="formData.riseType" placeholder="请选择抬头类型"
                                            >
                                                <el-option
                                                    v-for="item in headerOptions" :value="item.value"
                                                    :label="item.label" :key="item.value"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="单位名称：" prop="company"
                                        >
                                            <el-input
                                                v-model="formData.company" placeholder="请输入单位名称"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="单位税号：" prop="dutyParagraph"
                                        >
                                            <el-input
                                                v-model="formData.dutyParagraph" placeholder="请输入单位税号"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册地址：" prop="province">
                                            <el-cascader
                                                style="width: 300px"
                                                size="large"
                                                :options="addressData"
                                                v-model="selectAddressOptions"
                                                @change="handleAddressChange"
                                            >
                                            </el-cascader>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="详细地址" prop="registerAddress"
                                            :rules="formData.invoiceType==0? rules.registerAddress:[{
                                  required:false,
                                  message:'请输入详细地址'
                                }]"
                                        >
                                            <el-input
                                                placeholder="请输入详细地址" clearable
                                                v-model="formData.registerAddress"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="注册电话" prop="registerPhone"
                                            :rules="formData.invoiceType==0? rules.registerPhone:[{
                                  required:false,
                                  message:'请输入注册电话'
                                }]"
                                        >
                                            <el-input
                                                placeholder="请输入注册电话" clearable v-model="formData.registerPhone"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="邮箱" prop="email">
                                            <el-input
                                                placeholder="请输入邮箱" clearable v-model="formData.email"
                                            ></el-input>
                                        </el-form-item>

                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="开户银行：" prop="bank" :rules="formData.invoiceType==0? rules.bank:[{
                                  required:false,
                                  message:'请输入开户银行'
                                }]"
                                        >
                                            <el-input
                                                v-model="formData.bank" placeholder="请输入开户银行"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item
                                            label="银行账号：" prop="bankAccount"
                                            :rules="formData.invoiceType==0? rules.bankAccount:[{
                                  required:false,
                                  message:'请输入银行账号'
                                }]"
                                        >
                                            <el-input
                                                v-model="formData.bankAccount" placeholder="请输入银行账号"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <div class="col">
                                            <el-form-item label="收票人姓名：" prop="userName">
                                                <el-input
                                                    v-model="formData.userName" placeholder="请输入收票人姓名"
                                                ></el-input>
                                            </el-form-item>
                                        </div>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收票人手机号：" prop="userPhone">
                                            <el-input
                                                v-model="formData.userPhone" placeholder="请输入收票人手机号"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="收票人地址：" prop="userProvince">
                                            <el-cascader
                                                style="width: 300px"
                                                size="large"
                                                :options="addressData"
                                                v-model="selectAddressUserOptions"
                                                @change="handleAddressUserChange"
                                            >
                                            </el-cascader>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="详细地址" prop="userAddress">
                                            <el-input
                                                placeholder="请输入详细地址" clearable v-model="formData.userAddress"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                <el-col :span="12">
                                    <el-form-item label="是否默认：" prop="state">

                                        <template>
                                        <el-radio-group v-model="formData.state">
                                            <el-radio  :label='1'>是</el-radio>
                                            <el-radio  :label='0'>否</el-radio>
                                        </el-radio-group>
                                        </template>
                                    </el-form-item>
                                </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="抬头备注：" prop="productDescribe">
                                            <el-input type="textarea" v-model="formData.remarks"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-tabs>
            <div class="buttons">
                <el-button type="success" @click="submit">保存</el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import { create, findById } from '@/api/performance/invoiceRecord'
import '@/utils/jquery.scrollTo.min'

import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'

export default {
    data () {
        return {
            uploadMax: 10,
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
            },
            addressData: regionData, // 地址数据
            selectAddressOptions: [], // 地址选择
            // 数据加载
            formLoading: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            rowData: null, // 跳转过来的数据
            showForm: false,
            rules: {
                invoiceType: { required: true, message: '请选择发票类型', trigger: 'blur' },
                riseType: { required: true, message: '请选择抬头类型', trigger: 'blur' },
                registerPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                userPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                company: { required: true, message: '请选择单位名称', trigger: 'blur' },
                dutyParagraph: { required: true, message: '请选择单位税号', trigger: 'blur' },
                registerAddress: { validator: true, message: '请选择注册地址', trigger: 'blur' },
                bank: { required: true, message: '请选择开户银行', trigger: 'blur' },
                bankAccount: { required: true, message: '请选择银行账号', trigger: 'blur' },
            },
            selectAddressUserOptions: [], // 地址选择
            //表单数据
            formData: {
                invoiceRecordId: '',
                state: 0,
                invoiceType: 0,
                riseType: 1,
                company: '',
                dutyParagraph: '',
                registerAddress: '',
                registerPhone: '',
                bank: '',
                bankAccount: '',
                userName: '',
                userPhone: '',
                userAddress: '',
                email: '',
                province: '',
                city: '',
                county: '',
                userProvince: '',
                userCity: '',
                userCounty: '',
                district: '',

            },
            typeOptions: [
                { label: '增值税专用发票', value: 0 },
                { label: '增值税普通发票', value: 1 },
            ],
            headerOptions: [
                { label: '单位', value: 1 },
                { label: '个人', value: 2 },
            ],
            addressOptions: {
                province: [],
                city: [],
                district: []
            },

            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    components: {

    },
    created () {
        this.rowData = this.$route.params.viewType
        if (this.rowData == 'add') {
            this.viewType = 'add'
            this.formData.state = this.$route.params.state
        } else {
            this.formData.invoiceRecordId = this.$route.params.invoiceRecordId
            this.getByIdM()
        }

    },
    mounted () {
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        getByIdM () {
            findById({ id: this.formData.invoiceRecordId }).then(res=>{
                if (res.province != null)
                    this.addressFormatShow(res)
                if (res.userProvince != null) {
                    this.addUserRessFormatShow(res)
                }
                this.formData = res
            })
        },
        // 地址回显
        addressFormatShow (row) {
            if (row.province == null || row.province == '') {
                return
            }
            if (row.city == null || row.city == '') {
                return
            }
            if (row.county == null || row.county == '') {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 地址回显
        addUserRessFormatShow (row) {
            if (row.userProvince == null || row.userProvince == '') {
                return
            }
            if (row.userCity == null || row.userCity == '') {
                return
            }
            if (row.userCounty == null || row.UserCounty == '') {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.userProvince][row.userCity][row.userCounty].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressUserOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        handleAddressUserChange () {
            let addArr = this.selectAddressUserOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.formData.userProvince = province
            this.formData.userCity = city
            this.formData.userCounty = county
            this.formData.userAddress = province + city + county
        },
        changRiseType (state) {
            if (state == 0) {
                this.formData.riseType = 1
            }
        },

        // 地区
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.formData.province = province
            this.formData.city = city
            this.formData.county = county
            this.formData.registerAddress = province + city + county
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        submit () {
            this.$refs['formData'].validate(valid => {
                if (valid) {
                    create(this.formData).then(res=>{
                        if (res.code == 200) {
                            this.$message.success('保存成功')
                            this.$router.go(-1)
                        }
                    })
                }
            }
            )
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

.e-table {
    min-height: auto;
    background: #fff;
}

#tabs-content {
    padding-bottom: 70px !important;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
    display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 300px;
        margin-top: 0px;
    }
}

</style>
