<template>
    <div class="root">
        <div class="tabBox mt20 dfa">
            <div class="df center" style="width: 1326px">
                <div
                    @click="changTenderFrom(0) "
                    :style="{ background: tenderForm === 0 ? '#3083E2' : '', }"
                >
                    公开招标
                </div>
                <div
                    @click="changTenderFrom(1)"
                    :style="{ background: tenderForm === 1 ? '#3083E2' : '', }"
                >
                    邀请招标
                </div>
                <div
                    @click="changTenderFrom(2)"
                    :style="{ background: tenderForm === 2 ? '#3083E2' : '', }"
                >
                    询价
                </div>
                <div
                    @click="changTenderFrom(3)"
                    :style="{ background: tenderForm === 3 ? '#3083E2' : '', }"
                >
                    竞争性谈判
                </div>
                <div @click="changTenderFrom(4)"
                     :style="{ background: tenderForm === 4? '#3083E2' : '', }"
                >
                    单一性来源
                </div>
            </div>

            <!--                <div @click="changTenderFrom(6)"
                                 :style="{ color: tenderForm === 6 ? '#fff' : '', background: tenderForm === 6? 'rgba(33, 110, 198, 1)' : '', }"
                            >
                                响应
                            </div>-->
            <!--              <div @click="changTenderFrom(5)"-->
            <!--                    :style="{ color: tenderForm === 5 ? '#fff' : '', background: tenderForm === 5? 'rgba(33, 110, 198, 1)' : '', }"-->
            <!--                >-->
            <!--                    招标公告-->
            <!--                </div>-->
            <!--                <div-->
            <!--                  @click="getTenderNotice()"-->
            <!--                  :style="{-->
            <!--            color: tenderForm === 5 ? '#fff' : '',-->
            <!--            background: tenderForm === 5? 'rgba(33, 110, 198, 1)' : '',-->
            <!--          }"-->
            <!--              >-->
            <!--                中标公示-->
            <!--              </div>-->
        </div>
        <div class="main">
            <div class="numBox mt20">
                <div class="dateBox dfa">
                    <span>时间:&nbsp;</span>
                    <el-date-picker
                        value-format="yyyy-MM-dd"
                        v-model="dateValue"
                        type="daterange"
                        @change="getResult"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
                <div class="list dfb">
                    <div class="item mt20 dfb">
                        <div class="item_left">
                            <span>招标项目（个）</span>
                            <div>{{ tenderNum.biddingProject }}</div>
                        </div>
                        <img src="@/assets/images/img/z1.png" alt=""/>
                    </div>
                    <div class="item mt20 dfb">
                        <div class="item_left">
                            <span>中标金额（亿元）</span>
                            <div>{{ tenderNum.money }}</div>
                        </div>
                        <img src="@/assets/images/img/z2.png" alt=""/>
                    </div>
                    <div class="item mt20 dfb">
                        <div class="item_left">
                            <span>已完成（个）</span>
                            <div>{{ tenderNum.completionProject }}</div>
                        </div>
                        <img src="@/assets/images/img/z3.png" alt=""/>
                    </div>
                    <div class="item mt20 dfb">
                        <div class="item_left">
                            <span>进行中（个）</span>
                            <div>{{ tenderNum.proceedProject }}</div>
                        </div>
                        <img src="@/assets/images/img/z4.png" alt=""/>
                    </div>
                </div>
            </div>
            <div class="searchBox mt20">
                <div class="searchBox_top dfb">
                    <div class="dfa">
                        <div class="title">筛选条件：</div>
                        <div class="checkItem" v-for="(item, i) in checkList" :key="i">
                            {{ item }} <i class="el-icon-close" @click="deleteItem(i)"></i>
                        </div>
                        <span v-if="checkList.length > 0" @click="empty"
                        >清空已选条件</span
                        >
                    </div>
                </div>
                <div class="row dfb">
                    <div class="row_left dfa">
                        <div class="title">招标类型</div>
                        <div
                            class="item"
                            @click="click1(i)"
                            :style="{ color: filterField.tenderType === i ? 'rgba(34, 111, 199, 1)' : '' }"
                            v-for="(item, i) in areaList"
                            :key="i"
                        >
                            {{ item }}
                        </div>
                    </div>
                </div>
                <div class="row dfb">
                    <div class="row_left dfa">
                        <div class="title">招标状态</div>
                        <div
                            class="item"
                            @click="click2(i)"
                            :style="{ color: filterField.tenderState === i ? 'rgba(34, 111, 199, 1)' : '' }"
                            v-for="(item, i) in pm"
                            :key="i"
                        >
                            {{ item }}
                        </div>
                    </div>
                </div>
                <div class="row dfb">
                    <div class="row_left dfa">
                        <div class="title">选择时间</div>
                        <div
                            class="item"
                            @click="click3(i)"
                            :style="{ color: filterField.selectTime === i ? 'rgba(34, 111, 199, 1)' : '' }"
                            v-for="(item, i) in cd"
                            :key="i"
                        >
                            {{ item }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="listBox mt20">
                <div class="header dfa">
                    <div class="row dfb">
                        <div class="row_left dfa">
                            <div class="item2 pointer" @click="changNoby"
                                 :style="{ color: noby!=null ? 'rgba(34, 111, 199, 1)' : '', } ">综合排序
                            </div>
                            <div class="item2 pointer"
                                 @click="changReleaseDate"
                                 :style="{ color: releaseDateState!=null ? 'rgba(34, 111, 199, 1)' : '', }">
                                发布时间
                                <i v-if="releaseDateState==2" class="el-icon-bottom"></i>
                                <i v-else class="el-icon-top"></i>
                            </div>
                            <div class="item2 pointer"
                                 @click="changeTenderAmount"
                                 :style="{ color: tenderAmountState!= null ? 'rgba(34, 111, 199, 1)' : '', }">
                                预算金额
                                <i v-if="tenderAmountState==2" class="el-icon-bottom"></i>
                                <i v-else class="el-icon-top"></i>
                            </div>
                            <div class="item2 dfa">
                                <el-input
                                    style="width: 320px; margin-left: 20px; border-radius: 0"
                                    placeholder="请输入内容"
                                    prefix-icon="el-icon-search"
                                    v-model="keyword"
                                >
                                </el-input>
                                <el-button class="search" @click="getTenderPage">搜索 </el-button>
                            </div>
                        </div>
                    </div>
                    <!--<span class="notice_link" @click="$router.push('/mFront/biddingNotice')">招标公告</span>-->
                </div>
                <!-- ====================== -->
                <div class="bidList dfb">
                    <div class="bidItem" v-for="(item, i) in bidList" :key="i">
                        <div @click="goDetail(item)">
                            <div class="title dfa pointer"> {{ item.tenderName }}</div>
                            <div class="content dfa">
                                <div class="content_left">
                                    <div v-show="item.tenderForm !== '2'" class="priceTitle">采购金额</div>
                                    <div v-show="item.tenderForm === '2'" class="priceTitle">采购数量</div>
                                    <div v-show="item.tenderForm === '2'" class="price">{{ item.num }}</div>
                                    <div v-show="item.tenderForm !== '2'" class="price">{{ item.tenderAmount }}</div>
                                    <div class="js">倒计时：{{ item.js }}</div>
                                    <div class="status" style="background: rgba(42, 130, 228, 1)">
                                        {{ tenderStates[item.tenderState] }}
                                    </div>
                                </div>
                                <div class="content_right">
                                    <div class="item dfa">
                                        <img src="../../../assets/images/img/z5.png" alt=""/>
                                        <span>采购方式：</span>
                                        <div>{{ ['公开招标', '邀请招标', '询价', '竞争性谈判', '单一性来源'][parseInt(item.tenderForm)] }}</div>
                                    </div>
                                    <div class="item dfa">
                                        <img src="../../../assets/images/img/z6.png" alt=""/>
                                        <span>采购人：</span>
                                        <div>{{ item.tenderUser }}</div>
                                    </div>
                                    <div class="item dfa">
                                        <img src="../../../assets/images/img/z7.png" alt=""/>
                                        <span>发布时间：</span>
                                        <div>{{ item.releaseDate }}</div>
                                    </div>
                                    <div class="item dfa" style="margin: 0">
                                        <img src="../../../assets/images/img/z7.png" alt=""/>
                                        <span>截止时间：</span>
                                        <div>{{ item.tenderEndTime }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="paginationBox">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pages.currPage"
                    :page-sizes="[4, 10, 20, 30, 40]"
                    :page-size="4"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pages.totalCount"
                >
                </el-pagination>
            </div>
        </div>
        <Publicity/>
    </div>
</template>
<script>
import {  getTenderOnloginPages, getTenderNum } from '@/api/frontStage/bidding'
import { countdown } from '@/utils/common'
import { mapState } from 'vuex'
import Publicity from '../components/publicity.vue'

export default {
    components: { Publicity },
    data () {
        return {
            dateState: false,
            amountState: false,
            releaseDateState: null,
            noby: false,
            tenderAmountState: null,
            tenderNum: {
                biddingProject: 0,
                money: 0.0,
                CompletionProject: 0,
                proceedProject: 0
            },
            tenderForm: 0,
            keyword: null,
            value: '',
            checkList: [],
            tenderStates: ['新增', '审核中', '已审核', '已发布', '补遗中', '评标登记中', '已评标', '已公示', '关闭中', '已完成'],
            areaList: ['全部', '物资供应', '机材'],
            pm: ['全部', '未发布', '发布', '已评标', '已结束'],
            cd: ['不限', '近一周', '近一个月', '近三个月', '近半年', '近一年'],
            dateValue: [], // 开始时间和结束时间
            filterField: {
                tenderType: 0,
                tenderState: 0,
                selectTime: 2,
                orderBy: 0,
                releaseDate: 0,
                tenderAmount: 0
            },
            tenderOptions: [
                {
                    value: null,
                    label: '全部',
                },
                {
                    value: '1',
                    label: '公开招标',
                }, {
                    value: '2',
                    label: '询价',
                },
                {
                    value: '3',
                    label: '竞争性谈判',
                }, {
                    value: '4',
                    label: '单一来源',
                },
                {
                    value: '5',
                    label: '招标公示',
                },
            ],
            bidList: [],
            pages: {
                pageSize: 4,
                totalCount: 0,
                currPage: 1,
                destination: 1,
                totalPages: 0,
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
    },
    async created () {
        if (this.$route.query.tenderForm != null) {
            this.tenderForm = this.$route.query.tenderForm
        }
        await this.getTenderPage()
        setInterval(() => {
            this.bidList = this.bidList.map(item => {
                if (new Date(this.bidList[0].tenderEndTime) < new Date()) {
                    item.js = '已关闭'
                } else {
                    item.js = countdown(item.tenderEndTime)
                }

                return item
            })
        }, 1000)
        this.getResult()
    },
    methods: {
        changNoby () {
            this.noby = !this.noby
            this.releaseDateState = null
            this.tendeAmountStat = null
            this.getTenderPage()
        },
        changReleaseDate () {
            this.dateState = !this.dateState
            this.noby = null
            this.amountState = null
            if (this.dateState) {
                this.releaseDateState = 1
            } else {
                this.releaseDateState = 2
            }
            this.getTenderPage()
        },
        changeTenderAmount () {
            this.amountState = !this.amountState
            this.noby = null
            this.dateState = null
            this.tenderAmountState = this.amountState ? 1 : 2
            this.getTenderPage()
        },
        getResult () {
            let params = {
                mallConfig: 0
            }
            if (this.dateValue != null) {
                params.startDate = this.dateValue[0]
                params.endDate = this.dateValue[1]
            } else {
                let dateObj = this.getLastYear()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            getTenderNum(params).then(res => {
                res.money = (res.money / 10000000).toFixed(4)
                this.tenderNum = res
            })
        },

        goDetail (item) {
            let destination = ['biddingDetail', 'inviteDetail', 'enquiryDetail', 'competedetail', 'oneTender']
            this.$router.push({
                path: `/mFront/${destination[parseInt(item.tenderForm)]}`,
                name: destination[parseInt(item.tenderForm)],
                params: {
                    row: item
                }
            })
        },
        //采购方式选择
        changTenderFrom (i) {
            this.tenderForm = i
            if (i === 5) {
                return this.$router.push('/mFront/biddingNotice')
            }
            this.getTenderPage()
        },
        //获取招标数据
        async getTenderPage () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                tenderForm: this.tenderForm, //采购方式
                mallConfig: 0
            }
            let dateObj
            if (this.filterField.selectTime == 1) {
                dateObj = this.getLastWeek()
            } else if (this.filterField.selectTime == 2) {
                dateObj = this.getLastMonth()
            } else if (this.filterField.selectTime == 3) {
                dateObj = this.getLast3Month()
            } else if (this.filterField.selectTime == 4) {
                dateObj = this.getLast6Month()
            } else if (this.filterField.selectTime == 5) {
                dateObj = this.getLastYear()
            }
            params.startDate = dateObj.last + ' 00:00:00'
            params.endDate = dateObj.now + ' 23:59:59'
            if (this.releaseDateState != null) {
                params.releaseDate = this.releaseDateState
            }
            if (this.tenderAmountState != null) {
                params.tendeAmount = this.tenderAmountState
            }
            if (this.keyword != null) {
                params.keyword = this.keyword
            }
            if (this.filterField.tenderState == 1) {
                params.tenderState = 3
            } else if (this.filterField.tenderState == 2) {
                params.tenderState = 5
            } else if (this.filterField.tenderState == 3) {
                params.tenderState = 6
            } else if (this.filterField.tenderState == 4) {
                params.tenderState = 9
            }

            if (this.filterField.tenderType == 1) {
                params.tenderType = 4
            } else if (this.filterField.tenderType == 2) {
                params.tenderType = 5
            }
            if (!(localStorage.getItem('token')) != null) {
                let res = await getTenderOnloginPages(params)
                this.bidList = res.list
                this.bidList.forEach(item => item.js = null)
                this.pages = res
            } else {
                this.$message({
                    message: '未登录，无法查看信息',
                    type: 'warning'
                })
            }
            // if (this.tenderForm === 0) {
            //     let res = await getTenderLogOutPages(params)
            //     this.bidList = res.list || []
            //     this.bidList.forEach(item => item.js = null)
            //     this.pages = res
            // } else {
            //     if (!(localStorage.getItem('token')) != null) {
            //         let res = await getTenderOnloginPages(params)
            //         this.bidList = res.list
            //         this.bidList.forEach(item => item.js = null)
            //         this.pages = res
            //     } else {
            //         this.$message({
            //             message: '未登录，无法查看信息',
            //             type: 'warning'
            //         })
            //     }

            // }
            /* if (!(localStorage.getItem('token'))) {
                if (this.tenderForm === 0) {
                    await getTenderLogOutPages(params).then(res => {
                        this.bidList = res.list
                        this.bidList.forEach(item => {
                            item.js = null
                        })
                        this.pages = res
                    })
                } else {
                    this.$message({
                        message: '未登录，无法查看信息',
                        type: 'warning'
                    })
                }

            } else {
                getTenderOnloginPages(params).then(res => {
                    this.bidList = res.list
                    this.bidList.forEach(item => {
                        item.js = null
                    })
                    this.pages = res
                })
            }

            if (!(localStorage.getItem('token'))) {
                console.log(params)
                getTenderLogOutPages().then(res=>{
                    console.log(res)
                })
            }else {
                console.log(params)
                getTenderOnloginPages().then(res=>{
                    console.log(res)
                })
            } */

        },
        handleSizeChange (val) {
            this.pages.pageSize = val
            this.getTenderPage()
        },
        handleCurrentChange (val) {
            this.pages.currPage = val
            this.getTenderPage()
        },
        empty () {
            this.filterField.tenderState = 0
            this.filterField.tenderType = 0
            this.filterField.selectTime = 0
            this.checkList = []
            this.getTenderPage()
        },
        deleteItem (i) {
            this.checkList.splice(i, 1)
        },
        getCheckList () {
            let arr = []
            if (this.filterField.tenderType > 0) {
                arr.push(this.areaList[this.filterField.tenderType])
            }
            if (this.filterField.tenderState > 0) {
                arr.push(this.pm[this.filterField.tenderState])
            }
            if (this.filterField.selectTime > 0) {
                arr.push(this.cd[this.filterField.selectTime])
            }
            this.checkList = arr
        },
        click1 (i) {
            this.filterField.tenderType = i
            this.getCheckList()
            this.getTenderPage()
        },
        click2 (i) {
            this.filterField.tenderState = i
            this.getCheckList()
            this.getTenderPage()
        },
        click3 (i) {
            this.filterField.selectTime = i
            this.getCheckList()
            this.getTenderPage()
        },
        getLastWeek () {
            let myDate = new Date()
            let nowDate = new Date()
            myDate.setDate(myDate.getDate() - 7)
            let dateObj = {}
            dateObj.now = nowDate.getFullYear() + '-' + (nowDate.getMonth() + 1) + '-' + nowDate.getDate()
            dateObj.last = myDate.getFullYear() + '-' + (myDate.getMonth() + 1) + '-' + myDate.getDate()
            return dateObj
        },
        getLastMonth () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if (month - 1 <= 0) { //如果是1月，年数往前推一年<br>
                dateObj.last = (year - 1) + '-' + 12 + '-' + day
            } else {
                let lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate()
                if (lastMonthDay < day) {    // 1个月前所在月的总天数小于现在的天日期
                    if (day < nowMonthDay) {        //当前天日期小于当前月总天数
                        dateObj.last = year + '-' + (month - 1) + '-' + (lastMonthDay - (nowMonthDay - day))
                    } else {
                        dateObj.last = year + '-' + (month - 1) + '-' + lastMonthDay
                    }
                } else {
                    dateObj.last = year + '-' + (month - 1) + '-' + day
                }
            }
            return dateObj
        },
        getLast3Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if (month - 3 <= 0) { // 如果是1、2、3月，年数往前推一年
                let last3MonthDay1 = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate()    // 3个月前所在月的总天数
                if (last3MonthDay1 < day) {    // 3个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + last3MonthDay1
                } else {
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + day
                }
            } else {
                let last3MonthDay2 = new Date(year, (parseInt(month) - 3), 0).getDate()    //3个月前所在月的总天数
                if (last3MonthDay2 < day) {    //3个月前所在月的总天数小于现在的天日期
                    if (day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 3) + '-' + (last3MonthDay2 - (nowMonthDay - day))
                    } else {
                        dateObj.last = year + '-' + (month - 3) + '-' + last3MonthDay2
                    }
                } else {
                    dateObj.last = year + '-' + (month - 3) + '-' + day
                }
            }
            return dateObj
        },
        getLast6Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if (month - 6 <= 0) { // 年数往前推一年
                let last6MonthDay1 = new Date((year - 1), (12 - (6 - parseInt(month))), 0).getDate()    // 6个月前所在月的总天数
                if (last6MonthDay1 < day) {    // 6个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + last6MonthDay1
                } else {
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + day
                }
            } else {
                let last6MonthDay2 = new Date(year, (parseInt(month) - 6), 0).getDate()    //6个月前所在月的总天数
                if (last6MonthDay2 < day) {    //6个月前所在月的总天数小于现在的天日期
                    if (day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 6) + '-' + (last6MonthDay2 - (nowMonthDay - day))
                    } else {
                        dateObj.last = year + '-' + (month - 6) + '-' + last6MonthDay2
                    }
                } else {
                    dateObj.last = year + '-' + (month - 6) + '-' + day
                }
            }
            return dateObj
        },
        getLastYear () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            dateObj.last = (year - 1) + '-' + month + '-' + day
            return dateObj
        },
    },
}
</script>
<style scoped lang="scss">
div {
    line-height: 1;
    box-sizing: border-box;
}

/deep/ .el-input__inner {
    border-radius: 0;
}

.root {
    width: 100%;
    min-height: 100vh;
    background: #f5f5f5;

    .tabBox {
        width: 100%;
        height: 60px;
        font-size: 18px;
        font-weight: 400;
        color: #fff;
        background-color: #216EC6;

        div:not(.df) {
            width: 160px;
            line-height: 60px;
            text-align: center;
            cursor: pointer;
            user-select: none;
        }
    }

    .main {
        width: 1326px;
        margin: 0 auto;

        .numBox {
            width: 100%;
            height: 204px;
            opacity: 1;
            background: rgba(255, 255, 255, 1);
            padding: 16px 20px;

            .list {
                width: 1250px;
                margin: 0 auto;

                .item {
                    width: 290px;
                    height: 120px;
                    background: rgba(248, 251, 255, 1);
                    padding-left: 30px;
                    padding-right: 20px;

                    .item_left {
                        span {
                            font-size: 16px;
                            font-weight: 400;
                            color: rgba(0, 0, 0, 1);
                        }

                        div {
                            font-size: 24px;
                            font-weight: 500;
                            color: rgba(0, 0, 0, 1);
                            margin-top: 20px;
                        }
                    }
                }
            }
        }

        .searchBox {
            width: 100%;
            //height: 147px;
            //   border: 1px solid rgba(229, 229, 229, 1);
            background: #fff;

            .searchBox_top {
                height: 48px;

                .title {
                    width: 108px;
                    text-align: center;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                }

                span {
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                    cursor: pointer;
                }

                .checkItem {
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(51, 51, 51, 1);
                    padding: 6px 12px;
                    background: rgba(250, 250, 250, 1);
                    //   border: 1px solid rgba(229, 229, 229, 1);
                    margin-right: 10px;

                    i {
                        cursor: pointer;
                    }
                }

                .searchBox_top_right {
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                    padding-right: 20px;
                }
            }

            .row {
                height: 47px;
                border-top: 1px solid rgba(229, 229, 229, 1);

                .title {
                    width: 108px;
                    height: 48px;
                    opacity: 1;
                    background: rgba(250, 250, 250, 1);
                    text-align: center;
                    line-height: 48px;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(51, 51, 51, 1);
                    border-right: 1px solid rgba(229, 229, 229, 1);
                    border-top: 1px solid rgba(229, 229, 229, 1);
                }

                .item {
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(51, 51, 51, 1);
                    margin: 0 18px;
                    cursor: pointer;
                }

                .row_right {
                    width: 60px;
                    height: 28px;
                    opacity: 1;
                    background: rgba(255, 255, 255, 1);
                    border: 1px solid rgba(230, 230, 230, 1);
                    margin-right: 20px;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                }
            }
        }

        .listBox {
            width: 100%;
            background: #fff;
            position: relative;

            .header {
                height: 70px;
                border-bottom: 1px solid rgba(229, 229, 229, 1);
            }

            .notice_link {
                position: absolute;
                right: 30px;
                color: #216ec6;
                cursor: pointer;
            }

            .item2 {
                margin-left: 20px;
            }

            .search {
                height: 32px;
                line-height: 32px;
                //opacity: 1;
                border-radius: 0;
                border: 0;
                background: rgba(212, 48, 48, 1);
                color: #fff;
            }

            .bidList {
                width: 100%;
                min-height: 600px;
                padding: 20px;
                flex-wrap: wrap;

                .bidItem {
                    width: 633px;
                    height: 250px;
                    opacity: 1;
                    background: rgba(255, 255, 255, 1);
                    border: 1px solid rgba(229, 229, 229, 1);
                    margin-bottom: 20px;

                    .title {
                        height: 52px;
                        background: rgba(248, 251, 255, 1);
                        border-bottom: 1px solid rgba(229, 229, 229, 1);
                        padding-left: 20px;
                        font-size: 16px;
                        font-weight: 400;
                        color: rgba(51, 51, 51, 1);
                    }

                    .content {
                        height: 198px;

                        .content_left {
                            height: 158px;
                            padding-left: 20px;
                            padding-right: 60px;
                            border-right: 1px dashed rgba(204, 204, 204, 1);

                            .priceTitle {
                                font-size: 14px;
                                font-weight: 400;
                                color: rgba(51, 51, 51, 1);
                            }

                            .price {
                                font-size: 24px;
                                font-weight: 500;
                                color: rgba(255, 87, 51, 1);
                                margin-top: 15px;
                            }

                            .js {
                                font-size: 12px;
                                font-weight: 400;
                                color: rgba(102, 102, 102, 1);
                                margin-top: 30px;
                            }

                            .status {
                                width: 142px;
                                height: 40px;
                                opacity: 1;
                                background: rgba(67, 207, 124, 1);
                                font-size: 16px;
                                font-weight: 400;
                                color: rgba(255, 255, 255, 1);
                                text-align: center;
                                line-height: 40px;
                                margin-top: 15px;
                            }
                        }

                        .content_right {
                            padding-left: 40px;

                            .item {
                                margin-bottom: 24px;

                                span {
                                    display: block;
                                    margin-left: 10px;
                                    font-size: 14px;
                                    font-weight: 400;
                                    color: rgba(102, 102, 102, 1);
                                }

                                div {
                                    font-size: 14px;
                                    font-weight: 400;
                                    color: #000;
                                }
                            }
                        }
                    }
                }
            }
        }

        .paginationBox {
            display: flex;
            justify-content: flex-end;
            margin: 30px 0;
        }
    }

    .footer {
        width: 100%;
        background: #fff;
        height: 80px;

        .content {
            width: 1326px;
            margin: 0 auto;
            height: 100%;

            div {
                width: 25%;

                img {
                    width: 166px;
                    height: 26px;
                }
            }
        }
    }
}
</style>
