<!-- eslint-disable no-empty -->
<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <div>
            <div class="left">
              <div class="left-btn">
                  <el-button type="primary" @click="operate_td('新增')" class="btn-greenYellow">新增</el-button>
              </div>
            </div>
          </div>
          <!-- 新增按钮 -->
          <div class="search_box">
            <!-- <el-input type="text" @keyup.enter.native="onSearch" @blur="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png" slot="suffix" @click="getTableData" /></el-input> -->
            <el-input type="text" @keyup.enter.native="onSearch" @blur="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png" slot="suffix" @click="getTableData" /></el-input>
          </div>
        </div>
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table elTtreeTable" :style="{ width: '100%' }">
         <el-table :data="tableData" :height="rightTableHeight"  style="width: 100%;margin-bottom: 20px;" row-key="id"
            border :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <el-table-column label="操作" width="300">
                <template slot-scope="scope">
                    <div class="actionDiv">
                      <span class="action" v-if="scope.row.menuType != '3'" @click="operate_td('新增下级', scope.row)">新增下级</span>
                      <span class="action" v-if="!scope.row.menuSystem" @click="operate_td('编辑', scope.row)">编辑</span>
                      <span class="action" v-if="!scope.row.menuSystem" @click="operate_td('删除', scope.row)">删除</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="菜单名称" prop="menuName"/>
            <el-table-column label="排序" prop="sort" width="200" sortable/>
            <el-table-column label="菜单类型" prop="menuType" width="200">
                <template slot-scope="scope">
                    <span v-if="scope.row.menuType == '1'">目录</span>
                    <span v-else-if="scope.row.menuType == '2'">菜单</span>
                    <span v-else-if="scope.row.menuType == '3'">按钮</span>
                </template>
            </el-table-column>
            <el-table-column label="链接地址" prop="menuPath"/>
            <el-table-column label="权限标记" prop="perms"/>
        </el-table>
      </div>
    </div>
    <el-dialog v-dialogDrag :title="menuModal.title" :visible.sync="menuModal.visible" width="40%" :close-on-click-modal="false">
      <div class="e-form" style="padding: 0 10px 10px;">
        <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="系统名称：" prop="systemVal">
                  <el-select v-model="formData.systemVal" @change="changeSystem" placeholder="请选择系统名称">
                    <el-option v-for="item in systemOptions" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                  </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="上级菜单：" prop="prevLevel">
                  <el-select v-model="formData.prevLevel" placeholder="请选择上级菜单">
                    <el-option v-for="item in prevOptions" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                  </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="菜单类型：" prop="menuType">
                <el-radio-group v-model="formData.menuType">
                    <el-radio :label="'1'" v-if="formData.prevMenuType != '2'">目录</el-radio>
                    <el-radio :label="'2'" v-if="formData.prevMenuType != '2'">菜单</el-radio>
                    <el-radio :label="'3'" v-if="menuModal.tdId && formData.prevMenuType == '2'">按钮</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="菜单名称：" prop="menuName">
                <el-input v-model="formData.menuName" placeholder="请输入菜单名称"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="formData.menuType != '3'">
            <el-col :span="24">
              <el-form-item label="图标：" prop="imgIco" class="imgFormItem">
                <el-radio-group v-model="formData.imgIco">
                  <el-radio v-for="(item ,index) of iconArray" :key="index" :label="item.icon">
                    <img class="iconImg" :src="require('@/assets/images/userCenter/'+item.icon+'.png')"/>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="排序：" prop="sort">
                <el-input v-model="formData.sort" placeholder="请输入排序"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="formData.menuType == '2'">
          <el-col :span="24">
            <el-form-item label="链接地址：" prop="menuPath">
              <el-input v-model="formData.menuPath" placeholder="请输入链接地址"></el-input>
            </el-form-item>
          </el-col>
          </el-row>
          <el-row v-if="formData.menuType == '3'">
          <el-col :span="24">
            <el-form-item label="权限标记：" prop="perms">
              <el-input v-model="formData.perms" placeholder="请输入权限标记"></el-input>
            </el-form-item>
          </el-col>
          </el-row>
          <div class="buttons">
            <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
            <el-button @click="menuModal.visible = false">取消</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
// import { update, createSysParam, del } from '@/api/platform/system/systemParam'
import { getSysList, getMenuTree, saveMenu, updateMenu, deleteMenu } from '@/api/platform/system/menumanage/index'
// eslint-disable-next-line no-unused-vars
import { debounce, showLoading, hideLoading } from '@/utils/common'
export default {
    components: {},
    watch: {
        'keywords': {
            handler () {
                this.onSearch()
            }
        }
    },
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291 + 60
        },
    },
    data () {
        return {
            keywords: '',
            tableData: [],
            screenWidth: 0,
            screenHeight: 0,
            requestParams: {},
            menuModal: {
                visible: false, title: '', tdId: null
            },
            systemOptions: [],
            prevOptions: [],
            formData: {
                systemVal: undefined,
                prevLevel: undefined,
                menuType: '1',
                menuName: '',
                imgIco: undefined,
                sort: '',
                menuPath: '',
                perms: ''
            },
            iconArray: [
                { icon: 'personal_center' }, { icon: 'plan_list' }, { icon: 'ico1' },
                { icon: 'ico2' }, { icon: 'ico6' }, { icon: 'product_prices' },
                { icon: 'news' }, { icon: 'feedback' }, { icon: 'statistical_analysis' },
            ],
            formRules: {
                systemVal: [{ required: true, message: '请选择系统名称', trigger: 'change' }],
                menuType: [{ required: true, message: '请选择菜单类型', trigger: 'change' }],
                // prevLevel: [{ required: true, message: '请选择上级菜单', trigger: 'change' }],
                menuName: [
                    { required: true, message: '请输入菜单名称', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' },
                    { max: 10, message: '菜单名称长度不能超过10个字符', trigger: 'blur' }
                ],
                sort: [
                    // { required: true, message: '请输入排序', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' },
                    { pattern: new RegExp(/^[1-9]\d*$/), message: '请输入正整数', trigger: 'blur' },
                    { max: 4, message: '排序长度不能超过4个字符', trigger: 'blur' },
                ],
                menuPath: [
                    { required: true, message: '请输入链接地址', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' },
                    { max: 32, message: '链接地址长度不能超过32个字符', trigger: 'blur' }
                ],
                perms: [
                    { required: true, message: '请输入链接地址', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' },
                    { max: 32, message: '链接地址长度不能超过32个字符', trigger: 'blur' }
                ],
                // imgIco: [{ required: true, message: '请选择图标', trigger: 'change' }],
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        this.getTableData()
    },
    created () {
        // let res = [
        //     { value: '1', label: '后台管理平台' },
        //     { value: '2', label: '供应商履约平台' },
        //     { value: '3', label: '采购人履约平台' }
        // ]
        let systemOptions = []
        getSysList({}).then( response => {
            if(response && Array.isArray(response) && response.length > 0) {
                response.forEach(item => {
                    systemOptions.push({
                        value: item.id,
                        label: item.name
                    })
                })
                this.systemOptions = systemOptions
            }
        })
    },
    methods: {
        convertMenuFormat (backendMenu) {
            if (!backendMenu) return []
            // 处理数组或单个对象
            const menus = Array.isArray(backendMenu) ? backendMenu : [backendMenu]
            return menus.map(menu => ({
                id: menu.menuId,
                menuName: menu.title,
                systemVal: String(menu.classCode || '1'), // 确保是字符串
                level: menu.level?.toString(), // 转为字符串或undefined
                prevLevel: menu.parentMenuId || undefined, // 顶级菜单为undefined
                sort: menu.sort || 0, // 默认排序值
                menuType: menu.type,
                menuPath: menu.pathUrl,
                icon: menu.icon,
                perms: menu.perms,
                parentId: menu.parentMenuId,
                children: menu.children ? this.convertMenuFormat(menu.children) : [] // 递归处理子菜单
            }))
        },
        transformTable (tableList) {
            let tableNList = []
            let indexSort = 1
            for(let itemItem of this.systemOptions) {
                let itemObj = {
                    menuName: itemItem.label,
                    systemVal: itemItem.value,
                    menuType: '1',
                    sort: indexSort,
                    id: '1234_qwer' + itemItem.value,
                    menuSystem: itemItem.value,
                    menuPath: '',
                    perms: '',
                    parentId: itemItem.parentId,
                    children: []
                }
                for(let itemI of tableList) {
                    if(itemItem.value == itemI.systemVal) {
                        itemObj.children.push(itemI)
                    }
                }
                if(itemObj.children && itemObj.children.length) {
                    tableNList.push(itemObj)
                }
                indexSort++
            }
            return tableNList
        },
        changeSystem (row) {
            var res =  []
            if(row.menuSystem) {
                res.unshift({ label: '无' })
            }else {
                res.push({ value: row.id, label: row.menuName })
            }
            this.prevOptions = res
            // eslint-disable-next-line no-unused-vars
            // var a = this.prevOptions
            // this.formData.prevLevel = undefined
        },
        // 删除、编辑、新增
        operate_td (title, row ) {
            if(title === '删除') {
                this.clientPop('info', '您确定要删除该信息吗？', async () => {
                    showLoading()
                    deleteMenu({}, row.id).then(res => {
                        if(res.code === 200) {
                            /*this.clientPop('suc', '删除成功', () => {
                                this.getTableData()
                            })*/
                            this.$message.success('删除成功')
                            this.getTableData()
                        }else{
                            this.clientPop('warn', res.message, () => {})
                        }
                        hideLoading()
                    })
                    hideLoading()
                })
            }else if (title == '新增下级') {
                this.prevOptions = undefined
                this.formData.prevLevel = undefined
                if(row.menuSystem) {
                    this.formData.systemVal = row.menuSystem
                }else {
                    this.formData.systemVal = parseInt(row.systemVal)
                    this.formData.prevLevel = row.id
                    this.changeSystem(row)
                }
                this.formData.menuName = ''
                if(row.menuType === '2') {
                    this.formData.menuType = '3'
                }else {
                    this.formData.menuType = '1'
                }
                this.formData.prevMenuType = row.menuType
                this.formData.imgIco = undefined
                this.formData.sort = ''
                this.formData.menuPath = ''
                this.formData.perms = ''
                this.menuModal.title = '新增菜单'
                this.menuModal.tdId = row.id
                this.menuModal.visible = true
                this.$nextTick(()=>{
                    this.$refs.formEdit.clearValidate()
                })
            }else if (title == '编辑') {
                this.prevOptions = undefined
                this.formData.prevLevel = undefined
                if(row.menuSystem) {
                    this.formData.systemVal = row.menuSystem
                }else {
                    this.formData.systemVal = parseInt(row.systemVal)
                    this.formData.prevLevel = row.id
                    this.changeSystem(row)
                }
                this.formData.menuName = row.menuName || ''
                this.formData.menuType = row.menuType || '1'
                this.formData.prevMenuType = row.prevMenuType
                this.formData.imgIco = row.icon
                this.formData.sort = String(row.sort || '')
                this.formData.menuPath = row.menuPath || ''
                this.formData.perms = row.perms || ''
                this.formData.level = row.level
                this.formData.prevLevel = row.id
                this.menuModal.title = '编辑菜单'
                this.menuModal.tdId = row.id
                this.menuModal.visible = true
                this.$nextTick(()=>{
                    this.$refs.formEdit.clearValidate()
                })
            }else if (title == '新增') {
                this.formData.systemVal = undefined
                this.formData.prevLevel = undefined
                this.prevOptions = undefined
                this.formData.menuName = ''
                this.formData.menuType = '1'
                this.formData.prevMenuType = null
                this.formData.imgIco = undefined
                this.formData.sort = ''
                this.formData.menuPath = ''
                this.formData.perms = ''
                this.menuModal.title = '新增菜单'
                this.menuModal.tdId = null
                this.menuModal.visible = true
                this.$nextTick(()=>{
                    this.$refs.formEdit.clearValidate()
                })
            }
        },
        // 获取列表数据
        async getTableData () {
            this.requestParams = {
                // keywords: this.keywords
                keywords: ''
            }
            // let res = [
            //     { id: 1, menuName: '后台管理平台', systemVal: '1', level: undefined, prevLevel: undefined, sort: '1', menuType: '1', menuPath: '/usermang', children: [
            //         { id: 11, menuName: '系统管理', systemVal: '1', level: '2', prevLevel: undefined, sort: '1', menuType: '1', menuPath: '/usermang', children: [
            //             { id: 111, menuName: '用户管理', systemVal: '1', prevLevel: '2', sort: '1', menuType: '2', menuPath: '/userManage' },
            //             { id: 112, menuName: '用户管理新增', systemVal: '1', prevLevel: '2', sort: '2', menuType: '3', menuPath: '/userManageAdd' },
            //             { id: 113, menuName: '角色管理', systemVal: '1', prevLevel: '2', sort: '3', menuType: '2', menuPath: '/roleManage' },
            //         ] },
            //         { id: 12, menuName: '商品管理', systemVal: '1', level: '1', prevLevel: undefined, sort: '2', menuType: '1', menuPath: '/usermang', children: [
            //             { id: 121, menuName: '商品分类管理', systemVal: '1', prevLevel: '1', sort: '1', menuType: '2', menuPath: '/productManage' },
            //             { id: 122, menuName: '商品分类管理新增', systemVal: '1', prevLevel: '1', sort: '2', menuType: '3', menuPath: '/productManageAdd' }
            //         ] },
            //     ] },
            //     { id: 2, menuName: '供应商履约后台', systemVal: '2', level: undefined, prevLevel: undefined, sort: '2', menuType: '1', menuPath: '/usermang', children: [
            //         { id: 21, menuName: '系统管理', systemVal: '2', level: '1', prevLevel: undefined, sort: '1', menuType: '1', menuPath: '/usermang', children: [
            //             { id: 211, menuName: '用户管理', systemVal: '2', prevLevel: '1', sort: '1', menuType: '2', menuPath: '/userManage' },
            //             { id: 212, menuName: '用户管理新增', systemVal: '2', prevLevel: '1', sort: '2', menuType: '3', menuPath: '/userManageAdd' },
            //             { id: 213, menuName: '角色管理', systemVal: '2', prevLevel: '1', sort: '3', menuType: '2', menuPath: '/roleManage' },
            //         ] },
            //         { id: 22, menuName: '商品管理', systemVal: '2', level: '2', prevLevel: undefined, sort: '2', menuType: '1', menuPath: '/usermang', children: [
            //             { id: 221, menuName: '商品分类管理', systemVal: '2', prevLevel: '2', sort: '1', menuType: '2', menuPath: '/productManage' },
            //             { id: 222, menuName: '商品分类管理新增', systemVal: '2', prevLevel: '2', sort: '2', menuType: '3', menuPath: '/productManageAdd' }
            //         ] },
            //     ] },
            //     { id: 3, menuName: '采购人履约平台', systemVal: '3', level: undefined, prevLevel: undefined, sort: '3', menuType: '1', menuPath: '/usermang', children: [
            //         { id: 32, menuName: '商品管理', systemVal: '3', level: '1', prevLevel: undefined, sort: '2', menuType: '1', menuPath: '/usermang', children: [
            //             { id: 321, menuName: '商品分类管理', systemVal: '3', prevLevel: '1', sort: '1', menuType: '2', menuPath: '/productManage' },
            //             { id: 322, menuName: '商品分类管理新增', systemVal: '3', prevLevel: '1', sort: '2', menuType: '3', menuPath: '/productManageAdd' }
            //         ] },
            //         { id: 31, menuName: '系统管理', systemVal: '3', level: '2', prevLevel: undefined, sort: '1', menuType: '1', menuPath: '/usermang', children: [
            //             { id: 311, menuName: '用户管理', systemVal: '3', prevLevel: '2', sort: '1', menuType: '2', menuPath: '/userManage' },
            //             { id: 312, menuName: '用户管理新增', systemVal: '3', prevLevel: '2', sort: '2', menuType: '3', menuPath: '/userManageAdd' },
            //             { id: 313, menuName: '角色管理', systemVal: '3', prevLevel: '2', sort: '3', menuType: '2', menuPath: '/roleManage' },
            //         ] },
            //     ] },
            // ]
            // this.tableData = res
            this.tableDataSource = undefined
            this.tableData = undefined
            getMenuTree({}).then( response => {
                if(response && Array.isArray(response) && response.length > 0) {
                    let tableList = this.convertMenuFormat(response)
                    let tableNList = this.transformTable(tableList)
                    this.tableDataSource = JSON.parse(JSON.stringify(tableNList))
                    this.tableData = tableNList
                    if(this.keywords && this.keywords.trim()) {
                        this.onSearch()
                    }
                }
            })
        },
        // 关键词搜索
        onSearch () {
            // this.tableData = this.publicFunc.tableFunc.deepFuzzySearch(this.tableDataSource, this.keywords, [ 'menuName', 'menuPath', 'perms' ], 'children')
            this.tableData = this.publicFunc.tableFunc.filterTree(this.tableDataSource, this.keywords, [ 'menuName', 'menuPath', 'perms' ], 'children')
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    this.clientPop('info', '确认保存数据吗？', () => {
                        if (this.menuModal.title === '编辑菜单') {
                            return this.handleEditData()
                        }
                        this.handleCreateData()
                    })
                }
            })
        },
        // 修改数据
        handleEditData () {
            var saveParams = {}
            saveParams.menuId = this.formData.prevLevel
            saveParams.title = this.formData.menuName
            saveParams.type = this.formData.menuType
            saveParams.icon = this.formData.imgIco
            saveParams.sort = this.formData.sort
            saveParams.pathUrl = this.formData.menuType == '2' ? this.formData.menuPath : ''
            saveParams.perms = this.formData.menuType == '3' ? this.formData.perms : ''
            // saveParams.name = this.formData.systemVal
            saveParams.classCode = this.formData.systemVal
            updateMenu(saveParams).then(res => {
                if (res.code == 200) {
                    this.$message.success('保存成功')
                    this.menuModal.visible = false
                    this.getTableData()
                }
            })
            // update(this.formData).then(res => {
            //     if (res.message == '操作成功') {
            //         this.$message.success('保存成功')
            //         this.getTableData()
            //     }
            // })
        },
        // 保存数据
        handleCreateData () {
            // this.formData.prevLevel = row.level
            var saveParams = {}
            saveParams.title = this.formData.menuName
            saveParams.type = this.formData.menuType
            saveParams.icon = this.formData.imgIco
            saveParams.sort = this.formData.sort
            saveParams.pathUrl = this.formData.menuType == '2' ? this.formData.menuPath : ''
            saveParams.perms = this.formData.menuType == '3' ? this.formData.perms : ''
            saveParams.parentMenuId = this.formData.prevLevel
            // saveParams.name = this.formData.systemVal
            saveParams.classCode = this.formData.systemVal
            saveMenu(saveParams).then(res => {
                if (res.code == 200) {
                    this.$message.success('保存成功')
                    this.menuModal.visible = false
                    this.getTableData()
                }
            })
            // createSysParam(this.formData).then(res => {
            //     if (res.message == '操作成功') {
            //         this.$message.success('保存成功')
            //         this.getTableData()
            //     }
            // })
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.right .top {padding-right: 10px}
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}
/deep/ .el-dialog .el-dialog__body {margin: 0;height: auto;}
/deep/ .e-form .buttons {position: relative;background: transparent;}

/deep/ .e-form .el-form-item .el-form-item__explain {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}
.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}
.elTtreeTable /deep/ {
    .sort-caret.ascending {top: 5px;}
    .sort-caret.descending {display: inline-block;}
}
/deep/ .imgFormItem {
  align-items: start;
  .el-radio {margin: 0 20px 10px 0;}
  .el-radio__label {vertical-align: middle;display: inline-block;}
  img.iconImg {width: 28px;height: 27px;}
}
.actionDiv {display: inline-block;width: 160px;text-align: left;}
</style>
