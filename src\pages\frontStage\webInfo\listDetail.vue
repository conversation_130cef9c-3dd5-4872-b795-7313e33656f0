<template>
    <div class="article">
        <div v-html="content"></div>
    </div>
</template>
<script>
import { getInfoById } from '@/api/frontStage/webInfo'
import { switchTitle } from '../../../utils/common'
export default {
    data () {
        return {
            content: '',
        }
    },
    watch: {
        // eslint-disable-next-line
        $route (route) {
            document.title = '物资采购平台-' + switchTitle(this.$route.query.key)
        }
    },
    created () {
        document.title = '物资采购平台-' + switchTitle(this.$route.query.key)
        let params = { mallType: '0', state: 1, id: this.$route.query.id }
        getInfoById(params).then(res => {
            console.log(res)
            this.content = res.content
        })
    },
    methods: {},
}
</script>
<style scoped lang="scss">
.article {
    min-height: 536px;
    padding: 20px 40px;
    font-size: 16px;
    line-height: 28px;
    color: rgba(51, 51, 51, 1);
}
</style>