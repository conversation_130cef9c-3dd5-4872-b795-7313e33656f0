import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service
//删除菜单
const deleteById = params => {
    return httpGet({
        url: '/cloudCenter/sysMenu/delete',
        params,
    })
}
//批量修改菜单排序值
const changeSortValue = params => {
    return httpPost({
        url: '/cloudCenter/sysMenu/changeSortValue',
        params,
    })
}

//修改菜单状态
const updateState = params => {
    return httpPost({
        url: '/cloudCenter/sysMenu/updateState',
        params,
    })
}

const updateShowDev = params => {
    return httpPost({
        url: '/cloudCenter/sysMenu/updateShowDev',
        params,
    })
}
//修改菜单
const updateMune = params => {
    return httpPost({
        url: '/cloudCenter/sysMenu/update',
        params
    })
}

//登录时查看本机构招标信息
const getDateList = params => {
    return httpPost({
        url: '/cloudCenter/sysMenu/listByEntity',
        params,
    })
}

const getMenuDateList = params => {
    return httpPost({
        url: '/cloudCenter/sysMenu/listByEntity',
        params,
    })
}
//根据招标id查询分包信息
const save = params => {
    return httpPost({
        url: '/cloudCenter/sysMenu/create',
        params
    })
}
//查询系统当前节点所有父级订单
const selectParentMenus = params => {
    return httpPost({
        url: '/cloudCenter/sysMenu/selectParentMenus',
        params
    })
}
export {
    save,
    getDateList,
    updateMune,
    deleteById,
    changeSortValue,
    selectParentMenus,
    updateState,
    getMenuDateList,
    updateShowDev
}