<template>
    <div class="table-container">
        <div class="func-area">
            <div class="btns-group">
                <button @click="addClass">新增</button>
                <button @click="uploadItem">上架</button>
                <button @click="takeDownItems">下架</button>
                <button @click="handle">删除</button>
            </div>
            <div class="search-group">
                <div class="select-box">
                    <span>筛选：</span>
                    <el-select v-model="selectValue" placeholder="请选择">
                        <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                </div>
                <div class="search-box">
                    <input type="text" />
                </div>
            </div>
        </div>
        <el-table :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="classId"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :cell-style="cellStyle" :header-cell-style="headerRowStyle" header>
            <el-table-column label="名称" >
                <template slot-scope="scope">
                    <span style="color: #5d9eff; cursor: pointer; margin-right: 6px" @click="handleEditTable(scope)">{{scope.row.className}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="sort" label="备注"> </el-table-column>
        </el-table>
        <el-pagination @current-change="handlePageChange" @prev-click="toPrevPage" @next-click="toNextPage" background
            layout="prev, total, pager, next, jumper, sizes" :total="50"></el-pagination>
    </div>
</template>

<script>
import axios from 'axios'
import { tableHeaderStyle, tableCellStyle } from '../../assets/tableStyle.js'
export default {
    data () {
        return {
            selectValue: '数量',
            selectOptions: [
                {
                    value: '1',
                    labe: 'apple',
                },
            ],
            tableData: [],
        }
    },
    methods: {
        // 编辑表格数据
        handleEditTable (scope) {
            this.openWindowTab({ path: '/supplierSys/editView', query: { data: JSON.stringify(scope.row) } })
        },
        handle () { },
        // 上架
        uploadItem () { },
        // 下架
        takeDownItems () { },
        handleSelectionChange () { },
        handlePageSwitch (action, i) {
            console.log(action, i)
        },
        // 表格样式
        headerRowStyle () {
            return tableHeaderStyle
        },
        cellStyle () {
            return tableCellStyle
        },
        handlePageChange (i) {
            i
        },
        toPrevPage (i) {
            i
        },
        toNextPage (i) {
            i
        },
        getClassList () {
            axios({
                method: 'get',
                url: '/productCategoryTest/productCategory/listTree',
                params: { classType: '0' }
            }).then(res => {
                if(res.data.code === 200) {
                    this.tableData = res.data.data
                }else{
                    this.$message({
                        message: '获取分类列表失败，请重试！',
                        type: 'error',
                    })
                }
            })
        },
        editData (scope) {
            /* let data = {
                'children': [],
                'classBgColor': 'red',
                'classDescribe': '',
                'classIcon': '123',
                'classId': '',
                'classLevel': 0,
                'className': 'new class',
                'classType': '',
                'founderId': '',
                'founderName': '',
                'gmtCreate': '',
                'gmtModified': '',
                'isDelete': 0,
                'parentId': '234134134123',
                'sort': 0
            } */
            console.log(scope.row)
        },
        addClass () {
            this.openWindowTab({ path: '/supplierSys/editView', })
        },
        deleteClass () {
        }
    },
    created () {
        this.getClassList()
    },
}
</script>
<style scoped lang="scss">
.table-container {
    // height: 99%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .func-area {
        width: 100%;
        height: 86px;
        padding-left: 16px;
        background-color: #f9f9f9;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .btns-group {
            button {
                width: 78px;
                height: 38px;
                margin-right: 8px;
                border-radius: 6px;
                font-size: 17px;
                color: #fff;
                background-color: #8bcdea;

                &:last-child {
                    background-color: #ffb381;
                }
            }
        }

        .search-group {
            display: flex;
            align-items: center;

            .select-box {
                margin-right: 12px;
                color: #adadad;
            }

            .search-box {
                input {
                    width: 160px;
                    height: 30px;
                    border: 1px solid #f4f4f4;
                }
            }
        }
    }

    // /deep/ .el-table{border-bottom: 1px solid blue;}
    /deep/ .el-pagination {
        margin: 10px 0 50px;
    }
}
</style>
