 <template>
    <div class="productListPage">
        <main>
            <div class="center content df">
                <div class="main_left" @mouseleave="() => {typeCurrent = null; showPop = false}">
                    <div class="title p20">商品分类</div>
                    <transition-group appear name="el-fade-in">
                        <div class="typeItem p20 dfb"
                             :style="{ color: typeCurrent === i ? '#fff' : '', background: typeCurrent === i ? '#226FC7' : '' }"
                             v-for="(item, i) in typeList" :key="i"
                             @mouseenter="showPopMenu(i)">
                            <div @click="selectClass(item)">{{ item.className }}</div>
                            <i class="el-icon-arrow-right"></i>
                        </div>
                    </transition-group>
                    <!-- 弹框 -->
                    <transition appear name="el-fade-in">
                        <div class="pop" v-show="showPop" :style="{ height: typeList.length * 58 + 67 + 'px' }">
                            <div class="sector" v-for="(item, i) in popContent" :key="i">
                                <div class="pop_title pointer" @click="selectClass(item)">
                                    {{ item.className }}
                                </div>
                                <div class="items df">
                                    <span @click="selectClass(subitem)" v-for="(subitem, index) in item.children"
                                       :key="index">{{ subitem.className }}</span>
                                </div>
                            </div>
                        </div>
                    </transition>
                </div>
                <div class="product-box center front" v-loading="showLoading">
                    <filterBox :form="filterArr" :list="checkedList" @checkChange="checkChange"></filterBox>
                    <div class="dfa filterBox_top">
                        <el-radio-group v-model="queryAll.checkedRadio" @change="checkboxChange">
                            <el-radio v-for="item in checkboxList" :label="item.value" :value="item.value"
                                      :key="item.value">
                                {{ item.label }}
                            </el-radio>
                        </el-radio-group>
                        <div :class="['sortPrice', 'sortBtn', 'ml10', 'pointer', sortObj.tag === 0 ? 'selected' : '']"
                             @click="selectSort(0)">
                            <span style="margin-right: 0;">综合排序</span>
                        </div>
                        <div :class="['sortPrice', 'sortBtn', 'pointer', sortObj.tag === 1 ? 'selected' : '']"
                             @click="selectSort(1)">
                            <span>价格</span><img :src="priceArrow" alt="">
                        </div>
                        <div :class="['sortBtn', 'pointer', sortObj.tag === 2 ? 'selected' : '']"
                             @click="selectSort(2)">
                            <span>更新时间</span><img :src="timeArrow" alt="">
                        </div>
                    </div>
                    <div class="row dfa filterBox_bottom">
                        <div>价格范围：</div>
                        <div class="priceFilter">￥<input type="text" v-model="queryAll.price.min"></div>
                        <div class="bar"></div>
                        <div class="priceFilter">￥<input type="text" v-model="queryAll.price.max"></div>
                        <div class="searchInput df">
                            <div class="dfa">
                                <img src="@/assets/images/search1.png" alt="">
                                <input type="text" placeholder="搜索商品/店铺" v-model="queryAll.keyword"/>
                            </div>
                            <button @click="onSearch">搜索</button>
                        </div>
                    </div>
                    <div class="productClass df">
                        当前分类：
                        <div v-for="item in productClassList" :key="item.classId">
                            {{ item.label }} <i class="pointer el-icon el-icon-close" @click="removeProductClass(item)"></i>
                        </div>
                    </div>
                    <div class="product-list">
                        <transition-group
                            name="el-fade-in"
                            tag="div"
                            v-for="(item, i) in productList"
                            :key="item.productMinImg"
                            appear
                        >
                            <div
                                class="item pointer dfa mb20"
                                :style="{ marginRight: (i+1)%4===0 ? 0 : '20px' }"
                                @click="openWindowTab({ path: '/mFront/productDetail', query: { productId: item.productId } })"
                                :key="item.productMinImg"
                            >
                                <img
                                    :src="item.productMinImg ? imgUrlPrefixAdd + item.productMinImg : require('@/assets/images/img/queshen5.png')"
                                    alt="">
                                <div class="title mt10 textOverflow2">{{ item.productName }}</div>
                                <div class="description textOverflow1">{{ item.skuName || '   ' }}</div>
                                <div class="price">￥{{ item.productMinPrice.toFixed(2) }}</div>
                                <div class="productTag" v-if="item.isBusiness">平台自营</div>
                                <div :title="item.shopName" class="shopName textOverflow1" v-else-if="item.shopName" @click.stop="goToShop(item.shopId)">
                                    {{ item.shopName }}
                                </div>
                                <!-- <div class="shopTag" >{{item.shopName}}</div> -->

                            </div>
                        </transition-group>
                    </div>
<!--                    <div v-if="productList.length === totalCount" class="nomore">没有更多了~~</div>-->
                    <Pagination
                        :total="totalCount"
                        :current-page="queryAll.page"
                        :page-size.sync="queryAll.limit"
                        :page-sizes="[20, 40, 60, 80]"
                        @currentChange="handlePageChange"
                        @sizeChange="handleSizeChange"
                    />
                </div>
            </div>
        </main>
    </div>
</template>
<script>
import filterBox from '../../components/searchBox'
import Pagination from '@/pages/frontStage/components/simplePagination'
import arrow from '@/assets/images/arrow.png'
import arrow_up from '@/assets/images/arrow_up.png'
import arrow2 from '@/assets/images/arrow2.png'
import arrow2_up from '@/assets/images/arrow2_up.png'
import { getBrand } from '@/api/frontStage/brand'
// import { getByCity } from '@/api/frontStage/systemParam'
import { getLoginMaterialPageList, getMaterialPageList } from '@/api/frontStage/productList'
import { getCategoryTree } from '@/api/frontStage/productCategory'
import { getBrandMU, getCityMU } from '@/utils/common'
import { mapState } from 'vuex'

export default {
    computed: {
        ...mapState(['materialCity', 'userInfo']),
    },
    components: {
        filterBox, Pagination
    },
    data () {
        return {
            showLoading: false,
            typeCurrent: null,
            showPop: false,
            typeList: [],
            popContent: [],
            init: {
                // 值为99 代表低脂易耗品和大宗临购商品都克搜索
                productType: 99,
                mallType: 0
            },
            queryAll: {
                page: 1,
                limit: 20,
                label: {},
                price: {
                    min: null, max: null,
                },
                keyword: null,
                checkedRadio: null,
            },
            tagFilterStr: '0-desc',
            productList: [],
            arrow,
            arrow_up,
            arrow2,
            arrow2_up,
            priceArrow: arrow,
            timeArrow: arrow,
            checkedList: [],
            filterArr: [],
            checkboxList: [
                { label: '全部', value: null },
                { label: '平台自营', value: 1 },
                { label: '路桥内部店', value: 2 },
                { label: '其它', value: 3 },
            ],
            sortObj: {
                tag: 0,
                descend: true,
            },
            flag: true,
            totalCount: 0,
            productClassList: []
        }
    },
    watch: {
        $route: function () {
            this.queryAll.keyword = this.$route.query.keywords || ''
            this.getMaterialPageListM()
        },
        'sortObj.tag': {
            handler (newVal, oldVal) {
                if (oldVal == 2) {
                    if (this.sortObj.descend) {
                        return this.timeArrow = this.arrow
                    }
                    this.timeArrow = this.arrow_up
                } else if (oldVal == 1) {
                    if (this.sortObj.descend) {
                        return this.priceArrow = this.arrow
                    }
                    this.priceArrow = this.arrow_up
                }
            }
        },
        tagFilterStr (newVal) {
            switch (newVal) {
            case '1-asc':
                this.priceArrow = this.arrow2_up
                break
            case '1-desc':
                this.priceArrow = this.arrow2
                break
            case '2-asc':
                this.timeArrow = this.arrow2_up
                break
            case '2-desc':
                this.timeArrow = this.arrow2
                break
            }
        },
    },
    created () {
        let { keywords, classId, classPath } = this.$route.query
        this.queryAll.keyword = keywords || ''
        if(classId && classPath) this.productClassList[0] = { classId, label: classPath }
        this.getClassTree() // 获取商品分类树
        this.getFilterList()
        this.getMaterialPageListM() // 查询商品
    },
    async mounted () {
        // 懒加载列表
        // window.addEventListener('scroll', debounce(this.handleTouchBottom, 200))
    },
    beforeDestroy () {
        // window.removeEventListener('scroll', debounce(this.handleTouchBottom, 100))
    },
    methods: {
        goToShop (shopId) {
            this.openWindowTab({ path: '/mFront/shopIndex', query: { shopId } })
        },
        handleTouchBottom () {
            let isBottom = window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 500
            if (isBottom && this.productList.length < this.totalCount) {
                this.queryAll.page++
                this.getScrData()
            }
        },
        handlePageChange (page) {
            this.queryAll.page = page
            this.getMaterialPageListM()
        },
        handleSizeChange (size) {
            this.queryAll.limit = size
            this.getMaterialPageListM()
        },
        async getFilterList (id = '') {
            this.showLoading = true
            if(id) {
                let brands = await this.getBrandM()
                this.filterArr.forEach((item, i, arr) => {
                    if(item.name === '品牌') arr.splice(i, 1, brands)
                })
            }else{
                let brands = await this.getBrandM()
                let cities = null
                brands ? this.filterArr.push(brands) : null
                cities ? this.filterArr.push(cities) : null
            }
            this.showLoading = false
        },
        async getClassTree () {
            // 获取商品分类树 需要重置分类类型为0
            this.typeList = await getCategoryTree( {
                // 值为99 代表低脂易耗品和大宗临购商品都克搜索
                productType: 0,
                mallType: 0
            },)
        },
        showPopMenu (i) {
            this.typeCurrent !== i ? this.showPop = true : this.showPop = !this.showPop
            this.typeCurrent = i
            this.popContent = this.typeList[i].children
        },
        selectClass ({ classId, classPath }) {
            this.queryAll.keyword = ''
            this.productClassList[0] = { classId, label: classPath.replaceAll('/', ' > ') }
            this.showPop = false
            this.getFilterList(classId)
            this.getMaterialPageListM()
        },
        removeProductClass ({ classId }) {
            this.productClassList.forEach((item, i, arr) => {
                if (item.classId === classId) arr.splice(i, 1)
            })
            this.$router.push({ path: this.$route.path })
            this.getMaterialPageListM()
        },
        // 滚动获取
        async getScrData () {
            let params = this.initParams()
            let res
            if ((localStorage.getItem('token'))) {
                res = await getLoginMaterialPageList(params)
            } else {
                res = await getMaterialPageList(params)
            }
            this.totalCount = res.totalCount
            this.productList = this.productList.concat(res.list || [])
        },
        // 获取 城市
        async getByCityM () {
            return getCityMU(this.materialCity)
        },
        // 获取品牌
        async getBrandM () {
            /*let params = {
                page: 1,
                limit: 100
            }*/
            let res = await getBrand({ classId: this.productClassList[0]?.classId || '' })
            return getBrandMU(res)
        },
        // 获取商品列表
        async getMaterialPageListM () {
            // this.queryAll.page = 1
            // this.queryAll.limit = 20
            let params = this.initParams()
            this.showLoading = true
            if (localStorage.getItem('token')) {
                try {
                    let res1 = await getLoginMaterialPageList(params)
                    this.productList = res1.list || []
                    this.totalCount = res1.totalCount
                    if(res1.code !== 401) return this.showLoading = false
                    let res2 = await getMaterialPageList(params)
                    this.productList = res2.list || []
                    this.totalCount = res2.totalCount
                }catch(e) {
                    console.log('列表请求失败', e)
                }
            } else {
                try{
                    let res = await getMaterialPageList(params)
                    this.productList = res.list || []
                    this.totalCount = res.totalCount
                }catch (e) {
                    console.log('列表请求失败', e)
                }
            }
            this.showLoading = false
        },
        // 初始化参数
        initParams () {
            let params = {
                productType: this.init.productType,
                page: this.queryAll.page,
                limit: this.queryAll.limit,
                orderBy: this.tagFilterStr,
                isBusiness: this.queryAll.checkedRadio,
                classId: this.productClassList[0]?.classId || ''
            }
            let { keyword, label, price } = this.queryAll
            // 关键字
            keyword ? params.keywords = keyword : ''
            // 品牌
            label.brand ? params.brandName = label.brand.label : ''
            // 城市
            label.city ? params.city = label.city.value : ''
            // 以下价格
            price.max ? params.belowPrice = price.max : ''
            // 以上价格
            price.min ? params.abovePrice = price.min : ''
            return params
        },
        getFilterStr (obj) {
            if (obj.descend) {
                return this.tagFilterStr = `${obj.tag}-desc`
            }
            this.tagFilterStr = `${obj.tag}-asc`
        },
        // 条件列表
        checkChange (obj) {
            this.queryAll.label = obj
            this.getMaterialPageListM()
        },
        // 按钮点击
        checkboxChange () {
            this.getMaterialPageListM()
        },
        onSearch () {
            this.getMaterialPageListM()
        },
        // 排序
        selectSort (tag) {
            if (this.sortObj.tag == tag) {
                this.sortObj.descend = !this.sortObj.descend
            }
            this.sortObj.tag = tag
            this.getFilterStr(this.sortObj)
            this.getMaterialPageListM()
        },
    }
}
</script>
<style scoped lang="scss">

.main_left {
    width: 234px !important;
    min-height: 763px;
    background: #fff;
    position: relative;

    .title {
        font-size: 20px;
        font-weight: 500;
        color: rgba(0, 0, 0, 1);
        font-weight: 500;
        padding-bottom: 20px;
    }

    .typeItem {
        width: 100%;
        height: 58px;
        font-size: 16px;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        cursor: pointer;
        user-select: none;

        div {
            width: 80%;
        }
    }

    .typeItem:hover {
        color: rgba(34, 111, 199, 1);
    }

    .pop {
        width: 1092px;
        min-height: 762px;
        padding: 30px;
        border: 1px solid #E6E6E6;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
        background-color: #fff;
        position: absolute;
        top: 0;
        left: 234px;
        z-index: 10;
        overflow-y: auto;

        .sector:not(:last-child) {
            //margin-bottom: 10px;
        }

        .sector {
            .pop_title {
                margin-bottom: 16px;
                font-size: 16px;
                font-weight: 500;
            }

            .items {
                font-size: 14px;
                flex-wrap: wrap;
                color: #666;

                span {
                    margin: 0 0 16px 0;
                    padding: 0 10px;
                    border-right: 1px solid #E6E6E6;
                    cursor: pointer;
                }

                & span:first-child {
                    padding-left: 0;
                }

                & span:last-child {
                    border-right: none;
                }
            }
        }
    }
}

.productListPage {

    .tabs-bar {
        width: 100%;
        height: 60px;

        .top_content {
            //width: 1326px;
            //min-width: 1326px;
            height: inherit;

            & > div {
                width: 200px;
                height: inherit;
                font-size: 18px;
                font-weight: 500;
                text-align: center;
                line-height: 60px;
                color: rgba(51, 51, 51, 1);
                position: relative;
                cursor: pointer;

                .icon {
                    width: 18px;
                    height: 18px;
                    position: absolute;
                    left: 23px;
                    top: 20px;
                    background-image: url(../../../../assets/images/productList/分类.png);
                    background-size: 18px 18px;
                }
            }

            & > div:first-child {
                color: #fff;
                background: rgba(33, 110, 198, 1);
            }
        }
    }

    main {
        padding: 20px 0 60px 0;
        background-color: #f5f5f5;
    }

    .content {
        width: 1326px;
        justify-content: space-between;
    }

    .product-box {
        width: 1060px;
        //min-width: 1326px;

        .productClass {
            padding: 10px;
            line-height: 28px;

            div {
                height: 28px;
                padding: 0 10px;
                border: 1px solid lightgray;
                user-select: none;
            }
        }

        .product-list {
            //width: inherit;
            min-height: 800px;
            display: flex;
            flex-wrap: wrap;

            .item {
                width: 250px;
                height: 300px;
                font-size: 14px;
                flex-direction: column;
                background-color: #fff;
                position: relative;
                transition: opacity 0.5s;
                overflow: hidden;
                //margin-right: 20px;

                img {
                    width: 250px;
                    height: 200px;
                    object-fit: cover;
                }

                span {
                    margin-bottom: 5px;
                }

                .title {
                    max-width: 90%;
                    height: 40px;
                    line-height: 20px;
                    color: rgba(51, 51, 51, 1);
                    text-align: center;
                }

                .description {
                    max-width: 90%;
                    height: 20px;
                    line-height: 20px;
                    color: rgba(153, 153, 153, 1);
                }

                .price {
                    width: 100%;
                    padding-right: 12px;
                    font-size: 18px;
                    line-height: 23px;
                    text-align: right;
                    color: rgba(212, 48, 48, 1);
                }
                .productTag, .shopName {
                    position: absolute;
                    left: 12px;
                }

                .productTag {
                    padding: 2px 3px;
                    font-size: 10px;
                    color: #fff;
                    bottom: 6px;
                    background-color: #f99c4a;
                }
                .shopName {
                    max-width: 90px;
                    bottom: 10px;
                    font-size: 14px;
                    color: gray;
                }
                .shopTag {
                    padding: 2px 3px;
                    font-size: 10px;
                    position: absolute;
                    right: 12px;
                    bottom: 6px;
                    color: #fff;
                    background-color: #f99c4a;
                }
            }
        }
    }
}
/*/deep/ .el-pagination {
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .el-pagination__sizes {
        height: 34px;
    }
    .el-pagination__jump {
        height: 34px;
    }
    .el-input, .el-input__inner {
        height: 34px;
        border-radius: 0 !important;
    }
    .btn-prev, .btn-next {
        height: 34px;
        font-size: 14px;
        font-weight: bold;
        padding: 0 14px;
        border: 1px solid #cdcdcd;
    }
    .btn-next {
        margin-left: 10px;
    }
}*/
</style>