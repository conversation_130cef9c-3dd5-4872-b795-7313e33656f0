<template>
    <div class="e-form" v-loading="showLoading">
        <BillTop @cancel="handleClose"/>
        <div class="tabs warningTabs">
            <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="账号信息" name="accountInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="店铺信息" name="baseInfo" :disabled="clickTabFlag" v-if="shopInfoShow">
                </el-tab-pane>
                <el-tab-pane label="企业信息" name="enterpriseInfo" :disabled="clickTabFlag" v-if="enterprise">
                </el-tab-pane>
                <el-tab-pane label="管理员信息" name="adminInfo" :disabled="clickTabFlag" v-if="adminInfoShow">
                </el-tab-pane>
                <el-tab-pane label="个体户信息" name="individualHouseholdInfoCon" :disabled="clickTabFlag" v-if="individualHousehold">
                </el-tab-pane>
                <div id="tabs-content">
                    <div id="accountInfoCon" class="con">
                        <div class="tabs-title" id="accountInfo">账号信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="userInfo" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="用户编号：">
                                            <span>{{ userInfo.userNumber }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="昵称：" >
                                            <span>{{ userInfo.nickName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="真实姓名：" >
                                            <span>{{ userInfo.nickName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="账号：" >
                                            <span>{{ userInfo.account }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="手机号：">
                                            <span>{{ userInfo.userMobile }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="邮箱：">
                                            <span>{{ userInfo.email }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="性别：">
                                            <span v-if="userInfo.gender==1">男</span>
                                            <span v-if="userInfo.gender==0">女</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="是否内部用户：">
                                            <span v-if="userInfo.isInternalUser==1">是</span>
                                            <span v-if="userInfo.isInternalUser==0">否</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="上次登陆时间" >
                                            <span>{{ userInfo.gmtLogin }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="注册时间：" >
                                            <span>{{ userInfo.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div id="baseInfCon" class="con" v-if="shopInfoShow">
                        <div class="tabs-title" id="baseInfo">店铺信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="店铺名称：" prop="shopName">
                                            <span>{{ formData.shopName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="店铺类型：" prop="shopType">
                                            <span v-if="formData.shopType == '1'">企业店铺</span>
                                            <span v-if="formData.shopType == '0'">个体户店铺</span>
                                            <span v-if="formData.shopType == '2'">个人店铺</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="店铺所在地址：" prop="province">
                                            <span>{{ formData.province }}</span>
                                            <span>{{ formData.city }}</span>
                                            <span>{{ formData.county }}</span>
                                            <span>{{ formData.detailedAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="店铺创建时间：" prop="gmtCreate">
                                            <span>{{ formData.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!--                                <el-row>-->
                                <!--                                    <el-col :span="12">-->
                                <!--                                        <el-form-item label="经度：" prop="longitude">-->
                                <!--                                            <span>{{ formData.longitude }}</span>-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                    <el-col :span="12">-->
                                <!--                                        <el-form-item label="纬度：" prop="latitude">-->
                                <!--                                            <span>{{ formData.latitude }}</span>-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                </el-row>-->
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="店铺状态：" prop="state">
                                            <el-tag v-if="formData.state==1" type="success">启用</el-tag>
                                            <el-tag v-if="formData.state==0" type="danger">停用</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="是否自营：" prop="isBusiness">
                                            <span>{{ formData.isBusiness === 1 ? '自营' : '非自营' }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="是否内部店铺：" prop="isInternalShop">
                                            <span>{{ formData.isInternalShop === 1 ? '内部店铺' : '非内部店铺' }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="是否支持内部结算：" prop="isInternalSettlement">
                                            <span>{{ formData.isInternalSettlement === 1 ? '支持' : '不支持' }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!--                    企业信息-->
                    <div id="enterpriseInfo" class="con" v-if="enterprise">
                        <div class="tabs-title" id="enterpriseInfo">企业信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="enterpriseData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="企业名称：" prop="enterpriseName">
                                            <span>{{ enterpriseData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                            <span>{{ enterpriseData.socialCreditCode }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商：" prop="socialCreditCode">
                                            {{ enterpriseData.isSupplier == 2 ? '是' : '否' }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="法定代表人：" prop="legalRepresentative">
                                            <span>{{ enterpriseData.legalRepresentative }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="注册资本：" prop="registeredCapital">
                                            <span>{{ enterpriseData.registeredCapital }}(万元)</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册地址：" prop="detailedAddress">
                                            <span>{{ enterpriseData.detailedAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="注册日期：" prop="creationTime">
                                            <span>{{ enterpriseData.creationTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" >
                                        <el-form-item label="营业执照：" prop="businessLicense">
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" align="center"  >
                                        <el-image style="width: 900px; height: 500px" :src="enterpriseData.businessLicense"></el-image>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!--                    管理员信息-->
                    <div id="adminInfo" class="con" v-if="adminInfoShow">
                        <div class="tabs-title" id="adminInfo">管理员信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="enterpriseData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="姓名：" prop="adminName" >
                                            <span>{{ enterpriseData.adminName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="手机号码：" prop="adminPhone">
                                            <span>{{ enterpriseData.adminPhone }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="身份证号码：" prop="adminNumber">
                                            <span>{{ enterpriseData.adminNumber }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="身份证人像面："  prop="cardPortraitFace" >
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="身份证国徽面："  prop="cardPortraitNationalEmblem">
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12" align="center" >
                                        <el-image style="width: 360px; height: 200px" :src="enterpriseData.cardPortraitFace"></el-image>
                                    </el-col>
                                    <el-col :span="12" align="center">
                                        <el-image style="width: 360px; height: 200px" :src="enterpriseData.cardPortraitNationalEmblem"></el-image>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div id="individualHouseholdInfoCon" class="con" v-if="individualHousehold">
                        <div class="tabs-title" id="individualHouseholdInfoCon">个体户信息</div>
                        <div style="width: 100%" class="form">
                            <el-form  label-width="200px" ref="rulesBase22" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="名称：" prop="enterpriseName">
                                            <span>{{ enterpriseData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                            <span>{{ enterpriseData.socialCreditCode }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商：" prop="socialCreditCode">
                                            {{ enterpriseData.isSupplier == 2 ? '是' : '否' }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="经营者姓名：" prop="operator">
                                            <span>{{ enterpriseData.operator }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="经营场所：" prop="placeOfBusiness">
                                            <span>{{ enterpriseData.placeOfBusiness }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册日期：" prop="creationTime">
                                            <span>{{ enterpriseData.creationTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="经营范围：" prop="mainBusiness">
                                            <span>{{ enterpriseData.mainBusiness }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" >
                                        <el-form-item label="营业执照：" prop="businessLicense">
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" align="center">
                                        <el-image style="width: 900px; height: 500px" :src="enterpriseData.businessLicense"></el-image>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import $ from 'jquery'
import { throttle } from '@/utils/common'
import { getUserShopEnterpriseInfoByUserSn } from '@/api/platform/user/userInquire'
import { previewFile } from '@/api/platform/common/file'

export default {

    data () {
        return {
            showLoading: false,
            adminInfoShow: false,
            shopInfoShow: false,
            userInfo: {},
            individualHousehold: false,
            enterprise: false,
            // 企业信息
            enterpriseData: {},
            //基本信息表单数据
            formData: {},
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    created () {
        this.getInfoM()
    },
    mounted () {
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'enterpriseInfo', 'adminInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    return $item ? $item.offsetTop : null
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 70 + 'px !important'
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        // 获取企业信息
        getInfoM () {
            this.showLoading = true
            getUserShopEnterpriseInfoByUserSn({ userNumber: this.$route.query.userNumber }).then(res => {
                this.userInfo = res.user
                if(res.shop != null) {
                    this.formData = res.shop
                    this.shopInfoShow = true
                }
                this.enterpriseData = res.enterpriseInfo
                if(res.enterpriseInfo != null) {
                    if(res.enterpriseInfo.adminNumber != null) {
                        previewFile({ recordId: this.enterpriseData.cardPortraitFaceId }).then(res => {
                            this.enterpriseData.cardPortraitFace = window.URL.createObjectURL(res)
                        })
                        previewFile({ recordId: this.enterpriseData.cardPortraitNationalEmblemId }).then(res => {
                            this.enterpriseData.cardPortraitNationalEmblem = window.URL.createObjectURL(res)
                        })
                        this.adminInfoShow = true
                    }
                    let enterpriseType = this.enterpriseData.enterpriseType
                    previewFile({ recordId: this.enterpriseData.businessLicenseId }).then(res => {
                        this.enterpriseData.businessLicense = window.URL.createObjectURL(res)
                    })
                    if(enterpriseType == 0) {
                        this.individualHousehold = true
                        this.enterprise = false
                    }
                    if(enterpriseType == 1) {
                        this.enterprise = true
                        this.individualHousehold = false
                    }
                    if(enterpriseType == 2) {
                        this.enterprise = false
                        this.individualHousehold = false
                    }
                }
            }).finally(() => {
                this.showLoading = false
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
    }
}
</script>

<style lang='scss' scoped>
.warningTabs {
    padding-top: 70px;
}
.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
</style>
