<template>
    <main>
        <div class="list-title dfa">修改密码</div>
        <div class="content-box" v-loading="loginLoading">
            <!--      进度条      -->
            <div class="progress df">
                <div class="step">
                    <div class="bar">
                        <img :src="currentStage === 0 ? stepTwo : stepOne" alt="">
                        <div class="n">1</div>
                        <div class="step-name" :style="{ color: currentStage === 0 ? '#2A82E4' : '' }">1.手机号验证
                        </div>
                    </div>
                </div>
                <div class="step">
                    <div class="bar">
                        <img :src="currentStage === 1 ? stepTwo : ( currentStage === 0 ? stepThree : stepOne )" alt="">
                        <div class="n">2</div>
                        <div class="step-name" :style="{ color: currentStage === 1 ? '#2A82E4' : '' }">2.设置新密码
                        </div>
                    </div>
                </div>
                <div class="step">
                    <div class="bar">
                        <img :src="currentStage === 2 ? stepTwo : stepThree" alt="">
                        <div class="n">3</div>
                        <div class="step-name" :style="{ color: currentStage === 2 ? '#2A82E4' : '' }">3.完成</div>
                    </div>
                </div>
            </div>
            <!--      验证手机号      -->
            <div class="verify-phone" v-show="currentStage === 0">
                <div class="title">为了确认是您本人操作，请完成以下验证</div>
                <div class="account df">
                    <div>
                        <div class="mb10">昵称：</div>
                        <div class="info">{{ userInfo.userName }}</div>
                    </div>
                    <div>
                        <div class="mb10">手机号：</div>
                        <div class="info">{{ userMobile }}</div>
                    </div>
                </div>
                <div class="code">
                    <div class="mb10">验证码：</div>
                    <div class="df">
                        <el-input v-model="verificationCode" placeholder="请输入短信验证码"></el-input>
                        <button @click="codeDialogVisible = true">{{verifyText}}</button>
                    </div>
                </div>
                <button class="next" @click="handleNext">下一步</button>
            </div>
            <!--      设置密码      -->
            <div class="set-password" v-show="currentStage === 1">
                <div class="title">设置新密码</div>
                <div>
                    <el-input :type="pwType" v-model="newPassword" placeholder="请输入新密码">
                        <i slot="suffix">
                            <img class="input-icon pointer" :src="hidePass" @click="toggleShowPass"/>
                        </i>
                    </el-input>
                    <div style="margin-top: -2%;color:#9b9696;"><i class="el-icon-success" style="color: #216EC6;"></i>  密码由8-16位数字、字母或符号组成</div>
                    <div style="margin-top: 4%;color:#9b9696;"><i class="el-icon-success" style="color: #216EC6;"></i>  至少含2种以上字符</div>
                </div>
                <div style="margin-top: 4%;">
                    <button @click="handleSubmit" style="width:300px;">提交</button>
                    <div style="margin-top: 4%;color:#9b9696;">安全提示：新密码请勿与旧密码过于相似。</div>
                </div>
            </div>
            <!--      完成      -->
            <div class="complete" v-show="currentStage === 2">
                <img src="@/assets/images/userCenter/zc_chenggong.png" alt="">
                <div>密码修改成功</div>
            </div>
        </div>
        <Dialog title="图形验证码" :close-on-click-modal="false" width="40%" top="30vh" :visible.sync="codeDialogVisible" @open="getCodeImg">
            <div class="verifyBox dfc">
                <el-input v-model="verification.verifyInput" placeholder="请输入图形验证码"/>
                <img class="pointer" :src="verification.verifyImg" @click="getCodeImg" alt="">
            </div>
            <span slot="footer">
                <el-button
                    class="codeDialogBtn" style="margin-right: 30px;" @click="codeDialogVisible = false"
                >取消</el-button>
                <el-button class="codeDialogBtn" type="primary" @click="checkCode">确定</el-button>
            </span>
        </Dialog>
    </main>
</template>

<script>
import { mapState } from 'vuex'
import ico_hide from '@/assets/images/ico_hide.png'
import ico_show from '@/assets/images/ico_show.png'
import { checkUpdatePassCode, updatePassSendCode, updatePassword, getPrivateKeyId, getSendCodeImg, checkSendCodeVerify } from '@/api/frontStage/login'
import { encrypt } from '@/utils/common'
import { loginOut } from '@/api/frontStage/userCenter'
import Dialog from '@/pages/frontStage/components/dialog.vue'

export default {
    name: 'changePass',
    components: { Dialog },
    data () {
        return {
            verifyText: '短信验证码',
            loginLoading: false,
            codeDialogVisible: false,
            verification: { verifyImg: '', verifyId: '', verifyInput: '' },
            currentStage: 0,
            stepOne: require('@/assets/images/userCenter/step_1.png'),
            stepTwo: require('@/assets/images/userCenter/step_2.png'),
            stepThree: require('@/assets/images/userCenter/step_3.png'),
            verificationCode: null,
            ico_hide,
            ico_show,
            hidePass: ico_hide,
            pwType: 'password',
            newPassword: '',
        }
    },
    computed: {
        ...mapState(['userInfo']),
        userMobile () {
            let tel = this.userInfo.userMobile + ''
            return tel.substring(0, 4) + '****' + tel.substring(8)
        },
    },
    created () {
    },
    methods: {
        getCodeImg () {
            this.verification.verifyInput = ''
            getSendCodeImg().then(res => {
                let blob = new Blob([res.data], { type: 'image/jpeg' })
                this.verification.verifyImg = window.URL.createObjectURL(blob)
                this.verification.verifyId = res.headers.verifyid
            })
        },
        checkCode () {
            let { verifyId, verifyInput } = this.verification
            checkSendCodeVerify({ id: verifyId, verifyInput }).then(res => {
                if(res.code !== 200) return
                this.codeDialogVisible = false
                this.getVerificationCode()
            })
        },
        handleNext () {
            // 校验验证码
            this.loginLoading = true
            checkUpdatePassCode({ phone: this.userInfo.userMobile, code: this.verificationCode }).then(res => {
                if(res.code == 200) {
                    this.currentStage = 1
                    this.loginLoading = false
                }
                this.loginLoading = false
            }).catch(() =>{
                this.loginLoading = false
            })
        },
        async getPrivateKey (phone) {
            let res = await getPrivateKeyId({ phone })
            if(typeof res !== 'string' || !res.length > 0) return ''
            return encrypt(res.verification)
        },
        async getVerificationCode () {
            let privateKeyId = await this.getPrivateKey(this.userInfo.userMobile)
            if(!privateKeyId) return
            this.loginLoading = true
            updatePassSendCode({ phone: this.userInfo.userMobile, privateKeyId }).then(res => {
                if (res.code == 200) {
                    this.$message({
                        message: '发送成功',
                        type: 'success'
                    })
                    this.loginLoading = false
                    let countdown = 60
                    let timer = setInterval(() => {
                        if (countdown == 0) {
                            this.verifyText = '获取验证码'
                            return clearInterval(timer)
                        }
                        this.verifyText = `倒计时 ${countdown}`
                        countdown -= 1
                    }, 1000)
                }
            }).finally(() => {
                this.loginLoading = false
            })
        },
        toggleShowPass () {
            this.hidePass = this.hidePass === this.ico_hide ? this.ico_show : this.ico_hide
            this.pwType = this.pwType === 'text' ? 'password' : 'text'
        },
        handleSubmit () {
            let len = this.newPassword.trim().length
            if (len < 8 || len > 20) {
                return this.$message.error('请输入8到20位的密码！')
            }
            if (!new RegExp(/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9])(.{8,20})$/).test(this.newPassword)) {
                return this.$message.error('密码必须同时包含数字，字母和特殊字符！')
            }
            let enPass =  encrypt(this.newPassword)
            this.loginLoading = true
            updatePassword({ newPassword: enPass }).then(res =>{
                if(res.code == 200) {
                    this.currentStage = 2
                    this.loginLoading = false
                    loginOut().then(() => {
                        localStorage.removeItem('token')
                        this.$store.commit('setUserInfo', {})
                        window.location.reload()
                    })
                }
                this.loginLoading = false
            }).catch(() => {
                this.loginLoading = false
            })
        },
    }
}
</script>

<style scoped lang="scss">
main {
    border: 1px solid rgba(229, 229, 229, 1);
}
.list-title {
  height: 50px;
  padding: 15px 19px 15px 21px;
  font-size: 20px;
  line-height: 20px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);
  position: relative;

  &::before {
    width: 3px;
    height: 20px;
    margin-right: 10px;
    content: '';
    display: block;
    background-color: rgba(33, 110, 198, 1);
  }

  .msg {
    margin-left: 20px;
    font-size: 12px;
    color: #808080;

    span {
      color: #216EC6;
    }
  }

  .add-addr {
    width: 120px;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(33, 110, 198, 1);
    // border: 1px solid rgba(33, 110, 198, 1);
    justify-content: center;
    position: absolute;
    top: 10px;
    right: 19px;
    user-select: none;

    &:active {
      color: #fff;
      background-color: rgba(33, 110, 198, 1);
    }
  }
}
.content-box {
    height: 600px;
    padding-top: 40px;

    .progress {
        justify-content: center;

        .step {
            width: 240px;
            margin-right: -4px;

            .bar {
                height: 30px;
                position: relative;
                justify-content: center;
                text-align: center;

                .n {
                    height: 30px;
                    margin: -30px 0 15px;
                    font-size: 12px;
                    line-height: 26px;
                    color: #fff;
                }

                .step-name {
                    font-size: 14px;
                    color: #88B4E5;
                }
            }
        }
    }

    .verify-phone, .set-password, .complete {
        margin-top: 30px;
        padding-top: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .title {
            margin-bottom: 40px;
            font-size: 18px;
            color: #333;
            font-weight: bold;
        }

        button {
            height: 35px;
            box-sizing: border-box;
        }
    }

    .verify-phone {
        .account {
            width: 270px;
            margin-bottom: 40px;
            font-size: 14px;

            & > div:first-child {
                width: 142px;
            }

        }

        .mb10 {
            color: #999;
        }

        /deep/ .el-input {
            margin-right: 10px;

            &, .el-input__inner {
                width: 160px;
            }
        }

        button:not(.next) {
            width: 100px;
            border: 1px solid #216EC6;
            color: #216EC6;
            background-color: #fff;
        }
        .next {
            width: 270px;
            margin-top: 20px;
            background-color: #216EC6;
            color: #fff;
        }
    }

    .set-password {
        /deep/ .el-input {
            margin-bottom: 20px;

            &, .el-input__inner {
                width: 300px;

                .el-input__suffix {
                    right: 8px;
                }

                .input-icon {
                    width: 14px;
                    left: -2px;
                }
            }
        }

        button {
            width: 240px;
            background-color: #216EC6;
            color: #fff;
        }
    }

    .complete {
        img {
            width: 60px;
            height: 60px;
            margin: 60px 0 30px;
        }

        div {
            font-size: 22px;
            color: #333;
        }
    }
}

/deep/ .el-input {
    &, .el-input__inner {
        height: 35px !important;
        border-radius: 0;

        .el-input__inner {
            border: 1px solid #CCC;
        }
    }
}

/deep/ .el-button.codeDialogBtn {
    width: 90px;
    line-height: 40px;
    font-size: 20px;
    height: 40px;
    border-radius: 0;
}

.verifyBox {
    width: 100%;

    /deep/ .el-input {
        width: 250px;
        height: 50px !important;
        border-radius: 0;

        .el-input__inner {
            width: 250px;
            height: 50px !important;
            border-radius: 0;
        }
    }

    img {
        width: 140px;
        height: 50px;
        border: 1px solid lightgray;
    }

    /deep/ .el-button {
        padding: 0 0;
        text-align: center;
        font-size: 16px;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 0;
        color: rgba(33, 110, 198, 1);
        background-color: #fff;
    }
}

</style>
