import service from '@/utils/request'

// eslint-disable-next-line no-unused-vars
const { httpPost, httpGet } = service

const getInBoundList = params => {
    return httpPost({
        url: '/materialMall/stock/inboundSettlement/listByEntity',
        params
    })
}
const saveInBound = params => {
    return httpPost({
        url: '/materialMall/stock/inboundSettlement/save',
        params
    })
}
const updateInBound = params => {
    return httpPost({
        url: '/materialMall/stock/inboundSettlement/update',
        params
    })
}
const auditInBound = params => {
    return httpGet({
        url: '/materialMall/stock/inboundSettlement/updateState',
        params
    })
}
const saveAndSubmitInBound = params => {
    return httpPost({
        url: '/materialMall/stock/inboundSettlement/saveAndSubmit',
        params
    })
}
const updateAndSubmitInBound = params => {
    return httpPost({
        url: '/materialMall/stock/inboundSettlement/updateAndSubmit',
        params
    })
}
const listInBoundExport = params => {
    return httpGet({
        url: '/materialMall/stock/inboundSettlement/export',
        params,
        responseType: 'blob',
    })
}

const getOutBoundList = params => {
    return httpPost({
        url: '/materialMall/stock/outboundSettlement/listByEntity',
        params
    })
}
const saveOutBound = params => {
    return httpPost({
        url: '/materialMall/stock/outboundSettlement/save',
        params
    })
}
const updateOutBound = params => {
    return httpPost({
        url: '/materialMall/stock/outboundSettlement/update',
        params
    })
}
const auditOutBound = params => {
    return httpGet({
        url: '/materialMall/stock/outboundSettlement/updateState',
        params
    })
}
const saveAndSubmitOutBound = params => {
    return httpPost({
        url: '/materialMall/stock/outboundSettlement/saveAndSubmit',
        params
    })
}
const updateAndSubmitOutBound = params => {
    return httpPost({
        url: '/materialMall/stock/outboundSettlement/updateAndSubmit',
        params
    })
}
const listOutBoundExport = params => {
    return httpGet({
        url: '/materialMall/stock/outboundSettlement/export',
        params,
        responseType: 'blob',
    })
}
export {
    getInBoundList,
    saveInBound,
    getOutBoundList,
    saveOutBound,
    saveAndSubmitInBound,
    updateAndSubmitInBound,
    saveAndSubmitOutBound,
    updateAndSubmitOutBound,
    updateInBound,
    updateOutBound,
    listInBoundExport,
    listOutBoundExport,
    auditOutBound,
    auditInBound
}