import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/neoUser/listByParameters',
        params
    })
}
const getRoleList = () => {
    return httpGet({
        url: '/materialMall/platform/neoUser/list',
    })
}

const createUser = params => {
    return httpPost({
        url: '/materialMall/platform/neoUser/create',
        params
    })
}

const updateUser = params => {
    return httpPost({
        url: '/materialMall/platform/neoUser/update',
        params
    })
}

const deleteById = params => {
    return httpGet({
        url: '/materialMall/platform/neoUser/delete',
        params
    })
}
const getUserById = params => {
    return httpGet({
        url: '/materialMall/platform/neoUser/findById',
        params
    })
}
const resetPassword = params => {
    return httpPost({
        url: '/materialMall/platform/neoUser/resetPassword',
        params
    })
}
export {
    getList,
    createUser,
    updateUser,
    deleteById,
    getUserById,
    getRoleList,
    resetPassword
}