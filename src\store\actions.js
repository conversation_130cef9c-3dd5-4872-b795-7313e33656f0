// 接口只能引入这个文件，在app.vue里导入过，导入第2个js文件找不到，不清楚原因
import authRequest from '@/api/identity/auth.js'
// eslint-disable-next-line no-unused-vars
import { showLoading, hideLoading, getUrlParams, showErrorPop } from '@/utils/common'

/**
 * 设置token
 * 登录流程
 * @param ctx Vuex上下文对象
 * @param res 响应结果对象
 * @returns Promise对象，用于异步操作
 */
const setToken = (ctx, res) => {
    // 判断用户权限
    let { isExternal, isInterior, isPcwp, isSupplier, shopId } = res
    ctx.commit('setAuthorities', {
        hasShopId: shopId !== '' || shopId !== null,
        isExternalSupplier: isExternal === 1 && isSupplier === 1 && isPcwp === 1,
        isInternalSupplier: isInterior === 1 && isSupplier === 1,
    })
    // eslint-disable-next-line no-unused-vars
    return new Promise((resolve, reject) => {
        if (res.token) {
            localStorage.setItem('token', res.token)
            localStorage.setItem('lastTime', new Date().getTime())
            ctx.commit('setUserInfo', {
                ...res
            })
            resolve()
        }
    })
}

let actions = {

    /**
     * 获取token并存入localStorage
     *
     * @param ctx 上下文对象
     * @param userInfo 用户信息对象，包含账号和密码
     * @returns 返回Promise对象，resolve时传入请求结果，reject时传入错误信息
     */
    getToken (ctx, userInfo) {
        // 获取token存入localStorage
        // if (localStorage.getItem('userId')) return
        showLoading('')
        this.msg = ''
        return new Promise((resolve, reject) => {
            authRequest
                .identityAuthSignin({
                    account: userInfo.account,
                    password: userInfo.password
                })
                .then(res => {
                    resolve(res)
                    setToken(ctx, res)
                })
                .catch(err=>{
                    reject(err)
                })
                .finally(() => {
                    hideLoading()
                    this.msg = ''
                })
        })
    },

    /**
     * 获取token并存入localStorage
     *
     * @param ctx 上下文对象
     * @param authCode 授权码对象，包含phone和code属性
     * @returns 返回Promise对象，resolve时传入登录成功后的响应数据，reject时传入错误对象
     */
    getToken2 (ctx, authCode) {
        // if (localStorage.getItem('userId')) return
        showLoading('')
        this.msg = ''
        return new Promise((resolve, reject) => {
            authRequest
                .phoneLogin({
                    phone: authCode.phone,
                    code: authCode.code
                })
                .then(res => {
                    resolve(res)
                    setToken(ctx, res)
                })
                .catch(err=>{
                    reject(err)
                })
                .finally(() => {
                    hideLoading()
                    this.msg = ''
                })
        })
    },

    /**
     * 验证Token有效性并刷新Token
     *
     * @param ctx 上下文对象
     * @param token 待验证的Token
     * @returns Promise对象，表示异步操作的结果
     */
    volideToken (ctx, token) {
        if (!token) return
        showLoading('')
        return  authRequest
            .refreshToken({
                token
            })
            .then(async res => {
                setToken(ctx, res)
            })
            .finally(() => {
                hideLoading()
            })
    },

    /**
     * 异步获取用户所属机构信息
     *
     * @param ctx Vuex上下文对象
     * @returns 返回Promise对象，解析成功时执行resolve，失败时执行reject
     */
    async userIdGetOrgInfo (ctx) {
        return new Promise((resolve, reject) => {
            //获取机构信息存入vuex
            const orgId = localStorage.getItem('orgId')
            const userId = localStorage.getItem('userId')
            if (ctx.state.userInfo.orgInfo && Object.keys(ctx.state.userInfo.orgInfo).length !== 0 && (orgId === ctx.state.userInfo.orgInfo.orgId))
                resolve()
            if (!userId) resolve()
            //如果orgId不存在默认取用户第一个机构，如果存在则取当前机构
            authRequest
                .hrOrgGetOrgByUserId({
                    userId
                }).then(res => {
                    // res = [
                    //     {
                    //         'orgId': 'ab7324a9939b-b3ec-ab4e-2932-b69a36a6',
                    //         'orgName': '四川路桥建设集团交通工程有限公司成都至宜宾高速公路设计施工总承包ZCB3、ZCB4标段绿化工程项目经理部',
                    //         'shortCode': 'JTGCCYLHXM',
                    //         'parentId': 'a94220dc350b-b749-9643-a42f-d7303e27',
                    //         'parentName': '四川路桥建设集团交通工程有限公司',
                    //         'orgType': 5
                    //     }
                    // ]
                    if(res.length === 0) {
                        // showErrorPop('没有包含机构信息')
                        reject()
                    }else{
                        let orgInfo = []
                        if(orgId) {
                            orgInfo = res.find(x=>x.orgId === orgId)
                        }else{
                            orgInfo = res[0]
                        }
                        localStorage.setItem('orgId', orgInfo.orgId)
                        ctx.commit('setUserInfo', {
                            ...ctx.state.userInfo,
                            orgInfo: orgInfo
                        })
                        resolve()
                        // authRequest.getOrgById({
                        //     orgId: orgInfo.orgId
                        // }).then(val=>{
                        //     localStorage.setItem('orgId', val.orgId)
                        //     ctx.commit('setUserInfo', {
                        //         ...ctx.state.userInfo,
                        //         orgInfo: val
                        //     })
                        //     resolve()
                        // }).catch(()=>{
                        //     resolve()
                        // })
                    }
                }).catch(()=>{
                    reject()
                })
        })

    },
    // // 获取机构设置的本位币
    // getBaseCyByOrgId (ctx) {
    //     if (!ctx.state.userInfo?.orgInfo?.orgId) return
    //     // if (Object.keys(ctx.state.userInfo?.baseCyByOrgId).length !== 0) return
    //     authRequest.getBaseCyByOrgId({ orgId: ctx.state.userInfo.orgInfo.orgId }).then(res => {
    //         ctx.commit('setBaseCyByOrgId', res)
    //     })
    // },
    //获取菜单id
    getMenuId (ctx) {
        const menuId = getUrlParams('menuId')
        if(menuId) {
            ctx.commit('setmenuId', menuId)
        }
    },
    // 修改管理后台面包屑路径
    changeSteps (ctx, steps) {
        ctx.commit('setSteps', steps)
    },
    setToken
}

export default actions
