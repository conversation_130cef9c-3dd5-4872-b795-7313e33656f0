import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/systemParam/listByEntitySys',
        params
    })
}

const getListSys = params => {
    return httpPost({
        url: '/materialMall/platform/systemParam/listByEntitySys',
        params
    })
}

//更新已读状态
const update = params => {
    return httpPost({
        url: '/materialMall/platform/systemParam/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/systemParam/create',
        params
    })
}

const createSysParam = params => {
    return httpPost({
        url: '/materialMall/platform/systemParam/createSysParam',
        params
    })
}

const updateByBatch = params => {
    return httpPost({
        url: '/materialMall/platform/systemParam/updateByBatch',
        params
    })
}

const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/systemParam/deleteBatch',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/systemParam/delete',
        params
    })
}

const getListSystemInitVos = params => {
    return httpGet({
        url: '/materialMall/platform/systemParam/getListSystemInitVos',
        params
    })
}
const getParamsByCode = params => {
    return httpGet({
        url: '/materialMall/w/systemParam/listByCode',
        params
    })
}
export {
    getList,
    update,
    create,
    createSysParam,
    updateByBatch,
    batchDelete,
    del,
    getListSys,
    getListSystemInitVos,
    getParamsByCode
}