<template>
    <main>
        <div class="msgBox center">
            <div class="list-title dfa">对账单 <div class="add-addr dfa pointer" v-show="!viewShow" @click="handleNew">+ 新增对账单</div> </div>
            <div v-show="viewShow!==true" class="tableBox">
                <el-table
                    ref="msgTable"
                    :data="sheetList"
                    :header-row-style="{ fontSize: '16px', color: '#216EC6', fontWeight: '500' }"
                    :row-style="{ fontSize: '14px', color: '#666666', height: '48px' }"
                    :cell-style="{ textAlign: 'center' }"
                >
                    <el-table-column type="index" label="序号" width="100"/>
                    <el-table-column label="操作" width="100">
                        <template v-slot="scope">
                            <div class="action">
                                <span @click="handleView(scope)">查看详情</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="supplier" label="供应商名称" width=""/>
                    <el-table-column prop="formats" label="格式" width="190"/>
                    <el-table-column prop="startTime" label="开始时间" width="190"/>
                    <el-table-column prop="endTime" label="结束时间" width="190"/>
                    <el-table-column prop="reconciliationTime" label="对账时间" width="190"/>
                </el-table>
                <div class="pager dfb">
<!--                    <div class="batchAction dfa">
                        <el-checkbox v-model="checkAll" label="全选" :indeterminate="false" @change="toggleSelectAll"></el-checkbox>
                        <div class="btn del" @click="deleteSelected">删除选中</div>
                    </div>-->
                    <pagination
                        class="center"
                        :total="page.totalCount"
                        :currentPage="page.currPage"
                        :pageSize="page.pageSize"
                        :totalPage="page.totalPage"
                        :destination="page.destination"
                        :pagerSize="5"
                        @currentChange="currentChange"
                        @sizeChange="sizeChange"
                    ></pagination>
                </div>
            </div>
            <!-- 对账单详情 -->
            <div class="right" v-show="viewShow===true">
                <div class="e-form" style="padding: 0 10px 10px;">
                    <div class="tabs-title">对账单详情</div>
                    <el-form ref="formEdit" :model="formData" label-width="150px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="供应商名称：" prop="supplier">
                                    <span>{{ formData.supplier }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="状态：" prop="status">
                                    <span>{{ formData.status ? '启用' : '关闭' }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="开始时间：" prop="startTime">
                                    <span>{{ formData.startTime }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="结束时间：" prop="endTime">
                                    <span>{{ formData.endTime }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="对账时间：" prop="reconciliationTime">
                                    <span>{{ formData.reconciliationTime }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div class="p20 e-form"  style="overflow: auto;"  >
                    <div class="tabs-title mb10">物资清单</div>
                    <el-table border :data="splitList" :header-cell-style="{ background: '#f7f7f7' }" v-loading="fileLoading">
                        <el-table-column type="index" label="序号"></el-table-column>
                        <el-table-column
                            v-for="column in tableColumns"
                            :key="column.prop"
                            :prop="column.prop"
                            :label="column.label"
                            :width="column.width"
                        >
                            <template v-slot="scope">
                                <template v-if="column.template.hasInput">
                                    <el-input
                                        v-model="scope.row[column.prop]"
                                        :type="column.template.type"
                                        :min="column.template.type === 'number' ? column.template.min : null"
                                        :placeholder="column.template.placeholder"
                                    />
                                </template>
                                <template v-else>
                                    {{ scope.row[column.prop] }}
                                </template>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="90">
                            <template  v-slot="scope">
                                <div class="action">
                                    <span @click="handleSplit()">拆单</span>
                                    <span @click="handleDelSplit(scope.row.rowId)">删除</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="dfc">
                    <div class="theBtn pointer" style="margin-right: 10px" @click="updateSheet()">确定</div>
                    <div class="theBtn pointer" @click="viewShow=false">返回</div>
                </div>
            </div>
        </div>
        <!-- 新增对账单 -->
        <el-dialog class="front" v-dialogDrag width="60%" :visible.sync="dialogVisible" top="8vh">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>新增对账单</div>
                    </div>
                    <div class="dialog-close" @click="dialogVisible = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <div class="dialog-body center">
                <el-form :model="generatedData" ref="form" :rules="rules" label-width="110px" size="medium">
                    <el-form-item label="供应商名称：" prop="supplier" style="width: 70%;">
                        <el-input v-model="generatedData.supplier" placeholder="请输入供应商名称" />
                    </el-form-item>
                    <el-form-item label="持续时间：" prop="timeSpan">
                        <el-date-picker
                            v-model="generatedData.timeSpan"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            align="left"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="格式：" prop="formats" style="width: 40%;">
                        <!--<el-input v-model="generatedData.content" type="textarea" resize="none" placeholder="请输入内容" />-->
                        <el-select v-model="generatedData.formats" placeholder="请选择对账单格式">
                            <el-option
                                v-for="item in formatsOptions"
                                :key="item.label"
                                :label="item.label"
                                :value="item.label">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div class="df"><el-button @click="handleCreateSheet">确 认</el-button></div>
            </div>
        </el-dialog>
    </main>
</template>

<script>
import pagination from '../../components/pagination.vue'
import { getUuid, stripHtmlTags } from '@/utils/common'
export default {
    components: { pagination },
    data () {
        return {
            dialogVisible: false,
            fileLoading: false,
            splitList: [],
            checkAll: false,
            page: {
                totalPage: 0,
                totalCount: 0,
                currPage: 1,
                pageSize: 10,
                destination: 2
            },
            formData: {},
            formatsOptions: [
                { label: '浮动价格' },
                { label: '固定价格' },
            ],
            generatedData: {
                supplier: '',
                formats: '',
                timeSpan: '',
            },
            rules: {
                supplier: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
                timeSpan: [{ required: true, message: '请输入持续时间', trigger: 'blur' }],
                formats: [{ required: true, message: '请输入对账单格式', trigger: 'change' }],
            },
            sheetList: [{
                sheetNum: '123154534',
                formats: '浮动价格',
                status: 1,
                startTime: '2023-05-05 12:00:00',
                endTime: '2023-05-05 12:00:00',
                reconciliationTime: '2023-06-06 12:00:00',
                supplier: '四川路桥集团有限公司物资分公司',
                materialName: '挖掘机',
                model: 'P900',
                unit: '台',
                count: 12, // 数量
                onlinePrice: 1000, // 到场网价
                fixedPrice: 500, // 固定费用
                priceWithTax: 1500, // 到场含税单价
                totalAmountWithTax: 18000, // 含税金额
                content: '备注内容'
            }],
            viewShow: false,
        }
    },
    watch: {
        dialogVisible (newVal, oldVal) {
            this.generatedData = { supplier: '', status: null, timeSpan: '', }
            if(oldVal) this.$refs['form'].clearValidate()
        },
        newSplitList: {
            handler (newVal, oldVal) {
                if(oldVal.length === 0 || newVal.length !== oldVal.length) return
                for(let i = 0; i < newVal.length; i++) {
                    let newObj = newVal[i]
                    let oldObj = oldVal[i]
                    if(JSON.stringify(newObj) === JSON.stringify(oldObj)) continue
                    for(let key in newObj) { // 循环已修改对象的属性
                        if(newObj[key] === oldObj[key]) continue
                        if(newObj[key] === '') return this.splitList[i][key] = 0 // 如果某个属性为空，则设为0

                        let { priceWithTax, onlinePrice, fixedPrice, count } = this.digitizeValue(newObj)
                        let isOpZero = onlinePrice === 0 && fixedPrice > 0 && priceWithTax > fixedPrice
                        let isFpZero = fixedPrice === 0 && onlinePrice > 0 && priceWithTax > onlinePrice

                        // toFixed用于对js计算产生的精度误差进行修正，this.spliceFloat用于将用户输入的超过2位小数的值进行向下舍去
                        switch (key) { // 根据发生改变的对象属性对关联的值重新赋值
                        // 修改本期数量
                        case 'count':
                            this.splitList[i].totalAmountWithTax = (count * priceWithTax).toFixed(2)
                            break
                        // 修改到场含税单价
                        case 'priceWithTax':
                            this.splitList[i].totalAmountWithTax = (count * priceWithTax).toFixed(2)
                            this.splitList.forEach(item => item.priceWithTax = this.spliceFloat(priceWithTax))
                            this.$nextTick(() => {
                                if(isOpZero) {
                                    this.$set(this.splitList[i], 'onlinePrice', (priceWithTax - fixedPrice).toFixed(2))
                                }
                                if(isFpZero) {
                                    this.$set(this.splitList[i], 'fixedPrice', (priceWithTax - onlinePrice).toFixed(2))
                                }
                            })
                            break
                        // 修改到货网价和固定费用
                        case 'onlinePrice':
                        case 'fixedPrice':
                            this.splitList.forEach(item => {
                                item.onlinePrice = this.spliceFloat(onlinePrice)
                                item.fixedPrice = this.spliceFloat(fixedPrice)
                                item.priceWithTax = (onlinePrice + fixedPrice).toFixed(2)
                            })
                            break
                        default:
                            break
                        }
                    }
                }
            },
            deep: true
        }
    },
    computed: {
        // 结合computed实现拆单列表的监听，否则watch中无法拿到更改前列表的值
        newSplitList () {
            let list = JSON.parse(JSON.stringify(this.splitList))
            list.forEach(item => {
                for(let key in item) {
                    if(key === 'status') return
                    if(typeof item[key] === 'number') item[key] = String(item[key]) // 数字转换成字符串类型
                }
            })
            return list
        },
        tableColumns () {
            let columns = [
                { prop: 'sheetNum', label: '收料单号', width: '', template: {}, },
                { prop: 'materialName', label: '物资名称', width: '', template: {}, },
                { prop: 'model', label: '规格型号', width: '70', template: {}, },
                { prop: 'unit', label: '单位', width: '60', template: {}, },
                { prop: 'count', label: '本期数量', width: '', template: { hasInput: true, type: 'number', placeholder: '请输入数量' }, },
                { prop: 'onlinePrice', label: '到货网价', width: '', template: { hasInput: true, type: 'number', placeholder: '请输入价格' }, },
                { prop: 'fixedPrice', label: '固定费用', width: '', template: { hasInput: true, type: 'number', placeholder: '请输入价格' }, },
                { prop: 'priceWithTax', label: '到场含税单价（元）', width: '', template: { hasInput: true, type: 'number', placeholder: '请输入价格' }, },
                { prop: 'totalAmountWithTax', label: '含税金额（元）', width: '', template: {}, },
                { prop: 'content', label: '备注', width: '140', template: { hasInput: true, type: 'text', placeholder: '请输入备注' }, },
            ]
            if(this.formData.status !== 1) columns.splice(5, 2)
            return columns
        },
    },
    created () {
    },
    methods: {
        stripHtmlTags,
        handleNew () {
            this.dialogVisible = true
        },
        handleCreateSheet () {
            this.$refs['form'].validate(valid => {
                if(valid) return this.dialogVisible = false
                return false
            })
        },
        // 数字化参数
        digitizeValue (obj) {
            obj.count = parseInt(obj.count)
            obj.priceWithTax = parseFloat(obj.priceWithTax)
            obj.fixedPrice = parseFloat(obj.fixedPrice)
            obj.onlinePrice = parseFloat(obj.onlinePrice)
            return obj
        },
        // 数字转为小数位数为2的字符串
        spliceFloat (number) {
            const numberString = number.toString()
            const decimalIndex = numberString.indexOf('.')

            if (decimalIndex !== -1) {
                return numberString.substring(0, decimalIndex + 3)
            }

            return number.toString()
        },
        // 新增拆单
        handleSplit () {
            if(this.splitList.length === parseInt(this.formData.count)) return
            this.splitList.push({
                ...this.formData,
                rowId: getUuid(),
                count: 0,
                totalAmountWithTax: 0,
            })
        },
        // 删除本行的拆单
        handleDelSplit (id) {
            if(this.splitList.length === 1) return this.$message({ message: '不可删除最后一条拆单', type: 'error' })
            this.splitList.forEach((item, i, arr) => {
                if(item.rowId === id) arr.splice(i, 1)
            })
        },
        handleView ({ row }) {
            this.formData = row
            this.splitList = [{ rowId: getUuid(), ...row }]
            this.viewShow = true
        },
        handleDownload () {},
        // 确定按钮，校验数据
        updateSheet () {
            let isPriceError = false
            let hasZeroCount = false
            let totalCount = this.splitList.reduce((total, item) => total + parseInt(item.count), 0) // 获取所有拆单已填的数量

            this.splitList.forEach(item => {
                let { onlinePrice, fixedPrice, priceWithTax, count } = this.digitizeValue(item)
                if(count === 0) hasZeroCount = true // 判断是否有拆单数量为0
                if(onlinePrice + fixedPrice !== priceWithTax) isPriceError = true // 判断网价和固定价格的和和含税单价是否相等
            })

            if(hasZeroCount) return this.$message({ message: '数量不能为0', type: 'error' })
            if(totalCount < parseInt(this.formData.count)) return this.$message({ message: '总数量不足', type: 'error' })
            if(totalCount > parseInt(this.formData.count)) return this.$message({ message: '总数量大于最大数量', type: 'error' })
            if(isPriceError) return this.$message({ message: '到货网价加固定费用之和与到场含税单价不符', type: 'error' })
            this.$message({ message: '校验成功', type: 'success' })
        },
        // eslint-disable-next-line
        validateStatus (rule, value, callback) {
            if(value !== 1 || value !== 2) return callback(new Error('请选择对账单格式'))
            callback()
        },
        toggleSelectAll () {},
        deleteSelected () {},
        currentChange (currPage) {
            this.page.currentPage = currPage
        },
        sizeChange (pageSize) {
            this.page.pageSize = pageSize
        },
    },
}
</script>

<style scoped lang="scss">
main {
    padding: 30px;
    background-color: #f5f5f5;
}

.msgBox {
    //height: 712px;
    width: 1326px;
    border: 1px solid #e6e6e6;
    background-color: #fff;
}

.theBtn2 {
    width: 120px;
    height: 40px;
    margin: 20px auto 20px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    background-color: #93b7e0;
}

.theBtn {
    width: 120px;
    height: 40px;
    margin: 10px 0 20px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    background-color: #216EC6;
}

.list-title {
    height: 50px;
    padding: 15px 0 15px 20px;
    font-size: 18px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
.add-addr {
    width: 120px;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(33, 110, 198, 1);
    // border: 1px solid rgba(33, 110, 198, 1);
    justify-content: center;
    position: absolute;
    top: 10px;
    right: 19px;
    user-select: none;

    &:active {
        color: #fff;
        background-color: rgba(33, 110, 198, 1);
    }
}
/deep/.el-form-item {
    height: unset;
    margin-bottom: 0;
    .el-form-item__content {
        height: unset !important;
    }
}
/deep/.el-table {
    thead tr th .cell {
        text-align: center;
    }
    .el-table__row {
        td .cell {
            display: block;
            text-align: center;
        }
    }
}
.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
.tableBox {
    padding: 22px 30px 0;

    .taberHeader {
        font-size: 16px;
        color: #216EC6;
    }

    /deep/ .el-table {
        .el-table__body-wrapper {
            min-height: 600px;
        }
        .el-table__row {
            td .cell {
                display: block;
                text-align: center;
            }
        }
        .el-table__body .cell {
            display: flex;
            align-items: center;

            img {
                width: 13px;
                height: 12px;
                margin-right: 5px;
            }
        }

        thead {
            .el-checkbox {
                display: none;
            }
        }
    }

    .batchAction {
        padding-left: 20px;

        /deep/ .el-checkbox {
            margin-right: 20px;
        }

        .btn {
            width: 80px;
            height: 30px;
            text-align: center;
            font-size: 14px;
            line-height: 30px;
            cursor: pointer;
            user-select: none;
        }

        .del {
            margin-right: 15px;
            color: #fff;
            background-color: #216EC6;
        }

        .mark {
            color: #216EC6;
            border: 1px solid #216EC6;
        }
    }
}
/deep/ .dialog-body {
    padding: 40px 40px 20px;
    //.el-textarea, .el-textarea__inner { height: 300px; }
    .el-input, .el-input__inner, .el-textarea, .el-textarea__inner { border-radius: 0; }
    .el-form-item { margin-bottom: 20px; }
    .df { justify-content: flex-end; }
    .el-button { border-radius: 0; height: 40px; line-height: 40px; padding: 0 25px; color: #fff; background-color: #216ec6; }
}
</style>