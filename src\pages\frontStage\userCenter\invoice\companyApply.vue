<template>
  <main>
    <div class="list-title dfa mb20">我的发票</div>
    <div class="box dfa">
      <!--  温馨提示  -->
      <div class="remind p20">
        <div class="dfa mb20">
          <img src="@/assets/images/userCenter/ico_notice.png" alt="">
          <span>温馨提示</span>
        </div>
        <div class="line">1、订单完成90天内，可支持向第三方卖家申请开具增值税普通发票</div>
        <div class="line">2、申请开具发票内容默认为商品明细，如需开具商品大类可通知第三方卖家备注</div>
        <div class="line">3、发票申请提交后如有内容更新，可及时联系第三方卖家进行变更</div>
        <div class="line">4、暂不支持发票换开申请</div>
      </div>
      <!--  表单  -->
      <div class="apply-form">
        <el-form ref="form" :model="formData" :rules="rules" label-position="top" :inline="false">
          <div class="row">
            <!--  发票类型  -->
            <div class="col">
              <el-form-item label="发票类型：" prop="invoiceType">
                <el-select v-model="formData.invoiceType" placeholder="请选择发票类型" @change="changRiseType(formData.invoiceType)">
                  <el-option v-for="item in typeOptions" :value="item.value"
                             :label="item.label" :key="item.value" ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <!--  发票内容  -->

            <div class="col">
              <el-form-item label="抬头类型：" prop="riseType" v-if="formData.invoiceType==0">
                <el-input  @click="formData.riseType==1" placeholder="单位" :disabled="true"></el-input>
              </el-form-item>
              <el-form-item v-else label="抬头类型：" prop="riseType">
                <el-select   v-model="formData.riseType" placeholder="请选择抬头类型">
                  <el-option v-for="item in headerOptions" :value="item.value"
                             :label="item.label" :key="item.value" ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="row">
            <!--  发票抬头  -->
            <div class="col">
              <el-form-item label="发票抬头：" prop="invoiceTitle">
                <el-input  v-model="formData.invoiceTitle" placeholder="请输入发票抬头" :rules="formData.riseType==1? rules.invoiceTitle:[{
                                  required:true,
                                  message:'请输入发票抬头'
                                }] "  clearable></el-input>
              </el-form-item>
            </div>
            <div class="col">
              <el-form-item label="注册地址：" prop="registerAddress"
                            :rules="formData.invoiceType==1? rules.registerAddress:[{
                                  required:true,
                                  message:'请输入注册地址'
                                }]">
                <el-input v-model="formData.registerAddress" placeholder="请输入注册地址"></el-input>
              </el-form-item>
            </div>
            <!--  单位名称  -->
          </div>
          <div  class="row" v-show="formData.riseType==1">
            <div  class="col">
              <el-form-item label="单位名称：" prop="company"
                            :rules="formData.riseType==1? rules.company:[{
                                  required:false,
                                  message:'请输入单位名称'
                                }]">
                <el-input v-model="formData.company" placeholder="请输入单位名称"></el-input>
              </el-form-item>
            </div>
            <!--  单位名称  -->
            <div  class="col">
              <el-form-item label="单位税号：" prop="dutyParagraph"
                            :rules="formData.riseType==1? rules.dutyParagraph:[{
                                  required:false,
                                  message:'请输入单位税号'
                                }]">
                <el-input v-model="formData.dutyParagraph" placeholder="请输入单位税号"></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="row">
            <!--  注册电话  -->
            <div class="col">
              <el-form-item label="注册电话：" prop="registerPhone"
                            :rules="formData.invoiceType==0? rules.registerPhone:[{
                                  required:false,
                                  message:'请输入注册电话'
                                }]">
                <el-input v-model="formData.registerPhone" placeholder="请输入注册电话"
                ></el-input>
              </el-form-item>
            </div>
            <!--  开户银行  -->
            <div class="col">
              <el-form-item label="开户银行：" prop="bank"  :rules="formData.invoiceType==0? rules.bank:[{
                                  required:false,
                                  message:'请输入开户银行'
                                }]">
                <el-input v-model="formData.bank" placeholder="请输入开户银行"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="row">
            <!--  银行账号  -->
            <div class="col">
              <el-form-item label="银行账号：" prop="bankAccount"
                            :rules="formData.invoiceType==0? rules.bankAccount:[{
                                  required:false,
                                  message:'请输入银行账号'
                                }]">
                <el-input v-model="formData.bankAccount" placeholder="请输入银行账号"
                ></el-input>
              </el-form-item>
            </div>
            <!--  收票人姓名  -->
            <div class="col">
              <el-form-item label="收票人姓名：" prop="userName">
                <el-input v-model="formData.userName" placeholder="请输入收票人姓名"></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="row">
            <!--  收票人手机号  -->
            <div class="col">
              <el-form-item label="收票人手机号：" prop="userPhone">
                <el-input v-model="formData.userPhone" placeholder="请输入收票人手机号"></el-input>
              </el-form-item>
            </div>
            <!--  收票人地区  -->
            <div class="col">
              <el-form-item class="address" label="收票人地区：" prop="userAddress">
                <div class="df">
                  <el-select ref="selectLabel1" class="province" v-model="formData.province"
                             placeholder="省份" @change="(code) => getSubDistrict(code, 1)">
                    <el-option v-for="item in addressOptions.province" :key="item.value"
                               :label="item.districtName" :value="item.districtCode">
                    </el-option>
                  </el-select>
                  <el-select ref="selectLabel2" class="city" v-model="formData.city" placeholder="市级"
                             @change="(code) => getSubDistrict(code, 2)">
                    <el-option v-for="item in addressOptions.city" :key="item.value"
                               :label="item.districtName" :value="item.districtCode">
                    </el-option>
                  </el-select>
                  <el-select ref="selectLabel3" class="district" v-model="formData.district"
                             placeholder="区、县">
                    <el-option v-for="item in addressOptions.district" :key="item.value"
                               :label="item.districtName" :value="item.districtCode">
                    </el-option>
                  </el-select>
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <el-form-item label="发票内容：" prop="remarks">
                <el-input v-model="formData.remarks" placeholder="请输入发票内容"></el-input>
              </el-form-item>
            </div>
            <div class="col">
              <el-form-item label="发票邮箱：" prop="email">
                <el-input v-model="formData.email" placeholder="请输入发票邮箱"></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="row">
            <!--  详细地址  -->
            <div class="col">
              <el-form-item label="详细地址：" prop="detailAddr">
                <el-input type="textarea" :auto-resize="false" v-model="formData.detailAddr"
                          placeholder="请输入详细地址"></el-input>
              </el-form-item>
            </div></div>
        </el-form>
      </div>
      <div class="btns df">
        <div @click="$router.go(-1)">返回</div>
        <div @click="handleSubmit">提交</div>
      </div>
    </div>
  </main>
</template>

<script>
import { getCascaderOptions } from '@/api/platform/common/components'
import  { addDate } from '@/api/frontStage/invoice'
export default {
    name: 'apply',
    data () {
        return {
            // 校验规则
            rules: {
                invoiceType: { required: true, message: '请选择发票类型', trigger: 'blur' },
                riseType: { required: true, message: '请选择抬头类型', trigger: 'blur' },
                company: { required: true, message: '请选择单位名称', trigger: 'blur' },
                dutyParagraph: {  required: true, message: '请选择单位税号', trigger: 'blur' },
                registerAddress: { validator: true, message: '请选择注册地址', trigger: 'blur' },
                registerPhone: { required: true, min: 11, max: 11, message: '请输入正确的注册电话', trigger: 'blur' },
                bank: { required: true, message: '请选择开户银行', trigger: 'blur' },
                bankAccount: { required: true, message: '请选择银行账号', trigger: 'blur' },
            },

            // 表单数据
            formData: {
                invoiceTitle: '',
                productId: '',
                orderItemId: '',
                orderId: '',
                remarks: '',
                invoiceType: 0,
                invoiceNo: '',
                riseType: 1,
                company: '',
                dutyParagraph: '',
                registerAddress: '',
                registerPhone: '',
                bank: '',
                bankAccount: '',
                userName: '',
                userPhone: '',
                userAddress: '',
                detailAddr: '',
                province: '',
                city: '',
                district: '',
                orderSn: '',
                invoiceState: 0
            },
            typeOptions: [
                { label: '增值税专用发票', value: 0 },
                { label: '普通发票', value: 1 },
            ],
            headerOptions: [
                { label: '单位', value: 1 },
                { label: '个人', value: 2 },
            ],
            order: '',
            addressOptions: {
                province: [],
                city: [],
                district: []
            },
        }
    },
    computed: {
    },
    created () {
        this.reconciliationIds = this.$route.params.reconciliationIds
        console.log(this.reconciliationIds)

        this.getAddressPickerOptions()
    },
    mounted () {
    },
    methods: {
        changRiseType (state) {
            if (state == 0) {
                this.formData.riseType = 1
            }
        },
        // 提交申请
        handleSubmit () {
            this.formData.userAddress = this.$refs.selectLabel1.selectedLabel + this.$refs.selectLabel2.selectedLabel + this.$refs.selectLabel3.selectedLabel

            this.$refs['form'].validate(valid => {
                console.log(valid)
                if (!valid) return
                addDate(this.formData).then(res=>{
                    if (res.code == 200) {
                        this.$message({
                            message: '申请成功',
                            type: 'success'
                        })
                        this.$router.go(-1)
                    }
                })
            })
        },
        // 获取地址选择器数据
        async getAddressPickerOptions () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.addressOptions.province = res
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            this.formData.district = ''
            if (layer === 1) {
                this.formData.city = ''
            }
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1) {
                    return this.addressOptions.city = res
                }
                this.addressOptions.district = res
            })
        },
    }
}
</script>

<style lang="scss" scoped>
main > div {
  border: 1px solid rgba(229, 229, 229, 1);
}

.list-title {
  height: 50px;
  padding: 15px 19px 15px 21px;
  font-size: 20px;
  line-height: 20px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);
  position: relative;

  &::before {
    width: 3px;
    height: 20px;
    margin-right: 10px;
    content: '';
    display: block;
    background-color: rgba(33, 110, 198, 1);
  }
}

.box {
  padding: 30px;
  flex-direction: column;

  .remind, .apply-form {
    width: 630px;
  }

  // 温馨提示
  .remind {
    height: 165px;
    margin-bottom: 30px;
    font-size: 12px;
    background: rgba(255, 253, 238, 0.5);

    .dfa {
      color: #333;

      img {
        width: 12px;
        height: 12px;
        margin-right: 5px;
      }
    }

    .line {
      margin-bottom: 15px;
      color: #999;
    }
  }

  // 表单
  .apply-form {

  }
}

.row {
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;

  .col {
    width: 300px;
  }
}

/deep/ .el-form {
  .el-form-item {
    margin-bottom: 0;
  }

  .el-form-item__label {
    padding-bottom: 0;
    color: #999;
  }

  .el-input, .el-input__inner {
    width: 300px;
    height: 35px;
  }

    .el-input__inner {
        border-radius: 0;
        border: 1px solid rgba(217, 217, 217, 1);
    }

    .address {
        .el-input, .el-input__inner {
            width: 80px;
        }

        .el-select {
            width: 80px !important;
            margin-right: 10px;

            &:last-of-type {
                .el-input, .el-input__inner {
                    width: 120px;
                }

                width: 120px !important;
                margin-right: 0;
            }
        }
    }

    .row:last-of-type {
        margin-bottom: 40px;

        .el-input, .el-input__inner {
            height: 60px;
        }

        .el-textarea__inner {
            height: 70px !important;
            padding: 11px 10px;
            border-radius: 0;
            resize: none;
        }
    }
}

.btns {
    width: 190px;

    div {
        width: 80px;
        height: 40px;
        font-size: 16px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
        user-select: none;

        &:first-of-type {
            margin-right: 30px;
            border: 1px solid #216EC6;
            color: #216EC6;
        }

        &:last-of-type {
            color: #fff;
            background-color: #216EC6;
        }
    }
}
</style>