import service from '@/utils/request'

const { httpPost, httpGet } = service

const getPublishList = params => {
    return httpPost({
        url: '/materialMall/platform/adPicture/publish/findByConditionByPage',
        params
    })
}

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/adPicture/findByConditionByPage',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/platform/adPicture/create',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/adPicture/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/adPicture/delete',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/adPicture/updateByPublish',
        params
    })
}
//das
const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/adPicture/updateNotPublish',
        params
    })
}
//
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/adPicture/deleteBatch',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/platform/adPicture/updateBatchById',
        params
    })
}

export {
    getList,
    edit,
    getPublishList,
    create,
    del,
    batchDelete,
    batchPublish,
    batchNotPublish,
    changeSortValue
}
