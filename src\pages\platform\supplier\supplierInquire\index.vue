<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <!--                        <el-button   type="primary" @click="handleSd">检测蜀道企业</el-button>-->
                        <el-button v-show="this.showDevFunc" type="primary" @click="openUpdate">配置信用额度</el-button>
                        <el-button v-show="this.showDevFunc" class="btn-greenYellow" type="primary"
                                   @click="batchSubmitCheck">批量提交
                        </el-button>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按创建时间排序</el-radio>
                        <el-input type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table">
                <el-table class="table"
                          v-loading="tableLoading"
                          :height="rightTableHeight"
                          @selection-change="handleSelectionChange"
                          :data="tableData" border>
                    <el-table-column type="selection" width="40"></el-table-column>
                    <!--                    <el-table-column label="序号" type="index" width="60"></el-table-column>-->
                    <el-table-column v-if="showDevFunc" label="信用额度(元)" width="120"
                                     prop="arrearage">
                        <!--                        <template v-slot="scope">-->
                        <!--                            <el-input type="number" v-model="scope.row.arrearage" @change="getChangedRow(scope.row)"></el-input>-->
                        <!--                        </template>-->
                    </el-table-column>
                    <el-table-column v-if="showDevFunc" prop="arrearageDateNum" label="欠费过期时间时长"
                                     width="120">
                    </el-table-column>
                    <el-table-column v-if="showDevFunc" prop="arrearageDateType" label="欠费过期时间类型"
                                     width="120">
                        <template v-slot="scope">
                            <span v-show="scope.row.arrearageDateType==1">天</span>
                            <span v-show="scope.row.arrearageDateType==2">月</span>
                            <span v-show="scope.row.arrearageDateType==3">年</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="arrearageDateType" label="重置密码"
                                    width="120">
                        <template v-slot="scope">
                            <el-button v-if="!scope.row.interiorId" type="primary" @click="()=>resetPwd(scope.row.enterpriseId)">重置密码</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="蜀道企业">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.shuDaoFlag === 0">否</el-tag>
                            <el-tag v-else-if="scope.row.shuDaoFlag === 1" type="success">是</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业名称" width="260">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.enterpriseName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业类型" width="">
                        <template v-slot="scope">
                            <span v-if="scope.row.enterpriseType == '0'">个体户</span>
                            <span v-else-if="scope.row.enterpriseType == '1'">企业</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="注册资金(万元)" width="">
                        <template v-slot="scope">
                            {{ scope.row.registeredCapital }}
                        </template>
                    </el-table-column>
                    <el-table-column label="注册时间" width="160">
                        <template v-slot="scope">
                            {{ scope.row.creationTime }}
                        </template>
                    </el-table-column>
                    <el-table-column label="营业执照有效期" width="160">
                        <template v-slot="scope">
                            <span v-if="scope.row.licenseTerm != null">{{ scope.row.licenseTerm }}</span>
                            <span v-else-if="scope.row.licenseTerm == null">长期</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业所在城市" width="">
                        <template v-slot="scope">
                            {{ scope.row.city }}
                        </template>
                    </el-table-column>
                    <el-table-column label="企业注册地址" width="200">
                        <template v-slot="scope">
                            {{ scope.row.province }}{{ scope.row.county }}{{ scope.row.detailedAddress }}
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" width="160" prop="gmtCreate"/>
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData || tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="企业名称：" prop="enterpriseName">
                            <el-input v-model="filterData.enterpriseName" placeholder="请输入企业名称"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="企业所在城市：" prop="city">
                            <el-input v-model="filterData.city" placeholder="企业所在城市" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="蜀道企业：">
                            <!--                                <el-input clearable  v-model="filterData.status" placeholder="请输入店铺名称" style="width: 200px"></el-input>-->
                            <el-select v-model="filterData.shuDaoFlag" placeholder="请选择状态">
                                <el-option
                                    :key="999"
                                    label="全部"
                                    :value="999">
                                </el-option>
                                <el-option
                                    :key="1"
                                    label="是"
                                    :value="1">
                                </el-option>
                                <el-option
                                    :key="0"
                                    label="否"
                                    :value="0">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog class="updateVisible"
                   v-loading="addPlanLoading" v-dialogDrag top="5vh" width="80%"
                   title="配置信用额度"
                   :visible.sync="updateVisible"
        >
            <div class="dfa mb20">
                <span type="primary">配置信用额度</span>
            </div>
            <el-form
                ref="updateFormRote"
                label-width="180px"
                :model="updateForm"
                :data="updateForm"
                :rules="updateFormRote"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="可欠费金额（元）：" prop="arrearage">
                            <el-input type="number" clearable v-model="updateForm.arrearage"
                                      @change="getChangedRow(updateForm.arrearage)"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="arrearageDateNumShow">
                    <el-col :span="8">
                        <el-form-item label="可欠费过期时间时长：" prop="arrearageDateNum">
                            <el-input v-model="updateForm.arrearageDateNum" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="4">
                        <el-form-item label="可欠费过期时间类型：" prop="arrearageDateType">
                            <el-select v-model="updateForm.arrearageDateType">
                                <el-option label="天" :value="1"/>
                                <el-option label="月" :value="2"/>
                                <el-option label="年" :value="3"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-tabs>
                <el-tab-pane label="选择列表" class="df dlg">
                    <div class="box-left">
                        <div class="e-table">
                            <div class="top">
                                <div style="width: 200px" class="left">
                                    <el-input type="text" @blur="getupdateList" placeholder="输入搜索关键字"
                                              v-model="upkeywords">
                                        <img :src="require('@/assets/search.png')" slot="suffix"
                                             @click="getupdateList"/>
                                    </el-input>
                                    <span style="color: #ea083a">双击添加</span>
                                </div>
                            </div>
                            <el-table ref="selectContractOrPlanR"
                                      id="myTable"
                                      border
                                      :select-all="false"
                                      height="340px"
                                      @row-click="selectContractOrPlanRowClickM"
                                      :data="tableData2"
                                      v-loading="selectContractOrPlanLoading"
                                      class="table"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="enterpriseName" label="企业名称" width=""></el-table-column>
                                <el-table-column label="企业类型" width="">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.enterpriseType == '0'">个体户</span>
                                        <span v-else-if="scope.row.enterpriseType == '1'">企业</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="蜀道企业">
                                    <template v-slot="scope">
                                        <el-tag v-if="scope.row.shuDaoFlag === 0">否</el-tag>
                                        <el-tag v-else-if="scope.row.shuDaoFlag === 1" type="success">是</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="注册资金(万元)" width="">
                                    <template v-slot="scope">
                                        {{ scope.row.registeredCapital }}
                                    </template>
                                </el-table-column>
                            </el-table>
                            <Pagination
                                v-if="tableData2 != null && tableData2.length > 0"
                                :total="paginationInfo3.total"
                                :pageSize.sync="paginationInfo3.pageSize"
                                :currentPage.sync="paginationInfo3.currentPage"
                                @currentChange="currentChange2"
                                @sizeChange="sizeChange2"
                            />
                        </div>
                    </div>
                    <div class="box-right">
                        <div class="e-table">
                            <div class="top">
                                <div style="width: 200px" class="left">
                                    <span style="color: #ea083a">双击<br>移除</span>
                                </div>

                            </div>
                            <el-table ref="siteReceivingTableRef"
                                      v-loading="siteReceivingLoading"
                                      border
                                      height="340px"
                                      @row-dblclick="removeSelectOrderItemList"
                                      :data="updateRow"
                                      class="table"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="enterpriseName" label="企业名称" width=""></el-table-column>
                                <el-table-column label="企业类型" width="">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.enterpriseType == '0'">个体户</span>
                                        <span v-else-if="scope.row.enterpriseType == '1'">企业</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="蜀道企业">
                                    <template v-slot="scope">
                                        <el-tag v-if="scope.row.shuDaoFlag === 0">否</el-tag>
                                        <el-tag v-else-if="scope.row.shuDaoFlag === 1" type="success">是</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="注册资金(万元)" width="">
                                    <template v-slot="scope">
                                        {{ scope.row.registeredCapital }}
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <span slot="footer">
                <el-button type="primary" @click="siteReceivingTableDateSelectAffirmClick">确认修改</el-button>
                <el-button @click="updateVisible = false">取消</el-button>
            </span>
        </el-dialog>

        <!--        <el-dialog v-dialogDrag title="配置信用额度" :visible.sync="updateVisible" width="50%">-->
        <!--            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">-->
        <!--                <el-row>-->
        <!--                    <el-col :span="12">-->
        <!--                        <el-form-item width="150px" label="企业名称：" prop="enterpriseName">-->
        <!--                            <el-input v-model="filterData.enterpriseName" placeholder="请输入企业名称" clearable></el-input>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->
        <!--                </el-row>-->
        <!--                <el-row>-->
        <!--                    <el-col :span="12">-->
        <!--                        <el-form-item label="企业所在城市：" prop="city">-->
        <!--                            <el-input v-model="filterData.city" placeholder="企业所在城市" clearable></el-input>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->
        <!--                </el-row>-->
        <!--                <el-row   >-->
        <!--                    <el-col :span="12">-->
        <!--                        <el-form-item label="蜀道企业：">-->
        <!--                            &lt;!&ndash;                                <el-input clearable  v-model="filterData.status" placeholder="请输入店铺名称" style="width: 200px"></el-input>&ndash;&gt;-->
        <!--                            <el-select v-model="filterData.shuDaoFlag" placeholder="请选择状态">-->
        <!--                                <el-option-->
        <!--                                    :key="999"-->
        <!--                                    label="全部"-->
        <!--                                    :value="999">-->
        <!--                                </el-option>-->
        <!--                                <el-option-->
        <!--                                    :key="1"-->
        <!--                                    label="是"-->
        <!--                                    :value="1">-->
        <!--                                </el-option>-->
        <!--                                <el-option-->
        <!--                                    :key="0"-->
        <!--                                    label="否"-->
        <!--                                    :value="0">-->
        <!--                                </el-option>-->
        <!--                            </el-select>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->

        <!--                </el-row>-->
        <!--                <el-row>-->
        <!--                    <el-col :span="12">-->
        <!--                        <el-form-item label="创建时间：">-->
        <!--                            <el-date-picker-->
        <!--                                value-format="yyyy-MM-dd HH:mm:ss"-->
        <!--                                v-model="filterData.dateValue"-->
        <!--                                type="datetimerange"-->
        <!--                                range-separator="至"-->
        <!--                                start-placeholder="开始日期"-->
        <!--                                end-placeholder="结束日期"-->
        <!--                            >-->
        <!--                            </el-date-picker>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->
        <!--                </el-row>-->
        <!--            </el-form>-->
        <!--            <span slot="footer">-->
        <!--                <el-button type="primary" @click="confirmSearch">查询</el-button>-->
        <!--                <el-button @click="resetSearchConditions">清空</el-button>-->
        <!--                <el-button @click="queryVisible = false">取消</el-button>-->
        <!--            </span>-->
        <!--        </el-dialog>-->
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import {
    getList,
    updateArrearageBatch,
    getSdStatus,
    updateBatchArrearage,
    resetPwd
} from '@/api/platform/supplier/supplierAudit'
import { debounce, toFixed } from '@/utils/common'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    data () {
        return {
            dateFormat: 'yyyy-MM-dd HH:mm:ss',
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            // 表格数据
            keywords: null, // 关键字
            upkeywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            updateVisible: false, // 高级搜索显示
            addPlanLoading: false, // 高级搜索显示
            updateFormRote: {
                arrearage: [
                    { required: true, message: '请输入信用额度', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value < 0 || value > 9999999999) {
                                // 将负数和大于 199 的值赋值为 0
                                value = value < 0 ? 0 : (value > 9999999999 ? 0 : value)
                                callback(new Error('请输入0到9999999999之间的数字'))
                            } else {
                                callback()
                            }

                        },
                        trigger: 'blur'
                    }
                ],

            },
            updateForm: {
                arrearage: null,
                arrearageDateNum: null,
                arrearageDateType: 1,
                enterpriseIds: []
            },
            arrearageDateNumShow: false,
            selectContractOrPlanLoading: false,
            siteReceivingLoading: false,
            keywords2: '',
            tableLoading: false, // 表格Loading
            changedRow: [], // 信用额度批量修改
            updateRow: [], // 需要修改的元素
            selectRow: [], // 修改查询  查询修改的元素
            updateRow2: [],  //添加数据查询  需要修改的公司集合
            updateSumRow: [],  //所有元素
            subRow: [],
            pickerAfterOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo3: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            fullData: [],
            paginationInfo2: { // 分页
                total: 20,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            tableData2: [], // 表格数据
            // 高级搜索
            filterData: {
                shuDaoFlag: 999,
                enterpriseName: null,
                city: null,
                orderBy: 2,
                dateValue: []
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.getTableData()
    },
    methods: {
        currentChange2 (index) {
            this.paginationInfo3.currPage = index
            this.getupdateList()
        },
        sizeChange2 (index) {
            this.paginationInfo3.pageSize = index
            this.getupdateList()
        },
        getChangedRow (arrearage) {
            this.arrearageDateNumShow = true
            if (arrearage <= 0) {
                arrearage = 0
                this.arrearageDateNumShow = false
            }

            if (arrearage >= 999999999) {
                arrearage = 999999999
            }
            arrearage = this.fixed4(arrearage)
        },
        handleSelectionChange (selection) {
            this.updateRow = selection
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 选中状态进行查询
        stateTopOptionsClick (value) {
            this.selectOptionValue = value
            this.getTableData()
        },
        // 详情
        handleView (row) {
            this.$router.push({
                path: '/platform/supplier/supplierInquireDetail',
                name: 'supplierInquireDetail',
                params: {
                    row,
                }
            })
        },
        resetSearchConditions () {
            this.filterData.enterpriseName = ''
            this.filterData.city = ''
            this.filterData.dateValue = []
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        currentChangeUser2 (index) {
            console.log(index, 'currentChangeUser2')
            this.paginationInfo2.currentPage = index
            // 计算当前页应该显示的数据范围
            const startIndex = (index - 1) * this.paginationInfo2.pageSize
            const endIndex = startIndex + this.paginationInfo2.pageSize
            // 更新当前页显示的数据
            this.updateRow = this.fullData.slice(startIndex, endIndex)
        },
        sizeChangeUser2 (index) {
            this.paginationInfo2.pageSize = index
            const startIndex = (this.paginationInfo2.currentPage - 1) * index
            const endIndex = startIndex + index

            // 更新当前页显示的数据
            this.updateRow = this.fullData.slice(startIndex, endIndex)
        },
        siteReceivingTableDateSelectAffirmClick () {
            this.$refs.updateFormRote.validate(async valid => {
                if (!valid) return
                if (this.updateRow.length === 0) {
                    this.$message.warning('请选择公司。')
                } else {
                    this.$confirm('请确定是否修改欠费数据?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        console.log(this.updateRow)
                        this.updateForm.enterpriseIds = this.updateRow.map(item => {
                            return item.enterpriseId
                        })
                        updateBatchArrearage(this.updateForm).then(res => {
                            if (res.code == 200) {
                                this.$message.success('配置信用额度成功')
                            }
                            this.updateSumRow = []
                            this.updateRow = []
                            this.updateVisible = false
                            this.getTableData()
                        })
                    })

                }
            })

        },
        // 获取表格数据
        getTableData () {
            let params = {
                isSupplier: 2,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            if (this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.city != null) {
                params.city = this.filterData.city
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.shuDaoFlag === 999) {
                params.shuDaoFlag = null
            } else if (this.filterData.shuDaoFlag) {
                params.shuDaoFlag = this.filterData.shuDaoFlag
            }
            getList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            })
        },
        // getChangedRow (row) {
        //     if(this.changedRow.length == 0) {
        //         this.changedRow.push({ enterpriseId: row.enterpriseId, arrearage: row.arrearage, arrearageData: row.arrearageData })
        //     }else {
        //         this.changedRow.push({ enterpriseId: row.enterpriseId, arrearage: row.arrearage, arrearageData: row.arrearageData })
        //     }
        //     let flag = false
        //     this.changedRow.forEach(t => {
        //         if(t.enterpriseId == row.enterpriseId) {
        //             t.arrearage = row.arrearage
        //             t.arrearageData = row.arrearageData
        //             flag = true
        //         }
        //     })
        //     if(!flag) {
        //         this.changedRow.push({ enterpriseId: row.enterpriseId, arrearage: row.arrearage, arrearageData: row.arrearageData })
        //     }
        //     flag = true
        // },
        handleSd () {
            this.$confirm('此操作较为耗时, 是否继续?', '提示', {
                confirmButtonText: '继续',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableLoading = true
                getSdStatus().then(res => {
                    if (res) this.getTableData()
                }).finally(this.tableLoading = false)
            })

        },
        getupdateList () {
            let params = {
                isSupplier: 2,
                page: this.paginationInfo3.currentPage,
                limit: this.paginationInfo3.pageSize,
                shuDaoFlag: 0,
                isShortCode: 0

            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            if (this.upkeywords != null && this.upkeywords != '') {
                params.keywords = this.upkeywords
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.city != null) {
                params.city = this.filterData.city
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            // if (this.filterData.shuDaoFlag === 999) {
            //     params.shuDaoFlag = null
            // } else if (this.filterData.shuDaoFlag) {
            //     params.shuDaoFlag = this.filterData.shuDaoFlag
            // }
            getList(params).then(res => {
                this.tableData2 = res.list
                this.paginationInfo3.total = res.totalCount
                this.paginationInfo3.pageSize = res.pageSize
                this.paginationInfo3.currentPage = res.currPage
            })
        },
        openUpdate () {
            this.updateVisible = true
            this.updateForm.arrearage = null,
            this.updateForm.arrearageDateNum = null,
            this.updateForm.arrearageDateType = 1,
            this.updateForm.enterpriseIds = []
            this.getupdateList()

        },
        batchSubmitCheck () {
            if (this.updateSumRow.length === 0) {
                return this.$message('未修改列表当中的数据！')
            }
            console.log('arr', this.updateSumRow)
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                updateArrearageBatch(this.updateSumRow).then(res => {
                    this.getTableData()
                    this.updateSumRow = []
                    this.tableLoading = false
                    this.message(res)
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        removeSelectOrderItemList (row) {
            if (this.updateRow == 0) {
                return this.$message.warning('没有确认的公司')
            } else {
                this.updateRow = this.updateRow.filter(t => t.enterpriseId != row.enterpriseId)
                // this.updateRow.forEach((item, index) => {
                //     const foundIndex = this.subRow.findIndex(row => row.enterpriseId === item.enterpriseId)
                //     if (foundIndex !== -1) {
                //         this.updateSumRow.splice(index, 1)
                //     }
                // })
            }

        },

        getSiteReceivingTableDateM () {
            if (this.keywords2 != null && this.keywords2 != '') {
                this.updateRow = []
                this.updateSumRow.forEach(item => {
                    if (item.enterpriseName.includes(this.keywords2)) {
                        this.updateRow.push(item)
                    }
                })
            } else {
                this.updateRow = this.updateSumRow
            }

        },
        siteReceivingTableSelectM (selection) {
            this.subRow = selection
            // this.subitSubRow()
        },
        //选择公司添加到数组，确定后
        //updateRow2  待选择的公司
        // updateSumRow 待提交的数据
        selectContractOrPlanSelectM (selection) {
            this.updateRow2 = selection
            this.subitAddUpdateRow()
        },
        subitAddUpdateRow () {

            this.updateSumRow = [...this.updateRow2]
            //修改公司总数组
            if (this.updateSumRow == 0) {
                this.updateRow = this.updateSumRow
            } else {
                //遍历所有已选择的公司，如果新的公司数组没有在updateSumRow（总）中，添加，否则删除
                this.updateSumRow.forEach(item => {
                    const foundIndex = this.updateRow.findIndex(row => row.enterpriseId === item.enterpriseId)
                    if (foundIndex === -1) {
                        this.updateRow.push(item)
                    }
                })
            }
            this.paginationInfo2.total = this.updateRow.length

        },
        selectContractOrPlanRowClickM (row) {
            for (let i = 0; i < this.updateRow.length; i++) {
                let t = this.updateRow[i]
                if (t.enterpriseId == row.enterpriseId) {
                    return this.$message.warning('该公司已选择！')
                }
            }
            this.updateRow.push(row)
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        },
        resetPwd (enterpriseId) {
            this.$confirm('是否重置该企业关联用户的密码', '重置密码', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                resetPwd(enterpriseId).then(res=>{
                    if (res.code === 200) {
                        this.$message({
                            message: '重置密码成功',
                            type: 'success'
                        })
                    }
                })
            })
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
    -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 280px;
        margin-top: 0px;
    }
}

/deep/ .updateVisible {
    .el-dialog__body {
        height: 680px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}

/deep/ .el-tab-pane {
    overflow: auto;
    width: 100%;
}

.box-left, .box-right {
    width: 50% !important;
    flex-grow: 1;

    .top {
        padding: 10px;

        .el-input {
            margin-right: 10px;
        }
    }
}

/deep/ .el-dialog.dlg {
    height: 800px;

    .el-dialog__header {
        margin-bottom: 0;
    }

    .el-dialog__body {
        height: 474px;
        margin: 10px;
        display: flex;

        & > div {
            .e-pagination {
                background-color: unset;
            }

            //height: 670px;
            .title {
                height: 22px;
                margin-bottom: 10px;
                padding-left: 26px;
                text-align: left;
                line-height: 22px;
                color: #2e61d7;
                font-weight: bold;
                position: relative;
                display: flex;

                &::before {
                    content: '';
                    display: block;
                    width: 10px;
                    height: inherit;
                    border-radius: 5px;
                    background-color: blue;
                    position: absolute;
                    left: 10px;
                    top: 0;
                }
            }
        }

        .el-input__inner {
            border: 1px solid blue;
            border-radius: 6px;
        }

        .el-input__suffix {
            width: 20px;
        }

        .e-table {
            //flex-grow: 1;

            .table {
                height: 100%;
            }
        }

        .box-left {
            //width: 660px;

            .top {
                box-shadow: unset;

                .left {
                    display: flex; // 设置弹性布局
                    align-items: center; // 垂直居中
                    justify-content: space-between; // 左右对齐，两端留白
                    el-input {
                        margin-right: 10px; // 右侧留白
                        margin-left: 10px;
                    }

                    el-button {
                        margin-right: 10px; // 右侧留白
                        margin-left: 10px;
                    }
                }
            }
        }

        .box-right {
            border: 1px solid red;

            & > div {
                display: flex;
                flex-direction: column;
            }

            .top {
                justify-content: left;
                border-radius: 0;
                box-shadow: unset;

                .left {
                    display: flex; // 设置弹性布局
                    align-items: center; // 垂直居中
                    justify-content: space-between; // 左右对齐，两端留白
                    el-input {
                        margin-right: 10px; // 右侧留白
                        margin-left: 10px;
                    }
                }
            }

            .bottom {
                flex-grow: 1;
            }
        }
    }

    .el-dialog__footer {
        background-color: #eff2f6;
    }
}
</style>
