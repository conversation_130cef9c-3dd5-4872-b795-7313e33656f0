<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <!--                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>-->
                            <!--                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">启用</el-button>-->
                            <!--                            <el-button type="primary" @click="changePublishState(2)" class="btn-greenYellow">停用</el-button>-->
                            <!--                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-input type="text" @keyup.enter.native="onSearch" placeholder="合同名称/公司名称/计划编号"
                                  v-model="keywords"><img src="@/assets/search.png"
                                                          slot="suffix" @click="onSearch"/></el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table @row-click="handleCurrentInventoryClick2" ref="eltableCurrentRow2" class="table"
                          :height="rightTableHeight" :data="tableData" border highlight-current-row
                          @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="200" prop="totalAmount">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">发货计划</span>
                            <span class="action" @click="createQrcode(scope.row)">生成二维码</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="合同编号" width="100" prop="sourceNumber"></el-table-column>
                    <el-table-column label="合同名称" width="100" prop="sourceName"></el-table-column>
                    <el-table-column label="公司名称" width="" prop="orgName"></el-table-column>
                    <el-table-column label="计划编号" width="100" prop="billNo"></el-table-column>
                    <el-table-column label="物资年度计划编号" width="100" prop="yearPlanBillNo"></el-table-column>
                    <el-table-column label="计划金额" width="100" prop="amount"></el-table-column>
                    <el-table-column label="税率" width="100" prop="taxRate"></el-table-column>
                    <el-table-column label="税额" width="" prop="taxAmount"></el-table-column>
                    <el-table-column label="计划日期" width="100" prop="planDate"></el-table-column>
                    <el-table-column label="创建时间" width="" prop="gmtCreate"></el-table-column>
                    <el-table-column label="更新时间" width="" prop="gmtModified"></el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize"
                           :currentPage.sync="pages.currPage"
                           @currentChange="currentChange" @sizeChange="sizeChange"/>
        </div>
        <el-dialog title="扫码二维码" class="qrcodeDialog" :visible.sync="qrcodeVisible" width="220px" :before-close="beforeQrcodeClose">
            <div ref="qrcode" id="qrcode"></div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="() => { beforeQrcodeClose(); qrcodeVisible = false }">取 消</el-button>
                <el-button type="primary" @click="() => { beforeQrcodeClose(); qrcodeVisible = false }">确 定</el-button>
            </span>
        </el-dialog>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="true"
                   :before-close="closeDialog">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="合同名称：">
                            <el-input clearable v-model="filterData.sourceName" placeholder="请输入合同名称"
                                      style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="公司名称：">
                            <el-input clearable v-model="filterData.orgName" placeholder="请输入公司名称"
                                      style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="最低计划金额以上：">
                            <el-input clearable type="number" v-model="filterData.startAmount"
                                      placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                        <el-form-item label="最高计划金额价格以下：">
                            <el-input clearable type="number" v-model="filterData.endAmount"
                                      placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="计划日期：">
                            <el-date-picker
                                value-format="yyyy-MM-dd"
                                v-model="filterData.planDates"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import QRCode from 'qrcodejs2'
import ComPagination from '@/components/pagination/pagination.vue'
import { monthList } from '@/api/supplierSys/shipmentManage/shipmentPlan'
import { debounce } from '@/utils/common'
import { mapActions, mapState } from 'vuex'

export default {
    components: {
        ComPagination
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            qrcodeVisible: false,
            queryVisible: false,
            keywords: '',
            currentRow: null,
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
                totalCount: 0,
            },
            // 高级查询数据对象
            filterData: {
                orgName: '',
                sourceName: '',
                startAmount: '',
                endAmount: '',
                planDates: []

            },
            tableData: [],
            // 新增编辑 表单数据
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        this.getDateList()
    },
    methods: {
        createQrcode ({ billId }) {
            this.qrcodeVisible = true
            setTimeout(() => {
                let div = this.$refs.qrcode
                return new QRCode(div, {
                    text: billId, //二维码内容字符串
                    width: 180, //图像宽度
                    height: 180, //图像高度
                    colorDark: '#000000', //二维码前景色
                    colorLight: '#ffffff', //二维码背景色
                    correctLevel: QRCode.CorrectLevel.H, //容错级别
                })
            }, 100)
        },
        beforeQrcodeClose () {
            this.$refs.qrcode.innerHTML = ''
            this.qrcodeVisible = false
        },
        getDateList () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
            }

            if (this.filterData.planDates.length !== 0) {
                params.startTime = this.filterData.planDates[0]
                params.endTime = this.filterData.planDates[1]
            }
            if (this.filterData.startAmount !== '' && this.filterData.startAmount != null) {
                params.startAmount = this.filterData.startAmount
            }
            if (this.filterData.endAmount !== '' && this.filterData.endAmount != null) {
                params.endAmountt = this.filterData.endAmount
            }
            if (this.filterData.sourceName !== '' && this.filterData.sourceName != null) {
                params.sourceName = this.filterData.sourceName
            }
            if (this.keywords !== '' && this.keywords != null) {
                params.keywords = this.keywords
            }
            monthList(params).then(res => {
                this.tableData = res.list
                this.pages = res
            })
        },
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                sourceNumber: '',
                sourceName: '',
                orgName: '',
                startAmount: '',
                endAmount: '',
                planDate: [],
            }
            done()
        },
        hideDialog () {
            this.filterData = {
                orgName: '',
                sourceName: '',
                startAmount: '',
                endAmount: '',
                planDates: []
            }
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            this.getDateList()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getDateList()
        },
        sizeChange () {
            this.getDateList()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () {
        },
        handleView (scope) {
            this.formData = scope.row
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/shipmentPlanDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shipmentPlanDetail',
                params: {
                    row: scope.row
                }
            })
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        emptyForm () {},
        // 获取列表数据

        // 关键词搜索
        onSearch () {
            this.getDateList()

        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单

        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}
/deep/ .qrcodeDialog .el-dialog__body {
    height: 240px;
}
</style>
