<template>
    <div class="root">
        <div class="main">
            <div class="history mb10 center"><span class="router-usher">
        <a @click="$router.push('/mFront/biddingIndex')">招标</a>>
        <a @click="$router.push({path:'/mFront/biddingIndex',query:{tenderForm: 4}})">单一性来源招标</a>
        >招标详情<!--<div>返回</div>--></span></div>
        </div>
        <div class="topBox center">
            <div class="title">{{ form.tenderName }}</div>
            <DIV class="logImg">
                <img v-if="form.tenderState<3" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState==3" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState==4" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState>4&&form.tenderState<6" src="@/assets/images/img/biddingsignup.png" alt=""
                     width="135px">
                <img v-if="form.tenderState>=6" src="@/assets/images/img/<EMAIL>" alt="" width="135px"></DIV>
            <div class="center infoxBox">
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>招标名称:
                        <span>{{ form.tenderName }}</span></div>
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>招标编号: <span>
                       {{ form.billNo }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>招标类型:
                        <span v-if="form.tenderType==1">装备</span>
                        <span v-if="form.tenderType==2">装备租赁</span>
                        <span v-if="form.tenderType==3">周材</span>
                        <span v-if="form.tenderType==4">物资</span>
                        <span v-if="form.tenderType==5">机材</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>采购企业:
                        <span>{{ form.applyOrgName }}</span>
                    </div>
                </div>

                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>联系人:
                        <span>{{ form.tenderUser }}</span></div>
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>联系电话:
                        <span>{{ form.phone }}</span></div>

                </div>
            </div>
            <div class="lineBox">
                <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>预算金额:
                    <span>{{ form.tenderAmount }}</span></div>
                <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>发布时间:
                    <span>{{ form.releaseDate }}</span>
                </div>
                <!--                <div class="button_bule">收藏</div>-->
            </div>
            <div class="lineBox">
                <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>截止时间:
                    <span>{{ form.tenderEndTime }}</span>
                </div>
            </div>
        </div>

        <!-- =========== -->
        <tenderBar :tenderInfo="form"></tenderBar>
    </div>
</template>
<script>
// import { getbalseList, getTenderList, getTenderPackageSubcontractorList, getQuesionList, getTenderNotice } from '@/api/frontStage/bidding'
// import {
//     getbalseList,
//     getTenderdtlList,
//     getTenderPackageSubcontractorList,
//     getQuesionList,
//     getPurchasePlatformDate
// } from '@/api/frontStage/bidding'

export default {
    name: 'biggDetail',
    components: { tenderBar: () => import ( '@/components/tenderBar.vue') },
    data () {
        return {
            tenderDtlTable: [],
            tenderPackages: [],
            packageTable: [],
            tenderDtls: [],
            tenderPurchasePlatform: {},
            tenderNotice: '', //招标公告
            TenderCommentList: [],
            tenderTable: [],
            babalTable: [],
            TenderPackageVo: [],
            num: 1,
            tabCurrent: 0,
            form: {
                billId: '',
                billINo: '',
                tenderName: ''
            },
            proclamation: true,
            balse: false,
            tenderList: false,
            dataListSelections: [], //表格选中的数据
        }
    },
    created () {
        this.form = this.$route.params.row
        // this.balseList()
        // this.purchasePlatform(this.form.billNo)
    },
    // methods: {
    //
    //     balseList () {
    //         getbalseList({ id: this.form.billId }).then(res=>{
    //             this.babalTable = res
    //         })
    //     },
    //     //外部接口，获得采购平台数据
    //     purchasePlatform (billNo) {
    //         let parame = {
    //             Key: this.form.billNo
    //         }
    //         getPurchasePlatformDate(parame).then(res => {
    //             this.tenderPurchasePlatform = res
    //             this.packageTable = this.tenderPurchasePlatform.packages
    //             this.packageTable.forEach(item => {
    //                 item.details = []
    //                 item.tenderPackages = []
    //             })
    //
    //         })
    //     },
    //     Changetabcurrent (i) {
    //         this.tabCurrent = i
    //         if (i == 0) {
    //             this.showDetail(1)
    //         } else if (i == 1) {
    //             this.tenderPackages = this.tenderPurchasePlatform.tenderPackages
    //             if (this.packageTable.length > 0) {
    //                 for (let i = 0; i < this.packageTable.length; i++) {
    //                     var tenderPackagesList = new Set([])
    //                     if (this.tenderPackages.length > 0) {
    //                         if (this.tenderPackages.length > 0) {
    //                             for (let j = 0; j < this.tenderPackages.length; j++) {
    //                                 if (this.packageTable[i].id == this.tenderPackages[j].bidPackageId) {
    //                                     tenderPackagesList.add(this.tenderPackages[j])
    //                                 }
    //                             }
    //                         }
    //                     }
    //                     this.packageTable[i].tenderPackages = [...tenderPackagesList]
    //                 }
    //             }
    //
    //         } else if (i == 2) {
    //             this.quesionList()
    //         }
    //     },
    //     selectionChangeHandle (val) {
    //         this.dataListSelections = val
    //     },
    //     handleCurrentInventoryClick (row) {
    //         row.flag = !row.flag
    //         this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
    //     },
    //     showDetail (i) {
    //         if (i == 1) {
    //             this.proclamation = true
    //             this.balse = false
    //             this.tenderList = false
    //         }else  if (i == 2) {
    //             this.proclamation = false
    //             this.balse = true
    //             this.tenderList = false
    //         }else  if (i == 3) {
    //             this.proclamation = false
    //             this.balse = false
    //             this.tenderList = true
    //             this.getTenderdtlLists()
    //         }
    //     },
    //     getTenderdtlLists () {
    //         getTenderdtlList({ billId: this.form.billId }).then(res=>{
    //             this.tenderDtlTable = res
    //         })
    //     },
    //     getTenderPackageSubcontractor () {
    //         getTenderPackageSubcontractorList({ id: this.form.billId }).then(res=>{
    //             this.TenderPackageVo = res
    //         })
    //     },
    //     quesionList () {
    //         getQuesionList({ id: this.form.billId }).then(res=>{
    //             this.TenderCommentList = res
    //         })
    //     }
    // }
}
</script>
<style scoped lang="scss">
.root {
    background-color: #f5f5f5;
    padding-bottom: 50px;
}

span {
    line-height: 1;
}

.row {
    margin-bottom: 20px;
}

.router-usher {
    cursor: pointer;

    a:hover {
        color: rgba(34, 111, 199, 1);
    }
}

.main {
    width: 1326px;
    margin: 10px auto 0;

    .history {
        width: 100%;
        height: 40px;
        margin-top: 10px;
        padding-left: 20px;
        font-size: 12px;
        line-height: 40px;
        background-color: #fff;
        position: relative;

        div {
            width: 64px;
            height: 28px;
            text-align: center;
            line-height: 28px;
            border: 1px solid rgba(230, 230, 230, 1);
            color: rgba(153, 153, 153, 1);
            position: absolute;
            top: 6px;
            right: 10px;
            cursor: pointer;
            user-select: none;
        }
    }
}

.overflow {
    height: 500px;
    overflow: hidden;
    overflow-y: scroll;
}

.status {
    width: 142px;
    height: 40px;
    opacity: 1;
    border-radius: 1px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    line-height: 40px;
    margin-top: 15px;
}

.topBox {
    width: 1326px;
    background-color: #fff;
    padding: 20px 21px 30px 19px;
    position: relative;
}

.title {
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    padding-top: 20px;
    padding-bottom: 54px;
    border-bottom: 1px dashed rgba(229, 229, 229, 1);;
}

.el-table {
    margin: auto;
}

.infoBox {

    padding-top: 32px;

}

.lineBox {

    height: 30px;
    padding-left: 205px;
    margin-top: 21px;
    font-size: 14px;
    overflow: hidden;
    display: flex;

    span {
        margin-left: 10px;
    }

    .leftDiv, .rightDiv {
        width: 50%;
        display: flex;
        align-items: center;

        .tit-img {
            width: 20px;
            height: 20px;
            margin-right: 11px;
        }
    }

}

.button_bule {
    width: 142px;
    height: 40px;
    opacity: 1;
    background: #226FC7;
    font-size: 16px;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    line-height: 40px;
    margin-top: 15px;
    position: relative;
    left: 40%;
}

.logImg {
    z-index: 9999;
    width: 136px;
    height: 136px;
    border: 1px dashed rgba(229, 229, 229, 1);
    position: absolute;
    right: 110px;
    top: 0;
}

.detailBox {
    width: 1326px;
    min-height: 605px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(229, 229, 229, 1);
    margin-top: 60px;

    .tabBox {
        width: 1324px;
        height: 60px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        background: rgba(250, 250, 250, 1);

        div {
            width: 160px;
            height: 60px;
            text-align: center;
            line-height: 60px;
            font-size: 18px;
            cursor: pointer;
        }

        .tabab {
            background: #fff;
            border-top: solid 2px rgba(34, 111, 199, 1);
        }
    }

    .description {
        width: 1286px;
        min-height: 108px;
        border: 1px solid rgba(230, 230, 230, 1);
    }

    .row {
        padding-left: 27px;
        padding-right: 240px;

        .item {
            span {
                font-size: 16px;
                font-weight: 400;
                color: rgba(102, 102, 102, 1);
            }

            div {
                font-size: 16px;
                font-weight: 400;
                color: rgba(0, 0, 0, 1);
            }
        }
    }

    .han {
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 1);
        padding-left: 30px;
        margin-top: 30px;
    }

    .descriptionsBox {
        padding: 20px;
    }
}
</style>
