<template>
    <div class="base-page">
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-button
                                type="primary"
                                @click="outPutExcelM"
                            >导出</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-input v-model="keywords" clearable placeholder="输入搜索关键字" style="width: 300px" type="text" @blur="handleInputSearch">
                            <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    ref="tableRef"
                    v-loading="tableLoading"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                    class="table"
                    @selection-change="tableSelectM"
                    @row-click="tableRowClickM"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="企业名称" prop="enterpriseName" width="400"/>
                    <el-table-column label="信用额度（元）" prop="arrearage" width="120"/>
                    <el-table-column label="是否开通店铺年服务" prop="isOpenShop" width="150">
                        <template v-slot="scope">
                            <el-tag type="success" v-if="scope.row.isOpenShop == 1">是</el-tag>
                            <el-tag type="danger" v-if="scope.row.isOpenShop == 0">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否开通招标年服务" prop="isOpenBid" width="150">
                        <template v-slot="scope">
                            <el-tag type="success" v-if="scope.row.isOpenBid == 1">是</el-tag>
                            <el-tag type="danger" v-if="scope.row.isOpenBid == 0">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺年费截止日期" prop="shopEndTime" width="150">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.shopEndTime == null">无开通记录</el-tag>
                            <span v-else>{{scope.row.shopEndTime}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="电子招标年费截止日期" prop="bidEndTime" width="150">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.bidEndTime == null">无开通记录</el-tag>
                            <span v-else>{{scope.row.bidEndTime}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否欠费" prop="" width="">
                        <template v-slot="scope">
                            <el-tag type="danger" v-if="scope.row.totalPayFree >0">是</el-tag>
                            <el-tag type="success" v-if="scope.row.totalPayFree == 0">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺是否超过信用额度" prop="" width="150">
                        <template v-slot="scope">
                            <el-tag type="danger" v-if="scope.row.isShopExceedArrearage == 1">是</el-tag>
                            <el-tag type="success" v-if="scope.row.isShopExceedArrearage == 0">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="合同履约是否超过信用额度" prop="" width="150">
                        <template v-slot="scope">
                            <el-tag type="danger" v-if="scope.row.isBidExceedArrearage == 1">是</el-tag>
                            <el-tag type="success" v-if="scope.row.isBidExceedArrearage == 0">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺交易欠费金额（元）" prop="shopPayFree" width="150"/>
                    <el-table-column label="合同履约欠费金额（元）" prop="contractPayFree" width="150"/>
                    <el-table-column label="总欠费金额（元）" prop="totalPayFree" width="150"/>
                </el-table>
            </div>
            <Pagination
                v-show="tableData && tableData.length > 0"
                :currentPage.sync="paginationInfo.currentPage"
                :pageSize.sync="paginationInfo.pageSize"
                :total="paginationInfo.total"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="250px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="企业名称：">
                            <el-input clearable maxlength="100" placeholder="请输入企业名称" v-model="filterData.enterpriseName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="是否欠费：">
                            <el-select v-model="filterData.isArrearage" placeholder="请选择是否欠费">
                                <el-option
                                    v-for="item in filterData.isArrearageSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺交易是否超过信用额度：">
                            <el-select v-model="filterData.isShopExceedArrearage" placeholder="请选择店铺交易是否超过信用额度">
                                <el-option
                                    v-for="item in filterData.isArrearageSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="合同履约交易是否超过信用额度：">
                            <el-select v-model="filterData.isBidExceedArrearage" placeholder="请选择是否合同履约交易超过信用额度">
                                <el-option
                                    v-for="item in filterData.isArrearageSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺年费是否到期：">
                            <el-select v-model="filterData.isShopOut" placeholder="请选择店铺年费是否到期">
                                <el-option
                                    v-for="item in filterData.isArrearageSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="招标年费是否到期：">
                            <el-select v-model="filterData.isBidOut" placeholder="请选择招标年费是否到期">
                                <el-option
                                    v-for="item in filterData.isArrearageSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺年费到期时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd"
                                v-model="filterData.shopAuditTime"
                                type="daterange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="14">
                        <el-form-item label="招标年费到期时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd"
                                v-model="filterData.bidAuditTime"
                                type="daterange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { debounce, toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { platformTotalCountFree } from '@/api/fee/feeApi'
import { mapState } from 'vuex'
import { excelTotalCountFree } from '@/api/platform/order/orders'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        },
    },
    computed: {
        ...mapState([ 'userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    data () {
        return {
            tableSelectRow: [], // 多选框选择的数据
            tableLoading: false, // 加载
            keywords: null, // 关键字
            currentQuery: {},
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            uploadImgSize: 10, // 上传文件大小
            tableData: [], // 表格数据
            filterData: { // 高级搜索
                isBidOut: null,
                isShopOut: null,
                enterpriseName: null,
                isArrearage: null,
                isShopExceedArrearage: null,
                isBidExceedArrearage: null,
                isArrearageSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '否' },
                    { value: 1, label: '是' }
                ],
                recordTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '店铺年度服务费' },
                    { value: 2, label: '电子招标年度服务费' },
                    { value: 3, label: '店铺交易服务费' },
                    { value: 4, label: '合同履约交易服务费' },
                ],
                orderBy: 1,
                bidAuditTime: [],
                shopAuditTime: [],
                gmtModified: [],
                auditOpenTime: [],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            uploadLoading: false,
            fileList: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    // activated () {
    //     this.getTableData()
    // },
    created () {
        this.getTableData()
    },
    methods: {
        outPutExcelM () {
            if(this.tableSelectRow.length > 0) {
                let enterpriseNames = this.tableSelectRow.map(item => {
                    return item.enterpriseName
                })
                this.currentQuery.enterpriseNames = enterpriseNames
            }
            this.tableLoading = true
            excelTotalCountFree(this.currentQuery).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = '平台供应商费用报表.xlsx'
                a.click()
                window.URL.revokeObjectURL(url)
                this.currentQuery.enterpriseNames = []
                // this.tableSelectRow = []
                this.$message.success('操作成功')
            }).finally(() => {
                this.tableLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 多选框
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        // 行点击
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        //重置数据
        resetSearchConditions () {
            this.filterData.isArrearage = null
            this.filterData.isShopExceedArrearage = null
            this.filterData.isBidExceedArrearage = null
            this.filterData.enterpriseName = null
            this.filterData.isBidOut = null
            this.filterData.isShopOut = null
            this.filterData.shopAuditTime = []
            this.filterData.bidAuditTime = []
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.isArrearage != null) {
                params.isArrearage = this.filterData.isArrearage
            }
            if (this.filterData.isBidExceedArrearage != null) {
                params.isBidExceedArrearage = this.filterData.isBidExceedArrearage
            }
            if (this.filterData.isShopExceedArrearage != null) {
                params.isShopExceedArrearage = this.filterData.isShopExceedArrearage
            }
            if (this.filterData.isShopOut != null) {
                params.isShopOut = this.filterData.isShopOut
            }
            if (this.filterData.isBidOut != null) {
                params.isBidOut = this.filterData.isBidOut
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.bidAuditTime != null) {
                params.startBidAuditTime = this.filterData.bidAuditTime[0]
                params.endBidAuditTime = this.filterData.bidAuditTime[1]
            }
            if (this.filterData.shopAuditTime != null) {
                params.startShopAuditTime = this.filterData.shopAuditTime[0]
                params.endShopAuditTime = this.filterData.shopAuditTime[1]
            }
            this.tableLoading = true
            this.currentQuery = params
            platformTotalCountFree(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
            }).finally(() => {
                this.tableLoading = false
            })

        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
    appearance: textfield !important;
    -moz-appearance: textfield !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
/deep/ .addDia {
    .el-dialog__body {
        height: 600px;
    }
}

/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>
