<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按创建时间排序</el-radio>
                        <el-input @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" />
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table" v-loading="tableLoading">
                <el-table class="table" :height="rightTableHeight" :data="tableData"  border>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="供应商名称" width="" prop="enterpriseName"/>
                    <el-table-column label="店铺开通情况" width="" prop="shopId">
                      <template v-slot="scope">
                        <el-tag  v-if="scope.row.shopId" type="success">已开通</el-tag>
                        <el-tag  v-else type="danger">未开通</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="店铺名称" width="" prop="shopName">
                      <template v-slot="scope">
                        {{ scope.row.shopName || '--' }}
                      </template>
                    </el-table-column>
                    <el-table-column label="协议编号" width="" prop="agreementNo"/>
                    <el-table-column label="年费缴纳时间" width="" prop="annuaFeeCycle"/>
                    <el-table-column label="年费续费截止日期" width="" prop="serveEndTime"/>
                    <el-table-column label="累计交易金额" width="" prop="totalDealAmount"/>
                    <el-table-column label="累计交易服务费对账金额" width="" prop="totalDealFee"/>
<!--                    <el-table-column label="企业类型" width="">
                        <template v-slot="scope">
                            <span v-if="scope.row.enterpriseType == '0'">个体户</span>
                            <span v-else-if="scope.row.enterpriseType == '1'">企业</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="注册资金(万元)" width="">
                        <template v-slot="scope">
                            {{ scope.row.registeredCapital }}
                        </template>
                    </el-table-column>
                    <el-table-column label="注册时间" width="160">
                        <template v-slot="scope">
                            {{ scope.row.creationTime }}
                        </template>
                    </el-table-column>
                    <el-table-column label="营业执照有效期" width="160">
                        <template v-slot="scope">
                            <span v-if="scope.row.licenseTerm != null">{{ scope.row.licenseTerm }}</span>
                            <span v-else-if="scope.row.licenseTerm == null">长期</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业所在城市" width="">
                        <template v-slot="scope">
                                {{ scope.row.city }}
                        </template>
                    </el-table-column>
                    <el-table-column label="企业注册地址" width="">
                        <template v-slot="scope">
                                {{ scope.row.province }}{{ scope.row.county }}{{ scope.row.detailedAddress }}
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" width="160" prop="gmtCreate" />-->
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" >
                <el-row>
                    <el-col :span="12">
                        <el-form-item width="150px" label="供应商名称：" prop="enterpriseName">
                            <el-input v-model="filterData.enterpriseName" placeholder="请输入供应商名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="协议编号：" prop="contractNo">
                            <el-input v-model="filterData.contractNo" placeholder="请输入协议编号" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
 <!--                <el-row>
                                  <el-col :span="12">
                                      <el-form-item label="创建时间：">
                                          <el-date-picker
                                              value-format="yyyy-MM-dd HH:mm:ss"
                                              v-model="filterData.dateValue"
                                              type="datetimerange"
                                              range-separator="至"
                                              start-placeholder="开始日期"
                                              end-placeholder="结束日期">
                                          </el-date-picker>
                                      </el-form-item>
                                  </el-col>
                              </el-row>-->
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click = "confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
import { getListLedger, edit, create, del, batchPublish, batchNotPublish, batchDelete } from '@/api/platform/supplier/supplierAudit'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            tableLoading: false,
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            // 表格数据
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                enterpriseName: null,
                contractNo: null,
                orderBy: 2,
                dateValue: []
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        this.getTableData()
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    methods: {
        // 选中状态进行查询
        stateTopOptionsClick (value) {
            this.selectOptionValue = value
            this.getTableData()
        },
        resetSearchConditions () {
            this.filterData.contractNo = ''
            this.filterData.enterpriseName = ''
            this.filterData.dateValue = []
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                isSupplier: 2,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0],
                params.endDate = this.filterData.dateValue[1]
            }
            if(this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if(this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if(this.filterData.contractNo != null) {
                params.contractNo = this.filterData.contractNo
            }
            if(this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            this.tableLoading = true
            getListLedger(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            if(res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {min-width: 200px; padding: 0;}
.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;
    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {min-height: auto;}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}
/deep/ .el-dialog__body {
    margin-top: 0;
}
</style>
