<template>
    <div class="baseInfoRightT">
        <div class="baseInfoRight">
            <div class="title">{{ shop.isBusiness === 1 ? '平台自营' : '店铺信息' }}</div>
            <img @click="handleViewShop" :src="shop.shopImg ? imgUrlPrefixAdd + shop.shopImg : require('@/assets/images/img/queshen5.png')"
                alt="" />
        <div class="store dfc" @click="handleViewShop"><div class="router-usher textOverflow1" style="max-width: 220px;"><a>{{ shop.shopName }}</a></div></div>
            <div class="cell dfa" v-if="shop.shopType == '1'">
                <div class="cell_left">企业名称：</div>
                <div class="cell_right textOverflow1" :title="enterpriseInfo.enterpriseName">{{ enterpriseInfo.enterpriseName }}</div>
            </div>
            <div class="cell dfa">
                <div class="cell_left">店铺地址：</div>
                <div class="cell_right">{{ shop.detailedAddress }}</div>
            </div>
            <div class="cell dfa">
                <div class="cell_left">店铺主体：</div>
                <div class="cell_right">
                    <span>{{ ['个体户', '企业', '个人'][parseInt(shop.shopType)] }}</span>
                </div>
            </div>
            <div class="cell dfa">
                <div class="cell_left">路桥结算：</div>
                <div class="cell_right">
                    <span>{{ ['不支持', '支持'][parseInt(shop.isInternalSettlement)] }}</span>
                </div>
            </div>
        </div>
        <div class="score_detail">
            <div class="service_core">服务评分：
                <div class="cell_score">
                    <span class="score_val">{{shop.commentServiceScore}} </span>
                    <el-rate class="score_img" v-model="shop.commentServiceScore" :disabled='true' :max="5" :colors="['#FF0000','#FF0000','#FF0000']" />
                </div>
            </div>
            <div class="cell_score11">
                <div class="score_left">商品品质：<span class="score_c">{{shop.commentLevel}}</span>
                </div>
                <div class="score_right">保供能力：<span class="score_c">{{shop.commentSupply}}</span>
                </div>
            </div>
            <div class="cell_score11">
                <div class="score_left">诚信履约：<span class="score_c">{{shop.commentIntegrity}}</span>
                </div>
                <div class="score_right">服务水平：<span class="score_c">{{shop.commentService}}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { getShopInfo } from '@/api/frontStage/shop'
export default {
    name: 'simpleShop',
    props: ['shopId'],
    data () {
        return {
            value1: 4,
            shopInfoVo: {},
            shop: {},
            enterpriseInfo: {},
        }
    },
    created () {
        getShopInfo({ shopId: this.shopId }).then(res => {
            this.shopInfoVo = res
            this.shop = res.shop
            this.enterpriseInfo = res.enterpriseInfo
        })
    },
    methods: {
        // 查看店铺详情 f
        handleViewShop () {
            this.openWindowTab('/mFront/shopIndex?shopId=' + this.shopId)
        },
    }
}
</script>
<style scoped lang="scss">
.router-usher{
  cursor:pointer;
  a:hover{
    color: rgba(34, 111, 199, 1);
  }
}
.baseInfoRightT {
    .baseInfoRight {
        width: 260px;
        border: 1px solid rgba(229, 229, 229, 1);

        .title {
            width: 260px;
            height: 48px;
            opacity: 1;
            background: rgba(34, 111, 199, 1);
            text-align: center;
            line-height: 48px;
            color: #fff;
        }

        img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: block;
            margin: 0 auto;
            margin-top: 37px;
        }

        .store {
            font-size: 16px;
            font-weight: 400;
            color: rgba(0, 0, 0, 1);
            width: 100%;
            text-align: center;
            margin-top: 10px;
            //margin-bottom: 30px;
        }

        .cell {
            margin-bottom: 20px;

            .cell_left {
                min-width: 100px;
                padding: 0 20px;
                font-size: 14px;
                font-weight: 400;
                color: rgba(102, 102, 102, 1);
                padding-right: 2px;
            }

            .cell_right {
                font-size: 14px;
                font-weight: 400;
                color: rgba(0, 0, 0, 1);
            }
        }

    }
    .score_detail{
        border: none;
        position: relative;
        left: 24px;
        top: 8px;
        .service_core{

            .cell_score{
                background: #ffeee6;
                font-size: 18px;
                line-height: 30px;
                display: inline-block;
                border-radius: 6px;
                color: #ff0000;
                .score_val{
                    margin-left: 10px;
                }
                .score_img{
                    display: inline;
                    margin-right: 6px;
                    .el-rate__icon{
                        margin-right: 0px !important;
                    }
                }
            }
        }
        .cell_score11{
            margin-top: 8px;
            .score_left{
                display: inline;
                .score_c{
                    color: #669eb4;
                }
            }
            .score_right{
                display: inline-block;
                margin-left: 50px;
                .score_c{
                    color: #669eb4;
                }
            }
        }
    }
}
</style>