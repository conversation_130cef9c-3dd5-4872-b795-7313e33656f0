import service from '@/utils/request'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getBiddingPurchaseByPage = params => {
    return httpPost({
        url: '/materialMall/w/biddingPurchase/biddingPageList',
        params,
    })
}

const getBiddingPurchaseDetail = params => {
    return httpGet({
        url: '/materialMall/frontStage/biddingPurchase/getBidingDetail',
        params,
    })
}
const putBidingSupplierInfo = params => {
    return httpGet({
        url: '/materialMall/frontStage/biddingPurchase/putBidingSupplierInfo',
        params,
    })
}
const checkIsBiddingSupplier = params => {
    return httpGet({
        url: '/materialMall/frontStage/biddingPurchase/checkIsBiddingSupplier',
        params,
    })
}
const getBiddingProductInfo = params => {
    return httpPost({
        url: '/materialMall/biddingProduct/listByEntity',
        params,
    })
}

const getBiddingOrderInfo = params => {
    return httpGet({
        url: '/materialMall/orders/findById',
        params,
    })
}

const selectBidingPurchase = params => {
    return httpGet({
        url: '/materialMall/biddingProduct/selectBidingPurchaseByBidingId',
        params,
    })
}

const getBiddingProductByStSn = params => {
    return httpGet({
        url: '/materialMall/biddingProduct/getBiddingProductByStSn',
        params,
    })
}

const updateBiddingProductPrice = params => {
    return httpGet({
        url: '/materialMall/biddingProduct/updateBiddingProductPrice',
        params,
    })
}

export {
    updateBiddingProductPrice,
    getBiddingProductByStSn,
    getBiddingPurchaseByPage,
    getBiddingPurchaseDetail,
    getBiddingProductInfo,
    putBidingSupplierInfo,
    checkIsBiddingSupplier,
    getBiddingOrderInfo,
    selectBidingPurchase
}