<template>
    <div class="base-page">
        <div class="left" :style="{ height: '100%' }">
            <select-material-class ref="materialClassRef" :productType = "0"/>
        </div>
            <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="updateProductState(3)" class="btn-greenYellow">批量上架</el-button>
                            <el-button type="primary" @click="updateProductState(2)" class="btn-delete">批量下架</el-button>
                        </div>
                    </div>
                    <el-radio v-model="filterData.orderBy" :label="0">按上架时间</el-radio>
                    <el-radio v-model="filterData.orderBy" :label="2">按创建时间</el-radio>
                    <el-radio v-model="filterData.orderBy" :label="1">按排序值</el-radio>
                    <div class="search_box" style="width: 500px">
                            <el-input clearable type="text" @blur="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png"
                                    slot="suffix" @click="onSearch" /></el-input>
                            <div class="adverse">
                                <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                            </div>
                        </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table class="table" :height="rightTableHeight" :data="tableData"  border
                 @selection-change="selectionChangeHandle">
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column label="操作" width="130">
                    <template slot-scope="scope" >
                        <el-button style="padding:0px 8px 0px 8px;" v-if="scope.row.state===2 || scope.row.state===0"
                            size="mini"
                            type="success"
                            @click="updateState(scope.row,1,'上架')">上架</el-button>
                        <el-button style="padding:0px 8px 0px 8px;" v-if="scope.row.state===1 || scope.row.state===0"
                            size="mini"
                            type="danger"
                            @click="updateState(scope.row,2,'下架')">下架</el-button>
                    </template>
                </el-table-column>
                <!-- 商品名称 -->
                <el-table-column label="商品名称" width="200">
                    <template slot-scope="scope">
                        <span class="action" @click="handleView(scope.row)">{{scope.row.productName}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="店铺名称" width="200" prop="shopName"></el-table-column>
                <el-table-column label="状态" width="" prop="state">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.state==0">待上架</el-tag>
                        <el-tag v-if="scope.row.state==1" type="success">已上架</el-tag>
                        <el-tag v-if="scope.row.state==2" type="danger">已下架</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="类型" width="" prop="productType"></el-table-column>
                <el-table-column label="销售价" width="" prop="productMinPrice"></el-table-column>
                <el-table-column label="排序值" width="" prop="sort" />
                <el-table-column label="上架时间" width="160" prop="putawayDate" />
                <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                <el-table-column label="描述" width="300" prop="productDescribe"></el-table-column>
            </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
<!--        高级查询-->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="true" >
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="状态：">
                            <el-select @change="stateOptionsClick" v-model="filterData.selectStateTitle" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="价格以上：">
                            <el-input clearable type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                        <el-form-item label="价格以下：">
                            <el-input clearable type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click = "expertSearch">确定</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>

    </div>
</template>

<script>
import  '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '../../../../components/classTree'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapState, mapActions, mapMutations } from 'vuex'
import { getProductList, updateProductState } from '@/api/platform/product/shopProduct'
export default {
    components: {
        SelectMaterialClass, Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            // 表格数据
            dataListSelections: [], //选中的数据
            className: null,
            classId: null, // 分类id
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                belowPrice: null,
                abovePrice: null,
                dateValue: [], // 开始时间和结束时间
                selectStateTitle: null, // 选中的标题
                selectSateValue: null,  // 选中的值
                stateOptions: [{
                    value: null,
                    label: '全部'
                }, {
                    value: 0,
                    label: '待上架'
                }, {
                    value: 1,
                    label: '已上架'
                }, {
                    value: 2,
                    label: '已下架'
                }],
                orderBy: null,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        this.filterData.orderBy = 0
    },
    methods: {
        // 商品详情
        handleView (row) {
            row.className = this.className
            //利用$router.push进行跳转
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/product/shopProductDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shopProductDetail',
                params: {
                    row: row
                }
            })
        },
        // 高级搜索
        expertSearch () {
            this.keywords = null
            this.getTableData()
            //重置数据
            this.filterData.belowPrice = null // 以下价格
            this.filterData.abovePrice = null // 以上价格
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.selectStateTitle = null// 选中的标题
            this.filterData. selectSateValue = null // 选中的值
            this.queryVisible = false
        },
        // 高级搜索状态选中
        stateOptionsClick (value) {
            this.filterData.selectSateValue = value
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 单个上架
        updateState (row, state, title) {
            let productName = row.productName
            let params = {
                productIds: [row.productId],
                state: state
            }
            this.clientPop('info', '您确定要对商品【' + productName + '】进行【' + title + '】操作吗！', async () => {
                updateProductState(params).then(res => {
                    this.message(res)
                    this.getTableData()
                })
            })
        },
        // 批量修改上下架状态
        updateProductState (state) {
            if(this.dataListSelections.length == 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let params = {
                productIds: this.dataListSelections.map(item => {
                    return item.productId
                }),
                state: state
            }
            this.clientPop('info', '您确定要批量上下/下架这些商品吗！', async () => {
                showLoading()
                updateProductState(params).then(res => {
                    this.message(res)
                    this.getTableData()
                    hideLoading()
                })
                hideLoading()
            })
        },
        ...mapActions('equip', ['setUnitMeasur']),
        // 分类点击
        classNodeClick (data) {
            this.className = data.className
            this.classId = data.classId
            this.getTableData()
        },
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                product: 0,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.classId != null) {
                params.classId = this.classId
            }
            if(this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if(this.filterData.selectSateValue != null) {
                params.state = this.filterData.selectSateValue
            }
            if(this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0],
                params.endDate = this.filterData.dateValue[1]
            }
            if(this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if(this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if(this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            getProductList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            })
        },
        // 查询
        onSearch () {
            this.getTableData()
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }else {
                this.$message({
                    message: res.message,
                    type: 'error'
                })
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
.base-page .left {min-width: 200px; padding: 0;}
.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;
    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {min-height: auto;}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}
</style>
