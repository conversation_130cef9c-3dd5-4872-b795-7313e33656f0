<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="addReconciliationM">新增对账单</el-button>
                            <el-button v-if="this.showDevFunc" type="primary" class="btn-greenYellow" @click="addInvoiceM">开票</el-button>
<!--                            <el-button class="btn-greenYellow" type="primary" @click="batchSubmitCheck">批量提交</el-button>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="0">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按开始时间</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按结束时间</el-radio>
                        <el-input v-model="keywords" clearable placeholder="输入搜索关键字" style="width: 300px" type="text" @blur="handleInputSearch">
                            <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    ref="tableRef"
                    v-loading="tableLoading"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                    class="table"
                    @selection-change="tableSelectM"
                    @row-click="tableRowClickM"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="对账单编号" prop="reconciliationNo" width="190">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.reconciliationNo }}</span>
                        </template>
                    </el-table-column>
                  <el-table-column label="发票状态" width="100" prop="invoiceState" v-if="showDevFunc">
                    <template v-slot="scope">
                      <el-tag v-if="scope.row.invoiceState == 0&&scope.row.state == 3">未申请</el-tag>
                      <el-tag  type="info" v-if="scope.row.invoiceState == 1&&scope.row.state == 3">申请中</el-tag>
                      <el-tag  type="success" v-if="scope.row.invoiceState == 2&&scope.row.state == 3">已开票</el-tag>
                      <el-tag  type="danger" v-if="scope.row.invoiceState == 3&&scope.row.state == 3">申请被拒</el-tag>
                    </template>
                  </el-table-column>
                    <el-table-column label="对账类型" prop="type" width="100">
                        <template v-slot="scope">
                            <el-tag>{{ ['', '浮动价格对账', '固定价格对账'][scope.row.type] }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="新增来源类型" prop="type" width="130">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.createType == 1">收货单位新增</el-tag>
                            <el-tag v-if="scope.row.createType == 2">供货单位新增</el-tag>
                            <el-tag v-if="scope.row.createType == 3">PCWP推送</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="对账状态" prop="type" width="110">
                        <template v-slot="scope">
                            <el-tag
                                :type="['info', '', '', 'success', 'danger', '', '', 'danger'][scope.row.state]"
                            >
                                {{ ['草稿', '已提交', '待审核', '审核通过', '审核不通过', '', '', '已作废'][scope.row.state] }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="业务类型" prop="reconciliationProductType">
                        <template v-slot="scope">
                            <el-tag>
                                {{
                                    scope.row.reconciliationProductType === 0 || scope.row.reconciliationProductType === 10 ? '零星采购' :
                                    scope.row.reconciliationProductType === 1 || scope.row.reconciliationProductType === 13 ? '大宗临购' :
                                    scope.row.reconciliationProductType === 2 ? '周转材料' : ''
                                }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="收货单位" prop="purchasingOrgName" width="230"/>
                    <el-table-column label="供货单位" prop="supplierName" width="230"/>
                    <!-- <el-table-column label="收货单位是否确认" prop="type" width="140">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.purchaseIsAffirm == 0" type="info">否</el-tag>
                            <el-tag v-if="scope.row.purchaseIsAffirm == 1" type="success">是</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="供货单位是否确认" prop="type" width="140">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.supplierIsAffirm == 0" type="info">否</el-tag>
                            <el-tag v-if="scope.row.supplierIsAffirm == 1" type="success">是</el-tag>
                        </template>
                    </el-table-column> -->
                    <el-table-column label="含税总金额" prop="reconciliationAmount" width="130"/>
                    <el-table-column label="不含税总金额" prop="reconciliationNoRateAmount" width="150"/>
                    <el-table-column label="已结算金额" prop="settleAmount"/>
                    <el-table-column label="开始时间" prop="startTime" width="160"/>
                    <el-table-column label="结束时间" prop="endTime" width="160"/>
                    <el-table-column label="创建时间" prop="gmtCreate" width="160"/>
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData && tableData.length > 0"
                :currentPage.sync="paginationInfo.currentPage"
                :pageSize.sync="paginationInfo.pageSize"
                :total="paginationInfo.total"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="状态：">
                            <el-select v-model="filterData.state" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="对账类型：">
                            <el-select v-model="filterData.type" placeholder="请选择对账类型">
                                <el-option
                                    v-for="item in filterData.typeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="新增来源：">
                            <el-select v-model="filterData.createType" placeholder="请选择新增来源">
                                <el-option
                                    v-for="item in filterData.createTypeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="业务类型：">
                            <el-select v-model="filterData.reconciliationProductType" placeholder="请选择业务类型">
                                <el-option
                                    v-for="item in filterData.reconciliationProductTypeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="供应商名称：">
                            <el-input v-model="filterData.supplierName" clearable maxlength="100" placeholder="请输入供应商名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="采购员名称：">
                            <el-input v-model="filterData.purchasingOrgName" clearable maxlength="100" placeholder="请输入采购员名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="对账编号：">
                            <el-input v-model="filterData.reconciliationNo" clearable maxlength="100" placeholder="请输入对账编号"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="源单编号：">
                            <el-input v-model="filterData.sourceBillNo" clearable maxlength="100" placeholder="请输入源单编号"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog id="addreconciliationId" v-dialogDrag :close-on-click-modal="false" :visible.sync="showReconciliationForm" style="margin-left: 10%;" title="选择对账类型" width="50%">
            <el-form ref="bidingFormRef" :disabled="false" :model="reconciliationForm" class="demo-ruleForm" label-width="300px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="对账类型：" prop="type">
                            <el-radio v-model="reconciliationForm.type" :label="1">浮动价格对账</el-radio>
                            <el-radio v-model="reconciliationForm.type" :label="2">固定价格对账</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="buttons" style="position: absolute; right: 20px; bottom: 20px;">
                <el-button class="btn-blue" @click="addAffirmM">确认</el-button>
                <el-button @click="showReconciliationForm = false">返回</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
import { debounce } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { materialReconciliationSubmit, supplierListByEntity } from '@/api/reconciliation/reconciliation'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            tableSelectRow: [],
            reconciliationForm: {
                type: 1
            },
            showReconciliationForm: false,
            reconciliationFormLoading: false,
            tableLoading: false,
            // 状态选择查询
            // 表格数据
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                supplierName: null,
                sourceBillNo: null,
                reconciliationNo: null,
                purchasingOrgName: null,
                state: null,
                createType: null,
                reconciliationProductType: null,
                stateSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '草稿' },
                    { value: 1, label: '已提交' },
                    { value: 2, label: '待审核' },
                    { value: 3, label: '审核通过' },
                    { value: 4, label: '审核失败' },
                    { value: 7, label: '已作废' },
                ],
                type: null,
                typeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '浮动价格对账单' },
                    { value: 2, label: '固定价格对账单' },
                ],
                createTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '收货单位新增' },
                    { value: 2, label: '供货单位新增' },
                    { value: 3, label: 'PCWP推送' },
                ],
                reconciliationProductTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '零星采购' },
                    { value: 1, label: '大宗临购' },
                    { value: 2, label: '周转材料' },
                ],
                orderBy: 0,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        batchSubmitCheck () {
            if (this.tableSelectRow.length === 0) {
                return this.$message.error('请选择数据！')
            }
            let ids = this.tableSelectRow.filter(t => {
                return t.state == 0
            }).map(item => {
                return item.reconciliationId
            })
            if (ids.length == 0) {
                return this.$message.warning('请勾选有效的数据！')
            }
            this.clientPop('info', '您确定要批量提交这些数据吗？', async () => {
                this.tableLoading = true
                materialReconciliationSubmit(ids).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getTableData()
                    }
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        // 选择类型确认
        addAffirmM () {
            if (this.reconciliationForm.type == 1) {
                this.showReconciliationForm = false
                this.$router.push({
                    path: '/performance/sheet/floatDetail',
                    name: 'supplierSysFloatDetail',
                    query: {
                        type: this.reconciliationForm.type
                    }
                })
            }
            if (this.reconciliationForm.type == 2) {
                this.showReconciliationForm = false
                this.$router.push({
                    path: '/performance/sheet/fixationDetail',
                    name: 'supplierSysFixationDetail',
                    query: {
                        type: this.reconciliationForm.type
                    }
                })
            }
        },
        // 新增对账单点击
        addReconciliationM () {
            this.showReconciliationForm = true
        },

        addInvoiceM () {
            if (this.tableSelectRow.length === 0) {
                return this.$message.error('请选择数据！')
            }
            let ids =  this.tableSelectRow.filter(t => {
                return t.state === 3 && (t.invoiceState === 0 || t.invoiceState === 3)
            })
            if (ids.length == 0) {
                return this.$message.warning('请勾选有效的数据！')
            }
            let oneTaxRate = ids[0].taxRate
            let onesupplierOrgId = ids[0].supplierId
            for (let i = 0; i < ids.length; i++) {
                if (oneTaxRate != ids[i].taxRate || onesupplierOrgId != ids[i].supplierId) {
                    return this.$message.warning('对账单编号' +
                        ids[i].reconciliationNo + '收货单位：' + ids[i].purchasingOrgName + '，税率：' +
                        ids[i].taxRate + '%和其他不符合，不能一起开票')
                }
            }
            let reconciliationIds =  ids.map(item => {
                return item.reconciliationId
            })
            this.$router.push({
                path: '/supplierSys/supplierApply/invoice/apply',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'supplierSysApply',
                params: {
                    row: {
                        reconciliationIds: reconciliationIds,
                        supplierName: ids[0].supplierName,
                        taxRate: ids[0].taxRate
                    }
                }
            })
        },
        // 详情
        handleView (row) {
            if (row.type == 1) {
                this.$router.push({
                    path: '/supplierSys/floatDetailInfo',
                    name: 'supplierSysFloatDetailInfo',
                    query: {
                        sn: row.reconciliationNo
                    }
                })
            }
            if (row.type == 2) {
                this.$router.push({
                    path: '/supplierSys/fixationDetailInfo',
                    name: 'supplierSysFixationDetailInfo',
                    query: {
                        sn: row.reconciliationNo
                    }
                })
            }
        },
        resetSearchConditions () {
            //重置数据
            this.filterData.purchasingOrgName = null
            this.filterData.supplierName = null
            this.filterData.sourceBillNo = null
            this.filterData.reconciliationNo = null
            this.filterData.state = null
            this.filterData.createType = null
            this.filterData.type = null
            this.filterData.reconciliationProductType = null
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if (this.filterData.createType != null) {
                params.createType = this.filterData.createType
            }
            if (this.filterData.purchasingOrgName != null) {
                params.purchasingOrgName = this.filterData.purchasingOrgName
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.sourceBillNo != null) {
                params.sourceBillNo = this.filterData.sourceBillNo
            }
            if (this.filterData.reconciliationNo != null) {
                params.reconciliationNo = this.filterData.reconciliationNo
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.filterData.type != null) {
                params.type = this.filterData.type
            }
            if (this.filterData.reconciliationProductType != null) {
                params.reconciliationProductType = this.filterData.reconciliationProductType
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            this.tableLoading = true
            supplierListByEntity(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
</style>
