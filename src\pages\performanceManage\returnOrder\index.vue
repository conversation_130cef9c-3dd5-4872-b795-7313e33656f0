<!--<template>-->
<!--    <main class="userCenter" v-loading="showLoading">-->
<!--        <div class="title">退货申请</div>-->
<!--        <div class="content p20">-->
<!--            <div class="tabs mb20 dfb">-->
<!--                <div class="tab df">-->
<!--                    <div :class="activeTab == -1 ? 'active' : ''" @click="checkActiveTab(-1)">全部订单</div>-->
<!--                    <div :class="activeTab == 6 ? 'active' : ''" @click="checkActiveTab(6)">待发货</div>-->
<!--                    <div :class="activeTab == 8 ? 'active' : ''" @click="checkActiveTab(8)">已发货</div>-->
<!--                    <div :class="activeTab == 9 ? 'active' : ''" @click="checkActiveTab(9)">待收货</div>-->
<!--                    <div :class="activeTab == 10 ? 'active' : ''" @click="checkActiveTab(10)">已完成</div>-->
<!--                </div>-->
<!--                <div class="search df">-->
<!--                    <div class="box dfa">-->
<!--                        <img src="@/assets/images/ico_search.png" alt="">-->
<!--                        <input v-model="keyword" type="text" placeholder="商品名称/订单号">-->
<!--                    </div>-->
<!--                    <button @click="onSearch">搜索</button>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="titleBar mb20 dfa">-->
<!--                <el-select v-model="selectedVal" value-key="" @change="handleFilter">-->
<!--                    <el-option v-for="item in selectOptions"-->
<!--                               :key="item.value"-->
<!--                               :label="item.label"-->
<!--                               :value="item.value">-->
<!--                    </el-option>-->
<!--                </el-select>-->
<!--                <span>订单详情</span>-->
<!--                <span>收货人</span>-->
<!--                &lt;!&ndash;        <span>金额</span>&ndash;&gt;-->
<!--                <span>状态</span>-->
<!--                <span>操作</span>-->
<!--            </div>-->
<!--            <div class="orderList">-->
<!--                <div class="item mb10" v-for="item in list" :key="item.id">-->
<!--                    <div class="itemHead dfa">-->
<!--                        <div class="left">-->
<!--                            <span>{{ item.createTime }}</span><span>订单号：{{ item.orderNum }}</span>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="itemContent df">-->
<!--                        <p style="width: 316px;white-space:normal;word-break:break-all;">{{ item.title }}</p>-->
<!--                        <div class="quantity">x{{ item.quantity }}</div>-->
<!--                        <div class="cs"><span class="pointer" v-if="item.status == 4" @click="applyCustomerService(item.orderId)">申请售后</span></div>-->
<!--                        <div class="receiver">{{ item.receiver }}</div>-->
<!--                        <div class="status">-->
<!--                            <div class="mb10">-->
<!--                                <span v-if="item.status == 0">草稿</span>-->
<!--                                <span v-if="item.status == 1">已提交</span>-->
<!--                                <span v-if="item.status == 2">待确认</span>-->
<!--                                <span v-if="item.status == 3">已确认</span>-->
<!--                                <span v-if="item.status == 4">待签订合</span>-->
<!--                                <span v-if="item.status == 5">已签合同</span>-->
<!--                                <span v-if="item.status == 6">待发货</span>-->
<!--                                <span v-if="item.status == 7">已关闭</span>-->
<!--                                <span v-if="item.status == 8">发货中</span>-->
<!--                                <span v-if="item.status == 9">待收货</span>-->
<!--                                <span v-if="item.status == 10">已完成</span>-->
<!--                            </div>-->
<!--                            <div class="pointer"  style="color: #226fc7;" @click="handleViewDetail(item.orderNum)">订单详情</div>-->
<!--                        </div>-->
<!--                        <div class="status">-->
<!--                            &lt;!&ndash;              <div style="color: #d43030;" v-if="item.status == 2" @click="handleOrderSettlement(item.orderId)">付款&ndash;&gt;-->
<!--                            &lt;!&ndash;              </div>&ndash;&gt;-->
<!--                            &lt;!&ndash;                                <span style="color: #226fc7;" v-if="item.status == 7" @click="handleOrderReceive(item.orderId)">确认收货/</span>&ndash;&gt;-->
<!--                            <div class="pointer"   style="color: #226fc7;" v-if="item.status == 10" >-->
<!--                                <div v-if="item.invoiceState==1">已申请发票</div>-->
<!--                                <div  v-else @click="invoiceApply(item)">-->
<!--                                    申请发票-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="pointer"   style="color: #226fc7; margin-top: 10px"  v-if="item.status == 10||item.status==6" >-->
<!--                                <span v-if="(item.returnState==null||item.returnState==0)&&item.productType!=10 " @click="orderReturn(item)" >申请退货</span>-->
<!--                                <span v-show="item.status==6&&item.productType==10&&(item.returnState==null||item.returnState==0)" @click="orderReturn(item)" >申请退货</span>-->
<!--                                <span v-show="item.returnState=='1'" >审核中</span>-->
<!--                                <span style="color: #d43030;" v-show="item.returnState=='2'"   >退货中</span>-->
<!--                                <span v-show="item.returnState=='3'"  >退货失败</span>-->
<!--                                <span style="color: limegreen;" v-show="item.returnState=='4'"  >退货失败</span>-->
<!--                            </div>-->
<!--                            <div>-->
<!--                                &lt;!&ndash;                                <span v-if="item.status == 0" @click="cancelOrder(item.orderId)">取消订单</span>&ndash;&gt;-->
<!--                                &lt;!&ndash;                                <span v-else @click="handlePurchase(item.orderId)">再次购买</span>&ndash;&gt;-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"-->
<!--                        :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange">-->
<!--            </pagination>-->
<!--        </div>-->
<!--    </main>-->
<!--</template>-->
<!--<script>-->
<!--import pagination from '@/pages/frontStage/components/pagination'-->
<!--import { getUserOrderPageList } from '@/api/frontStage/order'-->
<!--export default {-->
<!--    components: { pagination },-->
<!--    data () {-->
<!--        return {-->
<!--            showLoading: false,-->
<!--            activeTab: -1,-->
<!--            selectedVal: 0,-->
<!--            keyword: null,-->
<!--            state: null,-->
<!--            selectOptions: [-->
<!--                { label: '近一个月订单', value: 0 },-->
<!--                { label: '近三个月订单', value: 1 },-->
<!--                { label: '近半年订单', value: 2 },-->
<!--                { label: '全部订单', value: 3 },-->
<!--            ],-->
<!--            list: [-->
<!--            ],-->
<!--            pagination: {-->
<!--                currPage: 1, //当前页-->
<!--                destination: null,-->
<!--                pageSize: 3, // 显示数量-->
<!--                totalNum: null,-->
<!--                totalPage: null,-->
<!--            },-->
<!--            destination: 2,-->
<!--        }-->
<!--    },-->
<!--    watch: {-->
<!--        activeTab (num) {-->
<!--            if(num == -1) {-->
<!--                this.state = null-->
<!--            }-->
<!--            if(num == 0) {-->
<!--                this.state = 0-->
<!--            }-->
<!--            if(num == 6) {-->
<!--                this.state = 6-->
<!--            }-->
<!--            if(num == 7) {-->
<!--                this.state = 7-->
<!--            }-->
<!--            if(num == 8) {-->
<!--                this.state = 8-->
<!--            }-->
<!--            if(num == 9) {-->
<!--                this.state = 9-->
<!--            }-->
<!--            if(num == 10) {-->
<!--                this.state = 10-->
<!--            }-->
<!--            this.getUserOrderPageListM()-->
<!--        },-->
<!--    },-->
<!--    created () {-->
<!--        this.getUserOrderPageListM()-->
<!--    },-->
<!--    mounted () {},-->
<!--    methods: {-->
<!--        // 标签点击-->
<!--        checkActiveTab (num) {-->
<!--            this.activeTab = num-->
<!--        },-->
<!--        invoiceApply (item) {-->
<!--            this.$router.push(-->
<!--                { path: '/user/myInvoice/invoice/company',-->
<!--                    name: 'companyApply',-->
<!--                    params: {-->
<!--                        row: item-->
<!--                    }-->
<!--                })-->
<!--        },-->
<!--        orderReturn (item) {-->
<!--            this.$router.push(-->
<!--                { path: '/user/refund/unshipped',-->
<!--                    name: 'refundApply',-->
<!--                    params: {-->
<!--                        row: item-->
<!--                    }-->
<!--                })-->
<!--        },-->
<!--        getLastMonth () {-->
<!--            let now = new Date()-->
<!--            let year = now.getFullYear()-->
<!--            let month = now.getMonth() + 1//0-11表示1-12月-->
<!--            let day = now.getDate()-->
<!--            let dateObj = {}-->
<!--            dateObj.now = year + '-' + month + '-' + day-->
<!--            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数-->
<!--            if(month - 1 <= 0) { //如果是1月，年数往前推一年<br>-->
<!--                dateObj.last = (year - 1) + '-' + 12 + '-' + day-->
<!--            }else{-->
<!--                let lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate()-->
<!--                if(lastMonthDay < day) {    // 1个月前所在月的总天数小于现在的天日期-->
<!--                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数-->
<!--                        dateObj.last = year + '-' + (month - 1) + '-' + (lastMonthDay - (nowMonthDay - day))-->
<!--                    }else{-->
<!--                        dateObj.last = year + '-' + (month - 1) + '-' + lastMonthDay-->
<!--                    }-->
<!--                }else{-->
<!--                    dateObj.last = year + '-' + (month - 1) + '-' + day-->
<!--                }-->
<!--            }-->
<!--            return dateObj-->
<!--        },-->
<!--        getLast3Month () {-->
<!--            let now = new Date()-->
<!--            let year = now.getFullYear()-->
<!--            let month = now.getMonth() + 1//0-11表示1-12月-->
<!--            let day = now.getDate()-->
<!--            let dateObj = {}-->
<!--            dateObj.now = year + '-' + month + '-' + day-->
<!--            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数-->
<!--            if(month - 3 <= 0) { // 如果是1、2、3月，年数往前推一年-->
<!--                var last3MonthDay1 = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate()    // 3个月前所在月的总天数-->
<!--                if(last3MonthDay1 < day) {    // 3个月前所在月的总天数小于现在的天日期-->
<!--                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + last3MonthDay1-->
<!--                }else{-->
<!--                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + day-->
<!--                }-->
<!--            }else{-->
<!--                let last3MonthDay2 = new Date(year, (parseInt(month) - 3), 0).getDate()    //3个月前所在月的总天数-->
<!--                if(last3MonthDay2 < day) {    //3个月前所在月的总天数小于现在的天日期-->
<!--                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份-->
<!--                        dateObj.last = year + '-' + (month - 3) + '-' + (last3MonthDay2 - (nowMonthDay - day))-->
<!--                    }else{-->
<!--                        dateObj.last = year + '-' + (month - 3) + '-' + last3MonthDay2-->
<!--                    }-->
<!--                }else{-->
<!--                    dateObj.last = year + '-' + (month - 3) + '-' + day-->
<!--                }-->
<!--            }-->
<!--            return dateObj-->
<!--        },-->
<!--        getLast6Month () {-->
<!--            let now = new Date()-->
<!--            let year = now.getFullYear()-->
<!--            let month = now.getMonth() + 1//0-11表示1-12月-->
<!--            let day = now.getDate()-->
<!--            let dateObj = {}-->
<!--            dateObj.now = year + '-' + month + '-' + day-->
<!--            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数-->
<!--            if(month - 6 <= 0) { // 年数往前推一年-->
<!--                let last6MonthDay1 = new Date((year - 1), (12 - (6 - parseInt(month))), 0).getDate()    // 6个月前所在月的总天数-->
<!--                if(last6MonthDay1 < day) {    // 6个月前所在月的总天数小于现在的天日期-->
<!--                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + last6MonthDay1-->
<!--                }else{-->
<!--                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + day-->
<!--                }-->
<!--            }else{-->
<!--                let last6MonthDay2 = new Date(year, (parseInt(month) - 6), 0).getDate()    //6个月前所在月的总天数-->
<!--                if(last6MonthDay2 < day) {    //6个月前所在月的总天数小于现在的天日期-->
<!--                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份-->
<!--                        dateObj.last = year + '-' + (month - 6) + '-' + (last6MonthDay2 - (nowMonthDay - day))-->
<!--                    }else{-->
<!--                        dateObj.last = year + '-' + (month - 6) + '-' + last6MonthDay2-->
<!--                    }-->
<!--                }else{-->
<!--                    dateObj.last = year + '-' + (month - 6) + '-' + day-->
<!--                }-->
<!--            }-->
<!--            return dateObj-->
<!--        },-->
<!--        getUserOrderPageListM () {-->
<!--            let params = {-->
<!--                page: this.pagination.currPage,-->
<!--                limit: this.pagination.pageSize,-->
<!--                productType: 12-->
<!--            }-->
<!--            if(this.state >= 0) {-->
<!--                params.state = this.state-->
<!--            }-->
<!--            if(this.selectedVal === 0) {-->
<!--                let dateObj = this.getLastMonth()-->
<!--                params.startDate = dateObj.last + ' 00:00:00'-->
<!--                params.endDate = dateObj.now + ' 23:59:59'-->
<!--            }-->
<!--            if(this.selectedVal === 1) {-->
<!--                let dateObj = this.getLast3Month()-->
<!--                params.startDate = dateObj.last + ' 00:00:00'-->
<!--                params.endDate = dateObj.now + ' 23:59:59'-->
<!--            }-->
<!--            if(this.selectedVal === 2) {-->
<!--                let dateObj = this.getLast6Month()-->
<!--                params.startDate = dateObj.last + ' 00:00:00'-->
<!--                params.endDate = dateObj.now + ' 23:59:59'-->
<!--            }-->
<!--            if(this.keyword != null) {-->
<!--                params.keywords = this.keyword-->
<!--            }-->
<!--            this.showLoading = true-->
<!--            getUserOrderPageList(params).then(res => {-->
<!--                this.list = []-->
<!--                res.list.forEach(t => {-->
<!--                    this.list.push({-->
<!--                        productId: t.productId,-->
<!--                        shipCounts: t.shipCounts,-->
<!--                        shopId: t.shopId,-->
<!--                        orderId: t.orderId,-->
<!--                        orderNum: t.orderSn,-->
<!--                        pictureUrl: t.productImg,-->
<!--                        title: t.untitled,-->
<!--                        returnCount: t.returnCount,-->
<!--                        quantity: t.buyCounts,-->
<!--                        receiver: t.receiverName,-->
<!--                        price: t.actualAmount,-->
<!--                        status: t.state,-->
<!--                        productType: t.productType,-->
<!--                        createTime: t.gmtCreate,-->
<!--                        invoiceState: t.invoiceState,-->
<!--                        returnState: t.returnState,-->
<!--                        brand: t.brand,-->
<!--                        orderItemId: t.orderItemId-->
<!--                    })-->
<!--                })-->
<!--                this.pagination.currPage = res.currPage-->
<!--                this.pagination.pageSize = res.pageSize-->
<!--                this.pagination.totalNum =  res.totalCount-->
<!--                this.pagination.totalPage =  res.totalPage-->
<!--                this.showLoading = false-->
<!--            }).catch(() => {-->
<!--                this.showLoading = false-->
<!--            })-->
<!--        },-->
<!--        currentChange (index) {-->
<!--            this.pagination.currPage = index-->
<!--            this.getUserOrderPageListM()-->
<!--        },-->
<!--        sizeChange (size) {-->
<!--            this.pagination.pageSize = size-->
<!--            this.getUserOrderPageListM()-->
<!--        },-->
<!--        // 下拉筛选-->
<!--        handleFilter () {-->
<!--            this.getUserOrderPageListM()-->
<!--        },-->
<!--        // 搜索-->
<!--        onSearch () {-->
<!--            this.getUserOrderPageListM()-->
<!--        },-->
<!--        // 申请售后-->
<!--        applyCustomerService (id) {-->
<!--            console.log(id)-->
<!--        },-->
<!--        // 跳转订单详情页面-->
<!--        handleViewDetail (id) {-->
<!--            this.$router.push({ path: '/user/synthesisMaterialDetail', query: { orderSn: id } })-->
<!--        },-->
<!--        // 评价订单-->
<!--        handleOrderReview (id) {-->
<!--            console.log(id)-->
<!--        },-->
<!--        // 订单付款-->
<!--        handleOrderSettlement (id) {-->
<!--            console.log(id)-->
<!--        },-->
<!--        // 取消订单-->
<!--        cancelOrder (id) {-->
<!--            console.log(id)-->
<!--        },-->
<!--        // 再次购买-->
<!--        handlePurchase (id) {-->
<!--            console.log(id)-->
<!--        },-->
<!--        // 确认收货-->
<!--        handleOrderReceive (id) {-->
<!--            console.log(id)-->
<!--        },-->
<!--    },-->
<!--}-->
<!--</script>-->
<!--<style scoped lang="scss">-->
<!--main {border: 1px solid rgba(230, 230, 230, 1);}-->
<!--.content {-->
<!--    height: 824px;-->
<!--    .tabs {-->
<!--        // margin-top: 20px;-->
<!--        .tab {-->
<!--            font-size: 16px;-->
<!--            color: rgba(102, 102, 102, 1);-->
<!--            div {-->
<!--                margin-right: 50px;-->
<!--                cursor: pointer;-->
<!--            }-->
<!--            .active {-->
<!--                color: rgba(0, 0, 0, 1);-->
<!--                &::after {-->
<!--                    content: '';-->
<!--                    display: block;-->
<!--                    width: 100%;-->
<!--                    height: 2px;-->
<!--                    margin-top: 4px;-->
<!--                    background-color: rgba(34, 111, 199, 1);-->
<!--                }-->
<!--            }-->
<!--        }-->
<!--        .search {-->
<!--            .box {-->
<!--                width: 268px;-->
<!--                height: 26px;-->
<!--                border: 1px solid rgba(229, 229, 229, 1);-->
<!--                border-right: 0;-->
<!--                img {-->
<!--                    width: 16px;-->
<!--                    height: 16px;-->
<!--                    margin: 0 4px 0 10px;-->
<!--                }-->
<!--                input {width: 230px;}-->
<!--            }-->
<!--            button {-->
<!--                width: 52px;-->
<!--                height: 26px;-->
<!--                font-size: 14px;-->
<!--                color: #fff;-->
<!--                background-color: rgba(212, 48, 48, 1);-->
<!--            }-->
<!--        }-->
<!--    }-->
<!--    .titleBar {-->
<!--        height: 50px;-->
<!--        padding: 0 20px;-->
<!--        border: 1px solid rgba(230, 230, 230, 1);-->
<!--        background-color: rgba(250, 250, 250, 1);-->
<!--        /deep/ .el-select {-->
<!--            margin-right: 54px;-->
<!--            &, .el-input {width: 104px;}-->
<!--            .el-input__inner {-->
<!--                padding-left: 0;-->
<!--                padding-right: 20px;-->
<!--                border: 0;-->
<!--                color: rgba(0, 0, 0, 1);-->
<!--            }-->
<!--            .el-select__caret {-->
<!--                //background-image: url(../../../../assets/images/userCenter/arrow_up.png);-->
<!--                background-position: 50% 50%;-->
<!--                background-repeat: no-repeat;-->
<!--                &::before{content: '';}-->
<!--            }-->
<!--        }-->
<!--        // /deep/ .el-select-dropdown__item span {font-family: MiSans !important;}-->
<!--        span:nth-of-type(1) {margin-right: 395px !important;}-->
<!--        span:not(:last-of-type) {margin-right: 83px;}-->
<!--    }-->
<!--    .orderList {-->
<!--        height: 576px;-->
<!--        .item {-->
<!--            border: 1px solid rgba(230, 230, 230, 1);-->
<!--            .itemHead {-->
<!--                height: 40px;-->
<!--                padding: 0 20px;-->
<!--                border-bottom: 1px solid rgba(230, 230, 230, 1);-->
<!--                background-color: rgba(250, 250, 250, 1);-->
<!--                position: relative;-->
<!--                .left {-->
<!--                    color: rgba(51, 51, 51, 1);-->
<!--                    span {margin-right: 24px;}-->
<!--                }-->
<!--                .right {-->
<!--                    width: 95px;-->
<!--                    font-size: 12px;-->
<!--                    color: rgba(153, 153, 153, 1);-->
<!--                    position: absolute;-->
<!--                    right: 20px;-->
<!--                    img {-->
<!--                        width: 16px;-->
<!--                        height: 16px;-->
<!--                        margin-right: 4px;-->
<!--                    }-->
<!--                }-->
<!--            }-->
<!--            .itemContent {-->
<!--                height: 140px;-->
<!--                padding: 30px 20px;-->
<!--                img {-->
<!--                    width: 80px;-->
<!--                    height: 80px;-->
<!--                    margin-right: 16px;-->
<!--                }-->
<!--                p {-->
<!--                    width: 236px;-->
<!--                    margin-right: 20px;-->
<!--                    color: rgba(51, 51, 51, 1);-->
<!--                }-->
<!--                .quantity {-->
<!--                    width: 55px;-->
<!--                }-->
<!--                .cs {-->
<!--                    width: 156px;-->
<!--                    margin-right: 46px;-->
<!--                }-->
<!--                .receiver {-->
<!--                    width: 100px;-->
<!--                    margin-right: 25px;-->
<!--                }-->
<!--                .price, .status {-->
<!--                    width: 100px;-->
<!--                    margin-right: 11px;-->
<!--                }-->
<!--                .actions span {cursor: pointer;}-->
<!--            }-->
<!--        }-->
<!--    }-->
<!--}-->
<!--</style>-->