<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="退货信息" name="baseInfo" :disabled="clickTabFlag"></el-tab-pane>
                <el-tab-pane label="退货项信息" name="orderItenInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">退货信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单号：">
                                            <span>{{ formData.orderSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="退货编号：">
                                            <span>{{ formData.orderReturnNo }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12" v-if="formData.orderClass!==1">
                                        <el-form-item label="二级订单编号：">
                                            <span>{{ formData.otherOrderSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="二级供应商名称：">
                                            <span>{{ formData.shipEnterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="采购机构名称：">
                                            <span>{{ formData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商名称：">
                                            <span>{{ formData.supplierName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row v-if="formData.orderClass!=2">
                                    <el-col :span="12">
                                        <el-form-item label="二级供应商总金额：">
                                            <span>{{ formData.otherTotalAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="二级供应商含税金额：">
                                            <span>{{ formData.otherRateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>

                                    <!--                  <el-col :span="12">-->
                                    <!--                    <el-form-item label="收件人：">-->
                                    <!--                      <span>{{ formData.receiverName }}</span>-->
                                    <!--                    </el-form-item>-->
                                    <!--                  </el-col>-->
                                    <!--                </el-row>-->
                                    <!--                <el-row>-->
                                    <!--                  <el-col :span="12">-->
                                    <!--                    <el-form-item label="收件人手机号：">-->
                                    <!--                      <span>{{ formData.receiverMobile }}</span>-->
                                    <!--                    </el-form-item>-->
                                    <!--                  </el-col>-->
                                    <!--                  <el-col :span="12">-->
                                    <!--                    <el-form-item label="收件地址：">-->
                                    <!--                      <span>{{ formData.receiverAddress }}</span>-->
                                    <!--                    </el-form-item>-->
                                    <!--                  </el-col>-->
                                    <!--                </el-row>-->
                                    <!--                <el-row>-->
                                    <el-col :span="12">
                                        <el-form-item label="总金额：">
                                            <span>{{ formData.totalAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="含税金额：">
                                            <span>{{ formData.rateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="退货来源：">
                                            <span v-show="formData.isOut==0">商城退货</span>
                                            <span v-show="formData.isOut==1">PCWP退货</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="订单类型：">
                                            <span v-if="formData.orderClass==1">普通订单</span>
                                            <span v-else >多供方订单</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单状态：">
                                            <el-tag v-if="showTableState(formData)">{{ tableStateTitle }}</el-tag>
                                            <el-tag v-else type="danger">已关闭</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="退货原因：">
                                            <el-tag v-if="formData.submitReason==1">7天无理由退货</el-tag>
                                            <el-tag v-if="formData.submitReason==2">不想要了</el-tag>
                                            <el-tag v-if="formData.submitReason==3">物流太慢</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!--                <el-row>-->
                                <!--                  <el-col :span="12">-->
                                <!--                    <el-form-item label="订单运费：">-->
                                <!--                      <span>{{ formData.orderFreight }}</span>-->
                                <!--                    </el-form-item>-->
                                <!--                  </el-col>-->

                                <!--                </el-row>-->
                                <el-row v-show="formData.returnMethod==2">

                                    <el-col :span="12" >
                                        <el-form-item label="物流单号：">
                                            <span>{{ formData.deliveryFlowId }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="物流公司：">
                                            <span>{{ formData.logisticsCompany }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="完成时间：">
                                            <span>{{ formData.flishTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="取消时间：">
                                            <span>{{ formData.cancelTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!--                <el-row>-->
                                <!--                  <el-col :span="24">-->
                                <!--                    <el-form-item label="商品图片：">-->
                                <!--                      <template slot-scope="scope">-->
                                <!--                        <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.pictureUrl" fit="cover"></el-image>-->
                                <!--                      </template>-->
                                <!--                    </el-form-item>-->
                                <!--                  </el-col>-->
                                <!--                </el-row>-->
                                <!--                <el-row>-->
                                <!--                  <el-col :span="12">-->
                                <!--                    <el-form-item label="订单关闭类型：">-->
                                <!--                      <span>{{ formData.closeType }}</span>-->
                                <!--                    </el-form-item>-->
                                <!--                  </el-col>-->
                                <!--                  <el-col :span="12">-->
                                <!--                    <el-form-item label="支付交易号：">-->
                                <!--                      <span>{{ formData.tradeNo }}</span>-->
                                <!--                    </el-form-item>-->
                                <!--                  </el-col>-->
                                <!--                </el-row>-->
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="创建时间：">
                                            <span>{{ formData.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item label="退货详情：">
                                            <span>{{ formData.remarks }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 订单商品-->
                    <div id="orderItenInfo" class="con" v-loading="tableLoading">
                        <div class="tabs-title" id="contractList">退货项信息</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table ref="tableRef"
                                      border
                                      height="200px"
                                      style="width: 100%"
                                      :data="orderItemtableData"
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="productName" label="商品名称" width=""></el-table-column>
                                <el-table-column prop="skuName" label="规格" width=""></el-table-column>
                                <el-table-column prop="productImg" label="商品图片" width="130">
                                    <template slot-scope="scope">
                                        <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="productPrice" label="单价" width="">
                                </el-table-column>
                                <el-table-column prop="buyCounts" label="购买数量" width=""></el-table-column>
                                <el-table-column prop="count" label="退货数量" width=""></el-table-column>
                                <el-table-column prop="totalAmount" label="退货金额" width=""></el-table-column>
                                <!--                                <el-table-column prop="buyTime" label="购买时间" width="160"></el-table-column>-->
                                <!--                                <el-table-column prop="remarks" label="备注" width="200"></el-table-column>-->
                            </el-table>
                        </div>
                        <!--            分页-->
                        <!--                        <Pagination-->
                        <!--                            v-show="tableData != null || tableData.length != 0"-->
                        <!--                            :total="paginationInfo.total"-->
                        <!--                            :pageSize.sync="paginationInfo.pageSize"-->
                        <!--                            :currentPage.sync="paginationInfo.currentPage"-->
                        <!--                            @currentChange="getTableData"-->
                        <!--                            @sizeChange="getTableData"-->
                        <!--                        />-->
                    </div>

                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button v-show="formData.state==1" type="success" @click="openRetuen(3)">通过</el-button>
            <el-button  v-show="formData.state==1" type="danger" @click="updatDate(4)">不通过</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
        <!--        <el-dialog title="填写物流信息" :visible.sync="queryVisible" width="50%" :close-on-click-modal="false" >-->
        <!--            <el-form :model="orderShip" ref="form" label-width="120px" :inline="false" size="normal">-->
        <!--                <el-row>-->
        <!--                    <el-col :span="12" :offset="0">-->
        <!--                        <el-form-item label="物流单号：" >-->
        <!--                            <el-input clearable maxlength="100" placeholder="请输入物流单号" v-model="orderShip.deliveryFlowId"></el-input>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->
        <!--                </el-row>-->
        <!--                <el-row>-->
        <!--                    <el-col :span="12" :offset="0">-->
        <!--                        <el-form-item label="物流公司：" >-->
        <!--                            <el-input clearable maxlength="100" placeholder="请输入物流公司" v-model="orderShip.logisticsCompany"></el-input>-->
        <!--                        </el-form-item>-->
        <!--                    </el-col>-->
        <!--                </el-row>-->
        <!--            </el-form>-->
        <!--            <span slot="footer">-->
        <!--                <el-button type="primary" @click = "createShip">确定</el-button>-->
        <!--                <el-button @click="queryVisible = false">取消</el-button>-->
        <!--            </span>-->
        <!--        </el-dialog>-->
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState, mapMutations } from 'vuex'
import { throttle } from '@/utils/common'
import $ from 'jquery'
// import Pagination from '@/components/pagination/pagination'
import {
    getOrderReturnDateById,
    updateOrderReturnState,
    getListByOrderReturnId,
} from '@/api/shopManage/refund/refundapply'
export default {

    data () {
        return {
            filterData: {
                sendAddress: '',
                receiverMobile: '',
                deliveryFlowId: '',
                logisticsCompany: '',
                receiverName: '',

            },
            formLoading: false,
            queryVisible: false,
            tableStateTitle: null, // 状态标题
            orderReturnId: '', // 状态标题
            tableLoading: false,
            orderItemtableData: [],
            stateOptions: [
                {
                    value: null,
                    label: '全部'
                }, {
                    value: 1,
                    label: '已申请'
                }, {
                    value: 2,
                    label: '退货中'
                }, {
                    value: 3,
                    label: '退货成功'
                }, {
                    value: 4,
                    label: '退货失败'
                }],

            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    created () {
        // showLoading()
        this.orderReturnId = this.$route.params.row
        this.getOrderReturnDate()
        this.getTableItemData()
        // hideLoading()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'orderItenInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        getTableItemData () {
            this.tableLoading = true
            getListByOrderReturnId({ orderReturnId: this.orderReturnId }).then(res => {
                this.orderItemtableData = res
            })
            this.tableLoading = false
        },
        openRetuen (state) {
            let parans = {
                orderReturnId: this.formData.orderReturnId,
                state: state
            }
            this.tableLoading = true
            updateOrderReturnState(parans).then(res=>{
                if (res.code == 200) {
                    this.$message({
                        message: '退货成功',
                        type: 'success'
                    })
                    this.tableLoading = false
                    this.$router.go(-1)
                }else {
                    this.$message({
                        message: '退货失败',
                        type: 'warning'
                    })
                    this.tableLoading = false
                }
            })
            this.queryVisible = true
        },
        updatDate (state) {
            let parans = {
                orderReturnId: this.formData.orderReturnId,
                state: state
            }
            updateOrderReturnState(parans).then(res=>{
                if (res.code == 200) {
                    this.$message({
                        message: '审核不通过',
                        type: 'success'
                    })
                    this.$router.go(-1)
                }
            })
        },
        getOrderReturnDate () {
            getOrderReturnDateById({ id: this.orderReturnId }).then(res=>{
                this.formData = res
            })
        },
        // 显示状态
        showTableState ( row ) {
            let stateValue  = row.state
            if(stateValue === 6) {
                return false
            }
            for (let i = 0; i < this.stateOptions.length; i++) {
                if(stateValue === this.stateOptions[i].value) {
                    this.tableStateTitle = this.stateOptions[i].label
                    return true
                }
            }
        },

        ...mapMutations(['setAuditParams']),
        ...mapMutations(['setSelectedInfo']),
        addData () {},
        deleteData () {},
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
</style>
