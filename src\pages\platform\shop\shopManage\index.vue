<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" v-show="viewList === true">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn dfa">
              <!--                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>-->
              <el-button @click="changePublishState(2)" class="btn-delete">批量停用</el-button>
              <!-- <el-button  type="primary" @click="openUpdate">添加欠费店铺</el-button>
                <el-button  type="primary" @click="openArrearage(0)">批量续费</el-button> -->
                <el-upload
                multiple action="fileUrl"
                :limit="limitNum"
                accept=".xls,.xlsx,csv"
                :file-list="fileList"
                :before-upload="beforeUpload"
                :on-exceed="onExceed"
                :show-file-list="true"
                :http-request="uploadFile"
                style="margin-left: 10px"
                >
                <el-button  type="primary">上传店铺启用停用Excel</el-button>
              </el-upload>
              <el-upload
              multiple action="fileUrl"
              :limit="limitNum"
              accept=".xls,.xlsx,csv"
              :file-list="fileList"
              :before-upload="beforeUpload"
              :on-exceed="onExceed"
              :show-file-list="true"
                :http-request="uploadArrearageFile"
                style="margin-left: 10px"
                >
                <!-- <el-button  type="primary">上传店铺欠费续费Excel</el-button> -->
              </el-upload>
              <el-button  type="primary" @click="changeIsOrderAuth">设置接单权限</el-button>
              <el-button  type="primary" @click="changePayDays">缴费截止日期设置</el-button>
              <!-- <el-dropdown style="margin-left: 10px" trigger="click" @command="btnClick">
                <el-button type="primary">
                  更多操作<i class="el-icon-arrow-down el-icon--right"/>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="changePublishState">批量启用</el-dropdown-item>
                  <el-dropdown-item command="onDownloadStopShop">下载关闭店铺模板</el-dropdown-item>
                  <el-dropdown-item command="onDownArrearage">下载店铺欠费模板</el-dropdown-item>
                  <el-dropdown-item command="changeSortValue">批量修改排序值</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown> -->
            </div>
          </div>
          <div class="search_box">
            <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
            <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>

            <el-input type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字"
                      v-model="keywords">
              <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" />
            </el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" v-loading="isLoading">
        <el-table
          @row-click="handleCurrentInventoryClick2" ref="mainTable2" class="table" :height="rightTableHeight"
          :data="tableData" border highlight-current-row
          @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <!--                    <el-table-column label="操作" width="80">-->
          <!--                        <template v-slot="scope">-->
          <!--                            <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""></span>-->
          <!--                        </template>-->
          <!--                    </el-table-column>-->
          <el-table-column label="店铺名称" width="260">
            <template v-slot="scope">
              <span class="action" @click="handleView(scope)">{{ scope.row.shopName }}</span>
            </template>
          </el-table-column>
          <!--                    <el-table-column label="所属城市" width="">-->
          <!--                        <template v-slot="scope">-->
          <!--                            <span @click="handleView(scope)">-->
          <!--                                {{ scope.row.city }}-->
          <!--                            </span>-->
          <!--                        </template>-->
          <!--                    </el-table-column>-->
          <el-table-column label="店铺地址" width="300">
            <template v-slot="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.province }}{{ scope.row.county }}{{ scope.row.detailedAddress }}
                            </span>
            </template>
          </el-table-column>
          <el-table-column label="店铺类型" width="">
            <template v-slot="scope">
              <span v-if="scope.row.shopType == '2'">个人</span>
              <span v-else-if="scope.row.shopType == '0'">个体户</span>
              <span v-else-if="scope.row.shopType == '1'">企业</span>

            </template>
          </el-table-column>
          <el-table-column label="首页展示" width="100">
            <template v-slot="scope">
              <el-switch
                @change='isIndexShowChange(scope.row)'
                v-model="scope.row.isIndexShow"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="店铺状态" width="">
            <template v-slot="scope">
              <el-tag v-if="scope.row.state==1" type="success">启用</el-tag>
              <el-tag v-if="scope.row.state==0" type="danger">停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否欠费" width="">
            <template v-slot="scope">
              <el-tag v-if="scope.row.isArrearage==0" type="success">否</el-tag>
              <el-tag v-if="scope.row.isArrearage==1" type="danger">是</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="接单权限" width="">
            <template v-slot="scope">
              <el-tag v-if="scope.row.state==0" type="danger">关闭</el-tag>
              <el-tag v-else type="success">正常</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="自营店铺" width="">
            <template v-slot="scope">
              <el-tag v-if="scope.row.isBusiness==1" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="内部店铺" width="">
            <template v-slot="scope">
              <el-tag v-if="scope.row.isInternalShop==1" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </template>
          </el-table-column>
          <!--                    <el-table-column label="路桥结算" width="">-->
          <!--                        <template v-slot="scope">-->
          <!--                            <el-tag v-if="scope.row.isInternalSettlement==1" type="success">支持</el-tag>-->
          <!--                            <el-tag v-else type="danger">不支持</el-tag>-->
          <!--                        </template>-->
          <!--                    </el-table-column>-->
          <el-table-column label="店铺审核状态" width="">
            <template v-slot="scope">
              <el-tag v-if="scope.row.auditStatus==2">未审核</el-tag>
              <el-tag v-if="scope.row.auditStatus==1" type="success">审核通过</el-tag>
              <el-tag v-if="scope.row.auditStatus==3" type="danger">审核未通过</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="是否缴费" width="" v-if="materialFee">
            <template v-slot="scope">
              <span v-if="scope.row.isArrearage==1">已缴费</span>
              <span v-if="scope.row.isArrearage==0">未缴费</span>
            </template>
          </el-table-column> -->
          <!-- 排序值 -->
          <el-table-column label="排序值" width="120" prop="sort">
            <template v-slot="scope">
              <el-input width="60" v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="交易费缴费截止日期" width="180" prop="sort">
            <template v-slot="scope">
              <span>结算日期后</span>
              <el-input v-model="scope.row.payDays" @change="getChangedRowPayDays(scope.row)" class="payDaysClass"></el-input>
              <span>天</span>
            </template>
          </el-table-column>
          <el-table-column label="冻结商铺" width="100">
            <template v-slot="scope">
              <el-switch
                @change='isFreozenChange(scope.row)'
                :value="scope.row.state"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="0"
                :inactive-value="1"
              >
              </el-switch>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination
        :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
        @currentChange="currentChange" @sizeChange="sizeChange"
      />
    </div>
    <div class="right" v-show="viewList !== true">
      <!-- ---------------------新增/编辑窗口--------------------- -->
      <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
        <div class="tabs-title">基本信息</div>
        <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="12">
              <el-form-item width="150px" label="店铺名称：" prop="name">
                <el-input v-model="formData.shopName" placeholder="请输入链接名称" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item width="150px" label="店铺类型：" prop="url">
                <el-input v-model="formData.shopType" placeholder="" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="店铺所在省份：" prop="sort">
                <el-input v-model="formData.province" placeholder="请选择店铺所在省份" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="店铺所在市：" prop="sort">
                <el-input v-model="formData.province" placeholder="请选择店铺所在省份" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="店铺所在县、区：" prop="sort">
                <el-input v-model="formData.city" placeholder="请选择店铺所在县、区" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="店铺详细地址：" prop="sort">
                <el-input v-model="formData.detailedAddress" placeholder="请输入店铺详细地址" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="店铺状态：" prop="sort">
                <el-input v-model="formData.state" placeholder="请输入店铺详细地址" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序值：" prop="sort">
                <el-input v-model="formData.sort" type="number" placeholder="填写排序值">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注信息：">
                <el-input type="textarea" v-model="formData.remarks" autocomplete="off"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="footer">
        <div class="right-btn">
          <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
          <el-button @click="onCancel">取消</el-button>
        </div>
      </div>
    </div>
    <!-- ----------------查询弹框---------------- -->
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
        <el-row>
          <el-col :span="12">
            <el-form-item label="店铺名称：" prop="shopName">
              <el-input v-model="filterData.shopName" placeholder="请输入店铺名称" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="店铺地址：" prop="detailedAddress">
              <el-input v-model="filterData.detailedAddress" placeholder="请输入店铺地址" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="店铺类型：">
              <el-select v-model="filterData.shopType" placeholder="店铺类型">
                <el-option
                  v-for="item in shopTypeFilter" :key="item.value" :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="店铺状态：">
              <el-select v-model="filterData.state" placeholder="店铺状态">
                <el-option
                  v-for="item in shopStateFilter" :key="item.value" :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="自营店铺：">
              <el-select v-model="filterData.isBusiness" placeholder="自营店铺">
                <el-option
                  v-for="item in shopBusinessFilter" :key="item.value" :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!--                    <el-col :span="12">-->
          <!--                        <el-form-item label="审核状态：">-->
          <!--                            <el-select v-model="filterData.auditStatus"  placeholder="审核状态">-->
          <!--                                <el-option v-for="item in shopAuditStatusFilter" :key="item.value" :label="item.label"-->
          <!--                                           :value="item.value" >-->
          <!--                                </el-option>-->
          <!--                            </el-select>-->
          <!--                        </el-form-item>-->
          <!--                    </el-col>-->
          <el-col :span="12">
            <el-form-item label="内部店铺：">
              <el-select v-model="filterData.isInternalShop" placeholder="是否为内部店铺">
                <el-option
                  v-for="item in shopInernalShopFilter" :key="item.value" :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否欠费：">
              <el-select v-model="filterData.isArrearage" placeholder="店铺状态">
                <el-option
                  v-for="item in shopArrearageStateFilter" :key="item.value" :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">返回</el-button>
            </span>
    </el-dialog>
    <el-dialog class="updateVisible"
               v-loading="isArrearageLoading" v-dialogDrag top="5vh" width="80%"
               title="添加欠费店铺"
               :visible.sync="updateVisible"
    >

      <el-tabs>
        <el-tab-pane label="选择列表" class="df dlg">
          <div class="box-left">
            <div class="e-table">
              <div class="top">
                <div style="width: 200px" class="left">
                  <el-input type="text" @blur="getupdateList" placeholder="输入搜索关键字"
                            v-model="arrearageKeywords">
                    <img :src="require('@/assets/search.png')" slot="suffix"
                         @click="getupdateList" />
                  </el-input>
                  <span style="color: #ea083a">双击添加</span>
                </div>
              </div>
              <el-table ref="selectContractOrPlanR"
                        id="myTable"
                        border
                        :select-all="false"
                        height="340px"
                        @row-click="selectContractOrPlanRowClickM"
                        :data="tableData2"
                        v-loading="selectContractOrPlanLoading"
                        class="table"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="shopName" label="店铺名称" width=""></el-table-column>
                <el-table-column prop="enterpriseName" label="企业名称" width=""></el-table-column>
                <el-table-column label="欠费状态" width="">
                  <template v-slot="scope">
                    <span v-if="scope.row.isArrearage == '0'">未欠费</span>
                    <span v-else-if="scope.row.isArrearage == '1'">已欠费</span>
                  </template>
                </el-table-column>

              </el-table>
              <Pagination
                v-if="tableData2 != null && tableData2.length > 0"
                :total="paginationInfo3.total"
                :pageSize.sync="paginationInfo3.pageSize"
                :currentPage.sync="paginationInfo3.currentPage"
                @currentChange="currentChange2"
                @sizeChange="sizeChange2"
              />
            </div>
          </div>
          <div class="box-right">
            <div class="e-table">
              <div class="top">
                <div style="width: 200px" class="left">
                  <span style="color: #ea083a">双击<br>移除</span>
                </div>

              </div>
              <el-table ref="siteReceivingTableRef"
                        v-loading="siteReceivingLoading"
                        border
                        height="340px"
                        @row-dblclick="removeSelectOrderItemList"
                        :data="updateRow"
                        class="table"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="shopName" label="店铺名称" width=""></el-table-column>
                <el-table-column prop="enterpriseName" label="企业名称" width=""></el-table-column>
                <el-table-column label="欠费状态" width="">
                  <template v-slot="scope">
                    <span v-if="scope.row.isArrearage == '0'">未欠费</span>
                    <span v-else-if="scope.row.isArrearage == '1'">已欠费</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer">
                <el-button type="primary" @click="siteReceivingTableDateSelectAffirmClick(1)">确认修改</el-button>
                <el-button @click="updateVisible = false">取消</el-button>
            </span>
    </el-dialog>
    <!--        excel导入返回弹窗-->
    <el-dialog v-dialogDrag title="导入结果" :visible.sync="showImportExcelLoading" width="70%">
      <div class="e-table" style="background-color: #fff">
        <el-table
          border
          :data="excelResult"
          class="table"
          :max-height="$store.state.tableHeight"
          ref="mainTable"
        >
          <el-table-column prop="id" label="序号" width="60"></el-table-column>
          <el-table-column prop="supplierName" label="供应商名称"></el-table-column>
          <el-table-column prop="shopName" label="店铺名称"></el-table-column>
          <el-table-column prop="state" label="状态" width="80">
            <template v-slot="scope">
              <el-tag v-if="scope.row.state==1" type="success">成功</el-tag>
              <el-tag v-if="scope.row.state==0" type="danger">失败</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="fail" label="失败原因"></el-table-column>
        </el-table>
      </div>
      <span slot="footer">
                <el-button class="mt20" @click="showImportExcelLoading = false">取消</el-button>
            </span>
    </el-dialog>
    <el-dialog
      :visible.sync="orderAuthDialogVisible"
      width="420px"
      custom-class="order-auth-dialog"
      :close-on-click-modal="false"
    >
      <div class="order-auth-row">
        <span class="order-auth-label">店铺名称：</span>
        <div class="order-auth-value">{{ selectedRows.map(item => item.shopName).join('、') }}</div>
      </div>
      <div class="order-auth-row">
        <span class="order-auth-label">接单权限：</span>
        <el-radio-group v-model="orderAuthRadio">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </div>
      <div class="order-auth-row order-auth-footer">
        <el-button @click="handleOrderAuthCancel">取消</el-button>
        <el-button type="primary" @click="handleOrderAuthConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import {
    batchDelete,
    batchNotPublish,
    batchPublish,
    changeSortValue,
    changePayDays,
    create,
    del,
    edit,
    freezeShop,
    updateBatchIsOrderAuth,
    getShopList,
    shopTemplate,
    shopArrearageExcelFile,
    stopShopStateExcelFile,
    updateBatchArrearage
} from '@/api/platform/shop/shopManager'
// eslint-disable-next-line no-unused-vars
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapActions } from 'vuex'
import Pagination from '@/components/pagination/pagination.vue'

export default {
    components: {
        Pagination,
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getShopList(this.requestParams).then(res => {
                    console.log(res)
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        }
    },
    data () {
        return {
            alertName: '店铺',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            arrearageKeywords: '',
            isArrearage: null,
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            changedRowOrderAuth: [],
            changedRowPayDays: [],
            pages: {
                currPage: 1,
                pageSize: 20
            },
            arrearagepages: {
                currPage: 1,
                pageSize: 20
            },
            arrearagetableData: [],
            paginationInfo3: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1
            },
            isArrearageLoading: false,
            siteReceivingLoading: false,
            updateRow: [], // 需要修改的元素
            selectContractOrPlanLoading: false,
            arrearageDateNumShow: false,
            tableData2: [],
            updateVisible: false,
            updateForm: {
                isArrearage: null,
                enterpriseIds: []

            },
            // 高级查询数据对象
            filterData: {
                isArrearage: null,
                shopName: '',
                city: '',
                detailedAddress: '',
                shopType: null,
                state: null,
                isBusiness: null,
                isSupplier: null,
                isInternalShop: null,
                auditStatus: 1,
                orderBy: 1
            },
            tableData: [],
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入链接名', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
                name: '',
                url: '',
                remarks: ''
            },
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            tableLoading: false,
            fileList: [], //文件列表
            limitNum: 1, //文件上传个数限制
            requestParams: {},
            isLoading: false,
            shopTypeFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '企业' },
                { value: 0, label: '个体户' },
                { value: 2, label: '个人' }
            ],
            shopStateFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '停用' },
                { value: 1, label: '启用' }
            ],
            shopArrearageStateFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '未欠费' },
                { value: 1, label: '已欠费' }
            ],
            shopBusinessFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '非自营' },
                { value: 1, label: '自营' }
            ],
            shopSupplierFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '供应商' },
                { value: 1, label: '非供应商' }
            ],
            shopInernalShopFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '非内部店铺' },
                { value: 1, label: '内部店铺' }
            ],
            shopAuditStatusFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '通过审核' },
                { value: 2, label: '未审核' },
                { value: 3, label: '审核未通过' }
            ],
            excelResult: [],
            showImportExcelLoading: false,
            orderAuthDialogVisible: false,
            orderAuthRadio: 1,
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    // keepAlive使用了触发
    activated () {
        this.getShopListM()
    },
    created () {
    //     this.getShopListM()
    },
    methods: {
        uploadFile (res) {
            let formData = new FormData()
            formData.append('file', res.file)
            this.tableLoading = true
            stopShopStateExcelFile(formData).then(res => {
                this.tableLoading = false
                this.showExcelResult(res)
                this.getTableData()
                this.fileList = []
            }).catch(() => {
                this.getTableData()
                this.fileList = []
            })
        },
        showExcelResult (result) {
            this.excelResult = result
            this.showImportExcelLoading = true
        },
        beforeUpload (file) {
            let regExp = file.name.replace(/.+\./, '')
            let lower = regExp.toLowerCase() //把大写字符串全部转为小写字符串
            let suffix = ['xls', 'xlsx']
            if (suffix.indexOf(lower) === -1) {
                return this.$message.warning('请上传后缀名为 xls、xlsx 的附件 !')
            }
            let isLt2M = file.size / 1024 / 1024 < 5
            if (!isLt2M) {
                return this.$message.error('请上传文件大小不能超过 5MB 的附件 !')
            }
        },
        onExceed (files, fileList) {
            return this.$message.warning(`只能选择${this.limitNum}个文件,当前共选择了${files.length + fileList.length}个`)
        },
        uploadArrearageFile (res) {
            console.log(res.file, 'res')
            let formData = new FormData()
            formData.append('file', res.file)
            this.tableLoading = true
            shopArrearageExcelFile(formData).then(res => {
                this.tableLoading = false
                this.showExcelResult(res)
                this.getTableData()
                this.fileList = []
            }).catch(() => {
                this.getTableData()
                this.fileList = []
            })
        },
        btnClick (command) {
            let actions = {
                'changePublishState': () => this.changePublishState(1),
                'onDownloadStopShop': () => this.onDownloadStopShop(1),
                'onDownArrearage': () => this.onDownArrearage(2),
                'changeSortValue': () => this.changeSortValue(),
            }
            actions[command]()
        },
        onDownloadStopShop (state) {
            shopTemplate({ shopState: state }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '关闭店铺模板.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
            })
        },
        onDownArrearage (state) {
            shopTemplate({ shopState: state  }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '添加欠费店铺模板.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
            })
        },

        excelStopShop () {},
        excelArrearage () {},
        siteReceivingTableDateSelectAffirmClick (state) {
            if (this.updateRow.length === 0) {
                this.$message.warning('请选择公司。')
            } else {
                this.$confirm('请确定是否修改店铺欠费状态，修改后店铺将不能接收新订单和上架商品?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.updateForm.enterpriseIds = this.updateRow.map(item => {
                        return item.enterpriseId
                    })
                    let params = {
                        enterpriseIds: this.updateForm.enterpriseIds,
                        state: state
                    }
                    updateBatchArrearage(params).then(res => {
                        if (res.code == 200) {
                            this.$message.success('设置欠费机构成功')
                            this.updateVisible = false
                        }
                        this.updateSumRow = []
                        this.updateRow = []
                        // this.updateVisible = false
                        this.getTableData()
                    })
                })

            }

        },
        removeSelectOrderItemList (row) {
            if (this.updateRow == 0) {
                return this.$message.warning('没有确认的公司')
            } else {
                this.updateRow = this.updateRow.filter(t => t.enterpriseId != row.enterpriseId)
                // this.updateRow.forEach((item, index) => {
                //     const foundIndex = this.subRow.findIndex(row => row.enterpriseId === item.enterpriseId)
                //     if (foundIndex !== -1) {
                //         this.updateSumRow.splice(index, 1)
                //     }
                // })
            }

        },
        getupdateList () {
            let params = {
                limit: this.paginationInfo3.pageSize,
                page: this.paginationInfo3.currPage,
                orderBy: this.filterData.orderBy,
                auditStatus: this.filterData.auditStatus,
                isArrearage: 0
            }
            if (this.arrearageKeywords != null && this.arrearageKeywords != '') {
                params.keywords = this.arrearageKeywords
            }
            getShopList(params).then(res => {
                this.tableData2 = res.list
                this.paginationInfo3.total = res.totalCount
                this.paginationInfo3.pageSize = res.pageSize
                this.paginationInfo3.currentPage = res.currPage
            })
        },

        selectContractOrPlanRowClickM (row) {
            for (let i = 0; i < this.updateRow.length; i++) {
                let t = this.updateRow[i]
                if (t.enterpriseId == row.enterpriseId) {
                    return this.$message.warning('该店铺已选择！')
                }
            }
            this.updateRow.push(row)
        },
        currentChange2 (index) {
            this.paginationInfo3.currPage = index
            this.getupdateList()
        },
        sizeChange2 (index) {
            this.paginationInfo3.pageSize = index
            this.getupdateList()
        },
        openUpdate () {
            this.updateVisible = true
            this.getupdateList()
        },
        openArrearage (num) {
            let arr = this.selectedRows.map(item => {
                return item.enterpriseId
            })
            if (!this.selectedRows[0]) {
                return this.$message('未选择数据')
            }
            let params = {
                enterpriseIds: arr,
                state: num
            }
            let warnMsg = num === 0 ? `您确定要打开的${this.alertName}续费吗？` : `您确定要添加停用选中的${this.alertName}吗？停用将不能接受订单和上架商品！`
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    updateBatchArrearage(params).then(() => {
                        this.$message.success('关闭店铺续费')
                        getShopList(this.requestParams).then(res => {
                            this.updateVisible = false
                            if (res.list) {
                                this.tableData = res.list
                            } else {
                                this.clientPop('warn', res.message, () => {
                                })
                            }
                            this.pages = res
                        })

                    })
                    break
                case 0:
                    updateBatchArrearage(params).then(res => {
                        if (res.message === '操作成功') {
                            this.updateVisible = false
                            this.clientPop('suc', '续费成功成功', () => {
                                getShopList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })

        },

        getShopListM () {
            this.isLoading = true
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                orderBy: this.filterData.orderBy,
                auditStatus: this.filterData.auditStatus
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.isArrearage != null && this.filterData.isArrearage != '') {
                params.isArrearage = this.filterData.isArrearage
            }
            getShopList(params).then(res => {
                this.isLoading = false
                this.pages = res
                this.tableData = res.list
            })
            this.getParams()
        },
        isIndexShowChange (row) {
            this.isLoading = true
            edit({ shopId: row.shopId, isIndexShow: row.isIndexShow }).then(res => {
                if (res.code == 200) {
                    this.getShopListM()
                    this.$message.success('操作成功')
                    this.isLoading = false
                }
            }).finally(() => {
                this.isLoading = false
            })
        },
        isFreozenChange (row) {
            // 计算切换后的状态（取反）
            const newState = row.state === 0 ? 1 : 0
            let warnMsg = newState === 0 ? `您确定要冻结${row.shopName}吗？冻结后将不能接受订单和上架商品！` : `您确定要激活${row.shopName}吗？`
            this.clientPop('info', warnMsg, async () => {
                // 用户点击确认，调用后台接口
                this.isLoading = true
                freezeShop({ shopId: row.shopId, state: newState }).then(res => {
                    if (res.code == 200) {
                        this.getShopListM()
                        this.$message.success('操作成功')
                        this.isLoading = false
                    }
                })
            }, () => {
                // 用户点击取消，不更新状态（保持原始状态）
            })
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        resetSearchConditions () {
            this.filterData.shopName = ''
            this.filterData.isArrearage = null
            this.filterData.city = ''
            this.filterData.detailedAddress = ''
            this.filterData.shopType = null
            this.filterData.state = null
            this.filterData.isBusiness = null
            this.filterData.isSupplier = null
            this.filterData.isInternalShop = null
            this.filterData.auditStatus = null
        },
        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.shopId
            })
            if (!this.selectedRows[0]) {
                return this.$message('未选择数据')
            }
            let warnMsg = num === 1 ? `您确定要启用选中的${this.alertName}吗？` : `您确定要停用选中的${this.alertName}吗？停用将下架当前店铺所有商品！`
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('修改成功')
                            getShopList(this.requestParams).then(res => {
                                if (res.list) {
                                    this.tableData = res.list
                                } else {
                                    this.clientPop('warn', res.message, () => {
                                    })
                                }
                                this.pages = res
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '停用成功', () => {
                                getShopList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${this.alertName}吗？`, async () => {
                showLoading()
                del({ id: scope.row.shopId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.shopId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () {
        },
        handleView (scope) {
            // this.viewList = 'class'
            // this.formData = scope.row
            // this.action = '编辑'
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/shop/shopManageDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shopManageDetail',
                params: {
                    row: scope.row
                }
            })
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (!this.changedRow[0]) {
                return this.changedRow.push({ shopId: row.shopId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if (item.shopId === row.shopId) {
                    return i
                }
            })
            if (arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ shopId: row.shopId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            // changeSortValue(this.changedRow).then(res => {
            //     this.getTableData()
            // })
            if (!this.changedRow[0]) {
                return this.$message('未选择数据')
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('修改成功')
                        this.getTableData()
                        this.changedRow = []
                    }
                })
            })
        },
        // 打开弹窗
        changeIsOrderAuth () {
            if (!this.selectedRows[0]) {
                return this.$message('未选择数据')
            }
            const allEqual = this.selectedRows.every(item => item.state === this.selectedRows[0].state)
            if (!allEqual) {
                return this.$message('不能同时选择接单权限不同的商铺')
            }
            this.orderAuthDialogVisible = true
        },
        // 确认
        handleOrderAuthConfirm () {
            this.isLoading = true
            this.orderAuthDialogVisible = false
            let arr = this.selectedRows.map(item => ({
                shopId: item.shopId,
                state: this.orderAuthRadio
            }))
            updateBatchIsOrderAuth(arr).then(res => {
                if (res.message === '操作成功') {
                    this.$message.success('修改成功')
                    this.getTableData()
                    this.changedRowOrderAuth = []
                    this.orderAuthRadio = 1
                }
            })
        },
        // 取消
        handleOrderAuthCancel () {
            this.orderAuthDialogVisible = false
        },

        // 获取修改过的表格行数据
        getChangedRowPayDays (row) {
            if (!this.changedRowPayDays[0]) {
                return this.changedRowPayDays.push({ shopId: row.shopId, payDays: parseInt(row.payDays) })
            }
            let arr = this.changedRowPayDays.map((item, i) => {
                if (item.shopId === row.shopId) {
                    return i
                }
            })
            if (arr[0]) {
                return this.changedRowPayDays[arr[0]].payDays = row.payDays
            }
            this.changedRowPayDays.push({ shopId: row.shopId, payDays: parseInt(row.payDays) })
        },
        // 缴费截止日期设置
        changePayDays () {
            // changeSortValue(this.changedRow).then(res => {
            //     this.getTableData()
            // })
            if (!this.changedRowPayDays[0]) {
                return this.$message('未选择数据')
            }
            let warnMsg = '您确定要修改‘这些’的缴费截止日期吗？'
            this.clientPop('info', warnMsg, async () => {
                changePayDays(this.changedRowPayDays).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('修改成功')
                        this.getTableData()
                        this.changedRowPayDays = []
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.isLoading = true
            this.getParams()
            getShopList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.isLoading = false
                this.pages = res
            })
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.mainTable2.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.action === '编辑') {
                        return this.handleEditData()
                    }
                    this.handleCreateData()
                }
            })
        },
        // 修改数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 保存数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}
.box-left, .box-right {
  width: 50% !important;
  flex-grow: 1;

  .top {
    padding: 10px;

    .el-input {
      margin-right: 10px;
    }
  }
}
/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}
/deep/ .updateVisible {
  .el-dialog__body {
    height: 680px;
    margin-top: 0px;
  }
}
.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}

.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}

.payDaysClass {
   width: 60px; display: inline-block; margin: 0 4px;
}

/* 设置接单权限弹窗样式 */
.order-auth-dialog ::v-deep .el-dialog__header {
  background: none !important;
  background-image: none !important;
}

.order-auth-dialog .order-auth-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.order-auth-dialog .order-auth-footer {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
  margin-bottom: 0;
}
.order-auth-dialog .order-auth-label {
  width: 90px;
  text-align: right;
  color: #666;
  flex-shrink: 0;
}
.order-auth-dialog .order-auth-value {
  margin-left: 10px;
  flex: 1;
  color: #222;
  word-break: break-all;
}
.order-auth-dialog .el-radio-group {
  margin-left: 10px;
}
/deep/ .order-auth-dialog .el-dialog__body {
  height: 180px !important;
  padding-bottom: 0 !important;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
</style>
