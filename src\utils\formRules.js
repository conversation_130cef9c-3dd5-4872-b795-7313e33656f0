// 表单公用方法
/** 验证输入框不能全是空格 */
export function verifySpaces (rule, value, callback) {
    let reminderInformation = '不能全是空格'
    // 获取当前验证的提示语
    if(event && event.currentTarget && event.currentTarget.placeholder) {
        reminderInformation = event.currentTarget.placeholder
    }
    if (/^\s*$/.test(value)) {
        return callback(new Error(reminderInformation))
    }
    callback()
}
/** 验证富文本输入框不能全是空格 */
export function verifyFwbSpaces (rule, value, callback) {
    let text = value.replace(/(<([^>]+)>)/ig, '')
    if(!text.trim()) {
        return callback(new Error('请输入描述'))
    }
    callback()
}
export default {
    verifySpaces, verifyFwbSpaces
}