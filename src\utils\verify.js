/* eslint-disable no-unused-vars */
import Sortable from 'sortablejs'
import Vue from 'vue'
const verify = {
    // 自定义指令验证只能输入数字和小数(保留2位小数)
    enterFloat: Vue.directive('enterFloat', {
        bind: function (el, binding) {
            el.handler = function () {
                const reg = /[^\d^.]+/g
                let val = el.childNodes[1].value
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value.set[binding.value.val] !== undefined) {
                        binding.value.set[binding.value.val][binding.value.key] = 0
                    } else {
                        binding.value.set[binding.value.key] = 0
                    }
                } else if (val.substr(0, 1) == '.') {
                    el.childNodes[1].value = '0.'
                }
                if (reg.test(el.childNodes[1].value)) {
                    if(binding.value) {
                        el.childNodes[1].value = null
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = null
                        } else {
                            binding.value.set[binding.value.key] = null
                        }
                    }else{
                        el.childNodes[1].dispatchEvent(new Event('input'))
                    }
                }
            }
            el.addEventListener('input', el.handler)
            //失去焦点的时候转为数字
            el.childNodes[1].onblur = () => {
                if (el.childNodes[1].value != '') {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                +el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = +el.childNodes[1].value
                        }
                    }
                }
            }
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let charcode = typeof e.charCode == 'number' ? e.charCode : e.keyCode
                if (charcode == 46) {
                    if (el.childNodes[1].value.includes('.')) {
                        e.preventDefault()
                    }
                    return
                }
                //保留两位小数
                if (el.childNodes[1].value.includes('.')) {
                    let last = el.childNodes[1].value.split('.')[1]
                    if (last.length > 1) e.preventDefault()
                }
            })
        }
    }),
    // 自定义指令验证只能输入数字和小数(保留2位小数，可负数)
    enterFloatNeg: Vue.directive('enterFloatNeg', {
        bind: function (el, binding) {
            el.handler = function () {
                const reg = /[^\d^.^-]+/g
                let val = el.childNodes[1].value
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value.set[binding.value.val] !== undefined) {
                        binding.value.set[binding.value.val][binding.value.key] = 0
                    } else {
                        binding.value.set[binding.value.key] = 0
                    }
                } else if (val.substr(0, 1) == '.') {
                    el.childNodes[1].value = '0.'
                }
                if (reg.test(el.childNodes[1].value)) {
                    if(binding.value) {
                        el.childNodes[1].value = null
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = null
                        } else {
                            binding.value.set[binding.value.key] = null
                        }
                    }else{
                        el.childNodes[1].dispatchEvent(new Event('input'))
                    }
                }
            }
            el.addEventListener('input', el.handler)
            //失去焦点的时候转为数字
            el.childNodes[1].onblur = () => {
                if (el.childNodes[1].value != '') {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                +el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = +el.childNodes[1].value
                        }
                    }
                }
            }
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let charcode = typeof e.charCode == 'number' ? e.charCode : e.keyCode
                if (charcode == 46) {
                    if (el.childNodes[1].value.includes('.')) {
                        e.preventDefault()
                    }
                    return
                }
                //保留两位小数
                if (el.childNodes[1].value.includes('.')) {
                    let last = el.childNodes[1].value.split('.')[1]
                    if (last.length > 1) e.preventDefault()
                }
            })
        }
    }),
    // 自定义指令验证只能输入数字和小数(保留2位小数正数)
    enterJustFloat: Vue.directive('enterJustFloat', {
        bind: function (el, binding) {
            el.handler = function () {
                const reg = /[^\d^.]+/g
                let val = el.childNodes[1].value
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value.set[binding.value.val] !== undefined) {
                        binding.value.set[binding.value.val][binding.value.key] = 0
                    } else {
                        binding.value.set[binding.value.key] = 0
                    }
                } else if (val.substr(0, 1) == '.') {
                    el.childNodes[1].value = '0.'
                } else if (val.substr(0, 1) == '-') {
                    el.childNodes[1].value = ''
                }
                if (reg.test(el.childNodes[1].value)) {
                    if(binding.value) {
                        el.childNodes[1].value = null
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = null
                        } else {
                            binding.value.set[binding.value.key] = null
                        }
                    }else{
                        el.childNodes[1].dispatchEvent(new Event('input'))
                    }
                }
            }
            el.addEventListener('input', el.handler)
            //失去焦点的时候转为数字
            el.childNodes[1].onblur = () => {
                if (el.childNodes[1].value != '') {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                +el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = +el.childNodes[1].value
                        }
                    }
                }
                // 修复NAN bug
                if (isNaN(el.childNodes[1].value)) {
                    el.childNodes[1].value = 0
                }
            }
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let charcode = typeof e.charCode == 'number' ? e.charCode : e.keyCode
                if (charcode == 46) {
                    if (el.childNodes[1].value.includes('.')) {
                        e.preventDefault()
                    }
                    return
                }
                //保留两位小数
                if (el.childNodes[1].value.includes('.')) {
                    let last = el.childNodes[1].value.split('.')[1]
                    if (last.length > 1) e.preventDefault()
                }
            })
        }
    }),
    // 自定义指令验证只能输入数字和小数(保留4位小数)
    enterFloat2: Vue.directive('enterFloat2', {
        bind: function (el, binding) {
            el.handler = function () {
                const reg = /[^\d^.]+/g
                let val = el.childNodes[1].value
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = 0
                        } else {
                            binding.value.set[binding.value.key] = 0
                        }
                    }
                } else if (val.substr(0, 1) == '.') {
                    el.childNodes[1].value = '0.'
                }
                if (reg.test(el.childNodes[1].value)) {
                    if(binding.value) {
                        el.childNodes[1].value = null
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = null
                        } else {
                            binding.value.set[binding.value.key] = null
                        }
                    }else{
                        el.childNodes[1].dispatchEvent(new Event('input'))
                    }
                }
            }
            el.addEventListener('input', el.handler)
            //失去焦点的时候转为数字
            el.childNodes[1].onblur = () => {
                if (el.childNodes[1].value != '') {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                +el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = +el.childNodes[1].value
                        }
                    }
                }
            }
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let charcode = typeof e.charCode == 'number' ? e.charCode : e.keyCode
                if (charcode == 46) {
                    if (el.childNodes[1].value.includes('.')) {
                        e.preventDefault()
                    }
                    return
                }
                //保留四位小数
                if (el.childNodes[1].value.includes('.')) {
                    let last = el.childNodes[1].value.split('.')[1]
                    if (last.length > 3) e.preventDefault()
                }
            })
        }
    }),
    // 自定义指令验证只能输入数字和小数(保留4位小数，可以为负数)
    enterFloat2Neg: Vue.directive('enterFloat2Neg', {
        bind: function (el, binding) {
            el.handler = function () {
                const reg = /[^\d^.^-]+/g
                let val = el.childNodes[1].value
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = 0
                        } else {
                            binding.value.set[binding.value.key] = 0
                        }
                    }
                } else if (val.substr(0, 1) == '.' ) {
                    el.childNodes[1].value = '0.'
                }
                if (reg.test(el.childNodes[1].value)) {
                    if(binding.value) {
                        el.childNodes[1].value = null
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = null
                        } else {
                            binding.value.set[binding.value.key] = null
                        }
                    }else{
                        el.childNodes[1].dispatchEvent(new Event('input'))
                    }
                }
            }
            el.addEventListener('input', el.handler)
            //失去焦点的时候转为数字
            el.childNodes[1].onblur = () => {
                if (el.childNodes[1].value != '') {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                    +el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = +el.childNodes[1].value
                        }
                    }
                }
            }
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let charcode = typeof e.charCode == 'number' ? e.charCode : e.keyCode
                if (charcode == 46) {
                    if (el.childNodes[1].value.includes('.')) {
                        e.preventDefault()
                    }
                    return
                }
                //保留四位小数
                if (el.childNodes[1].value.includes('.')) {
                    let last = el.childNodes[1].value.split('.')[1]
                    if (last.length > 3) e.preventDefault()
                }
            })
        }
    }),
    // 自定义指令验证只能输入数字和小数(保留4位小数正式)
    enterJustFloat2: Vue.directive('enterJustFloat2', {
        bind: function (el, binding) {
            el.handler = function () {
                const reg = /[^\d^.]+/g
                let val = el.childNodes[1].value
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = 0
                        } else {
                            binding.value.set[binding.value.key] = 0
                        }
                    }
                } else if (val.substr(0, 1) == '.') {
                    el.childNodes[1].value = '0.'
                } else if (val.substr(0, 1) == '-') {
                    el.childNodes[1].value = ''
                }
                if (reg.test(el.childNodes[1].value)) {
                    if(binding.value) {
                        el.childNodes[1].value = null
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = null
                        } else {
                            binding.value.set[binding.value.key] = null
                        }
                    }else{
                        el.childNodes[1].dispatchEvent(new Event('input'))
                    }
                }
            }
            el.addEventListener('input', el.handler)
            //失去焦点的时候转为数字
            el.childNodes[1].onblur = () => {
                if (el.childNodes[1].value != '') {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                +el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = +el.childNodes[1].value
                        }
                    }
                }
            }
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let charcode = typeof e.charCode == 'number' ? e.charCode : e.keyCode
                if (charcode == 46) {
                    if (el.childNodes[1].value.includes('.')) {
                        e.preventDefault()
                    }
                    return
                }
                //保留四位小数
                if (el.childNodes[1].value.includes('.')) {
                    let last = el.childNodes[1].value.split('.')[1]
                    if (last.length > 3) e.preventDefault()
                }
            })
        }
    }),
    //验证输入0-100的正整数
    enterInteger: Vue.directive('enterInteger', {
        bind: function (el, binding) {
            el.handler = function () {
                //验证输入0-100的正整数
                const num = /^([0-9]{1,2}|100)$/
                const num1 = /^[0-9]*$/ //数字
                const reg = /[^\d^]+/g //只能输入数字
                let val = el.childNodes[1].value
                console.log('val', val)
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value.set[binding.value.val] !== undefined) {
                        binding.value.set[binding.value.val][binding.value.key] = 0
                    } else {
                        binding.value.set[binding.value.key] = 0
                    }
                }
                if (!num.test(el.childNodes[1].value) && num1.test(el.childNodes[1].value)) {
                    el.childNodes[1].value = el.childNodes[1].value.slice(0, 2)
                    if (binding.value.set[binding.value.val] !== undefined) {
                        binding.value.set[binding.value.val][binding.value.key] =
                            el.childNodes[1].value
                    } else {
                        binding.value.set[binding.value.key] = el.childNodes[1].value
                    }
                }
                if (reg.test(el.childNodes[1].value)) {
                    el.childNodes[1].value = null
                    if (binding.value.set[binding.value.val] !== undefined) {
                        binding.value.set[binding.value.val][binding.value.key] = null
                    } else {
                        binding.value.set[binding.value.key] = null
                    }
                }
            }
            el.addEventListener('input', el.handler)
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let key = e.keyCode || e.charCode || e.which
                let reg = /\d/ //验证是不是数字
                if (!reg.test(String.fromCharCode(key)) && key > 9 && !e.ctrlKey) {
                    //String.fromCharCode(key)把键盘码转化为字符
                    if (e.preventDefault) {
                        //如果非数字则调用preventDefault事件阻止默认行为
                        e.preventDefault()
                    } else {
                        e.returnValue = false
                    }
                }
            })
        }
    }),
    //验证输入0-100的两位小数
    enterInteger2: Vue.directive('enterInteger2', {
        bind: function (el, binding) {
            el.handler = function () {
                //验证输入0-100的正整数
                const num = /^(?:0|[1-9][0-9]?|100)(\.[0-9]{0,2})?$/
                let val = el.childNodes[1].value
                if (!num.test(val)) {
                    val = val.slice(0, -1)
                    el.childNodes[1].value = val
                    el.childNodes[1].dispatchEvent(new Event('input'))
                }
            }
            el.addEventListener('input', el.handler)
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let charcode = typeof e.charCode == 'number' ? e.charCode : e.keyCode
                if (charcode == 46) {
                    if (el.childNodes[1].value.includes('.')) {
                        e.preventDefault()
                    }
                    return
                }
                //保留两位小数
                if (el.childNodes[1].value.includes('.')) {
                    let last = el.childNodes[1].value.split('.')[1]
                    if (last.length > 1) e.preventDefault()
                }
            })
        }
    }),
    //验证输入正整数
    enterInteger1: Vue.directive('enterInteger1', {
        bind: function (el, binding) {
            el.handler = function () {
                //验证输入0-100的正整数
                const num1 = /^[0-9]*$/ //数字
                const reg = /[^\d^]+/g //只能输入数字
                let val = el.childNodes[1].value
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value.set[binding.value.val] !== undefined) {
                        binding.value.set[binding.value.val][binding.value.key] = 0
                    } else {
                        binding.value.set[binding.value.key] = 0
                    }
                }
                if (num1.test(el.childNodes[1].value)) {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = el.childNodes[1].value
                        }
                    }
                }
                if (reg.test(el.childNodes[1].value)) {
                    el.childNodes[1].value = null
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = null
                        } else {
                            binding.value.set[binding.value.key] = null
                        }
                    }
                }
            }
            el.addEventListener('input', el.handler),
            //失去焦点的时候转为数字
            (el.childNodes[1].onblur = () => {
                if (el.childNodes[1].value != '') {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                    +el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = +el.childNodes[1].value
                        }
                    }
                }
            })
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let key = e.keyCode || e.charCode || e.which
                let reg = /\d/ //验证是不是数字
                if (!reg.test(String.fromCharCode(key)) && key > 9 && !e.ctrlKey) {
                    //String.fromCharCode(key)把键盘码转化为字符
                    if (e.preventDefault) {
                        //如果非数字则调用preventDefault事件阻止默认行为
                        e.preventDefault()
                    } else {
                        e.returnValue = false
                    }
                }
            })
        }
    }),
    //验证输入整数
    enterAllInteger1: Vue.directive('enterAllInteger1', {
        bind: function (el, binding) {
            el.handler = function () {
                //验证输入0-100的正整数
                const num1 = /^[0-9]*$/ //数字
                const reg = /[^\d^-]+/g //只能输入数字
                let val = el.childNodes[1].value
                if (val.substr(0, 2) == '00') {
                    el.childNodes[1].value = 0
                    if (binding.value.set[binding.value.val] !== undefined) {
                        binding.value.set[binding.value.val][binding.value.key] = 0
                    } else {
                        binding.value.set[binding.value.key] = 0
                    }
                }
                if (num1.test(el.childNodes[1].value)) {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = el.childNodes[1].value
                        }
                    }
                }
                if (reg.test(el.childNodes[1].value)) {
                    el.childNodes[1].value = null
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] = null
                        } else {
                            binding.value.set[binding.value.key] = null
                        }
                    }
                }
            }
            el.addEventListener('input', el.handler),
            //失去焦点的时候转为数字
            (el.childNodes[1].onblur = () => {
                if (el.childNodes[1].value != '') {
                    if (binding.value) {
                        if (binding.value.set[binding.value.val] !== undefined) {
                            binding.value.set[binding.value.val][binding.value.key] =
                                    +el.childNodes[1].value
                        } else {
                            binding.value.set[binding.value.key] = +el.childNodes[1].value
                        }
                    }
                }
            })
        },
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let key = e.keyCode || e.charCode || e.which
                let reg = /[^\d^-]+/g //验证整数
                if (reg.test(String.fromCharCode(key))) {
                    //String.fromCharCode(key)把键盘码转化为字符
                    if (e.preventDefault) {
                        //如果非整数则调用preventDefault事件阻止默认行为
                        e.preventDefault()
                    } else {
                        e.returnValue = false
                    }
                }
            })
        }
    }),
    //验证只能输入数字
    enterNumber: Vue.directive('enterNumber', {
        inserted (el) {
            el.addEventListener('keypress', e => {
                e = e || window.event
                let key = e.keyCode || e.charCode || e.which
                let reg = /\d/ //验证是不是数字
                if (!reg.test(String.fromCharCode(key)) && key > 9 && !e.ctrlKey) {
                    //String.fromCharCode(key)把键盘码转化为字符
                    if (e.preventDefault) e.preventDefault()
                    //如果非数字则调用preventDefault事件阻止默认行为
                    else e.returnValue = false
                }
            })
        }
    }),
    //控制通用流程状态颜色
    status: Vue.directive('status', {
        inserted (el, binding) {
            if(binding.value instanceof  Object) {
                const { statusObj, value } = binding.value
                /*
                    statusObj：当前状态值的归类区间
                    value：状态的值
                 */
                let statusClass
                let num = 0
                for(const key in statusObj) {
                    const val = parseInt(value)
                    if(statusObj[key].includes(val)) {
                        statusClass = key
                        break
                    }
                }
                statusClass = statusClass ? statusClass : 'approved'
                let className
                if(el.className) {
                    className = el.className.split('status-')
                    if(className.length > 1) {
                        className[1] = statusClass
                    }else{
                        className[0] = className[0] + ' '
                        className.push(statusClass)
                    }
                    className = className.join('status-')
                }else{
                    className = 'status-' + statusClass
                }
                el.className = className //更改类名
            }else{
                const statusObj = {
                    invalid: [-1, 5], //作废类
                    approved: [2, 10, 20], //审核完成类
                    audit: [1, 9], //审核过程中类
                    draft: [0], //草稿类
                }
                let statusClass
                let num = 0
                for(const key in statusObj) {
                    const val = parseInt(binding.value)
                    if(statusObj[key].includes(val)) {
                        statusClass = key
                        break
                    }
                }
                statusClass = statusClass ? statusClass : 'approved'
                let className
                if(el.className) {
                    className = el.className.split('status-')
                    if(className.length > 1) {
                        className[1] = statusClass
                    }else{
                        className[0] = className[0] + ' '
                        className.push(statusClass)
                    }
                    className = className.join('status-')
                }else{
                    className = 'status-' + statusClass
                }
                el.className = className
            }
        },
        update (el, binding) {
            if(binding.value instanceof  Object) {
                const { statusObj, value } = binding.value
                let statusClass
                let num = 0
                for(const key in statusObj) {
                    const val = parseInt(value)
                    if(statusObj[key].includes(val)) {
                        statusClass = key
                        break
                    }
                }
                statusClass = statusClass ? statusClass : 'approved'
                let className
                if(el.className) {
                    className = el.className.split('status-')
                    if(className.length > 1) {
                        className[1] = statusClass
                    }else{
                        className[0] = className[0] + ' '
                        className.push(statusClass)
                    }
                    className = className.join('status-')
                }else{
                    className = 'status-' + statusClass
                }
                el.className = className
            }else{
                const statusObj = {
                    invalid: [-1, 5], //作废类
                    approved: [2, 10, 20], //审核完成类
                    audit: [1, 9], //审核过程中类
                    draft: [0], //草稿类
                }
                let statusClass
                let num = 0
                for(const key in statusObj) {
                    const val = parseInt(binding.value)
                    if(statusObj[key].includes(val)) {
                        statusClass = key
                        break
                    }
                }
                statusClass = statusClass ? statusClass : 'approved'
                let className
                if(el.className) {
                    className = el.className.split('status-')
                    if(className.length > 1) {
                        className[1] = statusClass
                    }else{
                        className[0] = className[0] + ' '
                        className.push(statusClass)
                    }
                    className = className.join('status-')
                }else{
                    className = 'status-' + statusClass
                }
                el.className = className
            }
        }
    }),
    //控制表格拖拽
    sortTable: Vue.directive('sortTable', {
        inserted (el, binding) {
            const wrapperTr = $(el).find('.el-table__header-wrapper tr').get(0) //找到表头的原生节点
            const { tableData, _this, ref } = binding.value
            const dropCol = _this.$refs[ref].columns //后去el-table列头数据
            Sortable.create(wrapperTr, {
                animation: 180,
                delay: 0,
                onEnd: evt => { //拖动完更改列头数据顺序即可完成视图更新
                    const oldItem = dropCol[evt.oldIndex]
                    dropCol.splice(evt.oldIndex, 1)
                    dropCol.splice(evt.newIndex, 0, oldItem)
                }
            })
        },
        update (el, binding) {
            const wrapperTr = $(el).find('.el-table__header-wrapper tr').get(0)
            const { tableData, _this, ref } = binding.value
            const dropCol = _this.$refs[ref].columns
            Sortable.create(wrapperTr, {
                animation: 180,
                delay: 0,
                onEnd: evt => {
                    const oldItem = dropCol[evt.oldIndex]
                    dropCol.splice(evt.oldIndex, 1)
                    dropCol.splice(evt.newIndex, 0, oldItem)
                }
            })
        }
    })
}

export default verify
