<template>
    <main>
        <div class="list-title dfa">反馈中心
            <div class="publishFeedback dfa pointer" @click="dialogVisible = true">新增反馈</div>
        </div>
        <div class="tab df">
            <div :class="activeTab === 0 ? 'active' : ''" @click="activeTab = 0">公示反馈</div>
            <div :class="activeTab === 1 ? 'active' : ''" @click="activeTab = 1">我的反馈</div>
        </div>
        <div v-show="activeTab === 0">
            <div class="list p20">
                <div class="list-item" v-for="item in list" :key="item.id">
                    <div class="title mb20 pointer textOverflow1" @click="showContent(item)">留言内容：{{ item.messagContent }}</div>
                    <div class="reply pointer textOverflow1" @click="showContent(item)">管理员回复：{{ item.respondContent || '暂无回复' }}</div>
                    <div style="text-align: right;"> {{ item.messagDate }}</div>
                </div>
            </div>
        </div>
        <div v-show="activeTab === 1">
            <div class="list p20">
                <div class="list-item" v-for="item in list" :key="item.id">
                    <div class="title mb20 pointer textOverflow1" @click="showContent(item)">留言内容：{{ item.messagContent }}</div>
                    <div class="reply pointer textOverflow1" @click="showContent(item)">管理员回复：{{ item.respondContent || '暂无回复' }}</div>
                    <div style="text-align: right;"> {{ item.messagDate }}</div>
                </div>
            </div>
        </div>
        <!--      分页器-->
        <Pagination
            :total="page.totalCount"
            :currentPage="page.currPage"
            :pageSize="page.pageSize"
            :totalPage="page.totalPage"
            :destination="page.destination"
            @sizeChange="sizeChange"
            @currentChange="currentChange"
        />
        <frontDialog title="新增反馈" :visible.sync="dialogVisible" top="20vh">
            <div class="write-feedback p20">
                <el-input
                    type="textarea"
                    v-model="content"
                    :rows="8" placeholder="请输入您的反馈..."
                    clearable
                    maxlength="200"
                    resize="none"
                    show-word-limit
                />
                <div class="dfc">
                    <el-button class="dfa mt20" :loading="isPublishing" @click="publishFeedback">发布反馈</el-button>
                </div>
            </div>
        </frontDialog>
        <frontDialog title="回复内容" :visible.sync="contentVisible">
            <div class="response-form p20">
                <div class="title mb20">留言内容：{{ contentForm.messagContent }}</div>
                <div class="mb10">回复内容：</div>
                <div class="response">{{ contentForm.respondContent || '暂无回复' }}</div>
                <div class="dfc">
                    <el-button class="dfa mt20" @click="contentVisible = false">确定</el-button>
                </div>
            </div>
        </frontDialog>
    </main>
</template>
<script>
import Pagination from '../../components/pagination.vue'
import frontDialog from '@/pages/frontStage/components/dialog.vue'
import { createMessage, publicDisplay, queryPage } from '@/api/platform/mail/outbox'

export default {
    name: 'feedback',
    components: { Pagination, frontDialog },
    data () {
        return {
            limit: null,
            sendType: null,
            activeTab: 0,
            contentVisible: false,
            dialogVisible: false,
            isPublishing: false, // 是否处于发布中状态
            list: [],
            totalList: [],
            contentForm: {},
            content: null,
            page: {
                totalCount: null, // 总数据条数
                currPage: 1, // 当前页码
                pageSize: 10, // 每页条数
                destination: 1, // 要跳转页数
                totalPage: 1, // 总页数
            },
        }
    },
    watch: {
        activeTab (value) {
            value === 0 ? this.getTotalList() : this.getMyList()
        },
    },
    methods: {
        showContent (item) {
            let { messagContent, respondContent } = item
            this.contentForm = { messagContent, respondContent }
            this.contentVisible = true
        },
        /**
         * 查询所有收件人列表
         *
         * 消息列表
         */
        getSendList () {
            let params = {
                limit: this.page.pageSize,
                page: this.page.currPage,
                sendType: 2
            }
            queryPage(params).then(res => {
                this.sendMessageTable = res.list
                this.page = res
            })
        },
        // 发布反馈
        publishFeedback () {
            this.isPublishing = true
            let params = {
                messagContent: this.content,
            }
            if (params.messagContent != null) {
                // setTimeout(() => {
                //     this.isPublishing = false
                //     this.dialogVisible = false // 关闭弹窗
                //     this.$message.success('发布成功') // 发布成功提示
                // }, 1000)
                createMessage(params).then(res => {
                    if (res.code === 200) {
                        this.$message({
                            message: '反馈成功',
                            type: 'success'
                        })
                    }

                    this.formData = {}
                    this.isPublishing = false
                    this.dialogVisible = false
                    this.getSendList()
                    location.reload()
                })
            } else {
                this.$message({
                    message: '反馈失败,请输入反馈内容',
                    type: 'error'
                })
                this.isPublishing = false
            }
        },
        currentChange (currPage) {
            this.page.currPage = currPage
            if (this.activeTab === 0) {
                this.getTotalList()
            }
            if (this.activeTab === 1) {
                this.getMyList()
            }
        },
        sizeChange (pageSize) {
            this.page.pageSize = pageSize
            if (this.activeTab === 0) {
                this.getTotalList()
            }
            if (this.activeTab === 1) {
                this.getMyList()
            }

        },
        // 获取完整反馈列表
        getTotalList () {
            let params = {
                limit: this.page.pageSize,
                page: this.page.currPage,
            }
            publicDisplay(params).then(res => {
                this.list = res.list || []
                this.page = res
                console.log(res)
            })
        },
        // 获取我的反馈列表
        getMyList () {
            let params = {
                limit: this.page.pageSize,
                page: this.page.currPage,
            }
            queryPage(params).then(res => {
                this.list = res.list || []
                this.page = res
            })
        },
    },
    created () {
        this.getTotalList()
    },
}
</script>

<style scoped lang="scss">
main {
    height: 100%;
    border: 1px solid #e6e6e6;
}

.list-title {
    height: 50px;
    padding: 15px 0 15px 20px;
    font-size: 18px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.tab {
    padding: 20px 20px 10px;
    font-size: 16px;
    color: rgba(102, 102, 102, 1);

    div {
        margin-right: 50px;
        cursor: pointer;
    }

    .active {
        color: rgba(0, 0, 0, 1);

        &::after {
            content: '';
            display: block;
            width: 100%;
            height: 2px;
            margin-top: 4px;
            background-color: rgba(34, 111, 199, 1);
        }
    }
}

.list {
    min-height: 600px;

    .list-item:not(:last-of-type) {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e6e6e6;
    }

}
.title {
    font-size: 16px;
    font-weight: bold;
    color: #216ec6;
}

.publishFeedback {
    width: 120px;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(33, 110, 198, 1);
    // border: 1px solid rgba(33, 110, 198, 1);
    justify-content: center;
    position: absolute;
    top: 10px;
    right: 19px;
    user-select: none;

    &:active {
        color: #fff;
        background-color: rgba(33, 110, 198, 1);
    }
}

.write-feedback, .response-form {
    /deep/ .el-textarea {
        width: 90%;
        margin: 0 auto;

        .el-textarea__inner {
            border-radius: 0;
        }
    }

    /deep/ .el-button {
        height: 40px;
        //padding: 0 14px;
        line-height: 40px;
        border-radius: 0;
        color: #fff;
        background-color: #216ec6;
        user-select: none;
    }
}

.response-form {
    .response {
        min-height: 300px;
        padding: 10px;
        border: 1px solid #cdcdcd;
    }
}
</style>