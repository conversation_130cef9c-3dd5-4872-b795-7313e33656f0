<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-bottom: 70px; padding-top: 70px" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="竞价详情" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="商品信息" name="productInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="审核历史" name="auditRecords" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="竞价记录" name="biddingBidRecords" :disabled="clickTabFlag" v-if="formData.biddingState == 2 || formData.biddingState == 3">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">订单信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="竞价采购编号：">
                                            <span>{{ formData.biddingSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="标题：">
                                            <span>{{ formData.title }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发布时间：">
                                            <span>{{ formData.startTime }}</span>
                                        </el-form-item>
                                    </el-col>
<!--                                    <el-col :span="12">-->
<!--                                        <el-form-item label="公示状态：">-->
<!--                                            <el-tag v-if="formData.publicityState == 0" type="info">未发布</el-tag>-->
<!--                                            <el-tag type="success" v-if="formData.publicityState == 1">已发布</el-tag>-->
<!--                                        </el-form-item>-->
<!--                                    </el-col>-->
                                    <el-col :span="12">
                                        <el-form-item label="截止时间：">
                                            <span>{{ formData.endTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="联系人名称：">
                                            <span>{{ formData.linkName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="联系电话：">
                                            <span>{{ formData.linkPhone }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="状态：">
                                            <el-tag type="info" v-if="formData.state == 0">草稿</el-tag>
                                            <el-tag v-if="formData.state == 1">待审核</el-tag>
                                            <el-tag type="danger"  v-if="formData.state == 2">审核失败</el-tag>
                                            <el-tag type="success" v-if="formData.state == 5">审核通过</el-tag>
                                            <el-tag type="success" v-if="formData.state == 7">已确认</el-tag>
                                            <el-tag type="danger"  v-if="formData.state == 9">已流标</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="时间状态：">
                                            <el-tag v-if="formData.biddingState == 1" type="info">未开始</el-tag>
                                            <el-tag type="success" v-if="formData.biddingState == 2">进行中</el-tag>
                                            <el-tag type="danger" v-if="formData.biddingState == 3">已结束</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="创建时间：">
                                            <span>{{ formData.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="竞价说明：">
                                            <editor v-model="formData.biddingExplain" :disabled ='true'></editor>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div id="productInfo" class="con" v-loading="tableLoading">
                        <div class="tabs-title" id="contractList">商品信息</div>
                        <div class="e-table" style="background-color: #fff">
                            <el-table ref="masterOrderItemRef"
                                      border
                                      style="width: 100%"
                                      :data="formData.biddingProducts"
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="orderSn" label="订单号" width="260"></el-table-column>
                                <el-table-column prop="productName" label="商品名称" width="200px"></el-table-column>
                                <el-table-column prop="spec" label="规格型号" width=""></el-table-column>
                                <el-table-column prop="productTexture" label="商品材质" width=""></el-table-column>
                                <el-table-column prop="deliveryDate" label="送货时间" width=""></el-table-column>
                                <el-table-column prop="deliveryAddress" label="送货地点" width=""></el-table-column>
                                <el-table-column prop="remarks" label="备注" width=""></el-table-column>
                                <el-table-column prop="num" label="数量" width=""></el-table-column>
                                <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <div id="auditRecords" class="con" v-loading="tableLoading2">
                        <div class="tabs-title" id="auditRecords">审核历史</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="formData.auditRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="auditType" label="审核类型" width="160">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.auditType == 1">提交审核</el-tag>
                                        <el-tag v-if="scope.row.auditType == 2">变更审核</el-tag>
                                        <el-tag v-if="scope.row.auditType == 6">中标审核</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200">
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160">
                                </el-table-column>
                                <el-table-column prop="auditResult" label="审核意见" width="">
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <div id="biddingBidRecords" class="con" v-if="formData.biddingState == 2 || formData.biddingState == 3">
                        <div class="tabs-title" id="biddingBidRecords">竞价记录</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="formData.biddingBidRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="bidRecordSn" label="竞价记录编号" width="200">
                                    <template slot-scope="scope">
                                        <span class="action" @click="showBidingRecordInfoM(scope.row)">{{scope.row.bidRecordSn}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="supplierName" label="机构名称" width="200"/>
                                <el-table-column prop="bidAmount" label="不含税总金额" width=""/>
                                <el-table-column prop="bidRateAmount" label="含税总金额" width=""/>
                                <el-table-column prop="bidTime" label="竞价时间" width=""/>
                                <el-table-column prop="contactPerson" label="联系人" width=""/>
                                <el-table-column prop="contactPhone" label="联系电话" width=""/>
                                <el-table-column prop="remarks" label="备注信息" width=""/>
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
            <el-dialog v-dialogDrag id="supplierDialog"  title="选择订单商品" :visible.sync="showBidingOrderItemsDialog"  width="80%" style="margin-left: 10%;" :close-on-click-modal="false">
                <div class="e-table" style="background-color: #fff" v-loading="biddingOrderItemLoading">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div class="left">
                            <el-input  type="text" @blur="listBidingOrderItemsListM" placeholder="输入搜索关键字" v-model="keywords3">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="listBidingOrderItemsListM" />
                            </el-input>
                            <div class="search_box" style="margin-left: 10px">
                                <el-select v-model="bidingOrderItemState" @change="listBidingOrderItemsListM">
                                    <el-option v-for="item in bidingOrderItemStateSelect" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                    </div>
                    <el-table ref="bidingOrderItemRef"
                              border
                              @selection-change="bidingOrderItemSelectM"
                              style="width: 100%"
                              :data="bidingFormOrderItems"
                              class="table"
                              @row-click="bidingOrderItemRowClickM"
                              :max-height="$store.state.tableHeight"
                    >
                        <el-table-column type="selection" width="40"></el-table-column>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column label="订单编号" width="230" prop="orderSn"></el-table-column>
                        <el-table-column prop="productImg" label="商品图片" width="130">
                            <template slot-scope="scope">
                                <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                            </template>
                        </el-table-column>
                        <el-table-column prop="state" label="订单明细状态" width="100">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.state == 2" type="info">待分配</el-tag>
                                <el-tag v-if="scope.row.state == 3" type="info">待分配竞价</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="productName" label="商品名称" width="、"></el-table-column>
                        <el-table-column prop="skuName" label="规格" width="200"></el-table-column>
                        <el-table-column prop="buyCounts" label="数量" width="100"></el-table-column>
                        <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <Pagination
                    :total="paginationInfo3.total"
                    :pageSize.sync="paginationInfo3.pageSize"
                    :currentPage.sync="paginationInfo3.currentPage"
                    @currentChange="listBidingOrderItemsListM"
                    @sizeChange="listBidingOrderItemsListM"
                />
                <div class="buttons">
                    <el-button class="btn-blue" @click="affirmSelectBidingOrderItemsClick">确认选择</el-button>
                    <el-button @click="showBidingOrderItemsDialog = false">关闭</el-button>
                </div>
            </el-dialog>
            <el-dialog v-dialogDrag id="supplierDialog"  title="竞价记录详情" :visible.sync="showBidingRecord"  width="80%" style="margin-left: 10%;" :close-on-click-modal="false">
                <div class="e-table" style="background-color: #fff" v-loading="showBidingRecordLoading">
                    <el-form :model="biddingRecordForm" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="竞价记录编号：">
                                    <span>{{ biddingRecordForm.bidRecordSn }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="联系电话：">
                                    <span>{{ biddingRecordForm.contactPhone }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="联系人：">
                                    <span>{{ biddingRecordForm.contactPerson }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="机构名称：">
                                    <span>{{ biddingRecordForm.supplierName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="不含税总金额：">
                                    <span>{{ biddingRecordForm.bidAmount }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="含税总金额：">
                                    <span>{{ biddingRecordForm.bidRateAmount }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="报价时间：">
                                    <span>{{ biddingRecordForm.bidTime }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <el-table
                        border
                        style="width: 100%"
                        :data="biddingRecordForm.pageList.list"
                        class="table"
                        :max-height="$store.state.tableHeight"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="productName" label="商品名称" width=""></el-table-column>
                        <el-table-column prop="spec" label="规格型号" width=""></el-table-column>
                        <el-table-column prop="productTexture" label="商品材质" width=""></el-table-column>
                        <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                        <el-table-column prop="num" label="数量" width=""></el-table-column>
                        <el-table-column prop="bidPrice" label="不含税到场单价" width=""></el-table-column>
                        <el-table-column prop="taxRate" label="税率" width=""></el-table-column>
                        <el-table-column prop="bidRatePrice" label="含税到场单价" width=""></el-table-column>
                        <el-table-column prop="bidRateAmount" label="含税总金额" width=""></el-table-column>
                        <el-table-column prop="bidAmount" label="不含税总金额" width=""></el-table-column>
                        <el-table-column prop="remarks" label="报价方备注" width="160"></el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <Pagination
                    :total="paginationInfo4.total"
                    :pageSize.sync="paginationInfo4.pageSize"
                    :currentPage.sync="paginationInfo4.currentPage"
                    @currentChange="bidingRecordListByEntityM"
                    @sizeChange="bidingRecordListByEntityM"
                />
                <div class="buttons">
                    <el-button @click="showBidingRecord = false">关闭</el-button>
                </div>
            </el-dialog>
            <div class="buttons">
                <el-button v-if="formData.state == 1" class="btn-greenYellow" @click="checkBidingClick(1,'通过')">通过</el-button>
                <el-button v-if="formData.state == 1" class="btn-delete" @click="checkBidingClick(0,'不通过')">不通过</el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import editor from '@/components/quillEditor'
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
import {
    orderItemList,
    shopManageOrderList,
} from '@/api/platform/order/orders'
import {
    batchUpdateBiddingItemInfo,
    createBidingOrderItemsByBiddingId,
    getPlatformBiddingPurchaseInfo,
    auditBidingInfo,
    listBidingOrderItemsList, platformBidingRecordListByEntity
} from '@/api/shopManage/biding/biding'

export default {
    data () {
        return {
            currentBidRecordId: null,
            biddingRecordForm: {
                pageList: {
                    list: []
                }
            },
            showBidingRecord: false,
            showBidingRecordLoading: false,
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            showBidingOrderItemsDialog: false,
            bidingOrderItemState: null,
            bidingOrderItemStateSelect: [
                { value: null, label: '全部' },
                { value: 2, label: '待分配' },
                { value: 3, label: '待分配竞价' },
            ],
            // 竞价
            bidingFormOrderItems: [],
            bidingOrderItemSelectRow: [],
            biddingOrderItemLoading: false,
            tableLoading2: false,
            formLoading: false,
            keywords: null,
            keywords3: null,
            //基本信息表单数据
            formData: {
            },
            // 表格数据
            tableData: [],
            tableData2: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo3: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo4: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableLoading: false,
            changedRow: [],
        }
    },
    components: {
        Pagination,
        editor
    },
    created () {
        this.getFormData()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        // 显示明细
        showBidingRecordInfoM (row) {
            this.currentBidRecordId = row.bidRecordId
            this.bidingRecordListByEntityM()
            this.showBidingRecord = true
        },
        bidingRecordListByEntityM () {
            let params = {
                page: this.paginationInfo4.currentPage,
                limit: this.paginationInfo4.pageSize,
            }
            if(this.currentBidRecordId != null) {
                params.bidRecordId = this.currentBidRecordId
            }
            this.showBidingRecordLoading = true
            platformBidingRecordListByEntity(params).then(res => {
                this.paginationInfo4.total = res.pageList.totalCount
                this.paginationInfo4.pageSize = res.pageList.pageSize
                this.paginationInfo4.currentPage = res.pageList.currPage
                this.biddingRecordForm = res
            }).finally(() => {
                this.showBidingRecordLoading = false
            })
        },
        checkBidingClick (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗！', async () => {
                if(state == 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            biddingId: this.formData.biddingId,
                            isOpen: 0,
                            auditResult: value,
                        }
                        this.formLoading = true
                        auditBidingInfo(params).then(res => {
                            if(res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.getFormData()
                            }
                        })
                    }).finally(() => {
                        this.formLoading = false
                    })
                }else {
                    let params = {
                        biddingId: this.formData.biddingId,
                        isOpen: 1,
                    }
                    this.formLoading = true
                    auditBidingInfo(params).then(res => {
                        if(res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.getFormData()
                        }
                    }).finally(() => {
                        this.formLoading = false
                    })
                }
            })
        },
        updateBidingProductInfoClick () {
            if(this.changedRow.length == 0) {
                return this.$message.error('未检查到修改！')
            }
            this.clientPop('info', '您确定修改这些数据吗！', async () => {
                this.tableLoading = true
                batchUpdateBiddingItemInfo(this.changedRow).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('修改成功')
                        this.getFormData()
                        this.changedRow = []
                        this.tableLoading = false
                    }
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if(this.changedRow.length === 0) {
                this.changedRow.push({
                    biddingProductId: row.biddingProductId,
                    productName: row.productName,
                    spec: row.spec,
                    productTexture: row.productTexture,
                    deliveryDate: row.deliveryDate,
                    deliveryAddress: row.deliveryAddress,
                    remarks: row.remarks,
                })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if(t.biddingProductId === row.biddingProductId) {
                    t.productName = row.productName
                    t.spec = row.spec
                    t.productTexture = row.productTexture
                    t.deliveryDate = row.deliveryDate
                    t.deliveryAddress = row.deliveryAddress
                    t.remarks = row.remarks
                    flag = true
                }
            })
            if(!flag) {
                this.changedRow.push({
                    biddingProductId: row.biddingProductId,
                    productName: row.productName,
                    spec: row.spec,
                    productTexture: row.productTexture,
                    deliveryDate: row.deliveryDate,
                    deliveryAddress: row.deliveryAddress,
                    remarks: row.remarks,
                })
            }
        },
        //  确认选择订单商品
        affirmSelectBidingOrderItemsClick () {
            if(this.bidingOrderItemSelectRow.length == 0) {
                return this.$message.error('未选择订单商品！')
            }
            let params = {
                biddingId: this.formData.biddingId,
                biddingSn: this.formData.biddingSn,
                orderItems: this.bidingFormOrderItems
            }
            this.clientPop('info', '您确定选择该订单商品吗！', async () => {
                this.biddingOrderItemLoading = true
                createBidingOrderItemsByBiddingId(params).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('修改成功')
                        this.getFormData()
                        this.bidingOrderItemSelectRow = []
                        this.showBidingOrderItemsDialog = false
                    }
                }).finally(() => {
                    this.biddingOrderItemLoading = false
                })
            })

        },
        // 选择订单商品点击
        selectBidingOrderItemsClick () {
            this.listBidingOrderItemsListM()
            this.showBidingOrderItemsDialog = true
        },
        bidingOrderItemSelectM (value) {
            this.bidingOrderItemSelectRow = value
        },
        bidingOrderItemRowClickM (row) {
            row.flag = !row.flag
            this.$refs.bidingOrderItemRef.toggleRowSelection(row, row.flag)
        },
        listBidingOrderItemsListM () {
            let params = {
                page: this.paginationInfo3.currentPage,
                limit: this.paginationInfo3.pageSize,
            }
            if(this.keywords3 != null) {
                params.keywords = this.keywords3
            }
            if(this.bidingOrderItemState != null) {
                params.state = this.bidingOrderItemState
            }
            this.biddingOrderItemLoading = true
            listBidingOrderItemsList(params).then(res => {
                this.paginationInfo3.total = res.totalCount
                this.paginationInfo3.pageSize = res.pageSize
                this.paginationInfo3.currentPage = res.currPage
                this.bidingFormOrderItems = res.list
            }).finally(() => {
                this.biddingOrderItemLoading = false
            })
        },
        getFormData () {
            this.formLoading = true
            getPlatformBiddingPurchaseInfo({ biddingSn: this.$route.query.biddingSn }).then(res => {
                this.formData = res
            }).finally(() => {
                this.formLoading = false
            })
        },
        getTableData2 () {
            let params = {
                parentOrderId: this.formData.orderId,
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                isQueryTwoOrder: true
            }
            this.tableLoading2 = true
            shopManageOrderList(params).then(res => {
                this.tableData2 = res.list
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading2 = false
            })
        },
        // 获取表格数据
        getTableData () {
            let params = {
                orderId: this.formData.orderId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.keywords != null) {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            orderItemList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto auto 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 780px;
        margin-top: 0px;
    }
}

/deep/ #supplierDialog {
    .el-dialog__body {
        height: 580px;
        margin-top: 0px;
    }
}
</style>
