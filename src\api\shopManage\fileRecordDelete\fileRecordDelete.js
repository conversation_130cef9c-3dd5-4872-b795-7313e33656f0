import service from '@/utils/request'

const { httpPost, httpGet } = service

const createFileRecordDelete = params => {
    return httpPost({
        url: '/materialMall/fileRecordDelete/create',
        params
    })
}
const getPlatformFreeAccountAndAddress = params => {
    return httpGet({
        url: '/materialMall/platformYearFeeRecord/getPlatformFreeAccountAndAddress',
        params
    })
}
const getIsYearServe = params => {
    return httpGet({
        url: '/materialMall/platformYearFeeRecord/getIsYearServe',
        params
    })
}
const createWFileRecordDelete = params => {
    return httpPost({
        url: '/materialMall/w/fileRecordDelete/create',
        params
    })
}

export {
    createFileRecordDelete,
    getIsYearServe,
    getPlatformFreeAccountAndAddress,
    createWFileRecordDelete,
}