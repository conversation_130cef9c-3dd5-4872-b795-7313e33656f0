<template>
    <div class="articile">
        <div v-html="content"></div>
    </div>
</template>
<script>
import { getWebInfo } from '@/api/frontStage/webInfo'
import { switchTitle } from '@/utils/common.js'
export default {
    data () {
        return {
            content: '',
        }
    },
    watch: {
        // eslint-disable-next-line
        $route (route) {
            this.getData()
            document.title = '物资采购平台-' + switchTitle(this.$route.query.key)
        }
    },
    created () {
        this.getData()
        document.title = '物资采购平台-' + switchTitle(this.$route.query.key)
    },
    methods: {
        getData () {
            let params = { mallType: 1, state: 1, orderBy: 3, programaKey: this.$route.query.key, }
            getWebInfo(params).then(res => {
                if(res.list[0]) {
                    return this.content = res.list[0].content
                }
                this.content = '暂无数据'
            })
        },
    },
}
</script>
<style scoped lang="scss">
.articile {
    min-height: 893px;
    padding: 30px 40px 38px;
    font-size: 16px;
    line-height: 28px;
    color: rgba(51, 51, 51, 1);
}
</style>