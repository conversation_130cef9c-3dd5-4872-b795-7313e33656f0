<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" >
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn">
              <el-select v-model="filterData.tenderForm"  @change="getDateList" placeholder="请选择采购方式">
                <el-option v-for="item in tenderForms" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="search_box">
            <el-input type="text" @keyup.enter.native="getDateList" placeholder="项目名称/采购人" v-model="keyword"><img src="@/assets/search.png"
                                                                                                                          slot="suffix" @click="getDateList" /></el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table">
        <el-table @row-click="handleCurrentInventoryClick2" ref="eltableCurrentRow2" class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="项目名称"  width="260">
            <template slot-scope="scope">
              <span class="action" @click="handleView(scope)">{{scope.row.tenderName}}</span>
            </template>
          </el-table-column>
          <el-table-column label="采购人" width="300" prop="tenderUser">
          </el-table-column>
          <el-table-column label="采购方式" width="" >
            <template slot-scope="scope">
              <span v-if="scope.row.tenderForm === '0'" >公开招标</span>
              <span v-if="scope.row.tenderForm === '1'" >邀请招标</span>
              <span v-if="scope.row.tenderForm === '2'" >询价</span>
              <span v-if="scope.row.tenderForm === '3'" >竞争性谈判</span>
              <span v-if="scope.row.tenderForm === '4'" >单一性来源</span>
            </template>
          </el-table-column>
          <el-table-column label="招标单位" width="" prop="applyOrgName">
          </el-table-column>
          <el-table-column label="发布时间" width="" prop="releaseDate">
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                     @currentChange="currentChange" @sizeChange="sizeChange" />
    </div>
    <!-- ----------------查询弹框---------------- -->
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="true" :before-close="closeDialog">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
        <el-row>
          <el-col :span="18" >
            <el-form-item label="采购方式：">
              <el-select v-model="filterData.tenderForm"  >
                <el-option v-for="item in tenderForms" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" >
            <el-form-item label="项目名称：">
              <el-input clearable  v-model="filterData.tenderName" placeholder="请输入招标项目名称" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" >
            <el-form-item label="采购人：">
              <el-input clearable  v-model="filterData.tenderUser" placeholder="请输入采购人" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" >
            <el-form-item label="招标单位：">
              <el-input clearable  v-model="filterData.applyOrgName" placeholder="请输入申请机构" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="发布时间：">
              <el-date-picker
                  value-format="yyyy-MM-dd"
                  v-model="filterData.releaseDate"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getList } from '@/api/platform/shop/shopAudit'
import { winTenderList } from '@/api/supplierSys/bidManage/bidManage/bidManage'
import { debounce } from '@/utils/common'
import { mapActions, mapState } from 'vuex'

export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getList(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            screenHeight: 0,
            screenWidth: 0,
            queryVisible: false,
            socialCode: '',
            keyword: '',
            currentRow: null,
            selectedRows: [],
            pages: {
                totalCount: 0,
                currPage: 1,
                pageSize: 20,
            },
            tenderForms: [
                {
                    value: null,
                    label: '全部'
                },
                {
                    value: 0,
                    label: '公开招标'
                },
                {
                    value: 1,
                    label: '邀请招标'
                },
                {
                    value: 2,
                    label: '询价'
                },
                {
                    value: 3,
                    label: '竞争性谈判'
                },
                {
                    value: 4,
                    label: '单一性来源'
                }],
            // 高级查询数据对象
            filterData: {
                tenderForm: null,
                tenderName: '',
                tenderUser: '',
                releaseDate: [],
                applyOrgName: ''

            },
            tableData: [],
            formData: {},
        }
    },
    mounted () {
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        this.getDateList()
        this.getParams()
    },
    methods: {
        getDateList () {
            if (this.userInfo.isExternal === 1) {
                let params = {
                    limit: this.pages.pageSize,
                    page: this.pages.currPage,
                    mallConfig: 0
                }

                if( this.filterData.tenderForm != null) {
                    params.tenderForm = this.filterData.tenderForm
                }
                if (this.filterData.releaseDate != 0) {
                    params.startTime = this.filterData.releaseDate[0]
                    params.endTime = this.filterData.releaseDate[1]
                }
                if (this.filterData.tenderName != '' && this.filterData.tenderName != null) {
                    params.tenderName = this.filterData.tenderName
                }

                if (this.filterData.applyOrgName != '' && this.filterData.applyOrgName != null) {
                    params.applyOrgName = this.filterData.applyOrgName
                }
                if (this.keyword != '' && this.keyword != null) {
                    params.keyword = this.keyword
                }
                if (this.tenderUser != '' && this.tenderUser != null) {
                    params.tenderUser = this.tenderUser
                }
                winTenderList(params).then(res=>{

                    this.tableData = res.list
                    this.pages = res
                })
            }

        },
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                tenderForm: '',
                tenderName: '',
                tenderUser: '',
                releaseDate: [],
                applyOrgName: ''
            }
            done()
        },
        hideDialog () {
            this.filterData = {
                tenderForm: '',
                tenderName: '',
                tenderUser: '',
                releaseDate: [],
                applyOrgName: ''
            }
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keyword: this.keyword,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            this.getDateList()
            this.queryVisible = false
            this.filterData = {
                tenderForm: '',
                tenderName: '',
                tenderUser: '',
                releaseDate: [],
                applyOrgName: ''
            }
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleView (scope) {
            this.formData = scope.row
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/bidManage/bidManageDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'bidManageDetail',
                params: {
                    row: scope.row,
                    state: 1
                }
            })
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                this.pages = res
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },

        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 获取屏幕大小
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}
.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}

</style>
