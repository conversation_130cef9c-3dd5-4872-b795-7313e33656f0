import service from '@/utils/request'

const { httpPost, httpGet } = service

// 修改状态
const updateProductState = params => {
    return httpPost({
        url: '/materialMall/product/updateProductState',
        params
    })
}
// 修改状态
const updateProductJcState = params => {
    return httpPost({
        url: '/materialMall/product/updateProductJcState',
        params
    })
}
//查询供应商零星商品
const getSupplierLxProductList = params => {
    return httpPost({
        url: '/materialMall/product/getSupplierLxProductList',
        params
    })
}
const getSupplierDzProductList = params => {
    return httpPost({
        url: '/materialMall/product/getSupplierDzProductList',
        params
    })
}
// 修改库存
const updateProductStock = params => {
    return httpPost({
        url: '/materialMall/product/updateProductStock',
        params
    })
}
// 回显
const getMaterialInfo = params => {
    return httpPost({
        url: '/materialMall/product/getMaterialInfo',
        params
    })
}
// 检查商品回显
const getCheckMaterialInfo = params => {
    return httpPost({
        url: '/materialMall/product/getCheckMaterialInfo',
        params
    })
}
// 修改
const updateShopDevice = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/updateShopDevice',
        params
    })
}
// 批量修改（单表）
const updateBatch = params => {
    return httpPost({
        url: '/materialMall/product/updateBatch',
        params
    })
}

// 平台装备列表
const listMaterialPage = params => {
    return httpPost({
        url: '/materialMall/platform/product/listMaterialPage',
        params
    })
}
// 平台装备列表(均价)
const listMaterialPagePVP = params => {
    return httpPost({
        url: '/materialMall/platform/product/listMaterialPagePVP',
        params
    })
}
const allProductStatePass = params => {
    return httpPost({
        url: '/materialMall/platform/product/allProductStatePass',
        params
    })
}
/**
 * 保存物资类别（提供给物资贸易平台,只能保存低值易耗品的下级类别）
 * @param params
 * @returns {*}
 */
const rollBackSaveCategoryLibrary = params => {
    return httpPost({
        url: '/PCWP2/thirdapi/matarialpurchase/rollBackSaveCategoryLibrary',
        params
    })
}
/**
 * 回滚保存物资信息
 * @param params
 * @returns {*}
 */
const rollBackSaveMaterialInfo = params => {
    return httpPost({
        url: '/materialMall/w/thirdApi/material/rollBackSaveMaterialInfo',
        params
    })
}
/**
 * 保存物资类别（提供给物资贸易平台,只能保存低值易耗品的下级类别）
 * @param params
 * @returns {*}
 */
const saveCategoryLibrary = params => {
    return httpPost({
        url: '/thirdapi/matarialpurchase/saveCategoryLibrary',
        params
    })
}
/**
 * 通过分页加载物资信息（提供给物资贸易平台）
 * @param params
 * @returns {*}
 */
const queryPageMaterialDtl = params => {
    return httpPost({
        url: '/PCWP2/thirdapi/matarialpurchase/queryPageMaterialDtl',
        params
    })
}
/**
 * 获取物资信息（提供给物资贸易平台）
 * @param params
 * @returns {*}
 */
const getDataById = params => {
    return httpGet({
        url: '/PCWP2/matarialpurchase/id',
        params
    })
}
/**
 * 批量更新物资使用状态（提供给物资贸易平台）
 * @param params
 * @returns {*}
 */
const batchUpdateMaterialDtlStatePCWP = params => {
    return httpPost({
        url: '/PCWP2/thirdapi/matarialpurchase/batchUpdateMaterialDtlState',
        params
    })
}

const batchUpdateMaterialDtlState = params => {
    return httpPost({
        url: '/materialMall/thirdApi/material/batchUpdateMaterialDtlState',
        params
    })
}
/**
 * 批量更新物资使用状态（提供给物资贸易平台）
 * @param params
 * @returns {*}
 */
const batchUpdateCategoryLibraryState = params => {
    return httpPost({
        url: '/PCWP2/matarialpurchase/batchUpdateCategoryLibraryState',
        params
    })
}
/**
 * 保存物资信息（提供给物资贸易平台,只能保存低值易耗品及其所有下级类别的物资）
 * @param params
 * @returns {*}
 */
const saveMaterialInfo = params => {
    return httpPost({
        url: '/materialMall/thirdApi/material/saveMaterialInfo',
        params
    })
}

const outputExcel = params => {
    return httpPost({
        url: '/materialMall/platform/product/outputExcel',
        params,
        responseType: 'blob'
    })
}
const excelExport  = params => {
    return httpGet({
        url: '/materialMall/w/user/testPdf',
        params,
        responseType: 'blob',
    })

}
/**
 * 上架并保存
 * @param params
 * @returns {*}
 */
const updateMaterialAndState = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/updateMaterialAndState',
        params
    })
}
const getProductOneClick = params => {
    return httpPost({
        url: '/materialMall/platform/product/getStockUpInfo',
        params
    })
}
const oneClickStockUp = params => {
    return httpPost({
        url: '/materialMall/platform/product/oneClickStockUp',
        params
    })
}
export {
    queryPageMaterialDtl,
    saveMaterialInfo,
    outputExcel,
    getDataById,
    batchUpdateCategoryLibraryState,
    rollBackSaveMaterialInfo,
    updateProductState,
    getSupplierLxProductList,
    getSupplierDzProductList,
    getMaterialInfo,
    updateShopDevice,
    updateBatch,
    listMaterialPage,
    listMaterialPagePVP,
    getCheckMaterialInfo,
    allProductStatePass,
    saveCategoryLibrary,
    batchUpdateMaterialDtlState,
    batchUpdateMaterialDtlStatePCWP,
    rollBackSaveCategoryLibrary,
    excelExport,
    updateMaterialAndState,
    updateProductJcState,
    getProductOneClick,
    oneClickStockUp,
    updateProductStock
}