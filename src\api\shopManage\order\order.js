import service from '@/utils/request'

const { httpPost, httpGet } = service

// 改价
const updateOrderPrice = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/updateOrderPrice',
        params
    })
}

//大宗临购修改供方综合单价
const updateOrderCostPrice = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/updateOrderCostPrice',
        params
    })
}

// 获取订单
const orderFindById = params => {
    return httpGet({
        url: '/materialMall/orders/findById',
        params
    })
}
// 获取订单
const findByOrderSn = params => {
    return httpGet({
        url: '/materialMall/orders/findByOrderSn',
        params
    })
}
const orderShipDtlList = params => {
    return httpPost({
        url: '/materialMall/orderShipDtl/listByEntity',
        params
    })
}
const updateShipCountsValue = params => {
    return httpPost({
        url: '/materialMall/orderShipDtl/updateShipCountsByDtlIds',
        params
    })
}
export {
    updateOrderCostPrice,
    updateOrderPrice,
    orderFindById,
    findByOrderSn,
    orderShipDtlList,
    updateShipCountsValue
}