<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;"  v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="基础信息" name="msgID1" :disabled="clickTabFlag"/>
                <el-tab-pane label="对公账号" name="msgID2" :disabled="clickTabFlag"/>
                <el-tab-pane label="缴费信息" name="msgID3" :disabled="clickTabFlag"/>
                <el-tab-pane label="审核记录" name="msgID4" :disabled="clickTabFlag" />
                <div id="tabs-content">
                    <div id="baseInfo" class="con">
                        <el-form :model="formDtl" label-width="200px" :rules="formDtlRules" ref="formDtlRef" :disabled="false" class="demo-ruleForm">
                            <div class="tabs-title" id="msgID1">基础信息</div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="店铺名称：" prop="shopName">
                                        <span>{{formDtl.shopName}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="供应商名称：" prop="enterpriseName">
                                        <span>{{formDtl.enterpriseName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="上期年费有效期截止日期：" prop="previousFeeEndDate">
                                        <span>{{formDtl.previousFeeEndDate}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="上期年费状态：" prop="previousFeeStatus">
                                        <span>{{formDtl.previousFeeStatus}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <div class="tabs-title" id="msgID2">对公账户</div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="开户银行：" prop="khyh">
                                        <span>{{platformFreeyhAddress}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="银行户名：" prop="yhhm">
                                        <span>{{platformFreeyhOrgName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="银行账号：" prop="yhzh">
                                        <span>{{platformFreeyhAccount}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <div class="tabs-title" id="msgID3">缴费信息</div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="缴费金额（元）：" prop="payAmount">
                                        <span>{{fixed2(formDtl.payAmount)}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="状态：" prop="state">
                                        <el-tag type="info" v-if="formDtl.state == 0">草稿</el-tag>
                                        <el-tag v-if="formDtl.state == 1">待审核</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 2">审核通过</el-tag>
                                        <el-tag type="danger" v-if="formDtl.state == 3">审核不通过</el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item  label="缴费证明：" prop="file">
                                        <el-upload
                                            :class="'hide_box_min'"
                                            v-loading="uploadLoading"
                                            class="upload-demo"
                                            action="fakeaction"
                                            :limit="1"
                                            :file-list="fileList"
                                            :before-upload="handleBeforeUpload"
                                            :auto-upload="true"
                                            :http-request="uploadLicenseBusiness"
                                            list-type="picture-card"
                                            :disabled="true">
                                            <div slot="tip" class="el-upload__tip" v-show="!fileList || fileList.length === 0">只能上传图片文件</div>
                                            <i slot="default" class="el-icon-plus"></i>
                                            <div slot="file" slot-scope="{file}">
                                                <img
                                                    class="el-upload-list__item-thumbnail"
                                                    :src="file.url" alt="">
                                                <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                  </span>
                                            </div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="提交时间：" prop="tjsj">
                                        {{formDtl.gmtCreate}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                        <div id="auditRecords" class="con">
                            <div class="tabs-title" id="msgID4">审核记录</div>
                            <div class="e-table"  style="background-color: #fff">
                                <el-table
                                    border
                                    style="width: 100%"
                                    :data="formDtl.auditRecords"
                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                    :row-style="{ fontSize: '14px', height: '48px' }"
                                >
                                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                                    <el-table-column prop="auditType" label="审核结果" width="160">
                                        <template slot-scope="scope">
                                            <el-tag v-if="scope.row.auditType == 1">通过</el-tag>
                                            <el-tag v-if="scope.row.auditType == 2">未通过</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="auditResult" label="审核意见" width="">
                                    </el-table-column>
                                    <el-table-column prop="founderName" label="审核人" width="200">
                                    </el-table-column>
                                    <el-table-column prop="gmtCreate" label="审核时间" width="160">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                </div>
            </el-tabs>
            <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>

            <!-- 审核通过弹框 -->
            <el-dialog class="checkPassModal"
                title="确认审核通过"
                :visible.sync="auditPassDialogVisible"
                width="450px"
                :close-on-click-modal="false">
                <div class="checkPassDiv">
                    <p style="margin-bottom: 15px; color: #606266; font-size: 14px;">
                        上期年费有效期截止日期：{{ formDtl.previousFeeEndDate || '无' }}
                    </p>

                    <!-- 上期年费已到期的情况 -->
                    <p v-if="isExpired" style="margin-bottom: 15px; color: #E6A23C; font-size: 14px;">
                        截止到今日店铺上期年费已到期，请确认本期年费有效期开始日期
                    </p>

                    <div style="margin-bottom: 15px;">
                        <label v-if="isExpired" style="display: inline-block; margin-bottom: 5px; color: #606266; font-size: 14px;">本期年费缴费有效期开始日期：</label>
                        <label v-else style="display: inline-block; color: #606266; font-size: 14px;">本期年费缴费有效期开始日期：</label>
                        <el-date-picker
                            v-if="isExpired"
                            v-model="auditPassForm.startDate"
                            type="date"
                            placeholder="请选择"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            style="width: 200px;"
                            @change="calculateEndDate">
                        </el-date-picker>
                        <span v-else style="color: #303133; font-size: 14px;">{{ auditPassForm.startDate }}</span>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: inline-block; margin-bottom: 5px; color: #606266; font-size: 14px;">本期年费缴费有效期截止日期：</label>
                        <span style="color: #303133; font-size: 14px;">{{ auditPassForm.endDate || '根据开始日期自动计算' }}</span>
                    </div>
                </div>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="cancelAuditPass">取消</el-button>
                    <el-button type="primary" @click="confirmAuditPass">确认</el-button>
                </div>
            </el-dialog>
        </div>
        <div class="buttons">
            <el-button type="primary" class="btn-greenYellow"
                       v-if="formDtl.state === 1 "
                       @click="auditPlanM(1, '通过')">通过
            </el-button>
            <el-button type="primary" class="btn-delete"
                       v-if="formDtl.state === 1 "
                       @click="auditPlanM(0, '未通过')">未通过
            </el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { mapState } from 'vuex'
import { findBySn, updateFee, deleteFee, auditFee, supplierFetchSystemParams } from '@/api/fee/feeApi'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'

const getSysValue = (systemParams, key) => {
    if (!systemParams) {
        return '加载中'
    }
    const systemParam = systemParams.find(s => s.code == key)
    if (!systemParam) {
        return '加载失败'
    }
    return systemParam.keyValue
}

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return  dateStr.split(' ')[0]
        }
    },
    components: {
    },
    data () {
        return {
            fileList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            formDtlRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                reconciliationProductType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' },
                ],
            },
            uploadLoading: false,
            freeAmount: 2000,
            formDtl: {
                files: []
            },
            tableData: [
            ],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            formLoading: false,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            systemParams: null,
            // 审核通过弹框相关
            auditPassDialogVisible: false,
            auditPassForm: {
                startDate: '',
                endDate: ''
            },
            isExpired: false // 上期年费是否已到期
        }
    },
    created () {
        this.getFormDtl()
        // 获取系统参数（对公账户信息）
        supplierFetchSystemParams().then(res => {
            this.systemParams = res
        }).catch(error => {
            console.error('获取系统参数失败:', error)
            // 系统参数获取失败时不显示错误消息，因为这不是关键功能
        })
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        // 平台收费银行开户行
        platformFreeyhAddress () {
            return getSysValue(this.systemParams, 'platformFreeyhAddress')
        },
        // 平台收费银行账号
        platformFreeyhAccount () {
            return getSysValue(this.systemParams, 'platformFreeyhAccount')
        },
        // 平台收费公司名称
        platformFreeyhOrgName () {
            return getSysValue(this.systemParams, 'platformFreeyhOrgName')
        },

    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        // eslint-disable-next-line no-unused-vars
        auditPlanM (state, title) {
            if (state == 0) {
                // 审核未通过 - 直接弹出不通过原因输入框
                this.$prompt('未通过原因', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    inputType: 'textarea',
                    inputPlaceholder: '请输入不通过原因',
                    inputPattern: /^.+$/,
                    inputErrorMessage: '请输入不通过原因'
                }).then(({ value }) => {
                    let params = {
                        id: this.formDtl.paymentRecordId,
                        isOpen: 0,
                        auditResult: value,
                    }
                    this.formLoading = true
                    auditFee(params).then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.getFormDtl()
                        }
                    }).finally(() => {
                        this.formLoading = false
                    })
                })
            } else {
                // 审核通过 - 检查上期年费是否到期
                this.checkPreviousFeeExpiry()
            }
        },

        // 检查上期年费是否到期
        checkPreviousFeeExpiry () {
            const today = new Date()
            const previousEndDate = this.formDtl.previousFeeEndDate
            console.log('previousEndDate', previousEndDate)

            if (previousEndDate && this.formDtl.previousFeeStatus && this.formDtl.previousFeeStatus != '首次缴费') {
                const endDate = new Date(previousEndDate)
                this.isExpired = endDate < today
            } else {
                this.isExpired = true // 如果没有上期年费信息，视为已到期
            }

            this.showAuditPassDialog()
        },

        // 显示审核通过弹框
        showAuditPassDialog () {
            if (this.isExpired) {
                // 上期年费已到期，需要人工确认开始日期
                this.auditPassForm.startDate = ''
                this.auditPassForm.endDate = ''
            } else {
                // 上期年费未到期，自动计算日期
                const previousEndDate = new Date(this.formDtl.previousFeeEndDate)
                const startDate = new Date(previousEndDate)
                startDate.setDate(startDate.getDate() + 1) // 上期年费有效期截止日期+1天

                const endDate = new Date(startDate)
                endDate.setFullYear(endDate.getFullYear() + 1) // +1年

                this.auditPassForm.startDate = this.formatDate(startDate)
                this.auditPassForm.endDate = this.formatDate(endDate)
            }

            this.auditPassDialogVisible = true
        },

        // 格式化日期为 YYYY-MM-DD
        formatDate (date) {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            return `${year}-${month}-${day}`
        },

        // 计算结束日期（开始日期+1年）
        calculateEndDate () {
            if (this.auditPassForm.startDate) {
                const startDate = new Date(this.auditPassForm.startDate)
                const endDate = new Date(startDate)
                endDate.setFullYear(endDate.getFullYear() + 1)
                this.auditPassForm.endDate = this.formatDate(endDate)
            } else {
                this.auditPassForm.endDate = ''
            }
        },

        // 确认审核通过
        confirmAuditPass () {
            if (this.isExpired && !this.auditPassForm.startDate) {
                this.$message.error('请选择本期年费有效期开始日期')
                return
            }

            if (this.isExpired) {
                // 验证开始日期范围
                const startDate = new Date(this.auditPassForm.startDate)
                const today = new Date()
                const previousEndDate = (this.formDtl.previousFeeEndDate && this.formDtl.previousFeeStatus != '首次缴费') ? new Date(this.formDtl.previousFeeEndDate) : null

                if (previousEndDate && startDate <= previousEndDate) {
                    this.$message.error('本期年费缴费有效期开始日期必须大于上期年费有效期截止日期')
                    return
                }

                if (startDate > today) {
                    this.$message.error('本期年费缴费有效期开始日期不可大于当前日期')
                    return
                }
            }

            // 执行审核通过操作
            let params = {
                id: this.formDtl.paymentRecordId,
                isOpen: 1,
                serveStartTime: this.auditPassForm.startDate,
                serveEndTime: this.auditPassForm.endDate
            }

            this.formLoading = true
            auditFee(params).then(res => {
                if (res.code != null && res.code == 200) {
                    this.$message.success('操作成功')
                    this.auditPassDialogVisible = false
                    this.getFormDtl()
                }
            }).finally(() => {
                this.formLoading = false
            })
        },

        // 取消审核通过
        cancelAuditPass () {
            this.auditPassDialogVisible = false
            this.auditPassForm.startDate = ''
            this.auditPassForm.endDate = ''
        },
        checkInputQty () {
            const regex = /^-?\d+$/
            if (!regex.test(this.formDtl.paymentDuration)) {
                this.$message.error('请输入有效的数字')
                this.formDtl.payAmount = this.freeAmount
                return this.formDtl.paymentDuration = 1
            }
            if (this.formDtl.paymentDuration < 0 || this.formDtl.paymentDuration > 999) {
                this.$message.error('超过限制！')
                this.formDtl.payAmount = this.freeAmount
                return this.formDtl.paymentDuration = 1
            }
            this.formDtl.payAmount = this.formDtl.paymentDuration * this.freeAmount
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.formDtl.files = []
                    this.fileList = []
                }else {
                    this.formDtl.files = []
                    let resO = res[0]
                    this.formDtl.files.push({
                        name: resO.objectName,
                        relevanceType: 1,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.formDtl.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.formDtl.files = []
                this.fileList = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload (file) {
            this.uploadLoading = true
            let image = this.formDtl.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        deleteOneM () {
            this.clientPop('info', '您确定要删除操作吗？', async () => {
                this.formLoading = true
                deleteFee({ id: this.formDtl.paymentRecordId }).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        saveSheetM (num) {
            this.clientPop('info', '您确定要该操作吗？', async () => {
                this.formDtl.submitAud = num
                this.formLoading = true
                updateFee(this.formDtl).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        getFormDtl () {
            this.formLoading = true
            findBySn({ sn: this.$route.query.sn }).then(res => {
                this.formDtl = res
                if (res.files && res.files.length > 0) {
                    let image = res.files[0]
                    this.uploadLoading = true
                    previewFile({ recordId: image.fileFarId }).then(res => {
                        const blob = new Blob([res])
                        const url = window.URL.createObjectURL(blob)
                        this.fileList = []
                        this.fileList.push({
                            name: image.name,
                            url: url
                        })
                    }).finally(() => {
                        this.uploadLoading = false
                    })
                }
            }).catch(error => {
                console.error('获取详情数据失败:', error)
                this.$message.error('获取详情数据失败')
            }).finally(() => {
                this.formLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },

        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
.checkPassModal {
    /deep/ .el-dialog__body {
        height: auto;margin: 0;
    }
    .checkPassDiv {
        padding: 20px;
    }
}
</style>