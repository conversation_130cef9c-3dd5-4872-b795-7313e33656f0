<template>
    <div>
        <webHeader></webHeader>
        <main>
            <div class="contentBox center">
                <router-view></router-view>
            </div>
        </main>
        <webFooter></webFooter>
    </div>
</template>

<script>
import webHeader from '../components/mallWebHeader'
import webFooter from '../components/webFooter'
export default {
    components: {
        webHeader, webFooter
    },
    data () {
        return {}
    },
    methods: {},
}
</script>

<style scoped lang="scss">
main {
    width: 100%;
    padding: 20px 0;
    background-color: #f5f5f5;
    .contentBox {
        width: 1326px;
        height: 622px;
        background-color: #fff;
    }
}
</style>