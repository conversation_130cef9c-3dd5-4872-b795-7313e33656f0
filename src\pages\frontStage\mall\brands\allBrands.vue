<template>
    <main>
        <div class="content center">
            <div class="title dfa">
                <span>热门品牌</span><span>精细挑选 为您选购</span>
            </div>
            <div class="list df">
                <div class="list-item dfa mb20 pointer" v-for="(item, i) in 48" :key="i">
                    <img :src="item.pictureUrl ? imgUrlPrefixAdd + item.pictureUrl : require('@/assets/images/img/queshen3.png')" alt="">
                </div>
            </div>
        </div>
    </main>
</template>
<script>
export default {
    data () {
        return {
            list: [],
        }
    },
    created () {},
    mounted () {},
    methods: {},
}
</script>
<style scoped lang="scss">
main {
    padding: 20px 0;
    font-family: 'SourceHanSansCN-Regular';
    background-color: #f5f5f5;
}
.content {width: 1326px;}
.title {
    margin-bottom: 26px;
    span:first-child {
        margin-right: 10px;
        font-size: 24px;
        line-height: 35px;
    }
    span:last-child {
        line-height: 20px;
        color: #383838;
    }
}
.list {
    flex-wrap: wrap;
    .list-item {
        width: 204px;
        height: 100px;
        background-color: #fff;
        justify-content: center;
        &:not(:nth-of-type(6n)) {margin-right: 20px;}
    }
    img {max-height: 86px;}
}
</style>