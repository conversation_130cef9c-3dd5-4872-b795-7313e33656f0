import service from '@/utils/request'

// eslint-disable-next-line
const { httpPost, httpGet } = service
const findByProgramaKey = params => {
    return httpGet({
        url: '/materialMall/w/richContent/findByProgramaKey',
        params
    })
}
const getCurrentStep = params => {
    return httpGet({
        url: '/materialMall/userCenter/shop/getCurrentStep',
        params
    })
}
const findByProgramaKeyNoRelease = params => {
    return httpGet({
        url: '/materialMall/richContent/findByProgramaKey',
        params
    })
}

export {
    findByProgramaKey,
    findByProgramaKeyNoRelease,
    getCurrentStep,
}