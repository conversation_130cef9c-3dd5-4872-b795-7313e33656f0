<template>
    <div class="base-page">
        <div class="left">
            <select-material-class ref="materialClassRef" :productType="0"/>
        </div>
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn" style="min-width: 490px;">
                            <el-button @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button @click="changePublishState(1)" class="btn-greenYellow">启用
                            </el-button>
                            <!--                            <el-button type="primary" @click="changePublishState(2)" class="btn-delete">停用
                                                        </el-button>
                                                        <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                                                        <el-button type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改排序值</el-button>-->
                            <el-dropdown style="margin-left: 10px" trigger="click" @command="btnClick">
                                <el-button type="primary">
                                    更多操作<i class="el-icon-arrow-down el-icon--right"/>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="stop">停用</el-dropdown-item>
                                    <el-dropdown-item command="del">批量删除</el-dropdown-item>
                                    <el-dropdown-item command="changeSort">批量修改排序值</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                        <el-input type="text" @keyup.enter.native="handleInputSearch" placeholder="品牌名称" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table
                    class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="120">
                        <template v-slot="scope">
                            <span class="action" @click="onDel(scope)">
                                <img src="../../../../assets/btn/delete.png" alt="">
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="品牌名称" width="">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.name }}
                            </span>
                        </template>
                    </el-table-column>
                    <!-- 图片 -->
                    <el-table-column label="图片" width="120" type="index">
                        <template v-slot="scope">
                            <el-image v-show="scope.row.logo != null" style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.logo"></el-image>
                        </template>
                    </el-table-column>
                    <!-- 介绍 -->
                    <el-table-column label="介绍" width="">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.descript }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="首页显示状态" width="">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state==1" type="success">启用</el-tag>
                            <el-tag v-if="scope.row.state==0" type="danger">停用</el-tag>
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column label="备注" width="">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <span class="action" @click="handleView(scope)">-->
                    <!--                                {{ scope.row.remarks }}-->
                    <!--                            </span>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <!-- 图片排序值 -->
                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input type="number" :min="0" v-model="scope.row.sort" @change="getChangedRow(scope.row)"/>
                        </template>
                    </el-table-column>

                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item class="uploader" label="logo：" prop="">
                                <div>
                                    <el-upload
                                        class="avatar-uploader"
                                        action="fakeaction"
                                        v-loading="addLoading"
                                        name="img"
                                        :show-file-list="false"
                                        :auto-upload="true"
                                        :before-upload="handleBeforeUpload"
                                        :on-change="handleUploadChange"
                                        :http-request="uploadImg"
                                    >
                                        <img v-if="formData.logo" :src="imgUrlPrefixAdd + formData.logo" class="avatar" alt="">
                                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    </el-upload>
                                    <el-progress v-show="uploadInProgress" :percentage="uploadPercentage"></el-progress>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="分类：" prop="classId">
                                <category-cascader
                                    :classPath.sync="formData.classNamePath"
                                    :classId.sync='formData.classId'
                                    :catelogPath="formData.classNamePath"
                                    :productType="0"
                                    style="width: 200px"
                                />
<!--                                <div>
                                    <div class="dfa" v-for="item in classCounts" :style="{ marginTop: item !== 1 ? '10px' : 0 }" style="width: 460px; position: relative;" :key="item">
                                        <category-cascader
                                            :classPath.sync="formData.classNamePath"
                                            :classId.sync='formData.classId'
                                            :catelogPath="formData.classNamePath"
                                            :productType="0"
                                            style="width: 200px"
                                        />
                                        <span @click="classCounts += 1" v-if="item === 1" class="pointer action" style="position: absolute; right: 0;">
                                            添加分类
                                        </span>
                                    </div>
                                </div>-->
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="品牌名称：" prop="name">
                                <el-input clearable v-model="formData.name"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="品牌介绍：" prop="descript">
                                <el-input clearable v-model="formData.descript">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input clearable v-model="formData.sort" type="number" placeholder="填写排序值"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input clearable type="textarea" v-model="formData.remarks" autocomplete="off"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <el-button class="next-btn" type="success" @click="saveAndNext">保存并下一条</el-button>
                <div class="right-btn">
                    <el-button type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="品牌状态：">
                            <el-select v-model="filterData.state" clearable placeholder="logo启用状态">
                                <el-option
                                    v-for="item in stateFilter" :key="item.value" :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="品牌名称：">
                            <el-input clearable type="text" v-model="filterData.name" placeholder="请输入品牌名称"/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import SelectMaterialClass from '@/components/classTree'
import CategoryCascader from '@/components/category-cascader'
import {
    batchDelete,
    batchNotPublish,
    batchPublish,
    changeSortValue,
    create,
    del,
    edit,
    getDataById,
    getList
} from '@/api/platform/brand/brandLogo'
import { uploadFile } from '@/api/platform/common/file'
import { debounce, hideLoading, showLoading } from '@/utils/common'

export default {
    components: {
        ComPagination, SelectMaterialClass, CategoryCascader
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) {
                return this.screenHeight - 244
            }
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            classCounts: 1,
            addLoading: false,
            alertName: '品牌',
            queryVisible: false,
            action: '编辑',
            classType: '0',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentClass: null,
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询选项
            mallFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '慧采商城' },
                { value: 0, label: '装备商城' },
            ],
            stateFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '启用' },
                { value: 0, label: '停用' },
            ],
            // 高级查询数据对象
            filterData: {
                classId: null,
                logo: null,
                state: null,
                name: null,
                useType: null,
                mallType: 0,
                orderBy: 1
            },
            // 表单校验规则
            formRules: {
                classId: [
                    { required: true, message: '请选择分类', trigger: 'blur' },
                ],
                name: [{ required: true, message: '请输入品牌名称', trigger: 'blur' }],
            },
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [],
            orgDataTable: [],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            allData: [],
            // 新增编辑 表单数据
            formData: {
                name: '',
                classId: '',
                classNamePath: [],
                brandId: '', //品牌ID
                logo: '', // 品牌logo地址
                descript: '', //品牌介绍
                remarks: '', // 备注
                state: 0, // 启用状态 1启用 0停用
                mallType: 0, // 商城类型
            },
            // logo: '',
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            uploadPercentage: 0,
            uploadInProgress: false,
            init: {
                classId: null,
                keyword: null,
                classPath: [],
                className: null

            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        // this.setUnitMeasur()
    },
    created () {
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy
        }
        if (this.init.classId != null && this.init.classId !== '') {
            params.classId = this.init.classId
        }
        getList(params).then(res => {
            this.pages = res
            this.tableData = res.list
        })
        this.getParams()
    },
    methods: {
        btnClick (command) {
            let actions = {
                'stop': () => this.changePublishState(0),
                'del': () => this.handleDelete(),
                'changeSort': () => this.changeSortValue(),
            }
            actions[command]()
        },
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.formData.classNamePath = nodePath
            this.formData.classId = data.classId
            this.classPath = nodePath
            this.init.className = data.className
            // this.formData.className = data.className
            this.filterData.classId = data.classId
            console.log(this.formData, nodePath)
            this.getTableData()
        },
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                logo: null,
                state: null,
                name: null,
                useType: null,
                mallType: 0,
                orderBy: 1
            }
            done()
        },
        hideDialog () {
            this.filterData = {
                logo: null,
                state: null,
                orderBy: 1
            }
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
            }
            this.requestParams.page = this.pages.currPage
        },
        resetSearchConditions () {
            this.filterData.state = null
            this.filterData.name = ''
        },
        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 启用/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.brandId
            })
            if (!this.selectedRows[0]) {
                this.$message.info('未选择数据')
                return
            }
            console.log(arr)
            let warnMsg = num === 1 ? '您确定要启用选中的' + this.alertName + '吗？' : '您确定要停用选中的' + this.alertName + '吗？'
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('启用成功')
                            this.getTableData()
                        }
                    })
                    break
                case 0:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('停用成功')
                            this.getTableData()
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该' + this.alertName + '吗？', async () => {
                showLoading()
                del({ id: scope.row.brandId }).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                this.$message.info('未选择数据')
                return
            }
            this.clientPop('info', '您确定要删除选中的' + this.alertName + '吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.brandId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange (value) {
            this.paginationInfo.currentPage = value
            this.getTableData()
        },
        sizeChange (value) {
            this.paginationInfo.pageSize = value
            this.getTableData()
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 上传图片
        async uploadImg (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            this.addLoading = true
            uploadFile(form).then(res => {
                this.formData.logo = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                this.formData.logoId = res[0].recordId
                this.$message.success('上传成功')
            }).finally(() => {
                this.addLoading = false
            })
        },
        handleUploadChange (file) {
            if (file.status === 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status === 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        handleView (scope) {
            this.viewList = 'class'
            getDataById({ id: scope.row.brandId }).then(res => {
                this.formData = res
            })
            this.action = '编辑'
            this.logo = scope.row.logo
        },
        handleNew () {
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (!this.changedRow[0]) {
                return this.changedRow.push({ brandId: row.brandId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if (item.brandId === row.brandId) {
                    return i
                }
            })
            if (arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ brandId: row.brandId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                this.$message.info('当前没有排序值被修改')
                return
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                let res = await changeSortValue(this.changedRow)
                if (res.message === '操作成功') {
                    this.$message.success('修改成功')
                    this.getTableData()
                }
            })
        },
        // 获取列表数据
        async getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.classId != null) {
                params.classId = this.filterData.classId
            }
            if (this.keywords != null && this.keywords !== '') {
                params.keywords = this.keywords
            }
            if (this.filterData.state != null && this.filterData.name !== '') {
                params.state = this.filterData.state
            }
            if (this.filterData.name != null && this.filterData.name !== '') {
                params.name = this.filterData.name
            }
            if (this.useType != null && this.useType !== '') {
                params.useType = this.useType
            }
            if (this.filterData.orderBy != null && this.filterData.orderBy !== '') {
                params.orderBy = this.filterData.orderBy
            }
            getList(params).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.pages = res
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            this.formData.classId = ''
            this.formData.classNamePath = []
            this.formData.brandId = ''
            this.formData.logo = ''
            this.formData.name = ''
            this.formData.descript = ''
            this.formData.state = 0
            this.logo = ''
        },
        // 保存新增/删除
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (!valid) return
                if (this.action === '编辑') {
                    return this.handleEditData()
                }
                this.handleCreateData()
            })
        },
        saveAndNext () {
            this.$refs.formEdit.validate(async valid => {
                if (!valid) return
                let res
                showLoading()
                this.action === '编辑' ? res = await edit(this.formData) : res = await create(this.formData)
                hideLoading()
                if (res.code !== 200) return this.$message.error(res.message)
                this.$message.success('操作成功')
                this.formData.logo = ''
                this.formData.brandId = ''
                this.formData.descript = ''
                this.formData.remarks = ''
                this.formData.sort = ''
                this.formData.sort = ''
                this.formData.name = ''
                this.formData.state = null
            })
        },
        // 修改数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message === '操作成功') {
                    this.$message.success('操作成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        // 保存数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message === '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        onCancel () {
            this.emptyForm()
            this.$refs.formEdit.clearValidate()
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    height: 100%;
    padding: 0;
    overflow: scroll;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    //display: flex;
    //align-items: center;
    height: unset !important;
}

/deep/ .el-form-item.uploader {
    height: 200px;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload
.avatar-uploader {
    /deep/ .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/ .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/ .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

/deep/ .el-dialog {
    height: 500px !important;
    padding: 0;

    .el-dialog__header {
        margin-bottom: 20px;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        background: red url(../../../../assets/test.png) no-repeat;

        .el-dialog__title {
            color: #fff;
        }
    }

    .el-dialog__body {
        height: 280px;
        margin-top: 100px;
    }

    .el-dialog__close.el-icon.el-icon-close::before {
        width: 44px;
        height: 44px;
    }
}

/deep/ .next-btn {
    height: 38px;
    margin-right: 10px;
    line-height: 38px;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
    -moz-appearance: textfield !important;
}
</style>
