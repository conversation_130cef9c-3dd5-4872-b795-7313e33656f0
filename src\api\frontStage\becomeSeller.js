import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const createShopEchoInfo = params => {
    params.mallType = 0
    return httpGet({
        url: '/materialMall/userCenter/user/createShopEchoInfo',
        params,
    })
}
const createShopExternal = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/createShopExternal',
        params,
    })
}
export {
    createShopEchoInfo,
    createShopExternal,
}