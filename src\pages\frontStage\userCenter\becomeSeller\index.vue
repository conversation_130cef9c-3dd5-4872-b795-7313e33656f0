<template>
    <!-- 老 开店与缴费 -->
    <div>
        <main>
            <div class="content center" v-loading="showLoading" style="padding-top: 40px">
                <el-steps :active="currentStep-1" align-center v-if="shuDaoFlag == 0">
                    <el-step title="申请开店" description="开通店铺"></el-step>
                    <el-step title="平台初审" description="平台审核店铺"></el-step>
                    <el-step title="待缴费" description="店铺待缴费"></el-step>
                    <el-step title="平台复审" description="平台审核缴费"></el-step>
                    <el-step title="完成" description="完成"></el-step>
                </el-steps>
                <div class="head" v-if="currentStep == 1 || currentStep == 2 || shuDaoFlag == 1">
                    <div class="title center">{{ showForm }}</div>
                    <div class="form"></div>
                </div>
                <div class="formContent" v-if="currentStep == 1 || (shuDaoFlag == 1 && !requestComplete)">
                    <el-form :model="openShopForm" ref="enterpriseForm" :rules="shopRules" label-width="156px" :inline="false">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item class="shortInput" label="店铺名称：" prop="shopName">
                                    <el-input clearable v-model="openShopForm.shopName" placeholder="请输入店铺名称"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item  class="registerAddress" label="店铺地址：" prop="address">
                                    <div>
                                        <el-select ref="selectLabel1" class="province" v-model="openShopForm.province" value-key="" placeholder="省份" @change="(code) => getSubDistrict(code, 1)">
                                            <el-option v-for="item in addressOptions.province" :key="item.value" :label="item.districtName" :value="item.districtCode">
                                            </el-option>
                                        </el-select>
                                        <el-select ref="selectLabel2" class="city" v-model="openShopForm.city" value-key="" placeholder="市级" @change="(code) => getSubDistrict(code, 2)">
                                            <el-option v-for="item in addressOptions.city" :key="item.value" :label="item.districtName" :value="item.districtCode">
                                            </el-option>
                                        </el-select>
                                        <el-select ref="selectLabel3" @visible-change="addressChange"  class="county" v-model="openShopForm.county" value-key="" placeholder="区、县">
                                            <el-option v-for="item in addressOptions.district" :key="item.value" :label="item.districtName"
                                                       :value="item.districtCode">
                                            </el-option>
                                        </el-select>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="详细地址：" prop="detailedAddress">
                                    <el-input v-model="openShopForm.detailedAddress" placeholder="请输入详细地址" clearable></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="主营业务：" prop="mainBusiness">
                                    <el-input v-model="openShopForm.mainBusiness" placeholder="请输入主营业务" clearable></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col class="idCol" :span="11" :offset="0">
                                <el-form-item label="身份证人像面照：" prop="identityCardFace">
                                    <el-upload disabled class="identityUpload" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1, 2)"
                                               :show-file-list="false" :before-upload="handleBeforeUpload">
                                        <img class="identityUpload" v-if="openShopForm.identityCardFace" :src="openShopForm.identityCardFace" alt="">
                                        <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                                    </el-upload>
                                </el-form-item>
                            </el-col>
                            <el-col class="idCol" :span="11" :offset="2">
                                <el-form-item label="身份证国徽面照：" prop="identityCardBadge">
                                    <el-upload disabled class="identityUpload" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2, 2)"
                                               :show-file-list="false" :before-upload="handleBeforeUpload">
                                        <img class="identityUpload" v-if="openShopForm.identityCardBadge" :src="openShopForm.identityCardBadge" alt="">
                                        <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                                    </el-upload>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <div class="uploadTip">图片大小在100K到4M之间，图片格式仅支持：JPG、PNG、JPEG</div>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item class="shortInput" label="姓名：" prop="realName">
                                    <el-input disabled v-model="openShopForm.realName" placeholder="请输入姓名"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                                <el-form-item class="shortInput" label="身份证号码：" prop="identityCard">
                                    <el-input disabled v-model="openShopForm.identityCard" placeholder="请输入身份证号码"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <!--                        <el-row>-->
                        <!--                            <el-col  class="payCol" :span="12">-->
                        <!--                                <el-form-item label="是否支持路桥结算：" prop="isInternalSettlement">-->
                        <!--                                    <el-radio v-model="openShopForm.isInternalSettlement" :label="0" >否</el-radio>-->
                        <!--                                    <el-radio v-model="openShopForm.isInternalSettlement" :label="1">是</el-radio>-->
                        <!--                                    <img class="pointer" src="@/assets/images/userCenter/question.png" alt="" @click="showTerm">-->
                        <!--                                </el-form-item>-->
                        <!--                            </el-col>-->
                        <!--                        </el-row>-->
<!--                        <el-checkbox-group v-model="checkList">-->
<!--                            <el-checkbox label="复选框 A"></el-checkbox>-->
<!--                            <el-checkbox label="复选框 B"></el-checkbox>-->
<!--                            <el-checkbox label="复选框 C"></el-checkbox>-->
<!--                        </el-checkbox-group>-->
                        <el-form-item prop="agreeTerm">
                            <el-checkbox v-model="openShopForm.agreeTerm" :indeterminate="false">
                                您确认阅读并接受<span style="color: seagreen"
                                @click="openAgreemen">《慧采商城开店协议》</span></el-checkbox>
                        </el-form-item>
                    </el-form>
                    <div class="btn center" @click="onSubmit">提交</div>
                </div>
                <div class="complete" v-if="currentStep == 2 || (shuDaoFlag == 1 && requestComplete)">
                    <div class="icon center"></div>
                    <div class="msg1">开店申请提交成功</div>
                    <div class="msg2">平台将在1个工作日后审核完成，请耐心等候</div>
                    <div class="btn center" @click="okFun">完成</div>
                </div>
                <div class="complete" v-if="currentStep == 3">
<!--                    <div class="icon center"></div>-->
<!--                    <div class="msg1">店铺待缴费</div>-->
<!--                    <div class="msg2">店铺待缴费</div>-->
<!--                    <div class="btn center" @click="outFee">前往缴费</div>-->
                    <el-form label-width="200px" v-loading="showAddFeeLoading" ref="showAddFeeRef"  :data="addFeeForm"  :rules="showAddFeeRoles">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="收款账户公司名称：">
                                    {{platformAccountObj.platformFreeyhOrgName}}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="收款账户开户行：">
                                    {{platformAccountObj.platformFreeyhAddress}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="收款账户：">
                                    {{platformAccountObj.platformFreeyhAccount}}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="缴费方式：" prop="payType">
                                    <el-radio v-model="addFeeForm.payType" :label="1" >线下</el-radio>
                                    <el-radio v-model="addFeeForm.payType" disabled :label="2" >线上</el-radio>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="收费标准" prop="tot">
                                    <span style="color: red">2000元/年</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="缴费时长单位：" prop="paymentDurationType">
                                    <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="1" >天</el-radio>-->
                                    <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="2" >周</el-radio>-->
                                    <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="3" >月</el-radio>-->
                                    <el-radio v-model="addFeeForm.paymentDurationType" :label="4" >年</el-radio>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="缴费时长：" prop="paymentDuration">
                                    <el-input placeholder="请输入缴费时长" clearable type="number"  @change="checkInputQty" v-model="addFeeForm.paymentDuration"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12" >
                                <el-form-item label="缴费金额（元）：" prop="payAmount">
                           <span style="color: red">
                               {{addFeeForm.payAmount}}
                           </span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="备注：" prop="remarks">
                                    <el-input
                                        type="textarea"
                                        :auto-resize="false"
                                        v-model="addFeeForm.remarks"
                                        placeholder="请输入备注，格式：单位名称+缴费金额" maxlength="1000"
                                        show-word-limit
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item  label="缴费证明：" prop="file">
                                    <el-upload
                                        :class="addFeeForm.files.length === 1 ? 'hide_box_min' : ''"
                                        v-loading="uploadLoading"
                                        class="upload-demo"
                                        action="fakeaction"
                                        :limit="1"
                                        :file-list="fileList2"
                                        :before-upload="handleBeforeUpload"
                                        :auto-upload="true"
                                        :http-request="uploadLicenseBusiness"
                                        list-type="picture-card">
                                        <div slot="tip" class="el-upload__tip">只能上传图片文件</div>
                                        <i slot="default" class="el-icon-plus"></i>
                                        <div slot="file" slot-scope="{file}">
                                            <img
                                                class="el-upload-list__item-thumbnail"
                                                :src="file.url" alt="">
                                            <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload2(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="formDtlFileRemove(file)">
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </span>
                                        </div>
                                    </el-upload>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <div id="auditRecords" class="con" v-if="addFeeForm.auditRecords != null">
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="addFeeForm.auditRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="auditType" label="审核类型" width="160">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.auditType == 1">提交审核</el-tag>
                                        <el-tag v-if="scope.row.auditType == 2">变更审核</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200">
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160">
                                </el-table-column>
                                <el-table-column prop="auditResult" label="审核意见" width="">
                                </el-table-column>
                            </el-table>
                            </div>
                            </div>
                    <div class="btn center" @click="save(1)" v-if="this.addFeeForm.paymentRecordId == null">提交</div>
                    <div class="btn center" @click="save(1)" v-else>重新提交</div>
                </div>
                <div class="complete" v-if="currentStep == 4">
                    <div class="icon center"></div>
                    <div class="msg1">缴费提交成功</div>
                    <div class="msg2">平台待审核，请耐心等候</div>
                </div>
                <div class="complete" v-if="currentStep == 5">
                    <div class="icon center"></div>
                    <div class="msg1">已完成</div>
                </div>
            </div>
        </main>
        <el-dialog title="慧采商城开店协议" :visible.sync="showTerm">
            <span v-html="content"></span>
            <span slot="footer">
                        <el-button @click="showTerm = false">取消</el-button>
                        <el-button type="primary" @click="showTerm = false">确定</el-button>
                    </span>
        </el-dialog>
        <publicity></publicity>
        <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</template>
<script>
import publicity from '@/pages/frontStage/components/publicity'
import { mapState } from 'vuex'
import { uploadFile, previewFile } from '@/api/platform/common/file'
import { getCascaderOptions } from '@/api/platform/common/components'
// eslint-disable-next-line
import { createShopEchoInfo, createShopExternal } from '@/api/frontStage/becomeSeller'
import { findByProgramaKey, getCurrentStep } from '@/api/w/richContent'
import {
    createFileRecordDelete,
    getPlatformFreeAccountAndAddress
} from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { createFeeAndDealFree, updateFee } from '@/api/fee/feeApi'
export default {
    components: { publicity },
    watch: {
    },
    data () {
        return {
            addFeeForm: {
                state: 0,
                payType: 1,
                recordType: 1,
                paymentDurationType: 4,
                paymentDuration: 0,
                files: [],
                payAmount: 0,
                remarks: null,
            },
            showLoading: false,
            showAddFeeLoading: false,
            content: '',
            showLoadin: false,
            currentStep: 1,
            platformAccountObj: {},
            fileList2: [],
            shuDaoFlag: null,
            dialogImageUrl: null,
            dialogVisible: false,
            showTerm: false,
            uploadLoading: false,
            showForm: '',
            requestComplete: false,
            freeAmount: 2000,
            openShopForm: {
                shopId: null, // 如果有值就是修改
                shopName: '',
                shopType: null,
                province: '',
                city: '',
                county: '',
                detailedAddress: '',
                mainBusiness: '',
                identityCardFace: '',
                identityCardFaceId: null,
                identityCardBadge: '',
                identityCardBadgeId: null,
                realName: '',
                identityCard: '',
                isInternalSettlement: 0,
                agreeTerm: false,
            },
            showAddFeeRoles: {
                paymentDuration: [
                    { required: true, validator: this.validateAddress2, trigger: 'blur' },
                ],
                payAmount: [
                    { required: true, validator: this.validateAddress2, trigger: 'blur' },
                ],
                file: [
                    { required: true, message: '请上传证明', trigger: 'blur' },
                ],
            },
            shopRules: {
                shopName: [
                    { required: true, message: '请填写店铺名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' }
                ],
                address: { required: true, validator: this.validateAddress, trigger: 'change' },
                detailedAddress: [
                    { required: true, message: '请填写详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
                mainBusiness: [
                    { required: true, message: '请填写主营业务', trigger: 'blur' },
                    { min: 1, max: 1000, message: '超过限制', trigger: 'blur' }
                ],
                identityCardFace: { required: true, message: '请上传身份证人像面', trigger: 'blur' },
                identityCardBadge: { required: true, message: '请上传身份证国徽面', trigger: 'blur' },
                identityCard: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                realName: [
                    { required: true, message: '请输入姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
            },
            checkList: [],
            addressOptions: {
                province: [],
                city: [],
                district: []
            },

        }
    },
    computed: {
        ...mapState(['userInfo']),
    },
    created () {
        this.getCurrentStep()
        this.getAddressPickerOptions()
        this.getCreateShopEchoInfoM()
        if(this.$route.params.row != null) {
            var shopInfo = this.$route.params.row.shopInfo
            this.openShopForm.shopId = shopInfo.shopId
            this.openShopForm.shopName = shopInfo.shopName
            this.openShopForm.province = shopInfo.province
            this.openShopForm.city = shopInfo.city
            this.openShopForm.county = shopInfo.county
            this.openShopForm.mainBusiness = shopInfo.mainBusiness
            this.openShopForm.detailedAddress = shopInfo.detailedAddress
        }
        if(this.userInfo.auditStatus == 2) {
            this.requestComplete = true
        }
        this.getRegisterAgreeUser('becomeSeller')
    },
    mounted () { },
    methods: {
        // 保存表单
        saveSheetM (num) {
            this.clientPop('info', '您确定要该操作吗？', async () => {
                this.addFeeForm.submitAud = num
                this.showAddFeeLoading = true
                updateFee(this.addFeeForm).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.currentStep = 4
                    }
                }).finally(() => {
                    this.showAddFeeLoading = false
                })
            })
        },
        // 预览图片
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        // 保存缴费
        save (num) {
            if(this.addFeeForm.paymentRecordId != null) {
                this.saveSheetM(1)
                return
            }
            if (this.addFeeForm.files.length == 0) {
                return this.$message.error('请上传缴费证明！')
            }
            if( this.addFeeForm.recordType == 1 || this.addFeeForm.recordType == 2) {
                if(this.addFeeForm.paymentDuration <= 0) {
                    return this.$message.error('缴费时长需大于0！')
                }
            }
            if(this.addFeeForm.payAmount <= 0) {
                return this.$message.error('缴费金额需大于0！')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.addFeeForm.submitAud = num
                this.showAddFeeLoading = true
                createFeeAndDealFree(this.addFeeForm).then(res => {
                    if (res.code == null) {
                        this.$message.success('操作成功')
                        this.addFeeForm = {
                            state: 0,
                            payType: 1,
                            recordType: 1,
                            paymentDurationType: 4,
                            paymentDuration: 0,
                            files: [],
                            payAmount: 0,
                            remarks: null,
                        }
                        this.fileList2 = []
                        this.currentStep = 4
                    }
                }).finally(() => {
                    this.showAddFeeLoading = false
                })
            })
        },
        // 移除表单文件
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.addFeeForm.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.addFeeForm.files = []
                this.fileList2 = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload2 (file) {
            this.uploadLoading = true
            let image = this.addFeeForm.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.addFeeForm.files = []
                    this.fileList2 = []
                }else {
                    let resO = res[0]
                    this.addFeeForm.files.push({
                        name: resO.objectName,
                        relevanceType: 1,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        checkInputQty () {
            const regex = /^-?\d+$/
            if (!regex.test(this.addFeeForm.paymentDuration)) {
                this.$message.error('请输入有效的数字')
                this.addFeeForm.payAmount = this.freeAmount
                return this.addFeeForm.paymentDuration = 1
            }
            if (this.addFeeForm.paymentDuration < 0 || this.addFeeForm.paymentDuration > 999) {
                this.$message.error('超过限制！')
                this.addFeeForm.payAmount = this.freeAmount
                return this.addFeeForm.paymentDuration = 1
            }
            this.addFeeForm.payAmount = this.addFeeForm.paymentDuration * this.freeAmount
        },
        getRegisterAgreeUser (programaKey) {
            findByProgramaKey({ programaKey: programaKey }).then(res => {
                this.fileList = res.files
                this.content = res.content
            })
        },
        // 获取当前步骤
        getCurrentStep () {
            this.showLoading = true
            getCurrentStep().then(res => {
                this.currentStep = res.currentStep
                this.shuDaoFlag = res.shuDaoFlag
                if(this.currentStep == 2) {
                    this.requestComplete = true
                }
                if(this.currentStep == 3) {
                    this.showAddFeeLoading = true
                    if(res.platformYearFeeRecord != null) {
                        this.addFeeForm = res.platformYearFeeRecord
                        let image = this.addFeeForm.files[0]
                        this.uploadLoading = true
                        previewFile({ recordId: image.fileFarId }).then(res => {
                            const blob = new Blob([res])
                            const url = window.URL.createObjectURL(blob)
                            this.fileList2 = []
                            this.fileList2.push({
                                name: image.name,
                                url: url
                            })
                        }).finally(() => this.uploadLoading = false)
                    }
                    getPlatformFreeAccountAndAddress().then(res => {
                        this.platformAccountObj = res
                        this.thisEnName = res.enterpriseName
                        this.addFeeForm.recordType = 1
                        this.addFeeForm.remarks = this.thisEnName + ' 店铺年度服务费'
                    }).finally(() => this.showAddFeeLoading = false)
                }
            }).finally(() => this.showLoading = false)
        },
        openAgreemen () {
            this.showTerm = true
        },
        validateAddress2 (rule, value, callback) {
            callback()
        },
        validateAddress (rule, value, callback) {
            if ( this.openShopForm.province == null || this.openShopForm.province == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.openShopForm.city == null || this.openShopForm.city == '' ) {
                return callback(new Error('请选择市级！'))
            }
            callback()
        },
        okFun () {
            location.href = '/'
        },
        outFee () {
            this.$router.push({ path: '/supplierSys/fee/payRecordManage', name: 'supplierSysFeePayRecordManage', query: { show: true } })
        },
        // 开店回显
        async getCreateShopEchoInfoM () {
            this.showLoading = true
            let res = await createShopEchoInfo({})
            let res1 =  await previewFile({ recordId: res.identityCardFaceId })
            let url1 = window.URL.createObjectURL(res1)
            let res2 =  await previewFile({ recordId: res.identityCardBadgeId })
            let url2 = window.URL.createObjectURL(res2)
            this.openShopForm.identityCardFace = url1
            this.openShopForm.identityCardBadge = url2
            this.openShopForm.identityCardFaceId = res.identityCardFaceId
            this.openShopForm.identityCardBadgeId = res.identityCardBadgeId
            this.openShopForm.realName = res.realName
            this.openShopForm.identityCard = res.identityCard
            this.openShopForm.shopType = res.shopType
            if(res.shopType == 2) {
                this.showForm = '个人开店'
                this.showLoading = false
                return
            }
            if(res.shopType == 0) {
                this.showForm = '个体户开店'
                this.showLoading = false
                return
            }
            if(res.shopType == 1) {
                this.showForm = '企业开店'
                this.showLoading = false
                return
            }
            this.showForm = '个人开店'
            this.showLoading = false
        },
        // 获取地址选择器数据
        async getAddressPickerOptions () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.addressOptions.province = res
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            this.openShopForm.county = ''
            if (layer === 1) {
                this.openShopForm.city = ''
            }
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1) {
                    return this.addressOptions.city = res
                }
                this.addressOptions.district = res
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            if(file.size / 1024 / 1024 > 4) {
                this.$message.error('上传的图片大小不能超过4MB!')
                return false
            }
            if(file.size / 1024 < 100) {
                this.$message.error('请上传100KB以上的文件')
                return false
            }
            return true
        },
        // 上传身份证
        async uploadIdentity (params, num, type) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            if (num === 1) {
                uploadFile(form).then(res => {
                    if (type == 1) {
                        this.openShopForm.identityCardFace = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.openShopForm.identityCardFaceId = res[0].recordId
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                    }
                    if (type == 2) {
                        this.openShopForm.identityCardFace = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.openShopForm.identityCardFaceId = res[0].recordId
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                    }
                })
            }
            if (num === 2) {
                uploadFile(form).then(res => {
                    if (type === 1) {
                        this.openShopForm.identityCardBadge = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.openShopForm.identityCardBadgeId = res[0].recordId
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                    }
                    if (type === 2) {
                        this.openShopForm.identityCardBadge = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.openShopForm.identityCardBadgeId = res[0].recordId
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                    }
                })
            }
        },
        // 开店
        async createShopExternalM () {
            let res = await createShopExternal(this.openShopForm)
            if(res.code == 200) {
                this.requestComplete = true
                this.currentStep = 2
            }
        },
        // 校验店铺类型
        validateShopType (rule, value, callback) {
            let bool = false
            for(let key in value) {
                if(value[key]) bool = true
            }
            if(bool) {
                return callback()
            }
            callback(new Error('请选择至少一项店铺类型！'))
        },
        addressChange (val) {
            if(!val) {
                this.openShopForm.province = this.$refs.selectLabel1.selectedLabel
                this.openShopForm.city = this.$refs.selectLabel2.selectedLabel
                this.openShopForm.county = this.$refs.selectLabel3.selectedLabel
                let newAddress = this.openShopForm.province + this.openShopForm.city + this.openShopForm.county
                this.openShopForm.detailedAddress = newAddress
            }
        },
        // 提交
        onSubmit () {
            this.$refs['enterpriseForm'].validate(valid => {
                if(valid) {
                    if (this.openShopForm.agreeTerm) {
                    // this.openShopForm.province = this.$refs.selectLabel1.selectedLabel
                    // this.openShopForm.city = this.$refs.selectLabel2.selectedLabel
                    // this.openShopForm.county = this.$refs.selectLabel3.selectedLabel
                        this.createShopExternalM()
                    }else {
                        this.$message({
                            message: '请查看协议后勾选',
                            type: 'error'
                        })
                    }
                }
            })
        },
        // showTerm () {},
    },
}
</script>
<style scoped lang="scss">
main {
    padding: 20px;
    background-color: #f5f5f5;
}

.content {
    width: 1326px;
    // height: 854px;
    background-color: #fff;
}

.head {
    height: 87px;
    border-bottom: 1px solid #D9D9D9;

    .title {
        width: 200px;
        height: 100%;
        font-size: 26px;
        line-height: 87px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        position: relative;

        &::after {
            content: '';
            display: block;
            width: 100%;
            height: 3px;
            background: #216EC6;
            position: absolute;
            bottom: 0;
        }
    }
}

.formContent {
    padding: 70px 83px 40px 59px;

    /deep/ .el-col {height: 75px;}
    /deep/ .idCol {height: 100px;}
    /deep/ .payCol {height: 24px;margin-bottom: 42px;}

    /deep/ .el-form-item__label {
        line-height: 50px;
        padding-right: 0;
        font-size: 16px;
        color: #333;
    }

    /deep/ .el-form-item__content {
        line-height: 50px;
    }

    /deep/ .el-form-item {
        height: 50px;
    }

    /deep/ .el-input__inner {
        height: 50px;
        font-size: 16px;
        border-radius: 0;
        border: 1px solid rgba(204, 204, 204, 1);
        background: #FFFFFF;
    }

    /deep/ .el-radio__inner {
        background-color: transparent;

        &::after {
            width: 9px;
            height: 9px;
            margin: 0 auto;
            background-color: #216EC6;
        }
    }
    /deep/ .el-radio__label {font-size: 16px;color: #333;}

    /deep/ .shortInput .el-input__inner {width: 400px;}

    /deep/ .registerAddress {
        .el-select {
            width: 120px;
            margin-right: 10px;

            &:last-child {
                margin-right: 0;
            }
        }

        .el-form-item__error {
            margin-left: 0px;
            //margin-top: -50px;
        }
    }

    .identityUpload {
        width: 160px;
        height: 100px;
    }

    /deep/ .el-form-item__error {
        width: 80%;
        margin-top: 0px;
    }

    .uploadTip {
        width: 600px !important;
        font-size: 16px;
        margin: 15px 0 25px 144px;
        width: 380px;
        color: #808080;
    }
    i{
        color: #999;
    }
}
.complete {
    padding: 120px 0 110px;
    .icon {
        width: 100px;
        height: 100px;
        margin-bottom: 30px;
        background: url(../../../../assets/images/userCenter/zc_chenggong.png);
    }
    .msg1, .msg2 {text-align: center;}
    .msg1 {
        margin-bottom: 15px;
        font-size: 22px;
        color: #333;
    }
    .msg2 {
        margin-bottom: 72px;
        font-size: 16px;
        color: #999;
    }
}
/deep/ .el-checkbox {
    .el-checkbox__inner {
        border-color: #333;
    }
    .el-checkbox__label {color: #333;}
    .el-checkbox__input.is-checked {
        .el-checkbox__inner {
            border-color: #216ec6;
            background-color: #216ec6;
        }
    }
}
.btn {
    width: 160px;
    height: 50px;
    font-size: 22px;
    line-height: 50px;
    text-align: center;
    color: #fff;
    background-color: #216EC6;
    cursor: pointer;
    user-select: none;
}
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>