<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="基本信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 订单 信息-->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">发票信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :inline="true" ref="addPlanFormRoteRef" :model="addPlanForm" :data="addPlanForm" :rules="addPlanFormRote">
                                 <el-row style="">
                                    <!--  发票类型  -->
                                    <el-col :span="12">
                                        <el-form-item label="发票类型：" prop="invoiceType">
                                            <el-select v-model="form.invoiceType" placeholder="请选择发票类型">
                                                <el-option v-for="item in typeOptions" :value="item.value" :label="item.label"
                                                           :key="item.value"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <!--  发票内容  -->
                                    <el-col :span="12">
                                        <el-form-item label="发票编号：" prop="invoiceNo">
                                            <el-input v-model="form.invoiceNo" placeholder="请输入发票内容"></el-input>
                                        </el-form-item>
                                    </el-col>
                                 </el-row>
                                 <el-row style="">
                                    <!--  发票抬头类型  -->
                                    <el-col :span="12">
                                        <el-form-item label="抬头类型：" prop="riseType">
                                            <el-select v-model="form.riseType" placeholder="请选择发票抬头类型">
                                                <el-option v-for="item in headerOptions" :value="item.value"
                                                           :label="item.label" :key="item.value"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <!--  发票抬头名称  -->
                                    <el-col :span="12">
                                        <el-form-item label="单位名称：" prop="company">
                                            <el-input v-model="form.company" placeholder="请输入发票抬头名称"></el-input>
                                        </el-form-item>
                                    </el-col>
                                 </el-row>
                                 <el-row style="">
                                    <!--  收票人姓名  -->
                                    <el-col :span="12">
                                        <el-form-item label="收票人姓名：" prop="userName">
                                            <el-input v-model="form.userName" placeholder="请输入收票人姓名"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <!--  收票人手机号  -->
                                    <el-col :span="12">
                                        <el-form-item label="收票人手机号：" prop="userPhone">
                                            <el-input v-model="form.userPhone" placeholder="请输入收票人手机号"></el-input>
                                        </el-form-item>
                                    </el-col>
                                 </el-row>
                                 <el-row style="">
                                    <!--  收票人地区  -->
                                    <el-col :span="12">
                                        <el-form-item class="address" label="收票人地区：" prop="userAddress">
                                            <el-input v-model="form.userAddress" placeholder="请输入收票人地区"></el-input>
                                            <!--                                <div class="df">-->
                                            <!--                                    <el-select ref="selectLabel1" class="province" v-model="form.province"-->
                                            <!--                                               placeholder="省份" @change="(code) => getSubDistrict(code, 1)">-->
                                            <!--                                        <el-option v-for="item in addressOptions.province" :key="item.value"-->
                                            <!--                                                   :label="item.districtName" :value="item.districtCode">-->
                                            <!--                                        </el-option>-->
                                            <!--                                    </el-select>-->
                                            <!--                                    <el-select ref="selectLabel2" class="city" v-model="form.city" placeholder="市级"-->
                                            <!--                                               @change="(code) => getSubDistrict(code, 2)">-->
                                            <!--                                        <el-option v-for="item in addressOptions.city" :key="item.value"-->
                                            <!--                                                   :label="item.districtName" :value="item.districtCode">-->
                                            <!--                                        </el-option>-->
                                            <!--                                    </el-select>-->
                                            <!--                                    <el-select ref="selectLabel3" class="district" v-model="form.district"-->
                                            <!--                                               placeholder="区、县">-->
                                            <!--                                        <el-option v-for="item in addressOptions.district" :key="item.value"-->
                                            <!--                                                   :label="item.districtName" :value="item.districtCode">-->
                                            <!--                                        </el-option>-->
                                            <!--                                    </el-select>-->
                                            <!--                                </div>-->
                                        </el-form-item>
                                    </el-col>
                                 </el-row>
                                 <el-row style="">
                                    <!--  详细地址  -->
                                    <el-col :span="12">
                                        <el-form-item label="详细地址：" prop="detailAddr">
                                            <el-input type="textarea" :auto-resize="false" v-model="form.detailAddr"
                                                      placeholder="请输入详细地址"></el-input>
                                        </el-form-item>
                                    </el-col>
                                 </el-row>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button @click="$router.go(-1)">返回</el-button>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import { getDate } from '@/api/frontStage/invoice'
import { getCascaderOptions } from '@/api/platform/common/components'
export default {
    data () {
        return {

            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            tabsName: 'baseInfo',
            // 校验规则
            rules: {
                type: { required: true, message: '请选择发票类型', trigger: 'blur' },
                invoiceNo: { required: true, message: '请输入发票内容', trigger: 'blur' },
                header: { required: true, message: '请选择发票抬头类型', trigger: 'blur' },
                headerName: { required: true, message: '请输入发票抬头名称', trigger: 'blur' },
                receiver: { required: true, message: '请输入收票人姓名', trigger: 'blur' },
                receiverTel: { required: true, min: 11, max: 11, message: '请输入正确的收票人手机号', trigger: 'blur' },
                receiverAddr: { required: true, message: '请选择收票人地区', trigger: 'blur' },
                detailAddr: { required: true, message: '请输入详细地址', trigger: 'blur' },
            },
            // 表单数据
            form: {
                invoiceType: 0,
                invoiceNo: '',
                riseType: '',
                company: '',
                dutyParagraph: '',
                registerAddress: '',
                registerPhone: '',
                bank: '',
                bankAccount: '',
                userName: '',
                userPhone: '',
                userAddress: '',
                detailAddr: '',
                province: '',
                city: '',
                district: '',
                orderSn: '',
                invoiceState: 0
            },
            typeOptions: [
                { label: '普通发票', value: 0 },
            ],
            headerOptions: [
                { label: '单位', value: 1 },
                { label: '个人', value: 2 },
            ],
            addressOptions: {
                province: [],
                city: [],
                district: []
            },

        }
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder')
        },
        getInvoice (orderItemId) {
            getDate({ orderItemId: orderItemId }).then(res=>{
                this.form = res
            })
        },
        // 提交申请
        handleSubmit () {
            this.form.province = this.$refs.selectLabel1.selectedLabel
            this.form.city = this.$refs.selectLabel2.selectedLabel
            this.form.district = this.$refs.selectLabel3.selectedLabel
            this.$refs['form'].validate(valid => {
                if (!valid) return
            })
            let { province, city, district } = this.form
            console.log(province, city, district)
        },
        // 获取地址选择器数据
        async getAddressPickerOptions () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.addressOptions.province = res
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            this.form.district = ''
            if (layer === 1) {
                this.form.city = ''
            }
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1) {
                    return this.addressOptions.city = res
                }
                this.addressOptions.district = res
            })
        },
    },
    created () {
        this.form.orderSn = this.$route.params.row
        this.getInvoice(this.form.orderItemId)
        this.getAddressPickerOptions()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}

/deep/ .qrcodeDialog .el-dialog__body {
    height: 240px;
}
</style>
