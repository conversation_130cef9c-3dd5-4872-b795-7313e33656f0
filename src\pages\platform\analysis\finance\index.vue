<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">发布</el-button>
                            <el-button type="primary" @click="changePublishState(2)" class="btn-greenYellow">取消发布</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                        <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png"
                                slot="suffix" @click="onSearch" /></el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="80">
                        <template slot-scope="scope">
                            <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="发布状态" >
                        <template slot-scope="scope">
                            {{ scope.row.state == 1 ? '已发布' : '未发布' }}
                        </template>
                    </el-table-column>
                    <!-- 图片排序值 -->
                    <el-table-column label="排序值" width="120" prop="sort" />

                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange" />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新增编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="金融产品名称：" prop="name">
                                <el-input v-model="formData.name" placeholder="请输入金融产品名称" clearable></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                       <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input v-model="formData.sort" :min="0"  type="number" placeholder="填写排序值">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input type="textarea" v-model="formData.remarks" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                 </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="30%" :close-on-click-modal="true" :before-close="closeDialog">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="金融产品名称：">
                            <el-input v-model="filterData.name" placeholder="请输入金融产品名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="金融产品地址：">
                            <el-input v-model="filterData.url" placeholder="请输入金融产品地址" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getList, edit, batchPublish, batchNotPublish } from '@/api/platform/analysis/finance'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapActions } from 'vuex'
export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler (val) {
                console.log(val)
                this.getParams()
                getList(this.requestParams).then(res => {
                    console.log(res)
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertName: '金融产品',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                name: '',
                url: '',
                orderBy: 2
            },
            tableData: [],
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入金融产品', trigger: 'blur' }],
                // url: [{ required: true, message: '请输入正确的金融产品地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
                name: '',
                // url: '',
                remarks: '',
            },
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {}
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy
        }
        getList(params).then(res => {
            console.log(res)
            this.pages = res
            this.tableData = res.list
        })
        this.getParams()
    },
    methods: {
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                name: '',
                url: '',
                orderBy: 2
            },
            done()
        },
        hideDialog () {
            this.filterData = {
                name: '',
                url: '',
                orderBy: 2
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            console.log(this.filterData)
            for (let key in this.filterData) {
                if (this.filterData[key] === '') {
                    // return this.clientPop('warn', )
                    this.filterData[key] = null
                }
            }
            this.getParams()
            // 查询请求传参
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.queryVisible = false
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                console.log(res)
                this.pages = res
                // this.viewList = true
            })
        },
        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.financialId
            })
            if (!this.selectedRows[0]) {
                let msg = num == 1 ? `请选择要发布的${this.alertName}` : `请选择要取消发布的${this.alertName}`
                return this.clientPop('warn', msg, () => { })
            }
            let warnMsg = num === 1 ? `您确定要发布选中的${this.alertName}吗？` : `您确定要取消发布选中的${this.alertName}吗？`
            this.clientPop('info', warnMsg, async () => {
                switch (num)
                {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '发布成功', () => {
                                getList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => { })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '取消发布成功', () => {
                                getList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {})
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },

        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
            console.log(selection)
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () { },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = JSON.parse(JSON.stringify(scope.row))
            this.action = '编辑'
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            getList(this.requestParams).then(res => {
                console.log(res.list)
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                console.log(res)
                this.pages = res
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {})
                }
                console.log(res)
            })
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.action === '编辑') {
                        return this.handleEditData()
                    }
                    console.log(this.formData)
                    this.handleCreateData()
                }
            })
        },
        // 修改数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },

        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}
.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}
</style>
