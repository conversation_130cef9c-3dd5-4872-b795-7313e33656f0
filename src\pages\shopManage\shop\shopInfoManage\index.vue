<template>
    <div class="base-page">
        <div class="e-form" style="padding: 0 10px 10px;" v-loading="formLoading">
            <div class="tabs-title">店铺信息</div>
            <el-form :rules="formRules" ref="formEdit" :model="shopInfo" label-width="150px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺名称：" prop="shopName">
                            <el-input placeholder="请输入店铺名称" clearable  v-model="shopInfo.shopName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="主营业务：" prop="mainBusiness">
                            <el-input placeholder="请输入主营业务" clearable  v-model="shopInfo.mainBusiness"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="联系人：" prop="linkMan">
                            <el-input placeholder="请输入联系人" clearable  v-model="shopInfo.linkMan"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话：" prop="contactNumber">
                            <el-input placeholder="请输入联系电话" clearable  v-model="shopInfo.contactNumber"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="地址：" prop="province">
                            <el-cascader style="width: 300px"
                                size="large"
                                :options="addressData"
                                v-model="selectAddressOptions"
                                @change="handleAddressChange">
                            </el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="详细地址" prop="detailedAddress">
                            <el-input placeholder="请输入详细地址" clearable v-model="shopInfo.detailedAddress"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="退货联系人：" prop="returnRelationName">
                    <el-input placeholder="请输入退货联系人" clearable  v-model="shopInfo.returnRelationName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="退货联系电话：" prop="returnRelationNumber">
                    <el-input placeholder="请输入退货联系电话" clearable  v-model="shopInfo.returnRelationNumber"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="退货详细地址" prop="returnAddress">
                    <el-input placeholder="退货详细地址" clearable v-model="shopInfo.returnAddress"></el-input>
                  </el-form-item>
                </el-col>
                  <el-col :span="12">
                      <el-form-item label="税率" prop="taxRate">
                          {{shopInfo.taxRate}}%
                      </el-form-item>
                  </el-col>
              </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item class="licenseUploader" label="营业执照：" prop="businessLicense">
                          <el-upload class="avatar-uploader"   action="fakeaction" ref="licenseFileRef"
                                     :before-upload="handleBeforeUpload"
                                     :auto-upload="true"
                                     :http-request="uploadLicenseBusiness"
                                     :on-remove="licenseFileRemove"
                                     :on-change="licenseFileChange"
                          >
                            <img v-if="shopInfo.businessLicense" :src="shopInfo.businessLicense" class="avatar">
                            <div v-else class="licenseUploader">
                              <img src="@/assets/images/userCenter/upload_yyzz.png" />
                            </div>
                          </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item class="uploader" label="店铺logo （推荐：100x100）：" prop="minFile">
                            <el-upload :class="minFileLength===1?'hide_box_min':''"  action="fakeaction" ref="minFileRef"
                                       :file-list="shopInfo.minFile"
                                       list-type="picture-card"
                                       :before-upload="handleBeforeUpload"
                                       :auto-upload="false"
                                       :on-remove="minFileRemove"
                                       :on-change="minFileChange"
                            >
                                <i class="el-icon-plus"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item class="uploader" label="店铺广告图（推荐650x125）：" prop="adminFile">
                            <el-upload :class="adminFileLength===1?'hide_box_admin':''"  action="fakeaction" ref="adminFileRef"
                                       :file-list="shopInfo.adminFile"
                                       list-type="picture-card"
                                       :before-upload="handleBeforeUpload"
                                       :auto-upload="false"
                                       :limit="1"
                                       :on-remove="adminFileRemove"
                                       :on-change="adminFileChange"
                            >
                                <i class="el-icon-plus"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="店铺简介：">
                            <editor v-model="shopInfo.shopDescrible"></editor>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="buttons" style="margin-right: 30px">
            <el-button type="success" @click="submit">保存</el-button>
        </div>
    </div>
</template>
<script>
// eslint-disable-next-line
import { regionData, CodeToText, TextToCode } from 'element-china-area-data'
// eslint-disable-next-line
import editor from '../../../../components/quillEditor'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { getShopById, updateShopInfo } from '@/api/shopManage/shop/shop'
import { addImgUrl, spliceImgUrl } from '@/utils/common'

export default {
    components: {
        editor
    },
    data () {
        return {
            formLoading: false,
            adminFileLength: 0,
            minFileLength: 0,
            licenseFileLength: 0,
            addressData: regionData, // 地址数据
            selectAddressOptions: [], // 地址选择
            shopInfo: {
                shopName: null,
                returnAddress: null,
                mainBusiness: null,
                linkMan: null,
                contactNumber: null,
                province: null,
                minFile: [],
                returnRelationName: '',
                returnRelationNumber: '',
                adminFile: [],
                detailedAddress: null,
                businessLicense: null,
                businessLicenseId: null,
            },
            uploadImgSize: 10, // 上传文件大小
            // 表单校验规则
            formRules: {
                shopName: [
                    { required: true, message: '请输入店铺名称', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                // province: [
                //     { required: true, message: '请选择地址', trigger: 'blur' },
                // ],
                // returnAddress: [
                //     { required: true, message: '请输入退货联系地址', trigger: 'blur' },
                // ],
                // returnRelationName: [
                //     { required: true, message: '请输入退货联系姓名', trigger: 'blur' },
                // ],
                // returnRelationNumber: [
                //     { required: true, message: '请输入退货联系电话', trigger: 'blur' },
                // ],

            },
        }
    },
    created () {
        this.getShopByIdM()
    },
    methods: {
        // 上传营业执照
        async uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            uploadFile(form).then(res => {
                previewFile({ recordId: res[0].recordId }).then(res => {
                    this.shopInfo.businessLicense  = window.URL.createObjectURL(res)
                })
                this.shopInfo.businessLicenseId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })

            })
        },
        submit () {
            this.$refs.formEdit.validate(valid => {
                if(valid) {
                    spliceImgUrl(this.shopInfo, this.imgUrlPrefixDelete)
                    this.formLoading = true
                    updateShopInfo(this.shopInfo).then(res =>{
                        this.getShopByIdM()
                        this.formLoading = false
                        this.message(res)
                    }).catch(() => {
                        this.formLoading = false
                    })
                }else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
        getShopByIdM () {
            this.formLoading = true
            getShopById().then(res => {
                this.shopInfo = res
                this.addressFormatShow(res)
                this.minFileLength = res.minFile.length
                this.adminFileLength =  res.adminFile.length
                // this.licenseFileLength = res.businessLicense.length
                addImgUrl(res, this.imgUrlPrefixAdd)
                // 营业执照
                previewFile({ recordId: res.businessLicenseId }).then(res => {
                    this.shopInfo.businessLicense  = window.URL.createObjectURL(res)
                })
                this.shopInfo = res
            }).finally(() => {
                this.formLoading = false
            })
        },
        // 地址回显
        addressFormatShow (row) {
            if(row.province == null || row.province == '') {
                return
            }
            if(row.city == null || row.city == '') {
                return
            }
            if(row.county == null || row.county == '') {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        uploadFileInfo (params) {
            const form = new FormData()
            form.append('files', params.raw)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        // 小图上传
        minFileChange (file, fileList) {
            fileList.pop()
            this.uploadMinFile(file, fileList)
        },
        // 上传主图
        uploadAdminFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.adminFileLength = 1
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 1
                file.relevanceType = 4
                file.fileType = 1
                file.fileFarId =  res[0].recordId
                file.imgType =  0
                this.shopInfo.adminFile.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 上传小图
        uploadMinFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.minFileLength = 1
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 0
                file.relevanceType = 4
                file.fileType = 1
                file.fileFarId =  res[0].recordId
                file.imgType = 1
                this.shopInfo.minFile.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 装备主图上传
        adminFileChange (file, fileList) {
            fileList.pop()
            this.uploadAdminFile(file, fileList)
        },
        // 小图删除
        minFileRemove (file, fileList) {
            this.minFileLength = fileList.length
            let recordId = this.shopInfo.minFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.shopInfo.minFile = []
            })
        },
        licenseFileChange (file) {
            if (file.status == 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status == 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        // 上传营业执照
        uploadLicenseFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.licenseFileLength = 1
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 0
                file.relevanceType = 4
                file.fileType = 1
                file.fileFarId =  res[0].recordId
                file.imgType =  0
                this.shopInfo.businessLicense.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 营业执照删除
        licenseFileRemove (file, fileList) {
            this.licenseFileLength = fileList.length
            let recordId = this.shopInfo.businessLicense[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.shopInfo.businessLicense = null
            })
        },
        // 主图删除
        adminFileRemove (file, fileList) {
            this.adminFileLength = fileList.length
            let recordId = this.shopInfo.adminFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.shopInfo.adminFile = []
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        // 地区
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.shopInfo.province = province
            this.shopInfo.city = city
            this.shopInfo.county = county
            this.shopInfo.detailedAddress = province + city + county
        },
    },
    mounted () { },
}
</script>
<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
    -moz-appearance: textfield !important;
}
/deep/ input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
    flex-direction: column;
    .buttons {
        height: 78px;
        line-height: 78px;
        text-align: right;
    }
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-form {
    background-color: #fff;
    overflow-y: auto;
}

.right-btn {
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.avatar-uploader {
    /deep/.el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card{
    display: none;
}
/deep/ .hide_box_min .el-upload--picture-card{
    display: none;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
.licenseUploader {
    font-size: 40px;
    color: #8c939d;
    width: 138px;
    height: 138px;
    //margin-right: 20px;
    line-height: 140px;
    display: inline;
    .el-form-item__error {
        width: 500px;
    }
}
</style>