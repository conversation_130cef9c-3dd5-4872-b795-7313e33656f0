<template>
    <main>
        <div class="msgBox">
            <div class="list-title dfa">我的采购</div>
            <div v-show="viewShow!==true" class="tableBox">
                <el-table
                    ref="msgTable"
                    :data="list"
                    :header-row-style="{ fontSize: '16px', color: '#216EC6', fontWeight: '500' }"
                    :row-style="{ fontSize: '14px', color: '#666666', height: '48px' }"
                >
                    <el-table-column type="index" label="序号" width="54px">
                    </el-table-column>
                    <el-table-column prop="title" label="名称" width="">
                    </el-table-column>
                    <el-table-column prop="bidNum" label="编号" width="120px">
                    </el-table-column>
                    <el-table-column prop="company" label="单位" width="">
                        <template v-slot="scope">
                            <div class="textOverflow1">{{ scope.row.company }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100px">
                        <template v-slot="scope">
                            <div class="action">
                                <span @click="handleDeleteMsg(scope.row.stationMessageReceiveId)">删除</span>
                                <span @click="handleViewMsg(scope.row)">查看</span>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pager dfb">
                    <div class="batchAction dfa">
                        <el-checkbox v-model="checkAll" label="全选" :indeterminate="false"
                                     @change="toggleSelectAll"></el-checkbox>
                        <div class="btn del" @click="deleteSelected">删除选中</div>
                        <div class="btn mark" @click="markSelected">标记已读</div>
                    </div>
                    <pagination
                        :total="page.totalCount"
                        :currentPage="page.currPage"
                        :pageSize="page.pageSize"
                        :totalPage="page.totalPage"
                        :destination="page.destination"
                        :pagerSize="5"
                        @currentChange="currentChange"
                        @sizeChange="sizeChange"
                    ></pagination>
                </div>
            </div>

            <div class="right" v-show="viewShow===true">
                <h2>{{ formData.title }}</h2>
                <div class="publish-time mb20">发布时间：{{formData.publishTime}}</div>
                <div class="sim-table">
                    <div class="row">
                        <div class="label">招标名称</div>
                        <div class="value textOverflow1">{{ formData.title }}</div>
                    </div>
                    <div class="row">
                        <div class="label">招标编号</div>
                        <div class="value textOverflow1">{{ formData.bidNum }}</div>
                    </div>
                    <div class="row">
                        <div class="label">发布时间</div>
                        <div class="value textOverflow1">{{ formData.publishTime }}</div>
                    </div>
                    <div class="row">
                        <div class="label">招标单位</div>
                        <div class="value textOverflow1">{{ formData.company }}</div>
                    </div>
                </div>
                <div class="tab-box">
                    <div class="bid-tabs dfa">
                        <div class="tab pointer" :style="currentTab === 0 ? { background: 'white', borderColor: '#216EC6' } : {}" @click="currentTab = 0">招标公告</div>
                        <div class="tab pointer" :style="currentTab === 1 ? { background: 'white', borderColor: '#216EC6' } : {}" @click="currentTab = 1">包件信息</div>
                    </div>
                    <!--招标公告-->
                    <div class="announcement p20" v-show="currentTab === 0">
                        <div v-html="formData.announcement"></div>
                    </div>
                    <!--包件信息-->
                    <div class="package-info p20" v-show="currentTab === 1">
                        <el-table
                            border
                            :data="packages"
                            :style="{ minHeight: '300px' }"
                            :header-cell-style="{ background: '#f7f7f7' }"
                        >
                            <el-table-column type="index" label="序号" width="54px" align="center">
                            </el-table-column>
                            <el-table-column prop="packageNum" label="包件编号" width="100px" align="center">
                            </el-table-column>
                            <el-table-column prop="packageName" label="包件名称" align="center">
                            </el-table-column>
                            <el-table-column prop="price" label="价格" width="140px" align="center">
                            </el-table-column>
                            <el-table-column label="操作" width="190px" align="center">
                                <template v-slot="scope">
                                    <span style="color: #216EC6; margin-right: 8px;" class="pointer" @click="showPackage(scope, 'list')">查看清单</span>
                                    <span style="color: #216EC6;" class="pointer" @click="showPackage(scope, 'supplier')">查看供应商</span>

                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="return pointer" @click="viewShow = false">返回</div>
            </div>
        </div>
        <el-dialog  class="front" width="70%" :visible.sync="dialogVisible" close-on-click-modal>
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>{{ viewSupplier ? '供应商' : '包件清单' }}</div>
                    </div>
                    <div class="dialog-close" @click="dialogVisible = false">
                        <img src="@/assets/images/close.png" alt=""/></div>
                </div>
                <div></div>
            </div>
            <div class="dialog-body center p20">
                <!--供应商-->
                <el-table
                    border
                    :data="supplierList"
                    :header-cell-style="{ background: '#f7f7f7' }"
                    v-show="viewSupplier"
                >
                    <el-table-column prop="company" label="公司名称" align="center">
                    </el-table-column>
                    <el-table-column prop="contact" label="联系人" width="140px" align="center">
                    </el-table-column>
                    <el-table-column prop="tel" label="联系电话" width="170px" align="center">
                    </el-table-column>
                    <el-table-column prop="response" label="响应" width="70px" align="center">
                        <template v-slot="scope">
                            <i class="el-icon el-icon-check" v-if="scope.row.response"></i>
                            <i class="el-icon el-icon-close" v-else></i>
                        </template>
                    </el-table-column>
                    <el-table-column prop="bookPurchase" label="标书购买" width="70px" align="center">
                        <template v-slot="scope">
                            <i class="el-icon el-icon-check" v-if="scope.row.bookPurchase"></i>
                            <i class="el-icon el-icon-close" v-else></i>
                        </template>
                    </el-table-column>
                    <el-table-column prop="bookDownload" label="标书下载" width="70px" align="center">
                        <template v-slot="scope">
                            <i class="el-icon el-icon-check" v-if="scope.row.bookDownload"></i>
                            <i class="el-icon el-icon-close" v-else></i>
                        </template>
                    </el-table-column>
                    <el-table-column prop="margin" label="保证金" width="70px" align="center">
                        <template v-slot="scope">
                            <i class="el-icon el-icon-check" v-if="scope.row.margin"></i>
                            <i class="el-icon el-icon-close" v-else></i>
                        </template>
                    </el-table-column>
                    <el-table-column prop="query" label="澄清提问" width="70px" align="center"></el-table-column>
                    <el-table-column prop="compensate" label="补遗确认" width="70px" align="center"></el-table-column>
                    <el-table-column prop="quotation" label="报价" width="70px" align="center"></el-table-column>
                    <el-table-column label="操作" width="120px" align="center">
                        <template v-slot="scope">
                            <span style="color: #216EC6; margin-right: 8px;" class="pointer" @click="handleConfirm(scope)">确认</span>
                            <span style="color: #216EC6;" class="pointer" @click="rejectActionHandler(scope)">审核不通过</span>
                        </template>
                    </el-table-column>
                </el-table>
                <!--招标清单-->
                <el-table
                    border
                    :data="packageBidList"
                    :header-cell-style="{ background: '#f7f7f7' }"
                    v-show="viewList"
                >
                    <el-table-column prop="materialType" label="物资类别" width="100px" align="center">
                    </el-table-column>
                    <el-table-column prop="materialName" label="物资名称" align="center">
                    </el-table-column>
                    <el-table-column prop="spec" label="规格型号" width="200px" align="center">
                    </el-table-column>
                    <el-table-column prop="unit" label="计量单位" width="140px" align="center">
                    </el-table-column>
                    <el-table-column prop="material" label="材质" align="center">
                    </el-table-column>
                    <el-table-column prop="amount" label="数量" width="200px" align="center">
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </main>
</template>
<script>
import pagination from '../../components/pagination.vue'
import { del, delMessageBath, receiceList, updateReadStateBath } from '@/api/frontStage/messages'

export default {
    components: { pagination },
    data () {
        return {
            dialogVisible: false,
            viewSupplier: false,
            viewList: false,
            arr: [],
            selectList: [],
            checkAll: false,
            page: {
                totalPage: 0,
                totalCount: 0,
                currPage: 1,
                pageSize: 10,
                destination: 2
            },
            formData: {
                title: '四川铁建成都东部新区智能制造基地级配虽碎石采购',
                bidNum: 'TJVSRSSDDBSJVKSJ983',
                publishTime: '2023-4-20',
                company: '四川省铁路建设有限公司成都东部新区轨道交通工程智能装备及高性能轨道建设材料总部研发制造项目部',
                announcement: '招标公告内容'
            },
            list: [],
            viewShow: false,
            currentTab: 0,
            packages: [
                {
                    packageNum: '03',
                    packageName: '粉煤灰',
                    price: '18472.00'
                },
            ],
            supplierList: [
                {
                    company: '四川铁建成都东部新区智能制造基地级配虽碎石采购',
                    status: 1,
                    contact: '洋洋',
                    tel: '13829873600',
                    response: true,
                    bookPurchase: true,
                    bookDownload: false,
                    margin: true,
                    query: '',
                    compensate: '',
                    quotation: ''
                }
            ],
            packageBidList: [
                {
                    materialType: 1,
                    materialName: '粉没灰',
                    spec: '一级',
                    unit: 't',
                    material: '',
                    amount: ''
                }
            ]
        }
    },
    watch: {
        dialogVisible (isShow) {
            if(!isShow) {
                this.viewList = false
                this.viewSupplier = false
            }
        }
    },
    created () {
        this.list.push(this.formData)
    },
    mounted () {
    },
    methods: {
        // eslint-disable-next-line no-unused-vars
        showPackage ({ row }, tableType) {
            tableType === 'list' ? this.viewList = true : this.viewSupplier = true
            this.dialogVisible = true
        },
        // eslint-disable-next-line no-unused-vars
        handleConfirm ({ row }) {},
        // eslint-disable-next-line no-unused-vars
        rejectActionHandler ({ row }) {},
        message (res) {
            let msgType = res.code === 200 ? 'success' : 'error'
            this.$message({
                message: res.message,
                type: msgType
            })
        },
        getList () {
            let params = {
                limit: this.page.pageSize,
                page: this.page.currPage,
                receiveType: 1
            }
            receiceList(params).then(res => {
                this.list = res.list
                this.page = res
                this.page.totalPage = Math.ceil(this.page.totalCount / this.page.pageSize)
            })
        },
        handleDeleteMsg (id) {
            del({ id: id }).then(res => {
                this.message(res)
                this.getList()
            })
        },
        handleViewMsg (row) {
            this.viewShow = true
            this.formData = row
        },
        // 全选事件
        toggleSelectAll () {
            if (!this.checkAll) {
                return this.$refs['msgTable'].clearSelection()
            }
            this.$refs['msgTable'].toggleAllSelection()
        },
        // 选中项更改事件
        selectionChange (selection) {
            this.arr = selection
            if (selection.length == this.page.pageSize) {
                return this.checkAll = true
            }
            if (this.checkAll) this.checkAll = false
        },
        // 删除选中
        deleteSelected () {
            if (this.arr == null || this.arr == 0) {
                this.$message({
                    message: '未选中数据',
                    type: 'info'
                })
                return
            }
            this.selectList = this.arr.map(item => {
                return item.stationMessageReceiveId
            })
            delMessageBath(this.selectList).then(res => {
                if (res.code === 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.getList()
            })
        },

        // 标记已读
        markSelected () {
            if (this.arr == null || this.arr == 0) {
                this.$message({
                    message: '未选中数据',
                    type: 'info'
                })
                return
            }
            this.selectList = this.arr.map(item => item.stationMessageReceiveId)
            updateReadStateBath(this.selectList).then(res => {
                if (res.code === 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.getList()
            })
        },
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.msgTable.toggleRowSelection(row, row.flag)
        },
        // 页码变更
        currentChange (currPage) {
            this.page.currentPage = currPage
            this.getList()
        },
        sizeChange (pageSize) {
            this.page.pageSize = pageSize
            this.getList()
        },

    },
}
</script>
<style scoped lang="scss">
.msgBox {
  border: 1px solid #e6e6e6;
}

.theBtn2 {
  width: 120px;
  height: 40px;
  margin: 20px auto 20px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  background-color: #93b7e0;
}

.theBtn {
  width: 120px;
  height: 40px;
  margin: 20px auto 20px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  background-color: #216EC6;
}

.list-title {
  height: 50px;
  padding: 15px 0 15px 20px;
  font-size: 18px;
  line-height: 20px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);

  &::before {
    width: 3px;
    height: 20px;
    margin-right: 10px;
    content: '';
    display: block;
    background-color: rgba(33, 110, 198, 1);
  }
}

.tableBox {
  padding: 22px 30px 0;

  .taberHeader {
    font-size: 16px;
    color: #216EC6;
  }

  .action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:first-of-type {
      margin-right: 15px;
    }
  }

  /deep/ .el-table {
    min-height: 600px;

    .el-table__body .cell {
      display: flex;
      align-items: center;

      img {
        width: 13px;
        height: 12px;
        margin-right: 5px;
      }
    }

    thead {
      .el-checkbox {
        display: none;
      }

      tr th:nth-child(7) .cell {
        text-align: center;
      }
    }

    .el-table__row {
      td:first-child .cell {
        display: block;
        text-align: center;
      }
    }
  }

  .batchAction {
    padding-left: 20px;

    /deep/ .el-checkbox {
      margin-right: 20px;
    }

    .btn {
      width: 80px;
      height: 30px;
      text-align: center;
      font-size: 14px;
      line-height: 30px;
      cursor: pointer;
      user-select: none;
    }

    .del {
      margin-right: 15px;
      color: #fff;
      background-color: #216EC6;
    }

    .mark {
      color: #216EC6;
      border: 1px solid #216EC6;
    }
  }
}
.right {
  padding-bottom: 20px;
}
h2 {
  margin: 15px 0;
  text-align: center;
}
.publish-time {
  text-align: center;
}
.sim-table, .tab-box {
  width: 96%;
  margin: 0 auto 20px;
  border: 1px solid lightgray;
}
.sim-table {
  .row {
    height: 50px;
    display: flex;
    &:not(:last-of-type) { border-bottom: 1px solid lightgray; }
    .label, .value {
      line-height: 50px;
    }
    .label {
      width: 86px;
      text-align: center;
      border-right: 1px solid lightgray;
      background-color: #f7f7f7;
    }
    .value {
      max-width: 970px;
      padding-left: 10px;
    }
  }
}
.bid-tabs {
  height: 50px;
  background-color: #f7f7f7;
  .tab {
    width: 90px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    border-top: 2px solid transparent;
    user-select: none;
  }
}
.announcement, .package-info, .bid-list {
  min-height: 400px;
}
.return {
  width: 110px;
  height: 50px;
  margin: 0 auto;
  font-size: 18px;
  line-height: 50px;
  text-align: center;
  color: #616161;
  border: 1px solid lightgray;
  user-select: none;
}
</style>