<template>
    <main>
        <div class="box center">
            <div class="list">
                <div class="list-item df" v-for="item in newsList" :key="item.id" v-loading="loading">
<!--                    <el-image :src="imgUrlPrefixAdd + item.bannerImg" fit="cover" style="width: 100px; height: 100px; margin: 14px"/>-->
                    <div class="date">{{item.gmtRelease}}</div>
                    <div v-if="item.isImportantNotice == 1"  class="imp-notice">重要通知</div>
                    <div class="content">
                        <h3 @click="handleView(item)">{{item.title}}</h3>
                        <!-- <p>{{item.summary}}</p> -->
                    </div>
                </div>
                <el-empty
                    v-if="newsList.length === 0"
                    :image="require('@/assets/images/ico_kong.png')"
                    style="height: 600px"
                />
            </div>
            <Pagination
                :currentPage.sync="page.page"
                :destination="page.destination"
                :pageSize="page.limit"
                :total="page.totalCount"
                :totalPage="page.totalPage"
                @currentChange="currentChange"
                @sizeChange="sizeChange">
            </Pagination>
        </div>
    </main>
</template>

<script>
import Pagination from '../../components/pagination.vue'
import { getWebInfo } from '@/api/frontStage/webInfo'
export default {
    name: 'notifications-index',
    components: { Pagination },
    data () {
        return {
            newsList: [],
            page: {
                page: 1,
                limit: 10,
                destination: 1,
                totalCount: 0,
                totalPage: 1,
            },
            loading: false
        }
    },
    methods: {
        handleView (item) {
            this.$router.push({ path: '/mFront/newsDetail', query: { id: item.contentId } })
        },
        currentChange (index) {
            this.page.page = index
            this.getList()
        },
        sizeChange () {
            this.getList()
        },
        getList () {

            const param = this.page
            param.programaKey = 'notification'
            // param.state = 1
            getWebInfo(param).then(res=>{
                this.page.page = res.currPage
                this.page.limit = res.pageSize
                this.page.totalCount = res.totalCount
                this.page.totalPage = res.totalPage
                this.newsList = res.list
            }).finally(
                this.loading = false
            )
        }
    },
    created () {
        console.log('notifications-index')
        this.getList()
    },
}
</script>

<style scoped lang="scss">
main {
    height: 100%;
    padding: 20px 0;

    .box {
        width: 1326px;
        height: 100%;
        border: 1px solid #e5e5e5;
        background-color: #fff;
    }
}
.list {
    min-height: 600px;
}
.list-item {
    border-bottom: 1px solid rgba(230, 230, 230, 1);
    display: flex;
    align-items: center;
    &>div {height: 100%;}
    .date {
        width: 160px;
        // padding: 23px 0 28px 20px;
        padding: 20px 0 20px 20px;
        font-size: 14px;
        color: rgba(153, 153, 153, 1);
    }
     .imp-notice {
        background-color: #D9272C;
        color: white;
        border-radius: 4px;
        padding: 6px 8px;
        margin: 0 10px;
        font-size: 14px;
        white-space: nowrap;
    }
    .content {
        flex: 1; // 占据剩余空间
        // padding: 20px 0; // 统一上下内边距为20px，移除原来的padding-top
        padding-top:12px;
        p{
            font-size: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            ////能够显示的行数，超出部分用...表示
            //-webkit-box-orient: vertical;
        }
    }
    h3 {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
        cursor: pointer;
        &:hover {color: rgba(33, 110, 198, 1);}
    }
    p {
        width: 1030px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        color: rgba(153, 153, 153, 1);
    }
}
</style>