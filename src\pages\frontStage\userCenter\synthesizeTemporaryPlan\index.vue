
<template>
    <main class="userCenter" v-loading="planListLoading">
        <div class="list-title dfa mb20">
            大宗临购计划列表
            <div class="search df" style="position: absolute; right: 40px">
                <div v-if="showDevFunc" class="mr30">
                    <!--                    数据权限-->
                    <el-select :value="dataSource" @change="dataSourceChange">
<!--                        <el-option label="本级及下级" :value="0"/>-->
                        <el-option label="本机构" :value="1"/>
<!--                        <el-option v-if="userPermission.hasSubOrg()" label="下级机构" :value="2"/>-->
                        <el-option
                            v-for="item in userPermission.subOrg"
                            :label="item.orgName" :value="item.orgId" :key="item.orgId"
                        />
                    </el-select>
                </div>
                <div class="box dfa">
                    <img src="@/assets/images/ico_search.png" alt="" />
                    <input v-model="keyword" type="text" placeholder="计划编号" />
                </div>
                <button @click="getPlanListM">搜索</button>
            </div>
        </div>
        <el-table border ref="msgTable" :data="list" style="min-height: 472px" :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
            :row-style="{ fontSize: '14px', height: '48px' }">
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column prop="billNo" label="计划编号" width="300"></el-table-column>
            <el-table-column prop="orgName" label="收货单位名称" width="200"></el-table-column>
            <el-table-column prop="supplierName" label="供货单位名称" width="300"></el-table-column>
            <el-table-column prop="totalAmount" label="计划含税总金额" width=""></el-table-column>
            <el-table-column prop="planDate" label="创建日期" width="100"/>
            <el-table-column prop="" label="操作" width="">
                <template v-slot="scope">
                    <div class="pointer" style="color: rgba(33, 110, 198, 1)" @click="handleViewDetail(scope.row.billId)">查看详情</div>
                </template>
            </el-table-column>
        </el-table>
        <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"
            :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange">
        </pagination>
    </main>
</template>

<script>
import pagination from '@/pages/frontStage/components/pagination.vue'

import { mapState } from 'vuex'
import { getPCWPSynthesizeTemporaryPlan } from '@/api/plan/plan'
import { UserPermission } from '@/utils/permissions'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return dateStr.split(' ')[0]
        },
    },
    computed: {
        ...mapState(['userInfo']),
    },
    components: { pagination },
    name: 'index',
    data () {
        return {
            planListLoading: false,
            keyword: null,
            pagination: {
                currPage: 1,
                destination: null,
                pageSize: 10,
                totalNum: 10,
                totalPage: 1,
            },
            list: [],
            userPermission: new UserPermission('物资下单权限'),
            dataSource: 1,
        }
    },
    created () {
        this.getPlanListM()
    },
    mounted () { },
    methods: {
        dataSourceChange (state) {
            this.dataSource = state
            if (state === 0) {
                this.userPermission.getAllOrgData()
            } else if (state === 1) {
                this.userPermission.getHostOrgData()
            } else if (state === 2) {
                this.userPermission.getSubOrgData()
            } else if (state.length > 1) {
                this.userPermission.getSubOrgData(state)
            }
            this.getPlanListM()
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getPlanListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getPlanListM()
        },
        getPlanListM () {
            let orgState = this.userPermission.orgDisplayState // （1本机及子级2只看本级3看指定子级）
            let orgId
            let childStatus
            let shortCode
            if (orgState === 1) {
                // 全部// 有权限
                if (this.userPermission.getOrgSearch() === 1) {
                    orgId = this.userInfo.orgId
                    childStatus = true
                    shortCode = this.userInfo.orgInfo.shortCode
                }else {
                    orgId = this.userInfo.orgId
                    childStatus = false
                    shortCode = this.userInfo.orgInfo.shortCode
                }

            }if (orgState === 2) {
                // 自己
                orgId = this.userInfo.orgId
                childStatus = false
                shortCode = this.userInfo.orgInfo.shortCode
            }
            if (orgState === 3) {
                orgId =  this.userPermission.currentSubOrgId[0]
                if (!orgId) {
                    this.$message.error('请选择下级机构')
                    return
                }
                childStatus = false
                // TODO 获取机构简码
                shortCode = this.userPermission.getShortCodeByOrgId(orgId)
            }
            let params = {
                limit: this.pagination.pageSize,
                orgId: this.userInfo.orgId,
                page: this.pagination.currPage,
                isChild: childStatus,
                orgShor: shortCode
            }
            if (this.keyword != null) {
                params.keyword = this.keyword
            }
            this.planListLoading = true
            getPCWPSynthesizeTemporaryPlan(params)
                .then(res => {
                    this.list = res.list
                    this.pagination.totalPage = res.totalPage
                    this.pagination.totalNum = res.totalCount
                    this.pagination.currPage = res.currPage
                    this.pagination.pageSize = res.pageSize
                }).finally(() => {
                    this.planListLoading = false
                })
        },
        // 跳转详情
        handleViewDetail (billId) {
            this.$router.push({ path: '/user/synthesizeTemporaryPlanDetail', query: { billId: billId } })
        },
    },
}
</script>

<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);

main {
    padding: 0 20px;
    border: $border;
}

.list-title {
    padding: 0;

    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}

.search {
    .box {
        width: 268px;
        height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);
        border-right: 0;

        img {
            width: 16px;
            height: 16px;
            margin: 0 4px 0 10px;
        }

        input {
            width: 230px;
        }
    }

    button {
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
    }
}

.list {
    .item {
        height: 52px;
        padding: 0 20px;
        font-size: 14px;
        border: $border;
        color: rgba(102, 102, 102, 1);
        position: relative;

        &>div:not(.pointer) {
            margin-right: 60px;
        }

        .pointer {
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 20px;
        }

        span {
            color: rgba(51, 51, 51, 1);
        }
    }
}
/deep/ .el-input--suffix .el-input__inner{
    height: 26px;
}
</style>
