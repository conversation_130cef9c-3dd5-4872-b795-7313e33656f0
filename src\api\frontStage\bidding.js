import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service
//未登录查看招标信息
// const getTenderLogOutPages = params => {
//     return httpPost({
//         url: '/tender/w/tender/listByEntity',
//         params,
//     })
// }

//暂时把这个请求方法注释掉，返回固定值
const getTenderLogOutPages = () => {
    return []
}

//招标总数
const getTenderNum = params => {
    return httpPost({
        url: '/tender/w/tender/getTenderNum',
        params,
    })
}

//登录时查看本机构招标信息
const getTenderOnloginPages = params => {
    return httpPost({
        url: '/tender/listByEntity',
        params,
    })
}

//根据招标id查询分包信息
const getbalseList = params => {
    return httpGet({
        url: '/tender/tenderPackage/listByBillId',
        params,
    })
}
//根据招标id查询招标清单
const getTenderdtlList = params => {
    return httpGet({
        url: '/tender/tenderPackage/findTenderDtlsByBillIdAll',
        params,
    })
}
//根据招标id查询所有分包参加公司信息
const getTenderPackageSubcontractorList = params => {
    return httpGet({
        url: '/tender/tenderPackage/TeBerSuByBillId',
        params,
    })
}
//根据招标id查询所有分包提问回答
const getQuesionList = params => {
    return httpGet({
        url: '/tender/tenderComment/listByBillId',
        params,
    })
}

const getPurchasePlatformDate = params => {
    return httpPost({
        url: '/purchasePlatform/api/app/Bid1/By-Bid-No',
        params,
        headers: {
            AppCode: '00000002'
        }
    })
}
const biddingPageList = params => {
    return httpPost({
        url: '/materialMall/w/biddingPurchase/biddingPageList',
        params,
    })
}
export {
    getTenderLogOutPages,
    getTenderOnloginPages,
    getbalseList,
    getTenderdtlList,
    getTenderPackageSubcontractorList,
    getQuesionList,
    getTenderNum,
    getPurchasePlatformDate,
    biddingPageList,
}