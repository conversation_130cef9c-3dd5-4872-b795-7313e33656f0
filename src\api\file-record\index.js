import service from '@/utils/request'
const { httpPost } = service

const findPaging = params => {
    return httpPost({
        url: '/file-record/listPaging',
        params
    })
}

const findRelatedPaging = params => {
    return httpPost({
        url: '/materialMall/file-record-related/listByEntity',
        params
    })
}

const findRelatedCreate = params => {
    return httpPost({
        url: '/materialMall/file-record-related/create',
        params
    })
}

export {
    findPaging,
    findRelatedPaging,
    findRelatedCreate
}