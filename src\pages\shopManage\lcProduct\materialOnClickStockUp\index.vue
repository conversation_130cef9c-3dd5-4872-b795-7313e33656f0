<template>
  <div>
    <div class="base-page">
      <!-- 列表 -->
      <div class="right">
        <div class="e-table">
          <!-- -搜索栏----------------------------搜索栏 -->
          <div class="top">
            <div class="left"/>
            <!-- 新增按钮 -->
            <div class="search_box">
              <el-input clearable
                        type="text" @keyup.enter.native="onShopSearch" style="width: 80%;" placeholder="输入搜索关键字"
                        v-model="shopKeywords"
              ><img src="@/assets/search.png" slot="suffix" @click="onShopSearch"/>
              </el-input>
            </div>
          </div>
          <!-- -搜索栏----------------------------搜索栏 -->
        </div>
        <!-- ---------------------------------表格开始--------------------------------- -->
        <div class="e-table">
          <el-table
              ref="tableRef" class="table"
              :height="rightTableHeight" :data="shopTableData" border highlight-current-row
              v-loading="shopVisible"
          >
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <!-- 名称 -->
            <el-table-column label="店铺名称" width="">
              <template v-slot="scope">
                <span class="action" @dblclick="handleView(scope.row)"
                @click="handleView(scope.row)"
                >{{ scope.row.shopName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="店铺编号" prop="serialNum">
            </el-table-column>
            <el-table-column label="所在区域" prop="province">
              <template v-slot="scope">
                <span>{{ scope.row.province}}{{scope.row.city}}</span>
              </template>
            </el-table-column>
            <el-table-column label="开店时间" prop="openDate">
            </el-table-column>
            <el-table-column label="主营范围" prop="mainBusiness">
            </el-table-column>
          </el-table>
        </div>
        <!-- 分页器 -->
        <Pagination
            :total="shopPages.totalCount" :pageSize.sync="shopPages.pageSize"
            :currentPage.sync="shopPages.currPage"
            @currentChange="currentShopChange" @sizeChange="sizeShopChange"
        />
      </div>
    </div>
    <div class="base-page1">
      <div class="right1">
        <div class="e-table">
          <!-- -搜索栏----------------------------搜索栏 -->
          <div class="top">
            <div class="left">
              <el-button type="primary" @click="onClickStockUp" style="margin-left: 10px;" class="btn-blue">
                一键铺货
              </el-button>
            </div>
            <!-- 新增按钮 -->
            <div class="search_box">
              <el-cascader :options="classList"
                           style="width: 100%"
                           collapse-tags
                           collapse-tags-tooltip
                           placeholder="商品分类"
                           v-model="classId"
                           :props="groupSearchProps"
                           clearable filterable
                           :show-all-levels="true"></el-cascader>
              <el-input clearable
                        type="text" @keyup.enter.native="onGoodsSearch" style="width: 70%;" placeholder="输入搜索关键字"
                        v-model="goodsKeywords"
              ><img src="@/assets/search.png" slot="suffix" @click="onGoodsSearch"/>
              </el-input>
            </div>
          </div>
          <!-- -搜索栏----------------------------搜索栏 -->
        </div>
        <!-- ---------------------------------表格开始--------------------------------- -->
        <div class="e-table">
          <el-table
              @row-click="handleCurrentGoodsClick" ref="tableRef" class="table"
              :data="goodsTableData" border highlight-current-row
              :height="rightTableHeight"
              v-loading="goodsVisible"
              @current-change="handleCurrentGoodsChange" @selection-change="handleSelectionGoodsChange"
          >
            <el-table-column type="selection" width="40"></el-table-column>
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <!-- 名称 -->
            <el-table-column label="名称" width="" prop="productName">
            </el-table-column>
            <el-table-column label="物资分类" prop="classPathName">
            </el-table-column>
            <el-table-column label="商品编码" prop="serialNum">
            </el-table-column>
            <el-table-column label="图片">
              <template v-slot="scope">
                <el-image
                    style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg"
                    fit="cover"
                ></el-image>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="state">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.state==0">待上架</el-tag>
                <el-tag v-if="scope.row.state==1" type="success">已上架</el-tag>
                <el-tag v-if="scope.row.state==2" type="danger">已下架</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="销售价格" prop="sellPrice">
            </el-table-column>
            <el-table-column label="原价" prop="originalPrice">
            </el-table-column>
            <el-table-column label="成本价" prop="costPrice">
            </el-table-column>
            <el-table-column label="差价" prop="profitPrice">
            </el-table-column>
            <el-table-column label="库存" prop="stock">
            </el-table-column>
          </el-table>
        </div>
        <!-- 分页器 -->
        <Pagination
            :total="goodsPages.totalCount" :pageSize.sync="goodsPages.pageSize"
            :currentPage.sync="goodsPages.currPage"
            @currentChange="currentGoodsChange" @sizeChange="sizeGoodsChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/pagination/pagination.vue'
import { mapState } from 'vuex'
import { debounce } from '@/utils/common'
import { findTwoSupplierShopByPage } from '@/api/platform/shop/shopManager'
import { getProductOneClick, oneClickStockUp } from '@/api/platform/product/materialManage'
import { treeByName } from '@/api/platform/product/productCategory'
export default {
    components: { Pagination },
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            loading: false,
            queryVisible: false,
            goodsVisible: false,
            shopVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            shopKeywords: '',
            goodsKeywords: '',
            classId: '',
            groupSearchProps: {
                expandTrigger: 'hover',
                checkStrictly: true,
                children: 'children',
                label: 'className',
                value: 'classId',
            },
            classList: [],
            currentRow: null,
            currentShopRow: null,
            isGoods: false,
            changedRow: [],
            selectedRows: [],
            goodsPages: {
                totalCount: 0,
                currPage: 1,
                pageSize: 10,
            },
            shopPages: {
                totalCount: 0,
                currPage: 1,
                pageSize: 10
            },
            shopTableData: [],
            goodsTableData: [],
            permissionList: [],
            scope: '',
            tableLoading: false,

            a: '1',
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getShopTableData()
        this.getGoodsTableData()
        this.getGroupList()
    },
    created () {
    // this.getTableData()
    },
    methods: {
        getGroupList () {
            let params = {
                productType: 0
            }
            treeByName(params).then(res => {
                this.classList = res
            })
        },
        getShopTableData () {
            let params = {
                limit: this.shopPages.pageSize,
                page: this.shopPages.currPage,
                shopName: this.shopKeywords
            }
            findTwoSupplierShopByPage(params).then(res=>{
                this.shopPages.totalCount = res.totalCount
                this.shopTableData = res.list
            })
        },
        // 分页函数
        currentShopChange () {
            if (this.shopPages.currPage === undefined) {
                this.shopPages.currPage = this.shopPages.totalPage
            }
            this.getShopTableData()
        },
        currentGoodsChange () {
            if (this.goodsPages.currPage === undefined) {
                this.goodsPages.currPage = this.goodsPages.totalPage
            }
            this.getGoodsTableData()
        },
        sizeShopChange (size) {
            this.shopPages.pageSize = size
            this.getShopTableData()
        },
        sizeGoodsChange (size) {
            this.goodsPages.pageSize = size
            this.getGoodsTableData()
        },
        getGoodsTableData () {
            this.goodsVisible = true
            let params = {
                limit: this.shopPages.pageSize,
                page: this.shopPages.currPage,
                shopId: this.currentShopRow?.shopId ? this.currentShopRow.shopId : null,
                productName: this.goodsKeywords,
                serialNum: this.goodsKeywords,
                classId: this.classId ? this.classId[this.classId.length - 1] : null

            }
            getProductOneClick(params).then(res=>{
                this.goodsPages.totalCount = res.totalCount
                this.goodsTableData = res.list
                this.isGoods = true
            }).finally(()=>{
                this.goodsVisible = false
                this.shopVisible = false
            })
        },

        handleCurrentGoodsChange ( val ) {
            this.currentRow = val
        },
        handleSelectionGoodsChange (selection) {
            this.selectedRows = selection
        },
        handleCurrentGoodsClick (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        handleView (data) {
            this.shopVisible = true
            this.currentShopRow = data
            this.getGoodsTableData()
        },
        onShopSearch () {
            this.shopPages.currPage = 1
            this.getShopTableData()
        },
        onGoodsSearch () {
            this.goodsPages.currPage = 1
            this.getGoodsTableData()
        },
        onClickStockUp () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择铺货的商品', () => {
                })
            }

            this.clientPop('info', '确定铺货所选商品？', async () => {
                this.goodsVisible = true
                let ids = this.selectedRows.map(item => item.productId)
                oneClickStockUp(ids).then(res=>{
                    if(res.code == 200) {
                        this.$message.success('铺货成功')
                    }
                }).finally(()=>{
                    this.getGoodsTableData()
                })
            })
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }

}
</script>
<style lang="scss" scoped>
.right .top {
  padding-right: 10px
}
.right1 .top {
  padding-right: 10px;
  height: 70px
}

.base-page .left {
  width: 30%;
  padding: 0;
}
.base-page1 .left {
  width: 30%;
  padding: 0;
}

.base-page .center {
  width: 60%;
}
.base-page1 .center {
  width: 60%;
}

.base-page {
  width: 100%;
  height: 50%;
}
.base-page1 {
  width: 100%;
  height: 50%;
}

/deep/ .e-form .el-form-item .el-form-item__explain {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}

.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
  -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
  -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}

</style>