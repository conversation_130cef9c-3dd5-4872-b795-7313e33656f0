export default [
    {
        path: '/supplierSys',
        component: () => import('@/pages/supplierSys/index'),
        redirect: '/supplierSys/shopManage/mall',
        children: [
            // 默认展示空白页
            {
                path: '/supplierSys/shopManage/mall',
                name: 'shopManageMallIndex',
                component: () => import('@/pages/shopManage/mall/index.html'),
                meta: {
                    title: ''
                }
            },
            {
                path: '/supplierSys/product/materialSupplierYesAffirm',
                name: 'materialSupplierYesAffirm',
                component: () => import('@/pages/shopManage/product/materialSupplierYesAffirm'),
                meta: {
                    keepAlive: true,
                    title: '已确认商品'
                }
            },
            {
                path: '/supplierSys/product/materialSupplierNotAffirm',
                name: 'materialSupplierNotAffirm',
                component: () => import('@/pages/shopManage/product/materialSupplierNotAffirm'),
                meta: {
                    keepAlive: true,
                    title: '未确认的商品'
                }
            },
            {
                path: '/supplierSys/product/materialSupplierAffirm',
                name: 'materialSupplierAffirm',
                component: () => import('@/pages/shopManage/product/materialSupplierAffirm'),
                meta: {
                    keepAlive: true,
                    title: '待确认的零星采购商品'
                }
            },
            {
                path: '/supplierSys/product/materialSupplierAffirmDetail',
                name: 'materialSupplierAffirmDetail',
                component: () => import('@/pages/shopManage/product/materialSupplierAffirm/detail.vue'),
                meta: {
                    title: '商品详情'
                }
            },
            {
                path: '/supplierSys/myReminder/reminder',
                name: 'reminder',
                component: () => import('@/pages/supplierSys/myReminder/reminder/index'),
                meta: {
                    title: '招标提醒'
                }
            },
            {
                path: '/supplierSys/myReminder/reminderDetail',
                name: 'reminderDetail',
                component: () => import('@/pages/supplierSys/myReminder/reminder/detail.vue'),
                meta: {
                    title: '供应商平台-我的提醒详情'
                }
            },
            {
                path: '/supplierSys/myReminder/bidingdetail',
                name: 'bidingdetail',
                component: () => import('@/pages/supplierSys/myReminder/reminder/bidingdetail.vue'),
                meta: {
                    title: '供应商平台-我的提醒中标详情'
                }
            },
            {
                path: '/supplierSys/bidManage/attendBiding',
                name: 'attendBiding',
                component: () => import('@/pages/supplierSys/bidManage/attendBiding/index'),
                meta: {
                    title: '供应商平台-参与投标'
                }
            },
            {
                path: '/supplierSys/bidManage/bidingList',
                name: 'bidingList',
                component: () => import('@/pages/supplierSys/bidManage/bidingList/index'),
                meta: {
                    keepAlive: true,
                    title: '我的竞价列表'
                }
            },
            {
                path: '/supplierSys/bidManage/bidingListDetail',
                name: 'bidingListDetail',
                component: () => import('@/pages/supplierSys/bidManage/bidingList/detail'),
                meta: {
                    title: '竞价详情'
                }
            },
            {
                path: '/supplierSys/bidManage/bidingRecord',
                name: 'bidingRecord',
                component: () => import('@/pages/supplierSys/bidManage/biding-record/index'),
                meta: {
                    title: '竞价记录'
                }
            },
            {
                path: '/supplierSys/bidManage/bidingRecordDetail',
                name: 'bidingRecordDetail',
                component: () => import('@/pages/supplierSys/bidManage/biding-record/detail'),
                meta: {
                    title: '竞价记录详情'
                }
            },
            {
                path: '/supplierSys/bidManage/myBidding',
                name: 'myBidingList',
                component: () => import('@/pages/supplierSys/bidManage/myBidding/index'),
                meta: {
                    keepAlive: true,
                    title: '我参与的竞价'
                }
            },
            {
                path: '/supplierSys/bidManage/myBiddingDetail',
                name: 'myBiddingDetail',
                component: () => import('@/pages/supplierSys/bidManage/myBidding/detail'),
                meta: {
                    keepAlive: true,
                    title: '我的竞价'
                }
            },
            {
                path: '/supplierSys/bidManage/myLgBiddingDetail',
                name: 'myLgBiddingDetail',
                component: () => import('@/pages/supplierSys/bidManage/myBidding/detail1'),
                meta: {
                    keepAlive: true,
                    title: '我的竞价'
                }
            },
            {
                path: '/supplierSys/bidManage/attendBidingDetail',
                name: 'attendBidingDetail',
                component: () => import('@/pages/supplierSys/bidManage/attendBiding/detail'),
                meta: {
                    title: '供应商平台-参与投标详情'
                }
            },
            {
                path: '/supplierSys/bidManage/bidManage',
                name: 'bidManage',
                component: () => import('@/pages/supplierSys/bidManage/bidManage/index'),
                meta: {
                    title: '供应商平台-中标信息'
                }
            },
            {
                path: 'supplierSys/bidManage/bidManageDetail',
                name: 'bidManageDetail',
                component: () => import('@/pages/supplierSys/bidManage/bidManage/detail'),
                meta: {
                    title: '供应商平台-中标信息详情'
                }
            },
            {
                path: '/shopManage/product/materialManageDetail',
                name: 'materialManageDetail',
                meta: {
                    title: '供应商平台-投标管理'
                },
            },
            {
                path: '/supplierSys/pcwp1/accountStatement',
                name: 'pcwp1accountStatement',
                component: () => import('@/pages/supplierSys/accountStatement/accountStatement/pcwp1/index.vue'),
                meta: {
                    title: '供应商平台-pcwp1大宗物资供应商对账'
                }
            },
            {
                path: '/supplierSys/pcwp1/fragmentaryaccountStatement',
                name: 'pcwp1fragmentaryAccountStatement',
                component: () => import('@/pages/supplierSys/accountStatement/fragmentaryaccountStatement/pcwp1/index.vue'),
                meta: {
                    title: '供应商平台-pcwp1零星采购供应商对账'
                }
            },
            {
                path: '/supplierSys/pcwp2/accountStatement',
                name: 'pcwp2AccountStatement',
                component: () => import('@/pages/supplierSys/accountStatement/accountStatement/pcwp2/index.vue'),
                meta: {
                    title: '供应商平台-pcwp2大宗物资供应商对账'
                }
            },
            {
                path: '/supplierSys/pcwp1/accountStatementDetail',
                name: 'accountStatementDetail',
                component: () => import('@/pages/supplierSys/accountStatement/accountStatement/pcwp1/detail.vue'),
                meta: {
                    title: '供应商平台-pcwp1大宗物资供应商对账详情'
                }
            },
            {
                path: '/supplierSys/pcwp1/fragmentaryaccountStatementDetail',
                name: 'fragmentaryaccountStatementDetail',
                component: () => import('@/pages/supplierSys/accountStatement/fragmentaryaccountStatement/pcwp1/detail.vue'),
                meta: {
                    title: '供应商平台-pcwp1零星采购供应商对账详情'
                }
            },
            {
                path: '/supplierSys/pcwp2/accountStatementDetail',
                name: 'pcwp2AccountStatementDetail',
                component: () => import('@/pages/supplierSys/accountStatement/accountStatement/pcwp2/detail.vue'),
                meta: {
                    title: '供应商平台-pcwp2大宗物资供应商对账详情'
                }
            },
            {
                path: '/supplierSys/pcwp1/contractDetail',
                name: 'contractDetail',
                component: () => import('@/pages/supplierSys/contract/pcwp1/detail.vue'),
                meta: {
                    title: '供应商平台-pcwp1合同管理详情'
                }
            },
            {
                path: '/supplierSys/pcwp2/contractDetail',
                name: 'contractDetail',
                component: () => import('@/pages/supplierSys/contract/pcwp2/detail.vue'),
                meta: {
                    title: '供应商平台-pcwp2合同管理详情'
                }
            },
            {
                path: '/supplierSys/pcwp2/contract',
                name: 'contract',
                component: () => import('@/pages/supplierSys/contract/pcwp1/index.vue'),
                meta: {
                    title: '供应商平台-pcwp2合同管理'
                }
            },
            {
                path: '/supplierSys/pcwp1/contract',
                name: 'contract',
                component: () => import('@/pages/supplierSys/contract/pcwp2/index.vue'),
                meta: {
                    title: '供应商平台-pcwp1合同管理'
                }
            },
            {
                path: '/supplierSys/shipmentPlan',
                name: 'shipmentPlan',
                component: () => import('@/pages/supplierSys/shipmentManage/shipmentPlan/index'),
                meta: {
                    title: '供应商平台-发货计划'
                }
            },
            {
                path: '/supplierSys/shipmentPlanDetail',
                name: 'shipmentPlanDetail',
                component: () => import('@/pages/supplierSys/shipmentManage/shipmentPlan/detail.vue'),
                meta: {
                    title: '供应商平台-发货计划详情'
                }
            },
            {
                path: '/supplierSys/purchaseMentProject',
                name: 'purchaseMentProject',
                component: () => import('@/pages/supplierSys/shipmentManage/purchaseMentProject/index'),
                meta: {
                    title: '供应商平台-要货计划'
                }
            },
            {
                path: '/supplierSys/purchaseMentProjectDetail',
                name: 'purchaseMentProjectDetail',
                component: () => import('@/pages/supplierSys/shipmentManage/purchaseMentProject/detail.vue'),
                meta: {
                    title: '供应商平台-要货计划详情'
                }
            },
            {
                path: '/supplierSys/planDetail',
                name: 'planDetail',
                component: () => import('@/pages/supplierSys/shipmentManage/createShipmentPlanDtl/index.vue'),
                meta: {
                    title: '供应商平台-计划商品明细'
                }
            },
            // 店铺信息
            {
                path: '/supplierSys/shop/shopInfoManage',
                name: 'shopInfoManage',
                component: () => import('@/pages/shopManage/shop/shopInfoManage'),
                meta: {
                    title: '店铺信息'
                }
            },
            {
                path: '/supplierSys/shop/selectSupplier',
                name: 'selectSupplier',
                component: () => import('@/pages/shopManage/shop/shopInfoManage/selectSupplier.vue'),
                meta: {
                    title: '供方管理'
                }
            },
            {
                path: '/supplierSys/sheet/sheet',
                name: 'sheet',
                component: () => import('@/pages/supplierSys/sheet/sheet/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '对账单'
                }
            },
            {
                path: '/supplierSys/sheet/synthesizeTemporary',
                name: 'synthesizeTemporarySheet',
                component: () => import('@/pages/supplierSys/sheet/synthesizeTemporary/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗临购对账单'
                }
            },
            {
                path: '/supplierSys/sheet/synthesizeTemporarySDetail',
                name: 'synthesizeTemporarySDetail',
                component: () => import('@/pages/supplierSys/sheet/synthesizeTemporary/detail.vue'),
                meta: {
                    title: '大宗临购对账单详情'
                }
            },
            {
                path: '/supplierSys/sheet/synthesizeTemporaryFDetail',
                component: () => import('@/pages/supplierSys/sheet/synthesizeTemporary/floatDetailInfo.vue'),
                name: 'synthesizeTemporaryFDetail',
                meta: {
                    title: '大宗临购浮动价格对账详情'
                }
            },
            {
                path: '/supplierSys/sheet/synthesizeTemporaryGDetail',
                component: () => import('@/pages/supplierSys/sheet/synthesizeTemporary/fixationDetailInfo.vue'),
                name: 'synthesizeTemporaryGDetail',
                meta: {
                    title: '大宗临购固定价格对账详情'
                }
            },
            {
                path: '/supplierSys/sheet/sheet',
                name: 'sheet',
                component: () => import('@/pages/supplierSys/sheet/sheet/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗临购对账单'
                }
            },
            {
                path: '/supplierSys/sheet/twoSheet',
                name: 'twoSheet',
                component: () => import('@/pages/supplierSys/sheet/twoSheet/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级供应商零星对账单'
                }
            },

            {
                //物资公司
                path: '/supplierSys/sheet/blockLinCai/twoSheet',
                name: 'blockLinCaiTwoSheet',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/twoSupplier/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '对账-二级大宗临购对账单'
                }
            },

            {
                //物资公司
                path: '/supplierSys/sheet/dzMonth/twoSupplier/twoSheet',
                name: 'dzTwoSheet',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSupplier/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '对账-二级大宗月供对账单'
                }
            },
            {
                //二级供应商
                path: '/supplierSys/sheet/blockLinCai/supplierTwoSheet',
                name: 'blockSupplierTwoSheet',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/supplier/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级大宗临购对账-大宗临购对账单'
                }
            },
            {
                //二级供应商
                path: '/supplierSys/sheet/turnoverMaterials/supplierTwoSheet',
                name: 'turnoverSupplierTwoSheet',
                component: () => import('@/pages/supplierSys/sheet/turnoverMaterials/twoSheet/supplier/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级大宗临购对账-周转材料对账单'
                }
            },
            {  //二级供应商
                path: '/supplierSys/turnoverMaterials/twoSupplierAddFloatDetail',
                name: 'turnoverTwoSupplierAddFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/turnoverMaterials/twoSheet/supplier/floatAddDetail.vue'),
                meta: {
                    title: '二级订单-二级供应商周转材料对账单添加'
                }
            },
            {
                path: '/supplierSys/sheet/turnoverMaterials/fixationDetail',
                name: 'turnoverMaterialsFixationDetail',
                component: () => import('@/pages/supplierSys/sheet/turnoverMaterials/twoSheet/twoSupplier/fixationDetail.vue'),
                meta: {
                    title: '二级周转材料对账-新增固定价格对账'
                }
            },
            {
                //二级供应商
                path: '/supplierSys/sheet/turnoverMaterials/twoSupplierFloatDetail',
                name: 'turnoverTwoSupplierFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/turnoverMaterials/twoSheet/supplier/floatDetailInfo.vue'),
                meta: {
                    title: '二级订单-二级周转材料对账单详情'
                }
            },
            {
                //二级供应商
                path: '/supplierSys/sheet/dzMonth/supplierTwoSheet',
                name: 'dzSupplierTwoSheet',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSheet/supplier/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级订单-二级大宗月供对账单'
                }
            },
            {
                path: '/supplierSys/sheet/blockLinCai/fixationDetail',
                name: 'blockFixationDetail',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/twoSupplier/fixationDetail.vue' +
                ''),
                meta: {
                    title: '二级大宗临购对账-新增固定价格对账'
                }
            },

            {
                //物资公司
                path: '/supplierSys/sheet/blockLinCai/floatDetail',
                name: 'blockFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/twoSupplier/floatDetail.vue'),
                meta: {
                    title: '对账-新增二级大宗临购浮动价格对账单'
                }
            },

            {
                //物资公司
                path: '/supplierSys/sheet/dz/dzAddFloatDetail',
                name: 'dzAddFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSupplier/floatDetail.vue'),
                meta: {
                    title: '对账-新增二级大宗月供浮动价格对账单'
                }
            },
            {
                //物资公司
                path: '/supplierSys/sheet/dz/dzAddFlxDetail',
                name: 'dzAddFlxDetail',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSupplier/flxatDetail.vue'),
                meta: {
                    title: '对账-新增二级大宗临购固定价格对账单'
                }
            },
            {
                //物资公司
                path: '/supplierSys/sheet/blockLinCai/flxatDetail',
                name: 'blockFlxatDetail',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/twoSupplier/flxatDetail.vue'),
                meta: {
                    title: '对账-新增二级大宗临购固定价格对账单'
                }
            },
            {  //物资公司
                path: '/supplierSys/blockLinCai/twoFloatDetail',
                name: '/blockLinCaiFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/floatDetail.vue'),
                meta: {
                    title: '对账-二级供应商大宗临购对账单详情'
                }
            },

            {
                //二级供应商
                path: '/supplierSys/sheet/blockLinCai/twoSupplierFloatDetail',
                name: 'blockTwoSupplierFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/supplier/floatDetailInfo.vue'),
                meta: {
                    title: '二级订单-二级大宗临购对账单详情'
                }
            },
            {  //二级供应商
                path: '/supplierSys/blockLinCai/twoSupplierAddFloatDetail',
                name: 'blockTwoSupplierAddFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/supplier/floatAddDetail.vue'),
                meta: {
                    title: '二级订单-二级供应商大宗临购对账单添加'
                }
            },
            {  //二级供应商
                path: '/supplierSys/dzMonth/dzTwoSupplierAddFloatDetail',
                name: 'dzTwoSupplierAddFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSheet/supplier/floatAddDetail.vue'),
                meta: {
                    title: '二级订单-二级供应商大宗月供浮动对账单添加'
                }
            },
            {  //二级供应商
                path: '/supplierSys/dzMonth/dzTwoSupplierAddFixedDetail',
                name: 'dzTwoSupplierAddFixedDetail',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSheet/supplier/fixedAddDetail.vue'),
                meta: {
                    title: '二级订单-二级供应商大宗月供固定对账单添加'
                }
            },
            {
                //二级供应商
                path: '/supplierSys/sheet/dzMonth/dzTwoSupplierFixedDetail',
                name: 'dzTwoSupplierFixedDetail',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSheet/supplier/fixedDetailInfo.vue'),
                meta: {
                    title: '二级订单-二级大宗月供固定价格对账单详情'
                }
            },
            {
                //二级供应商
                path: '/supplierSys/sheet/dzMonth/dzTwoSupplierFloatDetail',
                name: 'dzTwoSupplierFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSheet/supplier/floatDetailInfo.vue'),
                meta: {
                    title: '二级订单-二级大宗月供浮动对账单详情'
                }
            },

            {
                //二级供应商
                path: '/supplierSys/sheet/blockLinCai/twoSupplierFlxatDetail',
                name: 'blockTwoSupplierFlxatDetail',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/supplier/flxatDetailInfo.vue'),
                meta: {
                    title: '二级订单-二级大宗临购固定价格对账单详情'
                }
            },
            {  //二级供应商
                path: '/supplierSys/blockLinCai/twoSupplierAddFlxatDetail',
                name: 'blockTwoSupplierAddFlxatDetail',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/supplier/flxatAddDetail.vue'),
                meta: {
                    title: '二级订单-二级供应商大宗临购固定价格对账单添加'
                }
            },
            {
                path: '/supplierSys/twoFloatDetail',
                name: 'supplierSysTwoFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/twoSheet/floatDetail.vue'),
                meta: {
                    title: '二级供应商零星采购对账单'
                }
            },
            {
                path: '/supplierSys/twoSheetDetailInfo',
                name: 'twoSheetDetailInfo',
                component: () => import('@/pages/supplierSys/sheet/twoSheet/floatDetailInfo.vue'),
                meta: {
                    title: '二级供应商零星采购对账单详情'
                }
            },

            {
                path: '/supplierSys/blockLinCai/twoSheetDetailInfo',
                name: 'blockLinCaiTwoSheetDetailInfo',
                component: () => import('@/pages/supplierSys/sheet/blockLinCai/twoSheet/floatDetailInfo.vue'),
                meta: {
                    title: '对账-二级供应商大宗临购对账单详情'
                }
            },
            {
                path: '/supplierSys/dzMonth/dzTwoSheetDetailInfo',
                name: 'dzTwoSheetDetailInfo',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSheet/floatDetailInfo.vue'),
                meta: {
                    title: '对账-二级供应商大宗月供浮动对账单详情'
                }
            },
            {
                path: '/supplierSys/dzMonth/dzTwoSheetFixedDetailInfo',
                name: 'dzTwoSheetFixedDetailInfo',
                component: () => import('@/pages/supplierSys/sheet/dzMonth/twoSheet/fixedDetailInfo.vue'),
                meta: {
                    title: '对账-二级供应商大宗月供固定对账单详情'
                }
            },

            // 周转材料对账单路由
            {
                //物资公司 - 二级供应商周转材料对账单列表
                path: '/supplierSys/sheet/turnoverMaterials/twoSheet',
                name: 'turnoverMaterialsTwoSheet',
                component: () => import('@/pages/supplierSys/sheet/turnoverMaterials/twoSheet/twoSupplier/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '对账-二级周转材料对账单'
                }
            },
            {
                //对账-新增二级周转材料浮动价格对账单
                path: '/supplierSys/sheet/turnoverMaterials/floatDetail',
                name: 'turnoverMaterialsFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/turnoverMaterials/twoSheet/twoSupplier/floatDetail.vue'),
                meta: {
                    title: '对账-新增二级周转材料浮动价格对账单'
                }
            },
            {  //二级供应商
                path: '/supplierSys/turnoverMaterials/twoSupplierAddFlxatDetail',
                name: 'turnoverTwoSupplierAddFlxatDetail',
                component: () => import('@/pages/supplierSys/sheet/turnoverMaterials/twoSheet/supplier/flxatAddDetail.vue'),
                meta: {
                    title: '二级订单-二级供应商周转材料固定价格对账单添加'
                }
            },
            {
                //对账-新增二级周转材料浮动价格对账单详情页面
                path: '/supplierSys/sheet/turnoverMaterials/floatDetailInfo',
                name: 'turnoverMaterialsFloatDetailInfo',
                component: () => import('@/pages/supplierSys/sheet/turnoverMaterials/twoSheet/twoSupplier/floatDetailInfo.vue'),
                meta: {
                    title: '对账-新增二级周转材料浮动价格对账单详情页面'
                }
            },

            {
                path: '/supplierSys/sheet/twoSupplierTwoSheet',
                name: 'twoSupplierTwoSheet',
                component: () => import('@/pages/supplierSys/sheet/twoSheet/twoSupplier/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级对零星账单对账'
                }
            },
            {
                path: '/supplierSys/twoSupplierFloatDetail',
                name: 'twoSupplierFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/twoSheet/twoSupplier/floatDetail.vue'),
                meta: {
                    title: '供应商-二级供应商对账单添加'
                }
            },
            {
                path: '/supplierSys/twoSupplierDetailInfo',
                name: 'twoSupplierDetailInfo',
                component: () => import('@/pages/supplierSys/sheet/twoSheet/twoSupplier/floatDetailInfo.vue'),
                meta: {
                    title: '二级订单- 二级零星采购对账单详情'
                }
            },
            {
                path: '/supplierSys/sheetDetail',
                name: 'sheetDetail',
                component: () => import('@/pages/supplierSys/sheet/sheet/detail.vue'),
                meta: {
                    title: '对账单详情'
                }
            },
            {
                path: '/supplierSys/floatDetail',
                name: 'supplierSysFloatDetail',
                component: () => import('@/pages/supplierSys/sheet/sheet/floatDetail.vue'),
                meta: {
                    title: '浮动对账单'
                }
            },
            {
                path: '/supplierSys/fixationDetail',
                name: 'supplierSysFixationDetail',
                component: () => import('@/pages/supplierSys/sheet/sheet/fixationDetail.vue'),
                meta: {
                    title: '固定对账单'
                }
            },
            {
                path: '/supplierSys/floatDetailInfo',
                name: 'supplierSysFloatDetailInfo',
                component: () => import('@/pages/supplierSys/sheet/sheet/floatDetailInfo.vue'),
                meta: {
                    title: '浮动对账单'
                }
            },
            {
                path: '/supplierSys/fixationDetailInfo',
                name: 'supplierSysFixationDetailInfo',
                component: () => import('@/pages/supplierSys/sheet/sheet/fixationDetailInfo.vue'),
                meta: {
                    title: '固定对账单'
                }
            },
            // 我的订单
            {
                path: '/supplierSys/order/searchOrder',
                name: 'searchOrder',
                component: () => import('@/pages/shopManage/order/searchOrder'),
                meta: {
                    keepAlive: true,
                    title: '零星采购订单'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/towIndex',
                name: 'towIndex',
                component: () => import('@/pages/shopManage/order/searchOrder/towIndex'),
                meta: {
                    keepAlive: true,
                    title: '零星采购订单'
                }
            },
            {
                path: '/supplierSys/order/work/index',
                name: 'towIndex',
                component: () => import('@/pages/shopManage/order/work/index'),
                meta: {
                    keepAlive: true,
                    title: '工单管理'
                }
            },
            {
                path: '/supplierSys/order/searchOrderDetail',
                name: 'shopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/detail'),
                meta: {
                    title: '零星采购订单详情'
                }
            },

            {
                path: '/supplierSys/order/searchOrder/officeIndex',
                name: 'officeIndex',
                component: () => import('@/pages/shopManage/order/searchOrder/officeIndex.vue'),
                meta: {
                    keepAlive: true,
                    title: '店铺订单'
                }
            },
            {
                path: '/supplierSys/order/officeDetail',
                name: 'officeDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/officeDetail'),
                meta: {
                    title: '店铺订单详情'
                }
            },
            {
                path: '/supplierSys/order/ordDetailDetail',
                name: 'ordDetailDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/ordDetail'),
                meta: {
                    title: '店铺订单详情'
                }
            },
            //供应商订单
            {
                path: '/supplierSys/order/smallShipTwo/searchOrder',
                name: 'searchOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/smallShip/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '零星采购订单'
                }
            },
            //供应商订单
            {
                path: '/supplierSys/blockOrder/smallShipTwo/searchOrder',
                name: 'blockOrderSearchOrder',
                component: () => import('@/pages/shopManage/order/blockOrder/smallShipTwo/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗临购订单'
                }
            },
            {
                //物资分公司
                path: '/supplierSys/order/blockOrder/searchOrder',
                name: 'blockOrder',
                component: () => import('@/pages/shopManage/order/blockOrder/towIndex.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗临购多供方订单'
                }
            },
            {
                //物资分公司(周转材料)
                path: '/supplierSys/order/turnoverMaterials/searchOrder',
                name: 'turnoverMaterials',
                component: () => import('@/pages/shopManage/order/turnoverMaterials/towIndex.vue'),
                meta: {
                    keepAlive: true,
                    title: '周转材料多供方订单'
                }
            },
            {
                path: '/supplierSys/order/blockOrderDetail',
                name: 'shopManageBlockOrderDetail',
                component: () => import('@/pages/shopManage/order/blockOrder/detail'),
                meta: {
                    title: '大宗临购订单详情'
                }
            },
            {
                //物资公司
                path: '/supplierSys/order/blockOrder/twoOrder/twoShip',
                name: 'blockOrderTwoShiped',
                component: () => import('@/pages/shopManage/order/blockOrder/twoShip.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗临购发货单'
                }
            },
            {
                //物资公司(周转材料)
                path: '/supplierSys/order/turnoverMaterials/twoOrder/twoShip',
                name: 'turnoverMaterials',
                component: () => import('@/pages/shopManage/order/turnoverMaterials/twoShip.vue'),
                meta: {
                    keepAlive: true,
                    title: '周转材料多供方发货单'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/twoOrder',
                name: 'searchOrderTwoOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/twoOrder/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '待确认零星采购订单'
                }
            },
            {
                path: '/supplierSys/order/blockOrder/twoOrder',
                name: 'blockOrderOrderTwoOrder',
                component: () => import('@/pages/shopManage/order/blockOrder/twoOrder/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '待确认大宗临购订单'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/contractPlanTwoOrder',
                name: 'searchContractPlanTwoOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/contractPlanTwoOrder/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '待确认大宗月供订单'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/contractPlanTwoOKOrder',
                name: 'searchContractPlanTwoOKOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/contractPlanTwoOKOrder/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '已确认大宗月供订单'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/contractPlanTwoOKOrderDetail',
                name: 'contractPlanTwoOKOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/contractPlanTwoOKOrder/detail.vue'),
                meta: {
                    title: '订单详情'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/monthPlanOrder',
                name: 'searchOrderMonthPlanOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/monthPlanOrder/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗月供订单'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/monthPlanTwoOrder',
                name: 'searchOrderMonthPlanTwoOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/monthPlanTwoOrder/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗月供多供方订单'
                }
            },

            {
                path: '/supplierSys/order/searchOrder/dzMonthTowIndex',
                name: 'dzMonthTowIndex',
                component: () => import('@/pages/shopManage/order/searchOrder/dzMonth/towIndex.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗月供多供方发货单'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/monthPlan',
                name: 'searchOrderMonthPlan',
                component: () => import('@/pages/shopManage/order/searchOrder/monthPlanOrder/plan/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗月供应计划'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/secondLevelMonthPlan',
                name: 'secondLevelMonthPlan',
                component: () => import('@/pages/shopManage/order/searchOrder/monthPlanOrder/plan/secondLevel/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级订单-大宗月供应计划'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/monthPlanDetail',
                name: 'searchOrderMonthPlanDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/monthPlanOrder/plan/detail'),
                meta: {
                    title: '大宗月供计划详情'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/secondLevelMonthPlanDetail',
                name: 'secondLevelMonthPlanDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/monthPlanOrder/plan/secondLevel/detail'),
                meta: {
                    title: '二级订单-大宗月供计划详情'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/contractPlanTwoOrderDetail',
                name: 'searchContractPlanTwoOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/contractPlanTwoOrder/detail'),
                meta: {
                    title: '订单详情'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/monthPlanShipOrder',
                name: 'searchOrderMonthPlanShipOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/monthplanShipOrder/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗发货单'
                }
            },
            {
                path: '/supplierSys/order/monthplanShipOrder/searchOrderDetail',
                name: 'monthplanShipOrderOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/monthplanShipOrder/detail'),
                meta: {
                    title: '月供计划大宗发货单详情'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/monthPlanOrderDetail',
                name: 'searchOrderMonthPlanOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/monthPlanOrder/detail.vue'),
                meta: {
                    title: '订单详情'
                }
            },
            {
                path: '/supplierSys/order/searchOrder/monthPlanTwoOrderDetail',
                name: 'searchOrderMonthPlanTwoOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/monthPlanTwoOrder/detail.vue'),
                meta: {
                    title: '订单详情'
                }
            },
            // {
            //     path: '/supplierSys/order/searchOrder/twoOrder/okDetail',
            //     name: 'okDetail',
            //     component: () => import('@/pages/shopManage/order/searchOrder/twoOrder/okDetail.vue'),
            //     meta: {
            //         title: '已确认订单详情'
            //     }
            // },
            {
                path: '/supplierSys/order/searchOrder/blockOrder/okDetail',
                name: 'blockOrderOkDetail',
                component: () => import('@/pages/shopManage/order/blockOrder/twoOrder/okDetail.vue'),
                meta: {
                    title: '已确认订单详情'
                }
            },
            // {
            //     path: '/supplierSys/order/searchOrder/turnoverMaterials/okDetail',
            //     name: 'turnoverMaterialsOkDetail',
            //     component: () => import('@/pages/shopManage/order/turnoverMaterials/twoOrder/okDetail.vue'),
            //     meta: {
            //         title: '二级订单-已确认周转材料订单详情'
            //     }
            // },
            {
                path: '/supplierSys/order/searchOrder/twoOrder/okIndex/:type',
                name: 'searchOrderTwoOrderOkIndex',
                component: () => import('@/pages/shopManage/order/searchOrder/twoOrder/okIndex'),
                // meta: {
                //     keepAlive: true,
                //     title: '已确认的零星采购订单'
                // }
                meta: {
                    title: route => {
                        const type = route.params.type
                        switch (type) {
                        case '0':
                            return '已确认的零星采购订单'
                        case '1':
                            return '已确认的大宗临购订单'
                        case '2':
                            return '已确认的周转材料订单'
                        default:
                            return ''
                        }
                    },
                },
            },
            // {
            //     path: '/supplierSys/order/blockOrder/twoOrder/okIndex',
            //     name: 'blockOrderTwoOrderOkIndex',
            //     component: () => import('@/pages/shopManage/order/blockOrder/twoOrder/okIndex'),
            //     meta: {
            //         keepAlive: true,
            //         title: '已确认的大宗临购订单'
            //     }
            // },
            // {
            //     path: '/supplierSys/order/turnoverMaterials/twoOrder/okIndex',
            //     name: 'turnoverMaterialsTwoOrderOkIndex',
            //     component: () => import('@/pages/shopManage/order/turnoverMaterials/twoOrder/okIndex'),
            //     meta: {
            //         keepAlive: true,
            //         title: '已确认的周转材料订单'
            //     }
            // },
            {
                //二级零星
                path: '/supplierSys/order/searchOrder/twoOrder/shiped',
                name: 'searchOrderTwoOrderShiped',
                component: () => import('@/pages/shopManage/order/searchOrder/twoOrder/shiped.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级订单管理-零星采购发货单'
                }
            },
            {
                //二级零星发货单详情
                path: '/supplierSys/order/searchOrder/twoOrder/searchOrderDetail',
                name: 'twoSearchOrderTwoOrderShipped',
                component: () => import('@/pages/shopManage/order/searchOrder/twoOrder/searchOrderDetail.vue'),
            },

            {
                //二级大宗
                path: '/supplierSys/order/searchOrder/contractPlanTwoOKOrder/shipped',
                name: 'searchOrderContractPlanTwoOKOrderShipped',
                component: () => import('@/pages/shopManage/order/searchOrder/contractPlanTwoOKOrder/shipped.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗月供发货单'
                }
            },
            {
                //二级供应商
                path: '/supplierSys/order/blockOrder/twoOrder/shipped',
                name: 'blockOrderTwoOrderShiped',
                component: () => import('@/pages/shopManage/order/blockOrder/twoOrder/shipped.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗临购发货单'
                }
            },
            {
                //二级供应商
                path: '/supplierSys/order/turnoverMaterials/twoOrder/shipped',
                name: 'turnoverMaterialsTwoOrderShiped',
                component: () => import('@/pages/shopManage/order/turnoverMaterials/twoOrder/shipped.vue'),
                meta: {
                    keepAlive: true,
                    title: '周转材料发货单'
                }
            },
            {
                //物资公司
                path: '/supplierSys/order/searchOrder/twoOrder/twoShip',
                name: 'searchOrderTwoShiped',
                component: () => import('@/pages/shopManage/order/searchOrder/twoOrder/twoShip.vue'),
                meta: {
                    keepAlive: true,
                    title: '零星采购发货单'
                }
            },
            {
                //大宗临购二级订单详情
                path: '/supplierSys/order/searchOrder/twoOrderDetail',
                name: 'twoOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/twoOrder/detail'),
                meta: {
                    title: '订单详情'
                }
            },
            {
                path: '/supplierSys/order/smallShipTwo/searchOrderDetail',
                name: 'smallShipShopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/smallShip/detail'),
                meta: {
                    title: '零星采购订单详情'
                }
            },

            {
                path: '/supplierSys/order/blockOrder/smallShipTwo/blockOrderDetail',
                name: 'blockOrderShopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/blockOrder/smallShipTwo/detail'),
                meta: {
                    title: '大宗多供方临购订单详情'
                }
            },
            {
                path: '/supplierSys/order/turnoverMaterials/smallShipTwo/turnoverMaterialsDetail',
                name: 'turnoverMaterialsShopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/turnoverMaterials/smallShipTwo/detail'),
                meta: {
                    title: '周转材料多供方订单详情'
                }
            },
            {
                path: '/supplierSys/order/shipedDtl/searchOrderDetail',
                name: 'shipedDtlShopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/shiped/shipdtl.vue'),
                meta: {
                    title: '零星采购发货单项详情'
                }
            },
            //供应商订单
            {
                path: '/supplierSys/order/shiped/searchOrder',
                name: 'shipedSearchOrder',
                component: () => import('@/pages/shopManage/order/searchOrder/shiped/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '零星采购发货单'
                }
            },

            {
                //供应商订单
                path: '/supplierSys/order/blockOrder/shipped',
                name: 'blockOrderShipped',
                component: () => import('@/pages/shopManage/order/blockOrder/shipped/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级大宗临购发货单'
                }
            },

            {
                path: '/supplierSys/order/shiped/searchOrderDetail',
                name: 'shipedShopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/shiped/detail'),
                meta: {
                    title: '零星采购发货单项详情'
                }
            },
            {
                path: '/supplierSys/order/shipped/blockOrderDetail',
                name: 'shippedShopManageBlockOrderDetail',
                component: () => import('@/pages/shopManage/order/blockOrder/shipped/detail'),
                meta: {
                    title: '订单管理-大宗临购发货单项详情'
                }
            },
            {
                path: '/supplierSys/order/shipped/turnoverMaterialsDetail',
                name: 'shippedShopManageTurnoverMaterialsDetail',
                component: () => import('@/pages/shopManage/order/turnoverMaterials/shipped/detail'),
                meta: {
                    title: '订单管理-周转材料发货单项详情'
                }
            },
            {
                path: '/supplierSys/order/shipped/twoBlockOrderDetail',
                name: 'twoBlockOrderDetail',
                component: () => import('@/pages/shopManage/order/blockOrder/shipped/twoShip/detail'),
                meta: {
                    title: '二级订单管理-大宗临购发货单项详情'
                }
            },
            {
                path: '/supplierSys/order/shipped/twoTurnoverMaterialsDetail',
                name: 'twoTurnoverMaterialsDetail',
                component: () => import('@/pages/shopManage/order/turnoverMaterials/shipped/twoShip/detail'),
                meta: {
                    title: '二级订单管理-周转材料发货单项详情'
                }
            },
            {
                path: '/supplierSys/order/shiped/twoOKOrderShippedDetail',
                name: 'twoOKOrderShippedDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/contractPlanTwoOKOrder/shippedDetail.vue'),
                meta: {
                    title: '大宗月供发货单项详情'
                }
            },
            {
                //物资公司查询所有发货单闲情
                path: '/supplierSys/order/shiped/oneSearchOrderDetail',
                name: 'shipedShopManageOneSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/searchOrder/shiped/oneDetail.vue'),
                meta: {
                    title: '大宗月供多供方发货单项详情'
                }
            },

            {
                //二级供应商
                path: '/supplierSys/blockOrder/shipped/searchOrderDetail',
                name: 'blockOrderTwoShopManageSearchOrderDetail',
                component: () => import('@/pages/shopManage/order/blockOrder/shipped/detail'),
                meta: {
                    title: '大宗临购发货单项详情'
                }
            },
            {
                path: '/supplierSys/mail/outBoxDetail',
                name: 'outBoxDetail',
                component: () => import('@/pages/shopManage/mail/outBox/detail'),
                meta: {
                    title: '收件箱详情'
                }
            },      {
                path: '/supplierSys/product/inBox',
                component: () => import('@/pages/shopManage/mail/inBox/index'),
                meta: {
                    keepAlive: true,
                    title: '收件箱'
                }
            },
            {
                path: '/supplierSys/mail/outBox',
                component: () => import('@/pages/shopManage/mail/outBox/index'),
                meta: {
                    keepAlive: true,
                    title: '发件箱'
                }
            },
            {
                path: '/supplierSys/comment/commentManage',
                component: () => import('@/pages/shopManage/content/commentManege/index'),
                meta: {
                    keepAlive: true,
                    title: '评价管理'
                }
            },
            {
                path: '/supplierSys/comment/supplierAggregateComment',
                component: () => import('@/pages/shopManage/content/supplierComment/index'),
                meta: {
                    keepAlive: true,
                    title: '供应商汇总评价'
                }
            },
            {
                path: '/supplierSys/comment/supplierCommentDetail',
                name: 'supplierCommentDetail',
                component: () => import('@/pages/shopManage/content/supplierComment/detail'),
                meta: {
                    title: '评价详情'
                }
            },
            {
                path: '/supplierSys/product/materialManage',
                component: () => import('@/pages/shopManage/product/materialManage/index'),
                meta: {
                    keepAlive: true,
                    title: '出售中商品'
                }
            },
            {
                path: '/supplierSys/synthesizeTemporary/noAffirm',
                name: 'noAffirm',
                component: () => import('@/pages/supplierSys/synthesizeTemporary/noAffirm/index'),
                meta: {
                    keepAlive: true,
                    title: '待确认的大宗临购清单'
                }
            },
            {
                path: '/supplierSys/synthesizeTemporary/noAffirmDetail',
                name: 'noAffirmDetail',
                component: () => import('@/pages/supplierSys/synthesizeTemporary/noAffirm/detail'),
                meta: {
                    title: '大宗临购清单明细'
                }
            },
            {
                path: '/supplierSys/synthesizeTemporary/audit',
                name: 'synthesizeTemporaryAudit',
                component: () => import('@/pages/supplierSys/synthesizeTemporary/audit/index'),
                meta: {
                    keepAlive: true,
                    title: '审核大宗临购清单'
                }
            },
            {
                path: '/supplierSys/synthesizeTemporary/auditDetail',
                name: 'synthesizeTemporaryAuditDetail',
                component: () => import('@/pages/supplierSys/synthesizeTemporary/audit/detail'),
                meta: {
                    title: '大宗临购清单明细'
                }
            },
            {
                path: '/supplierSys/synthesizeTemporary/yesAffirm',
                name: 'yesAffirm',
                component: () => import('@/pages/supplierSys/synthesizeTemporary/yesAffirm/index'),
                meta: {
                    keepAlive: true,
                    title: '已确认的大宗临购清单'
                }
            },
            {
                path: '/supplierSys/synthesizeTemporary/yesAffirmDetail',
                name: 'yesAffirmDetail',
                component: () => import('@/pages/supplierSys/synthesizeTemporary/yesAffirm/detail'),
                meta: {
                    title: '大宗临购清单明细'
                }
            },
            // 临购开始
            {
                path: '/supplierSys/product/lcMaterialSupplierNotAffirm',
                name: 'lcMaterialSupplierNotAffirm',
                component: () => import('@/pages/shopManage/lcProduct/materialSupplierNotAffirm'),
                meta: {
                    keepAlive: true,
                    title: '未确认的临购商品'
                }
            },
            // 供应商
            {
                path: '/supplierSys/product/lcMaterialSupplierNotSubmit',
                component: () => import('@/pages/shopManage/lcProduct/materialSupplierNotSubmit/index'),
                meta: {
                    keepAlive: true,
                    title: '待提交的临购商品'
                }
            },
            // {
            //     path: '/supplierSys/product/lcMaterialWarehouseSupplierDetail',
            //     name: 'lcMaterialWarehouseSupplierDetail',
            //     component: () => import('@/pages/shopManage/lcProduct/materialSupplierNotSubmit/detail'),
            //     meta: {
            //         title: '临购物资详情'
            //     }
            // },
            {
                path: '/supplierSys/product/lcMaterialWarehouseSupplierDetail',
                name: 'lcMaterialWarehouseSupplierDetail',
                component: () => import('@/pages/shopManage/lcProduct/materialSupplierNotSubmit/detail'),
                meta: {
                    title: '临购物资详情'
                }
            },
            {
                path: '/supplierSys/product/lcMaterialManage',
                component: () => import('@/pages/shopManage/lcProduct/materialManage/index'),
                meta: {
                    keepAlive: true,
                    title: '出售中临购商品'
                }
            },
            {
                path: '/supplierSys/product/lcMaterialManageDetail',
                name: 'lcMaterialDetail',
                component: () => import('@/pages/shopManage/lcProduct/materialManage/detail'),
                meta: {
                    title: '临购物资详情'
                }
            },
            {
                path: '/supplierSys/product/lcMaterialWarehouse',
                component: () => import('@/pages/shopManage/lcProduct/materialWarehouse/index'),
                meta: {
                    keepAlive: true,
                    title: '大宗临购商品'
                }
            },
            {
                path: '/supplierSys/product/lcMaterialWarehouseDetail',
                name: 'lcMaterialWarehouseDetail',
                component: () => import('@/pages/shopManage/lcProduct/materialWarehouse/detail'),
                meta: {
                    title: '物资详情'
                }
            },
            {
                path: '/supplierSys/product/lcMaterialCheck',
                component: () => import('@/pages/shopManage/lcProduct/materialCheck/index'),
                meta: {
                    keepAlive: true,
                    title: '审核的临购商品'
                }
            },
            {
                path: '/supplierSys/product/lcMaterialCheckDetail',
                name: 'lcMaterialCheckDetail',
                component: () => import('@/pages/shopManage/lcProduct/materialCheck/detail'),
                meta: {
                    title: '审核物资详情'
                }
            },
            {
                path: '/supplierSys/product/lcMaterialSupplierAffirm',
                name: 'lcMaterialSupplierAffirm',
                component: () => import('@/pages/shopManage/lcProduct/materialSupplierAffirm'),
                meta: {
                    keepAlive: true,
                    title: '待确认的大宗临购商品'
                }
            },
            {
                path: '/supplierSys/product/oneClickStockUp',
                name: 'oneClickStockUp',
                component: () => import('@/pages/shopManage/lcProduct/materialOnClickStockUp'),
                meta: {
                    keepAlive: true,
                    title: '一键铺货'
                }
            },
            {
                path: '/supplierSys/product/lcMaterialSupplierAffirmDetail',
                name: 'lcMaterialSupplierAffirmDetail',
                component: () => import('@/pages/shopManage/lcProduct/materialSupplierAffirm/detail.vue'),
                meta: {
                    title: '临购商品详情'
                }
            },
            // 商品
            {
                path: '/supplierSys/product/lcMaterialSupplierYesAffirm',
                name: 'lcMaterialSupplierYesAffirm',
                component: () => import('@/pages/shopManage/lcProduct/materialSupplierYesAffirm'),
                meta: {
                    keepAlive: true,
                    title: '已确认商品'
                }
            },
            {
                path: '/supplierSys/product/materialSupplierNotAffirm',
                name: 'lcMaterialSupplierNotAffirm',
                component: () => import('@/pages/shopManage/lcProduct/materialSupplierNotAffirm'),
                meta: {
                    keepAlive: true,
                    title: '未确认的商品'
                }
            },
            // {
            //     path: '/supplierSys/product/materialSupplierAffirm',
            //     name: 'lcMaterialSupplierAffirm',
            //     component: () => import('@/pages/shopManage/lcProduct/materialSupplierAffirm'),
            //     meta: {
            //         keepAlive: true,
            //         title: '待确认商品'
            //     }
            // },
            // 临购 结束
            {
                path: '/supplierSys/product/materialManageDetail',
                name: 'materialDetail',
                component: () => import('@/pages/shopManage/product/materialManage/detail'),
                meta: {
                    title: '物资详情'
                }
            },
            {
                path: '/supplierSys/product/materialCheck',
                component: () => import('@/pages/shopManage/product/materialCheck/index'),
                meta: {
                    keepAlive: true,
                    title: '审核的商品'
                }
            },
            {
                path: '/supplierSys/product/materialCheckDetail',
                name: 'materialCheckDetail',
                component: () => import('@/pages/shopManage/product/materialCheck/detail'),
                meta: {
                    title: '审核物资详情'
                }
            },
            /**
             * 供应商商品
             */
            {
                path: '/supplierSys/product/materialSupplierNotSubmit',
                component: () => import('@/pages/shopManage/product/materialSupplierNotSubmit/index'),
                meta: {
                    keepAlive: true,
                    title: '待提交的商品'
                }
            },
            {
                path: '/supplierSys/product/materialWarehouseSupplierDetail',
                name: 'materialWarehouseSupplierDetail',
                component: () => import('@/pages/shopManage/product/materialSupplierNotSubmit/detail'),
                meta: {
                    title: '物资详情'
                }
            },
            {
                path: '/supplierSys/product/materialWarehouse',
                component: () => import('@/pages/shopManage/product/materialWarehouse/index'),
                meta: {
                    keepAlive: true,
                    title: '零星采购商品'
                }
            },
            {
                path: '/supplierSys/product/materialWarehouseDetail',
                name: 'materialWarehouseDetail',
                component: () => import('@/pages/shopManage/product/materialWarehouse/detail'),
                meta: {
                    title: '物资详情'
                }
            },
            {
                //不可更改
                path: '/supplierSys/product/materialWarehouseDisDetail',
                name: 'materialWarehouseDisDetail',
                component: () => import('@/pages/shopManage/product/materialWarehouse/disDetail.vue'),
                meta: {
                    title: '物资详情'
                }
            },
            {
                path: '/supplierSys/product/allProducts',
                component: () => import('@/pages/shopManage/product/allProducts/index'),
                meta: {
                    title: '店铺商品管理'
                }
            },
            {
                path: '/supplierSys/order/paidOrder',
                component: () => import('@/pages/shopManage/order/paidOrder/index'),
                meta: {
                    title: '已支付订单'
                }
            },
            {
                path: '/supplierSys/order/finishedOrder',
                component: () => import('@/pages/shopManage/order/finishedOrder/index'),
                meta: {
                    title: '结算订单'
                }
            },

            {
                path: '/supplierSys/order/pendingOrder',
                component: () => import('@/pages/shopManage/order/pendingOrder/index'),
                meta: {
                    title: '待支付订单'
                }
            },
            {
                path: '/supplierSys/returnGoods/invoice',
                component: () => import('@/pages/shopManage/returnGoods/apply/index'),
                meta: {
                    title: '零星采购退货申请'
                }
            },
            {
                path: '/supplierSys/returnGoods/invoice/blockIndex',
                component: () => import('@/pages/shopManage/returnGoods/apply/blockIndex.vue'),
                meta: {
                    title: '退货管理-大宗临购退货申请'
                }
            },
            {
                path: '/supplierSys/returnGoods/twoApply',
                component: () => import('@/pages/shopManage/returnGoods/apply/two/index'),
                meta: {
                    title: '零星采购退货申请'
                }
            },
            {
                path: '/supplierSys/returnGoods/twoApply/blockIndex',
                component: () => import('@/pages/shopManage/returnGoods/apply/two/blockIndex.vue'),
                meta: {
                    title: '大宗临购退货申请'
                }
            },
            {
                path: '/supplierSys/returnGoods/applyDetail',
                name: 'applyDetail',
                component: () => import('@/pages/shopManage/returnGoods/apply/detail.vue'),
                meta: {
                    title: '退货管理-退货申请'
                }
            },
            {
                path: '/supplierSys/returnGoods/applyDzDetail',
                name: 'applyDzDetail',
                component: () => import('@/pages/shopManage/returnGoods/apply/dzDetail.vue'),
                meta: {
                    title: '退货管理-大宗退货详情'
                }
            },
            {
                path: '/supplierSys/returnGoods/recodDetail',
                name: 'recodDetail',
                component: () => import('@/pages/shopManage/returnGoods/apply/recodDetail.vue'),
                meta: {
                    title: '退货管理-退货记录'
                }
            },

            {
                path: '/supplierSys/returnGoods/duoGongFangApplyDetail',
                name: 'duoGongFangApplyDetail',
                component: () => import('@/pages/shopManage/returnGoods/duogongfang/detail.vue'),
                meta: {
                    title: '多供方退货申请详情'
                }
            },
            {
                path: '/supplierSys/returnGoods/applyTwoDetail',
                name: 'applyTwoDetail',
                component: () => import('@/pages/shopManage/returnGoods/apply/two/dzDetail.vue'),
                meta: {
                    title: '二级退货-大宗临购退货申请'
                }
            },
            {
                path: '/supplierSys/returnGoods/applyTwoDzDetail',
                name: 'applyTwoDzDetail',
                component: () => import('@/pages/shopManage/returnGoods/apply/two/detail.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级退货-零星退货申请'
                }
            },
            {
                path: '/supplierSys/returnGoods/recordTwoDetail',
                name: 'recordTwoDetail',
                component: () => import('@/pages/shopManage/returnGoods/apply/two/recordDetail.vue'),
                meta: {
                    keepAlive: true,
                    title: '二级退货-大宗临购退货申请记录'
                }
            },

            {
                path: '/supplierSys/returnGoods/bulkDetail',
                name: 'dazongDetail',
                component: () => import('@/pages/shopManage/returnGoods/dazong/apply/detail.vue'),
                meta: {
                    title: '大宗退货申请详情'
                }
            },
            {
                path: '/supplierSys/returnGoods/bulkRecordrecord',
                component: () => import('@/pages/shopManage/returnGoods/dazong/record/index'),
                meta: {
                    keepAlive: true,
                    title: '大宗商品退货记录'
                }
            },
            {
                path: '/supplierSys/returnGoods/dazongDetail',
                name: 'dazongDetail',
                component: () => import('@/pages/shopManage/returnGoods/dazong/apply/index.vue'),
                meta: {
                    title: '大宗退货申请'
                }
            },
            {
                path: '/supplierSys/returnGoods/bulkRecordDetail',
                name: 'bulkRecordDetail',
                component: () => import('@/pages/shopManage/returnGoods/dazong/record/detail.vue'),
                meta: {
                    title: '大宗退货申请详情'
                }
            },
            {
                path: '/supplierSys/returnGoods/twoApplyDetail',
                name: 'twoApplyDetail',
                component: () => import('@/pages/shopManage/returnGoods/apply/two/detail.vue'),
                meta: {
                    title: '二级退货-退货申请详情'
                }
            },
            {
                path: '/supplierSys/returnGoods/record',
                component: () => import('@/pages/shopManage/returnGoods/record/index'),
                meta: {
                    keepAlive: true,
                    title: '零星采购退货记录'
                }
            },
            {
                path: '/supplierSys/returnGoods/dzMonthRecord',
                component: () => import('@/pages/shopManage/returnGoods/record/dzMonthIndex.vue'),
                meta: {
                    keepAlive: true,
                    title: '订单管理-大宗月供退货记录'
                }
            },
            {
                path: '/supplierSys/returnGoods/record/two/block',
                component: () => import('@/pages/shopManage/returnGoods/record/block/index'),
                meta: {
                    keepAlive: true,
                    title: '退货管理-临购二级退货记录'
                }
            },
            {
                path: '/supplierSys/returnGoods/record/block',
                component: () => import('@/pages/shopManage/returnGoods/record/dzLinindex.vue'),
                meta: {
                    keepAlive: true,
                    title: '退货管理-临购退货记录'
                }
            },
            {
                path: '/supplierSys/returnGoods/zzDRecord',
                component: () => import('@/pages/shopManage/returnGoods/revolving-materials/zzDindex.vue'),
                meta: {
                    keepAlive: true,
                    title: '退货管理-周转多供退货记录'
                }
            },
            {
                path: '/supplierSys/returnGoods/zzRecord',
                component: () => import('@/pages/shopManage/returnGoods/revolving-materials/zzindex.vue'),
                meta: {
                    keepAlive: true,
                    title: '退货管理-周转退货记录'
                }
            },
            {
                path: '/supplierSys/returnGoods/twoRecord',
                component: () => import('@/pages/shopManage/returnGoods/record/two/index'),
                meta: {
                    keepAlive: true,
                    title: '零星采购退货记录'
                }
            },
            {
                path: '/supplierSys/returnGoods/block/twoRecord',
                component: () => import('@/pages/shopManage/returnGoods/record/block/two/index'),
                meta: {
                    keepAlive: true,
                    title: '大宗临购退货记录'
                }
            },
            {
                path: '/supplierSys/refundDetail',
                name: 'refundDetail',
                component: () => import('@/pages/shopManage/returnGoods/record/detail.vue'),
                meta: {
                    title: '物资商铺管理-退货详情'
                }
            },
            {
                path: '/supplierSys/invoice/invoice',
                component: () => import('@/pages/shopManage/invoice/record/index'),
                meta: {
                    keepAlive: true,
                    title: '我的发票记录'
                }
            },
            {
                path: '/supplierSys/invoice/twoIndex',
                component: () => import('@/pages/shopManage/invoice/record/twoIndex.vue'),
                meta: {
                    keepAlive: true,
                    title: '我的二级发票记录'
                }
            },

            {
                path: '/supplierSys/supplierApply/invoice/apply',
                name: 'supplierSysApply',
                component: () => import('@/pages/shopManage/invoice/supplierApply/invoice/apply/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '供应商开票'
                }
            },
            {
                path: '/supplierSys/twoSupplierApply/apply',
                name: 'twoEnterpriseApply',
                component: () => import('@/pages/shopManage/invoice/twoSupplierApply/apply.vue'),
                meta: {
                    title: '二级对账单-自营店申请发票'
                }
            },
            {
                path: '/supplierSys/twoSupplierApply/twoSupplierApply',
                name: 'twoSupplierApply',
                component: () => import('@/pages/shopManage/invoice/twoSupplierApply/twoSupplierApply.vue'),
                meta: {
                    title: '二级对账单-二级供应商开发票'
                }
            },

            {
                path: '/supplierApply/invoiceRiseApply',
                name: 'invoiceRiseApply',
                component: () => import('@/pages/shopManage/invoice/supplierApply/invoiceRise/apply.vue'),
                meta: {
                    title: '供应商-发票抬头新增'
                }
            },
            {
                path: '/supplierApply/invoiceRise',
                component: () => import('@/pages/shopManage/invoice/supplierApply/invoiceRise/index.vue'),
                meta: {
                    title: '供应商-发票抬头'
                }
            },
            // {
            //     path: '/supplierApply/invoiceRiseDetail',
            //     component: () => import('@/pages/shopManage/invoice/supplierApply/invoiceRise/detail.vue'),
            //     meta: {
            //         title: '供应商-发票抬头详情'
            //     }
            // },

            {
                path: '/supplierSys/invoice/recordDetail',
                name: 'recordDetail',
                component: () => import('@/pages/shopManage/invoice/record/detail.vue'),
                meta: {
                    title: '发票记录详情'
                }
            },
            {
                path: '/supplierSys/analysis/order',
                component: () => import('@/pages/shopManage/analysis/order/index'),
                meta: {
                    title: '店铺订单统计'
                }
            },
            {
                path: '/supplierSys/analysis/orderItem',
                component: () => import('@/pages/shopManage/analysis/order/orderItem.vue'),
                meta: {
                    title: '店铺订单项统计'
                }
            },
            {
                path: '/supplierSys/analysis/product',
                component: () => import('@/pages/shopManage/analysis/product/index'),
                meta: {
                    title: '商品统计'
                }
            },
            {
                path: '/supplierSys/analysis/productReportForms',
                component: () => import('@/pages/shopManage/analysis/reportForms/index'),
                meta: {
                    title: '上架商品报表'
                }
            },
            {
                path: '/supplierSys/analysis/shopreport',
                component: () => import('@/pages/shopManage/analysis/shopreport/index'),
                meta: {
                    title: '零星采购交易量报表'
                }
            },
            {
                path: '/supplierSys/analysis/tradingVolumeReport',
                component: () => import('@/pages/supplierSys/analysis/tradingVolumeReport/index'),
                meta: {
                    title: '商品交易量报表'
                }
            },
            {
                path: '/supplierSys/analysis/orderStatement',
                component: () => import('@/pages/shopManage/analysis/orderStatement/index'),
                meta: {
                    title: '报表管理-店铺结算统计'
                }
            },
            {
                path: '/supplierSys/analysis/productSheet',
                component: () => import('@/pages/supplierSys/analysis/productSheet/index'),
                meta: {
                    title: '物资结算报表'
                }
            },
            {
                path: '/supplierSys/analysis/operationStatistics',
                component: () => import('@/pages/supplierSys/analysis/operationStatistics/index'),
                meta: {
                    title: '报表管理-运营统计报表'
                }
            },
            {
                path: '/supplierSys/analysis/reconciliationStatistics',
                component: () => import('@/pages/supplierSys/analysis/reconciliationStatistics/index'),
                meta: {
                    title: '报表管理-物资对账统计台账'
                }
            },
            {
                path: '/supplierSys/analysis/inventoryManagement',
                component: () => import('@/pages/supplierSys/stockManage/inventoryManage/index'),
                meta: {
                    title: '库存管理-仓库管理'
                }
            },
            {
                path: '/supplierSys/analysis/stockDetail',
                name: 'stockDetail',
                component: () => import('@/pages/supplierSys/stockManage/inventoryManage/detail'),
                meta: {
                    title: '库存管理-仓库管理'
                }
            },
            {
                path: '/supplierSys/analysis/inOnBoundRecord',
                component: () => import('@/pages/supplierSys/stockManage/inOnBoundRecord/index'),
                meta: {
                    title: '库存管理-出入库记录'
                }
            },
            {
                path: '/supplierSys/analysis/inOnBoundRecordDetail',
                name: 'inOnBoundRecordDetail',
                component: () => import('@/pages/supplierSys/stockManage/inOnBoundRecord/detail.vue'),
                meta: {
                    title: '库存管理-出入库记录'
                }
            },
            {
                path: '/supplierSys/analysis/inboundManagement',
                component: () => import('@/pages/supplierSys/stockManage/inBoundManage/index'),
                meta: {
                    title: '库存管理-入库管理'
                }
            },
            {
                path: '/supplierSys/analysis/inBoundDetail',
                name: 'inBoundDetail',
                component: () => import('@/pages/supplierSys/stockManage/inBoundManage/detail'),
                meta: {
                    title: '库存管理-入库管理'
                }
            },
            {
                path: '/supplierSys/analysis/onBoundManagement',
                component: () => import('@/pages/supplierSys/stockManage/onBoundManage/index'),
                meta: {
                    title: '库存管理-出库管理'
                }
            },
            {
                path: '/supplierSys/analysis/onBoundDetail',
                name: 'onBoundDetail',
                component: () => import('@/pages/supplierSys/stockManage/onBoundManage/detail'),
                meta: {
                    title: '库存管理-出库管理'
                }
            },
            {
                path: '/supplierSys/analysis/orderStatement',
                component: () => import('@/pages/shopManage/analysis/orderStatement/index'),
                meta: {
                    title: '零星采购结算报表'
                }
            },
            // 大宗二级退货
            // 二级供应商 /supplierSys/returnGoods/second/bulk/index
            {
                path: '/supplierSys/returnGoods/second/bulk/index',
                component: () => import('@/pages/shopManage/returnGoods/bulk/secondSupplier/index'),
                meta: {
                    title: '退货管理-大宗月供退货'
                }
            },
            {
                path: '/supplierSys/returnGoods/second/bulk/detail',
                component: () => import('@/pages/shopManage/returnGoods/bulk/secondSupplier/detail.vue'),
                meta: {
                    title: '退货管理-大宗月供退货详情'
                },
                name: 'bulkReturnSecondDetail'
            },
            // 物资公司  /supplierSys/returnGoods/bulk/index
            {
                path: '/supplierSys/returnGoods/bulk/index',
                component: () => import('@/pages/shopManage/returnGoods/bulk/index.vue'),
                meta: {
                    title: '退货管理-大宗商品退货'
                }
            },
            {
                path: '/supplierSys/returnGoods/bulk/detail',
                component: () => import('@/pages/shopManage/returnGoods/bulk/detail.vue'),
                meta: {
                    title: '退货管理-大宗商品详情'
                },
                name: 'bulkReturnDetail'
            },
            {
                path: '/supplierSys/fee/yearPayRecord',
                name: 'supplierSysFeeYearPayRecord',
                component: () => import('@/pages/supplierSys/fee/yearPayRecord/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '年度服务费缴费'
                }
            },
            {
                path: '/supplierSys/fee/yearPayRecordDtl',
                name: 'supplierSysFeeYearPayRecordDtl',
                component: () => import('@/pages/supplierSys/fee/yearPayRecord/detail.vue'),
                meta: {
                    title: '年度服务费缴费详情'
                }
            },
            {
                path: '/supplierSys/fee/payRecordManage',
                name: 'supplierSysFeePayRecordManage',
                component: () => import('@/pages/supplierSys/fee/payRecordManage/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '缴费管理'
                }
            },
            {
                path: '/supplierSys/fee/freeDtlAll',
                name: 'supplierSysFreeDtlAll',
                component: () => import('@/pages/supplierSys/fee/freeDtlAll/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '费用明细'
                }
            },
            {
                path: '/supplierSys/fee/payRecordManageDtl',
                name: 'supplierSysFeePayRecordManageDtl',
                component: () => import('@/pages/supplierSys/fee/payRecordManage/detail.vue'),
                meta: {
                    title: '缴费管理详情'
                }
            },
            {
                path: '/supplierSys/fee/dealPayRecord',
                name: 'supplierSysFeeDealPayRecord',
                component: () => import('@/pages/supplierSys/fee/dealPayRecord/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '交易服务费缴费'
                }
            },
            {
                path: '/supplierSys/fee/dealPayRecordDetail',
                name: 'supplierSysFeeDealPayRecordDtl',
                component: () => import('@/pages/supplierSys/fee/dealPayRecord/detail.vue'),
                meta: {
                    title: '交易服务费结算记录详情'
                }
            },
            {
                path: '/supplierSys/fee/payRecordManage2Dtl',
                name: 'supplierSysFeeDealPayRecord2Dtl',
                component: () => import('@/pages/supplierSys/fee/payRecordManage/dealDetail.vue'),
                meta: {
                    title: '缴费详情'
                }
            },
            {
                path: '/supplierSys/feeDtl/dealPayRecord',
                name: 'supplierSysFeeDtlDeal',
                component: () => import('@/pages/supplierSys/feeDtl/deal/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '服务交易费记录'
                }
            },
            {
                path: '/supplierSys/feeDtl/DealDtl',
                name: 'supplierSysFeeDtlDealDtl',
                component: () => import('@/pages/supplierSys/feeDtl/deal/detail.vue'),
                meta: {
                    title: '服务交易费记录详情'
                }
            },
            {
                path: '/supplierSys/fee/year',
                name: 'supplierSysFeeYear',
                component: () => import('@/pages/supplierSys/fee/year/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '服务年费记录'
                }
            },
            {
                path: '/supplierSys/fee/yearDetail',
                name: 'yearDetail',
                component: () => import('@/pages/supplierSys/fee/year/detail.vue'),
                meta: {
                    title: '服务年费记录详情'
                }
            },
            {
                path: '/supplierSys/fee/yearDetailNo',
                name: 'yearDetailNo',
                component: () => import('@/pages/supplierSys/fee/year/yearDetail.vue'),
                meta: {
                    title: '服务年费记录详情'
                }
            },
            {
                path: '/supplierSys/product/turnMaterialSupplierNotSubmit',
                component: () => import('@/pages/shopManage/turnProduct/materialSupplierNotSubmit/index'),
                meta: {
                    keepAlive: true,
                    title: '待提交的周材商品'
                }
            },
            {
                path: '/supplierSys/product/turnMaterialWarehouseSupplierDetail',
                name: 'turnMaterialWarehouseSupplierDetail',
                component: () => import('@/pages/shopManage/turnProduct/materialSupplierNotSubmit/detail'),
                meta: {
                    title: '周材物资详情'
                }
            },
            {
                path: '/supplierSys/product/turnMaterialWarehouse',
                component: () => import('@/pages/shopManage/turnProduct/materialWarehouse/index'),
                meta: {
                    keepAlive: true,
                    title: '周转材料商品'
                }
            },
            {
                path: '/supplierSys/product/turnMaterialWarehouseDetail',
                name: 'turnMaterialWarehouseDetail',
                component: () => import('@/pages/shopManage/turnProduct/materialWarehouse/detail'),
                meta: {
                    title: '周材物资详情'
                }
            },
            {
                path: '/supplierSys/product/turnMaterialSupplierAffirm',
                name: 'turnMaterialSupplierAffirm',
                component: () => import('@/pages/shopManage/turnProduct/materialSupplierAffirm'),
                meta: {
                    keepAlive: true,
                    title: '待确认的周转材料商品'
                }
            },
            {
                path: '/supplierSys/product/turnMaterialSupplierAffirmDetail',
                name: 'turnMaterialSupplierAffirmDetail',
                component: () => import('@/pages/shopManage/turnProduct/materialSupplierAffirm/detail.vue'),
                meta: {
                    title: '周材商品详情'
                }
            },
        ]
    },
]
