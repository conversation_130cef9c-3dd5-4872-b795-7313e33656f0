/**
页面中要引用  document.body.clientHeight - 240(冯伟的分页组件计算)
不引用 document.body.clientHeight - 170
 */
<template>
    <div class="step top" style="background: #fff;">
        <ul>
            <li v-for="(item, index) in steps" :key="index">
                <el-tooltip class="item" :content="item.description" placement="bottom">
                    <span  @click="clickLink(item.http)"  :class="{hoverColor:item.http === fullPath}">
                        {{item.description}}
                    </span>
                </el-tooltip>
                <span class="el-icon-minus" v-if="steps[1].description != null && steps.length-1 !== index"></span>
                <span class="el-icon-right" v-if="steps[1].description != null && steps.length-1 !== index"></span>
                <!--<span class="el-icon-minus" v-if="steps.length-1 != index"></span>-->
                <!--<span class="el-icon-right" v-if="steps.length-1 != index"></span>-->
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    created () {
        this.getLinkAddr()
    },
    data () {
        return{
            steps: [],
        }
    },
    props: {
        stepInfo: {
            type: Array,
            default: function () {
                return []
            }
        }
    },
    watch: {
        stepInfo: {
            handler (newName) {
                this.steps = [].concat(newName)
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        clickLink (e) {
            if(e) this.$router.push({ path: e, })
        },
        getLinkAddr () {
            this.fullPath = this.$route.fullPath
        }
    },
}

</script>

<style lang="scss" scoped>
.step{
    width: 97.5%;
    min-height: 50px !important;
    margin: 13px 20px 7px 20px;
    display: flex;
    align-items: center;
}
.step ul{
    display: flex;
    margin-left: 25px;
}
.step ul li span{
    font-size: 16px;
}
.step ul li span:nth-child(1){
    cursor: pointer;
}
.step ul li span:not(:nth-child(1)){
    font-weight: 600;
}
.hoverColor{
    color: red;
}
</style>