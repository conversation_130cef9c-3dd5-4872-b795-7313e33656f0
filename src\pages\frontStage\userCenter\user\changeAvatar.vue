<template>
    <main>
        <el-tabs v-model="activeTab">
            <el-tab-pane label="基本信息" name="first">
                <div class="baseInfo">
                    <div class="center">
                        <el-form :model="baseForm" ref="form" :rules="baseRules" label-position="top" :inline="false">
                            <el-row>
                                <el-form-item label="昵称：" prop="nickName">
                                    <el-input class="nickName" v-model="baseForm.nickName" placeholder="请输入昵称"></el-input>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="性别：" prop="gender">
                                    <el-radio v-model="baseForm.gender" label="1"  @change="radioChange">男</el-radio>
                                    <el-radio v-model="baseForm.gender" label="0"  @change="radioChange">女</el-radio>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <!-- 选择生日 -->
                                <el-form-item label="生日：" prop="birthday">
                                    <!-- <el-date-picker v-model="birthday.year" type="year" placeholder="请选择"></el-date-picker> -->
                                    <el-select v-model="birthday.year" placeholder="请选择">
                                        <el-option v-for="item in 100" :key="item" :label="currentYear - item + 1" :value="currentYear - item + 1">
                                        </el-option>
                                    </el-select>
                                    <span class="unit">年</span>
                                    <el-select v-model="birthday.month" placeholder="请选择">
                                        <el-option v-for="item in 12" :key="item" :label="item" :value="item">
                                        </el-option>
                                    </el-select>
                                    <span class="unit">月</span>
                                    <el-select v-model="birthday.day" placeholder="请选择">
                                        <el-option v-for="item in daysOfCurrentMonth" :key="item" :label="item" :value="item">
                                        </el-option>
                                    </el-select>
                                    <span>日</span>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <!-- 选择居住地 -->
                                <el-form-item label="居住地：">
                                    <el-select v-model="address.province" placeholder="请选择" @change="getSubDistrict(1)">
                                        <el-option v-for="item in cascaderOptions.province" :key="item.districtId" :label="item.districtName"
                                                   :value="item.districtName">
                                        </el-option>
                                    </el-select>
                                    <span class="unit">省</span>
                                    <el-select v-model="address.city" placeholder="请选择" @change="getSubDistrict(2)">
                                        <el-option v-for="item in cascaderOptions.city" :key="item.districtId" :label="item.districtName"
                                                   :value="item.districtName">
                                        </el-option>
                                    </el-select>
                                    <span class="unit">市</span>
                                    <el-select v-model="address.district" placeholder="请选择">
                                        <el-option v-for="item in cascaderOptions.district" :key="item.districtId" :label="item.districtName"
                                                   :value="item.districtName">
                                        </el-option>
                                    </el-select>
                                    <span>区</span>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="详细地址：">
                                    <el-input v-model="baseForm.detailedAddr" placeholder="请输入详细地址"></el-input>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="邮箱：">
                                    <el-input v-model="baseForm.email" placeholder="请输入邮箱" clearable></el-input>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <div class="btn" @click="onSave">保存</div>
                            </el-row>
                        </el-form>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="头像照片" name="second">
                <div class="avatar">
                    <div class="center">
                        <el-upload class="uploadBtn mb10"
                                   action="fakeaction"
                                   :http-request="uploadAvatar"
                                   :before-upload="handleBeforeUpload"
                                   :show-file-list="false">
                            上传头像
                        </el-upload>
                        <div class="notice mb20">仅支持JPG、GIF、PNG、JPEG、BMP格式，文件小于4M</div>
                        <div class="notice mb20">建议图片比例：1：1</div>
                        <div class="avatarBox mb20 dfc">
                            <!--              <img v-if="avatarUrl" :src="imgUrlPrefixAdd + avatarUrl" alt="">-->
                            <!-- addimgurl -->
                            <!--              <img v-else src="@/assets/images/userCenter/avatar.png" alt="">-->
                            <img :src="avatarUrl ? imgUrlPrefixAdd + avatarUrl : require('@/assets/images/userCenter/avatar.png')" alt="">
                        </div>
                        <div class="btn" @click="onSaveImg">保存</div>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </main>
</template>
<script>
import { getCascaderOptions } from '@/api/platform/common/components'
import { uploadFile } from '@/api/platform/common/file'
import { update, getUserData, updateImg } from '@/api/frontStage/changeAvatar'
export default {
    data () {
        return {
            activeTab: 'first',
            avatarUrl: '',
            // 基本信息
            baseForm: {
                nickName: '',
                gender: '',
                birthday: '',
                addr: '',
                detailedAddr: '',
                email: '',
            },
            birthday: {
                year: '',
                month: '',
                day: '',
            },
            address: {
                province: '',
                city: '',
                district: '',
            },
            baseRules: {
                nickName: [
                    { required: true, message: '请填写昵称', trigger: 'blur' },
                ],
                // gender: [
                //     { required: true, message: '请选择性别', trigger: 'blur' },
                // ],
            },
            // 当前年份
            currentYear: new Date().getFullYear(),
            daysOfCurrentMonth: 31,
            cascaderOptions: {
                province: [],
                city: [],
                district: [],
            },
            userId: ''
        }
    },
    watch: {
        'birthday.month': {
            handler (val) {
                if (val === '') return
                let d = new Date(parseInt(this.birthday.year), parseInt(val), 0)
                this.daysOfCurrentMonth = d.getDate()
            }
        }
    },
    created () {
        this.getCascader()
        this.getUserInfo()
    },
    methods: {
        getUserInfo () {
            getUserData().then(res=>{
                this.avatarUrl = res.userImg
                this.baseForm.nickName = res.nickName
                this.baseForm.detailedAddr = res.detailAddress
                this.baseForm.gender = typeof res.gender === 'number' ? res.gender.toString() : res.gender
                this.baseForm.birthday = res.birthday
                this.address.province = res.province
                this.address.city = res.city
                this.address.district = res.county
                if (res.birthday != null) {
                    this.birthday.year = res.birthday.substring(0, 4)
                    this.birthday.month = res.birthday.substring(5, 7)
                    this.birthday.day = res.birthday.substring(8, 10)
                }
                this.userId = res.userId
                this.baseForm.email = res.email
            })
        },

        async getCascader () {

            let res = await getCascaderOptions({ distCode: '100000' })

            this.cascaderOptions.province = res

        },
        // 获取下级区域
        async getSubDistrict (i) {
            this.address.district = ''
            let key, distCode
            if (i === 1) {
                this.address.city = ''
                key = 'city'
                this.cascaderOptions.province.forEach(item => {
                    if (item.districtName === this.address.province) distCode = item.districtCode
                })
            } else {
                key = 'district'
                this.cascaderOptions.city.forEach(item => {
                    if (item.districtName === this.address.city) distCode = item.districtCode
                })
            }
            let res = await getCascaderOptions({ distCode, })
            this.cascaderOptions[key] = res
        },
        radioChange () {
            console.log(this.baseForm.gender)
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            if(file.size / 1024 / 1024 > 4) {
                this.$message.error('上传的图片大小不能超过4MB!')
                return false
            }
            return true
        },
        uploadAvatar (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            uploadFile(form).then(res => {

                this.avatarUrl = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                this.$message({
                    message: '图片上传成功',
                    type: 'success'
                })
            })
        },

        onSave () {
            let rule = 'form'
            this.$refs[rule].validate(valid => {
                console.log('表单校验：', valid)
                if (valid) {
                    let ad = this.address
                    let bi = this.birthday
                    let params = {
                        nickName: this.baseForm.nickName,
                        detailAddress: this.baseForm.detailedAddr,
                        gender: parseInt(this.baseForm.gender),
                        province: `${ad.province}`,
                        city: `${ad.city}`,
                        county: `${ad.district}`,
                        email: this.baseForm.email
                    }
                    if (bi.year !== ''  || bi.month !== ''  || bi.day !== '' ) {
                        params.birthday = `${bi.year}-${bi.month}-${bi.day}` + ' 00:00:01'
                    }
                    update(params).then(res=>{
                        if (res.code == 200) {
                            this.$message({
                                message: '保存成功',
                                type: 'success'
                            })
                        }}
                    )

                }
            })

        },
        onSaveImg () {
            if (this.avatarUrl != null) {
                let params = {
                    userImg: this.avatarUrl,
                }
                updateImg(params).then(res=>{
                    if (res.code == 200) {
                        this.$message({
                            message: '保存成功',
                            type: 'success'
                        })
                    }
                })
            }

        }
    },
}
</script>
<style scoped lang="scss">
main {
  height: 894px;
  border: 1px solid rgba(229,229,229,1);
}

/deep/.el-tabs__nav-scroll {
  height: 60px;
  background-color: #fafafa;

  .el-tabs__nav {
    border: none;

    .el-tabs__active-bar {
      height: 3px;
      background-color: #226FC7;
      top: 0;
      z-index: 2;
    }
  }

  .el-tabs__item.is-top {
    width: 160px;
    height: 60px;
    margin: 0;
    padding: 0;
    font-size: 18px;
    line-height: 60px;
    text-align: center;
    border: none;
    position: relative;
    z-index: 1;

    &.is-active {
      color: #333;
      background-color: #fff;
    }
  }

}

.baseInfo,
.avatar {
  padding-top: 100px;

  &>.center {
    width: 400px;
  }

  /deep/ .nickname.el-input .el-input__inner {
    width: 300px;
  }

  /deep/ .el-input__inner {
    height: 35px;
    border: 1px solid rgba(217, 217, 217, 1);
    border-radius: 0;
  }

  /deep/ .el-radio__inner {
    background-color: transparent;

    &::after {
      width: 9px;
      height: 9px;
      margin: 0 auto;
      background-color: #216EC6;
    }
  }

  /deep/ .el-select {
    width: 100px;
    margin-right: 5px;

    .el-input__inner {
      width: 100px;
      padding: 0 25px 0 10px;
    }

    .el-icon-date {
      display: none;
    }
  }

  .btn {
    width: 80px;
    height: 40px;
    font-size: 16px;
    text-align: center;
    line-height: 40px;
    color: #fff;
    background-color: #216EC6;
    cursor: pointer;
    user-select: none;
  }
}

.baseInfo {
  .unit {
    margin-right: 15px;
  }
}

.avatar {
  .uploadBtn {
    width: 100px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    text-align: center;
    color: #216EC6 !important;
    border: 1px solid rgba(33, 110, 198, 1);
  }

  .notice {
    font-size: 12px;
    color: #808080;
  }

  .avatarBox {
    height: 200px;
    border: 1px solid rgba(230, 230, 230, 1);
    background: #F7F7F7;

    img {
      width: 100px;
      height: 100px;
    }
  }
}
</style>