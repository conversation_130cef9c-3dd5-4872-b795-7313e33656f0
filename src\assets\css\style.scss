*{margin: 0; padding: 0; box-sizing: border-box;}
html{margin: 0; padding: 0;}
html.gray {-webkit-filter: grayscale(.95);}
html,#app {height: 100%;font-family: Neue,Helvetica,PingFang SC,Hiragino Sans GB,Microsoft YaHei,SimSun,sans-serif;}
body {
    min-width: 1200px;
    height: 100%;
    margin: 0;
    padding: 0;
    font-size: 14px;
    background-color: #fff;
    // overflow-y: scroll;
    scrollbar-width: 10px;
}
ul{padding: 0}
li{list-style: none;}
button{user-select: none;cursor: pointer;}
input, button{border: none; outline: none;}
a{color: unset;text-decoration: none;}
::-webkit-scrollbar {width: 10px;height: 10px;}
::-webkit-scrollbar-track {-webkit-border-radius: 10px;border-radius: 10px;background: #fff;}
::-webkit-scrollbar-thumb {border-radius: 10px;-webkit-border-radius: 10px;background: #dddee0;transition: all 0.8s ease-in;}
.el-dialog{padding: 20px;}
.el-dialog__body{overflow: auto;}
.el-table--scrollable-x .el-table__body-wrapper,
.scroll_bar {
    &::-webkit-scrollbar {
        height: 8px;
        z-index: 99;
        position: fixed;
        &:vertical {width: 8px;}
    }
    &:hover {
        &::-webkit-scrollbar-thumb {
            background: rgba(204, 204, 204, 0.6);
            &:hover {background: rgba(204, 204, 204, 1);}
        }
    }
}
.el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar {width: 8px;}

.a {display: block;color: unset;text-decoration: none;cursor: pointer;}

.ws-right {float: right !important;}

.ws-blue {background: #28a9e4;border: 1px solid #28a9e4;color: #fff;}

.no_date {padding: 20px 0; text-align: center;}

.amap-marker:first-child .amap-icon img {width: 25px; height: 34px;}

.el-table--border .el-table__cell,
.el-table td.el-table__cell {border-color: #e0e0e0;}

.table {
    font-size: 14px;
    .cell {position: relative;}
    .btn {
        position: absolute;
        right: 10px;
        top: 0;
        bottom: 0;
        z-index: 2;
        display: none;
        align-items: center;
        .el-button {font-size: 20px;}
    }

    .el-table__row:hover {
        .btn {display: flex;}
    }

    &.el-table--mini,
    &.el-table--small,
    &.el-table__expand-icon {font-size: 14px;}
}
.el-tabs__nav-wrap::after {display: none;}
.el-tabs__header {margin-bottom: 5px !important;}

.el-tree-node.is-current {
    & > .el-tree-node__content {background-color: #eaf2ff;}
}

.el-dialog__body {padding: 10px 20px 0 !important;}

.el-backtop {background: rgba(0, 123, 255, 0.2) !important;}

.el-breadcrumb__item:last-child .el-breadcrumb__inner {color: #fff !important;}

.el-submenu .el-menu-item,
.el-menu-item,
.el-submenu__title {
    height: 35px; // 取消important
    line-height: 35px; // 取消important
}

.el-submenu [class^='el-icon-'] {font-size: 22px !important;}

.el-menu--collapse .el-menu .el-submenu,
.el-menu--popup {min-width: auto !important;}

.el-menu--popup {
    .el-menu-item {color: #333 !important;}
}

.el-breadcrumb__inner.is-link {color: #fff !important;}

.handle-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 0;
    font-size: 12px;
    .handle-input {margin-right: 10px; width: 220px;}
}

.upload_file {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0 10px;
    .el-upload--text {border: none;}
}

.cur_step {
    .el-step__head.is-success {
        .el-step__line {background-color: #67c23a;}
    }
    .el-step__head.is-process {
        color: #409eff;
        border-color: #409eff;
        .el-step__line {background-color: #409eff;}
    }

    .el-step__title.is-process,
    .el-step__description.is-process {color: #409eff;}
    .el-step__head.is-wait,
    .el-step__title.is-wait,
    .el-step__description.is-wait {font-weight: bold;color: #303133;border-color: #303133;}
    .el-step__line {background-color: #303133;}
}

.dis {
    .el-step__head.is-success {
        color: #c0c4cc;
        border-color: #c0c4cc;
        .el-step__line {background-color: #c0c4cc;}
    }

    .el-step__head.is-process {
        color: #c0c4cc;
        border-color: #c0c4cc;
        .el-step__line {background-color: #c0c4cc;}
    }

    .el-step__title.is-success,
    .el-step__description.is-success {color: #c0c4cc;}

    .el-step__title.is-process,
    .el-step__description.is-process {color: #c0c4cc;}

    .el-step__head.is-wait,
    .el-step__title.is-wait,
    .el-step__description.is-wait {color: #c0c4cc; border-color: #c0c4cc;}

    .el-step__line {background-color: #c0c4cc;}
}

.dialog-footer {text-align: center;}

.full_dialog {
    .is-fullscreen {
        display: flex;
        flex-direction: column;
        .el-dialog__header {display: none;}
        .el-dialog__body {flex: 1;}
        .gc-statusBar .gc-statusbar {padding: 0;}
    }
}

// 分页
.e-pagination {
    padding: 10px 0;
    min-height: 76px;
    text-align: center;
    // line-height: 76px;
    background-color: #eff2f6;
    img.go {cursor: pointer;}
    .btn-prev,
    .btn-next {background: transparent;}
    button:disabled {background: transparent !important;}
    .el-pagination__sizes {margin-left: 20px;}
    .el-pager {
        li {margin: 0 2px;font-size: 12px;font-weight: normal;background: transparent;}
        .number, .el-icon {
            min-width: 20px;
            line-height: 20px;
            height: 20px;
            // background: #eff2f6;
            position: relative;
            top: 4px;
        }
        .active {background: #aaaaaa;color: #fff;border-radius: 50%;}
        // .el-icon {background: #eff2f6;}
    }
    .el-icon-arrow-left, .el-icon-arrow-right{
        background: #2e61d7;
        color: #fff;
        border-radius: 50%;
        &::before {display: block;content: '';width: 20px;height: 20px;background-size: 100% 100%;}
    }
    .el-icon-arrow-left {
        &::before {background: url(../btn/page_left.png);}
    }
    .el-icon-arrow-right {
        &::before {background: url(../btn/page_right.png);}
    }
    button[disabled='disabled'] {
        .el-icon-arrow-left,
        .el-icon-arrow-right {opacity: 0.5;}
    }
    .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
        height: 0;
    }
    .el-pagination__editor {
        width: 80px !important;
        border-radius: 140px !important;
    }
    .el-input__inner {border-radius: 20px;}
    .el-pagination__total {font-size: 12px;color: #303133;}
}

// .bottomSetting {
//     position: absolute;
//     bottom: 10px;
//     right: 0;
//     left: 0;
// }
.e-table .el-table__cell {text-align: center;}
.top {
    width: 100%;
    margin: 0 auto 20px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    // padding: 10px 0;
    border-radius: 5px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    box-sizing: border-box;
    .right, .left {
        display: flex;
        align-items: center;
        // margin-left: 15px;
        .batchBox {
            display: none;
            padding-left: 10px;
            cursor: pointer;
            .batchicon {vertical-align: middle;}
            .batchfont {vertical-align: middle;padding-left: 8px;}
        }
    }
    .right {
        .el-input__icon {
            color: #3869d9;
            &.el-icon-search {cursor: pointer;}
        }
        margin-right: 15px;
        .right-div1 {margin-right: 10px;}
        .item {margin: 0 20px 0 20px;}
        .squarecolor {
            display: flex;
            align-items: center;
            .color {width: 25px;height: 25px;margin-right: 8px;}
            .colorAdd {background-color: #06ad36;}
            .colorAdds {background: rgb(75, 178, 255);}
            .colorUpdate {background-color: #ffa40d;}
            .colorDel {background-color: #ff2300;}
        }
    }
}
// 表格
.e-table {
    // background-color: #eff2f6;
    th.gutter {
        display: table-cell !important;
    }
    colgroup.gutter {
        display: table-cell !important;
    }
    padding: 10px;
    .icon-active-color {
        color: red !important;
    }
    .icon-edit-active-color {
        color: blue !important;
    }
    .icon-delete-active-color {
        color: red !important;
    }
    .icon-plus-active-color {
        color: #7aa818 !important;
    }
    //变更后列表背景样式
    .el-table .red {
        background: #ff2300;
    }
    .el-table .green {
        background: #06ad36;
    }
    .el-table .yellow {
        background: #ffa40d;
    }
    .el-table .AddCol {
        background: rgb(75, 178, 255);
    }
    // 审核历史图标
    .el-icon-tickets {
        color: #fed737;
        font-size: 20px;
    }
    // 表头
    th {
        font-weight: normal;
        color: #0d0d0e;
        text-align: center !important;
    }
    .el-table__header {
        th {
            font-weight: bold;
        }
    }

    /*鼠标移入某行时的背景色*/
    .el-table--enable-row-hover .el-table__body tr:hover > td {
        background-color: #cbd7f5;
    }
    .el-tooltip {
        width: auto !important;
    }
    // 操作图标
    .fixed-left {
        position: absolute !important;
        left: 5px;
        z-index: 9;
        border: none !important;
        background: none !important;
        width: auto !important;
    }
    .el-table__fixed {
        height: auto;
        bottom: 9px;
        &::before {
            content: none;
        }
    }
    .el-dialog {
        position: absolute;
        right: 20px;
        .item {
            display: flex;
            align-items: center;
            span {
                white-space: nowrap;
                margin-right: 10px;
            }
        }
        .item:nth-child(n + 2) {
            margin-top: 10px;
        }
        .separate {
            margin: 0 8px;
            color: #cecece;
        }
        .el-dialog__footer {
            margin-top: 10px;
        }
    }

    .el-table {
        width: 100% !important;
        margin: 0 auto;
        box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
        border-radius: 10px;
        color: #000;
        &::before{height: 0;}
        // 选择框
        .el-checkbox {
            padding: 1px;
            border-radius: 50%;
            border: 1px solid #648be1;
            width: 16px;
            height: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fff;
        }
        .el-checkbox__inner {
            border-radius: 50%;
            border: 1px solid #fff;
            box-sizing: border-box;
            position: relative;
            top: 1px;
        }
        .is-checked {
            .el-checkbox__inner {
                background: #2f62d7 !important;
                border: 1px solid #fff;
            }
            .el-checkbox__input.is-checked .el-checkbox__inner::after {
                transform: rotate(0deg) scaleY(0);
            }
        }
    }
    // 表头
    thead th {
        background: #e0e7f9 !important;
        color: #000;
    }
    td.blue-column {
        color: #2f62d7;
        .cell {
            cursor: pointer;
        }
    }
    .el-checkbox__inner:hover {
        border: 1px solid #fff;
    }
    .el-table--enable-row-hover .el-table__body tr {
        transition: font-weight 0.3s ease;
    }
    .el-table--enable-row-hover .el-table__body tr:hover {
        font-weight: bold !important;
    }
    tbody .hover-row {
        font-weight: bold !important;
    }
    .el-table__cell {
        .cell {
            word-break: keep-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        &.is-left{
            text-align: left;
        }
    }
    .is-indeterminate {
        .el-checkbox__inner {
            background: #2f62d7 !important;
            // transform: rotate(0deg) scaleY(0);
        }
        .el-checkbox__inner::before {
            height: 0;
        }
    }
    .tab-header {
        //变更表头
        .tab-heade-icon {
            width: 20px;
            height: 20px;
            border-radius: 20px;
            line-height: 20px;
            font-size: 10px;
            background-color: red;
            color: #fff;
            display: inline-block;
        }
    }
}

// 评分表格
.e-scoreTable {
    @extend .e-table;
    .el-table_1_column_2 {
        color: #000;
    }
    .el-table__cell {
        color: #606266;
    }
    #Top {
        width: 93.5%;
        background: #fff;
        display: flex;
        justify-content: flex-end;
        padding: 20px;
        margin: 0 auto 20px;
        box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    }
    .el-radio {
        border: 1px solid #648be1;
        background: #fff;
        height: 16px;
        width: 16px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .el-radio__inner {
        border: 1px solid #fff;
        position: relative;
        left: 4.8px;
        top: 0.8px;
    }
    .el-radio__inner:hover {
        border: 1px solid #fff;
    }
    .el-dropdown-link {
        cursor: pointer;
        // color: #dadada;
    }
    .el-icon-arrow-down {
        font-size: 12px;
    }
    tbody {
        .el-table__row:nth-child(1) {
            background: #e4eed1;
        }
        .el-table__row:nth-child(2) {
            background: #fbdcd8;
        }
        .el-table__row:nth-child(3) {
            background: #fef4d1;
        }
    }
    .el-table__cell {
        border-bottom: none !important;
    }
    .el-radio__input.is-checked {
        .el-radio__inner {
            background: #2e61d7;
        }
        .el-radio__inner::after {
            background: transparent;
        }
    }
    tbody {
        .el-input__inner {
            border: none;
            background: transparent;
        }
    }
}

// 表单
.el-tabs__item {
    z-index: 3;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.el-tabs--left .el-tabs__active-bar.is-left {
    left: 0;
    width: auto;
    background-color: #fff;
}
.el-tabs--left .el-tabs__item.is-left {
    text-align: left;
}
.el-tabs__nav-wrap::after {
    z-index: 3;
}

.e-form {
    width: 100%;
    height: 100%;
    // margin-bottom: 79.6px; // 底部button浮动高度
    position: relative;
    // 标签对齐
    .el-select {
        width: 100%;
    }
    .el-input {
        width: 100%;
    }
    // 每个模块
    .con {
        // padding-bottom: 20px;
        // margin-bottom: 20px;
    }
    // 下拉组件按需加载占位loading
    .seize-a-seat {
        // min-height: 97vh;
        text-align: center;
        i {
            margin: 50px 0;
            font-size: 35px;
            color: #2e61d7;
        }
    }
    // 解决空字符串不居中
    .el-row {
        display: flex;
        align-items: center;
    }
    .el-form-item::before {
        display: none;
    }
    .el-tab-pane {
        .el-table {
            width: 100% !important;
        }
    }
    .el-tab-pane {
        padding-left: 10px;
    }
    .top-inside {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 13px;
    }
    .el-form-item {
        display: flex;
        align-items: center;
        width: 100%;
        // 表单统一按页面加label-width设置标签宽度，因为要考虑标签文字换行的问题，还有文字与textarea上对齐
        .el-form-item__label {
            text-align: right;
        }
        .el-form-item__content {
            flex: 1;
            margin: 0 !important;
            padding: 0 10px 0 0;
            // 解决表单右侧文字没对齐
            display: flex;
            align-items: center;
            word-break: break-all;
        }
        i {
            cursor: pointer;
            font-size: 20px;
            &.greenFont {
                color: #1698db;
                font-weight: bold;
            }
        }
        // 日期
        .el-date-editor--date {
            .el-input__icon{
                position: absolute;
                right: 20px;
            }
            .el-input__prefix {
                width: 100%;
                .el-icon-date {
                    right: 8px;
                }
            }
            .el-input__inner {
                padding-left: 10px;
            }
        }
    }
    // 上传
    .upload {
        .el-upload-dragger {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border: none;
        }
    }
    .el-dialog .buttons {
        bottom: -70px;
    }
    .el-dialog__header {
        padding: 0;
    }
    .el-dialog__body {
        padding: 0 !important;
    }
    .title {
        text-align: center;
        img {
            width: 100%;
        }
        span {
            width: 100%;
            position: absolute;
            z-index: 9;
            color: #fff;
            left: 0;
            right: 0;
            top: 10px;
        }
        i {
            width: 100%;
            position: absolute;
            z-index: 9;
            color: #fff;
            right: 20px;
            text-align: right;
            font-size: 20px;
            top: 10px;
        }
    }
    .tabs {
        .el-tabs {
            position: relative;
            .el-tabs__nav-wrap.is-left {
                background: #eff2f6;
                padding-bottom: 80px;
            }
            .el-tabs__header {
                position: absolute;
                left: 0;
                // top: 70px;
                z-index: 3;
                width: 190px;
            }
            .el-tabs__content {
                height: 100% !important;
                transition: 500ms;
                margin-left: 190px;
                padding: 0 20px;
                #tabs-content{
                    height: 100% !important;
                    padding-bottom: 30px;
                    overflow-y: scroll;
                    &::-webkit-scrollbar {width: 0;}
                }
            }
        }
        .el-tabs__item {
            color: #666;
            &.is-left {
                border: none;
            }
            &.is-disabled {
                color: #666;
            }
            &.is-active {
                background: none;
                color: #409eff;
                font-weight: bold;
            }
        }
        .el-tabs__header {
            margin-bottom: 0 !important;
            transition: 500ms;
            -webkit-tap-highlight-color: transparent;
            .header-before{
                &::before{
                    font-family: element-icons !important;
                    content: "\e6dd";
                    width: 100%;
                    font-size: 30px;
                    color: #2e61d7;
                    display: block;
                    padding: 10px 5px;
                    cursor: pointer;
                    box-sizing: border-box;
                    pointer-events:auto;
                    text-align: right;
                    background: #e4ecf0;
                }
            }
        }
        .el-tabs__nav {
            border-bottom: 1px solid #eff2f6;
            background: #eff2f6;
        }
        .el-tabs--card > .el-tabs__header {
            border: none !important;
        }
        &.hide{
            .el-tabs__header {
                margin-bottom: 0 !important;
                width: 50px;
                transition: 500ms;
                .header-before{
                    &::before{
                        font-family: element-icons !important;
                        content: "\e6dc";
                        width: 100%;
                        font-size: 30px;
                        color: #2e61d7;
                        display: block;
                        padding: 10px 5px;
                        cursor: pointer;
                        box-sizing: border-box;
                    }
                }
                .el-tabs__nav-wrap{
                    .el-tabs__nav{
                        display: none;
                    }
                }
            }
            .el-tabs__content{
                transition: 500ms;
                margin: 0 0 0 50px;
            }
        }
    }
    .billTop{
        position: absolute;
        // top: 0;
        z-index: 10;
        width: 100%;
        .billTop-title{
            display: flex;
            justify-content: space-between;
            font-size: 20px;
            height: 70px;
            align-items: center;
            padding: 0 20px;
            background-color: #eff2f6;
            .title{
                color: #0d0d0e;
            }
            .el-icon-circle-close{
                cursor: pointer;
                transform:rotate(-90deg);
                transition: 500ms;
            }
            .el-icon-circle-close:hover{
                color: #e9513e;
                transform:rotate(90deg);
                transition: 500ms;
            }
        }
    }
    .buttons {
        width: 100%;
        text-align: right;
        height: 78px;
        line-height: 78px;
        background: #eff2f6;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 1;
        .tip {
            margin-right: 40px;
        }

        .el-button {
            margin-right: 10px;
        }
    }
    .el-tabs__content {
        margin-top: 10px;
    }
    .tabs-title {
        margin: 0 0 0 10px;
        color: #409eff;
        line-height: 22px;
        padding: 10px 0 20px 20px;
        position: relative;
    }
    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #409eff;
        display: block;
        position: absolute;
        left: 0;
    }
    //多选框
    .el-form-item {
        .el-checkbox__input {
            width: 14px;
            height: 14px;
            background-image: url('../noCheck.png');
            background-size: 100%;
            transition: 0.3s;
            &.is-checked {
                background-image: url('../isCheck.png');
                & + .el-checkbox__label {
                    color: #606266;
                }
            }
            .el-checkbox__inner {
                display: none;
            }
            &.is-disabled {
                cursor: not-allowed;
                opacity: 0.3;
                &.is-checked {
                    & + .el-checkbox__label {
                        color: #c0c4cc;
                    }
                }
            }
        }
    }
}

// 树形表格
.e-tree-table {
    @extend .e-table;
    padding: 0;
    //margin-top: 10px;
    // border: 1px solid #ccc;
    // border-radius: 5px;
    // 选中颜色
    .el-table__body tr.current-row > td.el-table__cell {
        background-color: #cbd7f5;
    }
    // 表头
    .has-gutter th {
        background: #e0e7f9 !important;
        color: #000;
    }
    .el-table--enable-row-hover .el-table__body tr:hover {
        font-weight: bold;
    }
    .el-table .el-table__expand-icon--expanded {
        transform: none;
    }
    .el-tree .el-tree-node__expand-icon.expanded {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    //有子节点 且未展开
    .el-table .el-icon-arrow-right{
        display: flex;
        &:before {
            background: url('../add.png') center bottom no-repeat;
            background-size: contain;
            content: '';
            display: block;
            width: 20px;
            height: 20px;
            font-size: 20px;
        }
    }
    //有子节点 且已展开
    .el-table .el-table__expand-icon--expanded {
        // 取消旋转
        -webkit-transform: rotate(0);
        transform: rotate(0);
        .el-icon-arrow-right:before {
            background: url('../sub.png') center bottom no-repeat;
            background-size: contain;
            content: '';
            display: block;
            width: 20px;
            height: 20px;
            font-size: 20px;
        }
    }
    //没有子节点
    .el-tree .el-tree-node__expand-icon.is-leaf::before .el-table.el-table__placeholder::before {
        content: '';
        display: block;
        width: 20px;
        height: 20px;
        font-size: 20px;
        background-size: 20px;
    }
    .el-table__row{
        td:nth-child(1){
            .cell{
                display: flex;
                align-items: center;
            }
        }
    }
}

// 树形表格2
.e-tree-table2 {
    @extend .e-table;
    background-color: #FFF;
    //margin-top: 10px;
    // border: 1px solid #ccc;
    // border-radius: 5px;
    // 选中颜色
    .el-table__body tr.current-row > td.el-table__cell {
        background-color: #cbd7f5;
    }
    // 表头
    .has-gutter th {
        background: #e0e7f9 !important;
        color: #000;
    }
    .el-table--enable-row-hover .el-table__body tr:hover {
        font-weight: bold;
    }
    .el-table .el-table__expand-icon--expanded {
        transform: none;
    }
    .el-tree .el-tree-node__expand-icon.expanded {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    //有子节点 且未展开
    .el-table .el-icon-arrow-right{
        display: flex;
        &:before {
            background: url('../add.png') center bottom no-repeat;
            background-size: contain;
            content: '';
            display: block;
            width: 20px;
            height: 20px;
            font-size: 20px;
        }
    }
    //有子节点 且已展开
    .el-table .el-table__expand-icon--expanded {
        // 取消旋转
        -webkit-transform: rotate(0);
        transform: rotate(0);
        .el-icon-arrow-right:before {
            background: url('../sub.png') center bottom no-repeat;
            background-size: contain;
            content: '';
            display: block;
            width: 20px;
            height: 20px;
            font-size: 20px;
        }
    }
    //没有子节点
    .el-tree .el-tree-node__expand-icon.is-leaf::before .el-table.el-table__placeholder::before {
        content: '';
        display: block;
        width: 20px;
        height: 20px;
        font-size: 20px;
        background-size: 20px;
    }
    .el-table__row{
        td:nth-child(1){
            .cell{
                display: flex;
                align-items: center;
            }
        }
    }
}

// 树形文件
.e-tree-file {
    min-width: 180px;
    margin-bottom: 10px;
    .el-tree {
        border: 1px solid #ebeef5;
    }
    .title {
        background-color: #e0e7f9;
        padding: 10px;
    }
    .el-tree-node__content {
        line-height: 50px;
        margin-top: 8px;
    }
    // 树形列表
    .el-tree .el-tree-node__expand-icon.expanded {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    //有子节点 且未展开
    .el-tree .el-icon-caret-right:before {
        background: url('../add.png') no-repeat 0 3px;
        content: '';
        display: block;
        width: 22px;
        height: 22px;
        font-size: 16px;
        background-size: 16px;
    }

    //有子节点 且已展开
    .el-tree .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
        background: url('../sub.png') no-repeat 0 3px;
        content: '';
        display: block;
        width: 22px;
        height: 22px;
        font-size: 16px;
        background-size: 16px;
    }

    //没有子节点
    .el-tree .el-tree-node__expand-icon.is-leaf::before {
        background: transparent no-repeat 0 3px;
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        font-size: 16px;
        background-size: 16px;
    }
}

.el-button {
    font-size: 14px;
    font-family: "SourceHanSansCN-Regular";
    &.is-circle{padding: 8px; font-size: 20px;}
    &--small {
        border-radius: 5px;
    }
    &--primary {
        background-color: #2e61d7;
    }
    //黄绿色按钮
    &.btn-greenYellow {
        background: #7aa818;
        color: #fff;
        border-color: white;
        &:active {
            color: #fff;
            border-color: #7aa818;
            outline: 0;
            background: #7aa818;
        }
        &:hover,
        &:focus {
            color: #fff;
            background: #7aa818;
        }
    }
    //蓝色按钮
    &.btn-blue {
        background: #2e61d7;
        color: #fff;
        border-color: white;
        &:active {
            color: #fff;
            border-color: #2e61d7;
            outline: 0;
            background: #2e61d7;
        }
        &:hover,
        &:focus {
            color: #fff;
            background: #2e61d7;
        }
    }
    //红色按钮
    &.btn-delete,
    &.btn-red {
        background: #e9513e;
        color: #fff;
        border-color: #e9513e;
        &:active {
            color: #fff;
            border-color: #e9513e;
            outline: 0;
            background: #e9513e;
        }
        &:hover,
        &:focus {
            color: #fff;
            background: #e9513e;
        }
    }
    //灰色按钮
    &.btn-grey {
        background: grey;
        color: #fff;
        border-color: grey;
        &:active {
            color: #fff;
            border-color: grey;
            outline: 0;
            background: grey;
        }
        &:hover,
        &:focus {
            color: #fff;
            background: grey;
        }
    }
    &.is-disabled {
        opacity: 0.5;
    }
}
.el-icon-delete {
    font-size: 24px;
    color: #e9513e;
    cursor: pointer;
}

.form {
    .el-input,
    .el-select {
        width: 100%;
    }

    .el-form-item {
        margin-bottom: 20px !important;
        .el-form-item__label {
            display: inline-block;
            line-height: 1.5;
        }
    }

    .el-input__inner,
    .el-textarea__inner {
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

        &:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
        }
    }
}

//设置文本域样式，防止换行
.el-textarea {
    display: block;
}

//列表顶部按钮
.listTop {
    .btn {
        display: flex;
        margin-left: 30px !important;
        align-items: center;
        cursor: pointer;
        user-select: none;
        .btn-icon {
            width: 16px;
            margin-right: 8px;
        }
    }
}
//设置文本域样式，防止换行
.el-textarea {
    display: block;
}
.el-input__suffix {
    display: flex;
    align-items: center;
    .el-input__suffix-inner {
        display: flex;
        align-items: center;
    }
}
// 搜索框
.search_box {
    display: flex;
    align-items: center;
    .ipt {
        border-radius: 5px;
        width: calc(100% - 22px);
        cursor: pointer;
        border: 1px solid #ccc;
        .el-input__inner {
            border: none;
            height: 35px;
        }
        img {
            cursor: pointer;
        }
        ::placeholder {
            color: #cccc;
            padding-left: 10px;
        }
    }
    .adverse {
        margin: 0 10px;
    }
}
//高级搜索框
.searchDialog {
    padding-top: 30px;
    width: 94%;
    padding-right: 3%;
    padding-left: 3%;
    .el-input--suffix {
        &.is-focus {
            display: block !important;
        }
        i {
            cursor: pointer;
            font-size: 20px;
            display: flex; 
            align-items: center;
        }
    }
    .el-icon-circle-close {
        line-height: 40px;
    }
    .el-select-dropdown {
        top: auto !important;
        left: auto !important;
        right: auto !important;
        bottom: auto !important;
    }
    .el-icon-date {
        line-height: 40px;
    }
    .separate {
        padding: 0 10px;
        font-size: 8px;
    }
    .footer-btn {
        text-align: right;
        margin-right: 10px;
    }
    .el-row {
        display: flex;
        flex-wrap: wrap;
        .el-col {
            min-width: 300px;
            flex: 1;
        }
    }
    .el-input--mini {
        .el-input__icon {
            line-height: 40px;
        }
    }
    .el-select {
        width: 100%;
    }
    .el-checkbox-group{
        display: flex;
    }
    .el-checkbox{
        display: flex; align-items: center;
    }
}

//表格选择数据左右添加
.select-table {
    background-color: #fff;
    padding: 20px;
    .header-title {
        font-size: 18px;
        margin-bottom: 30px;
        color: #585252;
    }
    .bottom {
        margin-top: 20px;
        text-align: right;
    }
    .row {
        display: flex;
        min-height: 550px;
        .wrapper {
            border: 1px solid #ebeef5;
            padding: 20px;
            border-radius: 2px;
            margin: 5px;
            position: relative;
            height: auto;
            .search {
                margin: 10px;
                .el-input__icon {
                    cursor: pointer;
                }
            }
            .title {
                position: absolute;
                top: -10px;
                left: 10px;
                padding: 0 5px;
                background: #fff;
            }
        }
    }
}
//基础管理界面
.base-page {
    padding: 0;
    display: flex;
    // background-color: #eff2f6;
    .left {
        padding: 100px 0 0;
        border-radius: 5px;
        width: 246px;
        box-sizing: border-box;
        position: relative;
        // overflow: auto;
        &.org{
            width: 400px;
        }
    }
    .right {
        width: 100%;
        // flex: 1;
        // margin-left: 10px;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
        scrollbar-width: none;
        overflow: scroll;
        &::-webkit-scrollbar {display: none;}
        .e-form{
            // height: 770px;
            flex-grow: 1;
            position: relative;
            overflow-y: auto;
        }
        .top {
            flex: 1;
            display: flex;
            align-items: center;
            margin-bottom: 0;
            padding: 0;
            height: 55px;
            .left {
                width: 50%;
                margin-left: 10px;
                padding: 0;
                border: 0;
                .el-input--mini {
                    width: 200px;
                    margin-left: 10px;
                }
                .prompt {
                    margin: 0 0 0 10px;
                    font-size: 12px;
                    color: #999;
                }
            }
            .right {
                width: 50%;
                border: 0;
                .el-input--mini {
                    width: 200px;
                    margin-left: 10px;
                }
            }
        }
        .tabs {
            .el-tabs__header {
                left: 272px;
                top: 70px;
                bottom: 11px;
                height: auto;
            }
        }
        .footer {
            width: inherit;
            height: 70px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            background-color: rgb(239, 242, 246);
            .left-btn {
                width: 200px;
                margin-left: 10px;
            }
            .right-btn {
                margin-right: 10px;
                button{
                    width: 90px;
                    height: 38px;
                    line-height: 38px;
                }
            }
        }
    }
    .e-form {
        margin: 0;
        position: relative;
        background-color: #fff;
        .el-form-item__content {
            line-height: 1.5;
        }
    }
}
//错误提示
.errorMsg {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    margin: 10px 0;
    &::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
    }
}

.attach_ls {
    .el-upload {
        display: none;
    }
}

// 明细icon样式
.history-update-tab {
    display: flex;
    justify-content: center;
    align-items: center;
}

//树形列表操作列样式
.oparation_box {
    display: flex;
    align-items: center;
    width: 100%;
    cursor: pointer;
    .add_sub {
        margin: 0 5px 0 0;
        width: 20px;
        height: 20px;
    }
    .cancel_delete {
        margin: 0 5px 0 0;
        width: 20px;
        height: 20px;
    }
    .operation_icon{ //主列表操作图标样式
        margin: 0 10px;
        width: 20px;
        height: 20px;
    }
    .add_preview{
        margin: 0 5px 0 0;
        width: 25px;
    }
}
//普通树形列表操作列样式
.list_box{
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    cursor: pointer;
    .add_sub {
        margin: 0 5px 0 0;
        width: 20px;
        height: 20px;
    }
    .cancel_delete {
        margin: 0 5px 0 0;
        width: 20px;
        height: 20px;
    }
    .operation_icon{ //主列表操作图标样式
        margin: 0 10px;
        width: 20px;
        height: 20px;
    }
}

//控制表格当前行显示隐藏
.row-hide {
    display: none;
}
.row-show {
    display: table-row;
}

//el-tab-pane样式
.warningTabs {
    background-color: #eff2f6;
    height: 100%;
    .el-tabs {
        height: 90% !important;
    }
    .el-tabs__item {
        height: 100%;
        font-family: element-icons !important;
        display: flex !important;
        align-items: center;
        justify-content: space-between;
        .warning {
            color: #f56c6c;
        }
    }
    .el-tabs__content {
        background-color: #fff;
    }
}
//传输财务共享字体颜色
.redFont {
    color: red;
}
.greenFont {
    color: green;
}
//表格嵌套表单样式
.table{
    .el-form {
        .el-form-item{
            margin-bottom: 0;
            &.is-error{
                margin:18px 0;
            }
            .el-form-item__content{
                padding: 0;
            }
        }
    }

}
//两个时间选择器中间线样式
.separ{
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}
//主列表样式
.billList{
    display: flex;
    flex-flow: column;
    .el-table{
        .el-table__body-wrapper{
            overflow: auto!important;
        }
    }
}
//明细表格样式
.detailTable{
    min-height: 40px;
    .el-table__body-wrapper{
        overflow: auto!important;
    }
}
//表格高亮背景
.el-table__body tr.current-row>td.el-table__cell{
    background-color: #CBD7F5;
}
//表格边框线颜色
.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid #E0E0E0;
    height: 40px; // 全局设置表格行高度
}
.el-table--border .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right: 1px solid #E0E0E0;
}
//留痕图标
.historyData{
    .cell{
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}

.color-gray{
    color: #888;
}
.color-blue{
    color: #2e61d7;
}
.pdTopHeight{
    padding-top: 70px!important;
}

//流程状态颜色
.status-invalid{ //作废类
    color: #e9513e;
}
.status-approved{ //审核完成类
    color: #7aa818;
}
.status-audit{ //审核过程中类
    color: #fbc71b;
}
.status-draft{ //草稿类
    color: #2e61d7;
}
.el-menu-vertical-demo.el-menu{
    overflow-y: auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {display: none;}
    .el-submenu__title, .el-menu-item{
        width: 280px;
        height: 50px;
        margin: 0 auto 4px;
        line-height: 50px;
        font-family: "SourceHanSansCN-Regular";
        font-size: 17px;
        border-radius: 6px;
        img{width: 22px; height: 22px; margin-top: -4px;}
        span{line-height: 50px;}
        &:hover{
            background-color: #08305E !important;
            .bar{background-color: #ffd41c;}
        }
    }

    .el-submenu{
        i.el-icon-arrow-down::before{
            background: url(../images/tragle828.png) no-repeat 1px 4px;
            color: unset;
        }
        &.is-opened{
            .el-submenu__title{
                width: 280px;
                height: 50px;
                margin: 0 auto 4px;
                color: #FFD41C !important;
                background-color: #08305E !important;
                .bar{background-color: #ffd41c;}
            }
        }
        
    }
    .menu-item-box{
        padding: 10px 0;
        li.el-menu-item{width: 280px; height: 46px; margin-bottom: 6px; font-size: 15px; font-weight: 400 !important; line-height: 46px; color: red; position: relative;
            img{width: unset; height: unset; margin: 0 14px 0 -10px;}
            &:hover{background-color: unset !important;}
        }
        .el-menu-item{
            box-sizing: content-box;
            //&:first-child{padding-top: 10px;}
            //&:last-child{padding-bottom: 10px; margin-bottom: 0;}
        }
    }

}
.swiper-container{width: 100%; height: 100%;}
.el-input__inner{
    font-family: "SourceHanSansCN-Regular";}
.el-input--suffix .el-input__inner{border-radius: 0; height: 34px; border: 1px solid #cdcdcd;}
.center{margin: 0 auto;}
.pointer{cursor: pointer}
input[type="password"]::-ms-reveal{display: none;}
input[type="password"]::-ms-clear{display: none;}
input[type="password"]::-o-clear{display: none;}
input, button{outline: none;border: none;}
.search_bar{display: flex; justify-content: space-between; align-items: center;}
.full {
    height: 100%;
    position: relative;
    background-color: #eff2f6;
    #fold-btn{
        width: 11px;
        height: 80px;
        margin-top: -40px;
        border-radius: 5px;
        position: absolute;
        right: 0;
        top: 50%;
        z-index: 1;
        background-image: url(../menu_close.png);
        background-color: #2e61d7;
    }
}
.el-loading-spinner .circular{
    width: 42px;
    height: 42px;
    animation: loading-rotate 2s linear infinite;
    display: none;
    z-index: 100;
}
.el-loading-spinner{
    /* 图片替换为自定义的即可 */
    background: url(../loading/circle.gif) no-repeat;
    background-size: 48px 48px;
    width: 100%;
    height: 100%;
    position: relative;
    top: 50%;
    left: 50%;
}
.el-input--suffix .el-input__inner{border-radius: 0; height: 34px; border: 1px solid #cdcdcd;}
.center{margin: 0 auto;}
.menu {
    width: auto;
    height: 100%;
    padding: 0 15px;
    // transition: .3s;
    background: #111c29 url('../images/img81891.png') no-repeat 0 100%;
    overflow-y: overlay;
    &::-webkit-scrollbar-track {background-color: transparent;}
    .title {
        height: 158px;
        padding-top: 70px;
        img {margin: 0 auto; display: block;}
    }
    .bar {
        width: 6px;
        height: 100%;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        position: absolute;
        left: 0;
        // transition: .3s;
        // transition-delay: .3s;
    }
    .menu-item-box {
        width: 280px;
        margin: 3px auto;
        background: #08305e;
        border-radius: 6px;
        // overflow: scroll;
    }
}

.pagination-front {
    .el-pagination {
        button {
            width: 80px;
            height: 40px;
            background-color: beige;
            border: 1px solid rgba(230, 230, 230, 1);
        }
        .el-pager li {
            width: 40px !important;
            height: 40px !important;
            line-height: 40px;
            border: 1px solid rgba(230, 230, 230, 1);
            &.active {background-color: rgba(33, 110, 198, 1);color: #fff;}
            &:not(li:last-child) {
                margin-right: 5px;
            }
        }
        .btn-prev, .btn-next {
            padding: 0;
        }
        .btn-prev:disabled, .btn-next:disabled{color: #606266 !important;}
        .btn-prev, .btn-next, .el-input__inner{
            margin: 0 5px;
            font-size: 16px !important;
            background: none;
            color: rgba(0, 0, 0, 1);
        }
        .el-icon.el-icon-arrow-right::before {
            content: '下一页';
            font-size: 16px;
            font-weight: normal;
        }
        .el-icon.el-icon-arrow-left::before {
            content: '上一页';
            font-size: 16px;
            font-weight: normal;
        }
        .btn-prev, .btn-next, li.number {
            height: 40px;
        }
    }
}
// 前台商城dialog
.front{
    .filterBox_top {
        width: 100%;
        height: 48px;
        padding-left: 20px;
        border-top: 1px solid #E6E6E6;
        border-bottom: 1px solid #E6E6E6;
        background-color: #fff;

        /deep/ .el-radio {
            margin-right: 20px;

            .el-radio__inner {
                // border-radius: 0;
                border: 1px solid rgba(204, 204, 204, 1);
                // &::after {display: none;}
            }

            .el-radio__label {
                padding-left: 8px;
            }
        }

        .sortBtn {
            height: 26px;
            padding: 0 9px 0 10px;
            line-height: 26px;
            border: 1px solid rgba(229, 229, 229, 1);
            user-select: none;
            &:not(:last-of-type) {margin-right: -1px;}
            span {
                margin-right: 8px;
            }
        }

        .selected {
            color: rgba(212, 48, 48, 1);
            border-color: rgba(212, 48, 48, 1);
            z-index: 3;
        }
    }

    .filterBox_bottom {
        height: 50px;
        padding-left: 20px;
        background-color: #fff;

        .mailAddress {
            width: 196px;
            height: 26px;
            margin-right: 12px;
            color: rgba(51, 51, 51, 1);
            border: 1px solid rgba(229, 229, 229, 1);

            img {
                margin: 0 7px;
            }

            /deep/ .el-cascader {
                width: 110px;
                height: 50px;

                .el-input__inner {
                    width: 110px;
                    height: 50px;
                    padding-left: 0;
                    border: none;
                    background-color: transparent;
                }
            }
        }

        .priceFilter {
            width: 94px;
            height: 26px;
            padding-left: 8px;
            color: rgba(102, 102, 102, 1);
            border: 1px solid rgba(229, 229, 229, 1);

            input {
                width: 66px;
                height: 24px;
                margin-left: 4px;
                border: none;
            }
        }

        .bar {
            width: 10px;
            height: 1px;
            margin: 8px;
            background-color: rgba(229, 229, 229, 1);
        }

        .searchInput {
            margin-left: 12px;

            &>div:first-child {
                width: 196px;
                height: 26px;
                padding-left: 8px;
                border: 1px solid rgba(229, 229, 229, 1);

                input {
                    width: 150px;
                    height: 21px;
                    margin-left: 4px;

                    &::placeholder {
                        color: rgba(204, 204, 204, 1);
                    }
                }
            }

            button {
                width: 52px;
                height: 26px;
                font-size: 14px;
                font-weight: 400;
                color: rgba(255, 255, 255, 1);
                background: rgba(212, 48, 48, 1);
            }
        }
    }
    // &.el-dialog__wrapper {width: 1910px;}
    .el-dialog {
        max-height: 100vh;
        //overflow: auto;
        .el-dialog__body {
            padding: 0 !important;
        }
    
        .el-dialog__header,
        .el-dialog__close.el-icon.el-icon-close {
            display: none;
        }
    }
    
    .dialog-header {
        width: 100%;
        height: 41px;
        font-family: 'SourceHanSansCN-Medium';
        font-size: 20px;
        font-weight: 500;
        color: #333333;
        background-color: #fff;
    
        .dialog-header-top {
            height: 20px;
            margin-bottom: 20px;
            font-weight: 500;
    
            .dialog-title>div:first-child {
                height: 20px;
                width: 3px;
                margin-right: 9px;
                background: #216ec6;
            }
    
            .dialog-close {
                cursor: pointer;
            }
        }
    
        &>div:last-child {
            width: inherit;
            height: 1px;
            background: #d8d8d8;
        }
    }
    .dialog-body {
        overflow: auto;
        padding-top: 41px;
    }
}
// 前台商城表格
.order-list{
    .el-table {
        border: 1px solid rgba(230, 230, 230, 1);
        thead { 
            th {
                height: 52px;
                font-weight: 400;
                color: rgba(51, 51, 51, 1);
                background-color: rgba(247, 247, 247, 1);
                .cell {padding: 0;}
            }
            & th:first-child {
                .cell {padding-left: 40px;}
            }
        }
        .el-table__body {
            .el-table__row {
                height: 144px;
                padding: 22px 24px;
                .el-table__cell:first-child {
                    padding-left: 24px;
                }
                .el-table__cell:last-child {
                    padding-right: 24px;
                }
                .cell {padding: 0;}
            }
        }
    }
    .info, .price, .num, .total {height: 100px;}
    .info {
        width: 785px;
        img {
            width: 100px;
            height: 100px;
            margin-right: 12px;
        }
        h4, div{
            font-size: 14px;
            font-weight: 400;
        }
        h4 {margin-bottom: 11px; color: rgba(0, 0, 0, 1);}
        div {margin-bottom: 4px; color: rgba(102, 102, 102, 1);}
    }
    .price {
        width: 138px;
        & div:first-child {margin-bottom: 4px;}
    }
    .num {
        font-size: 14px;
        color: rgba(51, 51, 51, 1);
    }
    .numCalc {
        width: 101px;
        height: 26px;
        margin-right: 84px;
        text-align: center;
        line-height: 26px;
        border: 1px solid rgba(204, 204, 204, 1);
        div {
            width: 100%;
            height: 26px;
            cursor: pointer;
            &:first-child {border-right: 1px solid rgba(204, 204, 204, 1);}
            &:last-child {border-left: 1px solid rgba(204, 204, 204, 1);}
        }
        input {
            width: 49px;
            text-align: center;
        }
    }
    .total {
        font-size: 14px;
        font-weight: 700;
        color: rgba(51, 51, 51, 1);
    }
}
.df{display: flex;}
.dfa{display: flex;align-items: center;}
.dfc{display: flex;align-items: center;justify-content: center;}
.dfb{display: flex;align-items: center;justify-content: space-between;}
.p20{padding: 20px;}

.mt10{margin-top: 10px;}
.mt20{margin-top: 20px;}
.mt30{margin-top: 30px;}
.mt40{margin-top: 40px;}
.ml10{margin-left: 10px;}
.ml20{margin-left: 20px;}
.mr30{margin-right: 30px;}
.mb10{margin-bottom: 10px;}
.mb20{margin-bottom: 20px;}

// 个人中心
.userCenter {
    .title {
        height: 50px;
        padding-left: 20px;
        line-height: 50px;
        font-size: 18px;
        color: rgba(51, 51, 51, 1);
        border-bottom: 1px solid rgba(230, 230, 230, 1);
    }
    .tab {
        font-size: 16px;
        color: rgba(102, 102, 102, 1);
        div {
            margin-right: 50px;
            cursor: pointer;
        }
        .active {
            color: rgba(0, 0, 0, 1);
            &::after {
                content: '';
                display: block;
                width: 100%;
                height: 2px;
                margin-top: 4px;
                background-color: rgba(34, 111, 199, 1);
            }
        }
    }
}
.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);
    position: relative;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

// 文本溢出隐藏
.textOverflow1 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.textOverflow2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.nomore {
    width: 1326px;
    height: 140px;
    line-height: 140px;
    text-align: center;
    font-size: 18px;
    color: gray;
}

.noSliceSpace {
    white-space: pre-wrap;
}

div.el-pagination#productPager {
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    span.el-pagination__sizes {
        height: 34px;
        display: inline-block;
    }
    .el-pagination__jump {
        height: 34px;
    }
    .el-input, .el-input__inner {
        height: 34px;
        border-radius: 0 !important;
    }
    button.btn-prev, button.btn-next {
        height: 34px !important;
        font-size: 14px;
        font-weight: bold;
        padding: 0 14px;
        border: 1px solid #cdcdcd;
    }
    .btn-next {
        margin-left: 10px !important;
    }
}