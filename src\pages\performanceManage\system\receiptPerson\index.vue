<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">

                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">启用</el-button>
                            <el-button type="primary" @click="changePublishState(0)" class="btn-delete">停用</el-button>
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                        </div>
                    </div>
                    <!-- -搜索栏开始----------------------------搜索栏 -->
                    <div class="search_box">
                        <el-input type="text" clearable placeholder="请输入关键字搜索" @keyup.enter.native="handleInputSearch" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                    <!-- 高级搜索弹出框开始 -->
                    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%" >
                        <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="收料员状态：">
                                        <el-select v-model="filterData.state" clearable placeholder="收料员启用状态">
                                            <el-option v-for="item in stateFilter" :key="item.value" :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="电话号码：">
                                        <el-input clearable v-model="filterData.phone" type="number" maxlength="11" placeholder="输入手机号查询"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="姓名：">
                                        <el-input clearable v-model="filterData.name" placeholder="输入姓名查询"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <span slot="footer">
                            <el-button type="primary" @click="confirmSearch">查询</el-button>
                            <el-button @click="resetSearchConditions">清空</el-button>
                            <el-button @click="queryVisible = false">取消</el-button>
                        </span>
                    </el-dialog>
                    <!-- 高级搜索弹出框 -->
                    <!-- -搜索栏结束----------------------------搜索栏 -->
                </div>

            </div>
            <div class="e-table" :style="{ width: '100%' }">
                <el-table
                    class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @row-click="handleCurrentInventoryClick"
                    ref="eltableCurrentRow" @current-change="handleCurrentChange"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <!-- 收料人ID -->
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="120">
                        <template slot-scope="scope">
                            <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""/></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="收料人员" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.name }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="电话" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.phone }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" width="">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.state == 1" type="success">启用</el-tag>
                            <el-tag v-if="scope.row.state == 0" type="danger">停用</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.remarks }}
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.total" :limit="20" :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <!-- 新增页面 -->
        <div class="right" v-show="viewList !== true">
            <div class="e-form" style="padding: 0 10px 10px" v-show="viewList === 'class'">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="收料人员：" prop="name">
                                <el-input clearable v-model="formData.name"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="电话号码：" prop="phone">
                                <el-input clearable v-model="formData.phone"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="状态：" prop="state">
                                <el-radio v-model="formData.state" :label="1">启用</el-radio>
                                <el-radio v-model="formData.state" :label="0">停用</el-radio>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="备注信息：">
                                <el-input clearable type="textarea" v-model="formData.remarks" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { batchDelete, batchNotPublish, batchPublish, create, del, edit, getList } from '@/api/performance/receiptPerson'
import { debounce, hideLoading, showLoading } from '@/utils/common'

export default {
    components: {
        ComPagination
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return this.screenHeight - 21 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return this.screenWidth - 302 + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.pages.total > 0) {
                return this.screenHeight - 244
            }
            return this.screenHeight - 291
        }
    },
    data () {
        return {
            screenWidth: 0,
            alertName: '收料员',
            screenHeight: 0,
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            queryVisible: false, // 是否显示高级搜索：true 显示，false不显示

            keywords: '',
            tableData: [],
            requestParams: {},

            pages: {
                total: 0,
                currPage: 1,
                pageSize: 20
            },
            // 高级查询-启停用状态
            stateFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '启用' },
                { value: 0, label: '停用' },
            ],
            // 查询对象
            filterData: {
                state: null,
                name: null,
                phone: null,
            },
            // 表单数据
            formData: {
                // 收料人ID
                receiptPersonId: '',
                // 姓名
                name: '',
                // 电话
                phone: '',
                // 状态
                state: '',
                // 备注
                remarks: ''
            },
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入收料人员姓名', trigger: 'blur' }],
                phone: [
                    { required: true, message: '请输入收料人员电话', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
                ],
                state: [{ required: true, message: '请选择收料人员状态', trigger: 'blur' }]
            },
            action: '编辑',
            //
            currentClass: null,
            currentRow: null,
            changedRow: [],
            selectedRows: [],

        }
    },

    created () {
        this.getTableData()
    },
    methods: {
        // 获取列表数据
        async getTableData () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
            }
            if (this.filterData.name != null) {
                params.name = this.filterData.name
            }
            if (this.filterData.phone != null) {
                params.phone = this.filterData.phone
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.keywords != null || this.keywords != '') {
                params.keywords = this.keywords
            }
            getList(params).then(res => {
                if (res) {
                    this.tableData = res.list
                    this.pages.total = res.totalCount
                    this.pages.pageSize = res.pageSize
                    this.pages.currentPage = res.currPage
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                // console.log(this.pages)
                // this.pages = res
            })

            this.viewList = true
        },
        changeSortValue () {
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
            this.logo = ''
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 保存新增/删除
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.action === '编辑') {
                        return this.handleEditData()
                    }
                    this.handleCreateData()
                }
            })
        },
        // 新增表单数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        // 编辑表单数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message === '操作成功') {
                    this.$message.success('操作成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        // 启用/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.receiptPersonId
            })
            if (!this.selectedRows[0]) {
                this.$message.info('未选择数据')
                return
            }
            let warnMsg = num === 1 ? '您确定要启用选中的' + this.alertName + '吗？' : '您确定要停用选中的' + this.alertName + '吗？'
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('启用成功')
                            this.getTableData()
                            // getList(this.requestParams).then(res => {
                            //     if (res.list) {
                            //         this.tableData = res.list
                            //     } else {
                            //         this.clientPop('warn', res.message, () => {
                            //         })
                            //     }
                            //     this.pages = res
                            // })
                        }
                    })
                    break
                case 0:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.$message.success('停用成功')
                            // getList(this.requestParams).then(res => {
                            //     if (res.list) {
                            //         this.tableData = res.list
                            //     } else {
                            //         this.clientPop('warn', res.message, () => {
                            //         })
                            //     }
                            //     this.pages = res
                            // })
                            this.getTableData()
                        }
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        resetSearchConditions () {
            this.filterData.name = null
            this.filterData.phone = null
            this.filterData.state = null
        },
        // 高级查询
        confirmSearch () {
            this.keywords = null
            this.getTableData()
            this.queryVisible = false
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = scope.row
            this.action = '编辑'
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该' + this.alertName + '吗？', async () => {
                showLoading()
                del({ id: scope.row.receiptPersonId }).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                this.$message.info('未选择数据')
                return
            }
            this.clientPop('info', '您确定要删除选中的' + this.alertName + '吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.receiptPersonId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        // this.setUnitMeasur()
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-form-item.uploader {
    height: 200px;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload

/deep/ .el-dialog {
    height: 500px !important;
    padding: 0;

    .el-dialog__header {
        margin-bottom: 20px;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        background: red url(../../../../assets/test.png) no-repeat;

        .el-dialog__title {
            color: #fff;
        }
    }

    .el-dialog__body {
        height: 280px;
        margin-top: 100px;
    }

    .el-dialog__close.el-icon.el-icon-close::before {
        width: 44px;
        height: 44px;
    }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>
