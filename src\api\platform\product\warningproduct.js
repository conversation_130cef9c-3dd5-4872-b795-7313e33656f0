import service from '@/utils/request'

const { httpPost, httpGet } = service

// 新增规则

const getDataList = params => {
    return httpPost({
        url: '/materialMall/productWarning/listByEntity',
        params
    })
}
const findProductWarningById = params => {
    return httpGet({
        url: '/materialMall/productWarning/findById',
        params
    })
}
const dele = params => {
    return httpGet({
        url: '/materialMall/productWarning/delete',
        params
    })
}
const deleteBatch = params => {
    return httpPost({
        url: '/materialMall/productWarning/deleteBatch',
        params
    })
}
// 修改转台
const updateStateBatch = params => {
    return httpPost({
        url: '/materialMall/productWarning/updateState',
        params,
    })
}
// 修改
const update = params => {
    return httpPost({
        url: '/materialMall/productWarning/excel/update',
        params,
    })
}

//立即执行
const executeNowBatch = params => {
    return httpGet({
        url: '/materialMall/productWarning/excel/executeNowBatch',
        params,
    })
}

// 导出excel
export {
    getDataList,
    findProductWarningById,
    dele,
    deleteBatch,
    executeNowBatch,
    updateStateBatch,
    update
}