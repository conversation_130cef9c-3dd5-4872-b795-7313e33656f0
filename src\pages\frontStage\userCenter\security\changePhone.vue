<template>
    <main>
        <div class="list-title dfa">修改手机号</div>
        <div class="content-box" v-loading="loginLoading">
            <!--      进度条      -->
            <div class="progress df">
                <div class="step">
                    <div class="bar">
                        <img :src="currentStage === 0 ? stepTwo : stepOne" alt="">
                        <div class="n">1</div>
                        <div class="step-name" :style="{ color: currentStage === 0 ? '#2A82E4' : '' }">1.手机号验证
                        </div>
                    </div>
                </div>
                <div class="step">
                    <div class="bar">
                        <img :src="currentStage === 1 ? stepTwo : ( currentStage === 0 ? stepThree : stepOne )" alt="">
                        <div class="n">2</div>
                        <div class="step-name" :style="{ color: currentStage === 1 ? '#2A82E4' : '' }">2.更改手机号
                        </div>
                    </div>
                </div>
                <div class="step">
                    <div class="bar">
                        <img :src="currentStage === 2 ? stepTwo : stepThree" alt="">
                        <div class="n">3</div>
                        <div class="step-name" :style="{ color: currentStage === 2 ? '#2A82E4' : '' }">3.完成</div>
                    </div>
                </div>
            </div>
            <!--      验证手机号      -->
            <div class="verify-phone" v-show="currentStage === 0">
                <div class="title">为了确认是您本人操作，请完成以下验证</div>
                <div class="account df">
                    <div>
                        <div class="mb10">昵称：</div>
                        <div class="info">{{ userInfo.userName }}</div>
                    </div>
                    <div>
                        <div class="mb10">手机号：</div>
                        <div class="info">{{ userMobile }}</div>
                    </div>
                </div>
                <div class="code">
                    <div class="mb10">验证码：</div>
                    <div class="df">
                        <el-input v-model="verificationCode" placeholder="请输入短信验证码"></el-input>
                        <button class="btn" @click="codeDialogVisible = true">{{ verifyText1 }}</button>
                    </div>
                </div>
                <button class="next" @click="handleNext">下一步</button>
            </div>
            <!--      设置密码      -->
            <div class="set-password" v-show="currentStage === 1">
                <div class="title">修改新手机号</div>
                <div class="dfb mb20">
                    <span>中国+86</span>
                    <el-input v-model="newPhone" placeholder="请输入新手机号">
                    </el-input>
                </div>
                <div class="df mb20">
                    <el-input v-model="verificationCode" placeholder="请输入短信验证码"></el-input>
                    <button class="btn" @click="codeDialogVisible = true">{{ verifyText2 }}</button>
                </div>
                <button @click="handleSubmit">提交</button>
            </div>
            <!--      完成      -->
            <div class="complete" v-show="currentStage === 2">
                <img src="@/assets/images/userCenter/zc_chenggong.png" alt="">
                <div>新手机号修改成功</div>
            </div>
        </div>
        <Dialog title="图形验证码" :close-on-click-modal="false" width="40%" top="30vh" :visible.sync="codeDialogVisible" @open="getCodeImg">
            <div class="verifyBox dfc">
                <template v-if="currentStage === 0">
                    <el-input v-model="verification[0].verifyInput" placeholder="请输入图形验证码"/>
                    <img class="pointer" :src="verification[0].verifyImg" @click="getCodeImg" alt="">
                </template>
                <template v-else>
                    <el-input v-model="verification[1].verifyInput" placeholder="请输入图形验证码"/>
                    <img class="pointer" :src="verification[1].verifyImg" @click="getCodeImg" alt="">
                </template>
            </div>
            <span slot="footer">
                <el-button
                    class="codeDialogBtn" style="margin-right: 30px;" @click="codeDialogVisible = false"
                >取消</el-button>
                <el-button class="codeDialogBtn" type="primary" @click="checkCode">确定</el-button>
            </span>
        </Dialog>
    </main>
</template>

<script>
import { mapState } from 'vuex'
import ico_hide from '@/assets/images/ico_hide.png'
import ico_show from '@/assets/images/ico_show.png'
import { checkUpdatePhoneCode, updatePhone, updatePhoneSendCode, getPrivateKeyId, getSendCodeImg, checkSendCodeVerify } from '@/api/frontStage/login'
import { loginOut } from '@/api/frontStage/userCenter'
import { encrypt } from '@/utils/common'
import Dialog from '@/pages/frontStage/components/dialog.vue'

export default {
    name: 'changePhone',
    components: { Dialog },
    data () {
        return {
            loginLoading: false,
            currentStage: 0,
            stepOne: require('@/assets/images/userCenter/step_1.png'),
            stepTwo: require('@/assets/images/userCenter/step_2.png'),
            stepThree: require('@/assets/images/userCenter/step_3.png'),
            verifyText1: '短信验证码',
            verifyText2: '短信验证码',
            verificationCode: '',
            ico_hide,
            ico_show,
            hidePass: ico_hide,
            newPhone: '',
            codeDialogVisible: false,
            verification: [
                { verifyId: '', verifyInput: '', verifyImg: '' },
                { verifyId: '', verifyInput: '', verifyImg: '' },
            ]
        }
    },
    computed: {
        ...mapState(['userInfo']),
        userMobile () {
            let tel = this.userInfo.userMobile + ''
            return tel.substring(0, 4) + '****' + tel.substring(8)
        },
    },
    created () {
    },
    methods: {
        getCodeImg () {
            this.verification[this.currentStage].verifyInput = ''
            getSendCodeImg().then(res => {
                let blob = new Blob([res.data], { type: 'image/jpeg' })
                this.verification[this.currentStage].verifyImg = window.URL.createObjectURL(blob)
                this.verification[this.currentStage].verifyId = res.headers.verifyid
            })
        },
        checkCode () {
            let { verifyId, verifyInput } = this.verification[this.currentStage]
            checkSendCodeVerify({ id: verifyId, verifyInput }).then(res => {
                if(res.code !== 200) return
                this.codeDialogVisible = false
                this.getVerificationCode()
            })
        },
        handleNext () {
            this.loginLoading = true
            checkUpdatePhoneCode({ phone: this.userInfo.userMobile, code: this.verificationCode }).then(res => {
                if(res.code == 200) {
                    this.verificationCode = null
                    this.loginLoading = false
                    this.currentStage = 1
                }
                this.loginLoading = false
            }).catch(() =>{
                this.loginLoading = false
            })
        },
        async getPrivateKey (phone) {
            let res = await getPrivateKeyId({ phone })
            if(typeof res !== 'string' || !res.length > 0) return ''
            return encrypt(res.verification)
        },
        async getVerificationCode () {
            let isNewPhone = this.currentStage === 1
            let phone = isNewPhone ? this.newPhone : this.userInfo.userMobile
            let privateKeyId = await this.getPrivateKey(phone)
            if(!privateKeyId) return

            if(isNewPhone) {
                let len = this.newPhone.trim().length
                if (len !== 11) {
                    return this.$message.error('请输入11位手机号码！')
                }
                // if (!new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(this.newPhone)) {
                //     return this.$message.error('请输入正确的手机号码！')
                // }
            }

            this.loginLoading = true
            updatePhoneSendCode({ phone, privateKeyId }).then(res => {
                if (res.code !== 200) return
                this.$message.success('发送成功')
                isNewPhone ? this.handleNewCountdown() : this.handleOldCountdown()
            }).finally(() => {
                this.loginLoading = false
            })
        },
        handleOldCountdown () {
            let countdown = 60
            let timer = setInterval(() => {
                if (countdown === 0) {
                    this.verifyText1 = '获取验证码'
                    return clearInterval(timer)
                }
                this.verifyText1 = `倒计时 ${countdown}`
                countdown -= 1
            }, 1000)
        },
        handleNewCountdown () {
            let countdown = 60
            let timer = setInterval(() => {
                if (countdown === 0) {
                    this.verifyText2 = '获取验证码'
                    return clearInterval(timer)
                }
                this.verifyText2 = `倒计时 ${countdown}`
                countdown -= 1
            }, 1000)
        },
        toggleShowPass () {
            this.hidePass = this.hidePass === this.ico_hide ? this.ico_show : this.ico_hide
            this.pwType = this.pwType === 'text' ? 'password' : 'text'
        }
        ,
        handleSubmit () {
            this.loginLoading = true
            updatePhone({ newPhone: this.newPhone, code: this.verificationCode }).then(res =>{
                if(res.code == 200) {
                    this.currentStage = 2
                    this.loginLoading = false
                    loginOut().then(() => {
                        localStorage.removeItem('token')
                        this.$store.commit('setUserInfo', {})
                        window.location.reload()
                    })
                }
                this.loginLoading = false
            }).catch(() => {
                this.loginLoading = false
            })
        }
        ,
    }
}
</script>

<style scoped lang="scss">
main {
    border: 1px solid rgba(229, 229, 229, 1);
}
.list-title {
  height: 50px;
  padding: 15px 19px 15px 21px;
  font-size: 20px;
  line-height: 20px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);
  position: relative;

  &::before {
    width: 3px;
    height: 20px;
    margin-right: 10px;
    content: '';
    display: block;
    background-color: rgba(33, 110, 198, 1);
  }

  .msg {
    margin-left: 20px;
    font-size: 12px;
    color: #808080;

    span {
      color: #216EC6;
    }
  }

  .add-addr {
    width: 120px;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(33, 110, 198, 1);
    // border: 1px solid rgba(33, 110, 198, 1);
    justify-content: center;
    position: absolute;
    top: 10px;
    right: 19px;
    user-select: none;

    &:active {
      color: #fff;
      background-color: rgba(33, 110, 198, 1);
    }
  }
}
.content-box {
    height: 600px;
    padding-top: 40px;

    .progress {
        justify-content: center;

        .step {
            width: 240px;
            margin-right: -4px;

            .bar {
                height: 30px;
                position: relative;
                justify-content: center;
                text-align: center;

                .n {
                    height: 30px;
                    margin: -30px 0 15px;
                    font-size: 12px;
                    line-height: 26px;
                    color: #fff;
                }

                .step-name {
                    font-size: 14px;
                    color: #88B4E5;
                }
            }
        }
    }

    .verify-phone, .set-password, .complete {
        margin-top: 30px;
        padding-top: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .title {
            margin-bottom: 40px;
            font-size: 18px;
            color: #333;
        }

        button {
            height: 35px;
            box-sizing: border-box;
        }

        .btn {
            width: 100px;
            margin-left: 10px;
            border: 1px solid #216EC6;
            color: #216EC6;
            background-color: #fff;
        }
    }

    .verify-phone {
        .account {
            width: 270px;
            margin-bottom: 40px;
            font-size: 14px;

            & > div:first-child {
                width: 142px;
            }

        }

        .mb10 {
            color: #999;
        }

        /deep/ .el-input {
            margin-right: 10px;
            &, .el-input__inner {
                width: 160px;
            }
        }
        button:not(.next) {
            width: 100px;
            border: 1px solid #216EC6;
            color: #216EC6;
            background-color: #fff;
        }
        .next {
            width: 270px;
            margin-top: 20px;
            background-color: #216EC6;
            color: #fff;
        }
    }

    .set-password {
        .dfb {
            width: 270px;
            height: 35px;
            align-items: center;
        }

        /deep/ .dfb .el-input {

            &, .el-input__inner {
                width: 200px;

                .el-input__suffix {
                    right: 8px;
                }

                .input-icon {
                    width: 14px;
                }
            }
        }

        button:not(.btn) {
            width: 270px;
            background-color: #216EC6;
            color: #fff;
        }
    }

    .complete {
        img {
            width: 60px;
            height: 60px;
            margin: 60px 0 30px;
        }

        div {
            font-size: 22px;
            color: #333;
        }
    }
}

/deep/ .el-input {
    &, .el-input__inner {
        width: 160px;
        height: 35px !important;
        border-radius: 0;

        .el-input__inner {
            border: 1px solid #CCC;
        }
    }
}

/deep/ .el-button.codeDialogBtn {
    width: 90px;
    line-height: 40px;
    font-size: 20px;
    height: 40px;
    border-radius: 0;
}

.verifyBox {
    width: 100%;

    /deep/ .el-input {
        width: 250px;
        height: 50px !important;
        border-radius: 0;

        .el-input__inner {
            width: 250px;
            height: 50px !important;
            border-radius: 0;
        }
    }

    img {
        width: 140px;
        height: 50px;
        border: 1px solid lightgray;
    }

    /deep/ .el-button {
        padding: 0 0;
        text-align: center;
        font-size: 16px;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 0;
        color: rgba(33, 110, 198, 1);
        background-color: #fff;
    }
}

</style>
