<template>
    <div class="articile p20">
        <div class="list">
            <div class="item mb20" v-for="(item, i) in list" :key="i" @click="$router.push({ path: '/mFront/webInfoListDetail', query: { key: $route.query.key, id: item.contentId, } })">
                    {{ `${i + 1}. ${item.title}` }}
            </div>
        </div>
        <pagination
            :pageSize="page.pageSize"
            :total="page.total"
            :totalPage="page.totalPage"
            :currentPage="page.currPage"
            :destination="page.destination"
            @currentChange="currentChange"
            @sizeChange="sizeChange"
            ></pagination>
    </div>
</template>
<script>
import pagination from '../components/pagination.vue'
import { getWebInfo } from '@/api/frontStage/webInfo'
import { switchTitle } from '../../../utils/common'
export default {
    components: { pagination },
    data () {
        return {
            list: [],
            page: {
                currPage: 1,
                total: 0,
                pageSize: 9,
                destination: 2,
                totalPage: 1
            }
        }
    },
    watch: {
        // eslint-disable-next-line
        $route (route) {
            this.getData()
            document.title = '物资采购平台-' + switchTitle(this.$route.query.key)
        }
    },
    created () {
        this.getData()
        document.title = '物资采购平台-' + switchTitle(this.$route.query.key)
    },
    methods: {
        getData () {
            let params = { page: 1, limit: 9, mallType: '0', state: 1, orderBy: 3, programaKey: this.$route.query.key }
            getWebInfo(params).then(res => {
                /*if(!res.list[0]) {
                    console.log('暂无数据')
                }*/
                this.list = res.list
                this.page.total = res.totalCount
                this.page.totalPage = res.totalPage
            })
        },
        currentChange (page) {
            page
        },
        sizeChange (size) {
            size
        },
    },
}
</script>
<style scoped lang="scss">
.articile {
    min-height: 896px;
    .list {min-height: 776px;}
}
.item {
    height: 55px;
    padding-left: 20px;
    font-size: 16px;
    line-height: 55px;
    border: 1px solid rgba(229, 229, 229, 1);
    cursor: pointer;
}
</style>