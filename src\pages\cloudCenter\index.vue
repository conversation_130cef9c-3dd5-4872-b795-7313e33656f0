<template>
    <main class="df">
        <el-menu
            default-active="2"
            class="cloud-menu"
            background-color="#28333e"
            text-color="#fff"
            active-text-color="#409eff"
            :collapse="menuCollapse"
            unique-opened
            router
        >
            <div class="menu-title">商城云中心</div>
            <el-menu-item index="/cloudCenter/menuManage">
                <template slot="title"><i class="el-icon-menu"></i>菜单管理</template>
            </el-menu-item>
            <el-menu-item index="/cloudCenter/roleManage">
                <template slot="title"><i class="el-icon-s-custom"></i>角色管理</template>
            </el-menu-item>
        </el-menu>
        <div class="router-box">
            <div class="dfb p20 page-header">
                <i class="el-icon-refresh-right pointer" @click="$router.go(0)"/>
                <span>
                    <i class="el-icon-user pointer"/>
                    <i class="el-icon-switch-button pointer" @click="logout"/>
                </span>
            </div>
            <div style="flex-grow: 1">
                <router-view/>
            </div>
        </div>
    </main>
</template>

<script>
import { mapMutations } from 'vuex'
export default {
    name: 'cloudCenter',
    data () {
        return {
            menuCollapse: false,
        }
    },
    methods: {
        ...mapMutations(['setCToken']),
        goBack () {
            this.$router.go(-1)
        },
        logout () {
            this.setCToken('')
            this.$router.push('/cLogin')
        },
    },
    created () {},
}
</script>

<style scoped lang="scss">
.cloud-menu {
    height: 100vh;
    .menu-title {
        //height: 70px;
        padding: 20px;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        //line-height: 70px;
        color: #488edc;
    }
    /deep/ .el-menu-item  {
        width: 230px;
        line-height: 56px;
        height: 56px;
    }
}
.router-box {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    .page-header {
        border-bottom: 1px solid rgba(230, 230, 230, 1);
        i {
            font-size: 18px;
        }
        .el-icon-switch-button {
            margin-left: 20px;
        }
    }
}
.content {
    height: 100%;
    background-color: #f5f5f5;
}
/deep/ .el-table {
    .el-icon-delete {
        font-size: 16px;
        color: #fff;
    }
}
///deep/ .el-button {
//    border-radius: 0;
//}
/deep/ .el-input .el-input__inner{
    border-radius: 0 !important;
}
/deep/ .el-form {
    width: 80%;
    margin: 0 auto;
    .el-form-item, .el-form-item__content {
        width: unset;
    }
}
/deep/ .el-pagination {
    padding: 10px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
</style>