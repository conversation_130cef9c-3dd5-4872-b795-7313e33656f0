<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-show="viewList != 'class'">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane  label="基本信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="回复信息" name="filesInfo" :disabled="clickTabFlag" v-if="formData.respondContent!=null">
                </el-tab-pane>
                <!--        <el-tab-pane label="收件人信息" name="receive" :disabled="clickTabFlag" v-if="formData.checkState != 2">-->
                <!--        </el-tab-pane>-->
                <div id="tabs-content">
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">基本信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="留言人：">
                                            <span>{{ formData.messageName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <!--                  <el-col :span="12">-->
                                    <!--                    <el-form-item label="昵称：">-->
                                    <!--                      <span>{{ formData.messageName }}</span>-->

                                    <!--                    </el-form-item>-->

                                    <!--                  </el-col>-->
                                    <el-col :span="12">
                                        <el-form-item label="是否是内部用户：">
                                            <template >
                                                <el-tag v-if="formData.isInterior==0">否</el-tag>
                                                <el-tag v-else-if="formData.isInterior==1" >是</el-tag>
                                            </template>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="公司名称：">
                                            <span>{{ formData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="店铺名称：">
                                            <span>{{ formData.shopName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="发送时间：">
                                            <span>{{ formData.messagDate }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item label="状态：">
                                            <el-tag v-if="formData.state==0" type="danger">未处理</el-tag>
                                            <el-tag v-if="formData.state==1" >已处理</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="留言内容：" prop="productDescribe">
                                            <!--                        <editor2  v-model="formData.messagContent" :disabled ='true'></editor>-->
<!--                                            <editor  v-model="formData.messagContent" :disabled ='true'></editor>-->
                                            <span class="receiveText" >{{ formData.messagContent }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <div id="filesInfo" class="con" v-if="formData.respondContent!=null">
                            <div class="tabs-title" id="filesInfo">回复信息</div>
                            <div style="width: 100%" class="form">
                                <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="回复人：">
                                                <span>{{ formData.respondName }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="回复时间：">
                                                <span>{{ formData.respondDate }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="回复内容：">
                                                <span class="receiveText">{{ formData.respondContent }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </div>
                        </div>
                    </div>
                    <!--          <div id="receiveCon" class="con" v-if="formData.checkState != 2">-->

                    <!--            分页-->
                    <!--            <Pagination-->
                    <!--                v-show="receive.tableData != null || receive.tableData.length != 0"-->
                    <!--                :total="receive.paginationInfo.totalCount"-->
                    <!--                :pageSize.sync="receive.paginationInfo.pageSize"-->
                    <!--                :currentPage.sync="receive.paginationInfo.currentPage"-->
                    <!--                @currentChange="getReceiveList"-->
                    <!--                @sizeChange="getReceiveList"-->
                    <!--            />-->
                    <!--          </div>-->
                </div>
            </el-tabs>
            <div class="buttons">
                <el-button type="primary" @click="onWriter" >写回复</el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>

        <!-- 写回复-->
        <div class="right" v-show="viewList !== true"  style="padding-top: 70px;">
            <div class="e-form" style="padding: 0 10px 10px;" v-if="viewList === 'class'">
                <div class="tabs-title">写回复</div>
                <el-form :rules="formRules" ref="writer" :model="formData" label-width="150px">
                    <el-row>
                        <!--            <el-col :span="12">-->
                        <!--              <el-input clearable clean v-model="formData.title" placeholder="请输入标题"></el-input>-->
                        <!--              <el-form-item label="回复标题：" prop="title">
                                        <el-input clearable clean v-model="formData.title" placeholder="请输入标题"></el-input>
                                      </el-form-item>-->
                        <!--            </el-col>-->
                    </el-row>
                    <el-row>
                        <el-col class="editorCol" :span="24">
                            <el-form-item label="回复内容：" prop="content">
                                <input v-model="formData.content" class="receiveText">
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="buttons">
                    <el-button v-if="viewList === 'class'" type="primary" @click="sendMessage">确定</el-button>
                    <el-button @click="handleClose">返回</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { mapState, mapMutations } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
// import editor from '../../../../components/quillText'
import { queryAllPage, sendRespond } from '@/api/platform/mail/outbox'
import { previewFile } from '@/api/platform/common/file'
import { selectFileList } from '@/api/base/file'
// import editor2 from '../../../../components/quillEditor'
// import ComPagination from '@/components/pagination/pagination.vue'
// import Pagination from '@/components/pagination/pagination.vue'
export default {
    data () {

        return {
            sendMessageTable: [], //接收消息列表
            //基本信息表单数据
            fileList: [],
            // 表格数据
            isRead: null,
            receive: {
                type: 1,
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    totalCount: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            formRules: {
                title: { required: true, message: '请输入标题', trigger: 'blur' },
                content: { required: true, message: '请输入回复消息', trigger: 'blur' },
            },
            receiveTypes: [
                {
                    value: 1,
                    label: '用户'
                },
                {
                    value: 0,
                    label: '店铺'
                }
            ],
            isReads: [
                {
                    value: null,
                    label: '全部'
                },
                {
                    value: 0,
                    label: '未读'
                },
                {
                    value: 1,
                    label: '已读'
                }],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            viewList: true,
            formData: {
                publicDisplay: 0,
                respondId: null,
                respondName: null,
                respondContent: null,
                respondTitle: null, //标题
                messageId: '',
                receiveType: '', // 收件人类型0店铺 1用户2平台
                receiveUserList: [], //收件人id,
                receiveShopList: [], //商铺id
                receiveUserListTitle: [], //用户展示字段收件人
                title: null,
                content: null,
                files: [],
                messageInformationsId: null
            },
            test: { id: '111' }
        }
    },
    components: {
        // editor,
    },
    created () {
        this.formData = this.$route.params.row
        // this.getReceiveList()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'receive']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        //发送消息
        sendMessage () {
            let params = {
                messagContent: this.formData.content,
                messageInformationsId: this.formData.messageInformationsId
            }
            this.$confirm('您确定要回复吗？\n\r\u000d\t                  ' +
                '提示：确定之后会立即发布，留言用户将会可见回复内容', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                if (params.messagContent != null) {
                    sendRespond(params).then(res => {
                        if (res.code == 200) {
                            this.$message({
                                message: '回复成功',
                                type: 'success'
                            })
                        }
                        this.$router.replace('/platform/mail/message')
                        this.formData = {}
                        this.viewList = true
                        this.getsendList()
                    }
                    )}else{
                    this.$message({
                        message: '回复失败,请输入回复内容',
                        type: 'error'
                    })
                }

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消回复'
                })
            })
        },
        /**
         * 查询所有收件人列表
         *
         * 消息列表
         */
        getsendList () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                sendType: 2
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.filterData.allRead != null) {
                params.allRead = this.filterData.allRead
            }
            if (this.filterData.title != null && this.filterData.title != '') {
                params.title = this.filterData.title
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0],
                params.endDate = this.filterData.dateValue[1]
            }
            queryAllPage(params).then(res => {
                this.sendMessageTable = res.list
                this.pages = res
            })
        },
        onWriter () {
            // this.emptyForm()
            this.viewList = 'class'
        },
        emptyForm () {
            for (let key in this.formData) {
                if(!key.includes('file')) this.formData[key] = null
            }
        },

        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        getFileInfos (relevanceId) {
            let params = {
                relevanceId: relevanceId,
                relevanceType: 7,
            }
            selectFileList(params).then(res=>{
                this.fileList = res.list
            })
        },
        // // 获取收件人信息
        // getReceiveList () {
        //     let params = {
        //         page: this.receive.paginationInfo.currentPage,
        //         limit: this.receive.paginationInfo.pageSize,
        //         stationMessageId: this.formData.stationMessageId
        //     }
        //     if(this.isRead != null) {
        //         params.isRead = this.isRead
        //     }
        //     if(this.receive.keywords != null) {
        //         params.keywords = this.receive.keywords
        //     }
        //     queryAllPage(params).then(res => {
        //         this.receive.tableData = res.list
        //         this.receive.paginationInfo = res
        //     })
        // },

        //取消
        handleClose () {
            this.$router.replace('/platform/mail/message')
        },
        replyClose () {
            this.viewList === 'true'
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
.receiveText{
    background-color: white;
    font-size:15px;
    width:815px;
    height:304px;
    border:solid 1px #cbc3c3;
    margin-right: 30px;
    padding: 20px
}
</style>
