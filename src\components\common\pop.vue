<template>
    <div>
        <span style="display: none">{{ localPopConfirm }}</span>
    </div>
</template>

<script>
// 组件已注册在vue原型，不需要引用
// 使用方法:
// this.clientPop(
//     'info',   (提示info、成功suc、错误err)
//     `提示文本`,
//     回调方法,  (传方法，不是执行，不需要就不传)
//     回调参数   (不需要就不传)
// )

import { showPromptPop, showSuccessPop, showErrorPop, showErrorDetailPop, showWarningPop } from '@/utils/common'
import { mapState, mapMutations } from 'vuex'

export default {
    props: {},
    data () {
        return {
            callBack: null,
            arg: null,
            cancelCallBack: null
        }
    },
    computed: {
        ...mapState(['comPopConfirm', 'popConfirm']),
        // 点击提示窗“确定”按钮后的回调
        localPopConfirm () {
            if (this.comPopConfirm) {
                try {
                    // 确定
                    if (this.popConfirm) {
                        if (this.callBack) {
                            if (this.arg !== null) {
                                this.callBack(this.arg)
                            } else {
                                this.callBack()
                            }
                            // 解决下个弹窗没取消回调，会使用上个弹窗取消回调bug
                            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                            this.cancelCallBack = null
                        }
                    } else {
                        // 取消
                        if (this.cancelCallBack) {
                            if (this.arg !== null) {
                                this.cancelCallBack(this.arg)
                            } else {
                                this.cancelCallBack()
                            }
                            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                            this.cancelCallBack = null
                        }
                    }
                } catch (error) {
                    alert(error.message)
                }
                this.setComPopConfirm(false)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.callBack = null
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.arg = null
            }
            return this.popConfirm
        }
    },
    created () {},
    methods: {
        ...mapMutations(['setComPopConfirm']),
        pop (popType, popTxt, callBack = null, arg = null, cancelCallBack = null, title) {
            if (callBack !== null) this.callBack = callBack
            if (cancelCallBack !== null) {
                this.cancelCallBack = cancelCallBack
            }else if( cancelCallBack === null && ( popType === 'success' || popType === 'suc' ) && callBack !== null) {
                this.cancelCallBack = callBack
                cancelCallBack = callBack
            }
            if (arg !== null) this.arg = arg
            if (popType === 'prompt' || popType === 'info') {
                showPromptPop(popTxt, callBack, cancelCallBack)
            } else if (popType === 'success' || popType === 'suc') {
                showSuccessPop(popTxt, callBack, cancelCallBack)
            } else if(popType === 'errorDetail' || popType === 'errDt') {
                showErrorDetailPop(popTxt, callBack, cancelCallBack, title)
            }else if(popType === 'warning' || popType === 'warn') {
                showWarningPop(popTxt, callBack, cancelCallBack)
            }
            else{
                showErrorPop(popTxt, callBack, cancelCallBack)
            }
        }
    }
}
</script>

<style lang="sass" scoped></style>
