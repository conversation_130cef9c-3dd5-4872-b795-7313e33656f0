<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="企业信息" name="enterpriseInfo" :disabled="clickTabFlag" v-if="showEnterprise">
                </el-tab-pane>

                <el-tab-pane label="个体户信息" name="individualHouseholdInfo" :disabled="clickTabFlag" v-if="showIndividualHousehold">
                </el-tab-pane>
                <el-tab-pane label="附件信息" name="filesInfo" :disabled="clickTabFlag" >
                </el-tab-pane>

                <el-tab-pane label="管理员信息" name="adminInfo" :disabled="clickTabFlag" >
                </el-tab-pane>

<!--                <el-tab-pane label="账号信息" name="accountInfo" :disabled="clickTabFlag">-->
<!--                </el-tab-pane>-->
                <el-tab-pane label="店铺信息" name="shopInfo" :disabled="clickTabFlag" v-if="showShop">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 企业信息 -->
                    <div id="enterpriseInfoCon" class="con" v-if="showEnterprise">
                        <div class="tabs-title" id="enterpriseInfo">企业信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="enterpriseData" :label-width="formLabelWidth" :rules="{}" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="企业名称：" prop="enterpriseName">
                                            <span>{{ formData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                            <span>{{ formData.socialCreditCode }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商：" prop="socialCreditCode">
                                            {{ formData.isSupplier == 2 ? '是' : '否' }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="法定代表人：" prop="legalRepresentative">
                                            <span>{{ formData.legalRepresentative }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="注册资本：" prop="registeredCapital">
                                            <span>{{ formData.registeredCapital }}(万元)</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册地址：" prop="detailedAddress">
                                            <span>{{ formData.detailedAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="注册日期：" prop="creationTime">
                                            <span>{{ formData.creationTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" >
                                        <el-form-item label="营业执照：" prop="businessLicense" v-if="businessLicenseHidden">
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" align="center"  >
                                        <el-image style="width: 900px; height: 500px" :src=" formData.businessLicense"></el-image>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div id="filesInfo" class="con" v-loading="openShopLoading">
                        <div class="tabs-title" id="baseInfo">附件资料</div>
                        <div class="e-table"  style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-button type="primary" @click="batchDownloadFile">批量下载
                                    </el-button>
                                    <el-button type="primary" @click="batchDownloadFilePackage">批量下载并打包
                                    </el-button>
                                </div>
                            </div>
                            <el-table ref="fileTableRef"
                                      border
                                      style="width: 100%"
                                      :data="fileList"
                                      class="table"
                                      @row-click="handleCurrentInventoryClick"
                                      @selection-change="selectionChangeHandle"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="name" label="附件名称" width=""></el-table-column>
                                <el-table-column label="操作" width="100">
                                    <template slot-scope="scope">
                                        <el-button type="primary" class="btn-greenYellow" @click="openShopDow(scope.row)">下载
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <!-- 个体户信息 -->
                    <div id="individualHouseholdInfoCon" class="con" v-if="showIndividualHousehold">
                        <div class="tabs-title" id="individualHouseholdInfo">个体户信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="enterpriseData" :label-width="formLabelWidth" :rules="{}" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="名称：" prop="enterpriseName">
                                            <span>{{ formData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                            <span>{{ formData.socialCreditCode }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商：" prop="socialCreditCode">
                                            {{ formData.isSupplier == 2 ? '是' : '否' }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="经营者姓名：" prop="operator">
                                            <span>{{ formData.operator }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="经营场所：" prop="placeOfBusiness">
                                            <span>{{ formData.placeOfBusiness }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="注册日期：" prop="creationTime">
                                            <span>{{ formData.creationTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="经营范围：" prop="mainBusiness">
                                            <span>{{ formData.mainBusiness }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" >
                                        <el-form-item label="营业执照：" prop="businessLicense" v-if="businessLicenseHidden">
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" align="center"  >
                                        <el-image style="width: 900px; height: 500px" :src="formData.businessLicense"></el-image>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 管理员信息 -->
                    <div id="adminInfoCon" class="con" v-loading="openShopLoading" >
                        <div class="tabs-title" id="adminInfo">管理员信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="enterpriseData" :label-width="formLabelWidth" :rules="{}" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="姓名：" prop="adminName" >
                                            <span>{{ formData.adminName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="手机号码：" prop="adminPhone">
                                            <span>{{ formData.adminPhone }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="身份证号码：" prop="adminNumber">
                                            <span>{{ formData.adminNumber }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="身份证人像面："  prop="cardPortraitFace" v-if="showCardPortraitFace">
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="身份证国徽面："  prop="cardPortraitNationalEmblem" v-if="showCardPortraitFace">
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12" align="center" >
                                        <el-image style="width: 360px; height: 200px" :src="formData.cardPortraitFace"></el-image>
                                    </el-col>
                                    <el-col :span="12" align="center">
                                        <el-image style="width: 360px; height: 200px" :src="formData.cardPortraitNationalEmblem"></el-image>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>

                    <!-- 账号信息-->
<!--                    <div id="accountInfoCon" class="con">-->
<!--                        <div class="tabs-title" id="accountInfo">账号清单</div>-->
<!--&lt;!&ndash;                        <div style="padding:0 10px">&ndash;&gt;-->
<!--&lt;!&ndash;                            <div class="mainTitle">账号清单</div>&ndash;&gt;-->
<!--&lt;!&ndash;                        </div>&ndash;&gt;-->
<!--                        <div class="e-table" style="background-color: #ffffff">-->
<!--&lt;!&ndash;                            <div class="top">&ndash;&gt;-->
<!--&lt;!&ndash;                                <div class="left">&ndash;&gt;-->
<!--&lt;!&ndash;                                    <div>&ndash;&gt;-->
<!--&lt;!&ndash;                                        <el-button size="small" type="primary" class="btn-greenYellow" plain @click="addData">新增</el-button>&ndash;&gt;-->
<!--&lt;!&ndash;                                        <el-button size="small" class="btn-greenYellow" plain @click="dialogVisible = true">基础库导入&ndash;&gt;-->
<!--&lt;!&ndash;                                        </el-button>&ndash;&gt;-->
<!--&lt;!&ndash;                                        <el-button size="small" type="danger" class="btn-delete" plain @click="deleteData">批量删除</el-button>&ndash;&gt;-->
<!--&lt;!&ndash;                                    </div>&ndash;&gt;-->
<!--&lt;!&ndash;                                </div>&ndash;&gt;-->
<!--&lt;!&ndash;                            </div>&ndash;&gt;-->
<!--                            <div>-->
<!--                                <div class="errorMsg" v-if="false">-->
<!--                                    <span></span>-->
<!--                                </div>-->
<!--                                <el-table ref="tableRef" border style="width: 100%" :data="userData" class="table"-->
<!--                                          :max-height="$store.state.tableHeight">-->
<!--                                    <el-table-column label="序号" type="index" width="60"></el-table-column>-->
<!--                                    <el-table-column label="用户编号" width="200">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            {{scope.row.userNumber}}-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column label="姓名" width="">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            {{scope.row.realName}}-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column label="性别" width="">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            <span v-if="scope.row.gender == '0'">女</span>-->
<!--                                            <span v-else-if="scope.row.gender == '1'">男</span>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column label="账号" width="140">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            {{scope.row.account}}-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column label="手机号码" width="140">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            {{scope.row.userMobile}}-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column  label="邮箱" width="">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            {{scope.row.email}}-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column  label="上次登录时间" width="180">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            {{scope.row.gmtLogin}}-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                </el-table>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <!-- 店铺信息-->
                    <div id="shopInfoCon" class="con" v-if="showShop">
                        <div class="tabs-title" id="shopInfo">店铺清单</div>
<!--                        <div style="padding:0 10px">-->
<!--                            <div class="mainTitle">店铺清单</div>-->
<!--                        </div>-->
                        <div class="e-table" style="background-color: #ffffff">
                            <div class="top">
                                <div class="left">
<!--                                    <div>-->
<!--                                        <el-button size="small" type="primary" class="btn-greenYellow" plain @click="addData">新增</el-button>-->
<!--                                        <el-button size="small" class="btn-greenYellow" plain @click="dialogVisible = true">基础库导入-->
<!--                                        </el-button>-->
<!--                                        <el-button size="small" type="danger" class="btn-delete" plain @click="deleteData">批量删除</el-button>-->
<!--                                    </div>-->
                                </div>
                            </div>
                            <div>
                                <div class="errorMsg" v-if="false">
                                    <span></span>
                                </div>
                                <el-table ref="tableRef" border style="width: 100%" :data="shopData" class="table"
                                          :max-height="$store.state.tableHeight">
                                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                                    <el-table-column label="店铺名称" width="200">
                                        <template slot-scope="scope">
                                            {{scope.row.shopName}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="所属城市" width="">
                                        <template slot-scope="scope">
                                            {{ scope.row.city }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="店铺地址" width="260">
                                        <template slot-scope="scope">
                                            {{ scope.row.province }}{{ scope.row.county }}{{ scope.row.detailedAddress }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="店铺类型" width="">
                                        <template slot-scope="scope">
                                            <span v-if="scope.row.shopType == '2'">个人</span>
                                            <span v-else-if="scope.row.shopType == '0'">个体户</span>
                                            <span v-else-if="scope.row.shopType == '1'">企业</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="店铺状态" width="">
                                        <template slot-scope="scope">
                                            <!--                            {{ scope.row.state == 1 ? '启用' : '停用' }}-->
                                            <span v-if="scope.row.state == '1'">启用</span>
                                            <span v-else-if="scope.row.state == '0'">停用</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="自营店铺" width="">
                                        <template slot-scope="scope">
                                            {{ scope.row.isBusiness == 1 ? '自营' : '非自营' }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="供应商" width="">
                                        <template slot-scope="scope">
                                            {{ scope.row.isSupplier == 1 ? '是' : '否' }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="内部店铺" width="">
                                        <template slot-scope="scope">
                                            {{ scope.row.isInternalShop == 1 ? '是' : '否' }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="路桥结算" width="">
                                        <template slot-scope="scope">
                                            <span v-if="scope.row.isInternalSettlement == '1'">支持</span>
                                            <span v-else-if="scope.row.isInternalSettlement == '0'">不支持</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="店铺审核状态" width="150">
                                        <template slot-scope="scope">
                                            <span v-if="scope.row.auditStatus == '1'">审核通过</span>
                                            <span v-else-if="scope.row.auditStatus == '2'">未审核</span>
                                            <span v-else-if="scope.row.auditStatus == '3'">审核未通过</span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button @click="$router.push('/platform/enterprise/enterpriseManager')">返回</el-button>
        </div>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import { mapState, mapMutations } from 'vuex'
import $ from 'jquery'
import { hideLoading, throttle } from '@/utils/common'
import { getUserList } from '@/api/platform/user/userInquire'
import { getShopList } from '@/api/platform/shop/shopManager'
import { previewFile } from '@/api/platform/common/file'
import { getEnterPriseFileList } from '@/api/base/file'
import JSZip from'jszip'
import FileSaver from'file-saver'

export default {
    data () {
        return {
            openShopLoading: false,
            fileList: [],
            dialogVisible: false,
            topHeight: 120,
            //基本信息表单数据
            formData: {
                mallType: 0,
            },
            enterpriseData: {
            },
            // 账号数据
            userData: [],
            // 店铺数据
            shopData: [],
            formLabelWidth: '195px',
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            rulesBase: {
            },
            disabled: false, //是否禁止
            auditState: {}, //流程按钮显示状态
            auditParams: {
                'billId': this.$route.query.billid,
                'billType': 7024
            },
            isMonitorTaxRate: false, //是否计算税率
            isview: false, //是否隐藏底部按钮
            businessLicenseHidden: true, //企业营业执照图片显示
            showCardPortraitFace: true, //管理员省份证图片显示
            showIndividualHousehold: false, //个体户展示
            showEnterprise: false, //企业展示
            showShop: true, //店铺清单
            changedInfo: [],
            beforePath: '',
        }
    },
    async mounted () {
        if (this.$route.query.isview === 'true' || this.$route.query.isview === 'false') {
            this.isview = JSON.parse(this.$route.query.isview)
        }
        //接收上一个页面传递的参数
        this.formData = this.$route.params.row.ent
        previewFile({ recordId: this.formData.businessLicenseId }).then(res => {
            let url = window.URL.createObjectURL(res)
            this.formData.businessLicense = url
        })
        previewFile({ recordId: this.formData.cardPortraitFaceId }).then(res => {
            let url = window.URL.createObjectURL(res)
            this.formData.cardPortraitFace = url
        })
        previewFile({ recordId: this.formData.cardPortraitNationalEmblemId }).then(res => {
            let url = window.URL.createObjectURL(res)
            this.formData.cardPortraitNationalEmblem = url
        })
        //获取当前企业下所有账号
        let resUser = await getUserList({ 'enterpriseId': this.formData.enterpriseId } )
        this.userData = resUser.list
        //获得企业附件
        this.getFileInfos(this.formData.enterpriseId)
        //获取当前企业下所有店铺
        let resShop = await getShopList({ 'enterpriseId': this.formData.enterpriseId } )
        this.shopData = resShop.list
        //身份证照片显示
        if(!this.formData.businessLicense) {
            this.businessLicenseHidden = false
        }
        if(!this.formData.cardPortraitFace) {
            this.showCardPortraitFace = false
        }
        //个体户显示
        if(this.formData.enterpriseType === 0) {
            this.showIndividualHousehold = true
        }
        //企业显示
        if(this.formData.enterpriseType === 1) {
            this.showEnterprise = true
        }
        if(this.shopData.length === 0) {
            this.showShop = false
        }
        // 保存所有tabName
        let arr = [ 'adminInfo', 'accountInfo']
        if(this.showEnterprise) {
            arr.splice(0, 0, 'enterpriseInfo')
        }
        if(this.showIndividualHousehold) {
            arr.splice(0, 0, 'individualHouseholdInfo')
        }
        if(this.showShop) {
            arr.splice(3, 0, 'shopInfo')
        }
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return  document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
            currencyForm: state => state.equip.equipData.currencyForm, //币种
            financialSharingList: state => state.contract.ctClassify.financialSharingList, //传输财务共享
            state: state => state.equip.equipData.state, //通用流程状态
        }),
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        }
    },
    methods: {
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.fileTableRef.toggleRowSelection(row, row.flag)
        },
        //下载附件
        openShopDow (fileRow) {
            this.openShopLoading = true
            previewFile({ recordId: fileRow.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = fileRow.name
                a.click()
                window.URL.revokeObjectURL(url)
                this.openShopLoading = false
            }).catch(() => {
                this.openShopLoading = false
            })
        },
        batchDownloadFile () {
            if(this.fileSelectList.length == 0) {
                this.$message.info('未选择文件')
                return
            }
            this.fileSelectList.forEach(t => {
                this.openShopDow(t)
            })
            this.fileSelectList = []
        },
        async batchDownloadFilePackage () {
            this.openShopLoading = true
            let files = this.enterpriseData.files
            if(files == null || files.length == 0) {
                this.$message.info('附件为空！')
                return
            }
            const zip = new JSZip()
            for (let i = 0; i < files.length; i++) {
                let res = await previewFile({ recordId: files[i].fileFarId })
                zip.file(files[i].name, res)
            }
            let content = await zip.generateAsync({ type: 'blob' })
            FileSaver.saveAs(content, this.formData.shopName + '开店附件资料.zip')
            this.openShopLoading = false
        },
        selectionChangeHandle (val) {
            this.fileSelectList = val
        },
        //查询附件信息
        getFileInfos (relevanceId) {
            let params = {
                relevanceId: relevanceId,
                relevanceType: '8',
                mallType: 0
            }
            getEnterPriseFileList(params).then(res=>{
                this.fileList = res.list
            })
        },

        ...mapMutations(['setAuditParams']),
        ...mapMutations(['setSelectedInfo']),
        addData () { },
        deleteData () { },
        currentChange () {},
        sizeChange () {},
        shopStateChange () {
        },
        //返回
        handleClose () {
            hideLoading()
            // this.$router.go(-1)
            this.$router.replace(this.beforePath)
        },
        // 点击店铺明跳转
        handleView (scope) {
            this.viewList = 'class'
            this.formData = JSON.parse(JSON.stringify(scope.row))
            this.action = '编辑'
            this.$router.replace({ path: '/platform/shop/shopManageDetail', query: { shopInfo: JSON.stringify(scope.row) } })
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        showBusinessLicenseHidden (info) {
            if(info == null) {
                this.businessLicenseHidden = false
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}
</style>
