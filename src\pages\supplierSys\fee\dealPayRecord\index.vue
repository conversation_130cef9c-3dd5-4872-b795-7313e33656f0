<template>
    <div class="base-page">
        <template v-if="!showDetail">
            <div class="right">
                <div class="e-table">
                    <div class="top">
                        <div class="search_box">
                            <div class="adverse">
                                <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                            </div>
                        </div>
                    </div>
                </div>
                <!--表格-->
                <div class="e-table">
                    <el-table
                        ref="tableRef"
                        v-loading="tableLoading"
                        :data="tableData"
                        :height="rightTableHeight"
                        border
                        class="table"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column label="操作"  width="190">
                            <template v-slot="scope">
                                <span class="action" @click="handleView(scope.row)">查看详情</span>
                                <span class="action" v-if="scope.row.state==0||scope.row.state==3" @click="()=>goBill(scope.row)">上传对账单</span>
                                <span class="action" v-if="scope.row.state==2||scope.row.state==6" @click="()=>goPay(scope.row)">缴费</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="本次结算交易额（元）" prop="periodTransactionAmount" width="180"/>
                        <el-table-column label="累计结算交易额（元）" prop="totalTransactionAmount" width="180"/>
                        <el-table-column label="交易服务费（元）" prop="payAmount" width="170"/>
                        <el-table-column label="结算日期" prop="settleDate" width="130"/>
                        <el-table-column label="当期结算覆盖交易时间段" width="">
                            <template v-slot="scope">
                                <span >{{ scope.row.periodStartDate }}至{{ scope.row.periodEndDate }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="缴费截止日期" prop="paymentDeadline" width="130" />
                        <el-table-column label="确认状态" width="100">
                            <template v-slot="scope">
                                <el-tag  type="info" v-if="scope.row.state == 0">待确认</el-tag>
                                <el-tag  v-else-if="scope.row.state == 1">确认中</el-tag>
                                <el-tag  type="danger"  v-else-if="scope.row.state == 3">确认失败</el-tag>
                                <el-tag  type="success" v-else>已确认</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="缴费状态" width="100">
                            <template v-slot="scope">
                                <el-tag  type="info" v-if="scope.row.state == 2">未缴费</el-tag>
                                <el-tag  v-else-if="scope.row.state == 4">审核中</el-tag>
                                <el-tag  type="danger"  v-else-if="scope.row.state == 6">审核失败</el-tag>
                                <el-tag  type="success"  v-else-if="scope.row.state == 5">审核成功</el-tag>
                                <span v-else>/</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="审核人" width="130">
                            <template v-slot="{row:{ auditorName }}">
                                <span >{{ auditorName?auditorName:'/'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="审核时间" width="130">
                            <template v-slot="{row:{ auditOpenTime }}">
                                <span >{{ auditOpenTime?auditOpenTime:'/'}}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <Pagination
                    v-show="tableData && tableData.length > 0"
                    :currentPage.sync="paginationInfo.currentPage"
                    :pageSize.sync="paginationInfo.pageSize"
                    :total="paginationInfo.total"
                    @currentChange="getTableData"
                    @sizeChange="getTableData"
                />
            </div>
        </template>
        <template v-else>
            <detail
                :dfr="currentRecord"
                @pmt-slips-preview="file => handlePictureCardPreview(file)"
                @pay="(record) => goPay(record)"
                @bill="(record) => goBill(record)"
                @close="() => showDetail=false"
                />
        </template>
        <!--预览图片-->
        <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <!--高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="状态：">
                            <div style="display:flex">
                                <el-select v-model="filterData.state">
                                    <el-option v-for="item in stateOptions" :key="item.value" :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费截止时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd"
                                v-model="filterData.paymentDeadline"
                                type="daterange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    <!-- 上传交易服务费缴纳对账单 -->
        <el-dialog
            v-loading="billLoading" top="5vh" title="上传交易服务费缴纳对账单" v-dialogDrag :visible.sync="showBill" width="50%">
            <div class="tipDiv">
                <div class="tipContent"><span style="color: red;margin-right: 4px">*</span>请下载交易服务费对账单，盖章后上传扫描件</div>
                <div class="tipContent1">平台确认后将为你开具发票，请在收到发票后再到此页面缴费</div>
                <el-button class="downloadbtn" @click="downloadStatement">下载交易服务费对账单</el-button>
                <div style="width: 50%;margin-left: 25%">
                <el-upload
                    :class="bills.length === 1 ? 'hide_box_min' : ''"
                    v-loading="uploadLoading"
                    accept=".pdf"
                    class="upload-demo"
                    action="fakeaction"
                    :limit="1"
                    :file-list="bills"
                    :before-upload="handleBeforeUpload"
                    :auto-upload="true"
                    :on-remove="(file)=>handleFileRemove(file)"
                    :on-preview="()=>downloadFile(bills[0])"
                    :http-request="uploadBill"
                    list-type="text">
                    <!-- <div slot="tip" class="el-upload__tip">只能上传pdf文件</div> -->
                    <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                </el-upload>
                </div>
                <div v-if="currentRecord && currentRecord.state == '3'" class="tipContent2">审核未通过，未通过原因：{{ auditRecords[0]?auditRecords[0].auditResult:'' }}</div>
            </div>
            <span slot="footer">
                <el-button @click="showBill = false">取消</el-button>
                <el-button type="primary" class="btn-greenYellow" @click="saveBill">提交</el-button>
            </span>
        </el-dialog>
    <!-- 上传交易服务费 -->
        <el-dialog
            v-loading="showAddFeeLoading" class="addDia" top="5vh" title="交易服务费缴纳" v-dialogDrag :visible.sync="showAddFee" width="56%">
            <div class="payFeesTip">请用对公账户打款到四川路桥建设集团物资有限责任公司对公账号，并将缴费凭证截图在此处上传</div>
            <el-form label-width="245px" :data="addFeeForm" style="margin-top: 1.5%;">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="收款账户名：">
                            <span>{{ platformFreeyhOrgName }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户开户行：">
                            <span>{{ platformFreeyhAddress }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户：">
                            <span>{{ platformFreeyhAccount }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收款金额：">
                            <span style="color: red">{{currentRecord?currentRecord.payAmount:''}}元 </span>(交易服务费)
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费方式：" prop="payType">
                            <el-radio v-model="addFeeForm.payType" :label="1" >线下</el-radio>
                            <el-radio v-model="addFeeForm.payType" disabled :label="2" >线上</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item  label="缴费凭证：" prop="file">
                            <el-upload
                                :class="fileList.length === 1 ? 'hide_box_min' : ''"
                                v-loading="uploadLoading"
                                class="upload-demo"
                                action="fakeaction"
                                :limit="1"
                                :file-list="fileList"
                                :before-upload="handleBeforeUpload"
                                :auto-upload="true"
                                :http-request="uploadPayProof"
                                list-type="picture-card">
                                <div slot="tip" class="el-upload__tip" style="margin-top: -1%;">只能上传图片文件</div>
                                <i slot="default" class="el-icon-plus"></i>
                                <div slot="file" slot-scope="{file}">
                                    <img
                                        class="el-upload-list__item-thumbnail"
                                        :src="file.url"
                                        alt=""
                                        />
                                    <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview(file)">
                                    <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload(file)">
                                    <i class="el-icon-download"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="(file)=>handleFileRemove(file)">
                                    <i class="el-icon-delete"></i>
                                    </span>
                                </span>
                                </div>
                            </el-upload>
                            </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input
                                type="textarea"
                                :auto-resize="false"
                                v-model="addFeeForm.remarks"
                                placeholder="请输入备注" maxlength="1000"
                                show-word-limit
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="currentRecord && currentRecord.state == '6'" :span="22">
                        <el-form-item label="未通过原因：">
                            <el-input
                                type="textarea"
                                :auto-resize="false"
                                :value="auditRecords[0]?auditRecords[0].auditResult:''"
                            ></el-input>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
            <span slot="footer">
                <el-button @click="showAddFee = false">取消</el-button>
                <el-button type="primary" class="btn-greenYellow"  @click="save">提交</el-button>
            </span>
        </el-dialog>
        <el-dialog v-if="currentRecord" :visible.sync="statementVisible" title="交易服务费对账单" width="60%" class="addDia">
            <table id="printArea" ref="pdfContent" bgcolor="#cce8cf" border="1" cellspacing="0" class="table-container">
                <tbody>
                    <tr>
                    <th colspan="8" style="font-size: 28px">四川路桥物资采购平台交易服务费对账单</th>
                    </tr>
                    <tr>
                    <td colspan="8">
                        <span>平台用户：{{ userInfo.enterpriseName }}</span>
                        <span style="float: right;padding-right: 2%;">
                            对账期间：{{ currentRecord.periodStartDate | dateToChineseDate}}-{{ currentRecord.periodEndDate | dateToChineseDate}}
                        </span>
                    </td>
                    </tr>
                    <tr>
                    <td align="center" width="6%">序号</td>
                    <td align="center" width="30%">本期平台交易金额</td>
                    <td align="center" width="14%">交易服务费率</td>
                    <td align="center" width="10%">本期应缴纳交易服务费（元）</td>
                    <td align="center" width="10%">税率</td>
                    <td align="center" width="10%">税额（元）</td>
                    <td align="center" width="10%">不含税金额（元）</td>
                    <td align="center" width="10%">备注</td>
                    </tr>
                    <tr>
                    <td align="center">1</td>
                    <td align="center">{{ currentRecord.periodTransactionAmount }}</td>
                    <td align="center">{{ platformShopFeeDealRatio }}</td>
                    <td align="center">{{ currentRecord.payAmount }}</td>
                    <td align="center">{{ currentRecord.taxRate | percentText}}</td>
                    <td align="center">{{ currentRecord.taxAmount }}</td>
                    <td align="center">{{ currentRecord.noTaxAmount }}</td>
                    <td align="center">{{ currentRecord.remarks }}</td>
                    </tr>
                    <tr>
                    <td colspan="2" align="center">合计</td>
                    <td align="center">{{ platformShopFeeDealRatio }}</td>
                    <td align="center">{{ currentRecord.payAmount }}</td>
                    <td align="center">{{ currentRecord.taxRate | percentText}}</td>
                    <td align="center">{{ currentRecord.taxAmount }}</td>
                    <td align="center">{{ currentRecord.noTaxAmount }}</td>
                    <td align="center">{{ currentRecord.remarks }}</td>
                    </tr>
                    <tr>
                    <td colspan="3">平台用户：{{ userInfo.enterpriseName }}</td>
                    <td colspan="5">平台经营机构： 四川路桥建设集团物资有限责任公司</td>
                    </tr>
                    <tr>
                    <td colspan="3">签字并盖章：</td>
                    <td colspan="5">签字并盖章：</td>
                    </tr>
                    <tr>
                    <td colspan="3">时间：</td>
                    <td colspan="5">时间：</td>
                    </tr>
                </tbody>
            </table>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { debounce } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { supplierDealFeeList, supplierChangeDealFeeState,
    supplierFetchDealFeeFile, supplierFetchSystemParams, supplierFetchAudits, supplierFetchDealRecons } from '@/api/fee/feeApi'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { mapState } from 'vuex'
import moment from 'moment'
import html2pdf from 'html2pdf.js'
import Detail from './detail.vue'
import { generatePdfThumbnailDataUrl } from '@/utils/pdfUtils'

const stateOptions = [
    {
        value: -1,
        label: '全部'
    },
    {
        value: 0,
        label: '待确认'
    },
    {
        value: 1,
        label: '确认中'
    },
    {
        value: 2,
        label: '待缴费'
    },
    {
        value: 3,
        label: '确认失败'
    },
    {
        value: 4,
        label: '审核中'
    },
    {
        value: 5,
        label: '审核通过'
    },
    {
        value: 6,
        label: '审核未通过'
    },
]
const getSysValue = ( systemParams, key) => {
    if (!systemParams) {
        return '加载中'
    }
    const systemParam = systemParams.find(s => s.code == key)
    if (!systemParam) {
        return '加载失败'
    }
    return systemParam.keyValue
}
function isImage (fileName) {
    if (!fileName) return false
    const lower = fileName.toLowerCase()
    return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].some(ext => lower.endsWith(ext))
}
function isPdf (fileName) {
    if (!fileName) return false
    return fileName.toLowerCase().endsWith('.pdf')
}
// pdf使用iframe预览，图片使用img预览，注意iframe需要声明type: 'application/pdf'，否则预览pdf会失败
export const toPreviewableFile = ( fileFarId, name ) => {
    return previewFile({ recordId: fileFarId }).then(async res => {
        let url
        if (isPdf(name)) {
            url = await generatePdfThumbnailDataUrl(res)
        }else {
            const blob = new Blob([res])
            url = window.URL.createObjectURL(blob)
        }
        const fileName = name
        return {
            name: fileName,
            url,
            fileFarId,
        }
    })
}
export default {
    components: {
        Pagination, Detail
    },
    filters: {
        dateToChineseDate (date) {
            if (!date) {
                return ''
            }
            return moment(date).format('YYYY年MM月DD日')
        },
        percentText (value) {
            if (value === null || value === undefined || value === '') {
                return '-'
            }

            let num = Number(value)
            if (isNaN(num)) {
                return '-'
            }

            return (num * 100).toFixed(2).replace(/\.00$/, '%')
        }
    },
    computed: {
        ...mapState([ 'userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        platformFreeyhAddress () { // 平台收费银行开户行
            return getSysValue(this.systemParams, 'platformFreeyhAddress')
        },
        platformFreeyhAccount () { // 平台收费银行账号
            return getSysValue(this.systemParams, 'platformFreeyhAccount')
        },
        platformFreeyhOrgName () { // 平台收费公司名称
            return getSysValue(this.systemParams, 'platformFreeyhOrgName')
        },
        platformShopFeeDealRatio () { // 平台店铺交易费收取比例（单位千分之）
            const val = getSysValue(this.systemParams, 'platformShopFeeDealRatio')
            if (val == null || val === '' || isNaN(Number(val))) {
                return val // 可能是“加载中”或其它非数字内容
            }
            return `${val}‰` // 拼接千分号
        }
    },
    data () {
        return {
            stateOptions,
            dialogImageUrl: '',
            dialogVisible: false,
            showAddFee: false,
            showAddFeeLoading: false,
            statementVisible: false,
            addFeeForm: {
                payType: 1,
                remarks: null,
            },
            tableLoading: false, // 加载
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            uploadImgSize: 10, // 上传文件大小
            tableData: [], // 表格数据
            filterData: { // 高级搜索
                state: -1,
                paymentDeadline: null,
            },
            bills: [],
            billLoading: false,
            showBill: false,
            currentRecord: null,
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            uploadLoading: false,
            fileList: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            systemParams: null,
            auditRecords: [],
            fileIdsToRemove: [],
            showDetail: false,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        supplierFetchSystemParams().then( res => {
            this.systemParams = res
        })
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    methods: {
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleDownload (file) {
            previewFile({ recordId: file.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = file.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        goBill (record) {
            this.showBill = true
            this.billLoading = true
            this.bills = []
            this.fileIdsToRemove = []
            this.currentRecord = record
            const { dealFeeRecordId, state } = this.currentRecord
            const p1 = supplierFetchDealFeeFile(dealFeeRecordId, 3).then( f => {
                if (f.length > 0) {
                    this.bills = [f[0]]
                }
            })
            let p2
            if (state == '3') {
                p2 = supplierFetchAudits(dealFeeRecordId).then( res => {
                    this.auditRecords = res
                })
            }else {
                p2 = Promise.resolve()
            }
            Promise.all([p1, p2]).finally(() => this.billLoading = false)
        },
        goPay (record) {
            this.showAddFee = true
            this.showAddFeeLoading = true
            this.fileList = []
            this.fileIdsToRemove = []
            this.addFeeForm.remarks = record.remarks
            this.currentRecord = record
            const { dealFeeRecordId, state } = this.currentRecord
            const p1 = supplierFetchDealFeeFile(dealFeeRecordId, 2).then( files => {
                if (files.length > 0) {
                    const latestFile = files[0]
                    toPreviewableFile(latestFile.fileFarId, latestFile.name).then(file => {
                        this.fileList = [file]
                    })
                }
            })
            let p2
            if (state == '6' ) {
                p2 = supplierFetchAudits(dealFeeRecordId).then( res => {
                    this.auditRecords = res
                })
            }else {
                p2 = Promise.resolve()
            }
            Promise.all([p1, p2]).finally(() => this.showAddFeeLoading = false)
        },
        validateImage (rule, value, callback) {
            if (this.fileList.length === 0) {
                callback(new Error('请上传缴费凭证'))
            } else {
                callback()
            }
        },
        saveBill () {
            if (this.bills.length === 0) {
                return this.$message.error('请上传交易服务费缴纳对账单')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.billLoading = true
                // 减小网络传输流量
                // eslint-disable-next-line no-unused-vars
                const files = this.bills.map(({ url, ...rest }) => rest)
                supplierChangeDealFeeState(this.currentRecord.dealFeeRecordId, 1, { files }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('操作成功')
                        this.removeAllDeleted()
                        this.showBill = false
                        this.showDetail = false
                        this.getTableData()
                    }
                }).finally(() => {
                    this.billLoading = false
                })
            })
        },
        downloadFile (file) {
            previewFile({ recordId: file.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                const link = document.createElement('a')
                link.href = url
                link.download = file.name
                document.body.appendChild(link)
                link.click()

                // 清理
                document.body.removeChild(link)
                URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        removeAllDeleted () {
            this.fileIdsToRemove.forEach(id => {
                createFileRecordDelete({ recordId: id }).then(res => {
                    if(res.code !== 200) {
                        this.$message({
                            message: res.message,
                            type: 'success'
                        })
                    }
                })
            })
            this.fileIdsToRemove = []
        },
        save () {
            if (this.fileList.length === 0) {
                return this.$message.error('请上传缴费凭证！')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.showAddFeeLoading = true
                // 减小网络传输流量
                // eslint-disable-next-line no-unused-vars
                const files = this.fileList.map(({ url, ...rest }) => rest)
                const params = {
                    files,
                    remarks: this.addFeeForm.remarks
                }
                supplierChangeDealFeeState(this.currentRecord.dealFeeRecordId, 4, params).then(res => {
                    if (res.code == 200) {
                        this.$message.success('操作成功')
                        this.addFeeForm = {
                            payType: 1,
                            remarks: null,
                        }
                        this.fileList = []
                        this.removeAllDeleted()
                        this.showAddFee = false
                        this.getTableData()
                        this.showDetail = false
                    }
                }).finally(() => {
                    this.showAddFeeLoading = false
                })
            })
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            const fileName = file.name.toLowerCase()

            if (!isImage(fileName) && !isPdf(fileName)) {
                this.$message.error('只能上传图片或PDF文件')
                return false
            }
            return true
        },
        handleFileRemove (file) {
            this.bills = []
            this.fileList = []
            this.fileIdsToRemove.push(file.fileFarId)
        },
        uploadBill (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 3)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.bills = []
                }else {
                    let resO = res[0]
                    previewFile({ recordId: resO.recordId }).then(res => {
                        const blob = new Blob([res])
                        const url = window.URL.createObjectURL(blob)
                        this.bills = [{
                            name: file.name,
                            url,
                            fileFarId: resO.recordId
                        }]
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                    })
                }
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        uploadPayProof (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 3)
            this.uploadLoading = true
            uploadFile(form).then(async res => {
                if(res.code != null && res.code != 200) {
                    this.fileList = []
                }else {
                    const resO = res[0]
                    let url
                    if (isImage(resO.objectName)) {
                        url = URL.createObjectURL(file)
                    }else {
                        url = await generatePdfThumbnailDataUrl(file)
                    }
                    this.fileList = [{
                        name: file.name,
                        url, // 只有图片设置 URL
                        fileFarId: resO.recordId,
                    }]
                    this.$message.success('上传成功')
                }
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // 跳转详情
        handleView (row) {
            const record = { ...row,
                platformFreeyhAddress: this.platformFreeyhAddress,
                platformFreeyhAccount: this.platformFreeyhAccount,
                platformFreeyhOrgName: this.platformFreeyhOrgName,
            }
            this.currentRecord = record
            const { dealFeeRecordId } = record
            const setValue = (promise, key) => {
                record[key + 'Loading'] = true
                record[key] = []
                promise.then(value => {
                    // 防止数据加载慢造成数据污染
                    if (dealFeeRecordId !== this.currentRecord.dealFeeRecordId) {
                        return
                    }
                    this.currentRecord = { ...this.currentRecord }
                    this.currentRecord[key] = value
                }).then(() => {
                    if (dealFeeRecordId !== this.currentRecord.dealFeeRecordId) {
                        return
                    }
                    this.currentRecord[key + 'Loading'] = false
                })
            }
            setValue( supplierFetchDealFeeFile(dealFeeRecordId, 3), 'recons')
            // 缴费凭证需要预览
            setValue( supplierFetchDealFeeFile(dealFeeRecordId, 2).then(files => {
                if (files.length == 0) {
                    return files
                }
                return toPreviewableFile(files[0].fileFarId, files[0].name).then(f => [f, ...files.slice(1)])
            }), 'pmtSlips')
            // setValue( supplierFetchDealFeeFile(dealFeeRecordId, 2), 'pmtSlips')
            setValue( supplierFetchAudits(dealFeeRecordId), 'auditRecords')
            setValue( supplierFetchDealRecons(dealFeeRecordId), 'dealRecons')
            this.showDetail = true
            // this.$router.push({
            //     path: '/supplierSys/fee/dealPayRecordDetail',
            //     name: 'supplierSysFeeDealPayRecordDtl',
            //     query: {
            //         sn: row.dealFeeRecordUn
            //     }
            // })
        },
        //重置数据
        resetSearchConditions () {
            this.filterData = {
                state: -1,
                paymentDeadline: null,
            }
        },
        // 下载交易服务费对账单
        downloadStatement () {
            this.statementVisible = true
            this.$nextTick(() => {
                this.generatePdf(() => this.statementVisible = false)
            })
        },
        // 高级搜索
        confirmSearch () {
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            params.state = this.filterData.state
            if (this.filterData.paymentDeadline) {
                params.startDeadline = this.filterData.paymentDeadline[0]
                params.endDeadline = this.filterData.paymentDeadline[1]
            }
            this.tableLoading = true
            supplierDealFeeList(params).then(res => {
                this.paginationInfo.total = res.total
                this.paginationInfo.pageSize = res.size
                this.paginationInfo.currentPage = res.current
                this.tableData = res.records.map(r=>{
                    return { ...r, file: [], fileLoading: true }
                })
            }).finally(() => {
                this.tableLoading = false
            })
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
        generatePdf (callback) {
            const element = this.$refs.pdfContent
            const opt = {
                margin: 0.5,
                filename: '交易服务费缴纳对账单.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2, scrollX: 0, scrollY: 0 },
                jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
            }
            html2pdf().set(opt).from(element).save().then(callback)
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
    appearance: textfield !important;
    -moz-appearance: textfield !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
/deep/ .addDia {
    .el-dialog__body {
        height: 600px;
    }
}

/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
.tipDiv {
  text-align: center;
  margin-top: 2%;
  font-size: 16px;
}
.tipContent {
  padding: 0 0 10px 0;
  line-height: 22px;
}
.tipContent1 {
  padding: 0 0 10px 0;
  line-height: 22px;
  color: red;
}
.tipContent2 {
  padding: 0 0 10px 0;
  line-height: 22px;
  color: red;
  margin-top: 6%;
}
//上传文件的按钮样式
.uploadbtn{
  display: inline-flex;
  align-items: center;
  background-color: rgb(255,118,0);
  border: 1px solid rgb(255,118,0);
  text-align: center;
  width: 120px;
  height: 35px;
  line-height: 35px;
  margin-top: 10%;
}
.downloadbtn {
  display: inline-flex;
  align-items: center;
  text-align: center;
  width: 180px;
  height: 45px;
  line-height: 45px;
  margin-top: 3%;
}
.payFeesTip {
  text-align: center;
  padding: 0 0 10px 0;
  line-height: 22px;
  color: red;
  margin-top: 2%;
  font-size: 20px;
}
.table-container {
  width: 7.5in;
  max-width: 100%;
  box-sizing: border-box;
  table-layout: fixed;
  border-collapse: collapse;
  font-size: 14px;
  color: #000;
}
.table-container th,
.table-container td {
  border: 1px solid #000;
  word-wrap: break-word;
  padding: 8px;
}
#printArea {
  width: 794px; /* 固定为 A4 页面宽度，单位像素 */
  font-size: 14px;
}

#printArea table {
  width: 100%;
  border-collapse: collapse;
}

#printArea th,
#printArea td {
  border: 1px solid #000;
  padding: 8px;
  text-align: left;
}

/* PDF文件缩略图容器样式 */
.pdf-thumbnail-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa; /* 浅灰色背景 */
}
</style>
