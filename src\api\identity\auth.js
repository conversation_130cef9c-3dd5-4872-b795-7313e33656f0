import service from '@/utils/request'
// const service = require('@/utils/request')
// console.log('auth', service)
import qs from 'qs'

// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

const request = {
    // 登录接口
    identityAuthSignin (params) {
        params.mallType = 0
        return httpPost({
            url: '/materialMall/w/user/login',
            params: qs.stringify(params),
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    phoneLogin (params) {
        params.mallType = 0
        return httpPost({
            url: '/materialMall/w/user/phoneLogin',
            params: qs.stringify(params),
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    // 验证并刷新token
    refreshToken (params) {
        return httpPost({
            url: '/identity/auth/verifyToken',
            params: qs.stringify(params),
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    // router/index只认这个文件，很奇怪
    // 获取用户所在机构
    hrOrgGetOrgByUserId (params) {
        return httpGet({
            url: '/hr/org/getOrgByUserId',
            params
        })
    },
    // 获取本机构及其以下机构
    hrOrgGetChildrenOrg (params) {
        return httpGet({
            url: '/hr/org/getChildrenOrg',
            params
        })
    },
    configKvGetDicValue (params) {
        return httpGet({
            url: '/config/kv/getDicValue',
            params
        })
    },
}

export default request
