<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;"  v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <!-- <el-tab-pane label="缴费详情" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="缴费明细" name="baseInfoDtl" :disabled="clickTabFlag"  v-if="formDtl.state === 2">
                </el-tab-pane>
                <el-tab-pane label="欠费明细" name="feeNotPayList" :disabled="clickTabFlag" />
                <el-tab-pane label="审核历史" name="auditRecords" :disabled="clickTabFlag" /> -->
                <el-tab-pane label="缴费详情" name="msgID1" :disabled="clickTabFlag"/>
                <el-tab-pane label="服务交易缴费明细" name="msgID2" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="审核历史" name="msgID3" :disabled="clickTabFlag" />
                <el-tab-pane label="余额变动记录" name="msgI4" :disabled="clickTabFlag" />
                <div id="tabs-content">
                    <div id="baseInfo" class="con">
                        <div class="tabs-title" id="msgID1">缴费详情</div>
                        <el-form :model="formDtl" label-width="200px" :rules="formDtlRules" ref="formDtlRef" :disabled="false" class="demo-ruleForm">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="缴费编号：" prop="dealFeeRecordUn">
                                        <span>{{formDtl.dealFeeRecordUn}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="缴费人：" prop="founderName">
                                        <span>{{formDtl.founderName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="缴费方式：" prop="payType">
                                        <el-radio v-model="formDtl.payType" :label="1" >线下</el-radio>
                                        <el-radio v-model="formDtl.payType" disabled :label="2" >线上</el-radio>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="状态：" prop="state">
                                        <el-tag type="success" v-if="formDtl.state == 0">待确认</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 1">确认中</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 2">确认成功</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 3">确认失败</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 4">审核中</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 5">审核通过</el-tag>
                                        <el-tag type="success" v-if="formDtl.state == 6">审核未通过</el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="服务类型：" prop="recordType">
                                        <span v-if="formDtl.recordType == 1">店铺交易服务费</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="缴费金额（元）：" prop="payAmount">
                                        <span style="color: red">{{formDtl.payAmount}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="多余退回余额金额：" prop="returnBalance" v-if="formDtl.state === 2">
                                        <span style="color: red">{{formDtl.returnBalance}}元</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="缴费时间：" prop="gmtCreate">
                                        {{formDtl.gmtCreate}}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="修改时间：" prop="gmtModified">
                                        {{formDtl.gmtModified}}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="备注：" prop="remarks">
                                        <el-input :disabled="formDtl.state == 1 || formDtl.state == 2" style="width: 1000px;" type="textarea" :auto-resize="false" v-model="formDtl.remarks"
                                                  placeholder="请输入备注" maxlength="1000" show-word-limit></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item  label="缴费证明：" prop="file">
                                        <el-upload
                                            :class="formDtl.files.length === 1 ? 'hide_box_min' : ''"
                                            v-loading="uploadLoading"
                                            class="upload-demo"
                                            action="fakeaction"
                                            :limit="1"
                                            :file-list="fileList"
                                            :before-upload="handleBeforeUpload"
                                            :auto-upload="true"
                                            :http-request="uploadLicenseBusiness"
                                            list-type="picture-card">
                                            <div slot="tip" class="el-upload__tip" v-show="fileList.length === 0">只能上传图片文件</div>
                                            <i slot="default" class="el-icon-plus"></i>
                                            <div slot="file" slot-scope="{file}">
                                                <img
                                                    class="el-upload-list__item-thumbnail"
                                                    :src="file.url" alt="">
                                                <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                  </span>
                                            </div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <div id="feeNotPayList" class="con">
                        <div class="tabs-title" id="msgID2">服务交易缴费明细</div>
                        <div class="top" style="height: 50px">
                            <div class="left">
                                <div class="left-btn">
                                    <div style="display: flex;align-items: center;">
                                    <span>
                                &nbsp;
                                总欠费金额（元）：<span style="color: red">{{tableData2[0]==null?0:tableData2[0].paymentAmount}}</span>
                            </span>
                                    </div>
                                </div>
                            </div>
                            <div class="search_box">
                                <el-radio v-model="filterData2.orderBy" :label="0">按生成时间排序</el-radio>
                                <el-radio v-model="filterData2.orderBy" :label="1">按修改时间排序</el-radio>
                                <el-input v-model="keywords2" clearable placeholder="输入搜索关键字" style="width: 300px" type="text" @blur="getTableData2">
                                    <img slot="suffix" alt="" src="@/assets/search.png" @click="getTableData2"/>
                                </el-input>
                            </div>
                        </div>
                        <div class="e-table">
                            <el-table
                                ref="tableRef2"
                                v-loading="tableLoading2"
                                :data="tableData2"
                                height="300px"
                                border
                                class="table"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="enterpriseName" label="企业名称" width="260">
                                    <template v-slot="scope">
                                        {{scope.row.enterpriseName}}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="relevanceNu" label="关联单据编号" width="240">
                                </el-table-column>
                                <el-table-column prop="" label="关联单据类型" width="">
                                    对账单
                                </el-table-column>
                                <el-table-column prop="dealAmount" label="交易金额（元）" width="150">
                                    <template v-slot="scope">
                                        {{scope.row.dealAmount}}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="useExemptFree" label="占用免费交易金额（元）" width="150">
                                </el-table-column>
                                <el-table-column prop="exceedFree" label="需缴费交易金额（元）" width="150">
                                </el-table-column>
                                <el-table-column prop="feeRatio" label="收取比例（‰）" width="120">
                                </el-table-column>
                                <el-table-column prop="serveFee" label="服务费用（元）" width="140">
                                </el-table-column>
                                <el-table-column prop="payFee" label="已缴费金额（元）" width="120">
                                </el-table-column>
                                <el-table-column prop="finishPayFee" label="是否欠费" width="120">
                                    <template v-slot="scope">
                                        <el-tag type="success"  v-show="scope.row.finishPayFee==1">否</el-tag>
                                        <el-tag type="danger"  v-show="scope.row.finishPayFee==0">是</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="residuePayFee" label="剩余未缴费金额（元）" width="120">
                                </el-table-column>
                                <el-table-column prop="useBalance" label="缴费使用余额金额（元）" width="120">
                                </el-table-column>
                                <el-table-column label="生成时间" prop="gmtCreate" width="160"/>
                                <el-table-column label="修改时间" prop="gmtModified" width="160"/>
                            </el-table>
                        </div>
                        <Pagination
                            v-show="tableData2 && tableData2.length > 0"
                            :currentPage.sync="paginationInfo2.currentPage"
                            :pageSize.sync="paginationInfo2.pageSize"
                            :total="paginationInfo2.total"
                            @currentChange="getTableData2"
                            @sizeChange="getTableData2"
                        />
                    </div>
                    <!-- <div id="baseInfoDtl" class="con"  v-if="formDtl.state === 2">
                        <div class="tabs-title" id="baseInfoDtl">缴费明细</div>
                        <div class="e-table"  style="background-color: #fff" v-loading="dtlTableListLoading">
                            <el-table
                                border
                                style="width: 100%"
                                max-height="342px"
                                ref="tableListRef"
                                :data="formDtl.dtlVOs"
                                @selection-change="tableListSelectChange"
                                @row-click="tableListRowClick"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="relevanceNu" label="对账单编号"/>
                                <el-table-column prop="projectEnterpriseName" label="收货单位"/>
                                <el-table-column prop="enterpriseName" label="供货单位"/>
                                <el-table-column prop="dealAmount" label="交易金额"/>
                                <el-table-column prop="feeRatio" label="收取比例（‰）"/>
                                <el-table-column prop="serveFee" label="服务费用"/>
                                <el-table-column prop="payFee" label="已缴费金额"/>
                                <el-table-column prop="residuePayFee" label="剩余未缴费金额"/>
                                <el-table-column prop="finishPayFee" label="是否完成缴费">
                                    <template v-slot="scope">
                                        <el-tag type="success" v-if="scope.row.finishPayFee === 1">是</el-tag>
                                        <el-tag type="danger" v-if="scope.row.finishPayFee === 0">否</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="payAmount" label="本次缴费金额">
                                    <template v-slot="scope">
                                        <span style="color: red">{{scope.row.payAmount}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="交易时间" width="160"/>
                            </el-table>
                        </div>
                    </div> -->
                    <div id="auditRecords" class="con">
                        <div class="tabs-title" id="msgID3">审核历史</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="formDtl.auditRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="auditType" label="审核类型" width="160">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.relevanceType == 1">对账单确认审核</span>
                                        <span v-else>缴费审核</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200">
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160">
                                </el-table-column>
                                <el-table-column prop="auditResult" label="审核意见" width="">
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <div id="auditRecords2" class="con">
                        <div class="tabs-title" id="msgID4">余额变动记录</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="formDtl.balanceOperateRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="title" label="操作标题" width="300"/>
                                <el-table-column prop="amount" label="变动金额" width="160">
                                    <template slot-scope="scope">
                                        <span :style="{ color: scope.row.amount >= 0 ? 'green' : 'red' }">
                                            {{ scope.row.amount >= 0 ? '+' : '' }}{{ scope.row.amount }}
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="beforeAmount" label="修改前余额(元)" width="180"/>
                                <el-table-column prop="afterAmount" label="修改后余额(元)" width="180"/>
                                <el-table-column prop="operateUn" label="关联编号" width="240"/>
                                <el-table-column prop="relevanceType" label="关联类型" width="140">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.relevanceType == 1">平台交易</span>
                                    </template>
                                </el-table-column>
                                <!-- <el-table-column prop="operateType" label="操作类型" width="160">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.operateType == 1">缴费主表</span>
                                        <span v-if="scope.row.operateType == 2">平台交易费用明细</span>
                                        <span v-if="scope.row.operateType == 3">对账单</span>
                                    </template>
                                </el-table-column> -->
                                <el-table-column prop="gmtCreate" label="操作时间" width=""/>
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
            <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
        </div>
        <div class="buttons">
            <el-button type="primary" class="btn-greenYellow"
                       v-if="formDtl.state === 1 "
                       @click="auditPlanM(1, '通过')">通过
            </el-button>
            <el-button type="primary" class="btn-delete"
                       v-if="formDtl.state === 1 "
                       @click="auditPlanM(0, '未通过')">未通过
            </el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { mapState } from 'vuex'
import {
    findDealFeeBySn,
    platformDealFeeRecordUpdate,
    deleteDealFeeRecord,
    platformDealFeeRecordAudit,
    platformDealFeeRecordDtlBatchDelete,
    platformDealFeeRecordDtlBatchCreate,
    platformDealFeeRecordDtlListByEntity,
    supplierFetchSystemParams,
    supplierFetchAudits,
    platformBalanceOperateBatchQuery
} from '@/api/fee/feeApi'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import Pagination from '@/components/pagination/pagination.vue'

const getSysValue = (systemParams, key) => {
    if (!systemParams) {
        return '加载中'
    }
    const systemParam = systemParams.find(s => s.code == key)
    if (!systemParam) {
        return '加载失败'
    }
    return systemParam.keyValue
}
export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return  dateStr.split(' ')[0]
        }
    },
    components: {
        Pagination
    },
    data () {
        return {
            filterData2: { // 高级搜索
                orderBy: 1,
            },
            fileList: [],
            tableLoading2: false,
            dialogImageUrl: '',
            dialogVisible: false,
            formDtlRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                reconciliationProductType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' },
                ],
            },
            uploadLoading: false,
            dtlTableListLoading: false,
            freeAmount: 2000,
            formDtl: {
                files: [],
                balanceOperateRecords: []
            },
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [],
            tableData2: [],
            notPayFeeTableList: [],
            selectNotPayFeeTableList: [],
            selectTableList: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            formLoading: false,
            keywords2: null,
            showSelectNotPayFee: false,
            selectNotPayFeeLoading: false,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            systemParams: null,
        }
    },
    created () {
        this.getFormDtl()
        // 获取系统参数（对公账户信息）
        supplierFetchSystemParams().then(res => {
            this.systemParams = res
        }).catch(error => {
            console.error('获取系统参数失败:', error)
            // 系统参数获取失败时不显示错误消息，因为这不是关键功能
        })
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        // 平台收费银行开户行
        platformFreeyhAddress () {
            return getSysValue(this.systemParams, 'platformFreeyhAddress')
        },
        // 平台收费银行账号
        platformFreeyhAccount () {
            return getSysValue(this.systemParams, 'platformFreeyhAccount')
        },
        // 平台收费公司名称
        platformFreeyhOrgName () {
            return getSysValue(this.systemParams, 'platformFreeyhOrgName')
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
        'filterData2.orderBy': {
            handler () {
                this.getTableData2()
            }
        }
    },
    methods: {
        getTableData2 () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                isNotAbandon: 1,
                isArrearage: 1,
                enterpriseId: this.formDtl.enterpriseId
            }
            if(this.formDtl.recordType == 1) {
                params.freeQueryType = 3
            }
            if(this.formDtl.recordType == 2) {
                params.freeQueryType = 4
            }
            if (this.keywords2 != null && this.keywords2 != '') {
                params.keywords = this.keywords2
            }
            if (this.filterData2.orderBy != null) {
                params.orderBy = this.filterData2.orderBy
            }
            // 添加当期结算交易日期参数
            if (this.formDtl.periodStartDate) {
                params.periodStartDate = this.formDtl.periodStartDate
            }
            if (this.formDtl.periodEndDate) {
                params.periodEndDate = this.formDtl.periodEndDate
            }
            this.tableLoading2 = true
            platformDealFeeRecordDtlListByEntity(params).then(res => {
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
                this.tableData2 = res.list
            }).finally(() => {
                this.tableLoading2 = false
            })
        },
        batchDelete () {
            if(this.selectTableList.length == 0) {
                return this.$message.error('未选择数据！')
            }
            this.clientPop('info', '您确定要批量删除操作吗？', async () => {
                this.dtlTableListLoading = true
                let arr = this.selectTableList.map(item => {
                    return item.dealFeeRecordDtlId
                })
                platformDealFeeRecordDtlBatchDelete(arr).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.selectTableList = []
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.dtlTableListLoading = false
                })
            })
        },
        rowDelete (row) {
            this.clientPop('info', '您确定要删除操作吗？', async () => {
                this.dtlTableListLoading = true
                platformDealFeeRecordDtlBatchDelete([row.dealFeeRecordDtlId]).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.dtlTableListLoading = false
                })
            })
        },
        selectNotPayFeeAffirmClick () {
            if(this.selectNotPayFeeTableList.length == 0) {
                return this.$message.error('未选择明细！')
            }
            this.clientPop('info', '您确定要批量追加至缴费明细吗？', async () => {
                this.selectNotPayFeeLoading = true
                let arr = []
                for (let i = 0; i < this.selectNotPayFeeTableList.length; i++) {
                    let t = this.selectNotPayFeeTableList[i]
                    arr.push({
                        dealFeeRecordDtlId: null,
                        dealFeeRecordId: this.formDtl.dealFeeRecordId,
                        platformDealFeeDtlId: t.platformDealFeeDtlId,
                        payAmount: t.residuePayFee
                    })
                }
                platformDealFeeRecordDtlBatchCreate(arr).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.selectNotPayFeeTableList = []
                        this.showSelectNotPayFee = false
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.selectNotPayFeeLoading = false
                })
            })
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        siteReceivingTableSelectM (value) {
            this.selectNotPayFeeTableList = value
        },
        tableListRowClick (row) {
            row.flag = !row.flag
            this.$refs.tableListRef.toggleRowSelection(row, row.flag)
        },
        tableListSelectChange (value) {
            this.selectTableList = value
        },
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗？', async () => {
                if (state == 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            id: this.formDtl.dealFeeRecordId,
                            isOpen: 0,
                            auditResult: value,
                        }
                        this.formLoading = true
                        platformDealFeeRecordAudit(params).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.getFormDtl()
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                } else {
                    let params = {
                        id: this.formDtl.dealFeeRecordId,
                        isOpen: 1,
                    }
                    this.formLoading = true
                    platformDealFeeRecordAudit(params).then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.getFormDtl()
                        }
                    }).finally(() => {
                        this.formLoading = false
                    })
                }
            })
        },
        checkInputQty () {
            const regex = /^-?\d+$/
            if (!regex.test(this.formDtl.paymentDuration)) {
                this.$message.error('请输入有效的数字')
                this.formDtl.payAmount = this.freeAmount
                return this.formDtl.paymentDuration = 1
            }
            if (this.formDtl.paymentDuration < 0 || this.formDtl.paymentDuration > 999) {
                this.$message.error('超过限制！')
                this.formDtl.payAmount = this.freeAmount
                return this.formDtl.paymentDuration = 1
            }
            this.formDtl.payAmount = this.formDtl.paymentDuration * this.freeAmount
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.formDtl.files = []
                    this.fileList = []
                }else {
                    this.formDtl.files = []
                    let resO = res[0]
                    this.formDtl.files.push({
                        name: resO.objectName,
                        relevanceType: 2,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.formDtl.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.formDtl.files = []
                this.fileList = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload (file) {
            this.uploadLoading = true
            let image = this.formDtl.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        deleteOneM () {
            this.clientPop('info', '您确定要删除操作吗？', async () => {
                this.formLoading = true
                deleteDealFeeRecord({ id: this.formDtl.dealFeeRecordId }).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        saveSheetM (num) {
            this.clientPop('info', '您确定要该操作吗？', async () => {
                this.formDtl.submitAud = num
                this.formLoading = true
                platformDealFeeRecordUpdate(this.formDtl).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        getFormDtl () {
            this.formLoading = true
            findDealFeeBySn({ sn: this.$route.query.sn }).then(res => {
                this.formDtl = res

                // 处理缴费证明文件
                if (res.files && res.files.length > 0) {
                    let image = res.files[0]
                    this.uploadLoading = true
                    previewFile({ recordId: image.fileFarId }).then(fileRes => {
                        const blob = new Blob([fileRes])
                        const url = window.URL.createObjectURL(blob)
                        this.fileList = []
                        this.fileList.push({
                            name: image.name,
                            url: url
                        })
                    }).finally(() => {
                        this.uploadLoading = false
                    })
                } else {
                    this.fileList = []
                }

                // 获取审核记录
                if (res.dealFeeRecordId) {
                    supplierFetchAudits(res.dealFeeRecordId).then(auditRes => {
                        // 将审核记录添加到formDtl中，以便模板使用
                        this.$set(this.formDtl, 'auditRecords', auditRes || [])
                    }).catch(error => {
                        console.error('获取审核记录失败:', error)
                        this.$set(this.formDtl, 'auditRecords', [])
                    })
                } else {
                    this.$set(this.formDtl, 'auditRecords', [])
                }

                // 获取余额变动记录
                if (res.enterpriseId && res.periodStartDate && res.periodEndDate) {
                    const balanceParams = {
                        enterpriseId: res.enterpriseId,
                        periodStartDate: res.periodStartDate,
                        periodEndDate: res.periodEndDate
                    }
                    platformBalanceOperateBatchQuery(balanceParams).then(balanceRes => {
                        this.$set(this.formDtl, 'balanceOperateRecords', balanceRes || [])
                    }).catch(error => {
                        console.error('获取余额变动记录失败:', error)
                        this.$set(this.formDtl, 'balanceOperateRecords', [])
                    })
                } else {
                    this.$set(this.formDtl, 'balanceOperateRecords', [])
                }

                // 获取详情数据
                this.getTableData2()
            }).catch(error => {
                console.error('获取详情数据失败:', error)
                this.$message.error('获取详情数据失败')
            }).finally(() => {
                this.formLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },

        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

/deep/ .selectDealDia {
    .el-dialog__body {
        margin-top: 0px;
    }
}

</style>