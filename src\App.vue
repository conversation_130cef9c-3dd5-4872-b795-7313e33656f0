<template>
    <div id="app"  @click="isTimeOut">
        {{ msg }}
        <template v-if="n < 0">
            <div v-for="(item, index) in n" :key="index" @click="onClick">
                {{ item }}
            </div>
        </template>
        <router-view></router-view>
        <ComPop ref="ComPop" />
        <DiaLog ref="DiaLog" v-if="isVisible" />
    </div>
</template>

<script>
/* eslint-disable no-unused-vars */
import $ from 'jquery'
import { mapMutations, mapState } from 'vuex'
import { vueDom, hideLoading } from '@/utils/common.js'
import ComPop from '@/components/common/pop.vue'
import DiaLog from '@/components/common/dialog.vue'
import Vue from 'vue'
window.onload = function () {
    try {
        CefSharp.BindObjectAsync('callbackObj') // 注册客户端api
        // eslint-disable-next-line no-empty
    } catch (e) {}
}

export default {
    name: 'App',
    components: { ComPop, DiaLog },
    computed: {
        ...mapState(['popStatus', 'tableHeight', 'dialogVisible', 'setDialogParams']),
        isVisible () {
            return this.dialogVisible
        },
    },
    data () {
        return {
            lastTime: null, // 最后一次点击的时间
            currentTime: null, // 当前点击的时间
            timeOut: 20 * 60 * 1000, // 设置超时时间:30分钟
            turnGray: false,
            n: 10,
            msg: '',
            // 判断是否需要停止定时器
            count: 0,
            clientHeight: null,
        }
    },
    watch: {
        $route (to, from) {
            if(to.path == '/index') {
                document.querySelector('html').style.minWidth = '1400px'
            }else{
                document.querySelector('html').style.minWidth = '1326px'
            }
            vueDom(
                () => {
                    // 解决表单文字忘记加：
                    this.count = 0
                    $('.e-form .el-form-item__label').map((key, value) => {
                        const str = $(value).text()
                        const lastStr = str.charAt(str.length - 1)
                        // 如果没加分号
                        if (lastStr !== '：' && lastStr !== ':') {
                            $(value).text($(value).text() + '：')
                            this.count = 1
                        } else if (lastStr === ':') {
                            // 如果加的英文分号
                            $(value).text(str.replace(':', '：'))
                        }
                    })
                    // 解决form空白文本行高0px
                    $('.e-form .el-form-item__content').map((key, value) => {
                        // console.log(
                        //     value,
                        //     $(value).css('height'),
                        //     $(value).text(),
                        //     Boolean($(value).text())
                        // )
                        if (
                            $(value).css('height') === '0px' ||
                            // 判断更多边界情况
                            $(value).text().replace(/\s*/g, '') === '0'
                        ) {
                            $(value).css('height', '40px')
                            this.count = 1
                        }
                    })
                    try{
                        //给所有树形表格添加双击展开功能
                        $('#app').off('dblclick').on('dblclick', '.e-tree-table .el-table__row,.e-tree-table2 .el-table__row', function (e) {
                            $(this).find('.el-icon-arrow-right').trigger('click')
                        })
                    }catch(error) {
                        console.log( error)
                    }

                },
                () => {
                    return this.count !== 0
                }
            )
            this.changeBillTable()
            this.setTooltip()
            this.setShrinkage()
        },
    },
    created () {
        if(localStorage.getItem('token') != null) {
            let lastTime = localStorage.getItem('lastTime')
            this.currentTime = new Date().getTime()
            if (this.currentTime - lastTime > this.timeOut) {
                localStorage.removeItem('token')
                this.$store.commit('setUserInfo', {})
            }
        }
        this.handlePageFilter()
        hideLoading()
        let me = this
        // 信息弹窗回调
        window.confirmFn = function (obj) {
            // obj: 客户端回调时传递 “确定” 还是 “取消” 参数
            // 设置弹窗标志，供页面判断执行业务逻辑
            me.setPopConfirm(obj)
            me.setComPopConfirm(true)
        }
        this.clientHeight = document.documentElement.clientHeight
        this.changeDetailTable()
    },
    mounted () {
        Vue.prototype.clientPop = this.$refs.ComPop.pop
        window.addEventListener('resize', this.changeScreenWidth)
        this.setDialogVisible(false)
    },
    methods: {
        isTimeOut () {
            if(localStorage.getItem('token') != null) {
                localStorage.setItem('lastTime', new Date().getTime())
            }
        },
        ...mapMutations(['setSearchData', 'setPopStatus', 'setPopConfirm', 'setComPopConfirm', 'setTableHeight', 'setDialogVisible']),
        onClick () {},
        // 网站实现灰色效果
        handlePageFilter () {
            if(this.turnGray) $('html').addClass('gray')
        },
        //屏幕变化处理事件
        changeScreenWidth () {
            this.clientHeight = document.documentElement.clientHeight
            this.changeBillTable()
            this.changeDetailTable()
        },
        //动态改变台账列表高度
        changeBillTable () {
            let count = 0
            vueDom(
                () => {
                    if($('.billList').length >= 1) {
                        const pad = 20 //billList的padding
                        let height = 0
                        let el
                        $('.billList').children().map((key, value)=>{
                            const className = $(value).attr('class')
                            if(className) {
                                if(!className.includes('table')) { //获取除table外的节点高度
                                    height += $(value).outerHeight(true)
                                }else{
                                    el = $(value)//找到 billList下的table
                                }
                            }
                        })
                        let scrollHeight = 0
                        if($(window).width() - 20 < el.find('table').width()) {
                            scrollHeight =  18
                        }
                        let elHeight = this.clientHeight - height - pad - 1 //获取剩余高度
                        let headerHeight = el.find('.el-table__header-wrapper').height()
                        el.find('.el-table__body-wrapper').height(elHeight - headerHeight)
                        el.find('.el-table__fixed').height(elHeight)
                        el.height(elHeight)
                        count = 1
                    }
                },
                () => {
                    return count !== 0
                }
            )
        },
        //动态改变明细高度
        changeDetailTable () {
            this.setTableHeight(window.innerHeight - 110)
        },
        //给所有表格添加show-overflow-tooltip属性
        setTooltip () {
            let count = 0
            vueDom(
                () => {
                    $('#app').off('mouseover').on('mouseover', '.e-table .cell', function (e) {
                        const filterChild =  $(this).children().filter('.el-input')
                        if(filterChild.length === 0) {
                            const className = $(this).attr('class')
                            if(!className.includes('el-tooltip')) {
                                $(this).addClass('el-tooltip')
                            }
                        }else{
                            const className = $(this).attr('class')
                            if(className.includes('el-tooltip')) {
                                $(this).removeClass('el-tooltip')
                            }
                        }
                        count = 1
                    })
                },
                () => {
                    return count !== 0
                }
            )
        },
        //给所有明细页面添加收缩按钮
        setShrinkage () {
            let count = 0
            vueDom(
                () => {
                    if($('.header-before').length === 0 && $('.tabs').length > 0) {
                        $('.el-tabs .el-tabs__header').prepend('<div class="header-before"></div>')
                        $('.el-tabs').on('click', '.el-tabs__header .header-before', function () {
                            const parent = $(this).parents('.tabs')
                            const className = parent.attr('class')
                            if(className.includes('hide')) {
                                parent.removeClass('hide')
                            }else{
                                parent.addClass('hide')
                            }
                            count = 1
                        })
                    }
                },
                () => {
                    return count !== 0
                }
            )
        }
    }
}
</script>
<style>

@media screen and (max-width: 1354px) {
    .content-box {
        width: 100%;
    }
}
</style>