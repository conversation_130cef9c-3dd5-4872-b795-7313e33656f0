<template>
    <div>
        <main>
            <div class="shopBanner mb20 dfa">
                <div class="contentBox center dfa">
                    <img class="logo" :src="shopInfo.shopImg" alt="">
                    <div class="shopDetail">
                        <div class="title mb10">{{shopInfo.shopName}}</div>
                        <div class="info">地址：{{shopInfo.detailedAddress}}</div>
                        <div class="info mb10">主营：{{shopInfo.mainBusiness}}</div>
                      <!--  <div class="tags dfa">
                            <div>认证企业</div>
                            <div>信用：132</div>
                            <div>入驻：712天</div>//../../../../assets/images/shop/banner.png
                        </div>-->
                    </div>
                    <img class="banner" :src="shopInfo.adImg" alt="">
                </div>
            </div>
            <div class="shopSection center">
                <div class="tabs dfa">
                    <div :style="{ background: currentSection == 1 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 1">企业简介</div>
                    <div :style="{ background: currentSection == 0 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 0">商品</div>
                    <!-- <div :style="{ background: currentSection == 1 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 1">需求公告</div> -->

                    <!-- <div :style="{ background: currentSection == 3 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 3">经营业绩</div>
                    <div :style="{ background: currentSection == 4 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 4">专项资质</div>
                    <div :style="{ background: currentSection == 5 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 5">经营动态</div>
                    <div :style="{ background: currentSection == 6 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 6">信用荣誉</div>
                    <div :style="{ background: currentSection == 7 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 7">知识产权</div>
                    <div :style="{ background: currentSection == 8 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 8">宣传展示</div> -->
                </div>
            </div>
            <KeepAlive>
                <!-- 店铺商品 -->
                <products v-if="currentSection === 0"></products>
            </KeepAlive>
            <KeepAlive>
                <!-- 店铺信息 -->
                <shopInfo v-if="currentSection === 1"></shopInfo>
            </KeepAlive>
        </main>
    </div>
</template>

<script>
import products from './products.vue'
import shopInfo from './shopInfo.vue'
import { getShopInfo } from '@/api/frontStage/shop'
export default {
    components: { products, shopInfo },
    data () {
        return {
            currentSection: 1,
            startPlace: 1,
            destination: 2,
            places: [
                { label: '北京', value: 1 },
                { label: '上海', value: 2 },
            ],
            shopId: '',
            shopInfo: {}
        }
    },
    created () {
        this.shopId = this.$route.query.shopId
        let params = {
            shopId: this.shopId,
        }
        getShopInfo(params).then(res => {
            this.shopInfo = res.shop
        })

    },
    methods: {

    },
}
</script>

<style scoped lang="scss">
main {
    width: 100%;
    padding-bottom: 105px;
    background-color: #f5f5f5;
}
.contentBox {
    width: 1326px;
    min-width: 1326px;
    height: 100%;
}
.shopBanner {
    width: 100%;
    height: 170px;
    background-color: #e0e9f3;
    .logo {
        width: 118px;
        height: 120px;
        margin-right: 18px;
    }
    .shopDetail {
        width: 410px;
        .title {font-size: 20px;}
        .info {
            width: 302px;
            font-size: 16px;
            color: rgba(102, 102, 102, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .info:first-child {margin-bottom: 4px;}
        .tags div {
            padding: 2.5px 12px;
            font-size: 14px;
            border: 1px solid;
            border-radius: 2px;
            margin-right: 10px;
            &:first-child {
                color: rgba(255, 141, 26, 1);
                border-color: rgba(255, 141, 26, 1);
            }

            &:nth-child(2) {
                color: rgba(42, 130, 228, 1);
                border-color: rgba(42, 130, 228, 1);
            }
            &:last-child {
                color: rgba(153, 153, 153, 1);
                border-color: rgba(153, 153, 153, 1);
            }
        }
    }
    .banner {width: 780px;height: 150px;}
}
.shopSection {
    width: 1326px;
    color: #fff;
    .tabs {
        width: 100%;
        height: 52px;
        background-color: #333333;
        user-select: none;
        div {
            width: 147px;
            height: 52px;
            line-height: 52px;
            text-align: center;
            font-size: 20px;
            font-weight: 400;
            cursor: pointer;
        }
    }
}
</style>