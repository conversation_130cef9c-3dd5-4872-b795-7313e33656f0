<template>
    <main class="userCenter">
        <div class="title">我的关注</div>
        <div class="content p20">
            <div class="tabs mb20 dfb">
                <div class="tab df">
                    <div :class="activeTab == 0 ? 'active' : ''" @click="activeTab = 0">关注的商品</div>
                    <!-- <div :class="activeTab == 1 ? 'active' : ''" @click="activeTab = 1">关注的店铺</div> -->
                </div>
            </div>
            <div class="productList df">
                <div
                    class="productItem mb20" v-for="(item, i) in productList" :key="item.productId"
                    @click="handleViewProduct(item.productId)"
                >
                    <div class="imgBox">
                        <img
                            :src="item.pictureUrl ? imgUrlPrefixAdd + item.pictureUrl : require('@/assets/images/img/queshen5.png')"
                            alt=""
                        >
                        <div class="delIcon" v-show="!(showCover == i)" @click.stop="handleClickIcon(i)"><img
                            src="@/assets/images/userCenter/delete.png" alt=""
                        ></div>
                    </div>
                    <div class="productName textOverflow1">{{ item.title }}</div>
                    <div class="price">
                        <span>￥{{ item.price }}</span>
                        <span v-if="item.remain < 10">仅剩{{ item.remain }}件</span>
                    </div>
                    <!-- 删除遮罩 -->
                    <div class="delCover" v-show="showCover == i">
                        <div class="delBox">
                            <div class="mb20">确定删除？</div>
                            <div class="dfb">
                                <div @click.stop="handleDelete(item.productId)">确定</div>
                                <div @click.stop="showCover = null">取消</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <pagination
                :currentPage="page.currPage" :pageSize="page.pageSize" :destination="destination"
                :total="page.totalCount" :totalPage="page.totalCount / 8" @currentChange="currentChange"
            ></pagination>
        </div>
    </main>
</template>
<script>
import pagination from '@/pages/frontStage/components/pagination'
import { getCollectListApi, deleById } from '@/api/frontStage/productCollect'

export default {
    components: { pagination },
    data () {
        return {
            activeTab: 0,
            showCover: null,
            productList: [],
            page: {
                totalCount: 24,
                currPage: 1,
                pageSize: 8,
            },
            queryAll: {
                label: {},
                orderBy: '0',
            },
            destination: 2,
        }
    },
    watch: {
        activeTab (tab) {
            // 切换标签
            console.log(tab)
        },
    },
    created () {
        this.getCollectList()
    },
    mounted () {
    },
    methods: {
        // 获取收藏列表
        getCollectList () {
            let params = {
                page: this.page.currPage,
                limit: this.page.pageSize,
                orderBy: this.queryAll.orderBy,
            }
            getCollectListApi(params).then(res => {
                this.productList = res.list
                this.page.currPage = res.currPage
                this.page.pageSize = res.pageSize
                this.page.totalCount = res.totalCount
            })
        },
        // 查看商品详情
        handleViewProduct (id) {
            this.$router.push({ path: '/mFront/productDetail', query: { productId: id } })
        },
        // 点击删除图标
        handleClickIcon (i) {
            this.showCover = i
        },
        // 删除收藏
        handleDelete (index) {
            this.productList.forEach((item, i) => {
                if (item.productId == index) {

                    deleById({ id: item.collectId }).then(res => {
                        if (res.code == 200) {
                            this.clientPop('suc', res.message, () => {
                                this.getCollectList()
                            })

                        }
                    })

                    this.productList.splice(i, 1)
                    return
                }
            })
            this.showCover = null
        },
        currentChange (index) {
            this.page.currPage = index
            this.getCollectList()

        },
    },
}
</script>
<style scoped lang="scss">
main {
    height: 910px;
    border: 1px solid rgba(230, 230, 230, 1);
}

.productList {
    height: 696px;
    flex-wrap: wrap;
    overflow: scroll;

    &::-webkit-scrollbar {
        display: none;
    }

    .productItem {
        width: 251px;
        height: 328px;
        border: 1px solid rgba(230, 230, 230, 1);
        position: relative;

        &:hover {
            .imgBox .delIcon {
                display: block;
            }
        }

        &:not(:nth-of-type(4n)) {
            margin-right: 20px;
        }

        .imgBox {
            margin-bottom: 12px;
            position: relative;

            .delIcon {
                width: 44px;
                height: 44px;
                background: rgba(51, 51, 51, 1);
                position: absolute;
                top: 0;
                right: 0;
                display: none;
                cursor: pointer;

                img {
                    width: 20px;
                    margin: 12px;
                }
            }
        }

        .imgBox, .imgBox > img {
            width: 251px;
            height: 251px;
            object-fit: cover;
        }

        .productName {
            width: 224px;
            margin-left: 10px;
            margin-bottom: 10px;
        }

        .price {
            margin-left: 10px;

            span:last-child {
                font-size: 12px;
                color: rgba(212, 48, 48, 1);
            }

            span:first-child {
                margin-right: 100px;
                font-size: 16px;
                color: rgba(56, 56, 56, 1);
            }
        }

        .delCover {
            width: 251px;
            height: 328px;
            background-color: rgba(0, 0, 0, 0.7);
            position: absolute;
            top: 0;
            left: 0;

            .delBox {
                width: 191px;
                margin: 122px 30px;

                .mb20 {
                    font-size: 18px;
                    text-align: center;
                    color: #fff;
                }

                .dfb {
                    div {
                        width: 88px;
                        height: 36px;
                        font-size: 16px;
                        line-height: 36px;
                        text-align: center;
                        cursor: pointer;
                    }

                    div:first-child {
                        color: #fff;
                        background-color: rgba(212, 48, 48, 1);
                    }

                    div:last-child {
                        color: black;
                        background-color: #fff;
                    }
                }
            }
        }
    }
}
</style>