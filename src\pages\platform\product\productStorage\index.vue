<template>
    <div class="base-page">
        <div class="left">
            <select-material-class ref="materialClassRef" :productType="0"/>
        </div>
        <!-- 列表 -->
        <div class="right" v-show="viewList==true">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn dfa">
                            <el-button type="primary" @click="createData()">新增</el-button>
                            <el-button @click="updateStateBatch(true)" class="btn-greenYellow">批量启用
                            </el-button>
                            <el-button @click="updateStateBatch(false)" class="btn-delete">批量停用
                            </el-button>
                            <el-upload
                                multiple action="fileUrl"
                                :limit="limitNum"
                                accept=".xls,.xlsx,csv"
                                :file-list="fileList"
                                :before-upload="beforeUpload"
                                :on-exceed="onExceed"
                                :show-file-list="true"
                                :http-request="uploadFile"
                                style="margin-left: 10px"
                            >
                                <el-button type="primary">上传excel</el-button>
                            </el-upload>
                            <el-dropdown @command="btnClick" trigger="click" style="margin-left: 10px">
                                <el-button type="primary">
                                    更多操作<i class="el-icon-arrow-down el-icon--right"></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item  command="outputExcelM">导出低值易耗品</el-dropdown-item>
                                    <el-dropdown-item  command="loadputExcelM">下载模板</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <!--<el-button type="primary" @click="exportExcel()">导出低值易耗品</el-button>-->
                        </div>
                    </div>
                    <div class="search_box" style="width: 400px">
                        <el-input
                            clearable type="text" @blur="handleInputSearch" placeholder="输入搜索关键字"
                            v-model="init.keyword"
                        >
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table" style="width: 100%;">
                <el-table
                    v-loading="tableLoading" class="table" :height="rightTableHeight" :data="tableData" border
                    @selection-change="selectionChangeHandle" @row-click="handleCurrentInventoryClick"
                    ref="mainTable"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="130">
                        <template v-slot="scope">
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.isEnable==0"
                                size="mini"
                                type="success"
                                @click="updateState(scope.row,true,'启用')"
                            >启用
                            </el-button>
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.isEnable==1"
                                size="mini"
                                type="danger"
                                @click="updateState(scope.row,false,'停用')"
                            >停用
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" width="130">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.isEnable==1" type="success">启用</el-tag>
                            <el-tag v-if="scope.row.isEnable==0" type="danger">停用</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="物料编号" width="" prop="billNo"/>
                    <!--                    <el-table-column label="物料编号" width="200" prop="materialNo"/>-->
                    <el-table-column label="物料名称" width="" prop="materialName">
                        <template v-slot="scope">
                             <span class="action noSliceSpace" @click="handleView(scope.row)">
                                 {{ scope.row.materialName }}
                             </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="物料分类" width="" prop="className"/>
                    <el-table-column label="物料路径" width="" prop="classNamePath"/>
                    <el-table-column label="规格" width="" prop="spec"/>
                    <el-table-column label="单位" width="" prop="unit"/>
                </el-table>
            </div>
            <Pagination
                v-show="tableData && tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--高级查询-->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="状态：">
                            <el-select @change="stateOptionsClick" v-model="stateOptionTitle" placeholder="请选择状态">
                                <el-option
                                    v-for="item in stateOptions" :key="item.value" :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="物料编号：">
                            <el-input clearable v-model="filterData.materialNo" placeholder="请输入物料编号"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="物料名称：">
                            <el-input
                                clearable v-model="filterData.materialName"
                                placeholder="请输入物料名称"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="规格：">
                            <el-input clearable v-model="filterData.spec" placeholder="请输入规格"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="单位：">
                            <el-input clearable v-model="filterData.unit" placeholder="请输入单位"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
        <div class="right" v-show="viewList !== true">
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList==='action'||viewList==='class'">
                <div class="tabs-title">基本信息</div>
                <el-form ref="formEdit" :rules="formRules" :model="materialInfo" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item  v-if="viewList==='class'" label="分类：" prop="classId">
                                <category-cascader
                                    :classPath.sync="materialInfo.classPath"
                                    :catelogPath="materialInfo.classPath"
                                    :classId.sync="materialInfo.classId"
                                    :productType="0"
                                    change-on-select
                                    @change="handleCascaderChange"
                                ></category-cascader>
                            </el-form-item>
                            <el-form-item  v-else label="分类：" prop="classId">
                                <category-cascader disabled
                                                   :classPath.sync="materialInfo.classPath"
                                                   :catelogPath="materialInfo.classPath"
                                                   :classId.sync="materialInfo.classId"
                                                   :productType="0"
                                                   change-on-select
                                                   @change="handleCascaderChange"
                                ></category-cascader>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-show="viewList==='action'">
                        <el-col :span="12">
                            <el-form-item label="物料编号：" prop="billNo">
                                <el-input clearable v-model="materialInfo.billNo" disabled>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="物料名称：" prop="materialName">
                                <el-input v-if="viewList!='action'" clearable v-model="materialInfo.materialName">
                                </el-input>
                                <el-input disabled v-if="viewList==='action'" clearable
                                          v-model="materialInfo.materialName">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="规格：" prop="spec">
                                <el-input clearable v-model="materialInfo.spec">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="计量单位：" prop="unit">
                                <el-input clearable v-model="materialInfo.unit">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="状态：" prop="isEnable">
                                <el-switch
                                    v-model="materialInfo.isEnable"
                                    active-color="#13ce66"
                                    inactive-color="#ff4949"
                                    active-text="启用"
                                    inactive-text="停用"
                                    :active-value="1"
                                    :inactive-value="0"
                                >
                                </el-switch>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="buttons">
                        <el-button type="success" v-show="viewList == 'class'"  @click="onSave()">保存</el-button>
                        <el-button type="success" v-show="viewList !== 'class'" @click="updateM()">修改</el-button>
                        <el-button type="warning" v-show="viewList == 'class'" @click="onSave(true)">保存并下一个
                        </el-button>
                        <el-button type="fail" @click="closeViewListM()">取消</el-button>
                    </div>
                </el-form>
            </div>

        </div>
        <el-dialog v-dialogDrag title="导入结果" :visible.sync="platformImportExcelLoading" width="70%">
            <div class="e-table" style="background-color: #fff">
                <el-table
                    border
                    :data="excelResult"
                    class="table"
                    :max-height="$store.state.tableHeight"
                    ref="mainTable"
                >
                    <el-table-column prop="id" label="序号" width="60"></el-table-column>
                    <el-table-column prop="materialName" label="物资名称"></el-table-column>
                    <el-table-column prop="spec" label="规格"></el-table-column>
                    <el-table-column prop="unit" label="单位"></el-table-column>
                    <el-table-column prop="classNamePath" label="状态" width="80">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.success==true" type="success">成功</el-tag>
                            <el-tag v-if="scope.row.success==false" type="danger">失败</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="message" label="失败原因"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <el-button class="mt20" @click="platformImportExcelLoading = false">取消</el-button>
            </span>
        </el-dialog>

    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import CategoryCascader from '@/components/category-cascader.vue'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapState } from 'vuex'
import {
    batchUpdateMaterialDtlState,
    excelExport,
    outputExcel,
    queryPageMaterialDtl,
    saveMaterialInfo,
    updateBatch,

} from '@/api/platform/product/materialManage'

import { platforUploadMaterialExcelFile } from '@/api/platform/product/productCategory.js'
import { excelTemplate } from '@/api/shopManage/product/materialManage'

export default {
    components: { SelectMaterialClass, Pagination, CategoryCascader },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        },
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            viewList: true,
            limitNum: 1, //文件上传个数限制
            fileList: [], //文件列表
            excelResult: [],
            queryVisible: false,
            platformImportExcelLoading: false,
            action: '',
            materialInfo: {
                billId: '',
                billNo: '',
                classId: '',
                className: '',
                classPath: [],
                isEnable: 1,
                materialName: '',
                spec: '',
                unit: '',
            },
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                classId: null,
                keyword: '',
                classPath: [],
                className: null

            },
            classPath: [],
            // 商品库
            inventory: {
                selectRow: [],
                tableData: [],
                keyWord: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            stateOptionTitle: 1,
            stateOptions: [
                {
                    value: null,
                    label: '全部'
                }, {
                    value: 0,
                    label: '停用'
                }, {
                    value: 1,
                    label: '启用'
                }],
            dataListSelections: [], //表格选中的数据
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                materialName: '',
                materialNo: '',
                isActive: 1,
                spec: '',
                unit: ''
            },
            screenHeight: 0,
            formRules: {
                classId:
                    [{ required: true, message: '请选择分类', trigger: 'blur' }],
                isEnable: [{ required: true, message: '请选择状态', trigger: 'blur' }],
                materialName: [{ required: true, message: '请输入物料名称', trigger: 'blur' }],
            }
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.orgId = this.userInfo.orgInfo.orgId
    },
    methods: {
        // 状态下拉框点击
        stateOptionsClick (value) {
            this.stateOptionTitle = value
        },
        // 选择分类
        handleCascaderChange (/*value*/) {
        },
        createData () {
            this.closeViewList()
            this.viewList = 'class'
            this.action = '新增'
            // this.materialInfo.classId = this.init.classId
            // this.materialInfo.className = this.init.className
        },

        updateM (again = false) {
            this.$refs.formEdit.validate(async valid => {
                if (!valid) return
                if (this.materialInfo.classId != null) {
                    let a = this.materialInfo.classPath
                    this.materialInfo.classPath = null
                    this.clientPop('info', '你确定修改物资基础库吗？', async () => {
                        let res = await saveMaterialInfo(this.materialInfo)
                        if (res.code !== 200) return
                        this.$message({ type: 'success', message: '物料添加成功' })
                        if (again) {
                            this.materialInfo.materialName = ''
                            this.materialInfo.billId = ''
                            this.materialInfo.classPath = a
                            return
                        }
                        this.viewList = true
                        this.getTableData()
                    })

                } else {
                    this.$message({ message: '请先选择右侧最底层分类', type: 'warning', })
                }
            })
        },
        onSave (again = false) {
            this.materialInfo.billId = ''
            let a = this.materialInfo.classPath
            this.materialInfo.classPath = null
            this.$refs.formEdit.validate(async valid => {
                if (!valid) return
                if (this.materialInfo.classId != null) {
                    let res = await saveMaterialInfo(this.materialInfo)
                    if (res.code !== 200) return
                    this.$message({ type: 'success', message: '物料添加成功' })
                    if (again) {
                        this.materialInfo.materialName = ''
                        this.materialInfo.billId = ''
                        this.materialInfo.classPath = a
                        return
                    }
                    this.viewList = true
                    this.getTableData()
                } else {
                    this.$message({ message: '请先选择右侧最底层分类', type: 'warning', })
                }
            })
        },
        exportExcel () {
            this.$confirm('确认导出文件吗？', '提示', {}).then(() => {
                let params = {
                    classId: '1',
                    state: '1',
                }
                excelExport(params).then(
                    res => {
                        const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                        const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                        const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                        a.href = url
                        a.download = '低值易耗品物资数据.xlsx'
                        a.click()
                        this.$message.success('操作成功')
                        this.tableLoading = false
                    }
                ).catch(res => {
                    this.$message.error(res.code)
                })
            })
        },
        btnClick (command) {
            let actions = {
                'outputExcelM': () => this.outputExcelM(),
                'loadputExcelM': () => this.loadputExcelM(),
            }
            actions[command]()
        },

        uploadFile (res) {
            let formData = new FormData()
            formData.append('file', res.file)
            this.tableLoading = true
            // eslint-disable-next-line
            platforUploadMaterialExcelFile(formData).then(res => {
                this.tableLoading = false
                if(res.data) {
                    this.showExcelResult(res)
                }
                this.tableLoading = true
                this.getTableData()
                this.fileList = []
            }).catch(() => {
                this.getTableData()
                this.tableLoading = false
                this.fileList = []
            })
        },

        showExcelResult (result) {
            this.excelResult = result
            this.platformImportExcelLoading = true
        },
        beforeUpload (file) {
            let regExp = file.name.replace(/.+\./, '')
            let lower = regExp.toLowerCase() //把大写字符串全部转为小写字符串
            let suffix = ['xls', 'xlsx']
            if (suffix.indexOf(lower) === -1) {
                return this.$message.warning('请上传后缀名为 xls、xlsx 的附件 !')
            }
            let isLt2M = file.size / 1024 / 1024 < 5
            if (!isLt2M) {
                return this.$message.error('请上传文件大小不能超过 5MB 的附件 !')
            }
        },
        //文件超出个数限制时的钩子
        onExceed (files, fileList) {
            return this.$message.warning(`只能选择${this.limitNum}个文件,当前共选择了${files.length + fileList.length}个`)
        },
        loadputExcelM () {
            excelTemplate({ productType: 6 }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '物资基础库模板.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
            })
        },
        outputExcelM () {
            this.$confirm('确认导出文件吗？', '提示', {}).then(() => {
                let params = {
                    classId: '1',
                    state: 1,
                }
                this.tableLoading = true
                outputExcel(params).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '低值易耗品数据.xlsx'
                    a.click()
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },

        closeViewList () {
            this.materialInfo.billNo = null
            this.materialInfo.billId = null
            this.materialInfo.isEnable = 1
            this.materialInfo.materialName = null
            this.materialInfo.spec = null
            this.materialInfo.unit = null
            this.materialInfo.classPath = null
            this.queryVisible = false
        },
        closeViewListM () {
            this.viewList = true
            this.materialInfo.billNo = null
            this.materialInfo.billId = null
            this.materialInfo.isEnable = 0
            this.materialInfo.materialName = null
            this.materialInfo.spec = null
            this.materialInfo.unit = null
        },

        // 开始导入
        inventoryImportBatch () {
            if (this.inventory.selectRow.length === 0) {
                this.$message('请勾选要导入的数据！')
            }
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        // 选择
        selectDeviceRow (value) {
            this.inventory.selectRow = value
        },
        // 批量修改排序
        changeSortValue () {
            if (this.changedRow.length === 0) {
                return this.$message('未修改列表当中的排序值！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                let res = await updateBatch(this.changedRow)
                this.getTableData()
                this.changedRow = []
                this.tableLoading = false
                this.message(res)
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if (row.sort <= 0) row.sort = 0
            if (this.changedRow.length === 0) {
                this.changedRow.push({ productId: row.productId, sort: row.sort })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.productId !== row.productId) return
                t.sort = row.sort
                flag = true
            })
            if (!flag) {
                this.changedRow.push({ productId: row.productId, sort: row.sort })
            }
            flag = true
        },

        // 物料详情
        handleView (row) {
            this.viewList = 'action'
            this.action = '编辑'
            this.materialInfo.classId = row.classId
            this.materialInfo.className = row.className
            this.materialInfo.unit = row.unit
            this.materialInfo.spec = row.spec
            this.materialInfo.materialName = row.materialName
            this.materialInfo.billId = row.billId
            this.materialInfo.billNo = row.billNo
            this.materialInfo.classPath = row.classIdPath.split('/')
            this.updateclassPath = row.classIdPath.split('/')

        },
        resetSearchConditions () {
            this.filterData.materialName = ''
            this.filterData.materialNo = ''
            this.filterData.spec = ''
            this.filterData.unit = ''
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 高级搜索确认
        confirmSearch () {
            this.init.keyWord = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateOptions.length
        },
        // 状态全选
        stateAllSelect (value) {
            if (value) {
                this.filterData.state = this.filterData.stateOptions.map(t => {
                    return t.value
                })
            } else {
                this.filterData.state = []
            }
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 单个上架
        updateState (row, state, title) {
            let { materialName, topClassName } = row
            if (topClassName !== '低值易耗品') {
                return this.$message({
                    message: materialName + '不是低值易耗品，不能操作',
                    type: 'error'
                })
            }
            this.clientPop('info', '您确定要对物料【' + materialName + '】进行【' + title + '】操作吗?', async () => {
                let res = await batchUpdateMaterialDtlState({ MaterialDtlDTOList: [row], state })
                this.getTableData()
                this.message(res)
            })
        },
        // 批量修改状态
        updateStateBatch (state) {
            if (this.dataListSelections.length === 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let invalidList = this.dataListSelections.filter(item => !(item.topClassName === '低值易耗品'))
            let validList = this.dataListSelections.filter(item => (item.topClassName === '低值易耗品'))
            let MaterialDtlDTOList = []
            if (validList.length === 0) {
                return this.$message('请选择低值易耗品进行操作！')
            } else {
                for (let i = 0; i < validList.length; i++) {
                    MaterialDtlDTOList.push(validList[i])
                }
            }
            if (invalidList.length > 0) {
                let materialNames = invalidList.map(item => item.materialName).join(',')
                this.$message.error(materialNames + '不是低值易耗品，不能操作')
            }

            this.clientPop('info', '您确定要批量启用/停用这些物料吗！', async () => {
                this.tableLoading = true
                try {
                    let res = await batchUpdateMaterialDtlState({ MaterialDtlDTOList, state })
                    this.tableLoading = false
                    this.dataListSelections = []
                    this.message(res)
                    this.getTableData()
                } catch (e) {
                    this.tableLoading = false
                }
            })
        },
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.classPath = nodePath
            this.materialInfo.classPath = nodePath
            this.init.className = data.className
            if (data.children == null) {
                this.materialInfo.className = data.className
                this.materialInfo.classId = data.classId
            }
            this.getTableData()
        },
        // 获取表格数据
        getTableData () {
            let params = {
                pageIndex: this.paginationInfo.currentPage,
                pageSize: this.paginationInfo.pageSize,
                keyWord: this.init.keyword.trim()
            }
            if (this.filterData.materialName != null) {
                params.materialName = this.filterData.materialName
            }
            if (this.filterData.spec != null) {
                params.spec = this.filterData.spec
            }
            if (this.filterData.unit != null) {
                params.unit = this.filterData.unit
            }
            if (this.filterData.materialNo != null) {
                params.materialNo = this.filterData.materialNo
            }
            if (this.init.classId != null) {
                params.classId = this.init.classId
            }
            if (this.stateOptionTitle != null) {
                params.isActive = this.stateOptionTitle
            }
            this.tableLoading = true
            queryPageMaterialDtl(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => this.tableLoading = false)
        },
        // 消息提示
        message (res) {
            if (res.code !== 200) return
            this.$message({ message: res.message, type: 'success' })
        },
        getScreenInfo () {
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type=‘number’] {
    -moz-appearance: textfield !important;
}

.el-dialog__body {
    margin: 220px;
}

.base-page .left {
    min-width: 200px;
    height: 100%;
    padding: 0;
    overflow: scroll;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .el-dropdown {
    min-width: 75px;
    margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0;
    }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>
