<template>
    <div class="base-page">
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" v-if="isYearServe.isShopFeeServeBtn == 1" @click="addFree(1)">店铺年费续费</el-button>
                            <el-button type="primary" v-if="isYearServe.isBidFeeServeBtn == 1" @click="addFree(2)">招标年费续费</el-button>
                            <el-button type="primary" @click="addDealFree">交易服务费缴费</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-dropdown @command="handleChangeSort" trigger="click" placement="bottom">
                        <span class="pointer">
                            排序方式<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="0" :style="{ color: filterData.orderBy == 0 ? '#2e61d7' : '' }">
                                    按缴费时间排序
                                </el-dropdown-item>
                                <el-dropdown-item :command="1" :style="{ color: filterData.orderBy == 1 ? '#2e61d7' : '' }">
                                    按修改时间排序
                                </el-dropdown-item>
                                <el-dropdown-item :command="2" :style="{ color: filterData.orderBy == 2 ? '#2e61d7' : '' }">
                                    按审核时间排序
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
<!--                        <el-radio v-model="filterData.orderBy" :label="0">按缴费时间排序</el-radio>-->
<!--                        <el-radio v-model="filterData.orderBy" :label="1">按修改时间排序</el-radio>-->
<!--                        <el-radio v-model="filterData.orderBy" :label="2">按审核时间排序</el-radio>-->
                        <el-input v-model="keywords" clearable placeholder="输入搜索关键字" style="width: 200px;margin-left: 20px" type="text" @blur="handleInputSearch">
                            <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    ref="tableRef"
                    v-loading="tableLoading"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                    class="table"
                    @selection-change="tableSelectM"
                    @row-click="tableRowClickM"
                >
<!--                    <el-table-column type="selection" width="40"></el-table-column>-->
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="编号" prop="paymentRecordUn" width="200">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.paymentRecordUn }}</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="店铺名称" prop="shopName" width=""/>-->
<!--                    <el-table-column label="企业名称" prop="enterpriseName" width=""/>-->
                    <el-table-column label="缴费时长" prop="paymentDuration" width=""/>
                    <el-table-column label="缴费时长单位" prop="paymentDurationType" width="">
                        <template v-slot="scope">
                            <span  v-if="scope.row.paymentDurationType == 1">天</span>
                            <span  v-if="scope.row.paymentDurationType == 2">周</span>
                            <span  v-if="scope.row.paymentDurationType == 3">月</span>
                            <span  v-if="scope.row.paymentDurationType == 4">年</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="缴费金额（元）" prop="payAmount" width="">
                        <template v-slot="scope">
                            <span style="color: red">{{scope.row.payAmount}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="服务类型" prop="recordType" width="140">
                        <template v-slot="scope">
                            <el-tag  v-if="scope.row.recordType == 1 && scope.row.paymentDurationType != null">店铺年度服务费</el-tag>
                            <el-tag  v-if="scope.row.recordType == 2 && scope.row.paymentDurationType != null">电子招标年度服务费</el-tag>
                            <el-tag  v-if="scope.row.recordType == 1 && scope.row.paymentDurationType == null">店铺交易服务费</el-tag>
                            <el-tag  v-if="scope.row.recordType == 2 && scope.row.paymentDurationType == null">合同履约交易服务费</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="state" width="100">
                        <template v-slot="scope">
                            <el-tag  type="info" v-if="scope.row.state == 0">草稿</el-tag>
                            <el-tag  v-if="scope.row.state == 1">待审核</el-tag>
                            <el-tag  type="success" v-if="scope.row.state == 2">审核通过</el-tag>
                            <el-tag  type="danger" v-if="scope.row.state == 3">审核未通过</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="审核时间" prop="auditOpenTime" width="160"/>
                    <el-table-column label="缴费时间" prop="gmtCreate" width="160"/>
                    <el-table-column label="修改时间" prop="gmtModified" width="160"/>
                </el-table>
            </div>
            <Pagination
                v-show="tableData && tableData.length > 0"
                :currentPage.sync="paginationInfo.currentPage"
                :pageSize.sync="paginationInfo.pageSize"
                :total="paginationInfo.total"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费编号：">
                            <el-input clearable maxlength="100" placeholder="请输入缴费编号" v-model="filterData.paymentRecordUn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                下拉框方式选择状态-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="服务类型：">
                            <el-select v-model="filterData.recordType" placeholder="请选择服务类型">
                                <el-option
                                    v-for="item in filterData.recordTypeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="状态：">
                            <div style="display:flex">
                                <el-checkbox
                                    v-model="filterData.stateCheckAll" @change="stateAllSelect"
                                >全部
                                </el-checkbox>
                                <el-checkbox-group
                                    style="margin-left: 30px" v-model="filterData.states" @change="stateGroupChange">
                                    <el-checkbox
                                        v-for="option in filterData.stateSelect"
                                        :key="option.value"
                                        :label="option.value"
                                    >{{ option.label }}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.gmtCreate"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="修改时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.gmtModified"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="审核时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.auditOpenTime"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>

        <el-dialog
            v-loading="showAddFeeLoading" class="addDia" top="5vh" :title="addFeeTitle " v-dialogDrag :visible.sync="showAddFee" width="70%">
            <el-form label-width="200px"  ref="showAddFeeRef"  :data="addFeeForm"  :rules="showAddFeeRoles">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户公司名称：">
                            {{platformAccountObj.platformFreeyhOrgName}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户开户行：">
                            {{platformAccountObj.platformFreeyhAddress}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收款账户：">
                            {{platformAccountObj.platformFreeyhAccount}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费方式：" prop="payType">
                            <el-radio v-model="addFeeForm.payType" :label="1" >线下</el-radio>
                            <el-radio v-model="addFeeForm.payType" disabled :label="2" >线上</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="店铺年度服务到期时间：" prop="" v-if="addFeeForm.recordType == 1">
                            {{platformAccountObj.shopYearServerEndTime}}
                        </el-form-item>
                        <el-form-item label="电子招标年度服务到期时间：" prop="" v-if="addFeeForm.recordType == 2">
                            {{platformAccountObj.bidYearServerEndTime}}
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                <el-row>-->
<!--                    <el-col :span="24">-->
<!--                        <el-form-item label="缴费类型：" prop="recordType">-->
<!--                            <span v-if="addFeeForm.recordType == 1">店铺年度服务费</span>-->
<!--                            <span v-if="addFeeForm.recordType == 2">电子招标年度服务费</span>-->
<!--&lt;!&ndash;                            <el-radio v-model="addFeeForm.recordType" :label="1" >店铺年度服务费</el-radio>&ndash;&gt;-->
<!--&lt;!&ndash;                            <el-radio v-model="addFeeForm.recordType" :label="2" >电子招标年度服务费</el-radio>&ndash;&gt;-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                </el-row>-->
                <el-row v-if="addFeeForm.recordType == 1 || addFeeForm.recordType == 2">
                    <el-col :span="12">
                        <el-form-item label="缴费时长单位：" prop="paymentDurationType">
                            <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="1" >天</el-radio>-->
                            <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="2" >周</el-radio>-->
                            <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="3" >月</el-radio>-->
                            <el-radio v-model="addFeeForm.paymentDurationType" :label="4" >年</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收费标准" prop="tot">
                            <span style="color: red">2000元/年</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" v-if="addFeeForm.recordType == 1 || addFeeForm.recordType == 2">
                        <el-form-item label="缴费时长：" prop="paymentDuration">
                            <el-input placeholder="请输入缴费时长" clearable type="number"  @change="checkInputQty" v-model="addFeeForm.paymentDuration"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="addFeeForm.recordType == 3 || addFeeForm.recordType == 4">
                        <el-form-item label="缴费金额（元）：" prop="payAmount">
                            <el-input
                                placeholder="请输入缴费金额"
                                type="number"
                                clearable
                                v-model="addFeeForm.payAmount"
                                @change="payAmountChange"
                            >
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="addFeeForm.recordType == 1 || addFeeForm.recordType == 2">
                        <el-form-item label="缴费金额（元）：" prop="payAmount">
                           <span style="color: red">
                               {{addFeeForm.payAmount}}
                           </span>
<!--                            <el-input-->
<!--                                placeholder="请输入缴费金额"-->
<!--                                type="number"-->
<!--                                clearable-->
<!--                                v-model="addFeeForm.payAmount"-->
<!--                                @change="payAmountChange">-->
<!--                            </el-input>-->
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input
                                type="textarea"
                                :auto-resize="false"
                                v-model="addFeeForm.remarks"
                                placeholder="请输入备注，格式：单位名称+缴费金额" maxlength="1000"
                                show-word-limit
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item  label="缴费证明：" prop="file">
                            <el-upload
                                :class="addFeeForm.files.length === 1 ? 'hide_box_min' : ''"
                                v-loading="uploadLoading"
                                class="upload-demo"
                                action="fakeaction"
                                :limit="1"
                                :file-list="fileList"
                                :before-upload="handleBeforeUpload"
                                :auto-upload="true"
                                :http-request="uploadLicenseBusiness"
                                list-type="picture-card">
                                <div slot="tip" class="el-upload__tip">只能上传图片文件</div>
                                <i slot="default" class="el-icon-plus"></i>
                                <div slot="file" slot-scope="{file}">
                                    <img
                                        class="el-upload-list__item-thumbnail"
                                        :src="file.url" alt="">
                                    <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="formDtlFileRemove(file)">
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </span>
                                </div>
                            </el-upload>
                            </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" class="btn-greenYellow" @click="save(0)">保存</el-button>
                <el-button type="primary" class="btn-greenYellow"  @click="save(1)">保存并提交</el-button>
                <el-button @click="showAddFee = false">取消</el-button>
            </span>
        </el-dialog>

        <el-dialog
            v-loading="showAddFeeLoading2" class="addDia" top="5vh" title="合同履约及店铺交易服务费缴费 " v-dialogDrag :visible.sync="showAddFee2" width="70%">
            <el-form label-width="140px"  ref="showAddFeeRef"  :data="addFeeForm2"  :rules="showAddFeeRoles2">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户公司名称：">
                            {{platformAccountObj.platformFreeyhOrgName}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户开户行：">
                            {{platformAccountObj2.platformFreeyhAddress}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收款账户：">
                            {{platformAccountObj2.platformFreeyhAccount}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费方式：" prop="payType">
                            <el-radio v-model="addFeeForm2.payType" :label="1" >线下</el-radio>
                            <el-radio v-model="addFeeForm2.payType" disabled :label="2" >线上</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="缴费类型" prop="recordType">
                            <el-radio v-model="addFeeForm2.recordType" :label="3" >店铺交易服务费</el-radio>
                            <el-radio v-model="addFeeForm2.recordType" :label="4" >合同履约交易服务费</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="注释：" prop="">
                            缴费金额不能超过总欠费金额
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="缴费金额（元）" prop="payAmount">
                            <el-input
                                placeholder="请输入缴费金额"
                                type="number"
                                clearable
                                v-model="addFeeForm2.payAmount"
                                @change="payAmountChange2"
                            >
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input
                                type="textarea"
                                :auto-resize="false"
                                v-model="addFeeForm2.remarks"
                                placeholder="请输入备注，格式：单位名称+缴费金额" maxlength="1000"
                                show-word-limit
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item  label="缴费证明：" prop="file">
                            <el-upload
                                :class="addFeeForm2.files.length === 1 ? 'hide_box_min' : ''"
                                v-loading="uploadLoading2"
                                class="upload-demo"
                                action="fakeaction"
                                :limit="1"
                                :file-list="fileList2"
                                :before-upload="handleBeforeUpload"
                                :auto-upload="true"
                                :http-request="uploadLicenseBusiness2"
                                list-type="picture-card">
                                <div slot="tip" class="el-upload__tip">只能上传图片文件</div>
                                <i slot="default" class="el-icon-plus"></i>
                                <div slot="file" slot-scope="{file}">
                                    <img
                                        class="el-upload-list__item-thumbnail"
                                        :src="file.url" alt="">
                                    <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview2(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload2(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="formDtlFileRemove2(file)">
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </span>
                                </div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="tabs-title">欠费明细</div>
            <div class="right">
                <div class="e-table">
                    <div class="top">
                        <div class="left">
                            <div class="left-btn">
                                <div style="display: flex;align-items: center;">
                                    <span>
                                &nbsp;
                                总欠费金额（元）：<span style="color: red">{{tableData2[0]==null?0:tableData2[0].paymentAmount}}</span>
                            </span>
                                </div>
                            </div>
                        </div>
                        <div class="search_box">
                            <el-radio v-model="filterData2.orderBy" :label="0">按生成时间排序</el-radio>
                            <el-radio v-model="filterData2.orderBy" :label="1">按修改时间排序</el-radio>
                            <el-input v-model="keywords2" clearable placeholder="输入搜索关键字" style="width: 300px" type="text" @blur="getTableData2">
                                <img slot="suffix" alt="" src="@/assets/search.png" @click="getTableData2"/>
                            </el-input>
                        </div>
                    </div>
                </div>
                <!--表格-->
                <div class="e-table">
                    <el-table
                        ref="tableRef2"
                        v-loading="tableLoading2"
                        :data="tableData2"
                        height="300px"
                        border
                        class="table"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="enterpriseName" label="企业名称" width="260">
                            <template v-slot="scope">
                                {{scope.row.enterpriseName}}
                            </template>
                        </el-table-column>
                        <el-table-column prop="relevanceNu" label="关联单据编号" width="240">
                        </el-table-column>
                        <el-table-column prop="" label="关联单据类型" width="">
                            对账单
                        </el-table-column>
                        <el-table-column prop="dealAmount" label="交易金额（元）" width="150">
                            <template v-slot="scope">
                                {{scope.row.dealAmount}}
                            </template>
                        </el-table-column>
                        <el-table-column prop="useExemptFree" label="占用免费交易金额（元）" width="150">
                        </el-table-column>
                        <el-table-column prop="exceedFree" label="需缴费交易金额（元）" width="150">
                        </el-table-column>
                        <el-table-column prop="feeRatio" label="收取比例（‰）" width="120">
                        </el-table-column>
                        <el-table-column prop="serveFee" label="服务费用（元）" width="140">
                        </el-table-column>
                        <el-table-column prop="payFee" label="已缴费金额（元）" width="120">
                        </el-table-column>
                        <el-table-column prop="finishPayFee" label="是否欠费" width="120">
                            <template v-slot="scope">
                                <el-tag type="success"  v-show="scope.row.finishPayFee==1">否</el-tag>
                                <el-tag type="danger"  v-show="scope.row.finishPayFee==0">是</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="residuePayFee" label="剩余未缴费金额（元）" width="120">
                        </el-table-column>
                        <el-table-column prop="useBalance" label="缴费使用余额金额（元）" width="120">
                        </el-table-column>
                        <el-table-column label="生成时间" prop="gmtCreate" width="160"/>
                        <el-table-column label="修改时间" prop="gmtModified" width="160"/>
                    </el-table>
                </div>
                <Pagination
                    v-show="tableData2 && tableData2.length > 0"
                    :currentPage.sync="paginationInfo2.currentPage"
                    :pageSize.sync="paginationInfo2.pageSize"
                    :total="paginationInfo2.total"
                    @currentChange="getTableData2"
                    @sizeChange="getTableData2"
                />
            </div>
            <span slot="footer">
                <el-button type="primary" class="btn-greenYellow" @click="save2(0)">保存</el-button>
                <el-button type="primary" class="btn-greenYellow"  @click="save2(1)">保存并提交</el-button>
                <el-button @click="showAddFee2 = false">取消</el-button>
            </span>
        </el-dialog>

        <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>

        <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible2">
            <img width="100%" :src="dialogImageUrl2" alt="">
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { debounce, toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import {
    myQueryPayFreeListByEntity,
    createFeeAndDealFree,
    supplierDealFeeRecordDtlListByEntity
} from '@/api/fee/feeApi'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete, getPlatformFreeAccountAndAddress, getIsYearServe } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { mapState } from 'vuex'

export default {
    components: {
        Pagination
    },
    watch: {
        'addFeeForm.recordType': {
            handler (val) {
                if(val == 1 || val == 2) {
                    this.addFeeForm.payAmount = this.addFeeForm.paymentDuration * this.freeAmount
                }
                if(val == 3 || val == 4) {
                    this.addFeeForm.payAmount = 0
                }
            }
        },
        'addFeeForm2.recordType': {
            handler (val) {
                this.getTableData2()
                this.addFeeForm2.payAmount = 0
                if(val == 3) {
                    this.addFeeForm2.remarks = this.thisEnName2 + ' 店铺交易服务费'
                }
                if(val == 4) {
                    this.addFeeForm2.remarks = this.thisEnName2 + ' 合同履约交易服务费'
                }
            }
        },
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        },
        'filterData2.orderBy': {
            handler () {
                this.getTableData2()
            }
        }
    },
    computed: {
        ...mapState([ 'userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    data () {
        return {
            thisEnName: null,
            addFeeTitle: null,
            thisEnName2: null,
            dialogImageUrl: '',
            dialogImageUrl2: '',
            dialogVisible: false,
            dialogVisible2: false,
            freeAmount: 2000,
            showAddFee: false,
            showAddFee2: false,
            platformAccountObj: {},
            platformAccountObj2: {},
            isYearServe: {},
            showAddFeeLoading: false,
            showAddFeeLoading2: false,
            addFeeForm: {
                state: 0,
                payType: 1,
                recordType: 1,
                paymentDurationType: 4,
                paymentDuration: 0,
                files: [],
                payAmount: 0,
                remarks: null,
            },
            uploadLoading2: false,
            addFeeForm2: {
                state: 0,
                payType: 1,
                recordType: 3,
                files: [],
                payAmount: 0,
                remarks: null,
            },
            showAddFeeRoles: {
                paymentDuration: [
                    { required: true, validator: this.validateAddress, trigger: 'blur' },
                ],
                payAmount: [
                    { required: true, validator: this.validateAddress, trigger: 'blur' },
                ],
                file: [
                    { required: true, message: '请输入证明', trigger: 'blur' },
                ],
            },
            showAddFeeRoles2: {
                payAmount: [
                    { required: true, validator: this.validateAddress, trigger: 'blur' },
                ],
                file: [
                    { required: true, message: '请输入证明', trigger: 'blur' },
                ],
            },
            tableSelectRow: [], // 多选框选择的数据
            tableLoading: false, // 加载
            tableLoading2: false, // 加载
            keywords: null, // 关键字
            keywords2: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            uploadImgSize: 10, // 上传文件大小
            tableData: [], // 表格数据
            tableData2: [], // 表格数据
            filterData: { // 高级搜索
                stateCheckAll: false, // 选择全局
                state: null,
                states: [],
                paymentRecordUn: null,
                recordType: null,
                stateSelect: [
                    { value: 0, label: '草稿' },
                    { value: 1, label: '待审核' },
                    { value: 2, label: '审核通过' },
                    { value: 3, label: '审核未通过' },
                ],
                recordTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '店铺年度服务费' },
                    { value: 2, label: '电子招标年度服务费' },
                    { value: 3, label: '店铺交易服务费' },
                    { value: 4, label: '合同履约交易服务费' },
                ],
                orderBy: 1,
                gmtCreate: [],
                gmtModified: [],
                auditOpenTime: [],
            },
            filterData2: { // 高级搜索
                orderBy: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            uploadLoading: false,
            fileList: [],
            fileList2: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        if(this.$route.query.show) {
            this.addFree(1)
        }
        if(this.$route.query.isbid) {
            this.addFree(2)
        }
        this.getIsYearServeM()
        this.getTableData()
    },
    created () {
    },
    methods: {
        addFree (num) {
            this.tableLoading = true
            getPlatformFreeAccountAndAddress().then(res => {
                this.platformAccountObj = res
                this.thisEnName = res.enterpriseName
                this.addFeeForm.recordType = num
                if(num == 1) {
                    this.addFeeTitle = '店铺年费续费申请'
                    this.addFeeForm.remarks = this.thisEnName + ' 店铺年度服务费'
                }
                if(num == 2) {
                    this.addFeeTitle = '招标服务年费续费申请'
                    this.addFeeForm.remarks = this.thisEnName + ' 电子招标年度服务费'
                }
                this.showAddFee = true
            }).finally(() =>{
                this.tableLoading = false
            })
        },
        addDealFree () {
            this.tableLoading = true
            getPlatformFreeAccountAndAddress().then(res => {
                this.platformAccountObj2 = res
                this.thisEnName2 = res.enterpriseName
                this.addFeeForm2.remarks = this.thisEnName2 + ' 店铺交易服务费'
                this.showAddFee2 = true
                this.getTableData2()
            }).finally(() =>{
                this.tableLoading = false
            })
        },
        getIsYearServeM () {
            this.tableLoading = true
            getIsYearServe().then(res => {
                this.isYearServe = res
            }).finally(() =>{
                this.tableLoading = false
            })
        },
        //时间验证
        validateAddress (rule, value, callback) {
            callback()
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handlePictureCardPreview2 (file) {
            this.dialogImageUrl2 = file.url
            this.dialogVisible2 = true
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload (file) {
            this.uploadLoading = true
            let image = this.addFeeForm.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload2 (file) {
            this.uploadLoading2 = true
            let image = this.addFeeForm.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading2 = false
            })
        },
        save (num) {
            if (this.addFeeForm.files.length == 0) {
                return this.$message.error('请上传缴费证明！')
            }
            if( this.addFeeForm.recordType == 1 || this.addFeeForm.recordType == 2) {
                if(this.addFeeForm.paymentDuration <= 0) {
                    return this.$message.error('缴费时长需大于0！')
                }
            }
            if(this.addFeeForm.payAmount <= 0) {
                return this.$message.error('缴费金额需大于0！')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.addFeeForm.submitAud = num
                this.showAddFeeLoading = true
                createFeeAndDealFree(this.addFeeForm).then(res => {
                    if (res.code == null) {
                        this.$message.success('操作成功')
                        let recordType = this.addFeeForm.recordType
                        this.addFeeForm = {
                            state: 0,
                            payType: 1,
                            recordType: 1,
                            paymentDurationType: 4,
                            paymentDuration: 0,
                            files: [],
                            payAmount: 0,
                            remarks: null
                        }
                        this.fileList = []
                        this.showAddFee = false
                        if(recordType == 1 || recordType == 2) {
                            this.$router.push({
                                path: '/supplierSys/fee/payRecordManageDtl',
                                name: 'supplierSysFeePayRecordManageDtl',
                                query: {
                                    sn: res
                                }
                            })
                        }else {
                            this.$router.push({
                                path: '/supplierSys/fee/payRecordManage2Dtl',
                                name: 'supplierSysFeeDealPayRecord2Dtl',
                                query: {
                                    sn: res
                                }
                            })
                        }
                    }
                }).finally(() => {
                    this.showAddFeeLoading = false
                })
            })
        },
        save2 (num) {
            if (this.addFeeForm2.files.length == 0) {
                return this.$message.error('请上传缴费证明！')
            }
            if(this.addFeeForm2.payAmount <= 0) {
                return this.$message.error('缴费金额需大于0！')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.addFeeForm2.submitAud = num
                this.showAddFeeLoading2 = true
                createFeeAndDealFree(this.addFeeForm2).then(res => {
                    if (res.code == null) {
                        this.$message.success('操作成功')
                        this.addFeeForm2 = {
                            state: 0,
                            payType: 1,
                            recordType: 3,
                            files: [],
                            payAmount: 0,
                            remarks: null,
                        }
                        this.fileList2 = []
                        this.showAddFee2 = false
                        this.$router.push({
                            path: '/supplierSys/fee/payRecordManage2Dtl',
                            name: 'supplierSysFeeDealPayRecord2Dtl',
                            query: {
                                sn: res
                            }
                        })
                    }
                }).finally(() => {
                    this.showAddFeeLoading2 = false
                })
            })
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.addFeeForm.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.addFeeForm.files = []
                this.fileList = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove2 (file, fileList) {
            this.uploadLoading2 = true
            createFileRecordDelete({ recordId: this.addFeeForm2.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.addFeeForm2.files = []
                this.fileList2 = []
            }).finally(() =>{
                this.uploadLoading2 = false
            })
        },
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.addFeeForm.files = []
                    this.fileList = []
                }else {
                    let resO = res[0]
                    this.addFeeForm.files.push({
                        name: resO.objectName,
                        relevanceType: 1,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        uploadLicenseBusiness2 (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading2 = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.addFeeForm.files = []
                    this.fileList = []
                }else {
                    let resO = res[0]
                    this.addFeeForm2.files.push({
                        name: resO.objectName,
                        relevanceType: 2,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading2 = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        payAmountChange () {
            if(this.addFeeForm.payAmount <= 0 || this.addFeeForm.payAmount >= 9999999999) {
                this.addFeeForm.payAmount = this.fixed2(1)
            }else {
                this.addFeeForm.payAmount = this.fixed2(this.addFeeForm.payAmount)
            }
        },
        payAmountChange2 () {
            if(this.addFeeForm2.payAmount <= 0 || this.addFeeForm2.payAmount >= 9999999999) {
                this.addFeeForm2.payAmount = this.fixed2(0)
            }else {
                if(this.tableData2.length == 0 || this.tableData2[0].paymentAmount == 0) {
                    this.addFeeForm2.payAmount = this.fixed2(0)
                    return
                }
                var paymentAmount = this.tableData2[0].paymentAmount
                if(paymentAmount < this.addFeeForm2.payAmount || this.addFeeForm2.payAmount == paymentAmount) {
                    this.addFeeForm2.payAmount = this.fixed2(paymentAmount)
                }
            }
        },
        checkInputQty () {
            const regex = /^-?\d+$/
            if (!regex.test(this.addFeeForm.paymentDuration)) {
                this.$message.error('请输入有效的数字')
                this.addFeeForm.payAmount = this.freeAmount
                return this.addFeeForm.paymentDuration = 1
            }
            if (this.addFeeForm.paymentDuration < 0 || this.addFeeForm.paymentDuration > 999) {
                this.$message.error('超过限制！')
                this.addFeeForm.payAmount = this.freeAmount
                return this.addFeeForm.paymentDuration = 1
            }
            this.addFeeForm.payAmount = this.addFeeForm.paymentDuration * this.freeAmount
        },
        // 多选框
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        // 行点击
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        // 跳转详情
        handleView (row) {
            if(row.paymentDurationType != null) {
                this.$router.push({
                    path: '/supplierSys/fee/payRecordManageDtl',
                    name: 'supplierSysFeePayRecordManageDtl',
                    query: {
                        sn: row.paymentRecordUn
                    }
                })
            }else {
                this.$router.push({
                    path: '/supplierSys/fee/payRecordManage2Dtl',
                    name: 'supplierSysFeeDealPayRecord2Dtl',
                    query: {
                        sn: row.paymentRecordUn
                    }
                })
            }
        },
        //重置数据
        resetSearchConditions () {
            this.filterData.state = null
            this.filterData.recordType = null
            this.filterData.paymentRecordUn = null
            this.filterData.stateCheckAll = false
            this.filterData.states = []
            this.filterData.gmtCreate = []
            this.filterData.gmtModified = []
            this.filterData.auditOpenTime = []
        },
        // 状态全选
        stateAllSelect (value) {
            if (value) {
                this.filterData.states = this.filterData.stateSelect.map(t => {
                    return t.value
                })
            } else {
                this.filterData.states = []
            }
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateSelect.length
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.filterData.recordType != null) {
                params.recordType = this.filterData.recordType
            }
            if(this.filterData.states != null && this.filterData.states.length > 0) {
                params.states = this.filterData.states
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.paymentRecordUn != null) {
                params.paymentRecordUn = this.filterData.paymentRecordUn
            }
            if (this.filterData.gmtCreate != null) {
                params.startGmtCreate = this.filterData.gmtCreate[0]
                params.endGmtCreate = this.filterData.gmtCreate[1]
            }
            if (this.filterData.gmtModified != null) {
                params.startGmtModified = this.filterData.gmtModified[0]
                params.endGmtModified = this.filterData.gmtModified[1]
            }
            if (this.filterData.auditOpenTime != null) {
                params.startAuditTime = this.filterData.auditOpenTime[0]
                params.endAuditTime = this.filterData.auditOpenTime[1]
            }
            this.tableLoading = true
            myQueryPayFreeListByEntity(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
            }).finally(() => {
                this.tableLoading = false
            })
        },
        getTableData2 () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                freeQueryType: this.addFeeForm2.recordType,
                isNotAbandon: 1,
                isArrearage: 1
            }
            if (this.keywords2 != null && this.keywords2 != '') {
                params.keywords = this.keywords2
            }
            if (this.filterData2.orderBy != null) {
                params.orderBy = this.filterData2.orderBy
            }
            this.tableLoading2 = true
            supplierDealFeeRecordDtlListByEntity(params).then(res => {
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
                this.tableData2 = res.list
            }).finally(() => {
                this.tableLoading2 = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        handleChangeSort (value) {
            this.filterData.orderBy = value
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
    appearance: textfield !important;
    -moz-appearance: textfield !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
/deep/ .addDia {
    .el-dialog__body {
        height: 600px;
    }
}

/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>
