<template>
    <main>
        <div class="list-title dfa">认证中心</div>
        <div class="content">
            <div class="item df" v-if="showAuthRow">
                <div class="intro">
                    <div class="title">个人认证</div>
                    <div class="subtitle textOverflow1">
                        完成个人认证，用于提高账户安全性和信任级别，认证后方便成为个人店铺
                    </div>
                </div>
                <div style="width: 300px" class="btn btns" v-if="individual">
                    <span @click="becomeEnterprise('business')">成为个体户</span> | <span @click="becomeEnterprise('enterprise')">成为企业</span> | <span @click="handleView">查看</span> | <span @click="handleEdit('individual')">修改</span>
                </div>
            </div>
            <div class="item df" v-else>
              <div style="width: 100%;">
                <div style="display: flex;justify-content: space-between;width: 100%;">
                  <div class="intro">
                    <div class="title">企业/个体户认证</div>
                    <div class="subtitle textOverflow1">
                      完成企业/个体户认证后，可升级为企业/个体户店铺
                    </div>
                  </div>
                  <div class="btn btns" v-if="enterprise">
                    <span @click="handleView">查看</span> | <span @click="handleEdit()">修改</span>
                  </div>
                </div>
                <div v-if="showPrompt" style="margin-top: 2%">
                  <h3>如下信息已过期，请及时更新：</h3>
                  <div style="margin-top: 1.2%;color: red;">{{ expireInfo }}</div>
                </div>
                <div v-if="entInfo.fileModifyTime != null" style="margin-top: 4%">
                  <h3>资料修改审核</h3>
                  <el-table
                    ref="msgTable"
                    :data="[entInfo]"
                    style="min-height: 472px;margin-top: 1%"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                  >
                    <el-table-column prop="" label="操作" width="90">
                      <template slot-scope="scope">
                        <div style="color: #216EC6;cursor: pointer;">
                          <span @click="handleView(scope.row)">查看</span> | <span @click="handleEdit(scope.row)">修改</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="fileModifyTime" label="提交时间" width="" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="isFileModify" label="审核状态" width="" show-overflow-tooltip>
                      <template slot-scope="scope">
                        {{scope.row.isFileModify === 1 ? '待审核' : (scope.row.isFileModify === 0 && scope.row.isNoSupplierAudit === 0) ? '审核通过' : '审核未通过'}}
                      </template>
                    </el-table-column>
                    <el-table-column prop="auditFailReason" label="未通过原因" width="" show-overflow-tooltip>
                      <template slot-scope="scope">
                        {{scope.row.auditFailReason ? scope.row.auditFailReason : '-'}}
                      </template>
                    </el-table-column>
                    <el-table-column prop="gmtModified" label="审核时间" width="" show-overflow-tooltip>
                        <template slot-scope="scope">
                            {{scope.row.isFileModify != 1 ? scope.row.gmtModified : '-'}}
                        </template>
                    </el-table-column>
                  </el-table>
<!--                  <span @click="handleView">查看</span> | <span @click="handleEdit()">修改</span>
                  <span>提交时间：{{entInfo.fileModifyTime}}</span>
                  <span>审核状态：</span>
                  <span v-if="entInfo.isFileModify == 1">未审核</span>
                  <span v-else-if="entInfo.isFileModify == 0 && entInfo.isNoSupplierAudit == 0">审核通过</span>
                  <span v-else-if="entInfo.isFileModify == 0 && entInfo.isNoSupplierAudit == 1">审核未通过,原因：{{entInfo.auditFailReason}}</span>
                  <span>审核时间：{{entInfo.gmtModified}}</span>-->
                </div>
              </div>
            </div>
        </div>
    </main>
</template>
<script>
import { mapState } from 'vuex'
import { getEnterpriseAuthInfo } from '@/api/frontStage/verification'

export default {
    data () {
        return {
            showAuthRow: false,
            roleDetailStr: null,
            individual: true, // 个人认证状态
            enterprise: true, // 企业/个体户认证状态
            showPrompt: false,
            expireInfo: '',
            entInfo: {},
        }
    },
    computed: {
        ...mapState(['userInfo'])
    },
    created () {
        let enterpriseType = this.userInfo.enterpriseType
        // 个体户
        if(enterpriseType === 0) {
            this.roleDetailStr = 'business'
        }
        // 企业
        if(enterpriseType == 1) {
            this.roleDetailStr = 'enterprise'
        }
        // 个人
        if(enterpriseType == 2) {
            this.showAuthRow = true
            this.roleDetailStr = 'individual'
        }
        if(enterpriseType < 2) {
            this.getEnterpriseAuthInfo()
        }
    },
    mounted () {},
    methods: {
        getEnterpriseAuthInfo () {
            getEnterpriseAuthInfo({}).then(res => {
                console.log(res)
                this.entInfo = res
                let info = []
                if (this.isExpire(this.entInfo.licenseTerm)) {
                    info.push('营业执照')
                }
                if (this.isExpire(this.entInfo.tpcEndTime)) {
                    info.push('最近一期完税证明')
                }
                if(this.roleDetailStr === 'enterprise') {
                    if (this.isExpire(this.entInfo.trcEndTime)) {
                        info.push('税务评级证明')
                    }
                    if (this.isExpire(this.entInfo.ccrEndTime)) {
                        info.push('信用中国报告')
                    }
                }
                if (this.isExpire(this.entInfo.zxgkEndTime)) {
                    info.push('中国执行信息公开网查询情况')
                }
                if (this.isExpire(this.entInfo.qcEndTime)) {
                    info.push('资质证书')
                }
                if (this.isExpire(this.entInfo.otherEndTime)) {
                    info.push('其他证书')
                }
                if (this.isExpire(this.entInfo.lpEndTime)) {
                    info.push('法定代表人身份证')
                }
                if (info.length > 0) {
                    this.showPrompt = true
                    this.expireInfo = String(info)
                }
            })
        },
        isExpire (time) {
            if (time == null || time == undefined) {
                return false
            }
            if (Date.parse(time) < Date.now()) {
                return true
            }
            return false
        },
        becomeEnterprise (str) {
            if(str === 'business') {
                this.$router.push('/user/verification/becomeBusiness')
            }else {
                this.$router.push('/user/verification/becomeEnterprise')
            }
        },
        // 去认证
        // handleVerify (role) {
        //     if(role === 'individual') {
        //         return this.$router.push('/user/verification/individual')
        //     }
        //     if(role === 'enterprise') {
        //         return this.$router.push('/user/verification/enterprise')
        //     }
        // },
        // 查看认证
        handleView () {
            if(this.roleDetailStr === 'enterprise') {
                this.$router.push('/user/verification/enterpriseDetail')
            }else if(this.roleDetailStr === 'business') {
                this.$router.push('/user/verification/businessDetail')
            }else{
                this.$router.push('/user/verification/detail?type=individual')
            }
        },
        // 修改认证
        handleEdit () {
            if(this.roleDetailStr === 'enterprise') {
                return this.$router.push('/user/reverify/enterprise')
            }
            if(this.roleDetailStr === 'individual') {
                return this.$router.push('/user/reverify/individual')
            }
            if(this.roleDetailStr === 'business') {
                return this.$router.push('/user/reverify/business')
            }
        },
    },
}
</script>
<style scoped lang="scss">
main{
    height: 100%;
    border: 1px solid rgba(230, 230, 230, 1);
}
.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
.content {
    height: 530px;
    padding: 0 20px;
    .item:not(:last-of-type) {border-bottom: 1px dashed rgba(204,204,204,1);}
    .item {
        justify-content: space-between;
        height: 116px;
        padding: 30px 10px 0;
        .intro {
            width: 560px;
            .title {
                margin-bottom: 13px;
                font-size: 16px;
                color: #333;
                font-weight: bold;
            }
            .subtitle {
                width: 100%;
                font-size: 14px;
                color: #666;
            }
        }
        .status {
            width: 86px;
            height: 16px;
            margin-top: 28px;
            margin-right: 284px;
            font-size: 16px;
            justify-content: center;
            color: #999;
            &.success {color: #1AC47A;}
            img {
                width: 16px;
                height: 16px;
                margin-right: 6px;
            }
        }
        .btn {
            width: 100px;
            height: 40px;
            margin-top: 16px;
            font-size: 16px;
            line-height: 40px;
            text-align: center;
            color: #fff;
            background-color: #216EC6;
            &.btns {
                color: #E6E6E6;
                background-color: transparent;
                span {color: #216EC6;cursor: pointer;}
            }
        }
    }
}
</style>