<template>
    <el-pagination
        id="productPager"
        layout="sizes, prev, next, jumper"
        prev-text="上一页"
        next-text="下一页"
        v-bind="$props"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
    />
</template>

<script>
export default {
    name: 'simplePagination',
    props: {
        total: {
            type: Number,
            required: true,
        },
        currentPage: {
            type: Number,
            required: true,
        },
        pageSize: {
            type: Number,
            required: true
        },
        pageSizes: {
            type: Array,
            required: false,
            default: () => [],
        }
    },
    data () {
        return {
        }
    },
    methods: {
        handlePageChange (page) {
            this.$emit('currentChange', page)
        },
        handleSizeChange (size) {
            this.$emit('sizeChange', size)
        }
    }
}
</script>

<style scoped lang="scss">
</style>