<template>
    <div style="padding: 20px 20px 0">
      <div class="left">
        <el-tabs  v-model="active" type="card" @tab-click="handleClick">
          <el-tab-pane label="全部" name="first"></el-tab-pane>
          <el-tab-pane label="自营店" name="second"></el-tab-pane>
          <el-tab-pane label="非自营店" name="third"></el-tab-pane>
        </el-tabs>
      </div>
        <div style="margin-bottom: 10px; margin-top: 10px" class="dfa"><span class="info"></span>历史统计</div>
        <el-collapse v-model="activeName">
            <div class="digits dfa">
                <div class="digitBox dfa" v-for="(item,index) in staticsDataList0" :key="index">
                    <el-tooltip class="item" effect="dark" :content="item.num" placement="top">
                        <span class="digitCount mb10" v-html="setUnitMeasur(item)"></span>
                    </el-tooltip>
                    <span>{{ item.name }}</span>
                </div>
            </div>
            <el-collapse-item title="展开" name="1">
                <div class="digits dfa">
                    <div class="digitBox dfa" v-for="(item,index) in staticsDataList11" :key="index">
                        <el-tooltip class="item" effect="dark" :content="item.num" placement="top">
                            <span class="digitCount mb10" v-html="setUnitMeasur(item)"></span>
                        </el-tooltip>
                        <span>{{ item.name }}</span>
                    </div>
                </div>
                <div class="digits dfa">
                    <div class="digitBox dfa" v-for="(item,index) in staticsDataList12" :key="index">
                        <el-tooltip class="item" effect="dark" :content="item.num" placement="top">
                            <span class="digitCount mb10" v-html="setUnitMeasur(item)"></span>
                        </el-tooltip>
                        <span>{{ item.name }}</span>
                    </div>
                </div>
              <div class="digits dfa">
                <div class="digitBox dfa" v-for="(item,index) in staticsDataList13" :key="index">
                  <el-tooltip class="item" effect="dark" :content="item.num" placement="top">
                    <span class="digitCount mb10" v-html="setUnitMeasur(item)"></span>
                  </el-tooltip>
                  <span>{{ item.name }}</span>
                </div>
              </div>
            </el-collapse-item>
        </el-collapse>
        <div class="dfa mt20 mb20" ><span class="info"></span>区间统计</div>
        <div class="dfb timePicker">
            <el-date-picker
                type="daterange"
                @change="dateChange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                v-model="filterData.dateScope"
                :picker-options="pickerOptions"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
            >
            </el-date-picker>
            <i class="el-icon el-icon-refresh pointer" @click="refreshData"></i>
        </div>
        <div class="digits dfa">
            <div class="digitBox dfa" v-for="(item,index) in staticsDataList1" :key="index">
                <el-tooltip class="item" effect="dark" :content="item.num" placement="top">
                    <span class="digitCount mb10" v-html="setUnitMeasur(item)"></span>
                </el-tooltip>
                <span>{{ item.name }}</span>
            </div>
        </div>
        <el-collapse v-model="activeName1">
            <el-collapse-item title="展开" name="1">
        <div class="digits dfa">
            <div class="digitBox dfa" v-for="(item,index) in staticsDataList3" :key="index">
                <el-tooltip class="item" effect="dark" :content="item.num" placement="top">
                    <span class="digitCount mb10" v-html="setUnitMeasur(item)"></span>
                </el-tooltip>
                <span>{{ item.name }}</span>
            </div>
        </div>
        <div class="digits dfa">
            <div class="digitBox dfa" v-for="(item,index) in staticsDataList4" :key="index">
                <el-tooltip class="item" effect="dark" :content="item.num" placement="top">
                    <span class="digitCount mb10" v-html="setUnitMeasur(item)"></span>
                </el-tooltip>
                <span>{{ item.name }}</span>
            </div>
        </div>
              <div class="digits dfa">
                <div class="digitBox dfa" v-for="(item,index) in staticsDataList5" :key="index">
                  <el-tooltip class="item" effect="dark" :content="item.num" placement="top">
                    <span class="digitCount mb10" v-html="setUnitMeasur(item)"></span>
                  </el-tooltip>
                  <span>{{ item.name }}</span>
                </div>
              </div>
            </el-collapse-item>
        </el-collapse>
    </div>

</template>
<script>
import { getStaticsSystem } from '@/api/platform/analysis/system'
import $ from 'jquery'
import { hideLoading, showLoading } from '@/utils/common'

function getTimeScopes (days) {
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * days)
    start.setHours('00', '00', '00')
    end.setHours('23', '59', '59')
    return [start, end]
}

export default {
    data () {
        return {
            active: 'first',
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        let [start, end] = getTimeScopes(30)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        let [start, end] = getTimeScopes(90)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        let [start, end] = getTimeScopes(180)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        let [start, end] = getTimeScopes(360)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        let [start, end] = getTimeScopes(360 * 2)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            //  历史
            staticsDataList0: [],
            staticsDataList11: [],
            staticsDataList12: [],
            staticsDataList13: [],
            // 时间区间
            staticsDataList1: [],
            staticsDataList3: [],
            staticsDataList4: [],
            staticsDataList5: [],
            pageLoading: false,
            filterData: {
                dateScope: [],
                type: 1
            },
            activeName: 0,
            activeName1: 0,
        }
    },
    computed: {
        tableHeight () {
            return 43 * window.innerHeight / 100
        }
    },
    methods: {
        // 切换页签
        handleClick (tab, event) {
            console.log(tab, event)
            console.log(this.active)
            if (this.active == 'first' ) {
                this.filterData.type = 1 // 全部
            } else if (this.active == 'second') {
                this.filterData.type = 2 // 自营店
            } else if (this.active == 'third') {
                this.filterData.type = 3 // 非自营店
            } else {
                this.filterData.type = 1 // 默认全部
            }
            this.getStaticsData()
            this.getStaticsDataNoParams()
        },
        // 处理金额单位
        setUnitMeasur (item) {
            if (item.name.indexOf('金额') > -1) {
                return (item.num / 10000).toFixed(2) + '<span style="font-size: 16px;">万元</span>'
            } else {
                return item.num
            }
        },
        dateChange () {
            this.getStaticsData()
        },

        initMonth () {
            // 时间选择器默认为当前一个月
            const start = new Date()
            const end = new Date() // 获取当前日期
            start.setMonth(end.getMonth() - 1) // 减去一个月
            // start.setUTCHours('00', '00', '00')
            // end.setHours('23', '59', '59')
            // end.setUTCHours('23', '59', '59')
            this.filterData.dateScope = [start, end]
        },
        refreshData () {
            // 图标旋转动画
            // this.getStaticsData()
            $('.el-icon-refresh').animate({ deg: '+=180', }, {
                duration: 500,
                step: function (now) {
                    $(this).css({ transform: 'rotate(' + now + 'deg)' })
                },
                complete: function () {
                    $(this).css({ // 重置元素的旋转角度和动画时间
                        transform: 'rotate(0deg)',
                        transitionDuration: '0s'
                    })
                }
            })
        },
        getStaticsDataNoParams () {
            showLoading()
            // 添加类型
            const params =  {
                type: this.filterData.type
            }
            getStaticsSystem(params).then(res => {
                this.staticsDataList0 = res.slice(0, 2) || []
                this.staticsDataList11 = res.slice(2, 7) || []
                this.staticsDataList12 = res.slice(7, 11) || []
                this.staticsDataList13 = res.slice(11, 17) || []
            }).finally(() => {
                hideLoading()
            })
        },
        getStaticsData () {
            showLoading()

            if (this.filterData.dateScope === '') {
                this.filterData.dateScope = []
            }
            getStaticsSystem(this.filterData).then(res => {
                this.staticsDataList1 = res.slice(0, 2) || []
                this.staticsDataList3 = res.slice(2, 7) || []
                this.staticsDataList4 = res.slice(7, 11) || []
                this.staticsDataList5 = res.slice(11, 17) || []
            }).finally(() => {
                hideLoading()
            })

        }
    },
    created () {
        this.initMonth()
        this.getStaticsDataNoParams()
        this.getStaticsData()

    }
}
</script>

<style scoped lang="scss">
$mainColor: #74a0f9;
.info {
    height: 22px;
    width: 8px;
    border-radius: 40px;
    background-color: #409eff;
    display: block;
    margin-right: 10px;

}

.timePicker {
    margin-bottom: 20px;
    padding-right: 20px;

    i {
        font-size: 20px;

        :active {
            transform: rotate(90deg);
            transition: all linear .3s;
        }
    }
}

.digits {
    padding: 5px 0;
    border-radius: 10px;
    justify-content: space-evenly;
    background-color: #ecf2ff;

    .digitBox {
        padding: 0 10px;
        flex-direction: column;

        span {
            cursor: pointer;
        }

        span:last-child {
            color: #666;
        }

        /deep/ el-collapse-item {
            cursor: pointer;
        }

        /deep/ el-collapse-item:last-child {
            color: #666;
        }
    }

    .digitCount {
        font-size: 40px;
        font-weight: bold;
        font-family: 'DIN Alternate';
        color: $mainColor;
        flex-grow: 1;
    }
}
</style>