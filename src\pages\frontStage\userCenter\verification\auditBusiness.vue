<template>
    <main>
        <div class="list-title df mb20">修改个体户认证</div>
        <div class="content">
            <el-form class="businessForm" :model="businessForm" ref="businessForm" :rules="businessFormRules" label-width="168px" :inline="false">
                <div class="df">
                    <el-form-item class="licenseUploader" label="营业执照图片(推荐：750x420)：" prop="businessLicense">
                        <el-upload class="avatar-uploader" action="fakeaction" :before-upload="handleBeforeUpload" name="img" :auto-upload="true"
                            :show-file-list="false" :on-change="handleUploadChange" :http-request="uploadLicenseBusiness">
                            <img v-if="businessForm.businessLicense" :src="businessForm.businessLicense" class="avatar">
                            <div v-else class="licenseUploader">
                                <img src="@/assets/images/userCenter/upload_yyzz.png" />
                            </div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item class="licenseUploader">
                        <div class="uploadDemo dfa">
                            <img src="@/assets/images/userCenter/yyzz_demo.png" alt="">
                            <div><span>示例图</span><i class="el-icon-zoom-in"></i></div>
                        </div>
                    </el-form-item>
                    <div class="uploadTip">请上传10MB以内的PNG，JPG，GIF，BMP格式图片</div>
                </div>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="企业名称：" prop="enterpriseName">
                            <el-input clearable v-model="businessForm.enterpriseName" placeholder="请填写50字以内的企业名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="1">
                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                            <el-input clearable v-model="businessForm.socialCreditCode" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
              <!--供方类型-纳税人类别-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="供方类型：" prop="supplierType">
                    <div style="line-height: 50px;"><el-radio v-model="businessForm.supplierType" label="1">生产商</el-radio>
                      <el-radio v-model="businessForm.supplierType" label="2">贸易商</el-radio></div>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="纳税人类别：" prop="taxpayerType">
                    <el-select v-model="businessForm.taxpayerType" placeholder="请选择">
                      <el-option
                        v-for="item in taxpayers"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--主营业务\法定代表人-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="主营业务：" prop="mainBusiness">
                    <el-input clearable v-model="businessForm.mainBusiness" placeholder="请填写与营业执照一致的主营业务"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item class="licenseValidTime" label="法定代表人：" prop="legalRepresentative">
                    <el-input clearable v-model="businessForm.legalRepresentative" placeholder="请填写50字以内的法定代表人姓名"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--注册日期\营业执照有效期-->
              <el-row>
                <el-col :span="11">
                  <el-form-item prop="creationTime" label="注册时间：">
                    <el-date-picker
                      v-model="businessForm.creationTime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      align="right"
                      type="date"
                      placeholder="请选择注册时间"
                      style="width: 100%">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item class="licenseValidTime" label="营业执照有效期：" prop="licenseTerm">
                    <el-date-picker
                      v-model="businessForm.licenseTerm"
                      align="right"
                      type="date"
                      :value-format="dateFormat"
                      placeholder="请选择营业执照有效期"
                    />
                    <el-checkbox label="长期" :indeterminate="false" v-model="longTerm"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--注册资本-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="注册资本(万元)：" prop="registeredCapital">
                    <el-input clearable v-model="businessForm.registeredCapital" placeholder="请填写企业注册资本"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--企业注册地址-固定工作地址-->
              <el-row>
                <el-col :span="11">
                  <el-form-item  class="registerAddress" label="企业注册地址：" prop="address">
                    <div>
                      <el-select
                        ref="selectLabel4"
                        class="province"
                        v-model="businessForm.provincesCode"
                        placeholder="省份"
                        @change="(code) => getSubDistrict(code, 1)"
                      >
                        <el-option
                          v-for="item in addressOptions.province"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                      <el-select ref="selectLabel5" class="city" v-model="businessForm.cityCode" value-key="" placeholder="地级市"
                                 @change="(code) => getSubDistrict(code, 2)">
                        <el-option
                          v-for="item in addressOptions.city"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                      <el-select ref="selectLabel6" @visible-change="addressChange"  class="county" v-model="businessForm.countyCode" value-key="" placeholder="区、县">
                        <el-option
                          v-for="item in addressOptions.district"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item  class="registerAddress" label="固定工作地址：" prop="address_gd">
                    <div>
                      <el-select
                        ref="selectLabel4_gd"
                        class="province"
                        v-model="businessForm.provincesGdCode"
                        placeholder="省份"
                        @change="(code) => getSubDistrict(code, 3)"
                      >
                        <el-option
                          v-for="item in addressOptionsGd.province"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                      <el-select ref="selectLabel5_gd" class="city" v-model="businessForm.cityGdCode" value-key="" placeholder="地级市"
                                 @change="(code) => getSubDistrict(code, 4)">
                        <el-option
                          v-for="item in addressOptionsGd.city"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                      <el-select ref="selectLabel6_gd" @visible-change="addressChange_gd"  class="county" v-model="businessForm.countyGdCode" value-key="" placeholder="区、县">
                        <el-option
                          v-for="item in addressOptionsGd.district"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--详细地址-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="企业注册详细地址：" prop="detailedAddress">
                    <el-input v-model="businessForm.detailedAddress" placeholder="请输入企业注册详细地址"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="固定工作详细地址：" prop="workXxdz" placeholder="请输入固定工作详细地址">
                    <el-input v-model="businessForm.workXxdz" />
                  </el-form-item>
                </el-col>
              </el-row>
              <!--资质信息-->
              <div class="separ center"></div>
              <div class="subtitle">资质信息</div>
              <div class="separ center"></div>
              <!--主要业绩-->
              <h3 style="margin-bottom: 20px;margin-left: 2%;">主要业绩</h3>
              <el-button size="small" type="primary" class="addcardtop" @click="handleAddGth()">添加</el-button>
              <!--表格-->
              <div class="custom-table">
                <el-table
                  :data="tableData_GTH"
                  stripe
                  border
                  style="width: 100%">
                  <!-- 操作列 -->
                  <el-table-column label="操作" width="60px" align="center">
                    <template slot-scope="scope" style="text-align: center">
                      <el-button
                        style="width: 30px;margin: 0 auto;padding: 0"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDeleteGth(scope.$index)">
                      </el-button>
                    </template>
                  </el-table-column>
                  <!-- 项目名称列 -->
                  <el-table-column prop="projectName" label="项目名称" width="200px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.projectName" placeholder="请输入内容" :border="false" style="outline: none;width: 178px !important;">{{ scope.row.projectName }}</el-input>
                    </template>
                  </el-table-column>
                  <!-- 供应物资品类 -->
                  <el-table-column prop="supplyCategory" label="供应物资品类" width="200px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.supplyCategory" placeholder="请输入内容" :border="false" style="outline: none;width: 178px !important;">{{ scope.row.supplyCategory }}</el-input>
                    </template>
                  </el-table-column>
                  <!-- 合同金额（万元） -->
                  <el-table-column prop="contractAmount" label="合同金额（万元）" width="120px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.contractAmount" placeholder="请输入内容" :border="false" style="outline: none;width: 98px !important;">{{ scope.row.contractAmount }}</el-input>
                    </template>
                  </el-table-column>
                  <!-- 供货起止时间 -->
                  <el-table-column prop="ghdate" label="供货起止时间" width="400px" align="center">
                    <template slot-scope="scope">
                      <el-date-picker
                        v-model="scope.row.ghdate"
                        type="daterange"
                        :value-format="dateFormat"
                        range-separator="至"
                        start-placeholder="请选择"
                        end-placeholder="请选择"  style="width: 100%;">
                      </el-date-picker>
                    </template>
                  </el-table-column>
                  <!-- 业绩证明人 -->
                  <el-table-column prop="proofPerson" label="业绩证明人" width="120px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.proofPerson"  :border="false" style="outline: none;width: 98px !important;">{{ scope.row.proofPerson }}</el-input>
                    </template>
                  </el-table-column>
                  <!-- 证明人联系电话 -->
                  <el-table-column prop="proofPhone" label="证明人联系电话" width="200px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.proofPhone"  style="outline: none;width: 178px !important;">{{ scope.row.proofPhone }}</el-input>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <!--质量认证-->
              <h3 style="margin-top: 20px;margin-bottom: 20px;margin-left: 2%;">质量认证</h3>
              <div style="margin-left: 2%">
                <el-checkbox-group v-model="businessForm.certificate" style="display: flex">
                  <el-checkbox label="ISO9001质量体系认证" value="1"></el-checkbox>
                  <el-checkbox label="铁路产品CRCC认证" value="2"></el-checkbox>
                  <el-checkbox label="交通产品CCPC认证" value="3"></el-checkbox>
                  <el-checkbox label="CCC认证" value="4"></el-checkbox>
                  <el-checkbox label="其他质量认证" @change="showQuality_GTH()" value="5"></el-checkbox>
                </el-checkbox-group>
                <textarea v-model="businessForm.certificateOther" v-show="OtherQualityCertifications_GTH" placeholder="请填写其他质量认证" style="width: 900px;height: 100px;resize:none;margin-top: -3%"></textarea>
              </div>
              <!--企业基本情况-->
              <h3 style="margin-top: 20px;margin-bottom: 20px;margin-left: 2%">企业基本情况</h3>
              <div style="margin-left: 2%;margin-bottom: 20px;">
                <div style="font-size: 20px">企业概况</div>
                <textarea style="width: 900px;height: 70px;resize:none;" v-model="businessForm.companyProfile"></textarea>
              </div>
              <div style="margin-left: 2%;margin-bottom: 20px;">
                <div style="font-size: 20px">财务情况</div>
                <textarea style="width: 900px;height: 70px;resize:none;" v-model="businessForm.financialSituation"></textarea>
              </div>
              <div style="margin-bottom: 20px;margin-left: 2%;">
                <div style="font-size: 20px">诉讼情况</div>
                <textarea style="width: 900px;height: 70px;resize:none;" v-model="businessForm.litigationSituation"></textarea>
              </div>
              <!--设置对公账户-->
              <div class="separ center"></div>
              <div class="setAccount">
                <div class="subtitle">设置对公账户</div>
                <div class="subtitletip">企业账户信息，填写内容将写入合同并涉及发票，请谨慎填写。</div>
              </div>
              <!--开户银行、银行账户-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="开户银行：" prop="bankName">
                    <el-input clearable v-model="businessForm.bankName"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="银行户名：" prop="accountName">
                    <el-input clearable v-model="businessForm.accountName"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--银行账号-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="银行账号：" prop="bankAccount">
                    <el-input clearable v-model="businessForm.bankAccount"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--开票备注-->
              <el-row>
                <el-col :span="24" >
                  <el-form-item label="开票备注：">
                    <el-input clearable v-model="businessForm.invoiceRemark"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--法定代表人-->
              <div class="separ center"></div>
              <div class="subtitle">法定代表人</div>
              <!--双面身份证上传-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="身份证人像面照（推荐：420x180）：" prop="legalPersonFace">
                    <el-upload
                      class="identityUpload face"
                      action="fakeaction"
                      :http-request="(res) => uploadIdentity(res, 3,2)"
                      :show-file-list="false">
                      <img class="identityUpload" v-if="businessForm.legalPersonFace" :src="businessForm.legalPersonFace"
                           alt="">
                      <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="身份证国徽面照（推荐：420x180）：" prop="legalPersonNational">
                    <el-upload
                      class="identityUpload badge"
                      action="fakeaction"
                      :http-request="(res) => uploadIdentity(res, 4,2)"
                      :show-file-list="false">
                      <img class="identityUpload" v-if="businessForm.legalPersonNational" :src=" businessForm.legalPersonNational"
                           alt="">
                      <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--                          姓名身份证号码-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="姓名：" prop="legalPersonName">
                    <el-input clearable v-model="businessForm.legalPersonName" placeholder="请填写真实姓名"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="身份证号码：" prop="legalPersonNum">
                    <el-input clearable v-model="businessForm.legalPersonNum" placeholder="请填写18位身份证号码"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--                          有效开始日期-结束日期-->
              <el-row>
                <el-col :span="12">
                  <el-form-item label="有效期开始日期：" prop="lpStartTime">
                    <el-date-picker
                      v-model="businessForm.lpStartTime"
                      align="right"
                      type="date"
                      :value-format="dateFormat"
                      style="width: 100%"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12" :offset="1">
                  <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="lpEndTime">
                    <el-date-picker
                      v-model="businessForm.lpEndTime"
                      align="right"
                      type="date"
                      :value-format="dateFormat"
                      placeholder="请选择"
                    />
                    <el-checkbox label="长期" :indeterminate="false" v-model="szlpEndTime"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="separ center"></div>
              <div class="subtitle">设置管理员</div>
              <!--身份证人像面照\身份证国徽面照-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="身份证人像面照（推荐：420x180）：" prop="cardPortraitFace">
                    <el-upload class="identityUpload face" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1,2)"
                               :show-file-list="false">
                      <img class="identityUpload" v-if="businessForm.cardPortraitFace" :src="businessForm.cardPortraitFace"
                           alt="">
                      <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="身份证国徽面照（推荐：420x180）：" prop="cardPortraitNationalEmblem">
                    <el-upload class="identityUpload badge" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2,2)"
                               :show-file-list="false">
                      <img class="identityUpload" v-if="businessForm.cardPortraitNationalEmblem" :src="businessForm.cardPortraitNationalEmblem"
                           alt="">
                      <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--姓名\身份证号码-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="姓名：" prop="adminName">
                    <el-input clearable v-model="businessForm.adminName" placeholder="请填写真实姓名"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="身份证号码：" prop="adminNumber">
                    <el-input clearable v-model="businessForm.adminNumber" placeholder="请填写18位身份证号码"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--有效开始日期-结束日期-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="有效期开始日期：" prop="adminPeriodStart">
                    <el-date-picker
                      v-model="businessForm.adminPeriodStart"
                      align="right"
                      type="date"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :picker-options="pickerOptions"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="有效期结束日期：" prop="adminPeriodEnd">
                    <el-date-picker
                      v-model="businessForm.adminPeriodEnd"
                      align="right"
                      type="date"
                      style="width: 100%"
                      :value-format="dateFormat"
                      :picker-options="pickerAfterOptions"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <!--手机号码\验证码-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="手机号码：" prop="adminPhone">
                    <el-input type="number"  clearable  v-model="businessForm.adminPhone" placeholder="请输入11位手机号码"/>
                  </el-form-item>
                </el-col>
<!--                <el-col :span="11" :offset="1">
                  <el-form-item label="验证码：" prop="verificationCode">
                    <div class="verifyBox dfb">
                      <el-input clearable v-model="businessForm.verificationCode" placeholder="请输入短信验证码"/>
                      <el-button style="border-radius: 0;" @click="openPhoneCodeDialog">{{ verifyText3 }}</el-button>
                    </div>
                  </el-form-item>
                </el-col>-->
              </el-row>
              <!--                          登录密码-->
<!--              <el-row>
                <el-col :span="11">
                  <el-form-item label="登录密码：" prop="adminPassword">
                    <el-input prefix-icon="el-icon-unlock"  clearable :type="pwType"  v-model="businessForm.adminPassword" show-password placeholder="请输入8-20位由数字，字母和特殊字符组成的密码"/>
                  </el-form-item>
                </el-col>
              </el-row>-->
              <!--附件上传-->
              <div class="separ center"></div>
              <div class="subtitle">附件上传</div>
              <div class="separ center"></div>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="承诺书：" prop="powerOfAttorney">
                    <el-upload
                      :before-upload="handleBeforeUpload"
                      :http-request="uploadCns"
                      :on-change="handleChangeCns"
                      class="upload-demo"
                      :on-remove="handleRemoveAttorney"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :file-list="cnsFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                    <div @click="downloadCns">下载承诺书模板</div>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="授权委托书：" prop="propsqwts">
                    <el-upload
                      :http-request="uploadSqwts"
                      class="upload-demo"
                      :on-remove="handleRemovePropsqwts"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeSqwts"
                      :file-list="sqwtsFileList">
                      <el-button type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                    <div @click="downloadsqwts">下载授权委托书模板</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--法定代表人身份证明-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="法定代表人身份证明：" prop="propfaren">
                    <el-upload
                      :http-request="uploadFrSfzm"
                      class="upload-demo"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeFrSfzm"
                      :on-remove="handleRemovePropfaren"
                      :file-list="fddbrsfzmFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                    <div @click="downloadFrSfzm">下载法定代表人身份证明模板</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="separ center"></div>
              <!--最近一期完税证明、税务评级证明-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="最近一期完税证明：" prop="zjyqwszm">
                    <el-upload
                      :http-request="uploadWszm"
                      :before-upload="handleBeforeUpload"
                      :on-remove="handleRemoveZjyqwszm"
                      class="upload-demo"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeWszm"
                      :file-list="zjyqwszmFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="中国执行信息公开网查询情况：" prop="zgzxxxgk">
                    <el-upload
                      :http-request="uploadZxxx"
                      :before-upload="handleBeforeUpload"
                      :on-remove="handleRemoveZgzxxxgk"
                      class="upload-demo"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeZxxx"
                      :file-list="zgzxxxgkwFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 有效期开始日期-结束日期-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="有效期开始日期：" prop="tpcStartTime">
                    <el-date-picker
                      v-model="businessForm.tpcStartTime"
                      align="right"
                      type="date"
                      :value-format="dateFormat"
                      style="width: 100%"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item class="licenseValidTime" label="有效期开始日期：" prop="zxgkStartTime">
                    <el-date-picker
                      v-model="businessForm.zxgkStartTime"
                      align="right"
                      type="date"
                      :value-format="dateFormat"
                      style="width: 100%"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="有效期结束日期：" prop="tpcEndTime">
                    <el-date-picker
                      v-model="businessForm.tpcEndTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="zxgkEndTime">
                    <el-date-picker
                      v-model="businessForm.zxgkEndTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      :value-format="dateFormat"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <!--添加资质证书、添加其他资料-->
              <div class="separ center"></div>
              <div>
                <el-button size="small" type="primary" class="addcardtop" @click="addZzzs">添加资质证书</el-button>
                <el-button size="small" type="primary" class="addcardtop" @click="addQtzl">添加其他资料</el-button>
              </div>
              <div style="display: flex;margin-bottom: 30px">
                <!--                          上传添加资质证书-->
                <div class="addcard" v-if="isZzzs">
                  <div class="cardtop">
                    <div>资质证书</div>
                    <el-button class="addcarddel" size="small" type="primary" @click="deleteZzzs">删除</el-button>
                  </div>
                  <el-row><el-upload
                    :http-request="uploadZzzs"
                    :before-upload="handleBeforeUpload"
                    class="upload-demo"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :on-change="handleChange"
                    :file-list="zzzsFileList">
                    <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                  </el-upload></el-row>
                  <el-row>
                    <el-form-item class="licenseValidTime" label="有效期开始日期：">
                      <el-date-picker
                        v-model="businessForm.qcStartTime"
                        align="right"
                        type="date"
                        style="width: 61%"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptions"
                        placeholder="请选择"
                      />
                    </el-form-item>
                  </el-row>
                  <el-row>
                    <el-form-item class="licenseValidTime" label="有效期结束日期：">
                      <el-date-picker
                        v-model="businessForm.qcEndTime"
                        align="right"
                        type="date"
                        :value-format="dateFormat"
                        :picker-options="pickerAfterOptions"
                        placeholder="请选择"
                      />
                      <el-checkbox style="margin-right: 10px" label="永久有效" :indeterminate="false" v-model="szQcEndTime"/>
                    </el-form-item>
                  </el-row>
                </div>
                <!--                          上传添加资质证书-->
                <div class="addcard" v-if="isQtzs">
                  <div class="cardtop">
                    <div>其他资料</div>
                    <el-button class="addcarddel" size="small" type="primary" @click="deleteQtzs">删除</el-button>
                  </div>
                  <el-row><el-upload
                    :http-request="uploadQtzs"
                    :before-upload="handleBeforeUpload"
                    class="upload-demo"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :on-change="handleChange"
                    :file-list="qtzsFileList">
                    <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                  </el-upload></el-row>
                  <el-row>
                    <el-form-item class="licenseValidTime" label="有效期开始日期：">
                      <el-date-picker
                        v-model="businessForm.otherStartTime"
                        align="right"
                        type="date"
                        style="width: 61%"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptions"
                        placeholder="请选择"
                      />
                    </el-form-item>
                  </el-row>
                  <el-row>
                    <el-form-item class="licenseValidTime" label="有效期结束日期：">
                      <el-date-picker
                        v-model="businessForm.otherEndTime"
                        align="right"
                        type="date"
                        :value-format="dateFormat"
                        :picker-options="pickerAfterOptions"
                        placeholder="请选择"
                      />
                      <el-checkbox style="margin-right: 10px" label="永久有效" :indeterminate="false" v-model="szOtherEndTime"/>
                    </el-form-item>
                  </el-row>
                </div>
              </div>
              <div class="separ center"></div>
<!--                <div class="separ center"></div>
                <div class="subtitle">附件资料</div>
                <el-row>
                    <el-col :span="24" style="height: unset;" v-loading="fileLoading">
                        <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                            <el-upload
                                ref="multi-upload"
                                class="multi-file-uploader"
                                action="fakeaction"
                                accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                :on-remove="handleRemove"
                                multiple
                                :limit="10"
                                :before-upload="beforeOneOfFilesUpload"
                                :http-request="uploadOneOfFiles"
                                :on-exceed="handleExceed"
                                :file-list="businessForm.files"
                            >
                                <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip"><span>请上传</span>
                                    <div class="file dfa pointer" v-for="file in fileList" :key="file.url">
                                        <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                    </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
&lt;!&ndash;                        <el-form-item prop="agreeTerm">&ndash;&gt;
&lt;!&ndash;                            <el-checkbox v-model="agreeTerm" :indeterminate="false">&ndash;&gt;
&lt;!&ndash;                                您确认阅读并接受<span @click="showTerm = true">《物资采购平台企业认证协议》</span>&ndash;&gt;
&lt;!&ndash;                            </el-checkbox>&ndash;&gt;
&lt;!&ndash;                        </el-form-item>&ndash;&gt;
                    </el-col>
                </el-row>-->
            </el-form>
            <div class="btns center dfb">
                <button @click="$router.go(-1)">返回</button>
                <button @click="onSubmit">提交</button>
            </div>
        </div>
    </main>
</template>
<script>
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { getEnterpriseAuthInfo, updateEnterprise } from '@/api/frontStage/verification'
import { findByProgramaKey } from '@/api/w/richContent'
import { getEnterPriseFileList } from '@/api/base/file'
import { getCascaderOptions } from '@/api/platform/common/components'
import { createWFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'

export default {
    data () {
        return {
            businessFormFileList: [],
            fileList: [],
            cnsFileList: [],
            sqwtsFileList: [],
            fddbrsfzmFileList: [],
            zjyqwszmFileList: [],
            zgzxxxgkwFileList: [],
            zzzsFileList: [],
            qtzsFileList: [],
            uploadImgSize: 4,
            dateFormat: 'yyyy-MM-dd HH:mm:ss',
            showTerm: false,
            longTerm: false,
            szQcEndTime: false,
            szOtherEndTime: false,
            //个体户注册表格相关
            tableData_GTH: [
                //{ projectName: '', supplyCategory: '', contractAmount: '', ghdate: [], proofPerson: '', proofPhone: '', }
            ],
            // 地址选择器选项
            addressOptions: {
                province: [],
                city: [],
                district: []
            },
            addressOptionsGd: {
                province: [],
                city: [],
                district: []
            },
            OtherQualityCertifications_GTH: false,
            szlpEndTime: false,
            verifyText3: '获取短信验证码',
            phoneCodeDialogVisible: false,
            isZzzs: false,
            isQtzs: false,
            pwType: 'password',
            businessForm: {
                businessLicense: '',
                enterpriseName: '',
                socialCreditCode: '',
                enterpriseType: '',
                operator: '',
                creationTime: '',
                provinces: '',
                city: '',
                county: '',
                taxRate: '',
                detailedAddress: '',
                placeOfBusiness: '',
                mainBusiness: '',
                provincesCode: '',
                cityCode: '',
                countyCode: '',
                provincesGdCode: '',
                cityGdCode: '',
                countyGdCode: '',
                cardPortraitFace: '',
                cardPortraitNationalEmblem: '',
                adminPhone: '',
                verificationCode: '',
                adminName: '',
                adminPassword: '',
                adminNumber: '',
                agreeTerm: false,
                files: [],
                mallType: 0,
                verifyImg: '',
                verifyId: '',
                //个体户新增
                //纳税人、供应商类型
                taxpayerType: '',
                supplierType: '1',
                //法定代表人
                legalRepresentative: '',
                //营业执照有效期
                licenseTerm: '',
                //注册资本
                registeredCapital: '',
                //详细地址
                workXxdz: '',
                //质量认证、其他质量认证
                certificateOther: '',
                certificate: [],
                //企业基本情况文本框
                litigationSituation: '',
                financialSituation: '',
                companyProfile: '',
                //开户银行、银行户名
                accountName: '',
                bankName: '',
                //银行账号
                bankAccount: '',
                //开票备注
                invoiceRemark: '',
                legalPersonFace: '',
                legalPersonFaceId: '',
                legalPersonNational: '',
                legalPersonNationalId: '',
                //法定代表人姓名身份证号
                legalPersonNum: '',
                legalPersonName: '',
                provincesGd: '',
                cityGd: '',
                countyGd: '',
            },
            agreeTerm: false,
            //纳税人类别
            taxpayers: [
                {
                    value: '1',
                    label: '一般纳税人'
                },
                {
                    value: '2',
                    label: '小规模纳税人'
                }
            ],
            //个体户表单验证
            businessFormRules: {
            // files:[
            //     { required: true, message: '请上传附件资料', trigger: 'blur' }
            // ],
                businessLicense: { required: true, message: '请上传营业执照！', trigger: 'blur' },
                enterpriseName: [
                    { required: true, message: '请填写企业名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '请填写50字以内的企业名称', trigger: 'blur' }
                ],
                socialCreditCode: [
                    { required: true, message: '请输入统一信用代码', trigger: 'blur' },
                    { min: 18, max: 18, message: '请输入18位统一信用代码', trigger: 'blur' }
                ],
                supplierType: [
                    { required: true, message: '请选择供方类型', trigger: 'blur' }
                ],
                taxpayerType: [
                    { required: true, message: '请选择纳税人类别', trigger: 'blur' }
                ],
                operator: [
                    { required: true, message: '请输入经营者姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                creationTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                lpStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                adminPeriodStart: { required: true, validator: this.validateDate, trigger: 'blur' },
                adminPeriodEnd: { required: true, validator: this.validateDate, trigger: 'blur' },
                tpcStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                zxgkStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                tpcEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                zxgkEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                placeOfBusiness: [
                    { required: true, message: '请填写与营业执照一致的经营场所', trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' }
                ],
                taxRate: [
                    { required: true, validator: this.validateBusinessTaxRate, trigger: 'blur' },
                ],
                mainBusiness: [
                    { required: true, message: '请填写与营业执照相同的主营业务', trigger: 'blur' },
                    { min: 1, max: 1000, message: '超过限制', trigger: 'blur' }
                ],
                legalRepresentative: [
                    { required: true, message: '请填写企业法定代表人', trigger: 'blur' },
                    { min: 2, max: 10, message: '请填写企业法定代表人', trigger: 'blur' }
                ],
                registeredCapital: [
                    { required: true, message: '请输入注册资本',  trigger: 'blur' }
                ],
                address: { required: true, validator: this.validateBusinessAddress, trigger: 'change' },
                address_gd: { required: true, validator: this.validateBusinessAddress_gd, trigger: 'change' },
                detailedAddress: [
                    { required: true, message: '请填写企业注册详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                workXxdz: [
                    { required: true, message: '请填写固定工作详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                bankName: [
                    { required: true, message: '请输入开户银行',  trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' }
                ],
                accountName: [
                    { required: true, message: '请输入银行户名',  trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' }
                ],
                bankAccount: [
                    { required: true, message: '请输入银行账号',  trigger: 'blur' },
                    { min: 1, max: 19, message: '超过限制', trigger: 'blur' }
                ],
                legalPersonFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                legalPersonNational: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                cardPortraitFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                cardPortraitNationalEmblem: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                legalPersonName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                legalPersonNum: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                adminName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                powerOfAttorney: { required: true, validator: this.validatePowerOfAttorney, trigger: 'change' },
                propsqwts: { required: true, validator: this.validatePropsqwts, trigger: 'change' },
                zjyqwszm: { required: true, validator: this.validateZjyqwszm, trigger: 'change' },
                zgzxxxgk: { required: true, validator: this.validateZgzxxxgk, trigger: 'change' },
                adminNumber: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                adminPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                verificationCode: [
                    { required: true, message: '请输入6位短信验证码', trigger: 'blur' },
                    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' },
                ],
                adminPassword: { required: true, validator: this.validatePassword, trigger: 'blur' },
            },
            pickerAfterOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            // 日期选择器选项
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() > Date.now()
                },
                shortcuts: [{
                    text: '今天',
                    onClick (picker) {
                        picker.$emit('pick', new Date())
                    }
                }, {
                    text: '昨天',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24)
                        picker.$emit('pick', date)
                    }
                }, {
                    text: '一周前',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
                        picker.$emit('pick', date)
                    }
                }]
            },
        }
    },
    created () {
        this.getAddressPickerOptions()
        this.getEnterpriseAuthInfoM()
        //this.getRegisterAgreeUser()
    },
    mounted () { },
    methods: {
        writeTaxRate () {
            if (this.businessForm.taxRate != null) {
                if (!(0 <= this.businessForm.taxRate && this.businessForm.taxRate <= 100)) {
                    this.$message.error('税率不能小于0或大于100')
                    this.businessForm.taxRate = 0
                }
            }else {
                this.$message.error('税率不能为空')
                this.businessForm.taxRate = 0
            }
        },
        showQuality_GTH () {
            if(this.businessForm.certificate.includes('其他质量认证')) {
                this.OtherQualityCertifications_GTH = true
            }else {
                this.OtherQualityCertifications_GTH = false
                this.businessForm.certificateOther = null
            }
        },
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading2 = true
            let uploadRes = await uploadFile(form)
            this.fileLoading2 = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.businessFormFileList.push(file)
                this.businessFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                this.businessForm.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,
                })
            }
        },
        handleRemove (file) {
            this.fileLoading = true
            let files = this.enterpriseForm.files
            let recordId = null
            let newFiles = files.filter(t =>{
                if(file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                }else {
                    return true
                }
            })
            createWFileRecordDelete({ recordId: recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.enterpriseForm.files = newFiles
                }
                this.fileLoading = false
            }).catch(() => {
                this.fileLoading = false
            })
        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if(size > 100) {
                this.$message.error('文件大小不能超过100M')
                return false
            }
            return true
        },
        handleExceed () {
            this.$message.error('文件个数不能超出10个')
            return false
        },
        //获取成为个体户提所需下载附件资料
        getRegisterAgreeUser () {
            findByProgramaKey({ programaKey: 'individualRegistration' }).then(res => {
                this.fileList = res.files
            })
        },
        // 获取企业信息
        getEnterpriseAuthInfoM () {
            getEnterpriseAuthInfo({}).then(res => {
                this.businessForm = res
                this.longTerm = this.businessForm.licenseTerm === null
                this.szlpEndTime = this.businessForm.lpEndTime === null
                this.tableData_GTH = this.businessForm.epLists
                previewFile({ recordId: this.businessForm.businessLicenseId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.businessLicense = url
                })
                this.getCityOrDistrict(res.provincesCode, 1)
                this.getCityOrDistrict(res.cityCode, 2)
                this.getCityOrDistrict(res.provincesGdCode, 3)
                this.getCityOrDistrict(res.cityGdCode, 4)
                this.showQuality_GTH()
                this.fileList = this.businessForm.files
                this.cnsFileList = this.fileList.filter(item => item.category === '承诺书')
                this.sqwtsFileList = this.fileList.filter(item => item.category === '授权委托书')
                this.fddbrsfzmFileList = this.fileList.filter(item => item.category === '法定代表人身份证明')
                this.zjyqwszmFileList = this.fileList.filter(item => item.category === '最近一期完税证明')
                this.zgzxxxgkwFileList = this.fileList.filter(item => item.category === '中国执行信息公开网查询情况')
                this.zzzsFileList = this.fileList.filter(item => item.category === '资质证书')
                this.qtzsFileList = this.fileList.filter(item => item.category === '其他证书')
                this.isQtzs = this.qtzsFileList.length > 0
                this.isZzzs = this.zzzsFileList.length > 0
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        async uploadCns (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.businessFormRules.powerOfAttorney.required = false
                this.businessForm.letterCommitmentId = res[0].recordId
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.letterCommitment = url
                })
                //this.businessForm.letterCommitment = res[0].nonIpObjectPath
                this.addBusinessFiles(res[0].objectName, '承诺书', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadSqwts (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.businessFormRules.propsqwts.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.adminAuthorize = url
                })
                //this.businessForm.adminAuthorize = res[0].nonIpObjectPath
                this.businessForm.adminAuthorizeId = res[0].recordId
                this.addBusinessFiles(res[0].objectName, '授权委托书', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        deleteQtzs () {
            this.isQtzs = false
            this.businessForm.other = null
            this.businessForm.otherId = null
            this.businessForm.otherStartTime = null
            this.businessForm.otherEndTime = null
            this.qtzsFileList = []
        },
        async uploadQtzs (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.other = url
                })
                //this.businessForm.other = res[0].nonIpObjectPath
                this.businessForm.otherId = res[0].recordId
                this.addBusinessFiles(res[0].objectName, '其他证书', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadZzzs (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.qualificationCertificate = url
                })
                //this.businessForm.qualificationCertificate = res[0].nonIpObjectPath
                this.businessForm.qualificationCertificateId = res[0].recordId
                this.addBusinessFiles(res[0].objectName, '资质证书', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        deleteZzzs () {
            this.isZzzs = false
            this.businessForm.qualificationCertificate = null
            this.businessForm.qualificationCertificateId = null
            this.businessForm.qcStartTime = null
            this.businessForm.qcEndTime = null
            this.zzzsFileList = []
        },
        async uploadFrSfzm (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.businessFormRules.propfaren.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.lpIdentification = url
                })
                //this.businessForm.lpIdentification = res[0].nonIpObjectPath
                this.businessForm.lpIdentificationId = res[0].recordId
                this.addBusinessFiles(res[0].objectName, '法定代表人身份证明', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadWszm (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.businessFormRules.zjyqwszm.required = false
                this.businessForm.taxPaymentCertificateId = res[0].recordId
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.taxPaymentCertificate = url
                })
                //this.businessForm.taxPaymentCertificate = res[0].nonIpObjectPath
                this.addBusinessFiles(res[0].objectName, '最近一期完税证明', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadZxxx (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.businessFormRules.zgzxxxgk.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.zxgk = url
                })
                //this.businessForm.zxgk = res[0].nonIpObjectPath
                this.businessForm.zxgkId = res[0].recordId
                this.addBusinessFiles(res[0].objectName, '中国执行信息公开网查询情况', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        addZzzs () {
            this.isZzzs = true
        },
        addQtzl () {
            this.isQtzs = true
        },
        handleChangeCns (file, fileList) {
            this.cnsFileList = fileList
        },
        handleChangeSqwts (file, fileList) {
            this.sqwtsFileList = fileList
        },
        handleChangeFrSfzm  (file, fileList) {
            this.fddbrsfzmFileList = fileList
        },
        handleChangeWszm (file, fileList) {
            this.zjyqwszmFileList = fileList
        },
        handleChangeZxxx (file, fileList) {
            this.zgzxxxgkwFileList = fileList
        },
        handleChange () {},
        downloadFrSfzm () {
            this.handleDownload( { fileFarId: '1924378621633327106', name: '法定代表人（单位负责人）授权委托书.docx' } )
        },
        downloadsqwts () {
            this.handleDownload( { fileFarId: '1924378853041467394', name: '授权委托书.docx' } )
        },
        downloadCns () {
            this.handleDownload( { fileFarId: '1924375289770504193', name: '承诺书.docx' } )
        },
        handleRemoveAttorney (file, fileList) {
            this.cnsFileList = fileList
        },
        handleRemovePropsqwts (file, fileList) {
            this.sqwtsFileList = fileList
        },
        handleRemovePropfaren (file, fileList) {
            this.fddbrsfzmFileList = fileList
        },
        handleRemoveZjyqwszm (file, fileList) {
            this.zjyqwszmFileList = fileList
        },
        handleRemoveZgzxxxgk (file, fileList) {
            this.zgzxxxgkwFileList = fileList
        },
        handleUploadChange (file) {
            if (file.status == 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status == 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        getbusinessFormFileList () {
            let params = {

            }
            getEnterPriseFileList(params).then(res=>{
                this.businessFormFileList = res.list
            })
        },
        // 个体户上传营业执照
        async uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            uploadFile(form).then(res => {
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.businessLicense = url
                })
                this.businessForm.businessLicense = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                this.businessForm.businessLicenseId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        //时间验证
        validateDate (rule, value, callback) {
            if (value == null || value == '') {
                return callback(new Error('请选择时间！'))
            }
            callback()
        },
        // 校验个体户税率
        // eslint-disable-next-line
      validateBusinessTaxRate (rule, value, callback) {
            if(!value) return callback(new Error('请输入税率'))
            let lessThan = parseInt(value) < 0
            let biggerThan = parseInt(value) > 100
            lessThan ? this.businessForm.taxRate = 0 : ''
            biggerThan ? this.businessForm.taxRate = 100 : ''
            if(lessThan || biggerThan) return callback(new Error('超出限制'))
            if(value.includes('.') && value.split('.')[1].length > 2) this.businessForm.taxRate = parseFloat(parseFloat(value).toFixed(2))
            callback()
        },
        // 校验地址信息
        validateBusinessAddress (rule, value, callback) {
            if ( this.businessForm.provincesCode == null || this.businessForm.provincesCode == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.businessForm.cityCode == null || this.businessForm.cityCode == '' ) {
                return callback(new Error('请选择市级！'))
            }
            if ( this.businessForm.countyCode == null || this.businessForm.countyCode == '' ) {
                return callback(new Error('请选择县、区！'))
            }
            callback()
        },
        // 校验地址信息
        validateBusinessAddress_gd (rule, value, callback) {
            if ( this.businessForm.provincesGdCode == null || this.businessForm.provincesGdCode == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.businessForm.cityGdCode == null || this.businessForm.cityGdCode == '' ) {
                return callback(new Error('请选择市级！'))
            }
            if ( this.businessForm.countyGdCode == null || this.businessForm.countyGdCode == '' ) {
                return callback(new Error('请选择县、区！'))
            }
            callback()
        },
        //密码验证
        validatePassword (rule, value, callback) {
            let len = value.trim().length
            if (len < 8 || len > 20) {
                return callback(new Error('请输入8到20位的密码！'))
            }
            if (!new RegExp(/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9])(.{8,20})$/).test(value)) {
                return callback(new Error('密码必须同时包含数字，字母和特殊字符！'))
            }
            callback()
        },
        validateAddress (rule, value, callback) {
            if ( this.enterpriseForm.provinces == null || this.enterpriseForm.provinces == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.enterpriseForm.city == null || this.enterpriseForm.city == '' ) {
                return callback(new Error('请选择市级！'))
            }
            callback()
        },
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        // 获取地址选择器数据
        async getAddressPickerOptions () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.addressOptions.province = res
            this.addressOptionsGd.province = res
        },
        getCityOrDistrict (code, layer) {
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1 || layer === 2) {
                    layer === 1 ? this.addressOptions.city = res : this.addressOptions.district = res
                }else if (layer === 3 || layer === 4) {
                    layer === 3 ? this.addressOptionsGd.city = res : this.addressOptionsGd.district = res
                }
            })
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            if (layer === 1) {
                this.businessForm.city = ''
                this.businessForm.county = ''
                this.businessForm.cityCode = ''
                this.businessForm.countyCode = ''
                this.businessForm.detailedAddress = ''
            }else if (layer === 2) {
                this.businessForm.county = ''
                this.businessForm.countyCode = ''
                this.businessForm.detailedAddress = ''
            }else if (layer === 3) {
                this.businessForm.cityGd = ''
                this.businessForm.countyGd = ''
                this.businessForm.cityGdCode = ''
                this.businessForm.countyGdCode = ''
                this.businessForm.workXxdz = ''
            }else if (layer === 4) {
                this.businessForm.countyGd = ''
                this.businessForm.countyGdCode = ''
                this.businessForm.workXxdz = ''
            }
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1 || layer === 2) {
                    layer === 1 ? this.addressOptions.city = res : this.addressOptions.district = res
                }else if (layer === 3 || layer === 4) {
                    layer === 3 ? this.addressOptionsGd.city = res : this.addressOptionsGd.district = res
                }
            })
        },
        // 地址更改触发
        addressChange (val) {
            if(!val) {
                //个体户
                this.businessForm.provinces = this.$refs.selectLabel4.selectedLabel
                this.businessForm.city = this.$refs.selectLabel5.selectedLabel
                this.businessForm.county = this.$refs.selectLabel6.selectedLabel
                this.businessForm.detailedAddress = this.businessForm.provinces + this.businessForm.city + this.businessForm.county
            }
        },
        handleAddGth () {
            this.tableData_GTH.push({})
        },
        // 删除记录
        handleDeleteGth (index) {
            this.$confirm('确定要删除这条记录吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData_GTH.splice(index, 1)
                this.$message.success('删除成功!')
            })
        },
        // 地址更改触发
        addressChange_gd (val) {
            if(!val) {
                //个体户
                this.businessForm.provincesGd = this.$refs.selectLabel4_gd.selectedLabel
                this.businessForm.cityGd = this.$refs.selectLabel5_gd.selectedLabel
                this.businessForm.countyGd = this.$refs.selectLabel6_gd.selectedLabel
                this.businessForm.workXxdz = this.businessForm.provincesGd + this.businessForm.cityGd + this.businessForm.countyGd
            }
        },
        // 上传身份证
        async uploadIdentity (params, num, type) {
        //上传管理员身份证人像面
            if(num === 1 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    if(type == 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.enterpriseForm.cardPortraitFace = window.URL.createObjectURL(res)
                        })
                        this.enterpriseForm.cardPortraitFaceId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '管理员身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                    if(type == 2) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.businessForm.cardPortraitFace = window.URL.createObjectURL(res)
                        })
                        this.businessForm.cardPortraitFaceId = res[0].recordId
                        this.addBusinessFiles(res[0].objectName, '管理员身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传管理员身份证国徽面
            if(num === 2 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    if(type === 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.enterpriseForm.cardPortraitNationalEmblem = url
                        })
                        this.enterpriseForm.cardPortraitNationalEmblemId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '管理员身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                    if(type === 2 ) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.businessForm.cardPortraitNationalEmblem = url
                        })
                        this.businessForm.cardPortraitNationalEmblemId = res[0].recordId
                        this.addBusinessFiles(res[0].objectName, '管理员身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传法人身份证人像面
            if(num === 3 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    //企业注册
                    if(type == 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.enterpriseForm.legalPersonFace = window.URL.createObjectURL(res)
                        })
                        this.enterpriseForm.legalPersonFaceId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '法定代表人身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                    //个体户注册
                    if(type == 2) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.businessForm.legalPersonFace = window.URL.createObjectURL(res)
                        })
                        this.businessForm.legalPersonFaceId = res[0].recordId
                        this.addBusinessFiles(res[0].objectName, '法定代表人身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传法人身份证国徽面
            if(num === 4 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    //企业注册
                    if(type === 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.enterpriseForm.legalPersonNational = url
                        })
                        this.enterpriseForm.legalPersonNationalId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '法定代表人身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                    //个体户注册
                    if(type === 2 ) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.businessForm.legalPersonNational = url
                        })
                        this.businessForm.legalPersonNationalId = res[0].recordId
                        this.addBusinessFiles(res[0].objectName, '法定代表人身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
        },
        createUploadFile (file) {
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            return form
        },
        openPhoneCodeDialog () {
            if (this.verifyText3 !== '获取短信验证码') return
            // 取出表单中除verificationCode和adminPassword以外要验证的字段
            let propsToValidate = Object.keys(this.businessForm).filter(key => !['verificationCode', 'adminPassword'].includes(key))
            let validateState = true
            // 判断是否有未填写的字段
            propsToValidate.forEach(prop => {
                this.$refs['businessForm'].validateField(prop, errMsg => errMsg ? validateState = false : null)
            })
            if (!validateState) return this.$message.error('请检查是否有未填的信息')
            this.phoneCodeDialogVisible = true
        },
        //企业注册添加附件
        addEnterpriseFiles (objectName, fileName, nonIpObjectPath, recordId) {
            let fileSuffix = objectName.substr(objectName.lastIndexOf('.') + 1)
            this.enterpriseForm.files.push({
                name: fileName + '.' + fileSuffix,
                relevanceType: 8,
                url: nonIpObjectPath,
                fileType: 3,
                fileFarId: recordId,
                category: fileName
            })
        },
        addBusinessFiles (objectName, fileName, nonIpObjectPath, recordId) {
            let fileSuffix = objectName.substr(objectName.lastIndexOf('.') + 1)
            this.businessForm.files.push({
                name: fileName + '.' + fileSuffix,
                relevanceType: 8,
                url: nonIpObjectPath,
                fileType: 3,
                fileFarId: recordId,
                category: fileName
            })
        },
        validatePowerOfAttorney (rule, value, callback) {
            if (this.cnsFileList.length === 0) {
                callback(new Error('请上传承诺书!'))
            } else {
                callback()
            }
        },
        validatePropsqwts (rule, value, callback) {
            if (this.sqwtsFileList.length === 0) {
                callback(new Error('请上传授权委托书!'))
            } else {
                callback()
            }
        },
        validateZjyqwszm (rule, value, callback) {
            if (this.zjyqwszmFileList.length === 0) {
                callback(new Error('请上传最近一期完税证明!'))
            } else {
                callback()
            }
        },
        validateZgzxxxgk (rule, value, callback) {
            if (this.zgzxxxgkwFileList.length === 0) {
                callback(new Error('请上传中国执行信息公开网查询情况!'))
            } else {
                callback()
            }
        },
        onSubmit () {
            this.$refs['businessForm'].validate(valid => {
                if (valid) {
                    // if(this.agreeTerm) {
                    this.businessForm.epLists = this.tableData_GTH
                    this.businessForm.isFileModify = 1
                    updateEnterprise(this.businessForm).then(res => {
                        if(res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            })
                            this.$router.push('/user/verification/businessDetail')
                        }else {
                            this.$message({
                                message: '修改失败',
                                type: 'error'
                            })
                        }
                    })
                    // }else {
                    //     this.$message({
                    //         message: '请查看协议后勾选',
                    //         type: 'error'
                    //     })
                    // }

                }
            })
        },
    },
}
</script>
<style scoped lang="scss">
main>div {
    height: 100%;
    border: 1px solid rgba(230, 230, 230, 1);
}

.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.content {
    padding-bottom: 30px;
}

/deep/ .el-form-item {
    margin-bottom: 25px;

    .el-form-item__label {
        height: 100%;
        padding-right: 10px;
        line-height: 50px;
        font-size: 16px;
        color: #333;
    }

    .el-input__inner {
        height: 50px;
        font-size: 16px;
        border-radius: 0;
        border: 1px solid rgba(204, 204, 204, 1);
    }
}

/deep/ .el-checkbox {
    display: flex;
    align-items: center;

    .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(33, 110, 198, 1);
    }

    .el-checkbox__label {
        font-size: 16px;
        color: #333;

        span {
            color: #216EC6;
        }
    }
}

/deep/.avatar-uploader .el-upload {
    width: 138px;
    height: 138px;
    border: 1px dashed rgba(217, 217, 217, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

/deep/ .businessForm {
    width: 1066px;
    margin-top: 30px;

    .el-col:not(.el-col-24) {
        width: 48.4%;

        .el-input,
        .el-input__inner {
            width: 350px;
        }

        &.el-col-offset-1 {
            margin-left: 20px;
        }
    }

    .el-col-24 .el-input__inner,
    .el-input {
        width: 893px;
    }

    .el-input.is-disabled {
        background-color: #e6e6e6;
        color: #999;
    }

    .el-form-item__error {
        width: 80%;
        margin-top: -10px;
    }

    .licenseUploader {
        font-size: 40px;
        color: #8c939d;
        width: 138px;
        height: 138px;
        //margin-right: 20px;
        line-height: 140px;
        display: inline;

        .el-form-item__error {
            width: 500px;
        }
    }

    .avatar {
        width: 140px;
        height: 140px;
        display: block;
    }

    .uploadDemo {
        width: 138px;
        height: 138px;
        padding: 5px;
        margin-left: 40px;
        font-size: 16px;
        color: #999;
        border: 1px dashed rgba(217, 217, 217, 1);
        flex-direction: column;

        img {
            width: 130px;
            height: 95px;
        }

        span {
            margin-right: 5px;
        }
    }

    .uploadTip {
        font-size: 14px;
        margin-top: 155px;
        margin-bottom: 20px;
        margin-left: -108px;
        width: 380px;
        color: #808080;
    }

    .el-select {
        width: 100%;
    }

    .licenseValidTime {
        .el-form-item__content {
            display: flex;
        }

      .el-date-editor {width: 300px;margin-right: 20px;}

        .el-checkbox {
            height: 50px;
            margin-bottom: 0;
        }

        .el-checkbox__inner {
            border: 1px solid rgba(217, 217, 217, 1);
        }
    }

    .registerAddress {
        .el-select {
            width: 100px;
            margin-right: 10px;

            &:last-child {
                margin-right: 0;
            }

            .el-input,
            .el-input__inner {
                width: 100px;
            }
        }

        .el-form-item__error {
            margin-left: 0px;
            //margin-top: -50px;
        }
    }

    .separ {
        width: 1066px;
        height: 1px;
        margin-left: 20px;
        margin-bottom: 30px;
        border-top: 1px dashed rgba(204, 204, 204, 1);
    }

    .subtitle {
        margin-left: 60px;
        margin-bottom: 30px;
        font-size: 20px;
        font-weight: 500;
        color: #216EC6;
    }

    .identityUpload {
        width: 160px;
        height: 100px;
    }

    .verifyBox .el-button {
        width: 140px;
        height: 50px;
        padding: 0;
        font-size: 16px;
        line-height: 50px;
        color: #216EC6;
        background: #FFFFFF;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 0;
    }

    .verifyBox {

        .el-input,
        .el-input__inner {
            width: 195px !important;
            margin-right: 15px;
        }
    }

    .el-checkbox {
        margin-top: -3px;
        margin-bottom: 50px;

        .is-checked .el-checkbox__inner {
            background-color: #216ec6;
        }
    }
}

.btns {
    width: 350px;
    margin-top: -25px;

    button {
        width: 160px;
        height: 50px;
        font-size: 22px;
    }

    button:first-child {
        color: rgba(33, 110, 198, 1);
        ;
        border: 1px solid rgba(33, 110, 198, 1);
        background-color: #fff;
    }

    button:last-child {
        color: #fff;
        background-color: rgba(33, 110, 198, 1);
    }
}
//上传证书和资料
.addcardtop{
  width: 150px;
  height: 50px;
  background-color: rgb(0,155,237);
  font-size: 16px;
  margin-bottom: 30px;
  margin-left: 2%;
}
//表格
.custom-table {
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  margin-left: 2%;
  width: 100%;
}
//企业账户信息，填写内容将写入合同并涉及发票，请谨慎填写。
.setAccount{
  height: 30px;
  display: flex;
  line-height: 30px;
  margin-bottom: 30px;
}
.subtitletip{
  //border-style: dashed;
  color: #e53e30;
  font-size: 14px;
  margin-left: 50px;
}
//上传文件的按钮样式
.uploadbtn{
  display: inline-flex;
  align-items: center;
  background-color: rgb(255,118,0);
  border: 1px solid rgb(255,118,0);
  text-align: center;
  width: 120px;
  height: 35px;
  line-height: 35px;
}
.addcard{
  padding: 10px;
  margin-top: 20px;
  margin-right: 30px;
  border: 1px solid #cccccc;
  width: 500px;
  height: 100%;
  margin-left: 2%;
}
.cardtop{
  display: flex;
  justify-content: space-between;
}
.addcarddel{
  color: rgb(255,118,0);
  border-color: rgb(255,118,0);
  background-color: white;
}
</style>