<template>
    <div>
        <div class="dfa filterBox_top">
            <el-radio-group v-model="checkedRadio" @change="checkChange">
                <el-radio v-for="item in boxList" :label="item.value" :value="item.value" :key="item.value">
                    {{item.label}}
                </el-radio>
            </el-radio-group>
            <div :class="sortObj.tag === 'price' ? 'selected sortPrice sortBtn ml10 pointer': 'sortPrice sortBtn ml10 pointer'" @click="selectSort('price')">
                <span>价格</span><img src="@/assets/images/arrow.png" alt="">
            </div>
            <div :class="sortObj.tag === 'time' ? 'selected sortBtn pointer': 'sortBtn pointer'" @click="selectSort('time')">
                <span>更新时间</span><img src="@/assets/images/arrow2.png" alt="">
            </div>
        </div>
        <div class="row dfa filterBox_bottom">
            <div class="mailAddress dfa pointer">
                <img src="@/assets/images/destination.png" alt=""><span>收货地：选择收货地</span>
            </div>
            <div class="priceFilter">￥<input type="text" v-model="minPrice_"></div>
            <div class="bar"></div>
            <div class="priceFilter">￥<input type="text" v-model="maxPrice_"></div>
            <div class="searchInput df">
                <div class="dfa">
                    <img src="../../../assets/images/search1.png" alt="">
                    <input type="text" style="flex-grow: 1;" @keyup.enter="onSearch" placeholder="搜索商品/店铺" v-model="keyWord" />
                </div>
                <button @click="onSearch">搜索</button>
            </div>
        </div>
    </div>

</template>
<script>
import arrow from '@/assets/images/arrow.png'
import arrow2 from '@/assets/images/arrow2.png'
export default {
    props: ['checkBoxes', 'checked', 'minPrice', 'maxPrice', 'destination', 'keyword'],
    data () {
        return {
            boxList: this.checkBoxes,
            checkedRadio: this.checked,
            minPrice_: this.minPrice,
            maxPrice_: this.maxPrice,
            destination_: this.destination,
            keyWord: this.keyword,
            sortObj: {
                tag: 'price',
                decend: true,
            },
            arrow,
            arrow2
        }
    },
    watch: {
        checkBoxes (val) {
            this.boxList = val
        },
        checked (val) {
            this.checkedRadio = val
        },
        minPrice (val) {
            this.minPrice_ = val
        },
        maxPrice (val) {
            this.maxPrice_ = val
        },
        destination (val) {
            this.destination_ = val
        },
        keyword (val) {
            this.keyWord = val
        },
        'sortObj.tag': {
            handler () {
                this.sortObj.decend = !this.sortObj.decend
            }
        },
    },
    methods: {
        checkChange () {
        },
        onSearch () {
            this.$emit('onSearch')
        },
        selectSort (str) {
            this.sortObj.tag = str
            this.sortObj.decend = !this.sortObj.decend
        },
    },
}
</script>
<style scoped lang="scss">
.filterBox_top {
    width: 100%;
    height: 48px;
    padding-left: 20px;
    border: 1px solid #E6E6E6;

    /deep/ .el-radio {
        margin-right: 20px;

        .el-radio__inner {
            // border-radius: 0;
            border: 1px solid rgba(204, 204, 204, 1);
            // &::after {display: none;}
        }

        .el-radio__label {
            padding-left: 8px;
        }
    }

    .sortBtn {
        height: 26px;
        padding: 0 9px 0 10px;
        line-height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);

        span {
            margin-right: 8px;
        }
    }

    .sortPrice {
        margin-right: -1;
    }

    .selected {
        color: rgba(212, 48, 48, 1);
        border-color: rgba(212, 48, 48, 1);
    }
}

.filterBox_bottom {
    height: 50px;
    padding-left: 20px;
    background-color: #fff;

    .mailAddress {
        width: 196px;
        height: 26px;
        margin-right: 12px;
        color: rgba(51, 51, 51, 1);
        border: 1px solid rgba(229, 229, 229, 1);

        img {
            margin: 0 7px;
        }
    }

    .priceFilter {
        width: 94px;
        height: 26px;
        padding-left: 8px;
        color: rgba(102, 102, 102, 1);
        border: 1px solid rgba(229, 229, 229, 1);

        input {
            width: 66px;
            height: 24px;
            margin-left: 4px;
            border: none;
        }
    }

    .bar {
        width: 10px;
        height: 1px;
        margin: 8px;
        background-color: rgba(229, 229, 229, 1);
    }

    .searchInput {
        margin-left: 12px;

        &>div:first-child {
            width: 196px;
            height: 26px;
            padding-left: 8px;
            border: 1px solid rgba(229, 229, 229, 1);

            input {
                width: 104px;
                height: 21px;
                margin-left: 4px;

                &::placeholder {
                    color: rgba(204, 204, 204, 1);
                }
            }
        }

        button {
            width: 52px;
            height: 26px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(255, 255, 255, 1);
            background: rgba(212, 48, 48, 1);
        }
    }
}
</style>