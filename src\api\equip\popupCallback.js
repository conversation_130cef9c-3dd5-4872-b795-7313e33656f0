
import { selectDataClient } from '@/utils/common'

/**选择客户端弹窗*/
function removeRepetition (json) {
    let itemObj = {}
    return json.arr = json.arr.reduce((item, next)=>{
        if(next.changeType == -1) {
            item.push(next)
        }else{
            if(!itemObj[next[json.id]]) {
                item.push(next)
                itemObj[next[json.id]] = true
            }
        }
        return item
    }, [])
}

/**选择装备基础库*/
function selectEquipmentBase (json) {
    return selectDataClient({
        SelectServiceToken: '/facilitybase/equipment/getEquipmentCategoryAll', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: [], // 已选中id
        InitParamsStr: JSON.stringify(json),    // 接口参数
        Title: '装备基础库' // 标题
    })
}
/**选择配置计划装备*/
function selectConfigPlanEquip (json) {
    return selectDataClient({
        SelectServiceToken: '/facilityplan/get/configuration/plan/del', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        InitParamsStr: JSON.stringify(json.configId),    // 接口参数
        Title: '选择配置计划装备' // 标题
    })
}
/**选择人员弹窗 （选择本机构用户）*/
function selectName () {
    return selectDataClient({
        SelectServiceToken: '/hr/user/getUserUnderOrgPage', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: [], // 已选中id
        Title: '人员选择' // 标题
    })
}
/**选择经办人 (获取本机构及上级机构的所有用户)*/
function selectHandling () {
    return selectDataClient({
        SelectServiceToken: '/hr/user/getUserUnderAboveOrg', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: [], // 已选中id
        Title: '选择经办人' // 标题
    })
}
/**(获取本机构及其下级机构的所有用户)*/
function selectUserBelowOrg (json) {
    return selectDataClient({
        SelectServiceToken: '/hr/user/getUserBelowOrgPage', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: [], // 已选中id
        Title: json.title // 标题
    })
}
/**选择调入机构 (选择组织机构库所以机构) */
function selectAllOrg (json) {
    return selectDataClient({
        SelectServiceToken: '/hr/org/getAllOrgPage', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: [], // 已选中id
        Title: json.title// 标题
    })
}
/**选择接入人 */
function selectReceiver (orgId) {
    return selectDataClient({
        SelectServiceToken: '/hr/user/getUserUnderOrgPage', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: [], // 已选中id
        InitParamsStr: JSON.stringify({ 'orgId': orgId }),    // 接口参数
        Title: '选择接入人' // 标题
    })
}
/**(选择本机构及下级机构) */
function selectChildrenOrg () {
    return selectDataClient({
        SelectServiceToken: '/hr/org/getChildrenOrg', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: [], // 已选中id
        //InitParamsStr: JSON.stringify({ 'orgId': orgId }),    // 接口参数
        Title: '选择组织机构' // 标题
    })
}
/**选择装备（本机构的自有台账和租赁台账的装备) 装备调动明细*/
function selectMoveEquipment (json) {
    return selectDataClient({
        SelectServiceToken: '/facilityaccount/account/all/equipment', // 客户端对应方法名
        IsSingleMode: json.flag, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        InitParamsStr: JSON.stringify(json.params),    // 接口参数
        Title: '选择装备' // 标题
    })
}
//====================采购验收====================
/**选择装备采购合同 */
function selectPurchaseContract (json) {
    return selectDataClient({
        SelectServiceToken: '/subcontract/thirdParty/getEquipmentProcurement', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        InitParamsStr: JSON.stringify({ 'orgId': json.orgId }),    // 接口参数
        Title: '选择装备采购合同' // 标题
    })
}
/**选择采购验收明细 */
function selectPurchaseDtl (json) {
    return  selectDataClient({
        SelectServiceToken: '/subcontract/thirdParty/getEquipmentProcurementDetailedByContractId', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        InitParamsStr: JSON.stringify(json.contractId),    // 接口参数
        Title: '选择装备采购合同' // 标题
    })
}
//====================租赁验收============================
/**选择装备调动（内租）*/
function selectLeaseMoveEquip (ids) {
    return selectDataClient({
        SelectServiceToken: '/facilityconnect/lease/receive/get/transfer', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        //InitParamsStr: JSON.stringify({ 'listId': ids }),    // 接口参数
        Title: '选择装备调动' // 标题
    })
}
/**选择装备租赁计划/合同 */
function selectLeaseplanContract (ids, type) {
    return selectDataClient({
        SelectServiceToken: '/facilityconnect/lease/receive/get/plan/contract', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        InitParamsStr: JSON.stringify(type),    // 接口参数
        Title: '选择装备租赁计划/合同' // 标题
    })
}
/**选择租赁验收明细 */
function selectLeaseAcceptDtl (json, ids) {
    return selectDataClient({
        SelectServiceToken: '/facilityconnect/lease/receive/dtl/get/equipment', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        InitParamsStr: JSON.stringify(json),    // 接口参数
        Title: '选择租赁验收明细' // 标题
    })
}
//====================临租转长租====================
/**选择装备租赁合同*/
function selectLeaseContract (json) {
    return selectDataClient({
        SelectServiceToken: '/subcontract/thirdParty/getEquipmentLeasing', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        //InitParamsStr: JSON.stringify({ 'orgId': json.orgId }),    // 接口参数
        Title: '选择装备租赁合同' // 标题
    })
}
/**选择临租转长租明细*/
function selectlongLeaseDtl (json) {
    return selectDataClient({
        SelectServiceToken: '/subcontract/thirdParty/getEquipmentLeasingDetailedByContractId', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        InitParamsStr: JSON.stringify(json.contractId),    // 接口参数
        Title: '选择临租转长租明细' // 标题
    })
}
//====================租赁退场============================
/**租赁退场-选择接收单位 */
function selectLeaseExitOrg (ids) {
    return selectDataClient({
        SelectServiceToken: '/facilityconnect/lease/exit/choose/org', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        //InitParamsStr: JSON.stringify({ 'orgId': json.orgId }),    // 接口参数
        Title: '选择接收单位' // 标题
    })
}
/**租赁退场明细-选择验收明细 */
function selectLeaseExitDtl (ids, orgId) {
    return selectDataClient({
        SelectServiceToken: '/facilityconnect/lease/exit/choose/dtl', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        InitParamsStr: JSON.stringify(orgId),    // 接口参数
        Title: '选择验收明细 ' // 标题
    })
}
//====================运行记录============================
/**运行记录-选择操作手 */
function selectHandleOperator (ids) {
    return selectDataClient({
        SelectServiceToken: '/facilitybase/OperatorRegister/AdvancedQuery', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        //InitParamsStr: JSON.stringify(orgId),    // 接口参数
        Title: '选择验收明细 ' // 标题
    })
}
/**运行记录-选择外包方 */
function selectOutsourcing (ids) {
    return selectDataClient({
        SelectServiceToken: '/subcontract/thirdParty/getOperationRecord', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        //InitParamsStr: JSON.stringify(orgId),    // 接口参数
        Title: '选择外包方 ' // 标题
    })
}
//=================维修保养记录===============================
/**选择维修计划 */
function selectMaintenance (json) {
    return selectDataClient({
        SelectServiceToken: '/facilitymanagement/maintenance/log/get/list', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        InitParamsStr: JSON.stringify(json.type),    // 接口参数
        Title: '选择维修计划 ' // 标题
    })

}
/** 选择维保计划明细 */
function selectMaintenanceDtl (json) {
    return selectDataClient({
        SelectServiceToken: '/facilitymanagement/maintenance/log/dtl/get/by/id/list', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        InitParamsStr: JSON.stringify(json.billid),    // 接口参数
        Title: '选择维修计划 ' // 标题
    })
}
/**从机械发料引入 */
function selectStoreIssue (json) {
    return selectDataClient({
        SelectServiceToken: '/material/mechanicalMaterialIssuance/listByEquipIdForSelect', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: json.ids, // 已选中id
        InitParamsStr: JSON.stringify(json.type),    // 接口参数
        Title: '选择机械发料 ' // 标题
    })
}
/**物资基础库 */
function categoryLibrary () {
    return selectDataClient({
        SelectServiceToken: '/material/categoryLibrary/queryListByParentClassId2', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: [], // 已选中id
        InitParamsStr: JSON.stringify(
            {

                'materialType': null
            }
        ),    // 接口参数(name为模拟参数)
        Title: '选择基础库' // 标题
    })

}
//===========================报废申请===============================
/**报废申请-选择自有台账 */
function selectScrapApplication (ids, type) {
    return selectDataClient({
        SelectServiceToken: '/facilityaccount/account/own/equipment', // 客户端对应方法名
        IsSingleMode: false, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        InitParamsStr: JSON.stringify(type),    // 接口参数(name为模拟参数)
        Title: '选择自有台账' // 标题
    })
}
//========================自有台账==================================
/**自有台账-选择使用单位 */
function selectUseUnit (id) {
    const obj = {
        'businessUnitTypes': [],
        'keyword': '',
        'limit': 10,
        'orgId': '',
        'outerTypes': [],
        'page': 1,
        'states': [],
        'type': []
    }
    return selectDataClient({
        SelectServiceToken: 'GeneralSelectOuter', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: id, // 已选中id
        InitParamsStr: JSON.stringify(obj),    // 接口参数(name为模拟参数)
        Title: '选择使用单位' // 标题
    })
}
//========================外协台账==================================
/**外协台账-选择外协单位 */
function selectOutsourceUnit (ids, orgId) {
    return selectDataClient({
        SelectServiceToken: '/subcontract/thirdParty/getOutsourcingAccount', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: ids, // 已选中id
        InitParamsStr: JSON.stringify(orgId),    // 接口参数(name为模拟参数)
        Title: '选择外协单位' // 标题
    })
}
export {
    removeRepetition, //去重
    selectConfigPlanEquip, //选择配置计划装备
    selectEquipmentBase, //选择装备基础库
    selectName, //选择人员弹窗
    selectHandling, //选择经办人
    selectUserBelowOrg, //选择本机构及其下级机构的用户
    selectAllOrg, //选择调入机构
    selectReceiver, //选择接入人
    selectChildrenOrg, //(选择本机构及下级机构)
    selectPurchaseContract, //选择装备采购合同
    selectPurchaseDtl, //选择采购验收明细
    selectMoveEquipment, //选择装备-调动明细
    selectLeaseMoveEquip, //选择装备调动
    selectLeaseplanContract, //选择租赁计划/合同
    selectLeaseAcceptDtl, //选择租赁验收明细
    selectLeaseExitDtl, //租赁退场明细-选择验收明细
    selectLeaseExitOrg, //租赁退场-选择接收单位
    selectLeaseContract, //选择装备租赁合同
    selectlongLeaseDtl, //选择临租转长租明细
    selectHandleOperator, //运行记录-选择操作手
    selectOutsourcing, //运行记录选择外包方
    selectMaintenance, //维保记录-选择维修计划
    selectStoreIssue, //维保记录-机械发料
    categoryLibrary, //选择物资基础库
    selectMaintenanceDtl, //维保记录-选择维修计划明细
    selectScrapApplication, //报废申请-选择自有台账
    selectOutsourceUnit, //外协台账-选择外协单位
    selectUseUnit, //自有台账-选择使用单位
}
