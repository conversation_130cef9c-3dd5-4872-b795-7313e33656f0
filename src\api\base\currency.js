import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

// 获取字典
const getCurrency = params => {
    return httpGet({
        url: '/config/kv/getDicValue',
        params
    })
}
// 获取系统级字典
const getSystemCurrency = params => {
    return httpGet({
        url: '/config/kv/getDicValue1',
        params
    })
}
// 添加、修改功能级字典值
const setCurrency = params => {
    return httpPost({
        url: '/config/kv/setDicKV?dicName=' +  params.dicName,
        params
    })
}
// 删除功能级字典值
const deleteCurrency = params => {
    return httpPostForm({
        url: '/config/kv/delDicKV',
        params
    })
}
// 获取功能级字典值(树形字典)
const getCurrencyTree = params => {
    return httpGet({
        url: '/config/kv/getDicValueTree',
        params
    })
}
// 获取系统级字典值(树形字典)
const getSystemCurrencyTree = params => {
    return httpGet({
        url: '/config/kv/getDicValueTree1',
        params
    })
}
// 添加、修改功能级字典值(树形字典)
const setCurrencyTree = params => {
    return httpPost({
        url: '/config/kv/setDicKVTree?dicName=' +  params.dicName,
        params
    })
}
// 删除功能级字典值(树形字典)
const deleteCurrencyTree = params => {
    return httpPostForm({
        url: '/config/kv/delDicKVTree',
        params
    })
}
export {
    getCurrency,
    setCurrency,
    deleteCurrencyTree,
    getCurrencyTree,
    setCurrencyTree,
    deleteCurrency,
    getSystemCurrencyTree,
    getSystemCurrency
}
