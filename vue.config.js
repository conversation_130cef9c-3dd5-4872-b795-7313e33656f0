const path = require('path')
// const glob = require('glob-all')
// const PurgeCssPlugin = require('purgecss-webpack-plugin')
const webpack = require('webpack')
const compressionWebpackPlugin = require('compression-webpack-plugin')
// const UglifyJsPlugin = require('uglifyjs-webpack-plugin')
const productionGzipExtensions = ['js', 'css'] //压缩的文件类型
const plugins = [
    new webpack.ProvidePlugin({
        'window.Quill': 'quill/dist/quill.js',
        Quill: 'quill/dist/quill.js'
    })
]
if (process.env.NODE_ENV === 'production') {
    plugins.push(
        new compressionWebpackPlugin({
            //[file] 会被替换成原始资源。[path] 会被替换成原始资源的路径， [query] 会被替换成查询字符串。默认值是 "[path].gz[query]"。
            filename: '[path][base].gz', // 提示compression-webpack-plugin@3.0.0的话asset改为filename
            //可以是 function(buf, callback) 或者字符串。对于字符串来说依照 zlib 的算法(或者 zopfli 的算法)。默认值是 "gzip"。
            algorithm: 'gzip',
            //所有匹配该正则的资源都会被处理。默认值是全部资源。
            test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
            //只有大小大于该值的资源会被处理。单位是 bytes。默认值是 0。
            threshold: 10240,
            //只有压缩率小于这个值的资源才会被处理。默认值是 0.8。
            minRatio: 0.8
        })
    )
}
// 网关 ip端口号，默认http://localhost:10010
const target = process.env.VUE_APP_CONFIG_PROXY_TARGET || 'http://localhost:10010'
// oss微服务ip端口号 默认 'http://localhost:9009'
const ossTarget = process.env.VUE_APP_CONFIG_PROXY_OSS_TARGET || 'http://localhost:9009'
const minioTarget = process.env.VUE_APP_CONFIG_PROXY_MINIO_TARGET || 'http://localhost:9002'
// 前端devServer端口 默认9090
const port = process.env.VUE_APP_CONFIG_PORT || 9090

module.exports = {
    publicPath: '/', // 此处为 ./ 会导致动态路由报错
    assetsDir: 'static',
    transpileDependencies: [/node_modules[/\\\\](ElementUI|vuex|)[/\\\\]/],
    productionSourceMap: false,
    configureWebpack: {
        // mode: 'development',
        devtool: 'source-map',
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src')
            }
        },
        output: {
            filename: process.env.NODE_ENV === 'production' ? 'static/js/[name].[contenthash:8].bundle.js' : 'static/js/[name].[hash:8].bundle.js'
        },
        optimization: {
            splitChunks: {
                chunks: 'all',
                minSize: 200000,
                cacheGroups: {
                    // 'element-ui': {
                    //     name: 'element-ui',
                    //     test: /[\\/]node_modules[\\/]element-ui[\\/]/,
                    //     priority: -10
                    // },
                    // jquery: {
                    //     name: 'jquery',
                    //     test: /[\\/]node_modules[\\/]jquery[\\/]/,
                    //     priority: -10
                    // },
                    jquery: {
                        name: 'echarts',
                        test: /[\\/]node_modules[\\/]echarts[\\/]/,
                        priority: -10
                    },
                    vendors: {
                        name: 'vendors',
                        test: /[\\/]node_modules[\\/]/,
                        priority: -20
                    }
                }
            }
        },
        plugins,
        externals: {
            vue: 'Vue',
            vuex: 'Vuex',
            'vue-router': 'VueRouter',
            axios: 'axios',
            'element-ui': 'ELEMENT',
            jquery: '$'
        }
    },
    chainWebpack: config => {
        config.plugin('html').tap(args => {
            args[0].title = '物资采购平台'
            return args
        })
        config.optimization.minimizer('terser').tap(args => {
            args[0].terserOptions.output = {
                comments: false
            }
            return args
        })
    },
    // TODO 这里删除了部分意义不明的转发，后续有问题的话再一个个排查
    devServer: {
        port,
        proxy: {

            // //物资基础库接口
            // '/api/': {
            //     target: 'http://***************:15103', //测试
            //     changeOrigin: true,
            //     pathRewrite: {
            //         '': ''
            //     },
            // },
            // TODO 需要确认是否有用
            //  pcwp2测试接口
            '/api/PCWP2': {
                target: 'http://**************:15103', //测试
                // target: 'https://pcwp2.scrbg.com/thirdApi', //正式
                changeOrigin: true,
                pathRewrite: {
                    '/api/PCWP2': ''
                },
            },

            // TODO 需要确认是否有用
            // 物资采购合同
            '/api/pacwp1/buyContract': {
                target: 'http://171.221.203.162:7071',
                changeOrigin: true,
                pathRewrite: {
                    '/api/pacwp1/buyContract': ''
                },
            },
            // TODO 需要确认是否有用
            // 零星采购计划
            '/api/pacwp1': {
                target: 'http://171.221.203.162:7073', //测试
                changeOrigin: true,
                pathRewrite: {
                    '/api/pacwp1': ''
                },
            },
            // 这里直连oss微服务而不是网关，似乎文件上传下载的操作有意绕过网关操作？
            // 当前使用的版本 devServer.proxy 里的上下顺序影响匹配优先级，这里'/api/oss'要在'/api/*'上边
            // TODO 是否要优化鉴权
            '/api/oss': {
                target: ossTarget,
                changeOrigin: true,
                pathRewrite: {
                    '/api/oss': '/oss'
                }
            },
            // 招投标微服务未启动，路由失败会导致前端项目开发模式启动的进程退出，这里暂时随便写一个target
            // TODO 后续考虑调整所有tender开头的请求
            '/api/tender': {
                target: 'http://192.168.91.11:9030',
                changeOrigin: true,
                pathRewrite: {
                    '/api/tender': ''
                }
            },
            // 企业注册获取省市县名称
            '/api/config/*': {
                target: 'http://**************:15103/',
                changeOrigin: true,
                pathRewrite: {
                    '/api': ''
                },
            },
            // 首页、注册页一些接口需要直接访问商城微服务
            '/api/materialMall': {
                target: 'http://localhost:9010',
                changeOrigin: true,
                pathRewrite: {
                    '/api/materialMall': ''
                }
            },
            '/api/*': {
                target: target,
                pathRewrite: { '^/api': '' },
            },
            // TODO 需要确认是否有用
            '/fileUpload': {
                target: 'http://***************:15103',
                changeOrigin: true,
                pathRewrite: {
                    '/fileUpload': ''
                },
            },
            // 这里直连minio，响应的minio里的资源要设置为public
            '/mall/oss-url-': {
                target: minioTarget,
                pathRewrite: {
                    '^/mall/oss-url-': '/mall/oss-url-'
                },
                changeOrigin: true,
                secure: false,
            }

        }
    }
}
