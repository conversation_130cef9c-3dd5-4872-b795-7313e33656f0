<template>
    <div class="df">
        <div class="boxTop">
            <div class="title center">个人用户注册</div>
        </div>
        <div class="boxBottom">
            <div class="icon center"></div>
            <div class="msg1">注册成功</div>
            <div class="msg2">即将跳转至慧采商城首页…</div>
            <button class="center" @click="directTo('')">立即跳转</button>
        </div>
    </div>
</template>
<script>
export default {
    data () {
        return {}
    },
    methods: {
        directTo (url) {
            window.open(url)
        }
    }
}
</script>
<style scoped lang="scss">
.df {
    flex-direction: column;
}
.boxTop {
    height: 87px;
    border-bottom: 1px solid #D9D9D9;
    .title {
        width: 200px;
        height: 100%;
        font-size: 26px;
        font-weight: 500;
        line-height: 87px;
        text-align: center;
        border-bottom: 4px solid #216EC6;
        color: #333;
        user-select: none;
    }
}
.boxBottom {
    text-align: center;
    flex-grow: 1;
    .icon {
        width: 100px;
        height: 100px;
        margin-top: 120px;
        background: url(../../../assets/images/userCenter/zc_chenggong.png);
    }
    .msg1 {
        margin: 30px 0 15px 0;
        font-size: 22px;
        color: #333;
        text-align: center;
        font-weight: 400;
    }
    .msg2 {
        margin-bottom: 72px;
        font-size: 16px;
        text-align: center;
        color: #999;
    }
    button {
        width: 150px;
        height: 50px;
        font-size: 22px;
        color: #fff;
        background-color: #216EC6;
    }
}
</style>