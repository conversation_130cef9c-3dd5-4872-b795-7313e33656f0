<template>
  <div>
    <div class="right" v-loading="showLoading">
      <el-tabs v-model="activeName" @tab-click="handleClick" style="padding-left: 20px">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="零星采购" name="first"></el-tab-pane>
        <el-tab-pane label="大宗临购" name="second"></el-tab-pane>
        <el-tab-pane label="周转材料" name="third"></el-tab-pane>
      </el-tabs>
      <!--            <el-date-picker-->
      <!--                :default-time="['00:00:00', '23:59:59']"-->
      <!--                @change="dateChange"-->
      <!--                value-format="yyyy-MM-dd HH:mm:ss"-->
      <!--                v-model="filterData.dateScope"-->
      <!--                type="datetimerange"-->
      <!--                range-separator="至"-->
      <!--                :picker-options="pickerOptions"-->
      <!--                start-placeholder="开始日期"-->
      <!--                end-placeholder="结束日期">-->
      <!--            </el-date-picker>-->
      <!--            表格-->
      <div class="e-table" :style="{ width: '100%' }">
        <div class="top">
          <div class="left">
            <div class="left-btn">
              <div style="height: 50px; line-height: 50px;margin-left: 10px">
                <el-date-picker
                    :default-time="['00:00:00', '23:59:59']"
                    @change="dateChange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    v-model="filterData.dateScope"
                    type="datetimerange"
                    range-separator="至"
                    :picker-options="pickerOptions"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
              </div>
            </div>
            <div class="search_box">
              <div  style="display: flex;height: 50px; line-height: 50px;margin-left: 10px">
              <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords">
                <img src="@/assets/search.png"
                     slot="suffix" @click="onSearch"/></el-input>
                <el-button style="margin-left: 10px;margin-top: 10px" type="primary" @click="outputAll" class="">数据导出</el-button>

              </div>
            </div>
          </div>
        </div>
        <el-table
            :data="tableData"
            border
            show-summary
            :summary-method="getSummaries"
            style="width: 100%">
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="店铺名称" width="" prop="shopName"></el-table-column>
          <el-table-column label="订单数量" width="" prop="ordersCount"></el-table-column>
          <el-table-column label="总成本价" width="" prop="costPriceTotal"></el-table-column>
          <el-table-column label="总利润" width="" prop="profitPriceTotal" />
          <el-table-column label="总金额" width="" prop="actualAmount" />
        </el-table>
      </div>
      <!--            分页-->
      <Pagination
          v-show="tableData != null || tableData.length != 0"
          :total="paginationInfo.total"
          :pageSize.sync="paginationInfo.pageSize"
          :currentPage.sync="paginationInfo.currentPage"
          @currentChange="getPlatformShopCountM"
          @sizeChange="getPlatformShopCountM"
      />
    </div>

  </div>
</template>
<script>
//局部引用
import { getPlatformOrdersShopCount, getPlatformOrdersShopCountExcel } from '@/api/platform/order/shop'
import Pagination from '@/components/pagination/pagination'
export default{
    components: {
        Pagination
    },
    data () {
        return {
            activeName: 'all',
            showLoading: false,
            keywords: '',
            platformShopCountVo: {
                ordersCount: '',
                actualAmount: '',
                profitPriceTotal: ' ',
                costPriceTotal: ' ',
                totalAmount: ' ',
                shopCountVoList: []
            },
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableLoading: false,
            filterData: {
                dateScope: []
            },
            dataVOS: [],
            labelTitle: [], // 名称数组
            count: [], // 数量数组
        }
    },
    methods: {
        // 切换页签
        handleClick (tab, event) {
            console.log(tab, event)
            console.log(this.activeName)
            this.getPlatformShopCountM()
        },
        getSummaries () {
            // const { columns } = param
            const sums = []
            // columns.forEach((column, index) => {
            //     if (index === 0) {
            //         sums[index] = '总计'
            //         return
            //     }
            //     switch(column.property) {
            //     case 'shopName':
            //         sums[index] = 'N/A'
            //         break
            //     case 'ordersCount':
            //         sums[index] = this.platformShopCountVo.ordersCount
            //         break
            //     case 'profitPriceTotal':
            //         sums[index] = this.platformShopCountVo.profitPriceTotal
            //         break
            //     case 'totalAmount':
            //         sums[index] = this.platformShopCountVo.totalAmount
            //         break
            //     default:
            //         break
            //     }
            // })
            sums[0] = '总价'
            sums[1] = 'N/A'
            sums[2] = this.platformShopCountVo.ordersCount
            sums[3] = this.platformShopCountVo.costPriceTotal
            sums[4] = this.platformShopCountVo.profitPriceTotal
            sums[5] = this.platformShopCountVo.actualAmount
            return sums
        },
        onSearch () {
            this.getPlatformShopCountM()
        },
        outputAll () {
            getPlatformOrdersShopCountExcel(this.filterData).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '店铺商品统计表.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                this.currentQuery.ids = []
                this.dataListSelections = []
                this.$message.success('操作成功')
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        getPlatformShopCountM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: this.activeName === 'first' ? 0 : this.activeName === 'second' ? 1 : this.activeName === 'third' ? 2 : null
            }
            if (this.keywords != null && this.keywords != '') {
                params.homeName = this.keywords
            }
            if(this.filterData.dateScope != null) {
                params.startDate = this.filterData.dateScope[0],
                params.endDate = this.filterData.dateScope[1]
            }
            this.showLoading = true
            getPlatformOrdersShopCount(params).then(res => {
                this.platformShopCountVo = res
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = this.platformShopCountVo.shopCountVoList
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        dateChange () {
            this.getPlatformShopCountM()
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
    },
    //一加载页面就调用
    mounted () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope =  [this.dateStrM(start), this.dateStrM(end)]
        this.getPlatformShopCountM()
    }
}
</script>
<style scoped lang="scss">

</style>