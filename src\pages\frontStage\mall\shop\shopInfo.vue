<template>
    <div class="shopInfo">
        <div class="businessInfo contentBox mb20   center">
          <!--  <div class="title dfa">店铺信息</div>-->
            <div class="text">
                <el-form :model="shop" label-width="140px" :inline="false" size="normal">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="店铺名称：">{{ shop.shopName }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
<!--                            <el-form-item label="企业名称：">{{ enterpriseInfo.enterpriseName }}</el-form-item>-->
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="店铺编号：">{{ shop.serialNum }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="开店时间：">{{ shop.openDate | formDate }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="店铺主体：">  <span v-if="shop.shopType == '2'">个人</span>
                            <span v-else-if="shop.shopType == '0'">个体户</span>
                            <span v-else-if="shop.shopType == '1'">企业</span>
                            <span v-else-if="shop.shopType == '2'">企业</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="地址：">{{ shop.detailedAddress || '' }}</el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
<!--                        <el-col :span="8">-->
<!--                            <el-form-item label="是否支持路桥结算：" > <span v-if="shop.isInternalSettlement == '1'">是</span>-->
<!--                            <span v-else-if="shop.isInternalSettlement == '0'">否</span>-->
<!--                           </el-form-item>-->
<!--                        </el-col>-->
                        <el-col :span="8">
                            <el-form-item label="联系人：">{{ shop.linkMan }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="联系电话："> {{shop.contactNumber}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="税率："> {{shop.taxRate}}%
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" style="height: unset;">
                            <el-form-item label="主营业务：">{{ shop.mainBusiness }}</el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
        <div class="intro contentBox center mb20 center">
            <div class="title dfa">店铺简介</div>
            <div class="text" v-html="shop.shopDescrible || '暂无简介'"></div>
        </div>
        <div class="intro contentBox center mb20 center">
            <div class="title dfa">店铺证照</div>
            <div class="text">
                <template v-if="hasNoLicense">暂无证照</template>
                <template v-if="enterpriseInfo != null && enterpriseInfo.businessLicense">
                    <div class="mb10">营业执照：</div>
                    <el-image
                        style="width: 300px; height: 200px; object-fit: cover;"
                        :src="enterpriseInfo.businessLicense"
                        :preview-src-list="[enterpriseInfo.businessLicense]"
                    ></el-image>
                </template>
            </div>
        </div>

<!--        <div class="businessInfo contentBox center"  v-if="shop.shopType=='1'" >
            <div class="title dfa">企业信息</div>
            <div class="text">
                <el-form :model="enterpriseInfo" label-width="140px" :inline="false" size="normal">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="企业名称：">{{ enterpriseInfo.enterpriseName }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="统一社会信用代码：">{{ enterpriseInfo.socialCreditCode }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="注册时间：">{{ enterpriseInfo.creationTime | formDate }}</el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="注册地址：">{{ enterpriseInfo.detailedAddress }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="注册资本(万)：">{{ enterpriseInfo.registeredCapital }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="营业执照有效期：">
                                <span v-if="enterpriseInfo.licenseTerm">{{ enterpriseInfo.licenseTerm | formDate  }}</span>
                                <span v-else-if="!enterpriseInfo.licenseTerm">长期</span>
                              </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="主营业务：">{{ enterpriseInfo.mainBusiness  }}</el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>

        <div class="businessInfo contentBox center"  v-if="shop.shopType=='0'" >
            <div class="title dfa">企业信息</div>
            <div class="text">
                <el-form :model="enterpriseInfo" label-width="140px" :inline="false" size="normal">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="企业名称：">{{ enterpriseInfo.enterpriseName }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="统一社会信用代码：">{{ enterpriseInfo.socialCreditCode }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="注册时间：">{{ enterpriseInfo.creationTime | formDate }}</el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="经营场所：">{{ enterpriseInfo.detailedAddress }}</el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="经营者：">{{ enterpriseInfo.operator }}</el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="经营范围:">{{ enterpriseInfo.mainBusiness  }}</el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>-->
    </div>
</template>

<script>
import { previewFile } from '@/api/platform/common/file'
import { getShopInfo } from '@/api/frontStage/shop'

export default {
    props: ['shopInfoVo'],
    data () {
        return {
            shop: {},
            currentTab: null,
            shopType: null, //店铺类型 0：个体户  1：企业  2：个人
            enterpriseInfo: {}
        }
    },
    filters: {
        formDate (time) {
            var t = new Date(time)//row 表示一行数据, updateTime 表示要格式化的字段名称
            return t.getFullYear() + '-' + (t.getMonth() + 1) + '-' + t.getDate()
        }
    },
    computed: {
        hasNoLicense () {
            return this.enterpriseInfo == null || !this.enterpriseInfo.businessLicense
        }
    },
    created () {
        this.currentTab = this.$route.query.currentTab
        if (this.currentTab == 3) {
            let params = {
                shopId: '1878734518961074177'
            }
            getShopInfo(params).then(res => {
                this.shopInfoVo = res
                this.shop = this.shopInfoVo.shop
                this.shopType = this.shop.shopType
                this.enterpriseInfo = this.shopInfoVo.enterpriseInfo
                previewFile({ recordId: this.enterpriseInfo.businessLicenseId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseInfo.businessLicense = url
                    console.log(this.enterpriseInfo.businessLicense, 111333)
                })
            })
        } else {
            this.shop = this.shopInfoVo.shop
            this.shopType = this.shop.shopType
            this.enterpriseInfo = this.shopInfoVo.enterpriseInfo
            console.log(this.enterpriseInfo.businessLicenseId, 3333333)
            previewFile({ recordId: this.enterpriseInfo.businessLicenseId }).then(res => {
                let url = window.URL.createObjectURL(res)
                this.enterpriseInfo.businessLicense = url
                console.log(this.enterpriseInfo.businessLicense, 111333)
            })
        }
    },
    methods: {},
}
</script>
<style scoped lang="scss">
.contentBox {
    width: 1326px;
    min-width: 1326px;
    height: 100%;
}

.shopInfo {
    &>div {
        background-color: #fff;
    }
}

/deep/ .el-form-item__label {
    font-weight: bold;
    color: #333;
}

/deep/ .el-row {
    .el-col {
        height: 40px;
    }
}

.baseInfo {
    // height: 440px;
    padding: 20px 0;
}

.intro,
.businessInfo {
    padding: 16px;

    .title {
        padding-left: 14px;
        font-size: 18px;
        position: relative;

        &::before {
            content: '';
            display: block;
            width: 4px;
            height: 80%;
            border-radius: 2px;
            background-color: #5791d3;
            position: absolute;
            left: 0;
        }
    }

    .text {
        padding: 20px 0;
    }
}

.intro {}
</style>