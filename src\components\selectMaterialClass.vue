<template>
    <div class="tree">
        <div class="search_box">
            <el-input class="ipt" type="text"
                placeholder="输入搜索关键字"
                v-model="searchKey"
                @select="onSearch"
                @change="onSearch"
            ><img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
        </div>
        <el-tree
            v-loading="loading"
            ref="treeRef"
            :load="loadNode"
            :props="props"
            node-key="billId"
            @node-click="handleNodeClick"
            :render-content="renderContent"
            :data="data"
        >
        </el-tree>
    </div>
</template>

<script>
import { getMaterialClass } from '@/api/base/material'
import addIcon from '@/assets/btn/add.png'
import editIcon from '@/assets/btn/edit.png'
import delIcon from '@/assets/btn/delete.png'
export default {
    props: ['data'],
    data () {
        return {
            loading: false,
            searchKey: '',
            mapObj: {},
            treeMapObj: {},
            props: {
                label: 'className',
                children: 'children',
                isLeaf: 'isLeaf'
            },
        }
    },
    mounted () {},
    methods: {
        // eslint-disable-next-line
        handleClick (obj, node, component) {
        },
        async loadNode (node, resolve) {
            let billId = '0'
            if (node.level !== 0) {
                billId = node.data.billId
                this.$set(this.mapObj, billId, node.data)
            }
            this.$set(this.treeMapObj, billId, { node, resolve })
            const data = await this.getClass(billId)
            if ('data' in data) {
                this.loading = false
                resolve([])
            } else {
                data.forEach(item => {
                    item.isLeaf = item.isDetailed !== 0
                    if (!this.mapObj[item.billId]) {
                        this.$set(this.mapObj, item.billId, item)
                    }
                })
                this.loading = false
                if (node.level === 0) {
                    this.$set(this.mapObj, '0', data)
                } else {
                    this.$set(this.mapObj[node.data.billId], 'childList', data)
                }
                resolve(data)
            }
        },
        getClass (billId) {
            return getMaterialClass({
                parentiId: billId || '0'
            })
        },
        onSearch () {},
        handleNodeClick (data) {
            this.$parent.classNodeClick(data)
        },
        addTree (data) {
            this.$emit('addEvent', data)
        },
        editTree (data) {
            this.$emit('modifyEvent', data)
        },
        delTree (data) {
            this.$emit('delEvent', data)
        },
        // 新增顶级刷新
        refreshTreeAddTop (_data) {
            const curData = JSON.parse(JSON.stringify(_data))
            const lastNode = this.mapObj['0'][this.mapObj['0'].length - 1]
            this.mapObj['0'].push(curData)
            this.$set(this.mapObj, curData.billId, curData)
            const node = this.$refs.treeRef.getNode(lastNode)
            curData.isLeaf = true
            this.$refs.treeRef.store.insertAfter(curData, node)
        },
        // 删除
        refreshTreeDel (data) {
            delete this.mapObj[data.billId]
            let index
            this.mapObj['0'] && (index = this.mapObj['0'].findIndex(item => item.billId === data.billId))
            if (index !== -1) {
                this.mapObj['0'].splice(index, 1)
            }
            this.$refs.treeRef.store.remove(data)
        },
        // 修改刷新
        refreshTreeModify ({ billId, className }) {
            const data = this.mapObj[billId]
            data.className = className
        },
        refreshTree (_data, curData) {
            const addData = JSON.parse(JSON.stringify(_data))
            const { billId } = curData
            const data = this.mapObj[billId]
            data.isLeaf = false
            this.$refs.treeRef.store.append(addData, data)
            this.$set(this.mapObj, addData.billId, addData)
            const node = this.$refs.treeRef.getNode(data)

            // 如果已展开，则不用刷新，直接添加数据
            if (node.expanded) {
                return
            }

            // 否则刷新节点
            node.loaded = false
            node.isLeaf = false
            node.expand()
        },
        // eslint-disable-next-line no-unused-vars
        renderContent (h, { node, data, store }) {
            const me = this
            return h('div', {
                class: 'tree'
            }, [
                h('span', {
                    attrs: {
                        title: data.className,
                    },
                    class: 'txt'
                }, data.className
                ), h('img', {
                    attrs: {
                        title: '新增子级',
                        src: addIcon
                    },
                    class: 'btn',
                    on: {
                        click (e) {
                            e.stopPropagation()

                            me.addTree(data)
                        }
                    }
                },
                // 文字内容
                ''), h('img', {
                    attrs: {
                        title: '编辑分类',
                        src: editIcon
                    },
                    class: 'btn',
                    on: {
                        click (e) {
                            e.stopPropagation()
                            me.editTree(data)
                        }
                    }
                },
                ''), h('img', {
                    attrs: {
                        title: '删除分类',
                        src: delIcon
                    },
                    class: 'btn',
                    on: {
                        click (e) {
                            e.stopPropagation()
                            me.delTree(data)
                        }
                    }
                },
                '')
            ])
        }
    }
}
</script>

<style lang="scss" scoped>
    .search_box {
        flex-direction: column; margin: 5px 0;
    }
    /deep/ .el-tree-node__content{width: 100%;
        .tree{display: flex; align-items: center;
            .txt{max-width: 134px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
            .btn{display: none; margin: 0 0 0 5px; width: 22px; height: 22px;}
        }
        &:hover{
            .tree{
                .btn{display: block;}
            }
        }
    }
</style>