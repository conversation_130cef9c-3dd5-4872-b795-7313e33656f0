<template>
  <div class="base-page">
    <div class="left">
        <pcwp-org-tree ref="materialClassRef" :productType="0" is-lc="0" />
    </div>
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <div class="top">
          <div class="left">
            <div class="left-btn dfa">
              <el-button v-if="!systemUserAdd" type="primary" @click="operate_td('新增')">新增</el-button>
              <!-- <el-button :disabled="systemUserAdd" type="primary" @click="operate_td('新增')">新增</el-button> -->
              <!-- <el-button @click="updateStateBatch(true)" class="btn-greenYellow">批量启用</el-button>
              <el-button @click="updateStateBatch(false)" class="btn-delete">批量停用</el-button> -->
            </div>
          </div>
          <div class="search_box" style="width: 400px">
            <el-input
                clearable type="text" @blur="getTableData" placeholder="请输入用户名、姓名或手机号"
                v-model="init.keyword"
            >
              <img src="@/assets/search.png" slot="suffix" @click="getTableData" alt=""/>
            </el-input>
          </div>
        </div>
      </div>
      <!--表格-->
      <div class="e-table" style="width: 100%;">
        <el-table ref="mainTable"
                  v-loading="tableLoading" class="table" :height="rightTableHeight" :data="tableData" border
                  @selection-change="selectionChangeHandle" @row-click="handleCurrentInventoryClick">
          <!-- <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column> -->
          <el-table-column label="操作" width="140">
            <template v-slot="scope">
<!--              <span class="action" :disabled="1 == scope.row.isInternalUser"  @click="operate_td('',scope.row,)">编辑</span>-->
              <span class="action" :disabled="1 == scope.row.isInternalUser"  @click="operate_td('授权',scope.row,)">授权</span>
<!--              <span class="action" :disabled="1 == scope.row.isInternalUser"  @click="operate_td('删除',scope.row)">删除</span>-->
              <span class="action" :disabled="1 == scope.row.isInternalUser"  @click="operate_td('重置密码',scope.row)">重置密码</span>
              <!-- <el-button
                  style="padding:0 8px;" v-if="scope.row.state==0"
                  size="mini" type="success"
                  @click="updateState(scope.row,true,'启用')"
              >启用</el-button>
              <el-button
                  style="padding:0 8px;" v-if="scope.row.state==1"
                  size="mini" type="danger"
                  @click="updateState(scope.row,false,'停用')"
              >停用</el-button> -->
            </template>
          </el-table-column>
          <el-table-column label="用户名" width="" prop="userName"/>
          <el-table-column label="姓名" width="" prop="realName"/>
<!--          <el-table-column label="手机号" width="" prop="userMobile"/>-->
          <el-table-column label="机构" width="" prop="orgName"/>
          <el-table-column label="角色" width="" prop="roleId">
            <template v-slot="scope">
              <el-tag
                  v-for="tag in scope.row.roleNames"
                  :key="tag"
              >
                {{ tag }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80">
            <template v-slot="scope">
              <el-tag v-if="scope.row.state==1" type="success">正常</el-tag>
              <el-tag v-if="scope.row.state==0" type="danger">禁用</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <Pagination
          v-show="tableData && tableData.length > 0"
          :total="paginationInfo.total"
          :pageSize.sync="paginationInfo.pageSize"
          :currentPage.sync="paginationInfo.currentPage"
          @currentChange="getTableData"
          @sizeChange="getTableData"
      />
    </div>
    <el-dialog v-dialogDrag :title="userModal.title" :visible.sync="userModal.visible" width="40%"
               :close-on-click-modal="false">
      <div class="e-form" style="padding: 0 10px 10px;">
        <el-form ref="formEdit" :rules="formRules" :model="materialInfo" label-width="150px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="用户名称：" prop="userName">
                <el-input v-if="userModal.title == '新增用户'" placeholder="请输入用户名称"
                          v-model="materialInfo.userName"/>
                <el-input v-else v-model="materialInfo.userName" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="密码：" prop="password">
                <el-input v-if="userModal.title == '新增用户'" placeholder="请输入密码"
                          v-model="materialInfo.password"/>
                <el-input v-else type="password" v-model="materialInfo.password" disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="姓名：" prop="realName">
                <el-input disabled v-model="materialInfo.realName" placeholder="请输入姓名"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="手机号：" prop="userMobile">
                <el-input disabled v-model="materialInfo.userMobile" placeholder="请输入手机号"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="角色：" prop="roleIds">
                <el-select v-model="materialInfo.roleIds" multiple placeholder="请选择角色">
                  <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.name"
                             :value="item.roleId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="状态：" prop="state">
                <el-switch
                    v-model="materialInfo.state"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    active-text="正常"
                    inactive-text="禁用"
                    :active-value="1"
                    :inactive-value="0"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="buttons">
<!--            <el-button type="success" v-show="userModal.title == '授权'" @click="onSave()">保存</el-button>-->
            <el-button type="success" v-show="userModal.title == '授权'" @click="updateM()">保存</el-button>
<!--            <el-button type="warning" v-show="userModal.title == '授权'" @click="onSave(true)">保存并下一个-->
<!--            </el-button>-->
            <el-button type="fail" @click="closeViewListM()">取消</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <el-dialog v-dialogDrag title="重置密码" :visible.sync="passwordModal.visible" width="40%"
               :close-on-click-modal="false">
      <div class="e-form" style="padding: 0 10px 10px;">
        <el-form ref="passwordRef" :rules="passwordRules" :model="passwordForm" label-width="150px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="密码：" prop="newPassword">
                <el-input placeholder="请输入密码" v-model="passwordForm.newPassword" :type="pwType">
                  <i slot="suffix"><img class="input-icon pointer" :src="hidePass" @click="toggleShowPass" alt=""/></i>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="buttons">
            <el-button type="success" @click="confirm_password()">保存</el-button>
            <el-button type="fail" @click="passwordModal.visible = false">取消</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
// import SelectMaterialClass from './selectTree'
import PcwpOrgTree from '@/components/pcwpOrgTree.vue'
import Pagination from '@/components/pagination/pagination'
import { debounce, treeToList } from '@/utils/common'
import { hasPermission } from '@/utils/permissionOperate'
import { mapState } from 'vuex'
import {
    createUser,
    updateUser,
    deleteById,
    getUserById,
    getRoleList,
    resetPassword,
    getList,
} from '@/api/platform/system/sysUser'
import ico_hide from '@/assets/images/ico_hide.png'
import ico_show from '@/assets/images/ico_show.png'

export default {
    components: { PcwpOrgTree, Pagination },
    watch: {},
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        }
    },
    data () {
        return {
            systemUserAdd: false,
            materialInfo: {
                userId: '',
                userName: '',
                password: '',
                realName: '',
                state: 1,
                roleIds: undefined,
                userMobile: '',
            },
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                orgId: null,
                keyword: '',
            },
            dataListSelections: [], //表格选中的数据
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            screenHeight: 0,
            formRules: {
                userName: [{
                    required: true,
                    message: '请输入用户名',
                    validator: this.publicFunc.formFunc.verifySpaces,
                    trigger: 'blur'
                }],
                password: [{
                    required: true,
                    message: '请输入密码',
                    validator: this.publicFunc.formFunc.verifySpaces,
                    trigger: 'blur'
                }],
                name: [{
                    required: true,
                    message: '请输入姓名',
                    validator: this.publicFunc.formFunc.verifySpaces,
                    trigger: 'blur'
                }],
                phone: [{
                    required: true,
                    message: '请输入手机号',
                    validator: this.publicFunc.formFunc.verifySpaces,
                    trigger: 'blur'
                }],
                roleIds: [{ required: true, message: '请选择角色', trigger: 'blur' }],
                state: [{ required: true, message: '请选择状态', trigger: 'blur' }],
            },
            userModal: {
                visible: false, title: '',
            },
            roleOptions: [],
            passwordForm: {
                userId: '',
                newPassword: '',
            },
            passwordRules: {
                newPassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
            },
            passwordModal: {
                visible: false, userId: '',
            },
            ico_hide,
            ico_show,
            hidePass: ico_hide,
            pwType: 'password',
            orgCodes: []
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        this.getTableData()
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.systemUserAdd = hasPermission('systemMange:userManage:add', this.userInfo.perms || [])
        this.orgId = this.userInfo.orgInfo.orgId
    },
    methods: {
    // 分类点击
        classNodeClick (data) {
            this.orgCodes = []
            if(data.number && data.orglayertypenumber !== '1') {
                this.orgCodes.push(data.number)
                if(data.children) {
                    treeToList(data.children, res => {
                        this.orgCodes.push(res.number)
                    })
                }
            }
            this.getTableData()
        },
        // 修改
        updateM () {
            this.$refs.formEdit.validate(async valid => {
                if (!valid) return
                this.materialInfo.classPath = ''
                this.clientPop('info', '你确定授权用户角色吗？', async () => {
                    updateUser(this.materialInfo).then(res => {
                        if (res.code !== 200) return
                        this.$message({ type: 'success', message: '用户授权成功' })
                        this.getTableData()
                        this.userModal.title = ''
                        this.userModal.visible = false
                    })

                })
            })
        },
        onSave (again) {
            this.$refs.formEdit.validate(async valid => {
                if (!valid) return
                createUser(this.materialInfo).then(res => {
                    if (res.code !== 200) return
                    this.$message({ type: 'success', message: '用户新增成功' })
                    this.getTableData()
                    this.userModal.title = ''
                    this.userModal.visible = false
                    if(again) {
                        console.log('11111')
                    }
                })
            })
        },
        // 取消
        closeViewListM () {
            this.materialInfo.userName = ''
            this.materialInfo.realName = ''
            this.materialInfo.password = ''
            this.materialInfo.state = 1
            this.materialInfo.userMobile = ''
            this.materialInfo.role = undefined
            this.userModal.title = ''
            this.userModal.visible = false
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        operate_td (title, row) {
            if (title == '新增') {
                this.materialInfo.userName = ''
                this.materialInfo.realName = ''
                this.materialInfo.password = ''
                this.materialInfo.state = 1
                this.materialInfo.userMobile = ''
                this.materialInfo.role = undefined
                this.userModal.title = '新增用户'
                this.userModal.visible = true
                this.$nextTick(() => {
                    this.$refs.formEdit.clearValidate()
                })
            } else if (title == '编辑') {
                this.materialInfo.userId = row.userId || ''
                this.materialInfo.userName = row.userName || ''
                this.materialInfo.password = row.password || ''
                this.materialInfo.realName = row.realName || ''
                this.materialInfo.role = row.role
                this.materialInfo.state = row.state || 1
                this.materialInfo.userMobile = row.userMobile || ''
                this.userModal.title = '编辑用户'
                this.userModal.visible = true
                this.$nextTick(() => {
                    this.$refs.formEdit.clearValidate()
                })
            } else if (title == '删除') {
                this.clientPop('info', '你确定删除该用户吗？', async () => {
                    deleteById({ id: row.userId }).then(res => {
                        if (res.code !== 200) return
                        this.$message({ type: 'success', message: '用户删除成功' })
                        this.getTableData()
                    })
                })
            } else if (title == '重置密码') {
                this.passwordForm.userId = row.userId
                this.passwordForm.newPassword = ''
                this.passwordModal.visible = true
            }else if(title == '授权') {
                getRoleList().then(res=>{
                    this.roleOptions = res
                    getUserById({ userId: row.userId }).then(res=>{
                        this.materialInfo = res
                        this.userModal.title = '授权'
                        this.userModal.visible = true
                    })
                })
                this.$nextTick(() => {
                    this.$refs.formEdit.clearValidate()
                })
            }
        },
        // 用户密码重置-确认
        confirm_password () {
            this.$refs.passwordRef.validate(async valid => {
                if (!valid) return
                resetPassword(this.passwordForm).then(res=>{
                    if(res.code == 200) {
                        this.$message({ type: 'success', message: '用户密码重置成功' })
                        this.passwordModal.visible = false
                    }
                })
            })
        },
        // 获取表格数据
        getTableData () {
            let params = {
                pageIndex: this.paginationInfo.currentPage,
                pageSize: this.paginationInfo.pageSize,
                keyWord: this.init.keyword.trim()
            }
            if(this.orgCodes.length > 0) {
                params.orgCodes = this.orgCodes
            }
            this.tableLoading = true
            getList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage

            }).finally(() => this.tableLoading = false)
        },
        // 消息提示
        message (res) {
            if (res.code !== 200) return
            this.$message({ message: res.message, type: 'success' })
        },
        getScreenInfo () {
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        },
        // 是否显示密码
        toggleShowPass () {
            this.hidePass = this.hidePass === this.ico_hide ? this.ico_show : this.ico_hide
            this.pwType = this.pwType === 'text' ? 'password' : 'text'
        },
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type=‘number’] {
  -moz-appearance: textfield !important;
}

.el-dialog__body {
  margin: 220px;
}

.base-page .left {
  min-width: 400px;
  height: 100%;
  padding: 0;
  overflow: scroll;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .el-dropdown {
  min-width: 75px;
  margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.e-table {
  min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-dialog .el-dialog__body {
  margin: 0;
  height: auto;
}

/deep/ .e-form .buttons {
  position: relative;
  background: transparent;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}
</style>
