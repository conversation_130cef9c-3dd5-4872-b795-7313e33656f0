import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getShopStateByUserId = params => {
    return httpGet({
        url: '/materialMall/userCenter/user/getShopStateByUserId',
        params,
    })
}
const isTwoSupper = params => {
    return httpGet({
        url: '/materialMall/shopSupplierRele/shopManage/isTwoSupper',
        params,
    })
}
const getIsShowFee = params => {
    return httpGet({
        url: '/materialMall/userCenter/shop/getIsShowFee',
        params,
    })
}
// 获取名称
const getUserNameInfo = params => {
    return httpGet({
        url: '/materialMall/userCenter/user/getUserNameInfo',
        params,
    })
}
export {
    getShopStateByUserId,
    getUserNameInfo,
    getIsShowFee,
    isTwoSupper,
}