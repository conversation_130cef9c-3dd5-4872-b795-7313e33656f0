<template>
    <main>
        <div class="list-title dfa">我的发票</div>
        <div class="p20">
            <div class="tabs mb20 dfb">
                <div class="tab df">
                    <div class="search df">
                        <div class="box dfa">
                            <img src="@/assets/images/ico_search.png" alt="">
                            <input v-model="keywords" type="text" placeholder="商品名称/订单号">
                        </div>
                        <button @click="onSearch">搜索</button>
                    </div>
                </div>
            </div>
            <div class="list-bar dfa mb20">
                <div>订单详情</div>
                <div>发票类型</div>
                <div>状态</div>
                <div>操作</div>
            </div>
            <!--            列表-->
            <div class="list">
                <div class="list-item mb20" v-for="item in invoiceLists" :key="item.orderNum">
                    <div class="item-top dfa">
                        <span>{{ item.flishTime }}</span>
                        <span>订单号：{{ item.orderSn }}</span>
                    </div>
                    <div class="item-body df p20">
                        <img
                            :src="item.productMinImg ? imgUrlPrefixAdd + item.productMinImg : require('@/assets/images/img/queshen5.png')"
                            alt="">
                        <div class="title">{{ item.productName }}</div>
                        <div class="quantity"></div>
                        <div>
                            <div v-if="item.invoiceType==0" class="type">增值税专用发票</div>
                            <div v-else-if="item.invoiceType==1" class="type">普通发票</div>
                            <div v-else class="type">{{ item.totalAmount }}</div>
                        </div>
                        <div lass="quantity">
                            <div class="status" v-show="item.invoiceState==0" style="color: #71B247 ">已申请</div>
                            <div class="status" v-show="item.invoiceState==1" style="color: #5d9eff ">已开票</div>
                        </div>
                        <div class="action df">
                            <div class="pointer" @click="handleViewDetail(item)">发票详情</div>
                        </div>
                    </div>
                </div>
            </div>
            <pagination
                :pageSize="pages.pageSize"
                :total="pages.totalCount"
                :currentPage="pages.currPage"
                :totalPage="pages.totalPage"
                :destination="pages.destination"
                @currentChange="currentChange"
            ></pagination>
        </div>
    </main>
</template>
<script>
import pagination from '@/pages/frontStage/components/pagination.vue'
// import  { InvoiceList } from '@/api/frontStage/invoice'
import { InvoiceList } from '@/api/frontStage/invoice'

export default {
    components: { pagination },
    data () {
        return {
            state: null, //订单状态 1:待付款 2:待发货 3:待收货 4:待评价 5:已完成 6:已关闭 7结算中
            /*list: [
                {
                    time: '2022-11-08  14:30:00',
                    orderNum: '123456784',
                    pictureUrl: '',
                    title: '安徽省蚌埠市出租SY135履带式挖掘机',
                    quantity: 1,
                    type: '2399.00',
                    status: 0,
                }, {
                    time: '2022-11-08  14:30:00',
                    orderNum: '123456789',
                    pictureUrl: '',
                    title: '安徽省蚌埠市出租SY135履带式挖掘机',
                    quantity: 1,
                    type: '电子普通发票',
                    status: 1,
                },
            ],*/
            invoiceLists: [],
            keywords: '',
            pages: {
                pageSize: 5,
                totalCount: 20,
                currPage: 1,
                totalPage: 0,
                destination: 2
            }
        }
    },
    created () {
        // this.getInvoiceList()
    },
    mounted () {
    },
    methods: {

        onSearch () {
            // this.getInvoiceList()
        },
        // 获取发票列表
        getInvoiceList () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
            }
            if (this.state != null) {
                params.state = this.state
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            InvoiceList(params).then(res => {
                this.invoiceLists = res.list
                this.pages = res

            })

        },
        handleViewDetail (orderSn) {
            this.$router.push(
                {
                    path: '/user/myInvoice/detail',
                    name: 'myInvoiceDetail',
                    params: {
                        row: orderSn
                    }
                })
        },
        // 跳转申请发票页
        handleApply (item) {
            // 如果是公司类型的用户
            this.$router.push(
                {
                    path: '/user/myInvoice/invoice/company',
                    name: 'companyApply',
                    params: {
                        row: item
                    }
                })
            // 如果是个人类型的用户
            // this.$router.push({ path: '/user/myInvoice/invoice/individual', query: { id, } })
        },
        // eslint-disable-next-line
        currentChange (page) {
        },
    },
}
</script>
<style scoped lang="scss">
main {
    border: 1px solid rgba(229, 229, 229, 1);
}
.list-title {
    border-bottom: 1px solid rgba(229, 229, 229, 1);
}
.tabs {
    display: flex;
    justify-content: flex-end;

    .tab {

        font-size: 16px;
        color: rgba(102, 102, 102, 1);

        div {
            cursor: pointer;
        }

        .active {
            color: rgba(0, 0, 0, 1);

            &::after {
                content: '';
                display: block;
                width: 100%;
                height: 2px;
                margin-top: 4px;
                background-color: rgba(34, 111, 199, 1);
            }
        }
    }

    .search {
        .box {
            width: 268px;
            height: 26px;
            border: 1px solid rgba(229, 229, 229, 1);
            border-right: 0;

            img {
                width: 16px;
                height: 16px;
                margin: 0 4px 0 10px;
            }

            input {
                width: 230px;
            }
        }

        button {
            width: 52px;
            height: 26px;
            font-size: 14px;
            color: #fff;
            background-color: rgba(212, 48, 48, 1);
        }
    }
}

.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);
    position: relative;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.list-bar {
    height: 50px;
    padding: 0 20px;
    border: 1px solid rgba(230, 230, 230, 1);
    background-color: #FAFAFA;

    div:first-child {
        width: 700px;
    }

    div:nth-child(2) {
        width: 127px;
    }

    div:nth-child(3) {
        width: 121px;
    }
}

.list {
    min-height: 730px;

    .list-item {
        border: 1px solid rgba(230, 230, 230, 1);

        .item-top {
            height: 40px;
            padding: 0 20px;
            border-bottom: 1px solid rgba(230, 230, 230, 1);

            span {
                margin-right: 24px;
            }
        }

        .item-body {
            img {
                width: 80px;
                height: 80px;
                margin-right: 15px;
                object-fit: cover;
            }

            .title, .quantity, .type, .status, .pointer {
                padding-top: 8px;
            }

            .title {
                width: 240px;
                margin-right: 60px;
                color: #333;
            }

            .quantity {
                width: 281px;
            }

            .type {
                width: 100px;
                text-align: center;
            }

            .status {
                width: 126px;
                text-align: center;
            }

            .action {
                width: 126px;
                justify-content: center;

                .apply {
                    width: 100px;
                    height: 30px;
                    border: 1px solid #D43030;
                    color: #D43030;
                    text-align: center;
                    line-height: 30px;
                    user-select: none;
                    cursor: pointer;

                    &:active {
                        color: #fff;
                        background-color: #D43030;
                    }
                }

                .pointer {
                    color: #216EC6;
                }
            }
        }
    }
}
</style>