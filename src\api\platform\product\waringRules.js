import service from '@/utils/request'

const { httpPost, httpGet } = service

// 新增规则
const save = params => {
    return httpPost({
        url: '/materialMall/productWarningRules/create',
        params
    })
}
const getList = params => {
    return httpPost({
        url: '/materialMall/productWarningRules/listByEntity',
        params
    })
}
const dele = params => {
    return httpGet({
        url: '/materialMall/productWarningRules/delete',
        params
    })
}
const findById = params => {
    return httpGet({
        url: '/materialMall/productWarningRules/findById',
        params
    })
}
const deleteBatch = params => {
    return httpPost({
        url: '/materialMall/productWarningRules/deleteBatch',
        params
    })
}
const update = params => {
    return httpPost({
        url: '/materialMall/productWarningRules/update',
        params
    })
}

const updateStateBatch = params => {
    return httpPost({
        url: '/materialMall/productWarningRules/updateStateBatch',
        params,
    })
}

// 导出excel
export {
    save,
    getList,
    findById,
    dele,
    deleteBatch,
    updateStateBatch,
    update,
}