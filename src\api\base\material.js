import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

// 获取物资计划限额设置列表
const getMaterialSetList = params => {
    return httpPost({
        url: '/material/planLimitSetting/listByEntity',
        params
    })
}
// 获取计划限额详细信息
const getMaterialSet = params => {
    return httpGet({
        url: '/material/planLimitSetting/findById',
        params
    })
}
// 新增计划限额
const addMaterialSet = params => {
    return httpPost({
        url: '/material/planLimitSetting/create',
        params
    })
}
// 修改计划限额
const modifyMaterialSet = params => {
    return httpPost({
        url: '/material/planLimitSetting/update',
        params
    })
}
// 删除计划限额
const delMaterialSet = params => {
    return httpGet({
        url: '/material/planLimitSetting/delete',
        params
    })
}

// 根据条件分页查询乙方人员
const partBPersonLs = params => {
    return httpPost({
        url: '/material/personnelOfPartyB/listByEntity',
        params
    })
}
// 根据乙方人员id查询查询乙方人员基本信息
const partBPersonGetById = params => {
    return httpGet({
        url: '/material/personnelOfPartyB/findById',
        params
    })
}
// 根据乙方人员id获取乙方人员明细信息
const partBPersonGetLs = params => {
    return httpGet({
        url: '/material/personnelOfPartyB/getListById',
        params
    })
}
// 新增乙方人员
const partBPersonAdd = params => {
    return httpPost({
        url: '/material/personnelOfPartyB/create',
        params
    })
}
// 修改乙方人员
const partBPersonModify = params => {
    return httpPost({
        url: '/material/personnelOfPartyB/update',
        params
    })
}
// 根据乙方人员id删除乙方人员
const partBPersonDel = params => {
    return httpGet({
        url: '/material/personnelOfPartyB/delete',
        params
    })
}
// 新增乙方人员明细
const partBListAdd = params => {
    return httpPost({
        url: '/material/personnelOfPartyB/createLsit',
        params
    })
}
// 修改乙方人员明细
const partBListModify = params => {
    return httpPost({
        url: '/material/personnelOfPartyB/updateList',
        params
    })
}

// 获取单位转换列表
const getUnitConverLs = params => {
    return httpPost({
        url: '/material/unitConversion/listByEntity',
        params
    })
}
// 获取单位转换详情
const getUnitConverDetail = params => {
    return httpGet({
        url: '/material/unitConversion/findById',
        params
    })
}
// 新增单位转换
const addUnitConver = params => {
    return httpPost({
        url: '/material/unitConversion/create',
        params
    })
}
// 编辑单位转换
const modifyUnitConver = params => {
    return httpPost({
        url: '/material/unitConversion/update',
        params
    })
}
// 删除单位转换
const delUnitConver = params => {
    return httpGet({
        url: '/material/unitConversion/delete',
        params
    })
}

// 获取物资类别树
const getMaterialClass = params => {
    return httpPost({
        url: '/material/categoryLibrary/queryListByParentClassId',
        params
    })
}
// 获取物资类别详情
const getMaterialById = params => {
    return httpGet({
        url: '/material/categoryLibrary/findById',
        params
    })
}
// 新增物资类别
const addMaterialClass = params => {
    return httpPost({
        url: '/material/categoryLibrary/create',
        params
    })
}
// 修改物资类别
const modifyMaterialClass = params => {
    return httpPost({
        url: '/material/categoryLibrary/update',
        params
    })
}
// 删除物资类别
const delMaterialClass = params => {
    return httpGet({
        url: '/material/categoryLibrary/delete',
        params
    })
}
// 获取物资列表
const getMaterialLs = params => {
    return httpPost({
        url: '/material/baseLibrary/listByEntity',
        params
    })
}
// 获取物资明细
const getMaterialDetail = params => {
    return httpGet({
        url: '/material/baseLibrary/findById',
        params
    })
}
// 新增物资
const addMaterial = params => {
    return httpPost({
        url: '/material/baseLibrary/create',
        params
    })
}
// 修改物资
const modifyMaterial = params => {
    return httpPost({
        url: '/material/baseLibrary/update',
        params
    })
}
// 删除物资
const delMaterial = params => {
    return httpGet({
        url: '/material/baseLibrary/delete',
        params
    })
}
// 保存或更新权限机构信息
const saveOrUpdatePermission = params => {
    return httpPost({
        url: '/material/categoryLibrary/saveOrUpdatePermission',
        params
    })
}
// 获取权限机构信息
const getOrgClassId = params => {
    return httpGet({
        url: '/material/categoryLibrary/listPermissionOrgByClassId',
        params
    })
}
// 获取本机构及其以下机构
const getChildrenOrg = params => {
    return httpGet({
        url: '/hr/org/getChildrenOrg',
        params
    })
}
//获取全部可用类别
const getMaterialData = params => {
    return httpGet({
        url: '/material/categoryLibrary/listAllClass',
        params
    })
}
//保存物资类别权限设置
const saveOrupdate = params => {
    return httpPost({
        url: '/material/categoryPermissionSetting/saveOrupdate',
        params
    })
}

export {
    getMaterialSetList,
    getMaterialSet,
    addMaterialSet,
    modifyMaterialSet,
    delMaterialSet,
    partBPersonLs,
    partBPersonGetById,
    partBPersonAdd,
    partBPersonModify,
    partBPersonDel,
    partBPersonGetLs,
    partBListAdd,
    getUnitConverLs,
    getUnitConverDetail,
    addUnitConver,
    modifyUnitConver,
    delUnitConver,
    partBListModify,
    getMaterialClass,
    getMaterialById,
    addMaterialClass,
    modifyMaterialClass,
    delMaterialClass,
    getMaterialLs,
    getMaterialDetail,
    addMaterial,
    modifyMaterial,
    delMaterial,
    saveOrUpdatePermission,
    getOrgClassId,
    getChildrenOrg,
    getMaterialData,
    saveOrupdate
}
