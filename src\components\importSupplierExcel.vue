<template>
    <el-upload style=" display: inline-block;margin-left: 10px" multiple action="fileUrl" :limit="limitNum" accept=".xls,.xlsx,csv" :file-list="fileList"
               :before-upload="beforeUpload" :on-exceed="onExceed" :show-file-list="showFileList"
               :http-request="uploadFile">
        <el-button type="primary">导入供应商excel</el-button>
    </el-upload>
</template>
<script>
import { uploadSupplierExcelFile } from '@/api/shopManage/product/deviceManage'
export default {
    data () {
        return {
            fileUrl: '', //上传文件的域名地址
            limitNum: 1, //文件上传个数限制
            fileList: [], //文件列表
            showFileList: false //文件列表是否显示,默认不显示
        }
    },
    methods: {
        // 文件上传类型
        // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet --- 后缀为 .xlsx
        // application/vnd.ms-excel --- 后缀为 .xls或.csv
        //文件上传之前的钩子,可以做一些验证或限制
        beforeUpload (file) {
            let regExp = file.name.replace(/.+\./, '')
            let lower = regExp.toLowerCase() //把大写字符串全部转为小写字符串
            let suffix = ['xls', 'xlsx']
            if (suffix.indexOf(lower) === -1) {
                return this.$message.warning('请上传后缀名为 xls、xlsx 的附件 !')
            }
            let isLt2M = file.size / 1024 / 1024 < 5
            if (!isLt2M) {
                return this.$message.error('请上传文件大小不能超过 5MB 的附件 !')
            }
        },
        //文件超出个数限制时的钩子
        onExceed (files, fileList) {
            return this.$message.warning(`只能选择${this.limitNum}个文件,当前共选择了${files.length + fileList.length}个`)
        },
        //覆盖默认的上传行为,可以自定义上传的实现
        uploadFile (item) {
            let formData = new FormData()
            formData.append('file', item.file)
            this.$parent.showLoadingM(1)
            uploadSupplierExcelFile(formData).then(res => {
                // this.$parent.showExcelResult(res)
                this.$parent.getTableData()
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = '供应商导入结果.xlsx'
                a.click()
                window.URL.revokeObjectURL(url)
                this.$message.success('导入成功，自动下载导入结果')
                this.fileList = []
                this.$parent.showLoadingM()
            }).catch(() => {
                this.$parent.getTableData()
                this.fileList = []
                this.$parent.showLoadingM()
            })
        }
    }
}
</script>

<style lang="scss" scoped>
</style>