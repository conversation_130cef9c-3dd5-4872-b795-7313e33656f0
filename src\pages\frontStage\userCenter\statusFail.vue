<template>
    <div>
        <div class="notVerified df" v-if="shopErrorShow">
            <!--        <div class="boxTop">-->
            <!--            <div class="title center">{{caption}}</div>-->
            <!--        </div>-->
            <div class="boxBottom">
                <div class="icon center"></div>
                <div class="msg1">店铺审核失败</div>
                <div class="msg2">失败原因：【{{ shopInfo.failReason }}】</div>
                <button class="center" @click="directTo">重新审核</button>
            </div>
        </div>
        <el-dialog  class="front" :visible.sync="dialogVisible" :close-on-click-modal="true">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>内部用户开店</div>
                    </div>
                    <div class="dialog-close" @click="dialogVisible = false"><img src="@/assets/images/close.png"
                                                                                  alt=""/></div>
                </div>
                <div></div>
            </div>
            <div class="dialog-body center">
                <div class="company-info">
                    <div><span>企业名称：</span><span>{{ userInfo.orgName }}</span></div>
                </div>
                <el-form :model="form" ref="form" :rules="rules" label-position="top" :inline="false" size="normal">
                    <div class="row dfb">
                        <div class="col">
                            <el-form-item label="店铺名称：" prop="shopName">
                                <el-input clearable v-model="form.shopName" placeholder="请输入店铺名称"></el-input>
                            </el-form-item>
                        </div>
                        <div class="col">
                            <el-form-item label="联系人：" prop="contact">
                                <el-input clearable v-model="form.contact" placeholder="请输入联系人"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <el-form-item label="联系电话：" prop="tel">
                                <el-input clearable v-model="form.tel" placeholder="请输入联系电话"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <el-form-item label="详细地址：" prop="detailedAddress">
                            <el-input clearable type="textarea" :auto-resize="false" v-model="form.detailedAddress"
                                      placeholder="请输入详细地址"></el-input>
                        </el-form-item>
                    </div>
                    <div class="btn center" @click="handleSubmit">提交</div>
                </el-form>
            </div>
        </el-dialog>
    </div>

</template>

<script>

import { getRestartOpenShopInfo } from '@/api/frontStage/shop'
import { mapState } from 'vuex'
import { createShopInside } from '@/api/frontStage/userCenter'

export default {
    name: 'statusFail',
    data () {
        return {
            rules: {
                shopName: [
                    { required: true, message: '请输入店铺名称', trigger: 'blur' },
                    { min: 1, max: 100, message: '超出范围', trigger: 'blur' }
                ],
                contact: [
                    { required: true, message: '请输入联系人', trigger: 'blur' },
                    { min: 1, max: 20, message: '超出范围', trigger: 'blur' }
                ],
                tel: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailedAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
            },
            shopErrorShow: false,
            form: {
                shopName: '',
                contact: '',
                tel: '',
                detailedAddress: '',
            },
            dialogVisible: false,
            // caption: '店铺审核失败',
            shopInfo: {},
        }
    },
    created () {
        if(this.userInfo.auditStatus == 3) {
            this.shopErrorShow = true
        }else {
            return
        }
        this.getShopInfo()
    },
    computed: {
        ...mapState(['userInfo'])
    },
    methods: {
        // 内部开店
        createShopInsideM () {
            createShopInside(this.form).then(res => {
                if (res.code == 200) {
                    this.$message({
                        message: '开店成功等待审核！',
                        type: 'success'
                    })
                    setTimeout(function () {
                        window.open('/index')
                    }, 1000)
                }
            })
        },
        // 提交
        handleSubmit () {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    this.createShopInsideM()
                }
            })
        },
        getShopInfo () {
            this.shopId = this.$route.query.shopId
            getRestartOpenShopInfo({ shopId: this.shopId }).then(res => {
                this.shopInfo = res
            })
        },
        directTo () {
            if(this.userInfo.isInterior == 1) {
                this.form.shopId = this.shopInfo.shopId
                this.form.shopName = this.shopInfo.shopName
                this.form.contact = this.shopInfo.linkMan
                this.form.tel = this.shopInfo.contactNumber
                this.form.detailedAddress = this.shopInfo.detailedAddress
                this.dialogVisible = true
            }else {
                this.$router.push({
                    path: '/mFront/becomeSeller',
                    name: 'becomeSeller',
                    params: {
                        row: {
                            shopInfo: this.shopInfo
                        }
                    }
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
$font_m: 'SourceHanSansCN-Medium';
/deep/ .el-dialog {
    width: 800px !important;
    height: 600px !important;
    margin-top: 161px !important;

    .dialog-body {
        width: 630px;
        padding: 50px 0 10px;

        .company-info {
            height: 45px;
            margin-bottom: 30px;
            padding: 15px;
            background: #F7F7F7;

            div {
                font-size: 14px;

                span:first-child {
                    color: #999;
                }

                span:last-child {
                    color: #226FC7;
                }
            }
        }

        .row {
            margin-bottom: 30px;

            .col {
                width: 300px;
            }

            .el-input__inner {
                height: 35px;
                border-radius: 0;
            }

            .el-textarea__inner {
                height: 70px !important;
                padding: 11px 10px;
                border-radius: 0;
                resize: none;
            }
        }

        .el-form-item {
            margin-bottom: 0;
        }

        .el-form-item__label {
            height: 24px;
            line-height: 14px;
            color: #999;
        }

        .btn {
            width: 80px;
            height: 40px;
            line-height: 40px;
            font-size: 16px;
            text-align: center;
            color: #fff;
            background-color: #216EC6;
            cursor: pointer;
            user-select: none;
        }
    }
}
.dialog-header {
    height: 41px;
    font-family: $font_m;
    font-size: 20px;
    font-weight: 500;
    color: #333333;

    .dialog-header-top {
        height: 20px;
        margin-bottom: 20px;
        font-weight: 500;

        .dialog-title > div:first-child {
            height: 20px;
            width: 3px;
            margin-right: 9px;
            background: #216ec6;
        }

        .dialog-close {
            cursor: pointer;
        }
    }

    & > div:last-child {
        width: 100%;
        height: 1px;
        background: #d8d8d8;
    }
}
// 店铺审核失败
.notVerified {
    padding-bottom: 50px;
    flex-direction: column;
}
.boxTop {
    height: 87px;
    border-bottom: 1px solid #D9D9D9;
    .title {
        width: 200px;
        height: 100%;
        font-size: 26px;
        font-weight: 500;
        line-height: 87px;
        text-align: center;
        border-bottom: 4px solid #216EC6;
        color: #333;
        user-select: none;
    }
}
.boxBottom {
    text-align: center;
    flex-grow: 1;
    .icon {
        width: 100px;
        height: 100px;
        margin-top: 120px;
        background: url(../../../assets/images/userCenter/zc_shibai.png);
    }
    .msg1 {
        margin: 30px 0 15px 0;
        font-size: 22px;
        color: #333;
        text-align: center;
        font-weight: 400;
    }
    .msg2 {
        margin-bottom: 72px;
        font-size: 16px;
        text-align: center;
        color: #999;
    }
    button {
        width: 150px;
        height: 50px;
        font-size: 22px;
        color: #fff;
        background-color: #216EC6;
    }
}
</style>