<template>
    <span v-if="Object.keys(auditState).length !== 0">
        <el-button
            type="primary"
            class="btn-blue"
            @click="submit"
            v-if="auditState.canModify"
            size="small"
            v-debounce='3000'
            >保存</el-button
        >
        <el-button
            type="primary"
            class="btn-blue"
            @click="submitCommit"
            v-if="auditState.canModify"
            size="small"
            v-debounce='3000'
            >保存并提交</el-button
        >
        <el-button
            type="primary"
            class="btn-blue"
            @click="audit"
            v-if="auditState.canAudit"
            size="small"
            v-debounce='3000'
            >审核</el-button
        >
        <el-button
            type="primary"
            class="btn-delete"
            v-if="auditState.canUndoAudit"
            @click="undoAudit"
            size="small"
            v-debounce='3000'
            >撤回</el-button
        >
        <el-button
            type="primary"
            class="btn-delete"
            @click="nullify"
            v-if="isNullify"
            size="small"
            v-debounce='3000'
            >作废</el-button
        >
        <el-button
            slot="reference"
            type="primary"
            class="btn-delete"
            @click="deleteData"
            v-if="(state === 0 || state === '0' || financialSharingList === 0) && auditState.canModify && canDelete"
            size="small"
            v-debounce='3000'
            >删除</el-button
        >
        <el-button
            type="primary"
            class="btn-blue"
            @click="print"
            v-if="isPrint"
            size="small"
            v-debounce='3000'
            >打印</el-button
        >
        <!-- 选择打印 -->
        <el-dropdown
            size="small"
            type="primary"
            split-button
            @command="multipleEvent"
            v-if="multiple"
            >
            打印
                <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="command in commandList" :command="command.key" :key="command.key">{{command.value}}</el-dropdown-item>
                </el-dropdown-menu>
        </el-dropdown>
    </span>
</template>

<script>
import{ mapState } from 'vuex'
// import { debounce } from '@/utils/common.js'
export default {
    props: {
        auditState: {
            type: Object,
            default: function () {
                return {}
            }
        },
        state: {
            type: [Number, String],
            default: 0
        },
        financialSharingList: {
            //传输财务共享状态
            type: Number,
            default: -1
        },
        canDelete: {
            type: Boolean,
            default: true
        },
        routeTo: {
            type: Function,
            default: function () {}
        },
        founderId: {
            type: String,
        },
        isPrint: {
            type: Boolean,
            default: false
        },
        //是否展示选择打印
        multiple: {
            type: Boolean,
            default: false
        },
        //选择打印需要的数组
        //key:用于返回选中的数据id
        //value:用于展示
        commandList: {
            type: Array,
            default: ()=>[]
        }
    },
    data () {
        return {}
    },
    watch: {
        'state' (news, olds) {
            console.log(news)
            console.log(olds)
        }
    },
    computed: {
        ...mapState({
            userId: state => state.userInfo.userId,
        }),
        //判断是否可以作废
        isNullify () {
            if(this.founderId === this.userId && (this.state === 1 || this.state === '1')) {
                return true
            }else{
                return false
            }
        },
    },
    methods: {
        audit () {
            this.$emit('exas', 'audit', this.routeTo)
        },
        undoAudit () {
            this.$emit('exas', 'undoAudit', this.routeTo)
        },
        submitCommit () {
            this.$emit('exas', 'submitCommit', this.routeTo)
        },
        submit () {
            this.$emit('exas', 'submit', this.routeTo)
        },
        nullify () {
            this.$emit('exas', 'nullify', this.routeTo)
        },
        deleteData () {
            this.$emit('exas', 'deleteData', this.routeTo)
        },
        print () {
            this.$emit('exas', 'print', this.routeTo)
        },
        multipleEvent (command) {
            this.$emit('exas', { type: 'multiple', value: command }, this.routeTo)
        }
    }
}
</script>

<style lang="scss" scoped>

/deep/ .el-dropdown .el-button-group .el-button {
    margin-right: 0px;
    border: 0;
}
/deep/ .el-dropdown .el-button-group :nth-child(2) {
    margin-right: 5px;
}
/deep/ .el-dropdown .el-dropdown__caret-button::before {
    background-color: #2e61d7;
}

</style>
