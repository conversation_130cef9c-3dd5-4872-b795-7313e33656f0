import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getMaterialHomeInfo = params => {
    return httpPost({
        url: '/materialMall/w/homePage/getMaterialHomeInfo',
        params,
    })
}
const updateIsShowBid = params => {
    return httpGet({
        url: '/materialMall/userCenter/user/updateIsShowBid',
        params,
    })
}
const getRemindMessage = params => {
    return httpGet({
        url: '/materialMall/stationMessageReceive/getRemindMessage',
        params,
    })
}
export {
    getMaterialHomeInfo,
    updateIsShowBid,
    getRemindMessage
}