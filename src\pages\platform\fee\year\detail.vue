<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="服务年费记录详情" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="服务年费缴费记录" name="auditRecords" :disabled="clickTabFlag"/>
                <div id="tabs-content">
                    <div id="baseInfo" class="con">
                        <div class="tabs-title" id="baseInfo">服务年费记录详情</div>
                        <el-form :model="formDate" label-width="200px" ref="formDateRef" :disabled="false"
                                 class="demo-ruleForm">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="平台年费编号：" prop="platformYearFeeNu">
                                        <span>{{ formDate.platformYearFeeNu }}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="企业名称：" prop="enterpriseName">
                                        <span>{{ formDate.enterpriseName }}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="店铺名称：" prop="shopName">
                                        <span>{{ formDate.shopName }}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="服务是否过期：" prop="outTime">
                                        <el-tag type="danger" v-if="formDate.outTime===0">是</el-tag>
                                        <el-tag type="success" v-if="formDate.outTime === 1">否</el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="服务到期时间：" prop="serveEndTime">
                                        <span>{{ formDate.serveEndTime }}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="服务类型：" prop="serveType">
                                        <span v-show="formDate.serveType===1">店铺年度服务费</span>
                                        <span v-show="formDate.serveType===2">电子招标年度服务费</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="创建时间：" prop="gmtCreate">
                                        {{ formDate.gmtCreate }}
                                    </el-form-item>
                                </el-col>
                                <!--                                <el-col :span="12">-->
                                <!--                                    <el-form-item label="修改时间：" prop="gmtModified">-->
                                <!--                                        {{formDate.gmtModified}}-->
                                <!--                                    </el-form-item>-->
                                <!--                                </el-col>-->
                            </el-row>
                            <!--                            <el-row>-->
                            <!--                                <el-col :span="24">-->
                            <!--                                    <el-form-item label="备注：" prop="remarks">-->
                            <!--                                        <el-input :disabled="formDate.state == 1 || formDate.state == 2" style="width: 1000px;" type="textarea" :auto-resize="false" v-model="formDate.remarks"-->
                            <!--                                                  placeholder="请输入备注" maxlength="1000" show-word-limit></el-input>-->
                            <!--                                    </el-form-item>-->
                            <!--                                </el-col>-->
                            <!--                            </el-row>-->
                        </el-form>
                    </div>
                    <div id="baseInfoDtl" class="con">
                        <div class="tabs-title" id="baseInfoDtl">服务年费缴费记录</div>
                        <div class="e-table" style="background-color: #fff" v-loading="dtlTableListLoading">
                            <!--                            <div class="top" style="height: 50px; padding-left: 10px">-->
                            <!--                                <div class="left">-->
                            <!--                                    <div class="search_box" style="margin-left: 20px">-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <el-table
                                border
                                style="width: 100%"
                                max-height="342px"
                                ref="tableListRef"
                                :data="formDate.dtls"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="paymentRecordUn"  label="缴费记录编号" width="160">
                                    <template v-slot="scope">
                                        <span class="action" @click="handleView(scope.row)">{{ scope.row.paymentRecordUn }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="payAmount" label="缴费金额（元）" width="160"/>
                                <el-table-column prop="payType" label="缴费类型" width="160">
                                    <template v-slot="scope">
                                        <span v-show="scope.row.payType==1">线下</span>
                                        <span v-show="scope.row.payType==2">线上</span>
                                        <span v-show="scope.row.payType==3">其他</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="paymentDuration" label="缴费时长" width="260"/>
                                <el-table-column prop="paymentDurationType" label="缴费时长类型" width="160">
                                    <template v-slot="scope">
                                        <span v-show="scope.row.paymentDurationType==1">天</span>
                                        <span v-show="scope.row.paymentDurationType==2">周</span>
                                        <span v-show="scope.row.paymentDurationType==3">月</span>
                                        <span v-show="scope.row.paymentDurationType==4">年</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="auditOpenTime" label="审核时间" width="160"/>
                                <el-table-column prop="recordType" label="缴费记录类型" width="160">
                                    <template v-slot="scope">
                                        <span v-show="scope.row.recordType==1">店铺年度服务费</span>
                                        <span v-show="scope.row.recordType==2">电子招标年度服务费</span>`
                                    </template>
                                </el-table-column>
                                <el-table-column prop="state" label="状态">
                                    <template v-slot="scope">
                                        <el-tag type="info" v-show="scope.row.state==0">草稿</el-tag>
                                        <el-tag  v-show="scope.row.state==1">待审核</el-tag>
                                        <el-tag type="success" v-show="scope.row.state==2">审核通过</el-tag>
                                        <el-tag type="danger" v-show="scope.row.state==3">审核未通过</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="创建时间" width="160"/>
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
            <div class="footer">
                <div class="buttons">
                    <el-button @click="handleClose">返回</el-button>
                </div>
            </div>
        </div>
        <el-dialog v-dialogDrag  title="年费记录详情" :visible.sync="showTwoOrderDialog"  width="80%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div class="con">
                <div class="tabs-title" id="baseInfo">年费记录详情</div>
                <div style="width: 100%" class="form">
                    <el-form :model="formData2" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="缴费记录编号：">
                                    <span>{{ formData2.paymentRecordUn }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="店铺名称：">
                                    <span>{{ formData2.shopName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="企业名称：">
                                    <span>{{ formData2.enterpriseName }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="缴费类型：">
                                    <span v-show="formData2.payType==1">线下</span>
                                    <span v-show="formData2.payType==2">线上</span>
                                    <span v-show="formData2.payType==3">其他</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="缴费时长：">
                                    <span>{{ formData2.paymentDuration }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="缴费时长类型（单位）：">
                                    <span v-show="formData2.paymentDurationType==1">天</span>
                                    <span v-show="formData2.paymentDurationType==2">周</span>
                                    <span v-show="formData2.paymentDurationType==3">月</span>
                                    <span v-show="formData2.paymentDurationType==4">年</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="缴费金额（元）：">
                                    <span>{{ formData2.payAmount }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="缴费记录类型：">
                                    <span v-show="formData2.recordType==1">店铺年度服务费</span>
                                    <span v-show="formData2.recordType==2">电子招标年度服务费</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="审核时间：">
                                    <span>{{ formData2.auditOpenTime }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="状态：">
                                    <span v-show="formData2.state==0">草稿</span>
                                    <span v-show="formData2.state==1">待审核</span>
.                                    <span v-show="formData2.state==2">审核通过</span>
                                    <span v-show="formData2.state==3">审核未通过</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
            <div  class="con" >
                <div class="tabs-title" id="contractList">附件资料</div>
                <div class="e-table"  style="background-color: #fff">
                    <el-table
                        ref="tableRef"
                        border
                        style="width: 100%"
                        :data="formData2.files"
                        class="table"
                        :max-height="$store.state.tableHeight"
                    >
                        <el-table-column label="序号" type="index" width=""></el-table-column>
                        <el-table-column prop="name" label="附件名称" width=""></el-table-column>
                        <el-table-column prop="fileType" label="媒体类型" width=""></el-table-column>
                        <el-table-column prop="relevanceType" label="操作" width="130">
                            <template v-slot="scope">
                                <el-button type="primary" class="pointer" @click="handleDownload(scope.row)">下载</el-button>
                            </template>
                        </el-table-column>
<!--                      <el-table-column prop="relevanceType" label="操作" width="130">-->
<!--                            <template v-slot="scope">-->
<!--                                <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>-->
<!--                            </template>-->
<!--                        </el-table-column>-->
                        <!--                        <el-table-column prop="costPrice" label="成本价" width=""></el-table-column>-->
                    </el-table>
                </div>
                <!--分页-->
            </div>
            <div  class="con" >
                <div class="tabs-title" id="contractList2">审核历史</div>
                <div class="e-table"  style="background-color: #fff">
                    <el-table
                        ref="tableRef"
                        border
                        style="width: 100%"
                        :data="formData2.auditRecords"
                        class="table"
                        :max-height="$store.state.tableHeight"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="auditType" label="审核类型" width="200px">
                            <template v-slot="scope">
                               <span v-show="scope.row.auditType==1">提交审核</span>
                               <span v-show="scope.row.auditType==1">变更审核</span>
                               <span v-show="scope.row.auditType==6">竞价中标审核</span>
                               <span v-show="scope.row.auditType==7">作废审核</span>
                               <span v-show="scope.row.auditType==8">红字审核</span>
                               <span v-show="scope.row.auditType==9">开票审核</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="relevanceType" label="关联类型" >
                            <template v-slot="scope">
                                <span v-show="scope.row.auditType==1">月供计划</span>
                                <span v-show="scope.row.auditType==2">月供变更计划</span>
                                <span v-show="scope.row.auditType==3">竞价采购提交</span>
                                <span v-show="scope.row.auditType==4">竞价采购中标</span>
                                <span v-show="scope.row.auditType==5">对账单</span>
                                <span v-show="scope.row.auditType==6">二级对账单</span>
                                <span v-show="scope.row.auditType==7">大宗临购清单</span>
                                <span v-show="scope.row.auditType==8">发票</span>
                                <span v-show="scope.row.auditType==9">大宗临购供应商拒绝审核</span>
                                <span v-show="scope.row.auditType==10">年度服务费缴费审核</span>
                                <span v-show="scope.row.auditType==11">交易服务费缴费审核</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="auditResult" label="审核结果" >
                        </el-table-column>
                    </el-table>
                </div>
                <!--分页-->
            </div>
            <el-button class="btn-blue" style="margin-top: 20px;margin-left: 50%" @click="showTwoOrderDialog = false">关闭</el-button>
        </el-dialog>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { mapState } from 'vuex'
import { previewFile } from '@/api/platform/common/file'
import {
    platformFindYearFeeBySn
} from '@/api/fee/feeApi'
// import {
//     platformFindYearFeeBySn, findBySn
// } from '@/api/fee/feeApi'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return dateStr.split(' ')[0]
        }
    },
    data () {
        return {
            dtlTableListLoading: false,
            formData2: {},
            showTwoOrderDialog: false,
            formDate: {},
            selectTableList: [],
            tableData3: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            formLoading: false,
            fileLoading: false,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    created () {
        this.getFormDtl()
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        async handleDownload (file) {
            this.fileLoading = true
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes], { type: 'application/pdf' })
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
            this.fileLoading = false
        },
        handleView (row) {
            this.$router.push({
                path: '/platform/fee/yearPayRecordDtl',
                name: 'platformFeeYearPayRecordDtl',
                query: {
                    sn: row.paymentRecordUn
                }
            })
        },
        getFormDtl () {
            this.formLoading = true
            platformFindYearFeeBySn({ sn: this.$route.query.sn }).then(res => {
                this.formDate = res
                this.formLoading = false
            }).finally(() => {
                this.formLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },

        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}

/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }

    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}

/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

/deep/ .selectDealDia {
    .el-dialog__body {
        margin-top: 0px;
    }
}

</style>