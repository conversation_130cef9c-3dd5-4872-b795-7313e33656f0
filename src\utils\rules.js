import { creditCode } from '@/utils/common.js'
//验证是否是整数
const isNum = (rule, value, callback) => {
    const age = /^[0-9]*$/ //数字
    if (!age.test(value)) {
        callback(new Error('请输入整数'))
    } else {
        callback()
    }
}
//验证是否全为大写字母或数字
const capitalsAndNum = (rule, value, callback) => {
    const age = /^[0-9]*$/ //数字
    const age1 = /^[A-Z]+$/ //大写字母
    if (age.test(value)) {
        callback()
    } else if (age1.test(value)) {
        callback()
    } else {
        callback(new Error('请输入全为大写字母或数字'))
    }
}
//验证是否是两位小数
const isTwoDecimal = (rule, value, callback) => {
    const age = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
    if (!age.test(value)) {
        callback(new Error('请保留两位小数'))
    } else {
        callback()
    }
}

//保留四位小数 —— Input
const isFourInput = event => {
    if (event.path[0].value) {
        let str = event.path[0].value
        let len1 = str.substr(0, 1)
        let len2 = str.substr(1, 1)
        //如果第一位是0，第二位不是点，就用数字把点替换掉
        if (str.length > 1 && len1 == 0 && len2 != '.') {
            str = str.substr(1, 1)
        }
        //第一位不能是.
        if (len1 == '.') {
            str = ''
        }
        if (len1 == '+') {
            str = ''
        }

        //限制只能输入一个小数点
        if (str.indexOf('.') != -1) {
            let str_ = str.substr(str.indexOf('.') + 1)
            if (str_.indexOf('.') != -1) {
                str = str.substr(0, str.indexOf('.') + str_.indexOf('.') + 1)
            }
        }
        //正则替换
        str = str.replace(/[^\d^.]+/g, '') // 保留数字和小数点
        str = str.replace(/^\D*([0-9]\d*\.?\d{0,4})?.*$/, '$1') // 小数点后只能输 2 位
        // 去掉小数点之后非零数字后面的零
        // 去掉小数点
        let data = cutZero(str)
        event.path[0].value = data
    }
}
//保留四位小数正数 —— Input
const isFourInputJust = event => {
    if (event.path[0].value) {
        let str = event.path[0].value
        let len1 = str.substr(0, 1)
        let len2 = str.substr(1, 1)
        //如果第一位是0，第二位不是点，就用数字把点替换掉
        if (str.length > 1 && len1 == 0 && len2 != '.') {
            str = str.substr(1, 1)
        }
        //第一位不能是.
        if (len1 == '.') {
            str = ''
        }
        if (len1 == '+') {
            str = ''
        }
        if (len1 == '-') {
            str = ''
        }
        //限制只能输入一个小数点
        if (str.indexOf('.') != -1) {
            let str_ = str.substr(str.indexOf('.') + 1)
            if (str_.indexOf('.') != -1) {
                str = str.substr(0, str.indexOf('.') + str_.indexOf('.') + 1)
            }
        }
        //正则替换
        str = str.replace(/[^\d^.]+/g, '') // 保留数字和小数点
        str = str.replace(/^\D*([0-9]\d*\.?\d{0,4})?.*$/, '$1') // 小数点后只能输 2 位
        // 去掉小数点之后非零数字后面的零
        // 去掉小数点
        let data = cutZero(str)
        event.path[0].value = data
    }
}
//保留两位小数正数 —— Input
const isTwoInput = event => {
    let str = event.path[0].value
    let len1 = str.substr(0, 1)
    let len2 = str.substr(1, 1)
    //如果第一位是0，第二位不是点，就用数字把点替换掉
    if (str.length > 1 && len1 == 0 && len2 != '.') {
        str = str.substr(1, 1)
    }
    //第一位不能是.
    if (len1 == '.') {
        str = ''
    }
    if (len1 == '+') {
        str = ''
    }
    if (len1 == '-') {
        str = ''
    }

    //限制只能输入一个小数点
    if (str.indexOf('.') != -1) {
        let str_ = str.substr(str.indexOf('.') + 1)
        if (str_.indexOf('.') != -1) {
            str = str.substr(0, str.indexOf('.') + str_.indexOf('.') + 1)
        }
    }
    //正则替换
    str = str.replace(/[^\d^.]+/g, '') // 保留数字和小数点
    str = str.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, '$1') // 小数点后只能输 2 位
    event.path[0].value = str
}
// 去掉小数点后面无效的零
function cutZero (old) {
    //拷贝一份 返回去掉零的新串
    let newstr = old
    //循环变量 小数部分长度
    var leng = old.length - old.indexOf('.') - 1
    //判断是否有效数
    if (old.indexOf('.') > -1) {
        //循环小数部分
        for (let i = leng; i > 0; i--) {
            //如果newstr末尾有0
            if (
                newstr.lastIndexOf('0') > -1 &&
                newstr.substr(newstr.length - 1, 1) == 0
            ) {
                var k = newstr.lastIndexOf('0')
                //如果小数点后只有一个0 去掉小数点
                if (newstr.charAt(k - 1) == '.') {
                    return newstr.substring(0, k - 1)
                } else {
                    //否则 去掉一个0
                    newstr = newstr.substring(0, k)
                }
            } else {
                //如果末尾没有0
                return newstr
            }
        }
    }
    return old
}

//整数 —— Input
const isNumInput = event => {
    let str = event.path[0].value
    let len1 = str.substr(0, 1)
    let len3 = str.lastIndexOf('-')
    //如果第一位是0，第二位不是点，就用数字把点替换掉
    if (str.length > 1 && len3 > 0) {
        str = str.substring(0, len3) + str.substring(len3 + 1)
    }
    //第一位不能是.
    if (len1 == '.') {
        str = ''
    }
    if (len1 == '+') {
        str = ''
    }
    str = str.replace(/[^\d^-]+/g, '') // 保留数字和-号
    event.path[0].value = str
}

//正整数 —— Input
const isPositiveIntInput = event => {
    let str = event.path[0].value
    let len1 = str.substr(0, 1)
    let len3 = str.lastIndexOf('-')
    //如果第一位是0，第二位不是点，就用数字把点替换掉
    if (str.length > 1 && len3 > 0) {
        str = str.substring(0, len3) + str.substring(len3 + 1)
    }
    //第一位不能是.
    if (len1 == '.') {
        str = ''
    }
    if (len1 == '+') {
        str = ''
    }
    if (len1 == '-') {
        str = ''
    }
    if (len1 == '0') {
        str = ''
    }
    str = str.replace(/[^\d]+/g, '') // 保留数字和-号
    event.path[0].value = str
}
// //验证是否是统一信用代码验证
// const creditCodeVerification = (rule, value, callback) => {
//     let creditCode = /^[0-9A-Z]+$/
//     if (!value) {
//         return callback(new Error('统一信用代码不能为空'))
//     } else if (!creditCode.test(value)) {
//         callback(
//             new Error(
//                 '请输入18位数字或者大写字母'
//             )
//         )
//     } else {
//         callback()
//     }
// }
//验证是否是统一信用代码验证
const creditCodeVerification = (rule, value, callback) => {
    if (creditCode(value)) {
        callback()
    } else {
        callback(new Error('统一信用代码验证不通过，请检查！'))
    }
}
//验证是否符合座机和手机
const isPhone = (rule, value, callback) => {
    let creditCode = /^1\d{10}$|^(0\d{2,3}-?|\(0\d{2,3}\))?[1-9]\d{4,7}(-\d{1,8})?$/
    if (!value) {
        return callback(new Error('联系电话不能为空'))
    } else if (!creditCode.test(value)) {
        callback(new Error('请输入正确的联系电话'))
    } else {
        callback()
    }
}
//验证是否是电子邮箱
const isEmail = (rule, value, callback) => {
    let creditCode = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
    if(value != '') {
        if (!creditCode.test(value)) {
            callback(new Error('请输入正确的电子邮箱'))
        } else {
            callback()
        }
    } else {
        callback()
    }
}
//验证是否是身份证号码
const isIdcard = (rule, value, callback) => {
    let creditCode =
        /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
    if (!value) {
        return callback(new Error('身份证不能为空'))
    } else if (!creditCode.test(value)) {
        callback(new Error('请输入18位并正确的身份证号码'))
    } else {
        callback()
    }
}
//验证是否是真实姓名
const isName = (rule, value, callback) => {
    let creditCode = /^[a-zA-Z\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/
    if (!value) {
        return callback(new Error('姓名不能为空'))
    } else if (!creditCode.test(value)) {
        callback(new Error('请输入正确的姓名'))
    } else {
        callback()
    }
}
//验证是否是邮编
const isPostCode = (rule, value, callback) => {
    let creditCode = /^[1-9]\d{5}$|^[0][5-7]\d{4}$/
    if(value != '') {
        if (!creditCode.test(value)) {
            return callback(new Error('请输入正确的邮编'))
        } else {
            callback()
        }
    }else {
        callback()
    }
}
//验证是否是传真
const isFax = (rule, value, callback) => {
    let creditCode = /^(\d{3,4})?[-]?\d{7,8}$/
    if(value != '') {
        if (!creditCode.test(value)) {
            return callback(new Error('请输入正确的传真'))
        } else {
            callback()
        }
    }else {
        callback()
    }
}
//验证本位币汇率是否不为零
const baseCurRate = (rule, value, callback) => {
    if (value == '0') {
        callback(new Error('本位币汇率不能为0'))
    } else {
        callback()
    }
}
export {
    isNum,
    capitalsAndNum,
    isTwoDecimal,
    isFourInput,
    isFourInputJust,
    isNumInput,
    isTwoInput,
    creditCodeVerification,
    isPhone,
    isEmail,
    isIdcard,
    isName,
    baseCurRate,
    isPositiveIntInput,
    isPostCode,
    isFax
}
