<template>
    <div class="main front">
        <div class="title mb20">竞价公示</div>
        <div class="list">
            <el-table
                v-loading="tableLoading"
                :data="tableData"
                :header-cell-style="{ height: '50px', fontSize: '16px', color: '#fff', backgroundColor: '#216ec6' }"
                :cell-style="{ fontSize: '14px' }"
            >
                <el-table-column label="竞价单编号" prop="biddingSn" align="center" width="200" />
                <el-table-column label="竞价单标题" prop="title" align="center" />
                <el-table-column label="商品类型" prop="productType" align="center" width="140">
                    <template v-slot="scope">
                        <span v-if="scope.row.productType == 0">低值易耗品</span>
                        <span v-if="scope.row.productType == 1">大宗临购</span>
                        <span v-if="scope.row.productType == 2">大宗临购清单</span>
                    </template>
                </el-table-column>
                <el-table-column label="竞价类型" prop="type" align="center" width="140">
                    <template v-slot="scope">
                        {{ [null, '公开竞价', '邀请竞价'][parseInt(scope.row.type)] }}
                    </template>
                </el-table-column>
                <el-table-column label="竞价截止时间" prop="endTime" align="center" width="180" >
                    <template v-slot="scope">
                       <span>{{ scope.row.endTime.split(' ')[0] }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="竞价状态" prop="biddingState" align="center" width="120">
                    <template v-slot="scope">
                        <el-tag type="success" v-if="scope.row.biddingState==1" >未开始</el-tag>
                        <el-tag type="success" v-if="scope.row.biddingState==2" >进行中 </el-tag>
                        <el-tag  type="danger" v-if="scope.row.biddingState==3" >已结束</el-tag>
                        <!--<el-tag :type="[2, 3].includes(scope.row.state) ? 'danger' : ''">-->
                        <!--    {{ [null, '未开始', '进行中', '已结束'][scope.row.biddingState] }}-->
                        <!--</el-tag>-->
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="180">
                    <template v-slot="scope">
                        <el-button class="pointer" @click="viewDetail(scope.row)">查看详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <pagination
            :currentPage="pagination.currPage"
            :pageSize="pagination.pageSize"
            :total="pagination.totalCount"
            :totalPage="pagination.totalPage"
            :destination="pagination.destination"
            :pagerCount="pagination.pageSize"
            @currentChange="currentChange"
            @sizeChange="sizeChange"
        />
    </div>
</template>
<script>
import pagination from '../../components/pagination.vue'
import { mapState } from 'vuex'
import { getBiddingPurchaseByPage } from '@/api/frontStage/biddingPurchase'
export default {
    name: 'BiddingDisplay',
    components: { pagination },
    data () {
        return {
            tableLoading: false,
            tableData: [],
            pagination: {
                currPage: 1, //当前页
                destination: null,
                pageSize: 10, // 显示数量
                totalNum: 0,
                totalPage: 0,
            },
        }
    },
    watch: {},
    computed: {
        ...mapState(['userInfo'])
    },
    methods: {
        // 获取列表
        getBidingPageListM () {
            let params = {
                page: this.pagination.currPage,
                limit: this.pagination.pageSize,
            }
            this.tableLoading = true
            getBiddingPurchaseByPage(params).then(res => {
                this.tableData = res.list
                this.pagination.currPage = res.currPage
                this.pagination.pageSize = res.pageSize
                this.pagination.totalNum = res.totalCount
                this.pagination.totalPage = res.totalPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        viewDetail ({ biddingSn }) {
            if(Object.keys(this.userInfo).length === 0) return this.$message({ message: '请先登录', type: 'error' })
            this.openWindowTab({ path: '/mFront/biddingDisplayDetail', query: { biddingSn: biddingSn } })
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getBidingPageListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getBidingPageListM()
        },
    },
    created () {
        this.getBidingPageListM()
    },
}
</script>

<style scoped>
.main {
    width: 1326px;
    margin: 0 auto;
    padding: 20px 0;
}
.title {
    margin-top: 40px;
    font-size: 22px;
}
.list {
    min-height: 650px;
    background-color: #fff;
    .pointer {
        color: #409EFF;
        background-color: #e0f0ff;
        border: 0;
        border-radius: 0;
    }
}
.tableHeader {
    font-size: 28px;
}
</style>