<template>
  <div class="e-form">
    <BillTop @cancel="handleClose"/>
    <div class="tabs warningTabs">
      <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
          <el-tab-pane label="退货信息" name="baseInfo" :disabled="clickTabFlag"></el-tab-pane>
        <el-tab-pane label="退货项信息" name="baseItemInfo" :disabled="clickTabFlag">
        </el-tab-pane>
<!--        <el-tab-pane label="订单商品" name="productInfo" :disabled="clickTabFlag">-->
<!--        </el-tab-pane>-->
        <div id="tabs-content">
          <!-- 基本信息 -->
          <div id="baseInfCon" class="con">
            <div class="tabs-title" id="baseInfo">退货信息</div>
            <div style="width: 100%" class="form">
              <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="订单号：">
                      <span>{{ formData.orderSn }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="退货编号：">
                      <span>{{ formData.orderReturnNo }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                  <el-row>
                      <el-col :span="12">
                          <el-form-item label="退货类型：">
                              <span v-show="formData.sourceType===1">大宗合同 </span>
                              <span v-show="formData.sourceType===2">零星采购计划</span>
                              <span v-show="formData.sourceType===6">大宗临采</span>
                          </el-form-item>
                      </el-col>

                      <el-col :span="12">
                          <el-form-item label="退货来源：">
                              <span v-show="formData.isOut===0">PCWP退货</span>
                              <span v-show="formData.isOut===1">商城退货</span>
                          </el-form-item>
                      </el-col>
                  </el-row>

                  <el-row >
                      <el-col :span="12">
                          <el-form-item label="供应商名称：">
                              <span>{{ formData.supplierName }}</span>
                          </el-form-item>
                      </el-col>
                      <el-col :span="12">
                          <el-form-item label="采购企业名称：">
                              <span>{{ formData.enterpriseName }}</span>
                          </el-form-item>
                      </el-col>
                  </el-row>

<!--                  <el-row >-->
<!--                      <el-col :span="12">-->
<!--                          <el-form-item label="不含税总金额：">-->
<!--                              <span>{{ formData.noRateAmount }}</span>-->
<!--                          </el-form-item>-->
<!--                      </el-col>-->
<!--                      <el-col :span="12">-->
<!--                          <el-form-item label="含税总金额：">-->
<!--                              <span>{{ formData.RateAmount }}</span>-->
<!--                          </el-form-item>-->
<!--                      </el-col>-->
<!--                  </el-row>-->

<!--                  <el-row v-show="formData.orderClass!=1">-->
<!--                      <el-col :span="12">-->
<!--                          <el-form-item label="二级供应商不含税总金额：">-->
<!--                              <span>{{ formData.otherNoRateAmount }}</span>-->
<!--                          </el-form-item>-->
<!--                      </el-col>-->
<!--                      <el-col :span="12">-->
<!--                          <el-form-item label="二级供应商含税总金额：">-->
<!--                              <span>{{ formData.otherRateAmount }}</span>-->
<!--                          </el-form-item>-->
<!--                      </el-col>-->
<!--                  </el-row>-->
<!--                <el-row>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="退货编号：">-->
<!--                      <span>{{ formData.orderReturnNo }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="收件人：">-->
<!--                      <span>{{ formData.receiverName }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="收件人手机号：">-->
<!--                      <span>{{ formData.receiverMobile }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="收件地址：">-->
<!--                      <span>{{ formData.receiverAddress }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="退货数量：">
                      <span>{{ formData.count }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="订单状态：">
                      <el-tag v-if="showTableState(formData)">{{ tableStateTitle }}</el-tag>
                      <el-tag v-else type="danger">已关闭</el-tag>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="完成时间：">
                      <span>{{ formData.flishTime }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="创建时间：">
                      <span>{{ formData.gmtCreate }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
<!--                <el-row>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="订单关闭类型：">-->
<!--                      <span>{{ formData.closeType }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                  <el-col :span="12">-->
<!--                    <el-form-item label="支付交易号：">-->
<!--                      <span>{{ formData.tradeNo }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row>-->
<!--                  <el-col :span="24">-->
<!--                    <el-form-item label="退货详情：">-->
<!--                      <span>{{ formData.remarks }}</span>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
              </el-form>
            </div>

              <!--楼层信息-->

          </div>
            <div id="baseItemInfo" class="con">
                <div class="tabs-title" id="baseItemInfo">退货项信息</div>
                <div class="e-table" style="background-color: #ffffff">
<!--                    <div class="top">-->
<!--                        <div class="btns-box">-->
<!--                            <el-button size="small" type="primary" class="btn-greenYellow" plain @click="addData">新增</el-button>-->
<!--                            <el-button size="small" type="primary" @click="changePublishState(1)" class="btn-greenYellow">批量启用</el-button>-->
<!--                            <el-button size="small" type="primary" @click="changePublishState(0)" class="btn-delete">批量停用</el-button>-->
<!--                            <el-button size="small" type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改排序值</el-button>-->
<!--                            <el-button size="small" type="danger" class="btn-delete" plain @click="deleteData">批量删除</el-button>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div>
<!--                        <div class="errorMsg" v-if="false">-->
<!--                            <span></span>-->
<!--                        </div>-->
                        <el-table  v-loading="isLoading" border style="width: 100%" :data="baseItemList" class="table" :max-height="$store.state.tableHeight"
                                  >
<!--                            <el-table-column type="selection" width="40"></el-table-column>-->
<!--                            <el-table-column label="序号" type="index" width="60"></el-table-column>-->
                            <el-table-column label="订单项" width="200" prop="orderItemId"></el-table-column>
                            <el-table-column label="物资编号" width="200" prop="productSn"></el-table-column>
                            <el-table-column label="物资名称" width="200" prop="productName"></el-table-column>
                            <el-table-column label="规格" width="" prop="skuName"></el-table-column>
                            <el-table-column label="退货数量" width="" prop="count"></el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
          <!-- 订单商品-->
<!--          <div id="productInfo" class="con">-->
<!--            <div class="tabs-title" id="contractList">订单商品</div>-->
<!--            <div class="e-table"  style="background-color: #fff">-->
<!--              <div class="top" style="height: 50px; padding-left: 10px">-->
<!--                <div class="left">-->
<!--                  <el-input type="text" @blur="getTableData" placeholder="输入搜索关键字" v-model="keywords">-->
<!--                    <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData" />-->
<!--                  </el-input>-->
<!--                </div>-->
<!--              </div>-->
<!--              <el-table ref="tableRef"-->
<!--                        border-->
<!--                        style="width: 100%"-->
<!--                        :data="tableData"-->
<!--                        class="table"-->
<!--                        :max-height="$store.state.tableHeight"-->
<!--              >-->
<!--                <el-table-column label="序号" type="index" width="60"></el-table-column>-->
<!--                <el-table-column prop="skuName" label="规格" width="200px"></el-table-column>-->
<!--                <el-table-column prop="productImg" label="商品图片" width="130">-->
<!--                  <template slot-scope="scope">-->
<!--                    <el-image style="width: 90px; height: 60px" :src="scope.row.productImg"></el-image>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column prop="costPrice" label="成本价" width=""></el-table-column>-->
<!--                <el-table-column prop="productPrice" label="单价" width=""></el-table-column>-->
<!--                <el-table-column prop="buyCounts" label="购买数量" width=""></el-table-column>-->
<!--                <el-table-column prop="totalAmount" label="总金额" width=""></el-table-column>-->
<!--                &lt;!&ndash;                                <el-table-column prop="isComment" label="评论状态" width="">&ndash;&gt;-->
<!--                &lt;!&ndash;                                    <template slot-scope="scope">&ndash;&gt;-->
<!--                &lt;!&ndash;                                        <el-tag v-if="scope.row.isComment === 0">未评价</el-tag>&ndash;&gt;-->
<!--                &lt;!&ndash;                                        <el-tag v-if="scope.row.isComment === 1" type="success">已评价</el-tag>&ndash;&gt;-->
<!--                &lt;!&ndash;                                    </template>&ndash;&gt;-->
<!--                &lt;!&ndash;                                </el-table-column>&ndash;&gt;-->
<!--                &lt;!&ndash;                                <el-table-column prop="buyTime" label="购买时间" width="160"></el-table-column>&ndash;&gt;-->
<!--                &lt;!&ndash;                                <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>&ndash;&gt;-->
<!--                <el-table-column prop="remarks" label="备注" width="200"></el-table-column>-->
<!--              </el-table>-->
<!--            </div>-->
<!--            &lt;!&ndash;            分页&ndash;&gt;-->
<!--            <Pagination-->
<!--                v-show="tableData != null || tableData.length != 0"-->
<!--                :total="paginationInfo.total"-->
<!--                :pageSize.sync="paginationInfo.pageSize"-->
<!--                :currentPage.sync="paginationInfo.currentPage"-->
<!--                @currentChange="getTableData"-->
<!--                @sizeChange="getTableData"-->
<!--            />-->
<!--          </div>-->

        </div>
      </el-tabs>
    </div>
    <div class="buttons">
      <el-button @click="handleClose">返回</el-button>
    </div>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapMutations } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
// import Pagination from '@/components/pagination/pagination'
import { getOrderReturnDateById, getOrderReturItemList } from '@/api/platform/order/refund'
export default {

    data () {
        return {
            tableStateTitle: null, // 状态标题
            baseItemList: [],
            isLoading: false,
            orderReturnId: '', // 状态标题
            stateOptions: [
                {
                    value: null,
                    label: '全部'
                }, {
                    value: 1,
                    label: '已申请'
                }, {
                    value: 2,
                    label: '退货中'
                }, {
                    value: 3,
                    label: '退货成功'
                }, {
                    value: 4,
                    label: '退货失败'
                }],

            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    // components: {
    //     Pagination
    // },
    created () {
    // showLoading()
        this.orderReturnId = this.$route.params.row
        this.getOrderReturnDate()
        this.getOrderReturItemListM()
    // hideLoading()
    },
    mounted () {
    // 获取数据
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 70 + 'px !important'
        },
        // 填补底部空白，以使高度够滚动
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {

        getOrderReturItemListM () {
            this.isLoading = true
            let params = {
                orderReturnId: this.orderReturnId,
            }
            getOrderReturItemList(params).then(res=>{
                this.baseItemList = res.list
                this.isLoading = false
            })
            this.isLoading = false
        },
        getOrderReturnDate () {
            getOrderReturnDateById({ id: this.orderReturnId }).then(res=>{
                this.formData = res
            })
        },
        // 显示状态
        showTableState ( row ) {
            let stateValue  = row.state
            if(stateValue === 6) {
                return false
            }
            for (let i = 0; i < this.stateOptions.length; i++) {
                if(stateValue === this.stateOptions[i].value) {
                    this.tableStateTitle = this.stateOptions[i].label
                    return true
                }
            }
        },

        ...mapMutations(['setAuditParams']),
        ...mapMutations(['setSelectedInfo']),
        addData () {},
        deleteData () {},
        //取消
        handleClose () {
            this.$router.replace('/platform/order/refund')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
              document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.warningTabs {
    padding-top: 70px;
}

.e-table {
  min-height: auto;
  background: #fff;
}

.upload {
  margin: 20px auto;
  display: flex;
  justify-content: center;
  text-align: center;
}

.upload-demo {
  display: flex;
  justify-content: center;
  align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-tabs__content {
  // overflow: hidden;
  &::-webkit-scrollbar {width: 0;}
}
</style>
