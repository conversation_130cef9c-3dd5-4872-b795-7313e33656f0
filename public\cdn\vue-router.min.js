/*!
  * vue-router v3.2.0
  * (c) 2020 Evan You
  * @license MIT
  */
var t, e;t = this, e = function () {'use strict';function t (t) {return Object.prototype.toString.call(t).indexOf('Error') > -1}function e (t, e) {return e instanceof t || e && (e.name === t.name || e._name === t._name)}function r (t, e) {for(var r in e)t[r] = e[r];return t}var n = { name: 'RouterView', functional: !0, props: { name: { type: String, default: 'default' } }, render: function (t, e) {var n = e.props, i = e.children, a = e.parent, c = e.data;c.routerView = !0;for(var u = a.$createElement, s = n.name, p = a.$route, f = a._routerViewCache || (a._routerViewCache = {}), h = 0, l = !1;a && a._routerRoot !== a;) {var d = a.$vnode ? a.$vnode.data : {};d.routerView && h++, d.keepAlive && a._directInactive && a._inactive && (l = !0), a = a.$parent}if(c.routerViewDepth = h, l) {var v = f[s], y = v && v.component;return y ? (v.configProps && o(y, c, v.route, v.configProps), u(y, c, i)) : u()}var m = p.matched[h], g = m && m.components[s];if(!m || !g)return f[s] = null, u();f[s] = { component: g }, c.registerRouteInstance = function (t, e) {var r = m.instances[s];(e && r !== t || !e && r === t) && (m.instances[s] = e)}, (c.hook || (c.hook = {})).prepatch = function (t, e) {m.instances[s] = e.componentInstance}, c.hook.init = function (t) {t.data.keepAlive && t.componentInstance && t.componentInstance !== m.instances[s] && (m.instances[s] = t.componentInstance)};var w = m.props && m.props[s];return w && (r(f[s], { route: p, configProps: w }), o(g, c, p, w)), u(g, c, i)} };function o (t, e, n, o) {var i = e.props = function (t, e) {switch(typeof e) {case'undefined':return;case'object':return e;case'function':return e(t);case'boolean':return e ? t.params : void 0}}(n, o);if(i) {i = e.props = r({}, i);var a = e.attrs = e.attrs || {};for(var c in i)t.props && c in t.props || (a[c] = i[c], delete i[c])}}var i = /[!'()*]/g, a = function (t) {return'%' + t.charCodeAt(0).toString(16)}, c = /%2C/g, u = function (t) {return encodeURIComponent(t).replace(i, a).replace(c, ',')}, s = decodeURIComponent;function p (t) {var e = {};return(t = t.trim().replace(/^(\?|#|&)/, '')) ? (t.split('&').forEach(function (t) {var r = t.replace(/\+/g, ' ').split('='), n = s(r.shift()), o = r.length > 0 ? s(r.join('=')) : null;void 0 === e[n] ? e[n] = o : Array.isArray(e[n]) ? e[n].push(o) : e[n] = [e[n], o]}), e) : e}function f (t) {var e = t ? Object.keys(t).map(function (e) {var r = t[e];if(void 0 === r)return'';if(null === r)return u(e);if(Array.isArray(r)) {var n = [];return r.forEach(function (t) {void 0 !== t && (null === t ? n.push(u(e)) : n.push(u(e) + '=' + u(t)))}), n.join('&')}return u(e) + '=' + u(r)}).filter(function (t) {return t.length > 0}).join('&') : null;return e ? '?' + e : ''}var h = /\/?$/;function l (t, e, r, n) {var o = n && n.options.stringifyQuery, i = e.query || {};try{i = d(i)}catch(t) {}var a = { name: e.name || t && t.name, meta: t && t.meta || {}, path: e.path || '/', hash: e.hash || '', query: i, params: e.params || {}, fullPath: m(e, o), matched: t ? y(t) : [] };return r && (a.redirectedFrom = m(r, o)), Object.freeze(a)}function d (t) {if(Array.isArray(t))return t.map(d);if(t && 'object' == typeof t) {var e = {};for(var r in t)e[r] = d(t[r]);return e}return t}var v = l(null, { path: '/' });function y (t) {for(var e = [];t;)e.unshift(t), t = t.parent;return e}function m (t, e) {var r = t.path, n = t.query;void 0 === n && (n = {});var o = t.hash;return void 0 === o && (o = ''), (r || '/') + (e || f)(n) + o}function g (t, e) {return e === v ? t === e : !!e && (t.path && e.path ? t.path.replace(h, '') === e.path.replace(h, '') && t.hash === e.hash && w(t.query, e.query) : !(!t.name || !e.name) && t.name === e.name && t.hash === e.hash && w(t.query, e.query) && w(t.params, e.params))}function w (t, e) {if(void 0 === t && (t = {}), void 0 === e && (e = {}), !t || !e)return t === e;var r = Object.keys(t), n = Object.keys(e);return r.length === n.length && r.every(function (r) {var n = t[r], o = e[r];return'object' == typeof n && 'object' == typeof o ? w(n, o) : String(n) === String(o)})}function b (t, e, r) {var n = t.charAt(0);if('/' === n)return t;if('?' === n || '#' === n)return e + t;var o = e.split('/');r && o[o.length - 1] || o.pop();for(var i = t.replace(/^\//, '').split('/'), a = 0;a < i.length;a++) {var c = i[a];'..' === c ? o.pop() : '.' !== c && o.push(c)}return'' !== o[0] && o.unshift(''), o.join('/')}function x (t) {return t.replace(/\/\//g, '/')}var k = Array.isArray || function (t) {return'[object Array]' == Object.prototype.toString.call(t)}, R = I, E = j, O = function (t, e) {return $(j(t, e), e)}, _ = $, A = U, C = new RegExp(['(\\\\.)', '([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))'].join('|'), 'g');function j (t, e) {for(var r, n = [], o = 0, i = 0, a = '', c = e && e.delimiter || '/';null != (r = C.exec(t));) {var u = r[0], s = r[1], p = r.index;if(a += t.slice(i, p), i = p + u.length, s)a += s[1];else{var f = t[i], h = r[2], l = r[3], d = r[4], v = r[5], y = r[6], m = r[7];a && (n.push(a), a = '');var g = null != h && null != f && f !== h, w = '+' === y || '*' === y, b = '?' === y || '*' === y, x = r[2] || c, k = d || v;n.push({ name: l || o++, prefix: h || '', delimiter: x, optional: b, repeat: w, partial: g, asterisk: !!m, pattern: k ? P(k) : m ? '.*' : '[^' + T(x) + ']+?' })}}return i < t.length && (a += t.substr(i)), a && n.push(a), n}function S (t) {return encodeURI(t).replace(/[\/?#]/g, function (t) {return'%' + t.charCodeAt(0).toString(16).toUpperCase()})}function $ (t, e) {for(var r = new Array(t.length), n = 0;n < t.length;n++)'object' == typeof t[n] && (r[n] = new RegExp('^(?:' + t[n].pattern + ')$', q(e)));return function (e, n) {for(var o = '', i = e || {}, a = (n || {}).pretty ? S : encodeURIComponent, c = 0;c < t.length;c++) {var u = t[c];if('string' != typeof u) {var s, p = i[u.name];if(null == p) {if(u.optional) {u.partial && (o += u.prefix);continue}throw new TypeError('Expected "' + u.name + '" to be defined')}if(k(p)) {if(!u.repeat)throw new TypeError('Expected "' + u.name + '" to not repeat, but received `' + JSON.stringify(p) + '`');if(0 === p.length) {if(u.optional)continue;throw new TypeError('Expected "' + u.name + '" to not be empty')}for(var f = 0;f < p.length;f++) {if(s = a(p[f]), !r[c].test(s))throw new TypeError('Expected all "' + u.name + '" to match "' + u.pattern + '", but received `' + JSON.stringify(s) + '`');o += (0 === f ? u.prefix : u.delimiter) + s}}else{if(s = u.asterisk ? encodeURI(p).replace(/[?#]/g, function (t) {return'%' + t.charCodeAt(0).toString(16).toUpperCase()}) : a(p), !r[c].test(s))throw new TypeError('Expected "' + u.name + '" to match "' + u.pattern + '", but received "' + s + '"');o += u.prefix + s}}else o += u}return o}}function T (t) {return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g, '\\$1')}function P (t) {return t.replace(/([=!:$\/()])/g, '\\$1')}function L (t, e) {return t.keys = e, t}function q (t) {return t && t.sensitive ? '' : 'i'}function U (t, e, r) {k(e) || (r = e || r, e = []);for(var n = (r = r || {}).strict, o = !1 !== r.end, i = '', a = 0;a < t.length;a++) {var c = t[a];if('string' == typeof c)i += T(c);else{var u = T(c.prefix), s = '(?:' + c.pattern + ')';e.push(c), c.repeat && (s += '(?:' + u + s + ')*'), i += s = c.optional ? c.partial ? u + '(' + s + ')?' : '(?:' + u + '(' + s + '))?' : u + '(' + s + ')'}}var p = T(r.delimiter || '/'), f = i.slice(-p.length) === p;return n || (i = (f ? i.slice(0, -p.length) : i) + '(?:' + p + '(?=$))?'), i += o ? '$' : n && f ? '' : '(?=' + p + '|$)', L(new RegExp('^' + i, q(r)), e)}function I (t, e, r) {return k(e) || (r = e || r, e = []), r = r || {}, t instanceof RegExp ? function (t, e) {var r = t.source.match(/\((?!\?)/g);if(r)for(var n = 0;n < r.length;n++)e.push({ name: n, prefix: null, delimiter: null, optional: !1, repeat: !1, partial: !1, asterisk: !1, pattern: null });return L(t, e)}(t, e) : k(t) ? function (t, e, r) {for(var n = [], o = 0;o < t.length;o++)n.push(I(t[o], e, r).source);return L(new RegExp('(?:' + n.join('|') + ')', q(r)), e)}(t, e, r) : function (t, e, r) {return U(j(t, r), e, r)}(t, e, r)}R.parse = E, R.compile = O, R.tokensToFunction = _, R.tokensToRegExp = A;var M = Object.create(null);function V (t, e, r) {e = e || {};try{var n = M[t] || (M[t] = R.compile(t));return'string' == typeof e.pathMatch && (e[0] = e.pathMatch), n(e, { pretty: !0 })}catch(t) {return''}finally{delete e[0]}}function B (t, e, n, o) {var i = 'string' == typeof t ? { path: t } : t;if(i._normalized)return i;if(i.name) {var a = (i = r({}, t)).params;return a && 'object' == typeof a && (i.params = r({}, a)), i}if(!i.path && i.params && e) {(i = r({}, i))._normalized = !0;var c = r(r({}, e.params), i.params);if(e.name)i.name = e.name, i.params = c;else if(e.matched.length) {var u = e.matched[e.matched.length - 1].path;i.path = V(u, c, e.path)}return i}var s = function (t) {var e = '', r = '', n = t.indexOf('#');n >= 0 && (e = t.slice(n), t = t.slice(0, n));var o = t.indexOf('?');return o >= 0 && (r = t.slice(o + 1), t = t.slice(0, o)), { path: t, query: r, hash: e }}(i.path || ''), f = e && e.path || '/', h = s.path ? b(s.path, f, n || i.append) : f, l = function (t, e, r) {void 0 === e && (e = {});var n, o = r || p;try{n = o(t || '')}catch(t) {n = {}}for(var i in e)n[i] = e[i];return n}(s.query, i.query, o && o.options.parseQuery), d = i.hash || s.hash;return d && '#' !== d.charAt(0) && (d = '#' + d), { _normalized: !0, path: h, query: l, hash: d }}var H, z = [String, Object], D = [String, Array], F = function () {}, N = { name: 'RouterLink', props: { to: { type: z, required: !0 }, tag: { type: String, default: 'a' }, exact: Boolean, append: Boolean, replace: Boolean, activeClass: String, exactActiveClass: String, ariaCurrentValue: { type: String, default: 'page' }, event: { type: D, default: 'click' } }, render: function (t) {var e = this, n = this.$router, o = this.$route, i = n.resolve(this.to, o, this.append), a = i.location, c = i.route, u = i.href, s = {}, p = n.options.linkActiveClass, f = n.options.linkExactActiveClass, d = null == p ? 'router-link-active' : p, v = null == f ? 'router-link-exact-active' : f, y = null == this.activeClass ? d : this.activeClass, m = null == this.exactActiveClass ? v : this.exactActiveClass, w = c.redirectedFrom ? l(null, B(c.redirectedFrom), null, n) : c;s[m] = g(o, w), s[y] = this.exact ? s[m] : function (t, e) {return 0 === t.path.replace(h, '/').indexOf(e.path.replace(h, '/')) && (!e.hash || t.hash === e.hash) && function (t, e) {for(var r in e)if(!(r in t))return!1;return!0}(t.query, e.query)}(o, w);var b = s[m] ? this.ariaCurrentValue : null, x = function (t) {K(t) && (e.replace ? n.replace(a, F) : n.push(a, F))}, k = { click: K };Array.isArray(this.event) ? this.event.forEach(function (t) {k[t] = x}) : k[this.event] = x;var R = { class: s }, E = !this.$scopedSlots.$hasNormal && this.$scopedSlots.default && this.$scopedSlots.default({ href: u, route: c, navigate: x, isActive: s[y], isExactActive: s[m] });if(E) {if(1 === E.length)return E[0];if(E.length > 1 || !E.length)return 0 === E.length ? t() : t('span', {}, E)}if('a' === this.tag)R.on = k, R.attrs = { href: u, 'aria-current': b };else{var O = function t (e) {if(e)for(var r, n = 0;n < e.length;n++) {if('a' === (r = e[n]).tag)return r;if(r.children && (r = t(r.children)))return r}}(this.$slots.default);if(O) {O.isStatic = !1;var _ = O.data = r({}, O.data);for(var A in _.on = _.on || {}, _.on) {var C = _.on[A];A in k && (_.on[A] = Array.isArray(C) ? C : [C])}for(var j in k)j in _.on ? _.on[j].push(k[j]) : _.on[j] = x;var S = O.data.attrs = r({}, O.data.attrs);S.href = u, S['aria-current'] = b}else R.on = k}return t(this.tag, R, this.$slots.default)} };function K (t) {if(!(t.metaKey || t.altKey || t.ctrlKey || t.shiftKey || t.defaultPrevented || void 0 !== t.button && 0 !== t.button)) {if(t.currentTarget && t.currentTarget.getAttribute) {var e = t.currentTarget.getAttribute('target');if(/\b_blank\b/i.test(e))return}return t.preventDefault && t.preventDefault(), !0}}var J = 'undefined' != typeof window;function Q (t, e, r, n) {var o = e || [], i = r || Object.create(null), a = n || Object.create(null);t.forEach(function (t) {!function t (e, r, n, o, i, a) {var c = o.path, u = o.name, s = o.pathToRegexpOptions || {}, p = function (t, e, r) {return r || (t = t.replace(/\/$/, '')), '/' === t[0] ? t : null == e ? t : x(e.path + '/' + t)}(c, i, s.strict);'boolean' == typeof o.caseSensitive && (s.sensitive = o.caseSensitive);var f = { path: p, regex: X(p, s), components: o.components || { default: o.component }, instances: {}, name: u, parent: i, matchAs: a, redirect: o.redirect, beforeEnter: o.beforeEnter, meta: o.meta || {}, props: null == o.props ? {} : o.components ? o.props : { default: o.props } };if(o.children && o.children.forEach(function (o) {var i = a ? x(a + '/' + o.path) : void 0;t(e, r, n, o, f, i)}), r[f.path] || (e.push(f.path), r[f.path] = f), void 0 !== o.alias)for(var h = Array.isArray(o.alias) ? o.alias : [o.alias], l = 0;l < h.length;++l) {var d = h[l], v = { path: d, children: o.children };t(e, r, n, v, i, f.path || '/')}u && (n[u] || (n[u] = f))}(o, i, a, t)});for(var c = 0, u = o.length;c < u;c++)'*' === o[c] && (o.push(o.splice(c, 1)[0]), u--, c--);return{ pathList: o, pathMap: i, nameMap: a }}function X (t, e) {return R(t, [], e)}function Y (t, e) {var r = Q(t), n = r.pathList, o = r.pathMap, i = r.nameMap;function a (t, r, a) {var c = B(t, r, !1, e), s = c.name;if(s) {var p = i[s];if(!p)return u(null, c);var f = p.regex.keys.filter(function (t) {return!t.optional}).map(function (t) {return t.name});if('object' != typeof c.params && (c.params = {}), r && 'object' == typeof r.params)for(var h in r.params)!(h in c.params) && f.indexOf(h) > -1 && (c.params[h] = r.params[h]);return c.path = V(p.path, c.params), u(p, c, a)}if(c.path) {c.params = {};for(var l = 0;l < n.length;l++) {var d = n[l], v = o[d];if(W(v.regex, c.path, c.params))return u(v, c, a)}}return u(null, c)}function c (t, r) {var n = t.redirect, o = 'function' == typeof n ? n(l(t, r, null, e)) : n;if('string' == typeof o && (o = { path: o }), !o || 'object' != typeof o)return u(null, r);var c = o, s = c.name, p = c.path, f = r.query, h = r.hash, d = r.params;return f = c.hasOwnProperty('query') ? c.query : f, h = c.hasOwnProperty('hash') ? c.hash : h, d = c.hasOwnProperty('params') ? c.params : d, s ? (i[s], a({ _normalized: !0, name: s, query: f, hash: h, params: d }, void 0, r)) : p ? a({ _normalized: !0, path: V(function (t, e) {return b(t, e.parent ? e.parent.path : '/', !0)}(p, t), d), query: f, hash: h }, void 0, r) : u(null, r)}function u (t, r, n) {return t && t.redirect ? c(t, n || r) : t && t.matchAs ? function (t, e, r) {var n = a({ _normalized: !0, path: V(r, e.params) });if(n) {var o = n.matched, i = o[o.length - 1];return e.params = n.params, u(i, e)}return u(null, e)}(0, r, t.matchAs) : l(t, r, n, e)}return{ match: a, addRoutes: function (t) {Q(t, n, o, i)} }}function W (t, e, r) {var n = e.match(t);if(!n)return!1;if(!r)return!0;for(var o = 1, i = n.length;o < i;++o) {var a = t.keys[o - 1], c = 'string' == typeof n[o] ? decodeURIComponent(n[o]) : n[o];a && (r[a.name || 'pathMatch'] = c)}return!0}var G = J && window.performance && window.performance.now ? window.performance : Date;function Z () {return G.now().toFixed(3)}var tt = Z();function et () {return tt}function rt (t) {return tt = t}var nt = Object.create(null);function ot () {'scrollRestoration' in window.history && (window.history.scrollRestoration = 'manual');var t = window.location.protocol + '//' + window.location.host, e = window.location.href.replace(t, ''), n = r({}, window.history.state);n.key = et(), window.history.replaceState(n, '', e), window.addEventListener('popstate', function (t) {at(), t.state && t.state.key && rt(t.state.key)})}function it (t, e, r, n) {if(t.app) {var o = t.options.scrollBehavior;o && t.app.$nextTick(function () {var i = function () {var t = et();if(t)return nt[t]}(), a = o.call(t, e, r, n ? i : null);a && ('function' == typeof a.then ? a.then(function (t) {ft(t, i)}).catch(function (t) {}) : ft(a, i))})}}function at () {var t = et();t && (nt[t] = { x: window.pageXOffset, y: window.pageYOffset })}function ct (t) {return st(t.x) || st(t.y)}function ut (t) {return{ x: st(t.x) ? t.x : window.pageXOffset, y: st(t.y) ? t.y : window.pageYOffset }}function st (t) {return'number' == typeof t}var pt = /^#\d/;function ft (t, e) {var r, n = 'object' == typeof t;if(n && 'string' == typeof t.selector) {var o = pt.test(t.selector) ? document.getElementById(t.selector.slice(1)) : document.querySelector(t.selector);if(o) {var i = t.offset && 'object' == typeof t.offset ? t.offset : {};e = function (t, e) {var r = document.documentElement.getBoundingClientRect(), n = t.getBoundingClientRect();return{ x: n.left - r.left - e.x, y: n.top - r.top - e.y }}(o, i = { x: st((r = i).x) ? r.x : 0, y: st(r.y) ? r.y : 0 })}else ct(t) && (e = ut(t))}else n && ct(t) && (e = ut(t));e && window.scrollTo(e.x, e.y)}var ht, lt = J && (-1 === (ht = window.navigator.userAgent).indexOf('Android 2.') && -1 === ht.indexOf('Android 4.0') || -1 === ht.indexOf('Mobile Safari') || -1 !== ht.indexOf('Chrome') || -1 !== ht.indexOf('Windows Phone')) && window.history && 'function' == typeof window.history.pushState;function dt (t, e) {at();var n = window.history;try{if(e) {var o = r({}, n.state);o.key = et(), n.replaceState(o, '', t)}else n.pushState({ key: rt(Z()) }, '', t)}catch(r) {window.location[e ? 'replace' : 'assign'](t)}}function vt (t) {dt(t, !0)}function yt (t, e, r) {var n = function (o) {o >= t.length ? r() : t[o] ? e(t[o], function () {n(o + 1)}) : n(o + 1)};n(0)}function mt (e) {return function (r, n, o) {var i = !1, a = 0, c = null;gt(e, function (e, r, n, u) {if('function' == typeof e && void 0 === e.cid) {i = !0, a++;var s, p = xt(function (t) {var r;((r = t).__esModule || bt && 'Module' === r[Symbol.toStringTag]) && (t = t.default), e.resolved = 'function' == typeof t ? t : H.extend(t), n.components[u] = t, --a <= 0 && o()}), f = xt(function (e) {var r = 'Failed to resolve async component ' + u + ': ' + e;c || (c = t(e) ? e : new Error(r), o(c))});try{s = e(p, f)}catch(t) {f(t)}if(s)if('function' == typeof s.then)s.then(p, f);else{var h = s.component;h && 'function' == typeof h.then && h.then(p, f)}}}), i || o()}}function gt (t, e) {return wt(t.map(function (t) {return Object.keys(t.components).map(function (r) {return e(t.components[r], t.instances[r], t, r)})}))}function wt (t) {return Array.prototype.concat.apply([], t)}var bt = 'function' == typeof Symbol && 'symbol' == typeof Symbol.toStringTag;function xt (t) {var e = !1;return function () {for(var r = [], n = arguments.length;n--;)r[n] = arguments[n];if(!e)return e = !0, t.apply(this, r)}}var kt = function (t) {function e (e) {t.call(this), this.name = this._name = 'NavigationDuplicated', this.message = 'Navigating to current location ("' + e.fullPath + '") is not allowed', Object.defineProperty(this, 'stack', { value: (new t).stack, writable: !0, configurable: !0 })}return t && (e.__proto__ = t), e.prototype = Object.create(t && t.prototype), e.prototype.constructor = e, e}(Error);kt._name = 'NavigationDuplicated';var Rt = function (t, e) {this.router = t, this.base = function (t) {if(!t)if(J) {var e = document.querySelector('base');t = (t = e && e.getAttribute('href') || '/').replace(/^https?:\/\/[^\/]+/, '')}else t = '/';return'/' !== t.charAt(0) && (t = '/' + t), t.replace(/\/$/, '')}(e), this.current = v, this.pending = null, this.ready = !1, this.readyCbs = [], this.readyErrorCbs = [], this.errorCbs = []};function Et (t, e, r, n) {var o = gt(t, function (t, n, o, i) {var a = function (t, e) {return'function' != typeof t && (t = H.extend(t)), t.options[e]}(t, e);if(a)return Array.isArray(a) ? a.map(function (t) {return r(t, n, o, i)}) : r(a, n, o, i)});return wt(n ? o.reverse() : o)}function Ot (t, e) {if(e)return function () {return t.apply(e, arguments)}}Rt.prototype.listen = function (t) {this.cb = t}, Rt.prototype.onReady = function (t, e) {this.ready ? t() : (this.readyCbs.push(t), e && this.readyErrorCbs.push(e))}, Rt.prototype.onError = function (t) {this.errorCbs.push(t)}, Rt.prototype.transitionTo = function (t, e, r) {var n = this, o = this.router.match(t, this.current);this.confirmTransition(o, function () {n.updateRoute(o), e && e(o), n.ensureURL(), n.ready || (n.ready = !0, n.readyCbs.forEach(function (t) {t(o)}))}, function (t) {r && r(t), t && !n.ready && (n.ready = !0, n.readyErrorCbs.forEach(function (e) {e(t)}))})}, Rt.prototype.confirmTransition = function (r, n, o) {var i = this, a = this.current, c = function (r) {!e(kt, r) && t(r) && (i.errorCbs.length ? i.errorCbs.forEach(function (t) {t(r)}) : console.error(r)), o && o(r)};if(g(r, a) && r.matched.length === a.matched.length)return this.ensureURL(), c(new kt(r));var u = function (t, e) {var r, n = Math.max(t.length, e.length);for(r = 0;r < n && t[r] === e[r];r++);return{ updated: e.slice(0, r), activated: e.slice(r), deactivated: t.slice(r) }}(this.current.matched, r.matched), s = u.updated, p = u.deactivated, f = u.activated, h = [].concat(function (t) {return Et(t, 'beforeRouteLeave', Ot, !0)}(p), this.router.beforeHooks, function (t) {return Et(t, 'beforeRouteUpdate', Ot)}(s), f.map(function (t) {return t.beforeEnter}), mt(f));this.pending = r;var l = function (e, n) {if(i.pending !== r)return c();try{e(r, a, function (e) {!1 === e || t(e) ? (i.ensureURL(!0), c(e)) : 'string' == typeof e || 'object' == typeof e && ('string' == typeof e.path || 'string' == typeof e.name) ? (c(), 'object' == typeof e && e.replace ? i.replace(e) : i.push(e)) : n(e)})}catch(t) {c(t)}};yt(h, l, function () {var t = [];yt(function (t, e, r) {return Et(t, 'beforeRouteEnter', function (t, n, o, i) {return function (t, e, r, n, o) {return function (i, a, c) {return t(i, a, function (t) {'function' == typeof t && n.push(function () {!function t (e, r, n, o) {r[n] && !r[n]._isBeingDestroyed ? e(r[n]) : o() && setTimeout(function () {t(e, r, n, o)}, 16)}(t, e.instances, r, o)}), c(t)})}}(t, o, i, e, r)})}(f, t, function () {return i.current === r}).concat(i.router.resolveHooks), l, function () {if(i.pending !== r)return c();i.pending = null, n(r), i.router.app && i.router.app.$nextTick(function () {t.forEach(function (t) {t()})})})})}, Rt.prototype.updateRoute = function (t) {var e = this.current;this.current = t, this.cb && this.cb(t), this.router.afterHooks.forEach(function (r) {r && r(t, e)})};var _t = function (t) {function e (e, r) {var n = this;t.call(this, e, r);var o = e.options.scrollBehavior, i = lt && o;i && ot();var a = At(this.base);window.addEventListener('popstate', function (t) {var r = n.current, o = At(n.base);n.current === v && o === a || n.transitionTo(o, function (t) {i && it(e, t, r, !0)})})}return t && (e.__proto__ = t), e.prototype = Object.create(t && t.prototype), e.prototype.constructor = e, e.prototype.go = function (t) {window.history.go(t)}, e.prototype.push = function (t, e, r) {var n = this, o = this.current;this.transitionTo(t, function (t) {dt(x(n.base + t.fullPath)), it(n.router, t, o, !1), e && e(t)}, r)}, e.prototype.replace = function (t, e, r) {var n = this, o = this.current;this.transitionTo(t, function (t) {vt(x(n.base + t.fullPath)), it(n.router, t, o, !1), e && e(t)}, r)}, e.prototype.ensureURL = function (t) {if(At(this.base) !== this.current.fullPath) {var e = x(this.base + this.current.fullPath);t ? dt(e) : vt(e)}}, e.prototype.getCurrentLocation = function () {return At(this.base)}, e}(Rt);function At (t) {var e = decodeURI(window.location.pathname);return t && 0 === e.toLowerCase().indexOf(t.toLowerCase()) && (e = e.slice(t.length)), (e || '/') + window.location.search + window.location.hash}var Ct = function (t) {function e (e, r, n) {t.call(this, e, r), n && function (t) {var e = At(t);if(!/^\/#/.test(e))return window.location.replace(x(t + '/#' + e)), !0}(this.base) || jt()}return t && (e.__proto__ = t), e.prototype = Object.create(t && t.prototype), e.prototype.constructor = e, e.prototype.setupListeners = function () {var t = this, e = this.router.options.scrollBehavior, r = lt && e;r && ot(), window.addEventListener(lt ? 'popstate' : 'hashchange', function () {var e = t.current;jt() && t.transitionTo(St(), function (n) {r && it(t.router, n, e, !0), lt || Pt(n.fullPath)})})}, e.prototype.push = function (t, e, r) {var n = this, o = this.current;this.transitionTo(t, function (t) {Tt(t.fullPath), it(n.router, t, o, !1), e && e(t)}, r)}, e.prototype.replace = function (t, e, r) {var n = this, o = this.current;this.transitionTo(t, function (t) {Pt(t.fullPath), it(n.router, t, o, !1), e && e(t)}, r)}, e.prototype.go = function (t) {window.history.go(t)}, e.prototype.ensureURL = function (t) {var e = this.current.fullPath;St() !== e && (t ? Tt(e) : Pt(e))}, e.prototype.getCurrentLocation = function () {return St()}, e}(Rt);function jt () {var t = St();return'/' === t.charAt(0) || (Pt('/' + t), !1)}function St () {var t = window.location.href, e = t.indexOf('#');if(e < 0)return'';var r = (t = t.slice(e + 1)).indexOf('?');if(r < 0) {var n = t.indexOf('#');t = n > -1 ? decodeURI(t.slice(0, n)) + t.slice(n) : decodeURI(t)}else t = decodeURI(t.slice(0, r)) + t.slice(r);return t}function $t (t) {var e = window.location.href, r = e.indexOf('#');return(r >= 0 ? e.slice(0, r) : e) + '#' + t}function Tt (t) {lt ? dt($t(t)) : window.location.hash = t}function Pt (t) {lt ? vt($t(t)) : window.location.replace($t(t))}var Lt = function (t) {function r (e, r) {t.call(this, e, r), this.stack = [], this.index = -1}return t && (r.__proto__ = t), r.prototype = Object.create(t && t.prototype), r.prototype.constructor = r, r.prototype.push = function (t, e, r) {var n = this;this.transitionTo(t, function (t) {n.stack = n.stack.slice(0, n.index + 1).concat(t), n.index++, e && e(t)}, r)}, r.prototype.replace = function (t, e, r) {var n = this;this.transitionTo(t, function (t) {n.stack = n.stack.slice(0, n.index).concat(t), e && e(t)}, r)}, r.prototype.go = function (t) {var r = this, n = this.index + t;if(!(n < 0 || n >= this.stack.length)) {var o = this.stack[n];this.confirmTransition(o, function () {r.index = n, r.updateRoute(o)}, function (t) {e(kt, t) && (r.index = n)})}}, r.prototype.getCurrentLocation = function () {var t = this.stack[this.stack.length - 1];return t ? t.fullPath : '/'}, r.prototype.ensureURL = function () {}, r}(Rt), qt = function (t) {void 0 === t && (t = {}), this.app = null, this.apps = [], this.options = t, this.beforeHooks = [], this.resolveHooks = [], this.afterHooks = [], this.matcher = Y(t.routes || [], this);var e = t.mode || 'hash';switch(this.fallback = 'history' === e && !lt && !1 !== t.fallback, this.fallback && (e = 'hash'), J || (e = 'abstract'), this.mode = e, e) {case'history':this.history = new _t(this, t.base);break;case'hash':this.history = new Ct(this, t.base, this.fallback);break;case'abstract':this.history = new Lt(this, t.base)}}, Ut = { currentRoute: { configurable: !0 } };function It (t, e) {return t.push(e), function () {var r = t.indexOf(e);r > -1 && t.splice(r, 1)}}return qt.prototype.match = function (t, e, r) {return this.matcher.match(t, e, r)}, Ut.currentRoute.get = function () {return this.history && this.history.current}, qt.prototype.init = function (t) {var e = this;if(this.apps.push(t), t.$once('hook:destroyed', function () {var r = e.apps.indexOf(t);r > -1 && e.apps.splice(r, 1), e.app === t && (e.app = e.apps[0] || null)}), !this.app) {this.app = t;var r = this.history;if(r instanceof _t)r.transitionTo(r.getCurrentLocation());else if(r instanceof Ct) {var n = function () {r.setupListeners()};r.transitionTo(r.getCurrentLocation(), n, n)}r.listen(function (t) {e.apps.forEach(function (e) {e._route = t})})}}, qt.prototype.beforeEach = function (t) {return It(this.beforeHooks, t)}, qt.prototype.beforeResolve = function (t) {return It(this.resolveHooks, t)}, qt.prototype.afterEach = function (t) {return It(this.afterHooks, t)}, qt.prototype.onReady = function (t, e) {this.history.onReady(t, e)}, qt.prototype.onError = function (t) {this.history.onError(t)}, qt.prototype.push = function (t, e, r) {var n = this;if(!e && !r && 'undefined' != typeof Promise)return new Promise(function (e, r) {n.history.push(t, e, r)});this.history.push(t, e, r)}, qt.prototype.replace = function (t, e, r) {var n = this;if(!e && !r && 'undefined' != typeof Promise)return new Promise(function (e, r) {n.history.replace(t, e, r)});this.history.replace(t, e, r)}, qt.prototype.go = function (t) {this.history.go(t)}, qt.prototype.back = function () {this.go(-1)}, qt.prototype.forward = function () {this.go(1)}, qt.prototype.getMatchedComponents = function (t) {var e = t ? t.matched ? t : this.resolve(t).route : this.currentRoute;return e ? [].concat.apply([], e.matched.map(function (t) {return Object.keys(t.components).map(function (e) {return t.components[e]})})) : []}, qt.prototype.resolve = function (t, e, r) {var n = B(t, e = e || this.history.current, r, this), o = this.match(n, e), i = o.redirectedFrom || o.fullPath;return{ location: n, route: o, href: function (t, e, r) {var n = 'hash' === r ? '#' + e : e;return t ? x(t + '/' + n) : n}(this.history.base, i, this.mode), normalizedTo: n, resolved: o }}, qt.prototype.addRoutes = function (t) {this.matcher.addRoutes(t), this.history.current !== v && this.history.transitionTo(this.history.getCurrentLocation())}, Object.defineProperties(qt.prototype, Ut), qt.install = function t (e) {if(!t.installed || H !== e) {t.installed = !0, H = e;var r = function (t) {return void 0 !== t}, o = function (t, e) {var n = t.$options._parentVnode;r(n) && r(n = n.data) && r(n = n.registerRouteInstance) && n(t, e)};e.mixin({ beforeCreate: function () {r(this.$options.router) ? (this._routerRoot = this, this._router = this.$options.router, this._router.init(this), e.util.defineReactive(this, '_route', this._router.history.current)) : this._routerRoot = this.$parent && this.$parent._routerRoot || this, o(this, this)}, destroyed: function () {o(this)} }), Object.defineProperty(e.prototype, '$router', { get: function () {return this._routerRoot._router} }), Object.defineProperty(e.prototype, '$route', { get: function () {return this._routerRoot._route} }), e.component('RouterView', n), e.component('RouterLink', N);var i = e.config.optionMergeStrategies;i.beforeRouteEnter = i.beforeRouteLeave = i.beforeRouteUpdate = i.created}}, qt.version = '3.2.0', J && window.Vue && window.Vue.use(qt), qt}, 'object' == typeof exports && 'undefined' != typeof module ? module.exports = e() : 'function' == typeof define && define.amd ? define(e) : (t = t || self).VueRouter = e()