import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

/**
 * 查询所有接收消息
 */
const receiceList = params => {
    /**
     * 查询所有接收消息
     */
    return httpPost({
        url: '/materialMall/userCenter/message/list',
        params,
    })
}
/**
 * 根据接收者信息批量修改阅读状态
 */
const updateReadStateBath = params => {

    return httpPost({
        url: '/materialMall/userCenter/message/updateBathState',
        params,
    })
}
const delMessageBath = params => {

    return httpPost({
        url: '/materialMall/userCenter/message/deleteBatch',
        params,
    })
}

const del = params => {

    return httpGet({
        url: '/materialMall/userCenter/message/delete',
        params,
    })
}
const updateState = params => {
    return httpGet({
        url: '/materialMall/userCenter/message/updateStateById',
        params
    })
}
export {
    receiceList,
    updateReadStateBath,
    del,
    updateState,
    delMessageBath
}