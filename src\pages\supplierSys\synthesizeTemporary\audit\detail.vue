<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="清单信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="清单明细" name="productInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="审核历史" name="auditRecords" :disabled="clickTabFlag"/>
                <div id="tabs-content" style="padding-bottom: 70px;">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">清单信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="编号：">
                                            <span>{{ formData.synthesizeTemporarySn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="状态：">
                                            <el-tag v-if="formData.state == 2">待审核</el-tag>
                                            <el-tag type="success" v-if="formData.state == 3">审核通过</el-tag>
                                            <el-tag type="danger" v-if="formData.state == 4">审核不通过</el-tag>
                                            <el-tag type="success" v-if="formData.state == 6">已推送大宗临购计划</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="清单类型：">
                                            <el-tag v-if="formData.billType == 1">浮动价格</el-tag>
                                            <el-tag v-if="formData.billType == 2">固定价格</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="省：">
                                            <span>{{ formData.province }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="市：">
                                            <span>{{ formData.city }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="县、区：">
                                            <span>{{ formData.county }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="项目收货地址：">
                                            <span>{{ formData.receiverAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额（元）：">
                                            <span>{{ formData.synthesizeSumAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12" >
                                        <el-form-item label="超期垫资利息（%）：" prop="outPhaseInterest">
                                            {{formData.outPhaseInterest}}%
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" >
                                        <el-form-item label="货款支付周期（单位月）：" prop="paymentWeek">
                                            {{formData.paymentWeek}}月
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="确认时间：">
                                            <span>{{ formData.auditTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="提交时间：">
                                            <span>{{ formData.submitTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="采购单位是否删除：">
                                            <el-tag type="success" v-if="formData.orgIsDelete == 0">否</el-tag>
                                            <el-tag type="danger" v-if="formData.orgIsDelete == 1">是</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="创建时间：">
                                            <span>{{ formData.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="采购单位名称：">
                                            <span>{{ formData.orgName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供货单位名称：">
                                            <span>{{ formData.supplierOrgName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注信息：">
                                            {{formData.remarks}}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 订单商品-->
                    <div id="productInfo" class="con" v-loading="tableLoading">
                        <div class="tabs-title" id="contractList">清单明细</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                      border
                                      style="width: 100%"
                                      :data="formData.dtls"
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="productSn" label="商品编号" width="200"/>
                                <el-table-column prop="productName" label="商品名称" width=""/>
                                <el-table-column prop="materialName" label="物资名称" width=""/>
                                <el-table-column prop="spec" label="规格型号" width=""/>
                                <el-table-column prop="classNamePath" label="分类路径" width=""/>
                                <el-table-column prop="texture" label="材质" width=""/>
                                <el-table-column prop="brandName" label="品牌名称" width=""/>
                                <el-table-column v-if="formData.billType === 1" prop="netPrice" label="网价" width="100">
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 1" prop="fixationPrice" label="固定费" width="100">
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 2" prop="outFactoryPrice" label="出厂价" width="100">
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 2" prop="transportPrice" label="运杂费" width="100">
                                </el-table-column>
                                <el-table-column prop="synthesizePrice" label="含税单价" width=""/>
<!--                                <el-table-column prop="isTwoUnit" label="是否有副级单位" width="">-->
<!--                                    <template v-slot="scope">-->
<!--                                        <el-tag type="success" v-if="scope.row.isTwoUnit === 1">是</el-tag>-->
<!--                                        <el-tag type="info" v-if="scope.row.isTwoUnit === 0">否</el-tag>-->
<!--                                    </template>-->
<!--                                </el-table-column>-->
                                <el-table-column prop="qty" label="数量" width=""/>
                                <el-table-column prop="unit" label="计量单位" width=""/>
<!--                                <el-table-column prop="twoUnitNum" label="副级数量" width="">-->
<!--                                    <template v-slot="scope">-->
<!--                                        <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnitNum}}</span>-->
<!--                                        <span v-else>/</span>-->
<!--                                    </template>-->
<!--                                </el-table-column>-->
<!--                                <el-table-column prop="twoUnit" label="副级单位" width="">-->
<!--                                    <template v-slot="scope">-->
<!--                                        <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnit}}</span>-->
<!--                                        <span v-else>/</span>-->
<!--                                    </template>-->
<!--                                </el-table-column>-->
                                <el-table-column prop="synthesizeAmount" label="含税金额" width=""/>
                            </el-table>
                        </div>
                    </div>
                    <div id="auditRecords" class="con">
                        <div class="tabs-title" id="auditRecords">审核历史</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="formData.auditRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="auditType" label="审核类型" width="160">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.auditType == 1">提交审核</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200">
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160">
                                </el-table-column>
                                <el-table-column prop="auditResult" label="审核意见" width="">
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button
                type="primary" class="btn-greenYellow"
                v-if="formData.state === 2 && userInfo.roles.includes('物资大宗临购清单审核权限')"
                @click="auditPlanM(1, '通过')"
            >通过
            </el-button>
            <el-button
                type="primary" class="btn-delete"
                v-if="formData.state === 2  && userInfo.roles.includes('物资大宗临购清单审核权限')"
                @click="auditPlanM(0, '未通过')"
            >未通过
            </el-button>
            <el-button type="primary" v-if="formData.orgIsDelete === 3 || formData.orgIsDelete === 6" @click="outExcel">导出excel</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
import '@/utils/jquery.scrollTo.min'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import { supplierDeleteInfo, exportExcel, auditBusinessST, suppliersSynthesizeTemporaryGetBySn } from '@/api/frontStage/userCenter'
import { mapState } from 'vuex'
export default {

    data () {
        return {
            formLoading: false,
            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedRow: [],
            changedRowNum: [],
            tableLoading: false,
        }
    },
    created () {
        this.findByDataSnM()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        outExcel () {
            this.formLoading = true
            let str = ''
            if(this.formData.billType == 1) {
                str = '大宗临购清单（浮动价格）.xlsx'
            }
            if(this.formData.billType == 2) {
                str = '大宗临购清单（固定价格）.xlsx'
            }
            exportExcel({ id: this.formData.synthesizeTemporaryId }).then(res=>{
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = str
                a.click()
                window.URL.revokeObjectURL(url)
                this.$message.success('操作成功')
            }).finally(()=>{
                this.formLoading = false
            })
        },
        deleteBilM () {
            this.clientPop('info', '您确定要删除吗？', async () => {
                this.formLoading = true
                supplierDeleteInfo({ id: this.formData.synthesizeTemporaryId }).then(res => {
                    if(res.code == 200) {
                        this.$message.success('操作成功！')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗？', async () => {
                if (state == 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            id: this.formData.synthesizeTemporaryId,
                            isOpen: 0,
                            auditResult: value
                        }
                        this.formLoading = true
                        auditBusinessST(params).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.findByDataSnM()
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    }).catch(() => {
                    })
                } else {
                    let params = {
                        id: this.formData.synthesizeTemporaryId,
                        isOpen: 1
                    }
                    this.formLoading = true
                    auditBusinessST(params).then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.findByDataSnM()
                        }
                    }).finally(() => {
                        this.formLoading = false
                    })
                }
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        findByDataSnM () {
            this.formLoading = true
            suppliersSynthesizeTemporaryGetBySn({ sn: this.$route.query.sn }).then(res => {
                this.formData = res
            }).finally(() => {
                this.formLoading = false
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 780px;
        margin-top: 0px;
    }
}

/deep/ #supplierDialog {
    .el-dialog__body {
        height: 580px;
        margin-top: 0px;
    }
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
