<template>
    <main class="dfc">
        <div class="login-box p20">
            <div class="login-title mb20">商城云中心</div>
            <el-form :model="loginForm">
                <el-row>
                    <el-form-item label="账号：">
                        <el-input v-model="loginForm.account" placeholder="请输入账号"/>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="密码：">
                        <el-input type="password" clearable show-password v-model="loginForm.password" placeholder="请输入密码"/>
                    </el-form-item>
                </el-row>
                <div class="dfc mb20">
                    <el-button type="primary" :loading="pendingLogin" @click="handleLogin">登录</el-button>
                    <el-button type="primary" @click="clearForm">清空</el-button>
                </div>
            </el-form>
        </div>
    </main>
</template>

<script>
import { cLogin } from '@/api/cloudCenter/auth'
import { mapMutations } from 'vuex'
export default {
    name: 'cloudLogin',
    data () {
        return {
            loginForm: {
                account: 'admin',
                password: '123',
            },
            pendingLogin: false
        }
    },
    methods: {
        ...mapMutations(['setCToken']),
        handleLogin () {
            if(!this.loginForm.account || !this.loginForm.password) return this.$message.error('请输入账号和密码')
            this.pendingLogin = true
            cLogin(this.loginForm).then(res => {
                this.pendingLogin = false
                console.log(res)
                if(!res.token) return
                this.setCToken(res.token)
                this.$router.push('/cloudCenter')
            })
        },
        clearForm () {
            this.loginForm = { account: '', password: '' }
        },
    },
    created () {},
}
</script>

<style scoped lang="scss">
main {
    height: 100vh;
    //background-color: beige;
    align-items: center;
    background-image: url("https://wallpapers.microsoft.design/images/feature-10.jpg");
}
.login-box {
    width: 600px;
    background-color: #fff;
    opacity: 0.7;
    .login-title {
        font-size: 26px;
        font-weight: bold;
        text-align: center;
        color: #1b7cea;
    }
    /deep/ .el-form {
        width: 70%;
        margin: 0 auto;
        .el-input, .el-input__inner {
            height: 40px;
            border-radius: 0;
        }
        .el-button {
            width: 85px;
            height: 40px;
            margin-right: 24px;
            line-height: 40px;
            border-radius: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}
</style>