import service from '@/utils/request'
import qs from 'qs'

// eslint-disable-next-line
const { httpPost, httpGet } = service
const tokenLogin = params => {
    return httpGet({
        url: '/materialMall/w/user/tokenLogin',
        params
    })
}
const ttLogin = params => {
    return httpPost({
        url: '/materialMall/w/user/ttLogin',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
export {
    tokenLogin,
    ttLogin,
}