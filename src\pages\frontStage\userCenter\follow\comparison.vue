<template>
    <main class="userCenter">
        <div class="title">
           <span>商品对比历史记录</span>
            <div>
                <el-select v-model="selectedVal" value-key="" @change="handleFilter">
                    <el-option v-for="item in selectOptions"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
            </div>
<!--            <el-button style="margin-left: 390px;margin-right: 10px" class="content-width" @click="$router.go(-1)">返回</el-button>-->
            <div style="margin-left: 390px;"  class="box dfa">
                <img src="@/assets/images/ico_search.png" alt="">
                <input class="" type="text"  v-model="filterData.keywords" placeholder="输入搜索关键字" >
            </div>
            <button  class="search-button" style="margin-right: 20px" @click="handleSearch">搜索</button>
            <span class="content-width pointer" @click="exportMultiple"><i class="el-icon el-icon-download"/>合并导出记录</span>
            <span class="content-width pointer" @click="$router.go(-1)">返回</span>
        </div>
        <div class="content p20">
            <div class="compare-item p20 mb10" v-for="item in list" :key="item.compareId">
                <div class="df">
                    <el-checkbox v-model="item.select" />
                    <div class="item-content ml10">
                        <div class="dfb mb10">
                            <span class="pointerInfo" @click="viewDetail(item.compareId)">{{ item.productNames }}</span>
                            <span class="pointerInfo pointer" style="color: #216ec6;" @click="viewDetail(item.compareId)">查看详情</span>
                        </div>
                        <div style="color: #999;">{{ item.compareTime | trimDate }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="p20 pager dfb">
            <div class="batchAction dfa">
                <el-checkbox v-model="checkAll" label="全选" :indeterminate="false" @change="toggleSelectAll"></el-checkbox>
                <div class="btn del" @click="handleDeleteBatch">删除选中</div>
            </div>
            <Pagination
                :pageSize="page.limit"
                :currentPage="page.page"
                :total="page.totalCount"
                :totalPage="page.totalPage"
                :destination="page.destination"
                @currentChange="currentChange"
                @sizeChange="sizeChange"
            />
        </div>
    </main>
</template>

<script>
import { getComparisonHistory, exportMultiplePdf, deleteBatch } from '@/api/frontStage/productCompare'
import Pagination from '@/pages/frontStage/components/pagination'
import { hideLoading, showLoading } from '@/utils/common'

export default {
    name: 'comparison',
    components: { Pagination },
    data () {
        return {
            list: [],
            page: {
                limit: 10,
                page: 1,
                totalCount: 0,
                totalPage: 0,
                destination: 1,
            },
            filterData: {
                keywords: '',
                timeScope: []
            },
            selectedVal: 0,
            checkAll: false,
            selectOptions: [
                { label: '近一个月记录', value: 0 },
                { label: '近三个月记录', value: 1 },
                { label: '近半年记录', value: 2 },
                { label: '全部记录', value: 3 },
            ],
        }
    },
    filters: {
        trimDate (dateStr) {
            if(!dateStr) return ''
            return dateStr.split(' ')[0]
        },
    },
    methods: {
        // 按照时间筛查数据
        handleFilter () {
            if([0, 1, 2].includes(this.selectedVal)) {
                let dateObj = {}
                switch (this.selectedVal)
                {
                case 0:
                    dateObj = this.getLastMonth()
                    break
                case 1:
                    dateObj = this.getLast3Month()
                    break
                case 2:
                    dateObj = this.getLast6Month()
                    break
                }
                this.filterData.timeScope[0] = dateObj.last + ' 00:00:00'
                this.filterData.timeScope[1] = dateObj.now + ' 23:59:59'
            }
            this.page = { ...this.page, ...this.filterData }
            this.getHistory()
        },
        // 全选事件
        toggleSelectAll () {
            this.list.forEach(item => item.select = this.checkAll)
        },
        handleChangeTime () {
            this.page = { ...this.page, ...this.filterData }
            this.getHistory()
        },
        // 批量删除
        handleDeleteBatch () {
            let ids = this.list.filter(item => item.select).map(item => item.compareId)
            if(ids.length == 0) {
                this.$message.error('请勾选需要删除的比价记录')
                return
            }
            showLoading()
            deleteBatch(ids).then(res=>{
                if (res) {
                    this.$message.success('比价记录删除成功')
                    this.getHistory()
                }
            }).finally(()=>{
                hideLoading()
            })
        },
        handleSearch () {
            this.page = { ...this.page, ...this.filterData }
            this.getHistory()
        },
        // 批量导出比价记录
        exportMultiple () {
            let ids = this.list.filter(item => item.select).map(item => item.compareId)
            if(ids.length == 0) {
                this.$message.error('请勾选需要导出的比价记录')
                return
            }
            showLoading()
            exportMultiplePdf(ids).then(res => {
                let blob = new Blob([res], { type: 'application/pdf' })
                let link = document.createElement('a')
                link.href = URL.createObjectURL(blob)
                link.download = '比价.pdf'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            }).finally(()=>{
                hideLoading()
            })
        },
        // 获取比价记录
        getHistory () {
            showLoading()
            getComparisonHistory(this.page).then(res => {
                this.list = res.list.map(item => ({ ...item, select: false })) || []
                this.page.page = res.currPage
                this.page.totalCount = res.totalCount
                this.page.totalPage = res.totalPage
            }).finally(()=>{
                hideLoading()
            })
        },
        viewDetail (id) {
            this.openWindowTab({ path: '/user/productCompareDetail', query: { id } })
        },
        currentChange (page) {
            this.page.page = page
            this.getHistory()
        },
        sizeChange () {
            this.getHistory()
        },
        // 获取最近一个月
        getLastMonth () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 1 <= 0) { //如果是1月，年数往前推一年<br>
                dateObj.last = (year - 1) + '-' + 12 + '-' + day
            }else{
                let lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate()
                if(lastMonthDay < day) {    // 1个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数
                        dateObj.last = year + '-' + (month - 1) + '-' + (lastMonthDay - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 1) + '-' + lastMonthDay
                    }
                }else{
                    dateObj.last = year + '-' + (month - 1) + '-' + day
                }
            }
            return dateObj
        },
        // 获取最近三个月
        getLast3Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 3 <= 0) { // 如果是1、2、3月，年数往前推一年
                var last3MonthDay1 = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate()    // 3个月前所在月的总天数
                if(last3MonthDay1 < day) {    // 3个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + last3MonthDay1
                }else{
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + day
                }
            }else{
                let last3MonthDay2 = new Date(year, (parseInt(month) - 3), 0).getDate()    //3个月前所在月的总天数
                if(last3MonthDay2 < day) {    //3个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 3) + '-' + (last3MonthDay2 - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 3) + '-' + last3MonthDay2
                    }
                }else{
                    dateObj.last = year + '-' + (month - 3) + '-' + day
                }
            }
            return dateObj
        },
        // 获取最近六个月
        getLast6Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 6 <= 0) { // 年数往前推一年
                let last6MonthDay1 = new Date((year - 1), (12 - (6 - parseInt(month))), 0).getDate()    // 6个月前所在月的总天数
                if(last6MonthDay1 < day) {    // 6个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + last6MonthDay1
                }else{
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + day
                }
            }else{
                let last6MonthDay2 = new Date(year, (parseInt(month) - 6), 0).getDate()    //6个月前所在月的总天数
                if(last6MonthDay2 < day) {    //6个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 6) + '-' + (last6MonthDay2 - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 6) + '-' + last6MonthDay2
                    }
                }else{
                    dateObj.last = year + '-' + (month - 6) + '-' + day
                }
            }
            return dateObj
        },
    },
    created () {
        this.selectedVal = 0
        this.handleFilter()
        this.page = { ...this.page, ...this.filterData }
        this.getHistory()
    },
}
</script>

<style scoped lang="scss">
main {
    background-color: #fff;
    .title {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .pointerInfo {
            font-size: 14px;
            color: #216ec6;
            position: absolute;
            right: 20px;
        }
    }
    .content {
        //min-height: 940px;
    }
}
main, .compare-item {
    border: 1px solid rgba(230, 230, 230, 1);
}
.item-content {
    flex-grow: 1;
}
.compare-item {
    .pointer:last-of-type {
        //color: #216ec6;
    }
}
.batchAction {
    padding-left: 20px;

    /deep/ .el-checkbox {
        margin-right: 20px;
    }

    .btn {
        width: 80px;
        height: 30px;
        text-align: center;
        font-size: 14px;
        line-height: 30px;
        cursor: pointer;
        user-select: none;
    }

    .del {
        margin-right: 15px;
        color: #fff;
        background-color: #216EC6;
    }

    .mark {
        color: #216EC6;
        border: 1px solid #216EC6;
    }
}
.search-button{
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);

}
.content-width{
    font-size: 14px;
    color: #216ec6;
    //height: 26px;
    display: block;
    margin-right: 10px;

}
/deep/ .el-select {
    //margin-right: 54px;
    &, .el-input {width: 124px;}
    .el-input__inner {
        padding-left: 20px;
        padding-right: 20px;
        border: 0;
        color: rgba(0, 0, 0, 1);
    }
    .el-select__caret {
        background-image: url(../../../../assets/images/userCenter/arrow_up.png);
        background-position: 50% 50%;
        background-repeat: no-repeat;
        &::before{content: '';}
    }
}
.box {
    width: 268px;
    height: 26px;
    border: 1px solid rgba(229, 229, 229, 1);
    border-right: 0;
    img {
        width: 16px;
        height: 16px;
        margin: 0 4px 0 10px;
    }
    input {width: 230px;}
}
</style>