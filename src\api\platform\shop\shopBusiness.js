import service from '@/utils/request'

const { httpPost, httpGet } = service

const getShopBusinessList = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/listByEntity',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/create',
        params
    })
}
const createDataByEnterpriseId = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/createDataByEnterpriseId',
        params
    })
}

const updateState = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/updateState',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/shopBusiness/delete',
        params
    })
}

const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/deleteBatch',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/updateByPublish',
        params
    })
}

const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/updateNotPublish',
        params
    })
}

const getEnterpriseInfo = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/findById',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/updateBatchSortById',
        params
    })
}
const getInfo = params => {
    params.mallType = 0
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/getInfo',
        params
    })
}
const updateBatchAuditStatus = params => {
    return httpPost({
        url: '/materialMall/platform/shopBusiness/updateBatchAuditStatus',
        params
    })
}

export {
    getShopBusinessList,
    updateState,
    create,
    edit,
    del,
    batchPublish,
    batchNotPublish,
    batchDelete,
    getEnterpriseInfo,
    changeSortValue,
    getInfo,
    createDataByEnterpriseId,
    updateBatchAuditStatus
}
