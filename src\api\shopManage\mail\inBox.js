import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/stationMessage/list',
        params
    })
}

//批量删除
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/stationMessage/deleteBatch',
        params
    })
}

//删除
const del = params => {
    return httpGet({
        url: '/materialMall/stationMessageReceive/delete',
        params
    })
}
//查询新消息数量
const getMessageNum = params => {
    return httpGet({
        url: '/materialMall/stationMessage/getMessageNum',
        params
    })
}

//更新已读状态
const update = params => {
    return httpPost({
        url: '/materialMall/stationMessage/update',
        params
    })
}
////更新已读状态根据接收消息Id更新已读状态
const updateState = params => {
    return httpGet({
        url: '/materialMall/stationMessageReceive/updateById',
        params
    })
}

export {
    getList,
    batchDelete,
    del,
    update,
    getMessageNum,
    updateState
}