<template>
    <div class="root" v-loading="formLoading">
        <div class="main">
            <div class="history mb10 center">
                <span class="router-usher">
                  <a @click="$router.replace({ path: '/mFront/mallIndex' })">竞价采购</a>>竞价详情
                </span>
            </div>
        </div>
        <div class="topBox center">
            <div class="title">{{ form.title || '暂无' }}</div>
            <div class="logImg">
                <img v-if="form.biddingState === 1" src="@/assets/images/img/<EMAIL>" alt="">
                <img v-if="form.biddingState === 2" src="@/assets/images/img/<EMAIL>" alt="">
                <img v-if="form.biddingState === 3" src="@/assets/images/img/<EMAIL>" alt="">
            </div>
            <div class="center infoxBox">
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>商品类型:
                        <span v-if="form.productType == 0">低值易耗品</span>
                        <span v-if="form.productType == 1">大宗临购</span>
                        <span v-if="form.productType == 2">大宗临购清单</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>竞价编号: <span>
                       {{ form.biddingSn }}</span>
                    </div>
                </div>
                <div class="lineBox">
<!--                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>竞价来源:-->
<!--                        <span v-if="form.biddingSourceType === 1">订单</span>-->
<!--                        <span v-if="form.biddingSourceType === 2">商品</span>-->
<!--                    </div>-->
                    <div class="leftDiv" v-if="form.productType == 1"><img class="tit-img" src="@/assets/images/zao1.png"/>报价类型:
                        <span v-if="form.billType === 1">浮动价格</span>
                        <span v-if="form.billType === 2">固定价格</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>发布机构:
                        <span>{{ form.createOrgName }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="leftDiv">
                        <img class="tit-img" src="@/assets/images/zao1.png" alt=""/>竞价方式:
                        <span v-if="form.type == 1">公开竞价</span>
                        <span v-if="form.type == 2">邀请竞价</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>联系电话:
                        <span>{{ form.linkPhone }}</span></div>
                </div>
                <!--<div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>拟采金额:
                        <span>{{ form.tenderAmount }}</span></div>
                    <div class="rightDiv"><img class="tit-img" src="@/assets/images/zao1.png"/>保证金:
                        <span>{{ form.tenderBail }}</span>
                    </div>
                </div>-->
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>发布时间:
                        <span>{{ form.startTime }}</span>
                    </div>
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png"/>竞价截止时间:
                        <span>{{ form.endTime }}</span>
                    </div>
                </div>
<!--                <div class="lineBox">-->
<!--                </div>-->
<!--                 v-if="form.biddingState === 2"-->
                <div class="dfc mt20">
                    <button class="dfc" style="margin-right: 10px;"  @click="handleQuotation"><img src="@/assets/images/ico_baojia.png" alt="">报价</button>
                    <button class="dfc" @click="participateBidding"><img src="@/assets/images/ico_baojia.png" alt="">参与竞价</button>
                </div>
            </div>
        </div>
        <div class="tableTabs center">
            <div class="tabBox dfa mb20">
                <div @click="changeTabCurrent(0)" :class="[tabCurrent === 0 ? 'tabab' : '']">
                    物资清单
                </div>
                <div @click="changeTabCurrent(1)" :class="[tabCurrent === 1 ? 'tabab' : '']">
                    竞价详情
                </div>
            </div>
            <div v-show="tabCurrent === 0" style="padding-bottom: 20px">
                <div style="overflow: auto; padding: 0 20px 20px; height: 500px">
<!--                    <div class="lineBox">物资清单列表</div>-->
                    <el-table border :data="form.vos" width="1288" :header-cell-style="{ background: '#f7f7f7' }">
                        <el-table-column type="index" label="序号" width="80" align="center"/>
                        <el-table-column prop="productName" label="物资名称" align="center"/>
                        <el-table-column prop="spec" label="规格型号" width="110" align="center"/>
                        <el-table-column prop="productTexture" label="材质" width="110" align="center"/>
                        <el-table-column prop="unit" label="计量单位" width="110" align="center"/>
                        <el-table-column prop="num" label="数量" width="110" align="center"/>
                        <el-table-column prop="deliveryAddress" label="供货地点" width="110" align="center"/>
                        <el-table-column prop="deliveryDate" label="供货时间" width="110" align="center"/>
                        <el-table-column prop="remarks" label="备注" width="" align="center"/>
                    </el-table>
                </div>
            </div>
            <div v-show="tabCurrent === 1" style="padding-bottom: 20px">
                <div style="overflow: auto; padding: 0 20px 20px; height: 500px">
                    <div class="lineBox">竞价介绍</div>
                    <p><span v-html="form.biddingExplain"></span></p>
                </div>
            </div>
        </div>
        <el-dialog class="front" :visible.sync="dialogVisible" :close-on-click-modal="true">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>报价</div>
                    </div>
                    <div class="dialog-close" @click="dialogVisible = false">
                        <img src="@/assets/images/close.png" alt=""/>
                    </div>
                </div>
                <div></div>
            </div>
            <div class="dialog-body center">
                <p class="p20 mb10" style="text-indent: 3ch;">请先下版报价文件，在对应报价文件上填入价格并上传xls文件，另外需要上传签字打印盖章的PDF文件，共计两份文件。注意：请保证导入xls文件与报价盖章的文件数据一致，如有不一致报价由报价方负责。</p>
                <div class="dfc">
                    <button @click="downloadFile" style="margin-right: 10px;">下载报价文件</button>
                    <button @click="uploadFile">上传报价文件</button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { getBiddingPurchaseDetail, putBidingSupplierInfo, checkIsBiddingSupplier } from '@/api/frontStage/biddingPurchase'
import { mapState } from 'vuex'
export default {
    name: 'bigDetail',
    data () {
        return {
            formLoading: false,
            dialogVisible: false,
            tabCurrent: 0,
            form: {},
            proclamation: true,
            balse: false,
            tenderList: false,
            materialList: [],
        }
    },
    computed: {
        ...mapState(['userInfo'])
    },
    methods: {
        handleQuotation () {
            if(this.userInfo.isSupplier != 1) {
                return this.$message.error('不是供应商不能参与！')
            }
            this.formLoading = true
            checkIsBiddingSupplier({ biddingSn: this.$route.query.biddingSn }).then(res => {
                if(res.code != null && res.code == 200) {
                    if (this.form.productType === 0) {
                        this.openWindowTab({ path: '/supplierSys/bidManage/myBiddingDetail', query: { id: this.$route.query.biddingSn } })
                    }else {
                        this.openWindowTab({ path: '/supplierSys/bidManage/myLgBiddingDetail', query: { id: this.$route.query.biddingSn } })
                    }
                }
            }).finally(() => {
                this.formLoading = false
            })
        },
        participateBidding () {
            if(this.userInfo.isSupplier != 1) {
                return this.$message.error('不是供应商不能参与！')
            }
            this.formLoading = true
            putBidingSupplierInfo({ biddingSn: this.$route.query.biddingSn }).then(res => {
                if(res.code != null && res.code == 200) {
                    this.$message.success('参与成功')
                }
            }).finally(() => {
                this.formLoading = false
            })
        },
        downloadFile () {},
        uploadFile () {},
        changeTabCurrent (i) {
            this.tabCurrent = i
        },
    },
    async created () {
        this.formLoading = true
        let res = await getBiddingPurchaseDetail({ biddingSn: this.$route.query.biddingSn })
        this.form = res
        this.formLoading = false
    },
}
</script>
<style scoped lang="scss">
.root {
    background-color: #f5f5f5;
    padding-bottom: 50px;
}

span {
    line-height: 1;
}

.router-usher {
    cursor: pointer;

    a:hover {
        color: rgba(34, 111, 199, 1);
    }
}

.row {
    margin-bottom: 20px;
}

.main {
    width: 1326px;
    margin: 10px auto 0;

    .history {
        width: 100%;
        height: 40px;
        margin-top: 10px;
        padding-left: 20px;
        font-size: 12px;
        line-height: 40px;
        background-color: #fff;
        position: relative;

        div {
            width: 64px;
            height: 28px;
            text-align: center;
            line-height: 28px;
            border: 1px solid rgba(230, 230, 230, 1);
            color: rgba(153, 153, 153, 1);
            position: absolute;
            top: 6px;
            right: 10px;
            cursor: pointer;
            user-select: none;
        }
    }
}

.overflow {
    height: 500px;
    overflow: hidden;
    overflow-y: scroll;
}

.status {
    width: 142px;
    height: 40px;
    opacity: 1;
    border-radius: 1px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    line-height: 40px;
    margin-top: 15px;
}

.topBox {
    width: 1326px;
    padding: 20px 21px 20px 19px;
    background-color: #fff;
    padding-bottom: 30px;
    position: relative;
}

.title {
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    padding-top: 20px;
    padding-bottom: 54px;
    border-bottom: 1px dashed rgba(229, 229, 229, 1);;
}

.el-table {
    margin: auto;
}

.infoBox {

    padding-top: 32px;

}

.lineBox {
    height: 30px;
    font-size: 16px;
    padding-left: 205px;
    margin-top: 21px;
    overflow: hidden;
    display: flex;

    span {
        margin-left: 10px;
    }

    .leftDiv, .rightDiv {
        width: 50%;
        display: flex;
        align-items: center;

        .tit-img {
            width: 20px;
            height: 20px;
            margin-right: 11px;
        }
    }

}

/deep/ .el-dialog__body {
    overflow: unset;
}

.button_bule {
    width: 142px;
    height: 40px;
    opacity: 1;
    background: #226FC7;
    font-size: 16px;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    line-height: 40px;
    margin-top: 15px;
    position: relative;
    left: 40%;

}

.logImg {
    z-index: 1;
    width: 136px;
    height: 136px;
    border: 1px dashed rgba(229, 229, 229, 1);
    position: absolute;
    right: 110px;
    top: 0;
    img {
        width: 135px;
    }
}

button {
    min-width: 100px;
    height: 40px;
    padding: 0 10px;
    font-size: 16px;
    color: #fff;
    background-color: #216ec6;
    border-radius: 0;
    img {
        width: 18px;
        height: 18px;
        margin-right: 6px;
    }
}
.tableTabs {
    width: 1326px;
    // height: 605px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(229, 229, 229, 1);
    margin-top: 20px;

    .lineBox {
        padding-left: 0;
        margin-top: 0;
    }
    .tabBox {
        width: 1324px;
        height: 60px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        background: rgba(250, 250, 250, 1);

        div {
            width: 160px;
            height: 60px;
            text-align: center;
            line-height: 60px;
            font-size: 18px;
            cursor: pointer;
        }

        .tabab {
            background: #fff;
            border-top: solid 2px rgba(34, 111, 199, 1);
        }
    }

    .description {
        width: 1286px;
        // height: 108px;
        border: 1px solid rgba(230, 230, 230, 1);
    }

    .row {
        padding-left: 27px;
        padding-right: 240px;

        .item {
            span {
                font-size: 16px;
                font-weight: 400;
                color: rgba(102, 102, 102, 1);
            }

            div {
                font-size: 16px;
                font-weight: 400;
                color: rgba(0, 0, 0, 1);
            }
        }
    }

    .han {
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 1);
        padding-left: 30px;
        margin-top: 30px;
    }

    .descriptionsBox {
        padding: 20px;
    }
}
</style>
