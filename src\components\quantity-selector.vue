<template>
  <div class="quantity-selector">
    <button class="btn-minus" @click="decrease" :disabled="currentValue <= min">
      <svg t="1752481384921" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2898" width="20" height="20"><path d="M128 432h768v192H128z" fill="#000000" p-id="2899"></path></svg>
    </button>
    <input
      type="number"
      class="quantity-input"
      :value="currentValue"
      @input="handleInput"
      @blur="validateValue"
      :min="min"
      :max="max"
      ref="inputRef"
    />
    <button class="btn-plus" @click="increase" :disabled="currentValue >= max">
      <svg t="1752481497627" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4183" width="20" height="20"><path d="M832 469.333333H554.666667V192c0-25.6-17.066667-42.666667-42.666667-42.666667s-42.666667 17.066667-42.666667 42.666667V469.333333H192c-25.6 0-42.666667 17.066667-42.666667 42.666667s17.066667 42.666667 42.666667 42.666667H469.333333v277.333333c0 25.6 17.066667 42.666667 42.666667 42.666667s42.666667-17.066667 42.666667-42.666667V554.666667h277.333333c25.6 0 42.666667-17.066667 42.666667-42.666667s-17.066667-42.666667-42.666667-42.666667z" fill="#221E1F" p-id="4184"></path></svg>
    </button>
  </div>
</template>

<script>
export default {
    name: 'QuantitySelector',
    props: {
        value: {
            type: [Number, String],
            default: 1
        },
        min: {
            type: Number,
            default: 1
        },
        max: {
            type: Number,
            default: 100000
        }
    },
    data () {
        return {
            currentValue: parseInt(this.value) || this.min
        }
    },
    watch: {
        value (newVal) {
            this.currentValue = parseInt(newVal) || this.min
        }
    },
    methods: {
        increase () {
            if (this.currentValue < this.max) {
                this.currentValue++
                this.$emit('changeSelector', this.currentValue)
            }
        },
        decrease () {
            if (this.currentValue > this.min) {
                this.currentValue--
                this.$emit('changeSelector', this.currentValue)
            }
        },
        handleInput (e) {
            const value = e.target.value.trim()
            if (value === '') {
                this.currentValue = ''
                return
            }

            const num = parseInt(value)
            if (isNaN(num)) {
                this.currentValue = ''
                return
            }

            if (num < this.min) {
                this.currentValue = this.min
            } else if (num > this.max) {
                this.currentValue = this.max
            } else {
                this.currentValue = num
            }

            this.$emit('change', this.currentValue)
        },
        validateValue () {
            if (this.currentValue === '') {
                this.currentValue = this.min
                this.$emit('change', this.currentValue)
            }
        }
    }
}
</script>

<style scoped>
.quantity-selector {
  display: flex;
  align-items: center;
  width: 100px;
  height: 32px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.btn-minus, .btn-plus {
  width: 30px;
  flex: 0 0 24px;
  height: 100%;
  background-color: #f5f7fa;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-minus:hover, .btn-plus:hover {
  background-color: #e4e7ed;
}

.btn-minus:disabled, .btn-plus:disabled {
  background-color: #f5f7fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.quantity-input {
  width: 40px;
  flex: 1;
  height: 100%;
  /* border: none; */
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  text-align: center;
  font-size: 14px;
  color: #606266;
  outline: none;
  -moz-appearance: textfield;
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>