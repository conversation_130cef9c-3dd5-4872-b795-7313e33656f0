import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

// 商品结算
const settleAccountProduct = params => {
    return httpGet({
        url: '/materialMall/userCenter/orders/settleAccountProduct',
        params,
    })
}
const deleteLGOrderByOrderId = params => {
    return httpGet({
        url: '/materialMall/userCenter/orders/deleteLGOrderByOrderId',
        params,
    })
}
const deleteLXOrderByOrderId = params => {
    return httpGet({
        url: '/materialMall/userCenter/orders/deleteLXOrderByOrderId',
        params,
    })
}
const deleteYGOrderByOrderId = params => {
    return httpGet({
        url: '/materialMall/userCenter/orders/deleteYGOrderByOrderId',
        params,
    })
}
const updateDZLGOrderItemQty = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/updateDZLGOrderItemQty',
        params,
    })
}
const updateDZYGOrderItemQty = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/updateDZYGOrderItemQty',
        params,
    })
}
// 购物车结算
const settleAccountProductCart = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/settleAccountProductCart',
        params,
    })
}
// 创建物资订单
const createMaterialOrder = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/createMaterialOrder',
        params,
    })
}
// 我的订单列表
const getUserOrderPageList = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/getUserOrderPageList',
        params,
    })
}
// 我的订单评价列表
const getUserOrderComment = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/getUserOrderComment',
        params,
    })
}
// 获取订单详情
const getOrderDetail = params => {
    return httpGet({
        url: '/materialMall/userCenter/orders/getOrderDetail',
        params,
    })
}
// 获取订单详情
const getOrderById = params => {
    return httpGet({
        url: '/materialMall/userCenter/orders/getOrderById',
        params,
    })
}
//申请退货，改变状态为审核
const changReturnState = params => {
    return httpGet({
        url: '/materialMall/userCenter/orders/changReturnState',
        params,
    })
}
// 获取我的物流订单
const getUserLogisticsOrder = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/getUserLogisticsOrder',
        params,
    })
}

// 修改订单
const updateOrderInfo = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/updateOrderInfo',
        params,
    })
}
const orderItemListByOrderId = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/orderItemListByOrderId',
        params,
    })
}
export {
    settleAccountProduct,
    settleAccountProductCart,
    createMaterialOrder,
    getUserOrderPageList,
    getUserOrderComment,
    getOrderDetail,
    updateDZYGOrderItemQty,
    deleteYGOrderByOrderId,
    deleteLXOrderByOrderId,
    deleteLGOrderByOrderId,
    getOrderById,
    updateDZLGOrderItemQty,
    getUserLogisticsOrder,
    changReturnState,
    orderItemListByOrderId,
    updateOrderInfo,
}