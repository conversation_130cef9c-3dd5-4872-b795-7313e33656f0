import service from '@/utils/request'

const { httpPost, httpGet } = service

const getMyBidList = params => {
    return httpPost({
        url: '/materialMall/myBidding/list',
        params,
    })
}

const getBidInfoList = params => {
    return httpPost({
        url: '/materialMall/myBidding/getOfferBid',
        params,
    })
}
const udateOfferBid = (params, biddingSn) => {
    return httpPost({
        url: '/materialMall/myBidding/udateOfferBid?biddingSn=' + biddingSn,
        params,
    })
}
const udateLgOfferBid = (params, biddingSn) => {
    return httpPost({
        url: '/materialMall/myBidding/udateLgOfferBid?biddingSn=' + biddingSn,
        params,
    })
}
const downloadFile = params => {
    return httpGet({
        url: '/materialMall/myBidding/exportBidDataById',
        params,
        responseType: 'blob',
    })
}
/**
 * 导出竞价汇总表
 * @param params
 * @returns {*}
 */
const exportBiddingBidRecords = params => {
    return httpGet({
        url: '/materialMall/biddingBidRecord/exportBidSummary',
        params,
        responseType: 'blob',
    })
}
const uploadExcelFile = params => {
    return httpPost({
        url: '/materialMall/myBidding/uploadExcelFile',
        params,
    })
}

const createFileRecordDelete = params => {
    return httpPost({
        url: '/materialMall/w/fileRecordDelete/create',
        params,
    })
}

const saveBid = (params, biddingRecordId) =>{
    return httpPost({
        url: '/materialMall/myBidding/saveBid?biddingRecordId=' + biddingRecordId,
        params,
    })
}
const getProductInfo = params =>{
    return httpPost({
        url: '/materialMall/myBidding/getProductInfo',
        params,
    })
}
export {
    getMyBidList,
    getBidInfoList,
    udateOfferBid,
    downloadFile,
    uploadExcelFile,
    createFileRecordDelete,
    saveBid,
    getProductInfo,
    exportBiddingBidRecords,
    udateLgOfferBid
}