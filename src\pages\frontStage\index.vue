<template>
    <div class="page">
        <mallWebHeader></mallWebHeader>
        <div style="flex-grow: 1;">
            <router-view :key="key"></router-view>
        </div>
        <webFooter></webFooter>
    </div>
</template>
<script>
import { getParamsByCode } from '@/api/platform/system/systemParam'
import mallWebHeader from './components/mallWebHeader.vue'
import webFooter from './components/webFooter.vue'
export default {
    components: { mallWebHeader, webFooter },
    data () {
        return {
            key: true
        }
    },
    created () {
        this.getParamsByCodeM()
    },
    methods: {
        // 获取城市
        getParamsByCodeM () {
            getParamsByCode({ code: 'materialCity', size: 100 }).then(res => {
                this.$store.commit('setMaterialCity', res)
            })
        },
    }
}
</script>
<style scoped lang="scss">
.page {
    height: 100%;
    display: flex;
    flex-direction: column;
}
</style>