<template>
  <!-- 新 平台复审页面 -->
  <div class="df">
    <div class="boxTop">
      <div class="title center">企业用户注册</div>
    </div>
    <!--步骤条-->
    <div class="steps">
      <el-steps :active="4" align-center>
        <el-step title="注册"></el-step>
        <el-step title="平台初审"></el-step>
        <el-step title="申请开店"></el-step>
        <el-step title="合同签约及缴费"></el-step>
        <el-step title="平台复审"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <p class="wait-review">等待复核</p>
    <div class="successtxt">
      <p>缴费凭证提交成功，平台复审中（一般在3个工作日内完成），请等待。审核结果平台将发送短<br>消息通知，请注意查收！！！</p>
    </div>
    <!--        <div class="boxBottom">-->
    <!--            <div class="icon center"></div>-->
    <!--            <div class="msg1">注册成功</div>-->
    <!--            <div class="msg2">即将跳转至登陆…</div>-->
    <!--            <button class="center" @click="directTo('/index')">返回主页</button>-->
    <!--            <button style="margin-left: 20px;" @click="openShop">我要开店</button>-->
    <!--        </div>-->
  </div>
</template>
<script>

export default {
    data () {
        return {}
    },
    computed: {
    },
    methods: {
        directTo (url) {
            this.openWindowTab(url)
        },
        openShop () {
            this.$router.push('/mFront/openShop')
        },
    },
    mounted () {
        // setTimeout(() => {
        //     this.$router.push('/mFront/success')
        // }, 3000)
    }
}
</script>
<style scoped lang="scss">
.df {
  flex-direction: column;
}
.boxTop {
  height: 87px;
  border-bottom: 1px solid #D9D9D9;
  .title {
    width: 200px;
    height: 100%;
    font-size: 26px;
    font-weight: 500;
    line-height: 87px;
    text-align: center;
    border-bottom: 4px solid #216EC6;
    color: #333;
    user-select: none;
  }
}

.steps{
  //border-style: solid;
  margin: 0 auto;
  height: 10%;
  padding:50px 0;
  width: 800px;
  margin-bottom: -80px;
}
.successtxt{
  padding-left: 350px;
  padding-right: 350px;
  font-size: 20px;
  margin-top: 50px;
  width: 100%;
  text-align: center;
  //border: 1px solid red;
  color: red;
}
.wait-review {
  margin-top: 100px;
  text-align: center;
  font-weight: bold;
  font-size: 36px;
}
</style>
