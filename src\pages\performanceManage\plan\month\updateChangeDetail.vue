<template>
    <div class="e-form" v-loading="showLoading">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="变更明细" name="baseInfo" :disabled="clickTabFlag"/>
                <div id="tabs-content">
                    <div id="baseInfoCon" class="con">
                        <div class="dfa mb20">
                        </div>
                        <el-form :inline="true" ref="addPlanForm" :model="addPlanForm" :data="addPlanForm" :rules="addPlanFormRule">
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="变更计划编号：" prop="planChangeNo">
                                        <el-input disabled v-model="addPlanForm.planChangeNo"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="当前计划月份：" prop="thisTruePlanDate">
                                        <el-input disabled v-model="addPlanForm.thisTruePlanDate"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="计划编号：" prop="planNo">
                                        <el-input clearable disabled v-model="addPlanForm.planNo"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="7">
                                    <el-form-item label="变更单据状态：" prop="demandType">
                                        <el-tag type="info" v-if="addPlanForm.state == 0">草稿</el-tag>
                                        <el-tag v-if="addPlanForm.state == 1">已提交</el-tag>
                                        <el-tag type="success" v-if="addPlanForm.state == 2">通过</el-tag>
                                        <el-tag type="danger" v-if="addPlanForm.state == 3">未通过</el-tag>
                                        <el-tag type="warning" v-if="addPlanForm.state == 4">已作废</el-tag>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="9">
                                    <el-form-item label="单据机构：" prop="orgName">
                                        <el-input disabled v-model="addPlanForm.orgName"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="合同编号：" prop="contractNo">
                                        <el-input disabled v-model="addPlanForm.contractNo"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="7">
                                    <el-form-item label="变更计划月份：" prop="planDate">
                                        <el-date-picker
                                            value-format="yyyy-MM"
                                            v-model="addPlanForm.planDate"
                                            type="month"
                                            align="right"
                                            :picker-options="pickerOptions"
                                            placeholder="选择月"
                                        >
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="9">
                                    <el-form-item label="供应商：" prop="supplierName">
                                        <el-input disabled v-model="addPlanForm.supplierName"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="备注：" prop="remarks">
                                        <el-input
                                            style="width: 100%;" type="textarea" :auto-resize="false" v-model="addPlanForm.remarks"
                                            placeholder="请输入备注" maxlength="1000" show-word-limit
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <div class="custom-tabs dfa">
                            <div class="tab-btn pointer" :style="{ color: activeName === 'dtl' ? '#409EFF' : '' }" @click="activeName='dtl'">明细列表</div>
                            <div class="tab-btn pointer" :style="{ color: activeName === 'auditInfo' ? '#409EFF' : '' }" @click="activeName='auditInfo'">审核历史</div>
                        </div>
                        <div class="e-table">
                            <el-table
                                v-show="activeName === 'dtl'"
                                max-height="372px"
                                border
                                :data="addPlanForm.dtls"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"/>
                                <el-table-column prop="materialName" label="物资名称"/>
                                <el-table-column prop="spec" label="规格型号" width="140"/>
                                <el-table-column prop="unit" label="计量单位" width="100"/>
                                <el-table-column prop="sourceQty" label="合同明细总数量" width="100"/>
                                <el-table-column prop="maxQty" label="合同明细剩余数量" width="100"/>
                                <el-table-column prop="thisTrueQty" label="源计划数量" width="130"/>
                                <el-table-column prop="oldThisPlanQty" label="变更数量" width="100"/>
    <!--                            <el-table-column prop="isUpdate" label="是否变更" width="100">-->
    <!--                                <template v-slot="scope">-->
    <!--                                    <el-tag type="info" v-if="scope.row.oldThisPlanQty == scope.row.thisTrueQty">否</el-tag>-->
    <!--                                    <el-tag type="success" v-else>是</el-tag>-->
    <!--                                </template>-->
    <!--                            </el-table-column>-->
                                <el-table-column prop="orderQty" label="已下单数量" width="120"/>
                                <el-table-column v-if="addPlanForm.state != 2 && addPlanForm.state != 4" prop="thisPlanQty" label="更改变更数量" width="100">
                                    <template v-slot="scope">
                                        <el-input
                                            v-if="addPlanForm.state == 0 || addPlanForm.state == 3"
                                            type="number"
                                            size="mini" v-model="scope.row.thisPlanQty"
                                            :precision="4" :step="1" @change="changeSelectQtyM(scope.row)"
                                        >
                                        </el-input>
                                        <span v-else>{{scope.row.thisPlanQty}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作状态" width="140">
                                    <template v-slot="scope">
                                        <el-tag type="success" v-if="scope.row.dtlUpdateState == 1">新增</el-tag>
                                        <el-tag type="warning" v-if="scope.row.dtlUpdateState == 2">修改</el-tag>
                                        <el-tag type="danger" v-if="scope.row.dtlUpdateState == 3">删除</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="100" v-if="addPlanForm.state == 0 || addPlanForm.state == 3">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.dtlUpdateState !== 3" class="pointer" style="color: rgb(176,5,5); margin-left: 20px" @click="tableDeleteClick(scope.row)">删除</span>
                                        <span v-if="scope.row.planDtlId != null && scope.row.dtlUpdateState === 3" class="pointer" style="color: rgb(255,179,15); margin-left: 20px" @click="tableUpdateClick(scope.row)">标记修改</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <el-table
                                v-show="activeName === 'auditInfo'"
                                max-height="372px"
                                border
                                :data="addPlanForm.auditList"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="auditType" label="审核类型" width="160">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.auditType == 1">录入审核</span>
                                        <span v-if="scope.row.auditType == 2">变更审核</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200"/>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160"/>
                                <el-table-column prop="auditResult" label="审核意见"/>
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <el-dialog title="选择合同明细" v-dialogDrag v-loading="addPlanDialogLoading" :visible.sync="showAddPlanDialog" width="70%">
            <div class="e-table">
                <el-table
                    class="table"
                    ref="siteReceivingTableRef"
                    border
                    @selection-change="siteReceivingTableSelectM"
                    @row-click="siteReceivingTableRowClickM"
                    :data="contractDtlList"
                >
                    <el-table-column type="selection" width="50"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column prop="ItemName" label="物资名称"/>
                    <el-table-column prop="Model" label="规格型号"/>
                    <el-table-column prop="Unit" label="计量单位" width="100"/>
                    <el-table-column prop="Qty" label="数量" width="100"/>
                    <el-table-column prop="useQty" label="已生成计划数量" width="130"/>
                    <el-table-column prop="maxQty" label="未生成计划数量" width="130"/>
                    <el-table-column prop="thisPlanQty" label="选择数量" width="160">
                        <template v-slot="scope">
                            <el-input-number
                                v-if="scope.row.Qty != scope.row.useQty"
                                size="mini" v-model="scope.row.thisPlanQty"
                                :min="0" :precision="4" :step="1" :max="scope.row.maxQty"
                            >
                            </el-input-number>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <el-button class="btn-blue" @click="selectPlanList">确认选择</el-button>
                <el-button @click="showAddPlanDialog = false">取消</el-button>
            </span>
        </el-dialog>
        <div class="buttons">
            <el-button v-if="addPlanForm.state == 0 || addPlanForm.state == 3" class="btn-blue" @click="addPlanDtil">追加明细</el-button>
            <el-button v-if="addPlanForm.state == 0 || addPlanForm.state == 3" class="btn-greenYellow" @click="savePlanM">保存</el-button>
            <el-button v-if="addPlanForm.state == 0 || addPlanForm.state == 3" class="btn-greenYellow" @click="savePlanM(1)">保存并提交</el-button>
            <el-button class="btn-greenYellow" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="auditPlanM(1, '通过')">通过</el-button>
            <el-button class="btn-delete" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="auditPlanM(0, '未通过')">未通过</el-button>
            <el-button type="warning" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="cancellationPlanM()">作废</el-button>
            <el-button style="" @click="$router.go(-1)">返回</el-button>
        </div>
    </div>
</template>
<script>
import {
    auditChangePlan,
    cancellationChangePlan, checkTotalNum, getPCWP2BuyContractDtl,
    getPlanChangeDtlInfoByPlanNo,
    updatePlanChangeDtlByPlanId
} from '@/api/plan/plan'
import BillTop from '@/components/common/billTop.vue'
import { mapState } from 'vuex'

export default {
    components: { BillTop },
    data () {
        return {
            addPlanDtilList: [],
            addPlanDialogLoading: false,
            contractDtlList: [],
            showAddPlanDialog: false,
            changePlanDtlRowDate: [],
            tabsName: 'baseInfo',
            activeName: 'dtl',
            clickTabFlag: false,
            addPlanFormRule: {
                planNo: [
                    { required: true, message: '请输入计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                contractNo: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                planDate: [
                    { required: true, message: '请选择计划日期', trigger: 'blur' },
                ],
                supplierName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            showLoading: false,
            addPlanForm: {
            },
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    methods: {
        siteReceivingTableSelectM (value) {
            this.addPlanDtilList = value
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        changePlanDtlRowM (row) {
            if (row.thisPlanQty == null) {
                row.thisPlanQty = 0
            }
            if (this.addPlanDtilList.length === 0) {
                this.addPlanDtilList.push({
                    materialId: row.ItemID,
                    classPathName: row.MaterialClassName,
                    classPathId: row.MaterialClassld,
                    materialName: row.ItemName,
                    spec: row.Model,
                    unit: row.Unit,
                    texture: row.Spec,
                    thisPlanQty: row.thisPlanQty,
                    sourceQty: row.Qty,
                    contractDtlId: row.DtlId,
                    dtlUpdateState: 1,
                    thisTrueQty: 0,
                    oldThisPlanQty: 1,
                    orderQty: 0,
                    planChangeId: this.addPlanForm.planChangeId,
                    planId: this.addPlanForm.planId
                })
                return
            }
            let flag = false
            this.addPlanDtilList.forEach(t => {
                if (t.contractDtlId == row.DtlId) {
                    t.thisPlanQty = row.thisPlanQty
                    flag = true
                }
            })
            if (!flag) {
                this.addPlanDtilList.push({
                    materialId: row.ItemID,
                    classPathName: row.MaterialClassName,
                    classPathId: row.MaterialClassld,
                    materialName: row.ItemName,
                    spec: row.Model,
                    unit: row.Unit,
                    texture: row.Spec,
                    thisPlanQty: row.thisPlanQty,
                    sourceQty: row.Qty,
                    contractDtlId: row.DtlId,
                    dtlUpdateState: 1,
                    thisTrueQty: 0,
                    oldThisPlanQty: 1,
                    orderQty: 0,
                    planChangeId: this.addPlanForm.planChangeId,
                    planId: this.addPlanForm.planId
                })
            }
        },
        selectPlanList () {
            if(this.addPlanDtilList.length == 0) {
                return this.$message.error('未勾选数据！')
            }
            for (let i = 0; i < this.addPlanDtilList.length; i++) {
                let t = this.addPlanDtilList[i]
                if(t.thisPlanQty == null || t.thisPlanQty == 0) {
                    return this.$message.error(t.ItemName + '选择数量不能为空！')
                }
            }
            for (let i = 0; i < this.addPlanForm.dtls.length; i++) {
                let it = this.addPlanForm.dtls[i]
                for (let j = 0; j < this.addPlanDtilList.length; j++) {
                    let jt = this.addPlanDtilList[j]
                    if(jt.ItemID == it.materialId && it.dtlUpdateState != 3) {
                        return this.$message.error(jt.ItemName + '已存在列表当中！请修改变更数量即可！')
                    }
                }
            }
            for (let j = 0; j < this.addPlanDtilList.length; j++) {
                let row = this.addPlanDtilList[j]
                this.addPlanForm.dtls.push({
                    materialId: row.ItemID,
                    classPathName: row.MaterialClassName,
                    classPathId: row.MaterialClassld,
                    materialName: row.ItemName,
                    spec: row.Model,
                    unit: row.Unit,
                    texture: row.Spec,
                    thisPlanQty: row.thisPlanQty,
                    sourceQty: row.Qty,
                    contractDtlId: row.DtlId,
                    maxQty: row.maxQty,
                    dtlUpdateState: 1,
                    thisTrueQty: 0,
                    oldThisPlanQty: 1,
                    orderQty: 0,
                    planChangeId: this.addPlanForm.planChangeId,
                    planId: this.addPlanForm.planId
                })
            }
            this.showAddPlanDialog = false
        },
        addPlanDtil () {
            this.showAddPlanDialog = true
            let params = {
                params: {
                    billid: this.addPlanForm.contractId,
                    version: this.addPlanForm.pcwpVersion
                }
            }
            this.addPlanDialogLoading = true
            getPCWP2BuyContractDtl(params).then(res => {
                let resultList = res
                if (resultList.length != 0) {
                    checkTotalNum(resultList).then(res => {
                        this.contractDtlList = res
                        this.addPlanDialogLoading = false
                    }).finally(() => {
                        this.addPlanDialogLoading = false
                    })
                } else {
                    this.addPlanDialogLoading = false
                }
            })
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        cancellationPlanM () {
            this.clientPop('info', '您确定要废进行作废操作吗？', async () => {
                this.showLoading = true
                cancellationChangePlan([this.addPlanForm.planChangeId]).then(res => {
                    if (res.code != null && res.code === 200) {
                        this.$message.success('操作成功')
                        this.getPlanDtlM()
                    }
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗！', async () => {
                if (state === 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            planChangeId: this.addPlanForm.planChangeId,
                            isOpen: 0,
                            auditResult: value,
                        }
                        this.showLoading = true
                        auditChangePlan(params).then(res => {
                            if (res.code != null && res.code === 200) {
                                this.$message.success('操作成功')
                                this.getPlanDtlM()
                            }
                        }).finally(() => {
                            this.showLoading = false
                        })
                    })
                } else {
                    let params = {
                        planChangeId: this.addPlanForm.planChangeId,
                        isOpen: 1,
                    }
                    this.showLoading = true
                    auditChangePlan(params).then(res => {
                        if (res.code === 200) {
                            this.$message.success('操作成功')
                            this.getPlanDtlM()
                        }
                    }).finally(() => {
                        this.showLoading = false
                    })
                }
            })
        },
        tableUpdateClick (row) {
            if(row.planDtlId != null && row.dtlUpdateState === 3) {
                // 如果列表存在相同明细不可标记修改
                for (let i = 0; i < this.addPlanForm.dtls.length; i++) {
                    let t = this.addPlanForm.dtls[i]
                    if(t.materialId === row.materialId && t.dtlUpdateState !== 3) {
                        return this.$message.error('已存在相同明细，不可标记修改！')
                    }
                }
                row.dtlUpdateState = 2
            }
        },
        tableDeleteClick (row) {
            if(row.orderQty > 0) {
                return this.$message.error('单据已下单不能删除！')
            }
            if(row.dtlUpdateState == 2) {
                this.clientPop('info', '您确定要标记变更为删除吗？', async () => {
                    return row.dtlUpdateState = 3
                })
            }
            // 原单id没有说明是新增
            if(row.planDtlId == null) {
                let newArr = this.addPlanForm.dtls.filter(t => {
                    if(t.materialId != row.materialId) {
                        return true
                    }else {
                        return false
                    }
                })
                this.addPlanForm.dtls = newArr
            }
        },
        // 变更数量
        changeSelectQtyM (row) {
            if (row.thisPlanQty == null) {
                row.thisPlanQty = row.orderQty
            }
            if(row.thisPlanQty == 0) {
                row.thisPlanQty = 1
            }
            if(row.thisPlanQty < row.orderQty) {
                this.$message.error('变更数量不能小于下单数量！')
                row.thisPlanQty = row.orderQty
            }
            if(row.thisPlanQty > (row.thisTrueQty + row.maxQty)) {
                this.$message.error('数量超过可变更数量！')
                row.thisPlanQty = row.thisTrueQty + row.maxQty
            }
            // if (this.changePlanDtlRowDate.length === 0) {
            //     this.changePlanDtlRowDate.push({
            //         planDtlChangeId: row.planDtlChangeId,
            //         thisPlanQty: row.thisPlanQty,
            //     })
            //     return
            // }
            // let flag = false
            // this.changePlanDtlRowDate.forEach(t => {
            //     if (t.planDtlChangeId === row.planDtlChangeId) {
            //         t.thisPlanQty = row.thisPlanQty
            //         flag = true
            //     }
            // })
            // if (!flag) {
            //     this.changePlanDtlRowDate.push({
            //         planDtlChangeId: row.planDtlChangeId,
            //         thisPlanQty: row.thisPlanQty
            //     })
            // }
        },
        getPlanDtlM () {
            this.showLoading = true
            // 如果是计划编号
            getPlanChangeDtlInfoByPlanNo({ planChangeNo: this.$route.query.planChangeNo }).then(res => {
                this.addPlanForm = res
            }).finally(() => {
                this.showLoading = false
            })
        },

        // 标签点击暂时无用
        // eslint-disable-next-line no-unused-vars
        handleClick (tab, event) {
        },
        handleClose () {
            this.$router.replace('/performanceManage/plan/monthPlan')
        },
        savePlanM (isSubmit) {
            this.$refs.addPlanForm.validate((valid, errors) => {
                if (valid) {
                    this.clientPop('info', '您确定进行该操作吗！', async () => {
                        this.showLoading = true
                        // 保存并提交
                        let params = {
                            planChangeId: this.addPlanForm.planChangeId,
                            planDate: this.addPlanForm.planDate,
                            remarks: this.addPlanForm.remarks,
                            // dtls: this.changePlanDtlRowDate,
                            dtls: this.addPlanForm.dtls,
                        }
                        if (isSubmit == 1) {
                            params.isSubmit = 1
                        } else {
                            params.isSubmit = 0
                        }
                        updatePlanChangeDtlByPlanId(params).then(res => {
                            let msgType = res.code === 200 ? 'suc' : 'error'
                            let msg = res.code === 200 ? '操作成功' : res.message
                            if(res.code === 200) {
                                this.clientPop(msgType, msg, () => this.getPlanDtlM())
                            }else {
                                this.clientPop(msgType, msg, () => {})
                            }
                        }).finally(() => {
                            this.showLoading = false
                        })
                    })
                } else {
                    let prop = errors[Object.keys(errors)[0]]
                    this.$message.error(prop[0].message)
                }
            })
        },
    },
    created () {
        this.getPlanDtlM()
    }
}
</script>
<style scoped lang="scss">
/deep/ .el-dialog {
    .el-dialog__body {
        height: 500px;
        margin-top: 0px;
    }
}
.el-form-item {
    display: flex;
    align-items: flex-start;
}
.custom-tabs {
    width: 194px;
    height: 40px;
    margin-bottom: 5px;
    border: 1px solid #E4E7ED;
    color: #303133;
    .tab-btn {
        height: 100%;
        padding: 0 20px;
        line-height: 40px;
        font-size: 14px;
        transition: .3s;
        &:hover {
            color: #409EFF;
        }
    }
}
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

.add {
    width: 80px;
    height: 30px;
    margin: 18px 0 40px 0;
    line-height: 30px;
    text-align: center;
    color: rgba(33, 110, 198, 1);
    border: 1px solid rgba(33, 110, 198, 1);
}
.e-form {
}
.warningTabs {
    height: 100%;
    //flex-grow: 1;
    padding: 70px 0 78px;
    /deep/.el-tabs {
        height: 100%;
    }
}
</style>