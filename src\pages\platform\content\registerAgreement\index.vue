<template>
  <div class="base-page" v-if="showSetOrg">
    <!-- 列表 -->
    <div class="right" v-if="viewList === true">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn">
              <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
              <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">发布</el-button>
              <el-button type="primary" @click="changePublishState(2)" class="btn-delete">取消发布</el-button>
              <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
            </div>
          </div>
          <div class="search_box">
<!--            <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>-->
            <!--   <el-radio v-model="filterData.orderBy" :label="2">按创建时间排序</el-radio>-->
            <el-radio v-model="filterData.orderBy" :label="3">按修改时间排序</el-radio>
            <!--        <el-radio v-model="filterData.orderBy" :label="4">按发布时间排序</el-radio>-->
            <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords">
              <img src="@/assets/search.png" slot="suffix" @click="onSearch" />
            </el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
        <!--搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" :style="{ width: '100%' }">
        <el-table @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" v-loading="isLoading" class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""></span>
            </template>
          </el-table-column>
          <!-- 标题 -->
          <el-table-column label="标题"   width="">
            <template slot-scope="scope">
                          <span class="action" @click="handleView(scope)">
                            {{ scope.row.title }}
                          </span>
            </template>
          </el-table-column>
          <el-table-column label="用户类型" width="120">
            <template slot-scope="scope">
              <el-tag v-show="scope.row.agreementType === '1'">个人</el-tag>
              <el-tag v-show="scope.row.agreementType === '2'">个体户</el-tag>
              <el-tag v-show="scope.row.agreementType === '3'">企业</el-tag>
            </template>
          </el-table-column>
          <!-- 信息发布状态 -->
          <el-table-column label="发布状态" width="120">
            <template slot-scope="scope">
              {{ scope.row.state == 1 ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="发布时间" width="" >
            <template slot-scope="scope">
              {{ scope.row.gmtRelease }}
            </template>
          </el-table-column>
          <el-table-column label="排序值" width="120" type="index">
            <template v-slot="scope">
              <el-input type="number" v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                     :currentPage.sync="pages.currPage"
                     @currentChange="currentChange" @sizeChange="sizeChange" />
    </div>
    <div class="right" v-if="viewList !== true">
      <!-- ---------------------新增编辑窗口--------------------- -->
      <div class="e-form" style="padding: 0 10px 10px;" v-if="viewList === 'class'">
        <div class="tabs-title">{{ dialogTitle }}</div>
        <el-form :rules="formRules" ref="editForm" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="12">
              <el-form-item width="150px" label="标题：" prop="title">
                <el-input v-model="formData.title" placeholder="请输入标题名称" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item width="150px" label="用户类型：" prop="title">
                <el-select  v-model="formData.agreementType" placeholder="请选择状态">
                  <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="内容管理：" prop="content">
                <el-col :span="24">
                  <editor v-model="formData.content"></editor>
                </el-col>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row>
          </el-row> -->
        </el-form>
      </div>
      <div class="footer">
        <div class="right-btn">
          <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
          <el-button @click="onCancel">取消</el-button>
        </div>
      </div>
    </div>
    <!-- ----------------查询弹框---------------- -->
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="30%" :close-on-click-modal="true" :before-close="closeDialog">
      <el-form :model="queryForm" ref="form" label-width="120px" :inline="false" size="normal">
        <el-row>
          <el-col :span="24" :offset="0" width="60">
            <el-form-item label="标题名称：">
              <el-input v-model="filterData.title" placeholder="请输入标题名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="发布状态：">
              <el-select v-model="filterData.state" clearable placeholder="发布状态">
                <el-option v-for="item in stateFilter" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getList, edit, create, del, batchPublish, batchNotPublish, batchDelete, changeSortValue } from '@/api/platform/content/richContent'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
// import { getDicValue } from '@/api/dicValue/selectOptions'
import { mapState, mapActions, mapMutations } from 'vuex'
import editor from '../../../../components/quillEditor'
// import { batchNotPublish } from "@/api/platform/content/richContent";
// import { getOrgName } from '@/api/materials/materialsMarketPrice'
export default {
    components: {
        ComPagination, editor
    },
    watch: {
        'filterData.orderBy': {
            handler (val) {
                console.log(val)
                this.getParams()
                getList(this.requestParams).then(res => {
                    console.log(res)
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        ...mapState({
            numUnitOptions: state => state.selectOptions.numUnitOptions
        }),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) {
                return this.screenHeight - 244
            }
            return this.screenHeight - 291
        }
    },
    data () {
        return {
            stateOptions: [
                {
                    value: '1',
                    label: '个人'
                }, {
                    value: '2',
                    label: '个体户'
                }, {
                    value: '3',
                    label: '企业'
                }
            ],
            alertName: '信息',
            queryVisible: false,
            action: '编辑',
            dialogTitle: '基本信息',
            classType: '0',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            showSetOrg: true, //设置使用机构
            keywords: '',
            currentClass: null,
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            richContent: 'default content',
            queryForm: {
                one: ''
            },
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            stateFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '发布' },
                { value: 2, label: '未发布' },
            ],
            homeFiter: [
                { value: null, label: '全部' },
                { value: 1, label: '显示' },
                { value: 0, label: '隐藏' },
            ],
            topFiter: [
                { value: null, label: '全部' },
                { value: 1, label: '显示' },
                { value: 0, label: '隐藏' },
            ],
            infoTypeFiter: [
                { value: null, label: '全部' },
                { value: 1, label: '文字' },
                { value: 0, label: '音频' },
            ],
            filterData: {
                title: null,
                state: null,
                agreementType: '',
                orderBy: 3,
                limit: 20,
                page: 1,
                mallType: '0'
            },
            // 表单校验规则
            formRules: {
                title: { required: true, message: '请输入标题名称', trigger: 'blur' },
                agreementType: { required: true, message: '请选择协议类型', trigger: 'blur' },
                content: { required: true, validator: this.validateContent, trigger: 'blur' },
                sort: { required: true, message: '请填写排序值', trigger: 'blur' },
            },
            mallType: [
                { value: null, label: '全部' },
                { value: 0, label: '物资' },
                { value: 1, label: '装备' }
            ],
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            classData: {},
            tableData: [],
            orgDataTable: [],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            allData: [],
            mapObj: null,
            formData: {
                home: 2,
                top: 2,
                mallType: 0, // 商城类型
                content: '',
                gmtRelease: '',
            },
            currencyOptions: [],
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            Array: [],
            requestParams: {},
            defaultPicturePath: '',
            bigPicturePath: '',
            smallPicturePath: '',
            tinyPicturePath: '',
            requestKey: '',
            isLoading: false,
        }
    },
    mounted () {
    // let a = this.$route.query.programaKey
        console.log('--------------')
        // console.log(a)
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
    // showLoading()
    // getDicValue({ dicName: '计量单位', isEnable: true }).then(res => {
    //     this.$store.commit('setNumUnitOptions', res)
    //     console.log(this.numUnitOptions)
    // })
        this.isLoading = true
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy,
            programaKey: this.$route.query.programaKey
        }
        getList(params).then(res => {
            console.log(res)
            this.isLoading = false
            this.pages = res
            this.tableData = res.list
        })
        // hideLoading()
        this.getParams()
    },
    methods: {
        onSave1 () {
            console.log(this.formData.gmtRelease)
        },
        validateContent (rule, value, callback) {
            if(value === null) {
                return callback(new Error('请填写新闻内容'))
            }
            value = value.slice(3, -4).trim()
            console.log(`-${value}-`)
            if(value === '' || value === '<p></p>') {
                callback(new Error('请填写新闻内容'))
            }
            callback()
        },
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                title: null,
                state: null,
                home: null,
                top: null,
                limit: 20,
                page: 1,
                mallType: 0
            },
            done()
        },
        hideDialog () {
            this.filterData = {
                title: null,
                state: null,
                home: null,
                top: null,
                limit: 20,
                page: 1,
                mallType: 0
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData,
                programaKey: this.$route.query.programaKey
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            console.log(this.filterData)
            for(let key in this.filterData) {
                if(this.filterData[key] == '') {
                    // return this.clientPop('warn', )
                    // key == 'state' ? this.filterData[key] = 0 : this.filterData[key] = 1
                    this.filterData[key] = null
                }
            }
            this.getParams()
            getList(this.requestParams).then(res => {
                if(res.list) {
                    /*this.clientPop('suc', '查询成功', () => {})*/
                    this.queryVisible = false
                    for(let key in this.filterData) {
                        this.filterData[key] = null
                    }
                }else{
                    this.clientPop('warn', res.message, () => {})
                }
                console.log(res)
                this.pages = res
                this.tableData = res.list
            })
        },
        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.contentId
            })
            if (!this.selectedRows[0]) {
                let msg = num == 1 ? '请选择要发布的' + this.alertName : '请选择要取消发布的' + this.alertName
                return this.clientPop('warn', msg, () => { })
            }
            let warnMsg = num === 1 ? '您确定要发布选中的' + this.alertName + '吗？' : '您确定要取消发布选中的' + this.alertName + '吗？'
            this.clientPop('info', warnMsg, async () => {
                switch (num)
                {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '发布成功', () => {
                                getList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => { })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '取消发布成功', () => {
                                getList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {})
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该信息吗？', async () => {
                showLoading()
                del({ id: scope.row.contentId }).then(res => {
                    if(res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }else{
                        this.clientPop('warn', res.message, () => {})
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if(!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {})
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.contentId
                })
                batchDelete(arr).then(res => {
                    if(res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if(!this.changedRow[0]) {
                return this.changedRow.push({ contentId: row.contentId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if(item.contentId === row.contentId) {
                    return i
                }
            })
            if(arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ contentId: row.contentId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                let msg = '当前没有排序值被修改！'
                return this.clientPop('warn', msg, () => { })
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '修改成功', () => {
                            this.changedRow = []
                            this.getTableData()
                        })
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            this.isLoading = true
            getList(this.requestParams).then(res => {
                // console.log(res.list)
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                this.isLoading = false
                this.pages = res
            })
            this.viewList = true
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
            console.log(selection)
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange (size) {
            this.pages.pageSize = size
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        // 上传默认图片信息
        handleDefaultPictureUploadSuccess (res, file) {
            this.defaultPicturePath = URL.createObjectURL(file.raw)
            this.formData.defaultPicturePath = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        // 上传大图片信息
        handleBigPictureUploadSuccess (res, file) {
            this.bigPicturePath = URL.createObjectURL(file.raw)
            this.formData.bigPicturePath = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        // 上传小图片信息
        handleSmallPictureUploadSuccess (res, file) {
            this.smallPicturePath = URL.createObjectURL(file.raw)
            this.formData.smallPicturePath = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        // 上传微小图片信息
        handleTinyPictureUploadSuccess (res, file) {
            this.tinyPicturePath = URL.createObjectURL(file.raw)
            this.formData.tinyPicturePath = 'http://api_devss.wanxikeji.cn/api/savePic' + res.data
        },
        handlePicRemove () { },
        handleClose () { },
        handleFilter () {
            this.queryVisible = true
        },
        Save () {
            this.showSetOrg = true
        },
        Close () {
            this.showSetOrg = true
        },
        // 提交信息
        handleSubmit () {
            axios({
                method: ''
            })
        },
        handleView (scope) {
            this.viewList = 'class'
            this.dialogTitle = '基本信息'
            this.formData = JSON.parse(JSON.stringify(scope.row))
            this.action = '编辑'
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 复选框选中事件
        selectFun (selection, row) {
            console.log('selection>>>', selection)
            this.setRowIsSelect(row)

        },
        // 复选框点击事件
        setRowIsSelect (row) {
            if (row.isCheck) {
                this.Array.push(row)
                this.Array.forEach(item => {
                    if (item.children) {
                        item.children.forEach(val => {
                            this.Array.push(val)
                        })
                    }
                })
            } else {
                const index = this.Array.findIndex(x => x.orgId === row.orgId)
                if (index !== -1) {
                    this.Array.splice(index, 1)
                }
            }
        },
        // 检测表格数据是否全选
        checkIsAllSelect () {
            this.oneProductIsSelect = []
            this.orgDataTable.forEach(item => {
                this.oneProductIsSelect.push(item.isCheck)
            })
            //判断一级产品是否是全选.如果一级产品全为true，则设置为取消全选，否则全选
            let isAllSelect = this.oneProductIsSelect.every(
                selectStatusItem => {
                    return true == selectStatusItem
                }
            )
            return isAllSelect
        },
        // 表格全选事件
        selectAllFun (selection) {
            let isAllSelect = this.checkIsAllSelect()
            this.orgDataTable.forEach(item => {
                item.isCheck = !isAllSelect
                this.selectFun(selection, item)
            })
        },

        ...mapMutations(['setSelectedInfo']),
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        onSearch () {
            this.getParams()
            getList(this.requestParams).then(res => {
                console.log(res)
                this.pages = res
                this.tableData = res.list
            })
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存
        onSave () {
            this.$refs.editForm.validate(valid => {
                if (valid) {
                    if (this.action === '编辑') {
                        this.handleEdit()
                    } else {
                        this.handleCreate()
                    }
                }
            })
        },
        // 修改信息
        handleEdit () {
            console.log(this.formData)
            edit(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 新增信息
        handleCreate () {
            console.log('--------handleCreat1-------------')
            create({ ...this.formData, programaKey: this.$route.query.programaKey }).then(res => {
                console.log(this.formData)
                if(res.message == '操作成功') {
                    return this.clientPop('suc', '新增成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
            console.log(this.tableData)
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>
<style lang="scss" scoped>
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  height: unset !important;
  display: flex;
  align-items: center;
  .el-col.el-col-24 {
    height: unset;
  }
}

.e-table {
  min-height: auto;
}

.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

// upload
.avatar-uploader {
  /deep/.el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}

/deep/.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

/deep/.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

/deep/ .el-dialog {
  height: 500px !important;
  padding: 0;

  .el-dialog__header {
    margin-bottom: 20px;
    padding: 10px;
    text-align: center;
    background-color: red;
    font-weight: bold;
    background: url(../../../../assets/test.png) no-repeat;

    .el-dialog__title {
      color: #fff;

    }
  }

  .el-dialog__body {
    height: 280px;
    margin-top: 100px;
  }

  .el-dialog__close.el-icon.el-icon-close::before {
    width: 44px;
    height: 44px;
  }
}
/deep/ .el-form-item.el-form-item--small {
  //height: 500px;
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
  -moz-appearance: textfield !important;
}
</style>
