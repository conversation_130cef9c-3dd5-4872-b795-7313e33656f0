import service from '@/utils/request'

const { httpPost, httpGet } = service

const getFloorList = params => {
    return httpPost({
        url: '/materialMall/platform/floor/listByEntity',
        params
    })
}

const editFloor = params => {
    return httpPost({
        url: '/materialMall/platform/floor/update',
        params
    })
}

const createFloor = params => {
    return httpPost({
        url: '/materialMall/platform/floor/create',
        params
    })
}

const delFloor = params => {
    return httpPost({
        url: '/materialMall/platform/floor/delete',
        params
    })
}

const batchDeleteFloor = params => {
    return httpPost({
        url: '/materialMall/platform/floor/deleteBatch',
        params
    })
}

const batchPublishFloor = params => {
    return httpPost({
        url: '/materialMall/platform/floor/updateByPublish',
        params
    })
}

const batchNotPublishFloor = params => {
    return httpPost({
        url: '/materialMall/platform/floor/updateNotPublish',
        params
    })
}

const getEnterpriseInfoFloor = params => {
    return httpGet({
        url: '/materialMall/platform/floor/findById',
        params
    })
}

const changeSortValueFloor = params => {
    return httpPost({
        url: '/materialMall/platform/floor/updateBatchById',
        params
    })
}

const floorGoodsCreate = params => {
    return httpPost({
        url: '/materialMall/platform/floorGoods/batchCreate',
        params
    })
}
export {
    floorGoodsCreate,
    getFloorList,
    createFloor,
    editFloor,
    delFloor,
    batchPublishFloor,
    batchNotPublishFloor,
    batchDeleteFloor,
    getEnterpriseInfoFloor,
    changeSortValueFloor,
}
