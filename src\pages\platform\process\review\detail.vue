<template>
  <div class="e-form">
    <BillTop @cancel="handleClose" />
    <div v-loading="formLoading" class="tabs warningTabs" style="padding-top: 70px;">
      <!-- <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab"> -->
        <!-- <el-tab-pane label="物资" name="baseInfo" :disabled="clickTabFlag">
        </el-tab-pane> -->
        <div id="tabs-content" style="height: 762px;">
          <div id="baseInfCon" class="con">
            <div class="main-title">{{ processName }}</div>
            <!--新增-->
            <div style="width: 100%" class="form" v-if="showForm">
              <el-form
                :model="addForm.formData" :rules="formRules" label-width="60px" ref="formEdit"
                class="demo-ruleForm"
              >
                <!--提交-->
                <div v-for="(item, idx) in addForm.submitItem" :key="idx" class="process-row">
                  <div class="process-label"><span class="blue-bar"></span>{{ getNodeName(item.nodeNo) }}</div>
                  <el-row>
                    <el-col :span="6">
                      <el-form-item label="角色">
                        <el-select v-model="item.roleNo" placeholder="请选择角色" @change="val => onRoleChange(val, item)">
                          <el-option v-for="item in roleOptions" :key="item.roleNo" :label="item.roleName" :value="item.roleNo"/>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <!-- <el-col :span="6">
                      <el-form-item label="人员">
                        <el-select v-model="item.userId" placeholder="请选择人员" @change="val => onUserChange(val, item)">
                          <el-option v-for="item in userOptions" :key="item.userId" :label="item.userName" :value="item.userId"/>
                        </el-select>
                      </el-form-item>
                    </el-col> -->
                  </el-row>
                </div>
                <!--审核-->
                <div v-for="(item, idx) in addForm.auditItems" :key="idx" class="process-row">
                  <div class="process-label"><span class="blue-bar"></span>{{ getNodeName(item.nodeNo) }}</div>
                  <el-row>
                    <el-col :span="6">
                      <el-form-item label="角色">
                        <el-select v-model="item.roleNo" placeholder="请选择角色" @change="val => onRoleChange(val, item)">
                          <el-option v-for="item in roleOptions" :key="item.roleNo" :label="item.roleName" :value="item.roleNo"/>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <!-- <el-col :span="6">
                      <el-form-item label="人员">
                        <el-select v-model="item.userId" placeholder="请选择人员" @change="val => onUserChange(val, item)">
                          <el-option v-for="item in userOptions" :key="item.userId" :label="item.userName" :value="item.userId"/>
                        </el-select>
                      </el-form-item>
                    </el-col> -->
                  </el-row>
                </div>
                <!--审定-->
                <div v-for="(item, idx) in addForm.approveItem" :key="idx" class="process-row">
                  <div class="process-label"><span class="blue-bar"></span>{{ getNodeName(item.nodeNo) }}</div>
                  <el-row>
                    <el-col :span="6">
                      <el-form-item label="角色">
                        <el-select v-model="item.roleNo" placeholder="请选择角色" @change="val => onRoleChange(val, item)">
                          <el-option v-for="item in roleOptions" :key="item.roleNo" :label="item.roleName" :value="item.roleNo"/>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <!-- <el-col :span="6">
                      <el-form-item label="人员">
                        <el-select v-model="item.userId" placeholder="请选择人员" @change="val => onUserChange(val, item)">
                          <el-option v-for="item in userOptions" :key="item.userId" :label="item.userName" :value="item.userId"/>
                        </el-select>
                      </el-form-item>
                    </el-col> -->
                  </el-row>
                </div>
                <el-row>
                </el-row>
              </el-form>
            </div>
          </div>
        </div>
      <!-- </el-tabs> -->
      <div class="buttons">
        <el-button type="primary" @click="submit" class="btn-delete">保存</el-button>
        <el-button @click="handleClose">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
// eslint-disable-next-line no-unused-vars
import { spliceImgUrl, throttle } from '@/utils/common'
import { createMaterialSupplier } from '@/api/shopManage/product/materialManage'
import { getProcessConfigDtlById, update } from '@/api/platform/process/processConfig'

export default {
    data () {
        return {
            formLoading: false,
            formRules: {
                productDescribe: [
                    { required: true, message: '请输入描述', trigger: 'blur' }
                ]
            },
            rowData: null, // 跳转过来的数据
            showForm: false,
            viewType: null,
            //表单数据
            formData: {},
            // updData: {
            //     processId: null,
            //     processName: null,
            //     systemNo: null,
            //     systemName: null,
            //     remark: null,
            //     processConfigDtlItemVOs: []
            // },
            addForm: {
                formData: {
                    processId: null,
                    processName: null,
                    systemNo: null,
                    systemName: null,
                    remark: null,
                    processConfigDtlItemVOs: []
                },
                submitItem: null,
                auditItems: null,
                approveItem: null,
            },
            processName: null,
            // 角色
            roleOptions: [
                { value: '0', roleName: '供应商', roleNo: '0' },
                { value: '采购人', roleName: '采购人', roleNo: '采购人' },
                { value: '1', roleName: '采购人-机料内业', roleNo: '1' },
                { value: '2', roleName: '采购人-机料部门科室', roleNo: '2' },
                { value: '物资公司', roleName: '物资公司', roleNo: '物资公司' },
                { value: '3', roleName: '物资公司-电商运营部商业人员', roleNo: '3' },
                { value: '4', roleName: '物资公司-电商运营部负责人', roleNo: '4' },
                { value: '5', roleName: '物资公司-财务部', roleNo: '5' },
                { value: '6', roleName: '物资公司-分管领导', roleNo: '6' }
            ],
            // 用户
            userOptions: [
                { value: '全部', userName: '全部', userId: '全部' },
                { value: 'fa33e9c24fe311f0b7257c8ae1a484c8', userName: '王账册', userId: 'fa33e9c24fe311f0b7257c8ae1a484c8' },
                { value: '1d0e7d2c4ff811f0b7327c8ae1a484c8', userName: '李料仓', userId: '1d0e7d2c4ff811f0b7327c8ae1a484c8' },
                { value: '3d9e0cf84dbc11f087647c8ae1a484c8', userName: '陈店助', userId: '3d9e0cf84dbc11f087647c8ae1a484c8' },
                { value: '9b17aa7e4dbc11f087667c8ae1a484c8', userName: '赵卖手', userId: '9b17aa7e4dbc11f087667c8ae1a484c8' },
                { value: 'cc941ea44fe011f0b70a7c8ae1a484c8', userName: '钱算盘', userId: 'cc941ea44fe011f0b70a7c8ae1a484c8' },
                { value: 'dba26f7a4dbc11f087687c8ae1a484c8', userName: '张统筹', userId: 'dba26f7a4dbc11f087687c8ae1a484c8' }
            ],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120
        }
    },
    components: {
    },
    created () {
        this.rowData = this.$route.params.row
        if (this.rowData && this.rowData.viewType === 'add') {
            this.viewType = 'add'
            this.showForm = true
        } else {
            this.getProcessInfo()
        }
    },
    mounted () {
    // 获取数据
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 70 + 'px !important'
        }
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        }
    },
    methods: {
        onUserChange (val, item) {
            const selected = this.userOptions.find(u => u.userId === val)
            if (selected) {
                item.userName = selected.userName
                item.userId = selected.userId
            }
        },
        onRoleChange (val, item) {
            const selected = this.roleOptions.find(u => u.roleNo === val)
            if (selected) {
                item.roleName = selected.roleName
                item.roleNo = selected.roleNo
            }
        },
        getNodeName (no) {
            const map = { '0': '提交', '1': '审核', '2': '审定' }
            return map[no] || no
        },
        // 获取流程配置详情
        getProcessInfo () {
            let params0 = {
                processId: this.rowData.processId,
            }
            this.formLoading = true
            getProcessConfigDtlById(params0).then(res => {
                this.addForm.formData = res
                this.addForm.submitItem = res.processConfigDtlItemVOs.filter(item => item.nodeNo && item.nodeNo == '0')
                this.addForm.auditItems = res.processConfigDtlItemVOs.filter(item => item.nodeNo && item.nodeNo == '1')
                this.addForm.approveItem = res.processConfigDtlItemVOs.filter(item => item.nodeNo && item.nodeNo == '2')
                this.processName = res.processName
                this.showForm = true
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 计量单位选择
        numUnitChange (value) {
            this.addForm.formData.unit = value
        },
        // 提交
        submit () {
            this.$refs.formEdit.validate(valid => {
                if (!valid) return this.$message.error('请检查非空输入框')
                if (this.viewType === 'add') {
                    this.formLoading = true
                    createMaterialSupplier(this.addForm.formData).then(res => {
                        if (res.code != null && res.code != 200) {
                            return this.formLoading = false
                        }
                        // 重置
                        this.resetFormData()
                        this.formLoading = false
                        this.message(res)
                    }).finally(() => {
                        this.formLoading = false
                    })
                } else {
                    this.formLoading = true
                    console.log(this.addForm.formData)
                    // var updData = {
                    //     processId: this.addForm.formData.processId,
                    //     processName: this.addForm.formData.processName,
                    //     systemNo: null,
                    //     systemName: null,
                    //     remark: null,
                    //     processConfigDtlItemVOs: []
                    // }
                    // updData.processConfigDtlItemVOs.push(this.addForm.formData.processConfigDtlItemVOs[0])
                    update(this.addForm.formData).then(res => {
                        this.handleClose()
                        this.message(res)
                    }).catch(() => {
                        this.getProcessInfo()
                    }).finally(() => this.formLoading = false)
                }
            })
        },
        //取消
        handleClose () {
            this.$router.replace('/platform/process/review')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
            document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 重置数据
        resetFormData () {
            this.addForm.selectAddressOptions = []
            this.addForm.formData = {
                productDescribe: null
            }
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        }
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.e-table {
  min-height: auto;
  background: #fff;
}

.upload {
  margin: 20px auto;
  display: flex;
  justify-content: center;
  text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-tabs__content {
  // overflow: hidden;
  &::-webkit-scrollbar {
    width: 0;
  }
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
  display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
  display: none;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 550px;
    margin-top: 0;
  }
}

.main-title {
  font-size: 20px;
  font-weight: bold;
  margin: 24px 0 16px 32px;
}

/* 顶部灰色条 */
.tabs.warningTabs {
  background: #f5f5f5;
  padding: 0;
  min-height: 56px;
  box-sizing: border-box;
}
#tabs-content {
  background: #fff;
  margin-top: 0;
  box-shadow: none;
  border-radius: 0;
  padding-top: 0;
}

/* 主标题 */
.main-title {
  font-size: 16px;
  font-weight: 500;
  margin: 18px 0 18px 24px;
  color: #222;
}

/* 表单区块 */
.form {
  background: #f8f8f8;
  border-radius: 4px;
  margin: 0 0 0 0;
  padding: 24px 0 0 0;
  box-shadow: none;
}

/* 蓝色竖条和节点名 */
.process-label {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 8px;
  margin-left: 0;
  display: flex;
  align-items: center;
}
.blue-bar {
  display: inline-block;
  width: 4px;
  height: 18px;
  background: #1890ff;
  margin-right: 8px;
  border-radius: 2px;
}

/* 表单行和下拉 */
.process-row {
  margin-bottom: 32px;
  margin-left: 24px;
}
.el-form-item {
  margin-bottom: 0;
}
.el-form-item__label {
  width: 60px !important;
  color: #666;
  font-size: 14px;
}
.el-select {
  width: 240px;
}

/* 按钮组 */
/* .buttons {
  position: fixed;
  right: 32px;
  bottom: 24px;
  background: #eafaf6;
  padding: 12px 32px 12px 32px;
  border-radius: 6px;
  display: flex;
  gap: 16px;
  z-index: 10;
  box-shadow: none;
} */
</style>
