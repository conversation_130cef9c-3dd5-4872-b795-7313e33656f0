<template>
  <div>
    <main class="all" v-show="IsVerify">
      <el-header>安全中心</el-header>
      <el-card class="RetrievePassword">
        <div class="boxTop">找回密码</div>
        <div>
          <div style="font-weight: 555;margin-bottom: 15px">手机号码验证</div>
          <!-- <div style="font-weight: 555;margin-bottom: 15px">请填写完整的手机号<span
              style="color:rgb(255,179,53)">1212</span>以验证身份
          </div> -->
          <el-form :model="authCode" ref="phoneLogin" :rules="authCodeRules" :inline="false">
            <el-form-item prop="phone">
              <!-- 手机号输入框 -->
              <el-input v-model="authCode.phone" placeholder="请输入完整的手机号">
                <el-select v-model="select" slot="prepend" placeholder="+86">
                  <el-option label="+86" value="+86"></el-option>
                  <el-option label="+886" value="+886"></el-option>
                  <el-option label="+853" value="+853"></el-option>
                </el-select>
              </el-input>
            </el-form-item>
            <el-form-item prop="code" class="VerificationCodeInput">
              <!-- 验证码输入框 -->
              <el-input class="verification" v-model="authCode.code" placeholder="请输入短信验证码">
              </el-input>
              <div class="verificationbtn" @click="verifyPhone">{{ verifyText }}</div>
            </el-form-item>
          </el-form>
          <button class="nextbtn" @click="next">下一步</button>
          <Dialog title="图形验证码" :close-on-click-modal="false" width="25%" top="30vh"
                  :visible.sync="codeDialogVisible" @open="getCodeImg" class="verification-dialog">
            <div class="verifyBox dfc">
              <el-input v-model="verification.verifyInput" placeholder="请输入图形验证码"/>
              <img class="pointer" :src="verification.verifyImg" @click="getCodeImg" alt="">
            </div>
            <span slot="footer">
                <el-button
                  class="codeDialogBtn" style="margin-right: 10px;" @click="codeDialogVisible = false"
                >取消</el-button>
                <el-button class="codeDialogBtn" type="primary" @click="checkCode">确定</el-button>
            </span>
          </Dialog>
        </div>
      </el-card>
    </main>
    <Newpass v-if="IsNext"></Newpass>
  </div>
</template>

<script>
import Newpass from '@/pages/frontStage/reset/newPass.vue'
import { encrypt } from '@/utils/common.js'
import Dialog from '@/pages/frontStage/components/dialog.vue'
import {
    checkUpdatePassCodeByNoLogin,
    updatePassSendCodeByNoLogin,
    getPrivateKeyId,
    checkSendCodeVerify,
    getSendCodeImg
} from '@/api/frontStage/login'

export default {
    components: {
        Dialog, Newpass
    },
    data () {
        return {
            IsVerify: true,
            IsNext: false,
            select: '',
            loginLoading: false,
            verifyText: '发送验证码',
            authCode: {
                phone: '',
                code: '',
            },
            authCodeRules: {
                phone: [
                    { required: true, message: '请输入手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                code: [
                    { required: true, message: '请输入验证码', trigger: 'blur' },
                ],
            },
            codeDialogVisible: false,
            verification: { verifyImg: '', verifyId: '', verifyInput: '' },
        }
    },
    methods: {
        verifyPhone () {
            if (this.verifyText !== '发送验证码') return
            this.$refs['phoneLogin'].validateField('phone', phoneError => {
                if (phoneError) return
                this.codeDialogVisible = true
            })
        },
        //下一步
        next () {
            this.$refs.phoneLogin.validate(valid=> {
                if (valid) {
                    this.loginLoading = true
                    checkUpdatePassCodeByNoLogin({ phone: this.authCode.phone, code: this.authCode.code }).then(res => {
                        if (res.code === 200) {
                            this.loginLoading = false
                            this.IsVerify = false
                            this.IsNext = true
                            localStorage.setItem('phone', this.authCode.phone)
                            return this.$router.push('/resetPass')
                        }
                        this.loginLoading = false
                    }).catch(() => {
                        this.loginLoading = false
                    })
                }
            })
        },
        getCodeImg () {
            this.verification.verifyInput = ''
            getSendCodeImg().then(res => {
                let blob = new Blob([res.data], { type: 'image/jpeg' })
                this.verification.verifyImg = window.URL.createObjectURL(blob)
                this.verification.verifyId = res.headers.verifyid
            })
        },
        checkCode () {
            let { verifyId, verifyInput } = this.verification
            checkSendCodeVerify({ id: verifyId, verifyInput }).then(async res => {
                if (res.code !== 200) return
                this.codeDialogVisible = false
                let privateKeyId = await this.getPrivateKey(this.authCode.phone)
                if (!privateKeyId) return
                this.loginLoading = true
                updatePassSendCodeByNoLogin({ phone: this.authCode.phone, privateKeyId }).then(res => {
                    if (res.code !== 200) return
                    this.$message.success('发送成功')
                    this.handleCountdown()
                }).finally(() => {
                    this.loginLoading = false
                })
            })
        },
        async getPrivateKey (phone) {
            let res = await getPrivateKeyId({ phone })
            if (typeof res !== 'string' || !res.length > 0) return ''
            return encrypt(res.verification)
        },
        handleCountdown () {
            let countdown = 60
            let timer = setInterval(() => {
                if (countdown === 0) {
                    this.verifyText = '发送验证码'
                    return clearInterval(timer)
                }
                this.verifyText = `倒计时 ${countdown}`
                countdown -= 1
            }, 1000)
        },
    }
}
</script>
<style scoped>
.all {
  background-color: rgb(249, 251, 253);
  width: 100%;
  height: 80%;
}

.el-header {
  width: 100%;
  height: 70px;
  background-color: rgb(57, 117, 225);
  color: white;
  font-size: 20px;
  line-height: 60px;
  padding-left: 30px;
  margin-bottom: 50px;
}

.el-card {
  margin: 0 auto;
  width: 570px;
  height: 500px;
  padding-top: 50px;
  padding-left: 95px;
  padding-right: 95px;
}

.boxTop {
  border-style: solid;
  border-color: blue;
  padding-left: 10px;
  border-right: none;
  border-top: none;
  border-bottom: none;
  margin-bottom: 30px;
  font-size: 20px;
  font-weight: 550;
}

.nextbtn {
  width: 340px;
  height: 45px;
  border-radius: 5px;
  color: white;
  background-color: rgb(0, 155, 237);
}

.VerificationCodeInput {
  padding: 0;
}

.verification {
  width: 260px;
  float: left;
}

.verificationbtn {
  cursor: pointer;
  width: 75px;
  float: left;
  padding-left: 5px;
}
.verification-dialog {
  .el-dialog__footer {
    padding: 30px 0 0 20px;
  }
}
</style>
