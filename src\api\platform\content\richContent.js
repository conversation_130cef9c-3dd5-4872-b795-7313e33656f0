import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/findByConditionByPage',
        params
    })
}

const getNewsListHome = params => {
    return httpPost({
        url: '/materialMall/homePage/news/findByCondition',
        params
    })
}

const getListLog = id => {
    return httpGet({
        url: '/materialMall/platform/richContent/findListByContentId/' + id,
    })
}

const getMoreListHome = params => {
    return httpPost({
        url: '/materialMall/homePage/more/findByCondition',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/richContent/delete',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/updateByPublish',
        params
    })
}

const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/updateNotPublish',
        params
    })
}

const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/deleteBatch',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/updateContentInfo',
        params
    })
}
const savaAndupdateFile = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/savaAndupdateFile',
        params
    })
}

export {
    getList,
    getListLog,
    create,
    edit,
    del,
    batchPublish,
    batchNotPublish,
    batchDelete,
    changeSortValue,
    getNewsListHome,
    getMoreListHome,
    savaAndupdateFile
}
