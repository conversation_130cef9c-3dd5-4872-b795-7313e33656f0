<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-loading="planListLoading">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="addPlanM">新增</el-button>
                            <el-button
                                type="primary" v-show="userInfo.roles.includes('物资大宗月供计划提交权限')"
                                class="btn-greenYellow" @click="batchSubmitPlanM"
                            >提交审核
                            </el-button>
<!--                            因为调用pcwp反写接口，如果批量会出现事务问题-->
                            <el-button type="primary" class="btn-delete" @click="batchDeletePlanM">删除</el-button>
                            <el-button
                                type="warning" v-if="this.userInfo.isMonthPlanAudit == 1" @click="cancellationPlanM"
                            >作废
                            </el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-input
                            @keyup.enter.native="handleInputSearch" placeholder="计划编号、合同编号、供应商名称"
                            v-model="keyword"
                        >
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible=true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="e-table">
                <el-table
                    v-loading="isLoading"
                    class="table"
                    :height="rightTableHeight"
                    ref="tableCurrentRow"
                    @row-click="handleCurrentInventoryClick"
                    @selection-change="selectionChangeHandle"
                    border
                    :data="list"
                >
                    <el-table-column type="selection" :selectable="selectAllHandle" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="300px">
                        <template v-slot="scope">
                            <template >
                                <span v-if="scope.row.state === 2 && userPermission.isSameOrgByEnterpriseId(scope.row.localOrgId)" class="action" @click="planExportM(scope.row)">计划导出</span>
                                <span v-if="scope.row.state === 2 && userPermission.isSameOrgByEnterpriseId(scope.row.localOrgId)  && scope.row.isClose === 0" class="action" @click="alterationClickM(scope.row) ">变更</span>
                                <span v-if="scope.row.state === 2 && userPermission.isSameOrgByEnterpriseId(scope.row.localOrgId) && showDevFunc && closePlan===1&& scope.row.isClose === 0" class="action" @click="selectOrderListByPlanNoM(scope.row)">完结计划</span>
                                <span v-if="(scope.row.state === 0 || scope.row.state === 3) && userPermission.isSameOrgByEnterpriseId(scope.row.localOrgId)" class="action" @click="deletePlanM(scope.row)">删除</span>
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="showDevFunc&&closePlan==1" prop="planNo" label="完结状态" width="100px">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state === 2&&scope.row.isClose==1" type="success">已完结</el-tag>
                            <el-tag v-if="scope.row.state === 2&&scope.row.isClose==0" type="info">未完结</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="changeState" label="变更状态" width="90">
                        <template v-slot="scope">
                            <!--<el-tag type="info" v-if="scope.row.changeState == null">无变更</el-tag>-->
                            <el-tag type="info" v-if="scope.row.changeState === 0">草稿</el-tag>
                            <el-tag v-if="scope.row.changeState === 1">已提交</el-tag>
                            <el-tag type="success" v-if="scope.row.changeState === 2">通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.changeState === 3">未通过</el-tag>
                            <el-tag type="warning" v-if="scope.row.changeState === 4">已作废</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="planNo" label="计划编号" width="300px">
                        <template v-slot="scope">
                            <span class="action" @click="monthPlanDtl(scope.row)">{{ scope.row.planNo }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="planDate" label="计划日期" width="100">
                        <template v-slot="scope">
                            {{ scope.row.planDate | dateStr }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="contractNo" label="合同编号" width="200px"/>
                    <el-table-column prop="supplierName" label="供应商名称"/>
                    <el-table-column prop="orgName" v-if="showDevFunc" label="机构名称"/>
                    <el-table-column prop="state" label="单据状态" width="90">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                            <el-tag v-if="scope.row.state == 1">已提交</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 2">通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 3">未通过</el-tag>
                            <el-tag type="warning" v-if="scope.row.state == 4">已作废</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间"/>
                    <!--                    <el-table-column label="操作" width="60">
                                            <template v-slot="scope">
                                            </template>
                                        </el-table-column>-->
                </el-table>
            </div>
            <pagination
                :currentPage.sync="pagination.currPage" :destination="pagination.destination"
                :pageSize="pagination.pageSize"
                :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange"
                @sizeChange="sizeChange"
            >
            </pagination>
        </div>
        <!--新增计划-->
        <el-dialog
            v-loading="addPlanLoading" top="5vh" title="新增计划" v-dialogDrag :visible.sync="showAddPlanDialog"
            width="70%"
        >
            <div class="dfa mb20">
                <el-button type="primary" @click="selectContractM">选择合同</el-button>
            </div>
            <el-form
                label-width="140px" ref="addPlanFormRoteRef" :model="addPlanForm" :data="addPlanForm"
                :rules="addPlanFormRote"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="计划编号：" prop="planNo">
                            <el-input clearable disabled v-model="addPlanForm.planNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="单据状态：" prop="demandType">
                            <el-tag type="info" v-if="addPlanForm.state == 0">草稿</el-tag>
                            <el-tag type="" v-if="addPlanForm.state == 1">已提交</el-tag>
                            <el-tag type="success" v-if="addPlanForm.state == 2">通过</el-tag>
                            <el-tag type="danger" v-if="addPlanForm.state == 3">未通过</el-tag>
                            <el-tag type="warning" v-if="addPlanForm.state == 4">已作废</el-tag>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="单据机构：" prop="orgName">
                            <el-input disabled v-model="addPlanForm.orgName" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="合同编号：" prop="contractNo">
                            <el-input disabled v-model="addPlanForm.contractNo" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="计划月份：" prop="planDate">
                            <el-date-picker
                                value-format="yyyy-MM"
                                v-model="addPlanForm.planDate"
                                type="month"
                                align="right"
                                :picker-options="pickerOptions"
                                placeholder="选择月"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="供应商：" prop="supplierName">
                            <el-input disabled v-model="addPlanForm.supplierName" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input
                                type="textarea"
                                :auto-resize="false"
                                v-model="addPlanForm.remarks"
                                placeholder="请输入备注" maxlength="1000" show-word-limit
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                <el-tab-pane label="明细列表" name="dtl">
                    <div class="e-table" style="background-color: #ffffff">
                        <el-table
                            border
                            v-loading="contractDtlObj.contractLoading"
                            :data="contractDtlObj.contractDtlList"
                        >
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column prop="ItemName" label="物资名称">
                            </el-table-column>
                            <el-table-column prop="Model" label="规格型号">
                            </el-table-column>
                            <el-table-column prop="Unit" label="计量单位" width="100">
                            </el-table-column>
                            <el-table-column prop="Qty" label="合同数量" width="100">
                            </el-table-column>
<!--                            <el-table-column prop="ReceiveNumber" label="pcwp已消耗数量" width="130">-->
<!--                            </el-table-column>-->
<!--                            <el-table-column prop="useQty" label="已生成计划数量" width="130">-->
<!--                            </el-table-column>-->
<!--                            <el-table-column prop="maxQty" label="未生成计划数量" width="130">-->
<!--                            </el-table-column>-->
                            <el-table-column prop="maxQty" label="剩余未收料数量" width="130">
                            </el-table-column>
                            <el-table-column prop="thisPlanQty" label="选择数量" width="200">
                                <template v-slot="scope">
                                    <el-input-number
                                        v-if="scope.row.Qty != scope.row.useQty"
                                        v-model="scope.row.thisPlanQty"
                                        :min="0" :precision="4" :step="1" :max="scope.row.maxQty"
                                        @change="changePlanDtlRowM(scope.row)"
                                    >
                                    </el-input-number>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!--                        <pagination :currentPage.sync="contractDtlObj.pagination2.currPage" :destination="contractDtlObj.pagination2.destination" :pageSize="contractDtlObj.pagination2.pageSize"-->
                        <!--                                    :total="contractDtlObj.pagination2.totalNum" :totalPage="contractDtlObj.pagination2.totalPage" @currentChange="currentChange3" @sizeChange="sizeChange3">-->
                        <!--                        </pagination>-->
                    </div>
                </el-tab-pane>
            </el-tabs>
            <span slot="footer">
                <el-button  v-if="closePlan===1&&showDevFunc" type="primary" class="btn-greenYellow" @click="getSelePlanListM(addPlanForm.contractNo)">查询计划</el-button>
                <el-button type="primary" class="btn-greenYellow" @click="savePlanM">保存</el-button>
                <el-button
                    v-show="userInfo.roles.includes('物资大宗月供计划提交权限')" type="primary" class="btn-greenYellow"
                    @click="savePlanM(1)"
                >保存并提交</el-button>
                <el-button @click="showAddPlanDialog = false">取消</el-button>
            </span>
        </el-dialog>
        <!--合同列表-->
        <el-dialog
            v-dialogDrag title="合同列表" :visible.sync="showSelectContractDialog" width="80%"
            :close-on-click-modal="false"
        >
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="search_box" style="margin-right: 60%">
                        <el-input
                            clearable type="text"
                            @blur="getPCWP1BuyContractByKeyWord"
                            placeholder="合同名称、供应商名称、合同编号"
                            v-model="contractObj.keyword"
                        >
                            <img
                                :src="require('@/assets/search.png')" slot="suffix"
                                @click="getPCWP1BuyContractByKeyWord" alt=""
                            />
                        </el-input>
                    </div>
                </div>
            </div>
            <div class="e-table">
                <el-table
                    border
                    v-loading="contractObj.contractLoading"
                    :data="contractObj.contractList"
                    height="500"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column prop="" label="操作" width="100">
                        <template v-slot="scope">
                            <div
                                class="pointer" style="color: rgba(33, 110, 198, 1);"
                                @click="contractRowClick(scope.row)"
                            >选择合同
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="BillNo" label="合同编号" width="160"/>
                    <el-table-column prop="Name" label="合同名称"/>
                    <el-table-column prop="BName" label="供应商" width="360"/>
                    <el-table-column prop="BillDate" label="签订日期" width="160">
                        <template v-slot="scope">
                            {{ scope.row.BillDate | dateStr2 }}
                        </template>
                    </el-table-column>
                </el-table>
                <!--            <pagination :currentPage.sync="contractObj.pagination2.currPage" :destination="contractObj.pagination2.destination" :pageSize="contractObj.pagination2.pageSize"-->
                <!--                        :total="contractObj.pagination2.totalNum" :totalPage="contractObj.pagination2.totalPage" @currentChange="currentChange2" @sizeChange="sizeChange2">-->
                <!--            </pagination>-->
                <!--                <div class="dfa" style="justify-content: flex-end">
                                    <el-button  @click="showSelectContractDialog = false">取消</el-button>
                                </div>-->
            </div>
            <span slot="footer">
                <el-button @click="showSelectContractDialog = false">取消</el-button>
            </span>
        </el-dialog>
        <!--        高级查询-->
        <el-dialog title="高级查询" id="filterDataDi" v-dialogDrag :visible.sync="queryVisible" width="45%">
            <el-form style="margin-top: 50px" :model="filterData" label-width="160px" ref="form" :inline="true">
                <el-row>
                    <el-form-item label="计划编号：">
                        <el-input
                            clearable maxlength="100" placeholder="请输入计划编号" v-model="filterData.planNo"
                        ></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="合同编号：">
                        <el-input
                            clearable maxlength="100" placeholder="请输入合同编号" v-model="filterData.contractNo"
                        ></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="供应商名称：">
                        <el-input
                            clearable maxlength="100" placeholder="请输入供应商名称" v-model="filterData.supplierName"
                        ></el-input>
                    </el-form-item>
                </el-row>
                <el-row v-if="showDevFunc">
                    <el-form-item label="完结状态：">
                        <el-select :value="filterData.isClose" @change="dataSourceChange">
                            <el-option label="已完结" :value="1"/>
                            <el-option label="未完结" :value="0"/>
                        </el-select>
                    </el-form-item>
                </el-row>
                <el-row v-if="showDevFunc">
                    <el-form-item label="数据权限：">
                        <el-select :value="dataSource" @change="dataSourceChange">
                            <el-option label="全部机构" :value="0"/>
                            <el-option label="本机构" :value="1"/>
                            <el-option v-if="userPermission.hasSubOrg()" label="下级机构" :value="2"/>
                            <el-option v-for="item in userPermission.subOrg"
                                :label="item.orgName" :value="item.orgId" :key="item.orgId"
                            />
                        </el-select>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="计划时间：">
                        <el-date-picker
                            v-model="filterData.planDate"
                            type="monthrange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            align="center"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始月份"
                            end-placeholder="结束月份"
                            :picker-options="pickerOptionsQuery"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="单据状态：">
                        <el-checkbox
                            style="margin-right: 3px" v-model="filterData.stateCheckAll" @change="stateAllSelect"
                        >全部
                        </el-checkbox>
                        <el-checkbox-group v-model="filterData.state" @change="stateGroupChange">
                            <el-checkbox
                                :label="item.value" v-for="(item, index) in filterData.stateOptions" :key="index"
                            >
                                {{ item.label }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
        <!--变更-->
        <el-dialog
            v-loading="updatePlanLoading" title="变更信息"
            v-dialogDrag
            :visible.sync="showAlterationPlanDialog" width="80%" :close-on-click-modal="false"
        >
            <!--            <div class="dfa mb20">-->
            <!--                <el-button type="primary" class="btn-greenYellow" @click="saveChangePlanM">保存</el-button>-->
            <!--                <el-button type="primary" class="btn-greenYellow" @click="saveChangePlanM(1)">保存并提交</el-button>-->
            <!--            </div>-->
            <el-form
                label-width="140px" ref="addChangePlanFormRoteRef" :model="updateChangePlanForm"
                :data="updateChangePlanForm" :rules="addChangePlanFormRote"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="变更计划编号：" prop="planChangeNo">
                            <el-input disabled v-model="updateChangePlanForm.planChangeNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="单据状态：" prop="demandType">
                            <el-tag type="info" v-if="updateChangePlanForm.state == 0">草稿</el-tag>
                            <el-tag type="" v-if="updateChangePlanForm.state == 1">已提交</el-tag>
                            <el-tag type="success" v-if="updateChangePlanForm.state == 2">通过</el-tag>
                            <el-tag type="danger" v-if="updateChangePlanForm.state == 3">未通过</el-tag>
                            <el-tag type="warning" v-if="updateChangePlanForm.state == 4">已作废</el-tag>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="">
                    <el-col :span="12">
                        <el-form-item label="计划编号：" prop="planNo">
                            <el-input clearable disabled v-model="updateChangePlanForm.planNo"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="单据机构：" prop="orgName">
                            <el-input disabled v-model="updateChangePlanForm.orgName" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="合同编号：" prop="contractNo">
                            <el-input disabled v-model="updateChangePlanForm.contractNo" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="计划月份：" prop="planDate">
                            <el-date-picker
                                value-format="yyyy-MM"
                                v-model="updateChangePlanForm.planDate"
                                type="month"
                                align="right"
                                placeholder="选择月"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="供应商：" prop="supplierName">
                            <el-input disabled v-model="updateChangePlanForm.supplierName" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="14">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input
                                type="textarea" :auto-resize="false" v-model="updateChangePlanForm.remarks"
                                placeholder="请输入备注" maxlength="1000" show-word-limit
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                <el-tab-pane label="明细列表" name="dtl">
                    <div class="e-table" style="background-color: #ffffff">
                        <el-table
                            max-height="372px"
                            border
                            v-loading="alterationInfoDtlLoading"
                            :data="updateChangePlanForm.dtls"
                            style="min-height: 372px"
                            :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                            :row-style="{ fontSize: '14px', height: '48px' }"
                        >
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column prop="materialName" label="物资名称">
                            </el-table-column>
                            <el-table-column prop="spec" label="规格型号">
                            </el-table-column>
                            <el-table-column prop="unit" label="计量单位" width="100">
                            </el-table-column>
                            <el-table-column prop="sourceQty" label="合同数量" width="140">
                            </el-table-column>
                            <el-table-column prop="maxQty" label="剩余未收料数量" width="140">
                            </el-table-column>
                            <el-table-column prop="oldThisPlanQty" label="源计划数量" width="100">
                            </el-table-column>
                            <el-table-column prop="orderQty" label="已下单数量" width="120">
                            </el-table-column>
                            <el-table-column label="操作状态" width="140">
                                <template slot-scope="scope">
                                    <el-tag type="success" v-if="scope.row.dtlUpdateState === 1">新增</el-tag>
                                    <el-tag type="warning" v-if="scope.row.dtlUpdateState === 2">修改</el-tag>
                                    <el-tag type="danger" v-if="scope.row.dtlUpdateState === 3">删除</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="thisPlanQty" label="选择变更数量" width="140">
                                <template v-slot="scope">
                                    <el-input
                                        type="number"
                                        size="mini" v-model="scope.row.thisPlanQty"
                                        :precision="4" :step="1" :max="scope.row.oldThisPlanQty + scope.row.maxQty"
                                        @change="changeSelectQtyM(scope.row)"
                                    >
                                    </el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                                <template slot-scope="scope">
                                    <span
                                        v-if="scope.row.dtlUpdateState !== 3" class="pointer"
                                        style="color: rgb(176,5,5); margin-left: 20px"
                                        @click="tableDeleteClick(scope.row)"
                                    >删除</span>
                                    <span
                                        v-if="scope.row.planDtlId != null && scope.row.dtlUpdateState === 3"
                                        class="pointer" style="color: rgb(255,179,15); margin-left: 20px"
                                        @click="tableUpdateClick(scope.row)"
                                    >标记修改</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <span slot="footer">
                <el-button class="btn-blue" @click="addPlanDtil">追加明细</el-button>
                <el-button type="primary" class="btn-greenYellow" @click="saveChangePlanM">保存</el-button>
                <el-button
                    type="primary" v-show="userInfo.roles.includes('物资大宗月供计划提交权限')" class="btn-greenYellow"
                    @click="saveChangePlanM(1)"
                >保存并提交</el-button>
                <el-button @click="showAlterationPlanDialog = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog
            title="选择合同明细" v-dialogDrag v-loading="addPlanDialogLoading" :visible.sync="showAddPlanNewDialog"
            width="70%"
        >
            <div class="e-table" style="background-color: #ffffff">
                <el-table
                    class="table"
                    ref="siteReceivingTableRef"
                    border
                    @selection-change="siteReceivingTableSelectM"
                    @row-click="siteReceivingTableRowClickM"
                    :data="changeContractDtlList"
                >
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="ItemName" label="物资名称">
                    </el-table-column>
                    <el-table-column prop="Model" label="规格型号">
                    </el-table-column>
                    <el-table-column prop="Unit" label="计量单位" width="100">
                    </el-table-column>
                    <el-table-column prop="Qty" label="数量" width="100">
                    </el-table-column>
                    <el-table-column prop="useQty" label="已生成计划数量" width="130">
                    </el-table-column>
                    <el-table-column prop="maxQty" label="未生成计划数量" width="130">
                    </el-table-column>
                    <el-table-column prop="thisPlanQty" label="选择数量" width="160">
                        <template v-slot="scope">
                            <el-input-number
                                v-if="scope.row.Qty != scope.row.useQty"
                                size="mini" v-model="scope.row.thisPlanQty"
                                :min="0" :precision="4" :step="1" :max="scope.row.maxQty"
                            >
                            </el-input-number>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <el-button type="primary" class="btn-blue" @click="selectPlanList">确认选择</el-button>
                <el-button @click="showAddPlanNewDialog = false">取消</el-button>
            </span>
        </el-dialog>

<!--        订单列表1-->
        <el-dialog class="orderDialog"
            v-dialogDrag title="订单列表" :visible.sync="showOrderDialog" width="60%"
            :close-on-click-modal="false"
        >
            <div class="e-table">
                <el-table
                    border
                    v-loading="ordersListLoading"
                    :data="ordersList"
                    height="500"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column prop="orderSn" label="订单编号" width="200">
                        <template v-slot="scope">
                            <span class="action" @click="openOrders(scope.row)">{{ scope.row.orderSn }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="untitled" label="订单名称" width="200"/>
                    <el-table-column label="状态" prop="state">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.state == 0" type="info">草稿</el-tag>
                            <el-tag v-if="scope.row.state == 1">已提交</el-tag>
                            <el-tag v-if="scope.row.state == 2">待确认</el-tag>
                            <el-tag v-if="scope.row.state == 3">已确认</el-tag>
                            <el-tag v-if="scope.row.state == 4">待签订合</el-tag>
                            <el-tag v-if="scope.row.state == 5">已签合同</el-tag>
                            <el-tag v-if="scope.row.state == 6">待发货</el-tag>
                            <el-tag v-if="scope.row.state == 7">已关闭</el-tag>
                            <el-tag  v-if="scope.row.state == 8">发货中</el-tag>
                            <el-tag type="waring" v-if="scope.row.state == 9">待收货</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 10">已完成</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" prop="gmtCreate" width="160"/>
                    <el-table-column label="完成时间" prop="successDate" width="160"/>
                </el-table>
                <!--            <pagination :currentPage.sync="contractObj.pagination2.currPage" :destination="contractObj.pagination2.destination" :pageSize="contractObj.pagination2.pageSize"-->
                <!--                        :total="contractObj.pagination2.totalNum" :totalPage="contractObj.pagination2.totalPage" @currentChange="currentChange2" @sizeChange="sizeChange2">-->
                <!--            </pagination>-->
                <!--                <div class="dfa" style="justify-content: flex-end">
                                    <el-button  @click="showSelectContractDialog = false">取消</el-button>
                                </div>-->
            </div>
            <span slot="footer">
                <el-button @click="showOrderDialog = false">取消</el-button>
                <el-button type="danger" @click="endPlanM">确定完结</el-button>
            </span>
        </el-dialog>

        <el-dialog
            v-dialogDrag title="计划列表" :visible.sync="showPlanDialog" width="60%"
            :close-on-click-modal="false"
        >
                        <div class="e-table" style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="search_box" style="margin-right: 60%">
                                    <el-input
                                        clearable type="text"
                                        @blur="getSectionalPlanListM"
                                        placeholder="计划编号"
                                        v-model="keyword3"
                                    >
                                        <img
                                            :src="require('@/assets/search.png')" slot="suffix"
                                            @click="getSectionalPlanListM" alt=""
                                        />
                                    </el-input>
                                </div>
                            </div>
                        </div>
            <div class="e-table">
                <el-table
                    border
                    v-loading="closePlanLoading"
                    :data="closePlanDate"
                    height="500"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="操作" width="90px">
                        <template v-slot="scope">
                                <span v-if="scope.row.state === 2 && showDevFunc&& closePlan===1&& scope.row.isClose === 0"
                                      class="action" @click="selectOrderListByPlanNoM(scope.row)">完结计划</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="showDevFunc&&closePlan==1" prop="isClose" label="完结状态" width="100px">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state === 2&&scope.row.isClose==1" type="success">已完结</el-tag>
                            <el-tag v-if="scope.row.state === 2&&scope.row.isClose==0" type="info" >未完结</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="planNo" label="计划编号" width="300px"/>
                    <el-table-column prop="planDate" label="计划日期" width="">
                        <template v-slot="scope">
                            {{ scope.row.planDate | dateStr }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="state" label="单据状态" width="90">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                            <el-tag v-if="scope.row.state == 1">已提交</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 2">通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 3">未通过</el-tag>
                            <el-tag type="warning" v-if="scope.row.state == 4">已作废</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" prop="gmtCreate" width="160"/>
                </el-table>
                            <pagination :currentPage.sync="planPagination.currPage"
                                        :destination="planPagination.destination"
                                        :pageSize="planPagination.pageSize"
                                        :total="planPagination.totalNum"
                                        :totalPage="planPagination.totalPage"
                                        @currentChange="currentChangePlan2" @sizeChange="sizeChangePlan2">
                            </pagination>
            </div>
            <span slot="footer">
                <el-button @click="showPlanDialog = false">取消</el-button>

            </span>
        </el-dialog>

    </div>
</template>

<script>

import pagination from '@/components/pagination/pagination.vue'
import { UserPermission } from '@/utils/permissions'

import { mapState } from 'vuex'
import {
    deletePlanInfo,
    batchDeletePlan,
    batchSubmitPlan,
    cancellationPlan,
    checkTotalNum,
    createChangePlanAndPlanDtl,
    createPlanAndPlanDtl,
    getPCWP2BuyContract,
    getPCWP2BuyContractDtl,
    getPlanDtlInfoByPlanNo,
    materialMonthSupplyPlanList, planExport, closeMaterialMonthSupplyPlanByPlanNo
} from '@/api/plan/plan'
import {
    selectOrderListByPlanNo
} from '@/api/reconciliation/reconciliation'
import { getUuid } from '@/utils/common'
// import { selectOrderList } from '@/api/platform/order/orders'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            let newDateSr = dateStr.split('-')
            // let day = newDateSr[2].split(' ')[0]
            return newDateSr[0] + '年' + newDateSr[1] + '月'
        },
        dateStr2 (dateStr) {
            if (dateStr == null) {
                return
            }
            return dateStr.split('T')[0]
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    components: { pagination },
    name: 'index',
    data () {
        return {
            contractId: '', //查询新增计划合同的所有计划
            closePlanLoading: false,
            showPlanDialog: false,
            closePlanDate: [],
            keyword2: null, //查询订单信息
            keyword3: null, //查询计划完结
            contractNo: null, //订单完结后更新计划列表
            planPagination: {
                currPage: 1,
                destination: null,
                pageSize: 20,
                totalNum: 10,
                totalPage: 1,
            },
            planNo: '',
            ordersList: [], //未完结订单数
            showOrderDialog: false,
            ordersListLoading: false,
            userPermission: new UserPermission,
            dataSource: 0,
            addPlanDialogLoading: false,
            showAddPlanNewDialog: false,
            changeContractDtlList: [],
            pcwpVersion: null,
            screenHeight: 0,
            isLoading: false,
            // 变更数据
            alterationInfoDtlLoading: false,
            showAlterationPlanDialog: false,
            updatePlanLoading: false,
            pickerOptionsQuery: {
                shortcuts: [{
                    text: '本月',
                    onClick (picker) {
                        picker.$emit('pick', [new Date(), new Date()])
                    }
                }, {
                    text: '今年至今',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date(new Date().getFullYear(), 0)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近六个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setMonth(start.getMonth() - 6)
                        picker.$emit('pick', [start, end])
                    }
                }]
            },
            queryVisible: false,
            filterData: {
                supplierName: null,
                isClose: null,
                planNo: null,
                contractNo: null,
                planDate: [], // 修改时间
                stateCheckAll: false, // 选择全局
                state: [],
                stateOptions: [{
                    value: 0,
                    label: '草稿'
                }, {
                    value: 1,
                    label: '已提交'
                }, {
                    value: 2,
                    label: '通过'
                }, {
                    value: 3,
                    label: '未通过'
                }, {
                    value: 4,
                    label: '已作废'
                }],
            },
            // 选中的数据
            selectPlanListData: [],
            currentBillId: null, // 合同id
            changePlanDtlRowDate: [],
            addPlanLoading: false,
            addChangePlanFormRote: {
                planChangeNo: [
                    { required: true, message: '请输入变更计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                planNo: [
                    { required: true, message: '请输入计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                contractNo: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                planDate: [
                    { required: true, message: '请选择计划日期', trigger: 'blur' },
                ],
                supplierName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            addPlanFormRote: {
                planNo: [
                    { required: true, message: '请输入计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                contractNo: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                planDate: [
                    { required: true, message: '请选择计划日期', trigger: 'blur' },
                ],
                supplierName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            showSelectContractDialog: false,
            activeName: 'dtl',
            pickerOptions: {
                // disabledDate (time) {
                //     return time.getTime() < Date.now()
                // },
            },
            addPlanForm: {
                planNo: null,
                planDate: null,
                businessType: 0,
                contractId: null,
                contractNo: null,
                supplierId: null,
                supplierName: null,
                orgId: null,
                orgName: null,
                state: 0,
                remarks: null,
                isSubmit: 0,
                dtls: [],
            },
            addPlanDtilList: [],
            showAddPlanDialog: false,
            contractObj: {
                contractLoading: false,
                contractList: [], // 合同列表
                pagination2: {
                    currPage: 1,
                    destination: null,
                    pageSize: 10,
                    totalNum: 10,
                    totalPage: 1,
                },
                keyword: null,
            },

            contractDtlObj: {
                contractLoading: false,
                contractDtlList: [], // 合同明细
                pagination2: {
                    currPage: 1,
                    destination: null,
                    pageSize: 10,
                    totalNum: 10,
                    totalPage: 1,
                },
            },
            planListLoading: false,
            keyword: null,
            pagination: {
                currPage: 1,
                destination: null,
                pageSize: 20,
                totalNum: 10,
                totalPage: 1,
            },
            list: [],
            changeQtyRowDate: [],
            updateChangePlanForm: {},
        }
    },
    created () {
        this.getPlanListM()
    },
    mounted () {
        this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
    },
    methods: {
        openOrders (row) {
            this.$router.push({ path: '/performanceManage/synthesisMaterialDetail', query: { orderSn: row.orderSn } })

        },
        sizeChangePlan2 (index) {
            this.planPagination.pageSize = index
            this.getSectionalPlanListM()
        },
        currentChangePlan2 (index) {
            this.planPagination.currPage = index
            this.getSectionalPlanListM()
        },
        endPlanM () {
            this.clientPop('info', '您确定要完结计划编号为' + this.planNo + '吗？', async () => {
                if (this.ordersList.length > 0) {
                    const  noEndOrders = this.ordersList.filter(orders=>orders.state < 10)
                    if (noEndOrders.length > 0) {
                        const orderNo = noEndOrders.map(item => item.orderSn).join(',')
                        this.$message.warning('订单' + orderNo + '没有完成，不能完结计划')
                        this.getPlanListM()
                    }else {
                        closeMaterialMonthSupplyPlanByPlanNo({ planNo: this.planNo } ).then(res=>{
                            if (res.code == 200) {
                                this.$message.success('计划完结成功')
                            }
                            this.getSectionalPlanListM()
                            this.getPlanListM()
                            this.showOrderDialog = false
                        })
                    }

                }

            })

        },
        dataSourceChange (state) {
            this.dataSource = state
            if(state === 0) {
                this.userPermission.getAllOrgData()
            }else if(state === 1) {
                this.userPermission.getHostOrgData()
            }else if(state === 2) {
                this.userPermission.getSubOrgData()
            }else if(state.length > 1) {
                this.userPermission.getSubOrgData(state)
            }
        },
        selectPlanList () {
            if (this.addPlanDtilList.length == 0) {
                return this.$message.error('未勾选数据！')
            }
            for (let i = 0; i < this.addPlanDtilList.length; i++) {
                let t = this.addPlanDtilList[i]
                if (t.thisPlanQty == null || t.thisPlanQty == 0) {
                    return this.$message.error(t.ItemName + '选择数量不能为空！')
                }
            }
            for (let i = 0; i < this.updateChangePlanForm.dtls.length; i++) {
                let it = this.updateChangePlanForm.dtls[i]
                for (let j = 0; j < this.addPlanDtilList.length; j++) {
                    let jt = this.addPlanDtilList[j]
                    if (jt.ItemID == it.materialId && it.dtlUpdateState != 3) {
                        return this.$message.error(jt.ItemName + '已存在列表当中！请修改变更数量即可！')
                    }
                }
            }
            for (let j = 0; j < this.addPlanDtilList.length; j++) {
                let row = this.addPlanDtilList[j]
                this.updateChangePlanForm.dtls.push({
                    materialId: row.ItemID,
                    classPathName: row.MaterialClassName,
                    classPathId: row.MaterialClassld,
                    materialName: row.ItemName,
                    spec: row.Model,
                    unit: row.Unit,
                    texture: row.Spec,
                    thisPlanQty: row.thisPlanQty,
                    sourceQty: row.Qty,
                    contractDtlId: row.DtlId,
                    maxQty: row.maxQty,
                    dtlUpdateState: 1,
                    thisTrueQty: 0,
                    oldThisPlanQty: 1,
                    orderQty: 0,
                    planChangeId: this.updateChangePlanForm.planChangeId,
                    planId: this.updateChangePlanForm.planId
                })
            }
            this.showAddPlanNewDialog = false
        },
        siteReceivingTableSelectM (value) {
            this.addPlanDtilList = value
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        addPlanDtil () {
            this.showAddPlanNewDialog = true
            let params = {
                params: {
                    billid: this.updateChangePlanForm.contractId,
                    version: this.updateChangePlanForm.pcwpVersion
                }
            }
            this.addPlanDialogLoading = true
            getPCWP2BuyContractDtl(params).then(res => {
                let resultList = res
                if (resultList.length != 0) {
                    checkTotalNum(resultList).then(res => {
                        this.changeContractDtlList = res
                    }).finally(() => {
                        this.addPlanDialogLoading = false
                    })
                } else {
                    this.addPlanDialogLoading = false
                }
            })
        },
        tableUpdateClick (row) {
            if (row.planDtlId != null && row.dtlUpdateState === 3) {
                // 如果列表存在相同明细不可标记修改
                for (let i = 0; i < this.updateChangePlanForm.dtls.length; i++) {
                    let t = this.updateChangePlanForm.dtls[i]
                    if (t.materialId === row.materialId && t.dtlUpdateState !== 3) {
                        return this.$message.error('已存在相同明细，不可标记修改！')
                    }
                }
                row.dtlUpdateState = 2
            }
        },
        tableDeleteClick (row) {
            if (row.orderQty > 0) {
                return this.$message.error('单据已下单不能删除！')
            }
            if (row.dtlUpdateState == 2) {
                this.clientPop('info', '您确定要标记变更为删除吗？', async () => {
                    return row.dtlUpdateState = 3
                })
            }
            // 原单id没有说明是新增
            if (row.planDtlId == null) {
                let newArr = this.updateChangePlanForm.dtls.filter(t => {
                    if (t.materialId != row.materialId) {
                        return true
                    } else {
                        return false
                    }
                })
                this.updateChangePlanForm.dtls = newArr
            }
        },
        saveChangePlanM (isSubmit = null) {
            this.$refs.addChangePlanFormRoteRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定进行该操作吗！', async () => {
                        // 去除审核信息
                        this.updatePlanLoading = true
                        this.updateChangePlanForm.auditList = []
                        if (isSubmit != null && isSubmit === 1) {
                            this.updateChangePlanForm.isSubmit = 1
                        } else {
                            this.updateChangePlanForm.isSubmit = 0
                        }
                        for (let i = 0; i < this.updateChangePlanForm.dtls.length; i++) {
                            let t = this.updateChangePlanForm.dtls[i]
                            if (t.thisPlanQty == null) {
                                return this.$message.error(t.materialName + '变更数量不能为空！')
                            }
                        }
                        createChangePlanAndPlanDtl(this.updateChangePlanForm).then(res => {
                            if (res.code !== 200) {
                                this.clientPop('error', res.message, () => {
                                })
                            } else {
                                this.clientPop('suc', '操作成功', () => {
                                    this.getPlanListM()
                                    this.showAlterationPlanDialog = false
                                })
                            }
                        }).finally(() => {
                            this.updatePlanLoading = false
                        })
                    })
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
        // 变更数量
        changeSelectQtyM (row) {
            if (row.changeQty == null) {
                row.changeQty = row.orderQty
            }
            if (row.thisPlanQty < row.orderQty) {
                this.$message.error('变更数量不能小于下单数量！')
                row.thisPlanQty = row.orderQty
            }
            if (row.thisPlanQty > (row.oldThisPlanQty + row.maxQty)) {
                this.$message.error('数量超过可变更数量！')
                row.thisPlanQty = row.oldThisPlanQty + row.maxQty
            }
        },
        //根据计划编号查询订单信息
        selectOrderListByPlanNoM (row) {
            if (row.isClose == 1) {
                this.$message.warning('计划已经完结，不许要重复完结')
                return
            }
            this.planNo = row.planNo
            this.ordersListLoading = true
            let params = {
                limit: 100,
                page: 1,
                planNo: row.planNo,
                productType: 12
            }
            selectOrderListByPlanNo(params).then(res=>{
                this.ordersListLoading = false
                this.showOrderDialog = true
                this.ordersList = res
            })
        },
        planExportM (row) {
            this.clientPop('info', '您确定要导出计划数据？', async () => {
                this.tableLoading = true
                planExport({ id: row.planId }).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '物资月度供应计划表.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.currentQuery.ids = []
                    this.dataListSelections = []
                    this.$message.success('物资月度供应计划导出操作成功')
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },

        // 变更方法
        alterationClickM ({ planNo, changeState, planChangeNo }) {
            // 如果变更状态为null，说明是新增变更
            if (changeState == null) {
                this.showAlterationPlanDialog = true
                this.updatePlanLoading = true
                // 如果是计划编号
                getPlanDtlInfoByPlanNo({ planNo }).then(res => {
                    this.updateChangePlanForm = res
                    this.updateChangePlanForm.state = 0
                    this.updateChangePlanForm.planChangeNo = getUuid().replaceAll('-', '')
                }).finally(() => {
                    this.updatePlanLoading = false
                })
            } else {
                // 前往变更页面
                this.$router.push({
                    path: '/performanceManage/updateChangeDetail',
                    name: 'performanceManageUpdateChangeDetail',
                    query: { planChangeNo }
                })
            }
        },
        resetSearchConditions () {
            this.filterData.planNo = null
            this.filterData.contractNo = null
            this.filterData.supplierName = null
            this.filterData.state = []
            this.filterData.planDate = []
            this.dataSource = 0
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getPlanListM()
        },
        // 高级搜索确认
        confirmSearch () {
            this.keyword = ''
            this.getPlanListM()
            this.queryVisible = false
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateOptions.length
        },
        stateAllSelect (value) {
            if (value) {
                this.filterData.state = this.filterData.stateOptions.map(t => t.value)
            } else {
                this.filterData.state = []
            }
        },
        // 月供计划明细
        monthPlanDtl ({ planNo }) {
            this.$router.push({
                path: '/performanceManage/monthPlanDtl',
                name: 'performanceManageMonthPlanDtl',
                query: { planNo }
            })
        },
        // 批量作废
        cancellationPlanM () {
            if (this.selectPlanListData.length === 0) {
                return this.clientPop('warning', '请勾选已提交数据')
            }
            let ids = this.selectPlanListData.filter(t => {
                return t.state == 1
            }).map(item => item.planId)
            if (ids.length === 0) {
                return this.clientPop('warning', '请勾选已提交数据')
            }
            this.clientPop('info', '您确定要批量作废这些数据吗？', async () => {
                this.planListLoading = true
                cancellationPlan(ids).then(res => {
                    if (res.code != null && res.code === 200) {
                        this.$message.success('操作成功')
                        this.getPlanListM()
                    }
                }).finally(() => {
                    this.planListLoading = false
                })
            })
        },
        batchDeletePlanM () {
            if (this.selectPlanListData.length === 0) {
                return this.clientPop('warning', '请勾选草稿状态的数据')
            }
            let ids = this.selectPlanListData.filter(t => {
                return t.state == 0
            }).map(item => item.planId)
            if (ids.length === 0) {
                return this.clientPop('warning', '请勾选草稿状态的数据')
            }
            this.clientPop('info', '您确定要批量删除这些数据吗？', async () => {
                this.planListLoading = true
                batchDeletePlan(ids).then(res => {
                    if (res.code != null && res.code === 200) {
                        this.$message.success('操作成功')
                        this.getPlanListM()
                    }
                }).finally(() => {
                    this.planListLoading = false
                })
            })
        },
        deletePlanM (row) {
            this.clientPop('info', '您确定要删除这条数据吗？', async () => {
                this.planListLoading = true
                deletePlanInfo({ planId: row.planId }).then(res => {
                    if (res.code != null && res.code === 200) {
                        this.$message.success('操作成功')
                        this.getPlanListM()
                    }
                }).finally(() => {
                    this.planListLoading = false
                })
            })
        },
        // 批量提交
        batchSubmitPlanM () {
            if (this.selectPlanListData.length === 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let ids = this.selectPlanListData.filter(t => {
                return [0, 3].includes(t.state)
            }).map(item => item.planId)
            if (ids.length === 0) {
                return this.$message.warning('请勾选有效的数据！')
            }
            this.clientPop('info', '您确定要批量提交审核这些数据吗？', async () => {
                this.planListLoading = true
                batchSubmitPlan(ids).then(res => {
                    if (res.code != null && res.code === 200) {
                        this.$message.success('操作成功')
                        this.getPlanListM()
                    }
                }).finally(() => {
                    this.planListLoading = false
                })
            })
        },
        changePlanDtlRowM (row) {
            if (row.thisPlanQty == null) {
                row.thisPlanQty = 0
            }
            if (this.changePlanDtlRowDate.length === 0) {
                this.changePlanDtlRowDate.push({
                    contractDtlId: row.DtlId,
                    materialName: row.ItemName,
                    materialId: row.ItemID,
                    classPathId: row.MaterialClassld,
                    classPathName: row.MaterialClassName,
                    spec: row.Model,
                    unit: row.Unit,
                    texture: row.Spec,
                    sourceQty: row.Qty,
                    thisPlanQty: row.thisPlanQty
                })
                return
            }
            let flag = false
            this.changePlanDtlRowDate.forEach(t => {
                if (t.contractDtlId == row.DtlId) {
                    t.thisPlanQty = row.thisPlanQty
                    flag = true
                }
            })
            if (!flag) {
                this.changePlanDtlRowDate.push({
                    contractDtlId: row.DtlId,
                    materialName: row.ItemName,
                    materialId: row.ItemID,
                    classPathId: row.MaterialClassld,
                    classPathName: row.MaterialClassName,
                    spec: row.Model,
                    texture: row.Spec,
                    unit: row.Unit,
                    sourceQty: row.Qty,
                    thisPlanQty: row.thisPlanQty
                })
            }
        },
        // 保存计划
        savePlanM (isSubmit) {
            let newDtlArr = this.changePlanDtlRowDate.filter(t => {
                return t.thisPlanQty !== 0
            })
            this.$refs.addPlanFormRoteRef.validate(valid => {
                if (valid) {
                    if (newDtlArr.length === 0) {
                        return this.$message.error('未选择明细数量！')
                    }
                    this.clientPop('info', '您确定进行该操作吗！', async () => {
                        this.addPlanLoading = true
                        this.addPlanForm.dtls = newDtlArr
                        if (isSubmit != null && isSubmit == 1) {
                            this.addPlanForm.isSubmit = 1
                        } else {
                            this.addPlanForm.isSubmit = 0
                        }
                        // 保存并提交
                        createPlanAndPlanDtl(this.addPlanForm).then(res => {
                            if (res.code !== 200) {
                                this.clientPop('error', res.message)
                            } else {
                                // let  submit = this.addPlanForm.isSubmit
                                // let contractNo = this.addPlanForm.contractNo
                                this.clientPop('suc', '操作成功', () => {
                                    // 重置
                                    this.restartPlanFormM()
                                    this.addPlanForm.planNo = getUuid().replaceAll('-', '')
                                    this.contractDtlObj.contractDtlList = []
                                    this.showAddPlanDialog = false
                                    this.getPlanListM()
                                    // 查询计划列表
                                    // this.getPlanListM()
                                    // if (this.showDevFunc && this.closePlan == 1 && submit == 1) {
                                    //     this.showPlanDialog = true
                                    //     this.getSectionalPlanListM(contractNo)
                                    // }
                                })

                            }

                        }).finally(() => {
                            this.addPlanLoading = false
                            this.this.getPlanListM()
                        })
                        this.this.getPlanListM()
                    })
                } else {
                    this.$message.error('请检查非空输入框')
                }
            })
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.selectPlanListData = val
        },
        // 全选屏蔽下级数据
        selectAllHandle (row) {
            if (!this.userPermission.isSameOrgByEnterpriseId(row.localOrgId)) {
                return false
            }
            else {
                return true
            }
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            // 非本机构的数据不可选
            row.flag = !row.flag
            if (!this.userPermission.isSameOrgByEnterpriseId(row.localOrgId)) {
                this.$refs.tableCurrentRow.toggleRowSelection(row, false)
            }else{
                this.$refs.tableCurrentRow.toggleRowSelection(row, row.flag)
            }
        },
        // 合同点击
        contractRowClick (row) {
            this.pcwpVersion = row.version
            this.addPlanForm.pcwpVersion = row.version
            this.addPlanForm.contractId = row.BillId
            this.addPlanForm.contractNo = row.BillNo
            this.addPlanForm.supplierId = row.BId
            this.addPlanForm.supplierName = row.BName
            this.addPlanForm.orgId = row.OrgId
            this.addPlanForm.orgName = row.OrgName
            this.addPlanForm.supplierName = row.BName
            this.showSelectContractDialog = false
            this.addPlanForm.planNo = getUuid().replaceAll('-', '')
            // 清空之前选择的明细
            this.changePlanDtlRowDate = []
            this.currentBillId = row.BillId
            this.getPCWP1BuyContractDtlM()
        },
        // 选择合同
        selectContractM () {
            this.showSelectContractDialog = true
            this.getPCWP1BuyContractM()
        },
        // 标签点击暂时无用
        // eslint-disable-next-line no-unused-vars
        handleClick (tab, event) {
            // console.log(tab, event)
        },
        restartPlanFormM () {
            this.addPlanForm = {
                planNo: null,
                planDate: null,
                businessType: 0,
                contractId: null,
                contractNo: null,
                supplierId: null,
                supplierName: null,
                orgId: null,
                orgName: null,
                state: 0,
                remarks: null,
                isSubmit: 0,
                dtls: [],
            }
            this.addPlanForm.planNo = getUuid().replaceAll('-', '')
        },
        addPlanM () {
            // this.restartPlanFormM()
            this.addPlanForm.planNo = getUuid().replaceAll('-', '')
            this.showAddPlanDialog = true
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getPlanListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getPlanListM()
        },
        currentChange2 (index) {
            this.contractObj.pagination2.currPage = index
            this.getPCWP1BuyContractM()
        },
        sizeChange2 (size) {
            this.contractObj.pagination2.pageSize = size
            this.getPCWP1BuyContractM()
        },
        currentChange3 (index) {
            this.contractDtlObj.pagination2.currPage = index
            this.getPCWP1BuyContractDtlM()
        },
        sizeChange3 (size) {
            this.contractDtlObj.pagination2.pageSize = size
            this.getPCWP1BuyContractDtlM()
        },
        getPlanListM () {
            let params = {
                page: this.pagination.currPage,
                limit: this.pagination.pageSize,
                dataSelect: this.userPermission.orgDisplayState, // （1本机及子级2只看本级3看指定子级）
                dataScope: this.userPermission.currentSubOrgId
            }
            if (this.keyword != null) {
                params.keywords = this.keyword
            }
            if (this.filterData.planDate != null) {
                params.startPlanDate = this.filterData.planDate[0]
                params.endPlanDate = this.filterData.planDate[1]
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.planNo != null) {
                params.planNo = this.filterData.planNo
            }
            if (this.filterData.contractNo != null) {
                params.contractNo = this.filterData.contractNo
            }
            if (this.filterData.state.length !== 0) {
                params.states = this.filterData.state
            }
            this.planListLoading = true
            materialMonthSupplyPlanList(params).then(res => {
                this.list = res.list
                this.pagination.currPage = res.currPage
                this.pagination.pageSize = res.pageSize
                this.pagination.totalNum = res.totalCount
                this.pagination.totalPage = res.totalPage
            }).finally(() => {
                this.planListLoading = false
            })
        },
        getSelePlanListM (contractNo) {
            this.contractNo = contractNo
            this.getSectionalPlanListM()
        },
        getSectionalPlanListM () {
            let params = {
                page: this.planPagination.currPage,
                limit: this.planPagination.pageSize,
                dataSelect: this.userPermission.orgDisplayState, // （1本机及子级2只看本级3看指定子级）
                dataScope: this.userPermission.currentSubOrgId,

            }
            if (this.keyword3 != null) {
                params.planNo = this.keyword3
            }
            if (this.contractNo != null) {
                this.showPlanDialog = true
                params.contractNo = this.contractNo
            }

            // if (this.contractNo != null) {
            //     params.contractNo = this.contractNo
            // }
            if (this.filterData.state.length !== 0) {
                params.states = this.filterData.state
            }
            this.closePlanLoading = true
            materialMonthSupplyPlanList(params).then(res => {
                this.closePlanDate = res.list
                this.planPagination.currPage = res.currPage
                this.planPagination.pageSize = res.pageSize
                this.planPagination.totalNum = res.totalCount
                this.planPagination.totalPage = res.totalPage
            }).finally(() => {
                this.closePlanLoading = false
            })
        },
        getPCWP1BuyContractDtlM () {
            let params = {
                billid: this.currentBillId,
                version: this.pcwpVersion
            }

            this.contractDtlObj.contractLoading = true
            getPCWP2BuyContractDtl({ params }).then(res => {
                let resultList = res
                if (resultList.length !== 0) {
                    checkTotalNum(resultList).then(res => {
                        this.contractDtlObj.contractDtlList = res
                    }).finally(() => {
                        this.contractDtlObj.contractLoading = false
                    })
                } else {
                    this.contractDtlObj.contractLoading = false
                }
                // this.contractDtlObj.pagination2.totalPage = res.recordsTotal % res.PageSize == 0 ? res.recordsTotal / res.PageSize : Math.floor(res.result.recordsTotal / res.PageSize) + 1
                // this.contractDtlObj.pagination2.totalNum = res.recordsTotal
                // this.contractDtlObj.pagination2.currPage = res.PageIndex
                // this.contractDtlObj.pagination2.pageSize = res.PageSize
            })
        },
        // 根据关机字查询
        getPCWP1BuyContractByKeyWord () {
            let key = this.contractObj.keyword
            if (key != null) {
                this.contractObj.contractList = this.contractObj.contractList.filter(t => {
                    return t.BillNo.indexOf(key) != -1 || t.Name.indexOf(key) != -1 || t.BName.indexOf(key) != -1
                })
                this.contractObj.keyword = null
            } else {
                this.getPCWP1BuyContractM()
            }
        },
        getPCWP1BuyContractM () {
            this.contractObj.contractLoading = true
            getPCWP2BuyContract({ orgid: this.userInfo.orgId }).then(res => {
                this.contractObj.contractList = res
            }).finally(() => {
                this.contractObj.contractLoading = false
            })
        },
        // 跳转详情
        handleViewDetail (billId) {
            this.$router.push({ path: '/planDetail', query: { billId } })
        },
    },
}
</script>

<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);
main {
    padding: 0 20px;
    border: $border;
}

/deep/ .el-form-item__content {
    //width: 100%;
    .el-range-separator {
        width: unset;
    }
}

/deep/ .el-table__body-wrapper {
    height: 100%;
}

.list-title {
    padding: 0;

    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}

.search {
    .box {
        width: 268px;
        height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);
        border-right: 0;

        img {
            width: 16px;
            height: 16px;
            margin: 0 4px 0 10px;
        }

        input {
            width: 230px;
        }
    }

    button {
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
    }
}

.list {
    .item {
        height: 52px;
        padding: 0 20px;
        font-size: 14px;
        border: $border;
        color: rgba(102, 102, 102, 1);
        position: relative;

        & > div:not(.pointer) {
            margin-right: 60px;
        }

        .pointer {
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 20px;
        }

        span {
            color: rgba(51, 51, 51, 1);
        }
    }
}

/deep/ .el-dialog {
    .el-dialog__header {
        height: 50px;
        line-height: 50px;
        margin-top: 0;
        padding: 0;
    }

    .el-dialog__body {
        height: 750px;
        margin-top: 0;
    }
}
/deep/ .orderDialog {
    .el-dialog__header {
        height: 50px;
        line-height: 50px;
        margin-top: 0;
        padding: 0;
    }
    .el-dialog__body {
        height: 550px;
        margin-top: 0;
    }
}
/deep/ #filterDataDi {
    .el-dialog__header {
        height: 50px;
        line-height: 50px;
        margin-top: 0;
        padding: 0;
    }

    .el-dialog__body {
        height: 350px;
        margin-top: 0;
    }
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>