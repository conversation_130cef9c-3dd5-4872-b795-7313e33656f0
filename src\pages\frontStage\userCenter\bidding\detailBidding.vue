<template>
    <main>
        <div>
            <div class="list-title dfa mb20">竞价采购</div>
            <div class="detailBox">
                <div class="row">
                    <div class="col">
                        <div class="name">
                            <span>*</span>竞价编号：
                        </div>
                        {{ detail.sn }}
                    </div>
                    <div class="col">
                        <div class="name">
                            <span>*</span>竞价标题：
                        </div>
                        {{ detail.title }}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            <span>*</span>截止时间：
                        </div>
                        {{ detail.time }}
                    </div>
                    <div class="col">
                        <div class="name">
                            <span>*</span>竞价方式：
                        </div>
                        {{ detail.type }}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            <span>*</span>竞价状态：
                        </div>
                        {{ detail.state }}
                    </div>
                    <div class="col">
                        <div class="name">
                            <span>*</span>创建时间：
                        </div>
                        {{ detail.create }}
                    </div>
                </div>
            </div>
            <div class="list-title dfa mb20">商品列表</div>
            <el-table
                ref="msgTable"
                :data="list"
                :header-row-style="{ fontSize: '16px',color: '#216EC6' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column prop="sn" label="商品编码" width="">
                </el-table-column>
                <el-table-column prop="title" label="商品名称" width="">
                </el-table-column>
                <el-table-column prop="time" label="规格型号" width="">
                </el-table-column>
                <el-table-column prop="sn" label="计量单位" width="">
                </el-table-column>
                <el-table-column prop="sn" label="数量" width="">
                </el-table-column>
            </el-table>
            <div class="list-title dfa mb20">竞价情况</div>
            <el-table
                ref="msgTable"
                :data="list"
                :header-row-style="{ fontSize: '16px',color: '#216EC6' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column prop="sn" label="供应商名称" width="">
                </el-table-column>
                <el-table-column prop="title" label="报价价格" width="">
                </el-table-column>
                <el-table-column prop="time" label="报价时间" width="">
                </el-table-column>
                <el-table-column prop="time" label="备注" width="">
                </el-table-column>
                <el-table-column prop="time" label="操作" width="">
                </el-table-column>
            </el-table>
        </div>

    </main>
</template>

<script>
export default {
    name: 'detail',
    data () {
        return {
            isLase: false,
            negotiatedPriceList: [{
                nickName: '四川德胜有限公司',
                time: '2023-4-20 00:00:00',
                price: 1333.00,
                gmtCreate: '2023-4-20 00:00:00',

            }], //报价
            reviewList: [],          //问答
            detail: {
                sn: '2342424n2o4h',
                title: '五金材料采购',
                time: '2023-6-20 00:00:00',
                type: '公开竞价',
                state: '进行中',
                create: '2023-1-20 00:00:00'
            }
        }
    },
    created () {
        // this.getDetail()
    },
    methods: {
        // 获取详情
        getDetail () {
        },
        getNegotiatedPrice () {},
        getAskAnswer () {}
    }
}
</script>

<style scoped lang="scss">
main {
    min-height: 894px;
    padding: 0 20px;
    border: 1px solid rgba(229, 229, 229, 1);
}
.list-title {
  height: 50px;
  padding: 15px 19px 15px 21px;
  font-size: 20px;
  line-height: 20px;
  border-bottom: 1px solid rgba(230, 230, 230, 1);
  position: relative;

  &::before {
    width: 3px;
    height: 20px;
    margin-right: 10px;
    content: '';
    display: block;
    background-color: rgba(33, 110, 198, 1);
  }
}
.list-title {
    padding: 0;
}

.reviewList {
    min-height: 400px;
}

.reviewItem {
    &:not(:last-of-type) {
        margin-bottom: 24px;
        padding-bottom: 20px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
    }

}

.avatar {
    width: 44px;
    height: 44px;
    margin-right: 10px;
    border-radius: 50%;
}

.reviewItem {
    &:not(:last-of-type) {
        margin-bottom: 24px;
        padding-bottom: 20px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
    }

    .reviewTop {

        .user {
            div:first-child {
                font-size: 18px;
                font-weight: 500;
            }

            div:last-child {
                color: rgba(102, 102, 102, 1);
            }
        }
    }

    p {
        font-size: 16px;
        color: rgba(51, 51, 51, 1);
    }

    .imgs {
        margin-top: 10px;

        img {
            width: 120px;
            height: 120px;
            margin-right: 10px;
            object-fit: cover;
        }
    }
}

.historyItem {
    height: 108px;
    padding-right: 26px;

    &:not(:last-of-type) {
        border-bottom: 1px solid rgba(229, 229, 229, 1);
    }

    .product, .numbers, .finishTime {
        text-align: center;
        margin-left: 200px;
    }
}

.detailBox {
    margin: 70px;

    .row {
        margin-bottom: 22px;
        color: rgba(51, 51, 51, 1);

        &, .col {
            display: flex;
        }

        .col {
            width: 50%;
        }

        .name {
            width: 90px;
            text-align: right;

            span {
                color: rgba(255, 95, 95, 1);
            }
        }
    }
}
</style>