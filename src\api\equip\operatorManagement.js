import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

//装备管理 - 操作手管理 接口
const request = {

    //===============================================================================================操作手登记接口
    //前端生成billID
    getBillId () {
        return httpGet({
            url: '/uid/builder?num=1',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //获取用户信息
    getUserInfo (params) {
        return httpGet({
            url: '/hr/user/getUserById',
            params: {
                userId: params,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //高级分页查询操作手登记
    operatorList (params) {
        return httpPost({
            url: '/facilitybase/OperatorRegister/AdvancedQuery',
            params
        })
    },
    //操作手登记保存
    operatorAdd (params) {
        return httpPost({
            url: '/facilitybase/OperatorRegister/insert',
            params
        })
    },
    //获取操作手信息
    operatorInfo (params) {
        return httpGet({
            url: '/facilitybase/OperatorRegister/getOperator',
            params: {
                id: params,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //获取操作手基本信息
    operatorBaseInfo (params) {
        return httpGet({
            url: '/facilitybase/OperatorRegister/getOperator',
            params: {
                id: params,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //新增操作手工作经验
    insertWrokExperience (params) {
        return httpPost({
            url: '/facilitybase/OperatorRegister/insertWrokExperience',
            params
        })
    },
    //修改操作手信息
    operatorUpdate (params) {
        return httpPost({
            url: '/facilitybase/OperatorRegister/update',
            params
        })
    },
    //根据操作手ID获取操作手考核记录
    getOperatorAssessment (params) {
        return httpGet({
            url: '/facilitybase/OperatorRegister/getOperatorAssessment',
            params: {
                id: params,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //根据操作手ID获取操作手工作经验
    getOperatorWorkExperience (params) {
        return httpGet({
            url: '/facilitybase/OperatorRegister/getOperatorWorkExperience',
            params: {
                id: params,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //删除操作手信息
    operatorDelete (params) {
        return httpPostForm({
            url: '/facilitybase/OperatorRegister/delete',
            params
        })
    },
    //删除操作手工作经验
    deleteWorkExperience (params) {
        return httpPostForm({
            url: '/facilitybase/OperatorRegister/deleteWorkExperience',
            params
        })
    },
    //===============================================================================================操作手汇总接口

    //高级分页查询操作手汇总
    operatorPoolList (params) {
        return httpPost({
            url: '/facilitybase/OperatorSummary/AdvancedQuerySummary',
            params
        })
    },
    //高级分页查询操作手汇总
    operatorPoolTo (params) {
        return httpPostForm({
            url: '/facilitybase/OperatorSummary/shiftTo',
            params
        })
    },
    //============================================操作手考核===================================================
    //高级分页查询操作手汇总
    operatorAssessList (params) {
        return httpPost({
            url: '/facilitybase/operator/assess/list/advanced',
            params
        })
    },
    //操作手考核基本信息保存
    operatorAssessAdd (params) {
        return httpPost({
            url: '/facilitybase/operator/assess/add',
            params
        })
    },
    //获取操作手考核基本信息
    getOperatorAssessBase (params) {
        return httpGet({
            url: '/facilitybase/operator/assess/get/by/id',
            params: {
                id: params,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //获取操作手考核 考核明细
    getOperatorAssessDtl (params) {
        return httpGet({
            url: '/facilitybase/operator/assess/dtl/get/by/id',
            params: {
                id: params,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //更新操作手考核基本信息
    updateOperatorAssessBase (params) {
        return httpPost({
            url: '/facilitybase/operator/assess/update',
            params
        })
    },
    //更新操作手考核明细
    updateOperatorAssessDtl (params) {
        return httpPost({
            url: '/facilitybase/operator/assess/dtl/update',
            params
        })
    },
    //删除操作手考核信息
    operatorAssessDelete (params) {
        return httpGet({
            url: '/facilitybase/operator/assess/delete',
            params: {
                id: params,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
        })
    },
}

export default request
