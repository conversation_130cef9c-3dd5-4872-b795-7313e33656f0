<template>
    <div class="base-page">
        <div class="left" :style="{ height: '100%' }" style="overflow: scroll;">
            <select-material-zone
                v-loading="treeLoading" ref="materialClassRef" @addEvent="onAddMaterialClass" @modifyEvent="onModifyMaterialClass(arguments)"
                @delEvent="onDelMaterialClass" :data="tableData" @checkedNodesFn="checkedNodesFn" :treeExpandData="treeExpandData"
            />
        </div>
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <el-select @change="stateOptionsClick" v-model="stateOptionTitle" placeholder="请选择状态">
                            <el-option
                                v-for="item in stateOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                        <el-button @click="batchState(1)" style="margin-left: 10px" class="btn-greenYellow">批量启用</el-button>
                        <el-button @click="batchState(0)" class="btn-delete">批量停用</el-button>
                    </div>
                    <div class="right">
                        <div class="left-btn">
                                                        <el-button type="primary" @click="onAddTopLevel" class="btn-greenYellow">新增顶级</el-button>
                            <!--                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>-->
<!--                            <el-button @click="batchState(1)" class="btn-greenYellow">批量启用</el-button>-->
<!--                            <el-button @click="batchState(0)" class="btn-delete">批量停用</el-button>-->
                        </div>
                    </div>
                </div>
            </div>
            <div class="e-table" v-show="viewList === true">
                <div class="detail-info" :style="{ height: rightTableHeight + 'px' }">
                    <div v-loading="formLoading" class="e-form">
                        <div class="tabs" style="margin-bottom:120px">
                            <div class="tabs-title">{{ formTitle }}</div>
                            <div style="width: 100%" class="form">
                                <el-form ref="formEdit" :rules="formRules" :model="formClass" label-width="" class="demo-ruleForm">
                                    <el-row>
                                        <el-col :span="12" v-show="formClass.parentzone != undefined">
                                            <el-form-item label="上级类别名称" prop="parentzone">
                                                {{ formClass.parentzone }}
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="区域名称：" prop="zone">
                                                <el-input clearable v-model="formClass.zone" :disabled="addAndModify=='info'" autocomplete="off"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item width="150px" label="排序：" prop="sort">
                                                <el-input clearable v-model="formClass.sort" :disabled="addAndModify=='info'" type="number" size="normal"></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="5">
                                            <el-switch
                                                :disabled="addAndModify=='info'"
                                                v-model="formClass.state"
                                                active-color="#13ce66"
                                                inactive-color="#ff4949"
                                                active-text="启用"
                                                inactive-text="停用"
                                                :active-value="1"
                                                :inactive-value="0"
                                            >
                                            </el-switch>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="备注信息：">
                                                <el-input :maxlength="300" clearable type="textarea" :disabled="addAndModify=='info'" v-model="formClass.classDescribe" autocomplete="off"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </div>
                        </div>
                    </div>
                    <div class="buttons">
                        <el-button style="background: #2e61d7" @click="onSave()" v-if="addAndModify=== 'add' || addAndModify === 'modify' || addAndModify === 'addTop'">保存</el-button>
                        <el-button style="background: #178d1f" @click="updateOneSate(formClass)" v-if="formClass.state === 0 && addAndModify === 'modify'">启用</el-button>
                        <el-button style="background: #ff0000" @click="updateOneSate(formClass)" v-if="formClass.state === 1 && addAndModify === 'modify'">停用</el-button>
                    </div>
                </div>
            </div>

        </div>

    </div>

</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialZone from '../../../../components/selectProductZone.vue'
import { debounce } from '@/utils/common'
import { create, del, edit, editBatch, treeByName } from '@/api/platform/product/productZone'
import { mapState } from 'vuex'
export default {
    components: {
        SelectMaterialZone,
    },
    watch: {
        'formClass.sort': {
            handler () {
                if (this.formClass.sort >= 10000000) {
                    this.formClass.sort = 10000000
                }
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) {
                return this.screenHeight - 170
            }
            return this.screenHeight - 214
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            treeLoading: false,
            formLoading: false, // 加载
            treeExpandData: [], // 要展开的节点
            selectOptionValue: null, // 选中的值
            stateOptionTitle: '',
            stateOptions: [{
                value: null,
                label: '全部'
            }, {
                value: 1,
                label: '启用'
            }, {
                value: 0,
                label: '停用'
            }],
            alertName: '分类',
            formTitle: '', // 表单标题
            formTitleArr: [ // 表单标题
                '基础信息',
                '新增顶级',
                '新增子节点',
                '编辑类别'
            ],
            viewList: false, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            addAndModify: '', // 操作类型
            currentClass: null, // 当前的节点
            formRules: {
                zone: [
                    { required: true, message: '请输入区域名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '超出范围', trigger: 'blur' }
                ]
            },
            tableData: [], // treedata
            formClass: {
                productType: 0,
                classKeyword: null,
                zoneLevel: 1,
                zone: null,
                zoneId: null,
                parentId: null,
                sort: 0,
                classDescribe: null,
                parentzone: null,
                state: 0,
                mallType: 0,
            },
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            checkNodes: [],
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        let data = JSON.parse(localStorage.getItem('vuex'))
        this.orgId = data.userInfo.orgInfo.orgId
        this.treeSearch()
    },
    methods: {
        // 单个停用和启用
        updateOneSate (formClass) {
            let param = {
                zoneIds: [formClass.zoneId],
                state: null
            }
            if (formClass.state === 1) {
                param.state = 0
            }
            if (formClass.state === 0) {
                param.state = 1
            }
            this.formLoading = true
            editBatch(param).then(res => {
                this.message(res)
                this.treeSearch()
                this.viewList = false
                // this.classNodeClick()
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 状态下拉框点击
        stateOptionsClick (value) {
            this.selectOptionValue = value
            this.treeSearch()
        },
        // 批量启用或停用
        batchState (value) {
            if (this.checkNodes == null || !this.checkNodes[0]) {
                return this.$message('请勾选要修改的数据！')
            }
            let param = {
                ids: new Array(),
                state: value
            }
            for (let i = 0; i < this.checkNodes.length; i++) {
                param.ids.push(this.checkNodes[i].zoneId)
            }
            this.formLoading = true
            editBatch(param).then(res => {
                this.viewList = false
                this.message(res)
                this.treeSearch()
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 选中的节点
        checkedNodesFn (data) {
            this.checkNodes = data
        },
        //批量删除
        handleDelete () {
            if (!this.checkNodes[0]) {
                return this.$message('请勾选要修改的数据！')
            }
            let reverseArr = this.checkNodes.reverse()
            let node = reverseArr[0]
            let parentId = null
            let arr = reverseArr.map(item => {
                if (node.parentId !== item.zoneId) {
                    parentId = item.parentId
                    return item.zoneId
                } else {
                    return null
                }
            })
            this.clientPop('info', '您确定要删除勾选的分类？数据删除后不可恢复，请谨慎操作！', async () => {
                del(arr).then(res => {
                    this.message(res)
                    this.treeSearch()
                    this.treeExpandData = [parentId]
                })
            })
        },
        // 查询
        treeSearch (keywords) {
            let params = {}
            if (this.selectOptionValue != null) {
                params.state = this.selectOptionValue
            }
            if (keywords != null && keywords != '') {
                params.zone = keywords
            }
            this.treeLoading = true
            treeByName(params).then(res => {
                this.tableData = res
                this.checkNodes = []
                this.treeLoading = false
            }).catch(() => {
                this.treeLoading = false
            })
        },
        // 点击分类
        classNodeClick (arg) {
            this.resetClass()
            this.formTitle = this.formTitleArr[0]
            this.formClass = arg[0]
            this.formClass.parentzone = arg[1].parent.data.zone
            this.addAndModify = 'info'
            this.viewList = true
        },
        // 重置
        resetClass () {
            this.formClass = {
                classKeyword: null,
                zoneLevel: 1,
                zone: null,
                zoneId: null,
                parentId: null,
                sort: 0,
                classDescribe: null,
                parentzone: null,
                state: 0,
                mallType: 0,
            }
        },
        // 添加顶级
        onAddTopLevel () {
            this.resetClass()
            this.addAndModify = 'add'
            this.formTitle = this.formTitleArr[1]
            this.addAndModify = 'addTop'
            this.viewList = true
        },
        // 添加子级
        onAddMaterialClass (data) {
            this.resetClass()
            this.currentClass = data //存储当前节点
            this.updateClassInit(data)
            this.formTitle = this.formTitleArr[2]
            this.addAndModify = 'add'
            this.viewList = true
        },
        // 修改
        onModifyMaterialClass (arg) {
            this.resetClass()
            delete arg[0].children
            this.formClass = arg[0] // 当前节点
            let parent = arg[1] // 父节点
            this.formClass.parentzone = parent.zone
            this.formTitle = this.formTitleArr[3]
            this.viewList = true
            this.addAndModify = 'modify'
        },
        // 删除
        onDelMaterialClass (data) {
            this.clientPop('info', `您确定要删除【${data.zone}】吗？数据删除后不可恢复，请谨慎操作！`, async () => {
                del([data.zoneId]).then(res => {
                    this.message(res)
                    this.treeSearch()
                    this.treeExpandData = [data.parentId]
                })
            })
        },
        // 添加子节点和修改节点的属性为父
        updateClassInit (data) {
            this.formClass.parentId = data.zoneId
            this.formClass.zoneLevel = data.zoneLevel + 1
            this.formClass.parentzone = data.zone
        },
        // 修改或新增
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.addAndModify === 'modify') {
                        this.formLoading = true
                        let id = this.formClass.zoneId
                        edit(this.formClass).then(res => {
                            this.message(res)
                            this.treeSearch()
                            this.formLoading = false
                        }).catch(() => {
                            this.formLoading = false
                        })
                        this.treeExpandData = [id]
                    }
                    if (this.addAndModify === 'add') {
                        this.formLoading = true
                        let id = this.formClass.parentId
                        create(this.formClass).then(res => {
                            this.message(res)
                            this.resetClass()
                            this.updateClassInit(this.currentClass)
                            this.treeSearch()
                            this.formLoading = false
                        }).catch(() => {
                            this.formLoading = false
                        })
                        this.treeExpandData = [id]
                    }
                    if (this.addAndModify === 'addTop') {
                        create(this.formClass).then(res => {
                            this.message(res)
                            this.resetClass()
                            this.treeSearch()
                        })
                    }
                }
            })
        },
        //返回消息
        message (res) {
            if (res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}

.base-page .left {
    min-width: 200px;
    height: 100%;
    padding: 0;
    overflow: scroll;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-table__header-wrapper {
    .el-checkbox {
        display: none;
    }
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 15px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}
</style>
