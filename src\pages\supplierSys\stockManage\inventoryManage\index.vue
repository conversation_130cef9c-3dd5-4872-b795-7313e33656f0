<template>
  <div class="base-page" v-loading="showLoading">
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <div class="top">
          <div class="left">
            <div class="left-btn dfa" style="flex-wrap: wrap;">
              <el-button type="primary" class="btn-greenYellow" @click="add">入库</el-button>
            </div>
          </div>
          <div class="search_box" style="width: 400px">
            <el-input
                clearable type="text" @blur="handleInputSearch" placeholder="输入搜索关键字"
                v-model="init.keywords"
            >
              <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
            </el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="highSearch">高级搜索</el-button>
            </div>
          </div>
        </div>
      </div>
      <!--表格-->
      <div class="e-table">
        <el-table
            @row-click="handleCurrentInventoryClick2" ref="mainTable2" v-loading="tableLoading" class="table"
            :height="rightTableHeight" :data="tableData" border
            @selection-change="selectionChangeHandle"
        >
        >
          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="130">
            <template v-slot="scope">
              <el-button
                  style="padding:0px 8px 0px 8px;"
                  size="mini"
                  type="danger"
                  @click="deleteRow(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="物资名称" width="200">
            <template v-slot="scope">
              <span class="action" @click="handleView(scope.row)">{{ scope.row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="物资分类" width="240" prop="classPathName">
          </el-table-column>
          <el-table-column label="商品编码" width="240">
            <template v-slot="scope">
              <span class="action" @click="handleView(scope.row)">{{ scope.row.serialNum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="图片" width="120" type="index">
            <template v-slot="scope">
              <el-image
                  style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg"
                  fit="cover"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column label="税率%" prop="taxRate"></el-table-column>
<!--          <el-table-column label="差价" prop="profitPrice"/>-->
          <el-table-column label="库存" prop="stock"/>
          <el-table-column label="库房" prop="warehouseId"/>
          <el-table-column label="操作人" width="200" prop="operationUser"/>
          <el-table-column label="创建时间" width="160" prop="gmtCreate"/>

        </el-table>
      </div>
      <!--            分页-->
      <Pagination
          v-show="tableData != null || tableData.length != 0"
          :total="paginationInfo.total"
          :pageSize.sync="paginationInfo.pageSize"
          :currentPage.sync="paginationInfo.currentPage"
          @currentChange="getTableData"
          @sizeChange="getTableData"
      />
    </div>
    <!--高级查询-->
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
        <el-row>
          <el-col :span="12">
            <el-form-item label="物资名称：">
              <el-input
                  clearable maxlength="100" placeholder="请输入物资名称" v-model="filterData.productName"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品编码：">
              <el-input
                  clearable maxlength="100" placeholder="请输入商品编码" v-model="filterData.serialNum"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建时间：">
              <el-date-picker
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="filterData.createDate"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import ImportExcel from '@/components/importExcel.vue'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapMutations, mapState } from 'vuex'
import {
    deleteSelfStock,
    selfStockList
} from '@/api/supplierSys/stock/stockManage'

export default {
    components: {
    // eslint-disable-next-line vue/no-unused-components
        SelectMaterialClass, Pagination, ImportExcel
    },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            //fixedNum: null,
            productId: null,
            showJcLoading: false,
            fileUrl: '', //上传文件的域名地址
            limitNum: 1, //文件上传个数限制
            fileList: [], //文件列表
            showLoading: false,
            showImportExcelLoading: false,
            deviceInventoryLoading: false,
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
                className: null,
                keywords: null,
                classPath: [],
            },
            // 商品库
            showDeviceDialog: false,
            inventory: {
                selectRow: [],
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            dataListSelections: [], //表格选中的数据
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                productName: null,
                serialNum: null,
                belowPrice: null,
                abovePrice: null,
                createDate: [], // 创建时间
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            excelResult: []
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.getTableData()
    //this.getFixed()
    },
    methods: {
        toDetail () {
            this.skipView({ productId: this.productId })
            this.showJcLoading = false
        },
        //文件上传之前的钩子,可以做一些验证或限制
        beforeUpload (file) {
            let regExp = file.name.replace(/.+\./, '')
            let lower = regExp.toLowerCase() //把大写字符串全部转为小写字符串
            let suffix = ['xls', 'xlsx']
            if (suffix.indexOf(lower) === -1) {
                return this.$message.warning('请上传后缀名为 xls、xlsx 的附件 !')
            }
            let isLt2M = file.size / 1024 / 1024 < 5
            if (!isLt2M) {
                return this.$message.error('请上传文件大小不能超过 5MB 的附件 !')
            }
        },
        //文件超出个数限制时的钩子
        onExceed (files, fileList) {
            return this.$message.warning(`只能选择${this.limitNum}个文件,当前共选择了${files.length + fileList.length}个`)
        },
        btnClick (command) {
            console.log(command)
            let actions = {
                'putawayProductExportM': () => this.putawayProductExportM(),
                'updateStateBatch': () => this.updateStateBatch(2),
                'onDownload': () => this.onDownload(),
                'outputExcelM': () => this.outputExcelM(),
                'changeSortValue': () => this.changeSortValue(),
                'batchDelete': () => this.batchDelete()
            }
            actions[command]()
        },
        showExcelResult (result) {
            this.excelResult = result
            this.showImportExcelLoading = true
        },
        handleChangeSort (value) {
            this.init.orderBy = value
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        // 排序变换行
        getChangedRow (row) {
            if (row.shopSort <= 0) {
                row.shopSort = 0
            }
            if (this.changedRow.length === 0) {
                this.changedRow.push({ productId: row.productId, shopSort: row.shopSort })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.productId === row.productId) {
                    t.shopSort = row.shopSort
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push({ productId: row.productId, shopSort: row.shopSort })
            }
            flag = true
        },
        skipView (data) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/analysis/stockDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'stockDetail',
                params: {
                    row: data
                }
            })
        },
        //新增
        add () {
            let data = {}
            data.viewType = 'add'
            data.classPath = this.classPath
            this.skipView(data)
        },
        // 物资详情
        handleView (row) {
            row.viewType = 'view'
            row.classPath = this.classPath
            this.skipView(row)
        },
        highSearch () {
            this.resetSearchConditions()
            this.queryVisible = true
        },
        resetSearchConditions () {
            this.filterData.productName = ''
            this.filterData.serialNum = ''
            this.filterData.createDate = []
        },
        // 高级搜索确认
        confirmSearch () {
            this.init.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.mainTable2.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 单个删除
        deleteRow (row) {
            this.clientPop('info', '您确定要对物资【' + row.productName + '】进行【删除】操作吗？', async () => {
                deleteSelfStock( row.recordId ).then(res => {
                    this.message(res)
                    this.getTableData()
                })
            })
        },
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.classPath = nodePath
            this.getTableData()
        },
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.serialNum != null) {
                params.serialNum = this.filterData.serialNum
            }
            if (this.init.keywords != null) {
                params.keywords = this.init.keywords
            }
            if (this.filterData.createDate != null) {
                params.startCreateDate = this.filterData.createDate[0]
                params.endCreateDate = this.filterData.createDate[1]
            }
            this.tableLoading = true
            selfStockList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.el-dialog__body {
  margin: 220px;
}

.base-page .left {
  min-width: 200px;
  height: 100%;
  padding: 0;
  overflow: auto;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .el-dropdown {
  min-width: 75px;
  margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.e-table {
  min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}
</style>
