import service from '@/utils/request'

const { httpPost } = service

// 查询一级大宗临购可对账物资列表
const getReconcilableMaterialList = params => {
    return httpPost({
        url: '/materialMall/orderShipDtl/getReconcilableMaterialList',
        params: params
    })
}

// 查询可对账计划列表
const getReconciliablePlansBySupplier = params => {
    return httpPost({
        url: '/materialMall/planReconciliation/getReconciliablePlanList',
        params: params
    })
}

// 根据供应商查询大宗临购可对账的项目部
const getReconciliableEnterprisePageList = params => {
    return httpPost({
        url: '/materialMall/planReconciliation/getReconciliableEnterprisePageList',
        params: params
    })
}

export {
    getReconcilableMaterialList,
    getReconciliablePlansBySupplier,
    getReconciliableEnterprisePageList
}
