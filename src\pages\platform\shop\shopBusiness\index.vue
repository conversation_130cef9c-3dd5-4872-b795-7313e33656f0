<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" >
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn">
              <el-button @click="createVisibleM" class="btn-greenYellow">新增</el-button>
              <el-button @click="changeSortValue" class="btn-greenYellow">批量修改排序值</el-button>
            </div>
          </div>
          <div class="search_box">
            <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
            <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
            <el-input type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字"
                      v-model="keywords">
              <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" />
            </el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" v-loading="isLoading">
        <el-table
          @row-click="handleCurrentInventoryClick2" ref="mainTable2" class="table" :height="rightTableHeight"
          :data="tableData" border highlight-current-row
          @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="130">
            <template v-slot="scope">
              <el-button
                style="padding:0 8px;" v-if="scope.row.state==0"
                size="mini"
                type="success"
                @click="updateStateM(scope.row,true,1)"
              >启用
              </el-button>
              <el-button
                style="padding:0 8px;" v-if="scope.row.state==1"
                type="danger"
                @click="updateStateM(scope.row,false,0)"
              >停用
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="店铺名称" width="260">
            <template v-slot="scope">
              <span class="action" @click="handleView(scope)">{{ scope.row.shopName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="企业名称" width="">
            <template v-slot="scope">
              <span class="action">{{ scope.row.enterpriseName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="使用状态" width="">
            <template v-slot="scope">
             <el-tag v-if="scope.row.state===1" type="success">启用</el-tag>
             <el-tag v-else type="danger">停止使用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="排序值" width="120" type="index">
            <template v-slot="scope">
              <el-input type="number" :min="0" v-model="scope.row.sort" @change="getChangedRow(scope.row)"/>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination
        :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
        @currentChange="currentChange" @sizeChange="sizeChange"
      />
    </div>

    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
        <el-row>
          <el-col :span="12">
            <el-form-item label="店铺名称：" prop="shopName">
              <el-input v-model="filterData.shopName" placeholder="请输入店铺名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
          <el-row>
          <el-col :span="12">
            <el-form-item label="企业名称：" prop="enterpriseName">
              <el-input v-model="filterData.enterpriseName" placeholder="请输入企业名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="店铺使用状态：">
              <el-select v-model="filterData.state" placeholder="店铺类型">
                <el-option
                  v-for="item in shopStateFilter" :key="item.value" :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">返回</el-button>
            </span>
    </el-dialog>
    <!-- ----------------查询弹框---------------- -->
    <el-dialog v-dialogDrag title="选择企业" :visible.sync="createVisible" width="50%">
      <div class="e-table" v-loading="selectshopBusinessLoading">
        <div class="top dfa" style="height: 50px; padding-left: 10px">
          <el-input
            style="width: 200px; " type="text" @blur="getEnterpriseListM"
            placeholder="输入搜索关键字" v-model="keywords"
          >
            <img :src="require('@/assets/search.png')" slot="suffix" @click="getEnterpriseListM" />
          </el-input>
        </div>
        <el-table
          ref="selectTwoSupplierR"
          border
          @selection-change="getEnterpriseListM"
          :data="shopBusinessData"
          class="table"
          :max-height="$store.state.tableHeight"
        >
          <!--<el-table-column type="selection" width="40"></el-table-column>-->
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column label="操作">
            <template v-slot="scope">
              <div
                class="pointer" style="color: rgba(33, 110, 198, 1);"
                @click="createByEnterpriseIdM(scope.row)"
              >选择企业成为自营
              </div>
            </template>
          </el-table-column>
          <el-table-column label="供应商企业名称" prop="enterpriseName" />
        </el-table>
      </div>
      <!--分页-->
      <span slot="footer">
                    <Pagination
                      :total="paginationInfo.total"
                      :pageSize.sync="paginationInfo.pageSize"
                      :currentPage.sync="paginationInfo.currentPage"
                      @currentChange="currentChangeUser"
                      @sizeChange="sizeChangeUser"
                    />
                </span>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import {
    batchDelete,
    changeSortValue,
    del,
    createDataByEnterpriseId,
    getShopBusinessList, updateState
} from '@/api/platform/shop/shopBusiness'
// eslint-disable-next-line no-unused-vars
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapActions } from 'vuex'
import Pagination from '@/components/pagination/pagination.vue'
import { getListAll } from '@/api/platform/supplier/supplierAudit'
export default {
    components: {
        Pagination,
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getShopBusinessList(this.requestParams).then(res => {
                    console.log(res)
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        }
    },
    data () {
        return {
            alertName: '店铺',
            queryVisible: false,
            createVisible: false,
            selectshopBusinessLoading: false,
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1
            },
            action: '编辑',
            keywords: '',
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            pages: {
                currPage: 1,
                pageSize: 20
            },
            shopBusinessData: [],
            // 高级查询数据对象
            filterData: {
                shopName: '',
                enterpriseName: '',
                state: null,
                orderBy: 1
            },
            tableData: [],
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入链接名', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
                name: '',
                url: '',
                remarks: ''
            },
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            isLoading: false,

            shopStateFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '停用' },
                { value: 1, label: '启用' }
            ]
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.getTableData()
    },
    methods: {

        async  updateStateM (row, state, title) {
            let params = {
                shopId: row.shopId,
                enterpriseId: row.enterpriseId,
                state: title,
            }
            if (state) {
                this.clientPop('info', '你确定启用这个自营店吗', async () => {
                    let res =  await updateState(params)
                    console.log(res)
                    if (res.code == 200) {
                        this.getTableData()
                        this.$message.success('自营店启用成功')

                    }

                })
            }else {
                this.clientPop('info', '你确定停止使用自营店，停止使用后将不能在商城查询或上架改店铺商品',  async () => {
                    let res =  await updateState(params)
                    console.log(res)
                    if (res.code == 200) {
                        this.getTableData()
                        this.$message.success('自营店停用成功，用户将不能在商城首页看到该自营商品')

                    }

                })
            }
        },
        getTableData () {
            this.isLoading = true
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
            }
            if (this.filterData.orderBy != null && this.filterData.orderBy != '') {
                params.orderBy = this.orderBy
            }
            if (this.filterData.state != null && this.filterData.state != '') {
                params.orderBy = this.filterData.state
            }
            if (this.filterData.enterpriseName != null && this.filterData.enterpriseName != '') {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            getShopBusinessList(params).then(res => {
                this.isLoading = false
                this.pages = res
                this.tableData = res.list
            })
            this.getParams()
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },

        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        resetSearchConditions () {
            this.filterData.shopName = ''
            this.filterData.enterpriseName = ''
            this.filterData.state = 1
        },
        createVisibleM () {
            this.createVisible = true
            this.getEnterpriseListM()
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${this.alertName}吗？`,  () => {
                showLoading()
                del({ id: scope.row.shopId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        currentChangeUser () {},
        createByEnterpriseIdM (row) {
            this.clientPop('info', '你确定选择本企业为自营机构吗？', async () => {
                createDataByEnterpriseId({ enterpriseId: row.enterpriseId }).then(res=>{
                    if (res.code == 200) {
                        this.createVisible = false
                        this.getTableData()

                    }else {
                        this.$message.error(res.message)
                    }
                })
            })
        },
        getEnterpriseListM () {
            this.selectshopBusinessLoading = true
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                interiorId: true,
                state: 1,
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            getListAll(params).then(res=>{
                if (res.list) {
                    this.shopBusinessData = res.list
                    this.paginationInfo = res
                }
                this.selectshopBusinessLoading = false
            })

            console.log(params)
        },
        sizeChangeUser () {},

        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.shopId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (!this.changedRow[0]) {
                return this.changedRow.push({ shopId: row.shopId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if (item.shopId === row.shopId) {
                    return i
                }
            })
            if (arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ shopId: row.shopId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                return this.$message('未选择数据')
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('修改成功')
                        this.getTableData()
                        this.changedRow = []
                    }
                })
            })
        },

        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.mainTable2.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.action === '编辑') {
                        return this.handleEditData()
                    }
                    this.handleCreateData()
                }
            })
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}

.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}
</style>
