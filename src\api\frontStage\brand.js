import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getBrand = params => {
    return httpPost({
        url: '/materialMall/w/brand/pageList',
        params,
    })
}

const getBrandByClassId = params => {
    return httpGet({
        url: '/materialMall/w/brand/selectAllByClassId',
        params,
    })
}

export {
    getBrand,
    getBrandByClassId
}