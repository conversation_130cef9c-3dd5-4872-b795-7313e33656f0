import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet, httpGetFile } = service

//装备管理 - 装备台账 接口
const request = {

    //高级查询外协台账
    outAccountList (params) {
        return httpPost({
            url: '/facilityaccount/account/external/list/advanced',
            params
        })
    },
    //外协台账导出
    outAccountExport () {
        return httpGetFile({
            url: '/facilityaccount/account/external/export'
        })
    },
    //添加外协台账基本信息
    outAccountAdd (params) {
        return httpPost({
            url: '/facilityaccount/account/external/add',
            params
        })
    },
    //删除外协台账
    outAccountDelete (params) {
        return httpGet({
            url: '/facilityaccount/account/external/delete',
            params: {
                id: params
            }
        })
    },
    //修改外协台账基本信息
    outAccountUpdate (params) {
        return httpPost({
            url: '/facilityaccount/account/external/update',
            params
        })
    },
    //获取外协台账基础信息
    outAccountBaseInfo (params) {
        return httpGet({
            url: '/facilityaccount/account/external/get',
            params: {
                id: params
            }
        })
    },
    //获取进场验收基本信息
    outAccountAcceptorBase (params) {
        return httpPostForm({
            url: '/facilityaccount/account/external/get/approach/acceptor',
            params: {
                billId: params
            }
        })
    },
    //添加进场验收基本信息
    outAccountAcceptorBaseAdd (params) {
        return httpPost({
            url: '/facilityaccount/account/external/add/approach/acceptor',
            params
        })
    },
    //修改进场验收基本信息
    outAccountAcceptorBaseUpdate (params) {
        return httpPost({
            url: '/facilityaccount/account/external/update/approach/acceptor',
            params
        })
    },
    //获取检查记录
    outAccountCheckRecord (params) {
        return httpGet({
            url: '/facilityaccount/account/external/get/approach/check',
            params: {
                billId: params
            }
        })
    },
    //根据id获取检查记录-基本信息
    outAccountCheckRecordBase (params) {
        return httpGet({
            url: '/facilityaccount/account/external/get/by/id/approach/check',
            params: {
                id: params
            }
        })
    },
    //新增、编辑、删除检查记录 changType 1 新增 2 编辑 -1 删除
    outAccountCheckRecordUpdate (params) {
        return httpPost({
            url: '/facilityaccount/account/external/update/approach/check',
            params
        })
    },
    //获取进出场记录
    outAccountTurnoverRecord (params) {
        return httpGet({
            url: '/facilityaccount/account/external/get/account/external/access/record',
            params: {
                accountId: params,
            }
        })
    },
    //根据id获取进出场记录-基本信息
    outAccountTurnoverRecordBase (params) {
        return httpGet({
            url: '/facilityaccount/account/external/get/by/id/external/access/record',
            params: {
                accountId: params,
            }
        })
    },
    //新增、编辑、删除进出场记录 changType 1 新增 2 编辑 -1 删除
    outAccountTurnoverRecordUpdate (params) {
        return httpPost({
            url: '/facilityaccount/account/external/update/account/external/access/record',
            params
        })
    },
    //===============================================================================================自有台账接口

    //高级查询自有台账
    ownAccountList (params) {
        return httpPost({
            url: '/facilityaccount/account/list/advanced',
            params
        })
    },
    //获取自有台账基础信息
    ownAccountBaseInfo (params) {
        return httpGet({
            url: '/facilityaccount/account/get',
            params
        })
    },
    //获取自有台账进出场记录
    getEntryExitRecords (params) {
        return httpGet({
            url: '/facilityaccount/detail/get/account/own/access/record',
            params
        })
    },
    //保存自有台账进出场记录
    setEntryExitRecords (params) {
        return httpPost({
            url: '/facilityaccount/detail/add/account/own/access/record',
            params
        })
    },
    //获取自有台账维修保养记录
    getMaintenanceRecords (params) {
        return httpGet({
            url: '/facilityaccount/detail/get/account/own/maintenance/record',
            params
        })
    },
    //获取自有台账其他
    getOwnOther (params) {
        return httpGet({
            url: '/facilityaccount/detail/get/account/own/rests',
            params
        })
    },
    //初始化装备生成自有台账
    ownAccountInit (params) {
        return httpPost({
            url: '/facilityaccount/account/add',
            params
        })
    },
    //导出
    ownAccountExport () {
        return httpGetFile({
            url: '/facilityaccount/account/export'
        })
    },
    //导出(财务类)
    ownAccountExportFinance (params) {
        return httpGetFile({
            url: `/facilityaccount/account/export/finance?listId=${params}`,
            // params: {
            //     listId:
            // }
        })
    },
    //获取指定机构的正常装备
    ownAccountGetEquipment (params) {
        return httpGet({
            url: '/facilityaccount/account/org/equipment',
            params
        })
    },

    //===============================================================================================租赁台账接口

    //高级查询租赁台账
    leaseAccountList (params) {
        return httpPost({
            url: '/facilityaccount/account/lease/list/advanced',
            params
        })
    },
    //获取租赁台账基础信息
    leaseAccountBaseInfo (params) {
        return httpGet({
            url: '/facilityaccount/account/lease/get',
            params
        })
    },
    //导出
    leaseAccountExport (params) {
        return httpGetFile({
            url: '/facilityaccount/account/lease/export',
            params
        })
    },
    //获取租赁台账进出场记录
    leaseEntryExitRecords (params) {
        return httpGet({
            url: '/facilityaccount/detail/get/account/lease/access/record',
            params
        })
    },
    //获取租赁台账维修保养记录
    leaseMaintenanceRecords (params) {
        return httpGet({
            url: '/facilityaccount/detail/get/account/lease/maintenance/record',
            params
        })
    },
    //获取租赁台账其他
    leaseAccountOther (params) {
        return httpGet({
            url: '/facilityaccount/detail/get/account/own/rests',
            params: {
                id: params
            }
        })
    },

}

export default request
