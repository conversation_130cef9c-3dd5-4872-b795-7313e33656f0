<template>
    <div>
        <div class="right" v-loading="showLoading">
<!--            <el-date-picker-->
<!--                :default-time="['00:00:00', '23:59:59']"-->
<!--                @change="dateChange"-->
<!--                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                v-model="filterData.dateScope"-->
<!--                type="datetimerange"-->
<!--                range-separator="至"-->
<!--                :picker-options="pickerOptions"-->
<!--                start-placeholder="开始日期"-->
<!--                end-placeholder="结束日期">-->
<!--            </el-date-picker>-->
            <div style="width:100%;height:300px;" ref="chart"></div>
            <!--            表格-->
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <div style="height: 50px; line-height: 50px;margin-left: 10px">
                                <el-date-picker
                                    :default-time="['00:00:00', '23:59:59']"
                                    @change="dateChange"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-model="filterData.dateScope"
                                    type="datetimerange"
                                    range-separator="至"
                                    :picker-options="pickerOptions"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                </el-date-picker>
                            </div>
                        </div>
                    </div>
                </div>
                <el-table class="table" v-loading="tableLoading" :data="tableData" border>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="用户编号" width="240" prop="userNumber"></el-table-column>
                    <el-table-column label="用户昵称" width="" prop="nickName"></el-table-column>
                    <el-table-column label="真实姓名" width="" prop="realName" />
                    <el-table-column label="用户账号" width="" prop="account"></el-table-column>
                    <el-table-column label="用户手机号" width="" prop="userMobile" />
                    <el-table-column label="上一次登录时间" width="160" prop="gmtLogin" />
                    <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                </el-table>
            </div>
            <!--分页-->
            <Pagination
                v-show="tableData  && tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getUserCountListM"
                @sizeChange="getUserCountListM"
            />
        </div>

    </div>
</template>
<script>
//局部引用
import Pagination from '@/components/pagination/pagination'
import { getUserCountList } from '@/api/platform/user/userInquire'

const echarts = require('echarts')
export default{
    components: {
        Pagination
    },
    data () {
        return {
            showLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        let [start, end] = this.getTimeScope(30)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        let [start, end] = this.getTimeScope(90)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        let [start, end] = this.getTimeScope(180)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        let [start, end] = this.getTimeScope(360)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        let [start, end] = this.getTimeScope(360 * 2)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableLoading: false,
            filterData: {
                dateScope: []
            },
            dataVOS: [],
            labelTitle: [], // 名称数组
            count: [], // 数量数组
        }
    },
    methods: {
        getTimeScope (days) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * days)
            start.setHours('00', '00', '00')
            end.setHours('23', '59', '59')
            return [start, end]
        },
        getUserCountListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.filterData.dateScope != null) {
                params.startCreateDate = this.filterData.dateScope[0]
                params.endCreateDate = this.filterData.dateScope[1]
            }
            this.showLoading = true
            getUserCountList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
                if(res.list.length > 0) {
                    this.labelTitle = res.list[0].labelTitle
                    this.count = res.list[0].count
                }else {
                    this.count = []
                }
                this.initCharts()
            }).finally(() => {
                this.showLoading = false
            })
        },
        dateChange () {
            this.getUserCountListM()
        },
        initCharts () {
            // 基于准备好的dom，初始化echarts实例
            let myChart = echarts.init(this.$refs.chart)
            // 绘制图表
            myChart.setOption({
                itemStyle: {
                    // 高亮时点的颜色
                    color: '#74A0F9'
                },
                tooltip: {},
                xAxis: {
                    data: this.labelTitle
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1,
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                series: [{
                    name: '数量',
                    type: 'bar',
                    data: this.count
                }]
            })
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
    },
    //一加载页面就调用
    mounted () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope =  [this.dateStrM(start), this.dateStrM(end)]
        this.getUserCountListM()
    }
}
</script>
<style scoped lang="scss">
</style>