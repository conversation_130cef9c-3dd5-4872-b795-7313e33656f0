<template>
    <div class="tree">
        <div class="search_box">
            <el-input clearable class="ipt" type="text"
                      placeholder="输入搜索关键字"
                      v-model="searchKey"
            ><img src="../assets/search.png" slot="suffix" @click="onSearch" /></el-input>
        </div>
        <el-tree
            show-checkbox
            v-loading="loading"
            ref="treeRef"
            :props="props"
            node-key="classId"
            @node-click="handleNodeClick(arguments)"
            :render-content="renderContent"
            :default-expanded-keys="treeExpandData"
            :data="data"
            @check="checkBoxFn"
        >
        </el-tree>
    </div>
</template>

<script>
import addIcon from '@/assets/btn/add.png'
import editIcon from '@/assets/btn/edit.png'
import delIcon from '@/assets/btn/delete.png'
export default {
    props: ['data', 'treeExpandData'],
    data () {
        return {
            loading: false,
            searchKey: null,
            mapObj: {},
            treeMapObj: {},
            props: {
                label: 'zone',
                children: 'children',
                isLeaf: 'isLeaf'
            },
            checkNodes: [],
        }
    },
    mounted () {},
    watch: {
        searchKey (data) {
            this.$parent.treeSearch(data)
        }
    },
    methods: {
        checkBoxFn () {
            let checkedNodes = this.$refs.treeRef.getCheckedNodes()
            this.$emit('checkedNodesFn', checkedNodes)
        },
        onSearch () {
            this.$parent.treeSearch(this.searchKey)
        },
        handleNodeClick (arg) {
            this.$parent.classNodeClick(arg)
        },
        addTree (data) {
            this.$emit('addEvent', data)
        },
        editTree (data, parent) {
            this.$emit('modifyEvent', data, parent)
        },
        delTree (data) {
            this.$emit('delEvent', data)
        },
        // eslint-disable-next-line no-unused-vars
        renderContent (h, { node, data, store }) {
            const me = this
            return h('div', {
                class: 'tree'
            }, [
                h('span', {
                    attrs: {
                        title: data.zone,
                    },
                    class: 'txt'
                }, data.zone
                ),
                h('div', { class: 'box' }, [
                    h('img', {
                        attrs: {
                            title: '新增子级',
                            src: addIcon
                        },
                        class: 'btn',
                        on: {
                            click (e) {
                                e.stopPropagation()

                                me.addTree(data)
                            }
                        }
                    },
                    // 文字内容
                    ''), h('img', {
                        attrs: {
                            title: '编辑分类',
                            src: editIcon
                        },
                        class: 'btn',
                        on: {
                            click (e) {
                                e.stopPropagation()
                                me.editTree(data, node.parent.data)
                            }
                        }
                    },
                    '')
                    , h('img', {
                        attrs: {
                            title: '删除分类',
                            src: delIcon
                        },
                        class: 'btn',
                        on: {
                            click (e) {
                                e.stopPropagation()
                                me.delTree(data)
                            }
                        }
                    },
                    '')
                ])
            ])
        }
    }
}
</script>

<style lang="scss" scoped>
.search_box {
    flex-direction: column;
    margin: 5px 0;
}

.tree {
    height: 100%;
    display: flex;
    flex-direction: column;
    // background-color: red;
}
/deep/ .el-tree {
    height: 100%;
    .el-loading-mask{height: 100%;}
    .el-loading-spinner {height: 50px;}
}
/deep/ .el-tree-node__content {
    width: 100%;

    .tree {
        height: 100%;
        display: flex;
        align-items: center;
        position: relative;
        padding-right: 81px;

        // .txt{max-width: 134px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
        .box {
            display: flex;
            position: absolute;
            right: 0;
        }

        .btn {
            display: none;
            margin: 0 0 0 5px;
            width: 22px;
            height: 22px;
        }
    }

    &:hover {
        .tree {
            .btn {
                display: block;
            }
        }
    }
}
</style>