import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

const request = {
    configKvGetDicValue (params) {
        params.isEnable = true
        return httpGet({
            url: '/config/kv/getDicValue',
            params
        })
    },
    configKvGetDicValue1 (params) {
        params.isEnable = true
        return httpGet({
            url: '/config/kv/getDicValue1',
            params
        })
    },
}

export default request
