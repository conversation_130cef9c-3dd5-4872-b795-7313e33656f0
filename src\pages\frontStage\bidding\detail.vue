<template>
    <div class="root">
        <div class="main">
            <div class="history mb10 center">
            <span class="router-usher">
              <a @click="$router.push('/mFront/biddingIndex')">招标</a>>
              <a @click="$router.push({path:'/mFront/biddingIndex',query:{tenderForm: 0}})">公开招标</a>>
              招标详情
            </span></div>
        </div>
        <div class="topBox center">
            <div class="title">{{ form.tenderName }}</div>
            <div class="logImg">
                <img v-if="form.tenderState<3" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState==3" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState==4" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState==5" src="@/assets/images/img/biddingsignup.png" alt=""
                     width="135px">
                <img v-if="form.tenderState>=6" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
            </div>
            <div class="center infoxBox">
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>招标名称:
                        <span>{{ form.tenderName }}</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>招标编号: <span>
                       {{ form.billNo }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>采购类型:
                        <span>{{ ['', '装备', '装备租赁', '周材', '物资', '机材'][form.tenderType] }}</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="@/assets/images/zao1.png" alt=""/>招标机构:
                        <span>{{ form.orgName }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="leftDiv">
                        <img class="tit-img" src="../../../assets/images/zao1.png" alt=""/>招标方式:
                        <span v-if="form.tenderForm === '0'">公开招标</span>
                        <span v-if="form.tenderForm === '1'">邀请招标</span>
                        <span v-if="form.tenderForm === '2'">询价</span>
                        <span v-if="form.tenderForm === '3'">竞争性谈判</span>
                        <span v-if="form.tenderForm === '4'">单一性来源</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="@/assets/images/zao1.png"/>联系电话:
                        <span>{{ form.phone }}</span></div>
                </div>

                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png"/>拟采金额:
                        <span>{{ form.tenderAmount }}</span></div>
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>保证金:
                        <span>{{ form.tenderBail }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png"/>币种:
                        <span>{{ form.baseCurName }}</span></div>
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png"/>发布时间:
                        <span>{{ form.releaseDate }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png"/>投标截止时间:
                        <span>{{ form.tenderEndTime }}</span>
                    </div>
                    <div class="leftDiv"><img class="tit-img" src="@/assets/images/zao1.png"/>采购需求程述/招标工程概况:
                        <span>{{ form.remarks }}</span>
                    </div>
                </div>
            </div>
        </div>
        <tenderBar :tenderInfo="form"></tenderBar>
    </div>
</template>
<script>
export default {
    name: 'biggDetail',
    components: { tenderBar: () => import ( '@/components/tenderBar.vue') },
    data () {
        return {
            tenderNotice: '', //招标公告
            TenderCommentList: [],
            tenderTable: [],
            babalTable: [],
            TenderPackageVo: [],
            num: 1,
            tabcurrent1: 0,
            form: {},
            proclamation: true,
            balse: false,
            tenderList: false,
            dataListSelections: [], //表格选中的数据
        }
    },
    created () {
        this.form = this.$route.params.row
        // this.balseList()
    },
}
</script>
<style scoped lang="scss">
.root {
    background-color: #f5f5f5;
    padding-bottom: 50px;
}

span {
    line-height: 1;
}

.router-usher {
    cursor: pointer;

    a:hover {
        color: rgba(34, 111, 199, 1);
    }
}

.row {
    margin-bottom: 20px;
}

.main {
    width: 1326px;
    margin: 10px auto 0;

    .history {
        width: 100%;
        height: 40px;
        margin-top: 10px;
        padding-left: 20px;
        font-size: 12px;
        line-height: 40px;
        background-color: #fff;
        position: relative;

        div {
            width: 64px;
            height: 28px;
            text-align: center;
            line-height: 28px;
            border: 1px solid rgba(230, 230, 230, 1);
            color: rgba(153, 153, 153, 1);
            position: absolute;
            top: 6px;
            right: 10px;
            cursor: pointer;
            user-select: none;
        }
    }
}

.overflow {
    height: 500px;
    overflow: hidden;
    overflow-y: scroll;
}

.status {
    width: 142px;
    height: 40px;
    opacity: 1;
    border-radius: 1px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    line-height: 40px;
    margin-top: 15px;
}

.topBox {
    width: 1326px;
    background-color: #fff;
    padding: 20px 21px 30px 19px;
    position: relative;
}

.title {
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    padding-top: 20px;
    padding-bottom: 54px;
    border-bottom: 1px dashed rgba(229, 229, 229, 1);;
}

.el-table {
    margin: auto;
}

.infoBox {

    padding-top: 32px;

}

.lineBox {
    height: 30px;
    padding-left: 205px;
    margin-top: 21px;
    font-size: 14px;
    overflow: hidden;
    display: flex;

    span {
        margin-left: 10px;
    }

    .leftDiv, .rightDiv {
        width: 50%;
        display: flex;
        align-items: center;

        .tit-img {
            width: 20px;
            height: 20px;
            margin-right: 11px;
        }
    }

}

.button_bule {
    width: 142px;
    height: 40px;
    opacity: 1;
    background: #226FC7;
    font-size: 16px;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    line-height: 40px;
    margin-top: 15px;
    position: relative;
    left: 40%;
}

.logImg {
    z-index: 1;
    width: 136px;
    height: 136px;
    border: 1px dashed rgba(229, 229, 229, 1);
    position: absolute;
    right: 110px;
    top: 0;
}

.detailBox {
    width: 1326px;
    // height: 605px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(229, 229, 229, 1);
    margin-top: 20px;

    .tabBox {
        width: 1324px;
        height: 60px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        background: rgba(250, 250, 250, 1);

        div {
            width: 160px;
            height: 60px;
            text-align: center;
            line-height: 60px;
            font-size: 18px;
            cursor: pointer;
        }

        .tabab {
            background: #fff;
            border-top: solid 2px rgba(34, 111, 199, 1);
        }
    }

    .description {
        width: 1286px;
        // height: 108px;
        border: 1px solid rgba(230, 230, 230, 1);
    }

    .row {
        padding-left: 27px;
        padding-right: 240px;

        .item {
            span {
                font-size: 16px;
                font-weight: 400;
                color: rgba(102, 102, 102, 1);
            }

            div {
                font-size: 16px;
                font-weight: 400;
                color: rgba(0, 0, 0, 1);
            }
        }
    }

    .han {
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 1);
        padding-left: 30px;
        margin-top: 30px;
    }

    .descriptionsBox {
        padding: 20px;
    }
}
</style>
