export default [
    {
        path: '/cLogin',
        name: 'cloudLogin',
        component: () => import('@/pages/cloudCenter/login')
    },
    {
        path: '/cloudCenter',
        name: 'cloudCenter',
        component: () => import('@/pages/cloudCenter/index'),
        children: [
            {
                path: '/cloudCenter/menuManage',
                name: 'menuManage',
                component: () => import('@/pages/cloudCenter/menuManage'),
                meta: {
                    // keepAlive: true,
                    title: '菜单管理',
                },
            },
            {
                path: '/cloudCenter/roleManage',
                name: 'roleManage',
                component: () => import('@/pages/cloudCenter/roleManage'),
                meta: {
                    // keepAlive: true,
                    title: '角色管理',
                },
            },
            {
                path: '/cloudCenter/roleMenuManage',
                name: 'roleMenuManage',
                component: () => import('@/pages/cloudCenter/roleMenuManage')
            },
        ],
    }
]