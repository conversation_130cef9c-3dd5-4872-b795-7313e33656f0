<template>
    <!-- 3类用户注册 -->
    <main>
        <div class="contentBox center" v-loading="viewLoading">
            <div class="tabBox inProgress" v-if="!(registerComplete || registerFail)">
                <div class='goLogin'>已有账号，去<span @click="$router.push('/login')">登录</span></div>
                <el-tabs v-model="activeTab" @tab-click="emptyForm">
                    <!-- 个人用户注册 -->
                    <el-tab-pane label="个人用户" name="individual" >
                        <el-form :model="individualForm" :rules="individualFormRules" ref="individualForm" label-width="0" class="individualForm center"
                                 :inline="false">
                            <h2>个人用户注册</h2>
                            <el-form-item prop="userMobile">
                                <el-input type="number" clearable class="telInput" v-model="individualForm.userMobile" placeholder="请输入11位手机号">
                                    <span slot="prefix">手机号码</span>
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="verificationCode">
                                <div class="verifyBox dfb">
                                    <el-input clearable v-model="individualForm.verificationCode" placeholder="请输入短信验证码"/>
                                    <el-button @click="openPhoneCodeDialog">{{verifyText1}}</el-button>
                                </div>
                            </el-form-item>
                            <el-form-item prop="password">
                                <el-input class="passInput" :type="pwType" v-model="individualForm.password" placeholder="请输入8-16位由数字，字母和特殊字符组成的密码">
                                    <span slot="prefix">登录密码</span>
                                    <i slot="suffix" @click="showPassword = !showPassword"><img :src="eyeOfPass" alt=""></i>
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="agreeTerm">
                                <el-checkbox v-model="individualForm.agreeTerm" :indeterminate="false">您确认阅读并接受<span
                                    @click="openAgreement">《物资采购平台企业认证协议》</span></el-checkbox>
                            </el-form-item>
                            <el-form-item class="loginBtn">
                                <el-button @click="handleIndividualReg()">注册</el-button>
                            </el-form-item>
                        </el-form>
                    </el-tab-pane>
                    <!-- 企业用户注册 -->
                    <el-tab-pane label="企业用户" name="enterprise"  >
                    <!--步骤条-->
                      <div class="steps">
                        <el-steps :active="zcstate" align-center>
                          <el-step title="注册"></el-step>
                          <el-step title="平台初审"></el-step>
                          <el-step title="申请开店"></el-step>
                          <el-step title="合同签约及缴费"></el-step>
                          <el-step title="平台复审"></el-step>
                          <el-step title="完成"></el-step>
                        </el-steps>
                      </div>
                    <!--企业注册表单-->
                        <el-form  :model="enterpriseForm" :rules="enterpriseFormRules" ref="enterpriseForm" label-width="168px" class="enterpriseForm center"
                                  :inline="false">
                          <h2>企业用户注册</h2>
                          <div class="failReason" v-show="isshowNoAdopt">未通过原因:<span>{{noAdoptReason}}</span></div>
                            <div class="df">
                                <el-form-item class="licenseUploader" label="营业执照图片(推荐：750x420)：" prop="businessLicense">
                                    <el-upload class="avatar-uploader" action="fakeaction"
                                               :before-upload="handleBeforeUploadBusinessLicense" name="img"
                                               :on-change="handleRemoveBusinessLicense"
                                               :file-list="businessLicenselist"
                                               :auto-upload="true"
                                               :show-file-list="false"
                                               :http-request="uploadLicenseEnterprise">
                                        <img v-if="enterpriseForm.businessLicense" :src="enterpriseForm.businessLicense" class="avatar"  alt="">
                                        <div v-else class="licenseUploader" >
                                            <img src="../../../assets/images/userCenter/upload_yyzz.png"  alt=""/>
                                        </div>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item class="licenseUploader">
                                    <div class="uploadDemo dfa" >
                                      <el-image
                                          ref="sltpreview"
                                          style="width: 120px; height: 100px"
                                          :src="url"
                                          :preview-src-list="srcList">
                                      </el-image>
<!--                                        <img src="../../../assets/images/userCenter/yyzz_demo.png" alt="">-->
                                        <div class="bigpicture" @click="bigpicture"><span>示例图</span><i class="el-icon-zoom-in"></i></div>
                                    </div>
                                </el-form-item>
                                <div class="uploadTip">请上传10MB以内的PNG，JPG，GIF格式图片</div>
                            </div>
<!--                          企业用户企业名称-统一社会信用代码-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="企业名称：" prop="enterpriseName">
                                        <el-input clearable v-model="enterpriseForm.enterpriseName" placeholder="请填写50字以内的企业名称"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                        <el-input clearable v-model="enterpriseForm.socialCreditCode"  v-on:blur="selectIsPcwpUser(enterpriseForm.socialCreditCode,'companyRegistration')" placeholder="请填写18位信用代码"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          企业用户供方类型-纳税人类别-->
                            <el-row>
                            <el-col :span="11">
                              <el-form-item label="供方类型：" prop="supplierType">
                                <div style="line-height: 50px;"><el-radio v-model="enterpriseForm.supplierType" label="1">生产商</el-radio>
                                  <el-radio v-model="enterpriseForm.supplierType" label="2">贸易商</el-radio></div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="纳税人类别：" prop="taxpayerType">
                                <el-select v-model="enterpriseForm.taxpayerType" placeholder="请选择">
                                  <el-option
                                      v-for="item in taxpayers"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value">
                                  </el-option>
                                </el-select>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户经营范围-法定代表人-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="主营业务：" prop="mainBusiness">
                                        <el-input clearable v-model="enterpriseForm.mainBusiness" placeholder="请填写与营业执照相同的主营业务"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item class="licenseValidTime" label="法定代表人：" prop="legalRepresentative">
                                        <el-input clearable v-model="enterpriseForm.legalRepresentative" placeholder="请填写50字以内的法定代表人姓名"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          企业用户注册时间-有效期-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="企业注册时间：" prop="creationTime">
                                        <el-date-picker
                                            v-model="enterpriseForm.creationTime"
                                            align="right"
                                            type="date"
                                            style="width: 100%"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            placeholder="请选择企业注册时间"
                                            :picker-options="pickerOptions"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item class="licenseValidTime" label="营业执照有效期：" prop="licenseTerm">
                                        <el-date-picker
                                            v-model="enterpriseForm.licenseTerm"
                                            align="right"
                                            type="date"
                                            :value-format="dateFormat"
                                            placeholder="请选择营业执照有效期"
                                            :picker-options="pickerAfterOptions"
                                        />
                                        <el-checkbox label="长期" :indeterminate="false" v-model="longTerm"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="注册资本(万元)：" prop="registeredCapital">
                                        <el-input clearable v-model="enterpriseForm.registeredCapital" placeholder="请填写企业注册资本"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          企业用户企业注册地址-固定工作地址-->
                            <el-row>
                              <el-col :span="11">
                                <el-form-item  class="registerAddress" label="企业注册地址：" prop="address">
                                  <div>
                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="getProvinceName(enterpriseForm.provincesCode)"
                                        placement="top-start"
                                        :disabled="!isProvinceNameLong(enterpriseForm.provincesCode)"
                                    >
                                      <el-select
                                          ref="selectLabel1"
                                          class="province"
                                          v-model="enterpriseForm.provincesCode"
                                          placeholder="省份"
                                          @change="(code) => getSubDistrict(code, 1)"
                                      >
                                        <el-option
                                            v-for="item in addressOptions.province"
                                            :key="item.value"
                                            :label="item.districtName"
                                            :value="item.districtCode"
                                        />
                                      </el-select>
                                    </el-tooltip>
                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="getCityName(enterpriseForm.cityCode)"
                                        placement="top-start"
                                        :disabled="!isCityNameLong(enterpriseForm.cityCode)"
                                    >
                                      <el-select
                                          ref="selectLabel2"
                                          class="city"
                                          v-model="enterpriseForm.cityCode"
                                          placeholder="城市"
                                          @change="(code) => getSubDistrict(code, 2)"
                                      >
                                        <el-option
                                            v-for="item in addressOptions.city"
                                            :key="item.value"
                                            :label="item.districtName"
                                            :value="item.districtCode"
                                        />
                                      </el-select>
                                    </el-tooltip>
                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="getCountyName(enterpriseForm.countyCode)"
                                        placement="top-start"
                                        :disabled="!isCountyNameLong(enterpriseForm.countyCode)"
                                    >
                                      <el-select
                                          ref="selectLabel3"
                                          class="county"
                                          v-model="enterpriseForm.countyCode"
                                          placeholder="区、县"
                                          @visible-change="addressChange"
                                      >
                                        <el-option
                                            v-for="item in addressOptions.district"
                                            :key="item.value"
                                            :label="item.districtName"
                                            :value="item.districtCode"
                                        />
                                      </el-select>
                                    </el-tooltip>
                                  </div>
                                </el-form-item>
                              </el-col>
                              <el-col :span="11" :offset="1">
                                <el-form-item class="registerAddress" label="固定工作地址：" prop="address_gd">
                                  <div>
                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="getGdProvinceName(enterpriseForm.provincesGdCode)"
                                        placement="top-start"
                                        :disabled="!isGdProvinceNameLong(enterpriseForm.provincesGdCode)"
                                    >
                                      <el-select
                                          ref="selectLabel1_gd"
                                          class="province"
                                          v-model="enterpriseForm.provincesGdCode"
                                          placeholder="省份"
                                          @change="(code) => getSubDistrict(code, 3)"
                                      >
                                        <el-option
                                            v-for="item in addressOptionsGd.province"
                                            :key="item.value"
                                            :label="item.districtName"
                                            :value="item.districtCode"
                                        />
                                      </el-select>
                                    </el-tooltip>
                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="getGdCityName(enterpriseForm.cityGdCode)"
                                        placement="top-start"
                                        :disabled="!isGdCityNameLong(enterpriseForm.cityGdCode)"
                                    >
                                      <el-select
                                          ref="selectLabel2_gd"
                                          class="city"
                                          v-model="enterpriseForm.cityGdCode"
                                          placeholder="地级市"
                                          @change="(code) => getSubDistrict(code, 4)"
                                      >
                                        <el-option
                                            v-for="item in addressOptionsGd.city"
                                            :key="item.value"
                                            :label="item.districtName"
                                            :value="item.districtCode"
                                        />
                                      </el-select>
                                    </el-tooltip>
                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="getGdCountyName(enterpriseForm.countyGdCode)"
                                        placement="top-start"
                                        :disabled="!isGdCountyNameLong(enterpriseForm.countyGdCode)"
                                    >
                                      <el-select
                                          ref="selectLabel3_gd"
                                          class="county"
                                          v-model="enterpriseForm.countyGdCode"
                                          placeholder="区、县"
                                          @visible-change="addressChange_gd"
                                      >
                                        <el-option
                                            v-for="item in addressOptionsGd.district"
                                            :key="item.value"
                                            :label="item.districtName"
                                            :value="item.districtCode"
                                        />
                                      </el-select>
                                    </el-tooltip>
                                  </div>
                                </el-form-item>
                              </el-col>
                            </el-row>
<!--                          企业用户详细地址-->
                            <el-row>
                            <el-col :span="11">
                              <el-form-item label="企业注册详细地址：" prop="detailedAddress">
                                <el-input v-model="enterpriseForm.detailedAddress" placeholder="请输入企业注册详细地址"/>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="固定工作详细地址：" prop="workXxdz" placeholder="请输入固定工作详细地址">
                                <el-input v-model="enterpriseForm.workXxdz" />
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户资质信息-->
                            <div class="separ center"></div>
                            <div class="subtitle">资质信息</div>
                            <div class="separ center"></div>
<!--                          企业用户主要业绩-->
                          <h3 style="margin-bottom: 20px;">主要业绩</h3>
                          <el-button size="small" type="primary" class="addcardtop" @click="handleAdd()">添加</el-button>
<!--                          企业用户表格-->
                          <div class="custom-table">
                            <el-table
                                :data="tableData"
                                stripe
                                border
                                style="width: 100%">
                              <!-- 操作列 -->
                              <el-table-column label="操作" width="60px" align="center">
                                <template slot-scope="scope" style="text-align: center">
                                  <el-button
                                      style="width: 30px;margin: 0 auto;padding: 0"
                                      type="text"
                                      icon="el-icon-delete"
                                      @click="handleDelete(scope.$index)">
                                  </el-button>
                                </template>
                              </el-table-column>
                              <!-- 项目名称列 -->
                              <el-table-column prop="projectName" label="项目名称" width="200px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.projectName" placeholder="请输入内容" :border="false" style="outline: none;font-size: 18px">{{ scope.row.projectName }}</el-input>
                                </template>
                              </el-table-column>
                              <!-- 供应物资品类 -->
                              <el-table-column prop="supplyCategory" label="供应物资品类" width="200px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.supplyCategory" placeholder="请输入内容" style="border: none;font-size: 18px">{{ scope.row.supplyCategory }}</el-input>
                                </template>
                              </el-table-column>
                              <!-- 合同金额（万元） -->
                              <el-table-column prop="contractAmount" label="合同金额（万元）" width="120px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.contractAmount" placeholder="请输入内容" style="font-size: 18px">{{ scope.row.contractAmount }}</el-input>
                                </template>
                              </el-table-column>
                              <!-- 供货起止时间 -->
                              <el-table-column prop="ghdate" label="供货起止时间" width="400px" align="center">
                                <template slot-scope="scope">
                                  <el-date-picker
                                      v-model="scope.row.ghdate"
                                      type="daterange"
                                      :value-format="dateFormat"
                                      range-separator="至"
                                      start-placeholder="请选择"
                                      end-placeholder="请选择"  style="width: 100%;">
                                  </el-date-picker>
                                </template>
                              </el-table-column>
                              <!-- 业绩证明人 -->
                              <el-table-column prop="proofPerson" label="业绩证明人" width="100px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.proofPerson"  style="border: none;font-size: 18px">{{ scope.row.proofPerson }}</el-input>
                                </template>
                              </el-table-column>
                              <!-- 证明人联系电话 -->
                              <el-table-column prop="proofPhone" label="证明人联系电话" width="200px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.proofPhone"  style="border: none;font-size: 18px">{{ scope.row.proofPhone }}</el-input>
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
<!--                          企业用户质量认证-->
                          <h3 style="margin-top: 20px;margin-bottom: 20px;">质量认证</h3>
                          <div >
                            <el-checkbox-group v-model="enterpriseForm.certificate" style="display: flex">
                              <el-checkbox label="ISO9001质量体系认证" value="1"></el-checkbox>
                              <el-checkbox label="铁路产品CRCC认证" value="2"></el-checkbox>
                              <el-checkbox label="交通产品CCPC认证" value="3"></el-checkbox>
                              <el-checkbox label="CCC认证" value="4"></el-checkbox>
                              <el-checkbox label="其他质量认证" @change="showQuality()" value="5"></el-checkbox>
                            </el-checkbox-group>
                            <textarea v-model="enterpriseForm.certificateOther" v-show="OtherQualityCertifications" placeholder="请填写其他质量认证" style="width: 1140px;height: 100px;resize:none;"></textarea>
                          </div>
<!--                          企业用户企业基本情况-->
                          <h3 style="margin-top: 20px;margin-bottom: 20px;">企业基本情况</h3>
                          <div class="BasicInformation">
                            <div style="font-size: 18px">企业概况</div>
                            <textarea v-model="enterpriseForm.companyProfile"></textarea>
                          </div>
                          <div class="BasicInformation">
                            <div style="font-size: 18px">财务情况</div>
                            <textarea v-model="enterpriseForm.financialSituation"></textarea>
                          </div>
                          <div class="BasicInformation">
                            <div style="font-size: 18px;font-weight: 400">诉讼情况</div>
                            <textarea v-model="enterpriseForm.litigationSituation"></textarea>
                          </div>
<!--                          企业用户设置对公账户-->
                            <div class="separ center"></div>
                            <div class="setAccount">
                              <div class="subtitle">设置对公账户</div>
                              <div class="subtitletip">企业账户信息，填写内容将写入合同并涉及发票，请谨慎填写。</div>
                            </div>
<!--                          企业用户开户银行、银行账户-->
                            <el-row>
                            <el-col :span="11">
                              <el-form-item label="开户银行：" prop="bankName">
                                <el-input clearable v-model="enterpriseForm.bankName"/>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="银行户名：" prop="accountName">
                                <el-input clearable v-model="enterpriseForm.accountName"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户银行账号-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="银行账号：" prop="bankAccount">
                                <el-input clearable v-model="enterpriseForm.bankAccount"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户开票备注-->
                          <el-row>
                            <el-col :span="24" >
                              <el-form-item label="开票备注：">
                                <el-input clearable v-model="enterpriseForm.invoiceRemark"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户法定代表人-->
                            <div class="separ center"></div>
                            <div class="subtitle">法定代表人</div>
<!--                          企业用户双面身份证上传-->
                            <el-row>
                            <el-col :span="11">
                              <el-form-item label="身份证人像面照（推荐：420x180）：" prop="legalPersonFace">
                                <el-upload
                                    :before-upload="uploadBeforePicture"
                                class="identityUpload face"
                                action="fakeaction"
                                :http-request="(res) => uploadIdentity(res, 3,1)"
                                :show-file-list="false">
                                  <img class="identityUpload" v-if="enterpriseForm.legalPersonFace" :src="enterpriseForm.legalPersonFace"
                                       alt="">
                                  <img class="identityUpload" v-else src="../../../assets/images/userCenter/sfz_renmian.png" alt="">
                                </el-upload>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="身份证国徽面照（推荐：420x180）：" prop="legalPersonNational">
                                <el-upload
                                    :before-upload="uploadBeforePicture"
                                class="identityUpload badge"
                                action="fakeaction"
                                :http-request="(res) => uploadIdentity(res, 4,1)"
                                :show-file-list="false">
                                  <img class="identityUpload" v-if="enterpriseForm.legalPersonNational" :src=" enterpriseForm.legalPersonNational"
                                       alt="">
                                  <img class="identityUpload" v-else src="../../../assets/images/userCenter/upload_sfz.png" alt="">
                                </el-upload>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户姓名身份证号码-->
                            <el-row>
                            <el-col :span="11">
                              <el-form-item label="姓名：" prop="legalPersonName">
                                <el-input clearable v-model="enterpriseForm.legalPersonName" placeholder="请填写真实姓名"/>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="身份证号码：" prop="legalPersonNum">
                                <el-input clearable v-model="enterpriseForm.legalPersonNum" placeholder="请填写18位身份证号码"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户有效开始日期-结束日期-->
                            <el-row>
                            <el-col :span="11">
                              <el-form-item label="有效期开始日期：" prop="lpStartTime">
                                <el-date-picker
                                    v-model="enterpriseForm.lpStartTime"
                                    align="right"
                                    type="date"
                                    style="width: 100%"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    :picker-options="pickerOptions"
                                    placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="lpEndTime">
                                <el-date-picker
                                    v-model="enterpriseForm.lpEndTime"
                                    align="right"
                                    type="date"
                                    :value-format="dateFormat"
                                    :picker-options="pickerAfterOptions"
                                    placeholder="请选择"
                                />
                                <el-checkbox label="长期" :indeterminate="false" v-model="entLpLongTerm"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户设置管理员-->
                            <div class="separ center"></div>
                            <div class="subtitle">设置管理员</div>
<!--                          企业用户双面身份证上传-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="身份证人像面照（推荐：420x180）：" prop="cardPortraitFace">
                                        <el-upload
                                            :before-upload="uploadBeforePicture"
                                        class="identityUpload face"
                                        action="fakeaction"
                                        :http-request="(res) => uploadIdentity(res, 1,1)"
                                        :show-file-list="false">
                                            <img class="identityUpload" v-if="enterpriseForm.cardPortraitFace" :src="enterpriseForm.cardPortraitFace"
                                                 alt="">
                                            <img class="identityUpload" v-else src="../../../assets/images/userCenter/sfz_renmian.png" alt="">
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="身份证国徽面照（推荐：420x180）：" prop="cardPortraitNationalEmblem">
                                        <el-upload
                                            :before-upload="uploadBeforePicture"
                                        class="identityUpload badge"
                                        action="fakeaction"
                                        :http-request="(res) => uploadIdentity(res, 2,1)"
                                                   :show-file-list="false">
                                            <img class="identityUpload" v-if="enterpriseForm.cardPortraitNationalEmblem" :src=" enterpriseForm.cardPortraitNationalEmblem"
                                                 alt="">
                                            <img class="identityUpload" v-else src="../../../assets/images/userCenter/upload_sfz.png" alt="">
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          企业用户姓名身份证号码-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="姓名：" prop="adminName">
                                        <el-input clearable v-model="enterpriseForm.adminName" placeholder="请填写真实姓名"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="身份证号码：" prop="adminNumber">
                                        <el-input clearable v-model="enterpriseForm.adminNumber" placeholder="请填写18位身份证号码"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          企业用户有效开始日期-结束日期-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="有效期开始日期：" prop="adminPeriodStart">
                                <el-date-picker
                                  v-model="enterpriseForm.adminPeriodStart"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  :picker-options="pickerOptions"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="12" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="adminPeriodEnd">
                                <el-date-picker
                                  v-model="enterpriseForm.adminPeriodEnd"
                                  align="right"
                                  type="date"
                                  :value-format="dateFormat"
                                  :picker-options="pickerAfterOptions"
                                  placeholder="请选择"
                                />
                                <el-checkbox label="长期" :indeterminate="false" v-model="entAdminLongTerm"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户手机号 短信验证码-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="手机号码：" prop="adminPhone">
                                        <el-input type="number"  clearable v-model="enterpriseForm.adminPhone" placeholder="请输入11位手机号码"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="短信验证码：" prop="verificationCode">
                                        <div class="verifyBox dfb">
                                            <el-input clearable v-model="enterpriseForm.verificationCode" placeholder="请输入短信验证码"/>
                                            <el-button style="border-radius: 0;" @click="openPhoneCodeDialog">{{ verifyText2 }}</el-button>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          企业用户登录密码-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="登录密码：" prop="adminPassword">
                                        <el-input clearable prefix-icon="el-icon-unlock" show-password :type="pwType" v-model="enterpriseForm.adminPassword" placeholder="请输入8-16位由数字，字母和特殊字符组成的密码"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          企业用户附件上传-->
                            <div class="separ center"></div>
                            <div class="subtitle">附件上传</div>
                            <div class="separ center"></div>
<!--                          承诺书/授权委托书-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="承诺书：" prop="powerOfAttorney">
                                        <el-upload
                                            :limit="1"
                                            :on-exceed="UploadLimitCns"
                                            :on-change="handleChangeCns"
                                            :before-upload="uploadBeforeDocx"
                                            :http-request="uploadCns"
                                            :on-remove="handleRemoveAttorney"
                                            class="upload-demo"
                                            action="https://jsonplaceholder.typicode.com/posts/"
                                            :file-list="cnsFileList">
                                            <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                        </el-upload>
                                        <div @click="downloadCns" style="cursor: pointer;">下载承诺书模板</div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                <el-form-item label="授权委托书：" prop="propsqwts">
                                  <el-upload
                                      :limit="1"
                                      :on-exceed="UploadLimitSqwts"
                                      :before-upload="uploadBeforeDocx"
                                    :http-request="uploadSqwts"
                                    class="upload-demo"
                                    action="https://jsonplaceholder.typicode.com/posts/"
                                    :on-change="handleChangeSqwts"
                                    :on-remove="handleRemovePropsqwts"
                                    :file-list="sqwtsFileList">
                                    <el-button type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                  </el-upload>
                                  <div @click="downloadsqwts" style="cursor: pointer;">下载授权委托书模板</div>
                                </el-form-item>
                              </el-col>
                            </el-row>
<!--                          企业用户法定代表人身份证明-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="法定代表人身份证明：" prop="propfaren" class="single-line-label">
                                <div style="width: 300px;height: 70px;margin-left: 10px">
                                  <el-upload
                                      :limit="1"
                                      :on-exceed="UploadLimitSfzm"
                                      :before-upload="uploadBeforeDocx"
                                      :http-request="uploadFrSfzm"
                                      class="upload-demo"
                                      action="https://jsonplaceholder.typicode.com/posts/"
                                      :on-change="handleChangeFrSfzm"
                                      :on-remove="handleRemovePropfaren"
                                      :file-list="fddbrsfzmFileList">
                                    <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                  </el-upload>
                                  <div @click="downloadFrSfzm" style="cursor: pointer;">下载法定代表人身份证明模板</div>
                                </div>
                              </el-form-item>
                            </el-col>
                          </el-row>
                            <div class="separ center"></div>
<!--                          企业用户最近一期完税证明、税务评级证明-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="最近一期完税证明：" prop="zjyqwszm">
                                <el-upload
                                  :http-request="uploadWszm"
                                  :before-upload="handleBeforeUpload"
                                  class="upload-demo"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :on-change="handleChangeWszm"
                                  :on-remove="handleRemoveZjyqwszm"
                                  :file-list="zjyqwszmFileList">
                                  <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="税务评级证明：" prop="swpjzm">
                                <el-upload
                                  :http-request="uploadSwpjzm"
                                  :before-upload="handleBeforeUpload"
                                  class="upload-demo"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :on-change="handleChangeSwpjzm"
                                  :on-remove="handleRemoveSwpjzm"
                                  :file-list="swpjzmFileList">
                                  <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户有效期开始日期-结束日期-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="有效期开始日期：" prop="tpcStartTime">
                                <el-date-picker
                                  v-model="enterpriseForm.tpcStartTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  :value-format="dateFormat"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期开始日期：" prop="trcStartTime">
                                <el-date-picker
                                  v-model="enterpriseForm.trcStartTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  :value-format="dateFormat"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-row>
                            <el-col :span="12">
                              <el-form-item label="有效期结束日期：" prop="tpcEndTime">
                                <el-date-picker
                                  v-model="enterpriseForm.tpcEndTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="12" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="trcEndTime">
                                <el-date-picker
                                  v-model="enterpriseForm.trcEndTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  :value-format="dateFormat"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <div class="separ center"></div>
<!--                          企业用户信用中国报告、中国执行信息公开网查询情况-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="信用中国报告：" prop="xyzgbg">
                                <el-upload
                                  :http-request="uploadXyzgbg"
                                  :before-upload="handleBeforeUpload"
                                  class="upload-demo"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :on-change="handleChangeXyzgbg"
                                  :on-remove="handleRemoveXyzgbg"
                                  :file-list="xyzgbgFileList">
                                  <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="中国执行信息公开网查询情况：" prop="zgzxxxgk" class="single-line-label">
                                <el-upload
                                  :http-request="uploadZxxx"
                                  :before-upload="handleBeforeUpload"
                                  class="upload-demo2"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :on-change="handleChangeZxxx"
                                  :on-remove="handleRemoveZgzxxxgk"
                                  :file-list="zgzxxxgkwFileList">
                                  <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户有效期开始日期-结束日期-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="有效期开始日期：" prop="ccrStartTime">
                                <el-date-picker
                                  v-model="enterpriseForm.ccrStartTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期开始日期：" prop="zxgkStartTime">
                                <el-date-picker
                                  v-model="enterpriseForm.zxgkStartTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  :value-format="dateFormat"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="有效期结束日期：" prop="ccrEndTime">
                                <el-date-picker
                                  v-model="enterpriseForm.ccrEndTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="zxgkEndTime">
                                <el-date-picker
                                  v-model="enterpriseForm.zxgkEndTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  :value-format="dateFormat"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          企业用户添加资质证书、添加其他资料-->
                          <div class="separ center"></div>
                          <div>
                            <el-button size="small" type="primary" class="addcardtop" @click="addZzzs">添加资质证书</el-button>
                            <el-button size="small" type="primary" class="addcardtop" @click="addQtzl">添加其他资料</el-button>
                          </div>
                          <div style="display: flex;margin-bottom: 20px;">
 <!--                          上传添加资质证书-->
                            <div class="addcard" v-if="isZzzs">
                              <div class="cardtop">
                                <div>资质证书</div>
                                <el-button class="addcarddel" size="small" type="primary" @click="deleteZzzs">删除</el-button>
                              </div>
                              <el-row><el-upload
                                :http-request="uploadZzzs"
                                :before-upload="uploadBeforePicture"
                                class="upload-demo"
                                action="https://jsonplaceholder.typicode.com/posts/"
                                :on-change="handleChange"
                                :file-list="zzzsFileList">
                                <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件</div>
                              </el-upload></el-row>
                              <el-row>
                                <el-form-item class="licenseValidTime" label="有效期开始日期：">
                                  <el-date-picker
                                    v-model="enterpriseForm.qcStartTime"
                                    align="right"
                                    type="date"
                                    style="width: 61%"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    :picker-options="pickerOptions"
                                    placeholder="请选择"
                                  />
                                </el-form-item>
                              </el-row>
                              <el-row>
                                <el-form-item class="licenseValidTime" label="有效期结束日期：">
                                  <el-date-picker
                                    v-model="enterpriseForm.qcEndTime"
                                    align="right"
                                    type="date"
                                    :value-format="dateFormat"
                                    :picker-options="pickerAfterOptions"
                                    placeholder="请选择"
                                  />
                                  <el-checkbox style="margin-right: 10px" label="永久有效" :indeterminate="false" v-model="szQcEndTime"/>
                                </el-form-item>
                              </el-row>
                            </div>
<!--                          上传添加其他证书-->
                            <div class="addcard" v-if="isQtzs">
                              <div class="cardtop">
                                <div>其他资料</div>
                                <el-button class="addcarddel" size="small" type="primary" @click="deleteQtzs">删除</el-button>
                              </div>
                              <el-row><el-upload
                                :http-request="uploadQtzs"
                                :before-upload="uploadBeforePicture"
                                class="upload-demo"
                                action="https://jsonplaceholder.typicode.com/posts/"
                                :on-change="handleChange"
                                :file-list="qtzsFileList">
                                <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件</div>
                              </el-upload></el-row>
                              <el-row>
                                <el-form-item class="licenseValidTime" label="有效期开始日期：">
                                  <el-date-picker
                                    v-model="enterpriseForm.otherStartTime"
                                    align="right"
                                    type="date"
                                    style="width: 61%"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    :picker-options="pickerOptions"
                                    placeholder="请选择"
                                  />
                                </el-form-item>
                              </el-row>
                              <el-row>
                                <el-form-item class="licenseValidTime" label="有效期结束日期：">
                                  <el-date-picker
                                    v-model="enterpriseForm.otherEndTime"
                                    align="right"
                                    type="date"
                                    :value-format="dateFormat"
                                    :picker-options="pickerAfterOptions"
                                    placeholder="请选择"
                                  />
                                  <el-checkbox style="margin-right: 10px" label="永久有效" :indeterminate="false" v-model="szOtherEndTime"/>
                                </el-form-item>
                              </el-row>
                            </div>
                          </div>
                          <div class="separ center"></div>
                            <!-- <el-row v-show="registerPcwpFiles.isPcwoSupplier =='unPcwpSupplier'">
                                <el-col :span="24" style="height: unset;" v-loading="fileLoading">
                                    <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                                        <el-upload
                                            ref="multi-upload"
                                            class="multi-file-uploader"
                                            action="fakeaction"
                                            accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                            :on-remove="handleRemoveIsPcwp"
                                            multiple
                                            :limit="10"
                                            :before-upload="beforeOneOfFilesUpload"
                                            :http-request="uploadOneOfFilesC"
                                            :on-exceed="handleExceed"
                                            :file-list="businessPcwpFormFileList"
                                        >
                                            <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                            <div slot="tip" class="el-upload__tip"><span>请上传以下资料</span>
                                                <div class="file dfa pointer" v-for="file in registerPcwpFiles.files" :key="file.url">
                                                    <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                                </div>
                                            </div>
                                            <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-show="registerPcwpFiles.isPcwoSupplier=='isPcwpSupplier'">
                                <el-col :span="24" style="height: unset;" v-loading="fileLoading">
                                    <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                                        <el-upload
                                            ref="multi-upload"
                                            class="multi-file-uploader"
                                            action="fakeaction"
                                            accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                            :on-remove="handleRemoveNoPcwp"
                                            multiple
                                            :limit="10"
                                            :before-upload="beforeOneOfFilesUpload"
                                            :http-request="uploadOneOfFilesD"
                                            :on-exceed="handleExceed"
                                            :file-list="businessPcwpFormFileList"
                                        >
                                            <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                            <div slot="tip" class="el-upload__tip"><span>请上传以下资料</span>
                                                <div class="file dfa pointer" v-for="file in registerPcwpFiles.files" :key="file.url">
                                                    <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                                </div>
                                            </div>
                                            <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row> -->
                            <el-row>
                                <el-col :span="24" >
                                    <el-form-item prop="agreeTerm" label-width="0px">
                                        <el-checkbox v-model="enterpriseForm.agreeTerm" :indeterminate="false">您确认阅读并接受<span  @click="openAgreement">《物资采购平台企业认证协议》</span>
                                        </el-checkbox>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item class="loginBtn">
                                <el-button @click="handleEnterpriseReg()">注册</el-button>
                            </el-form-item>
                        </el-form>
                    </el-tab-pane>
                    <!-- 个体户注册 -->
                    <el-tab-pane label="个体户" name="business">
                      <!--步骤条-->
                      <div class="steps">
                        <el-steps :active="zcstate" align-center>
                          <el-step title="注册"></el-step>
                          <el-step title="平台初审"></el-step>
                          <el-step title="申请开店"></el-step>
                          <el-step title="合同签约及缴费"></el-step>
                          <el-step title="平台复审"></el-step>
                          <el-step title="完成"></el-step>
                        </el-steps>
                      </div>
                        <el-form class="businessForm center" :model="businessForm" ref="businessForm" :rules="businessFormRules" label-width="168px" :inline="false">
                            <h2>个体户注册</h2>
                            <div class="df">
                                <el-form-item class="licenseUploader" label="营业执照图片(推荐：750x420)：" prop="businessLicense">
                                    <el-upload class="avatar-uploader" action="fakeaction"
                                               :before-upload="handleBeforeUploadBusinessLicense" name="img"
                                               :on-change="handleRemoveBusinessLicenseGth"
                                               :file-list="businessLicenselistGth"
                                               :auto-upload="true"
                                               :show-file-list="false"
                                               :http-request="uploadLicenseBusiness">
                                        <img v-if="businessForm.businessLicense" :src="businessForm.businessLicense" class="avatar"  alt="">
                                        <div v-else class="licenseUploader">
                                            <img src="../../../assets/images/userCenter/upload_yyzz.png" />
                                        </div>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item class="licenseUploader">
                                    <div class="uploadDemo dfa" >
                                      <el-image
                                          ref="sltpreview"
                                          style="width: 120px; height: 100px"
                                          :src="url"
                                          :preview-src-list="srcList">
                                      </el-image>
<!--                                        <img src="../../../assets/images/userCenter/yyzz_demo.png" alt="">-->
                                        <div ><span>示例图</span><i class="el-icon-zoom-in"></i></div>
                                    </div>
                                </el-form-item>
                                <div class="uploadTip">请上传10MB以内的PNG，JPG，GIF格式图片</div>
                            </div>
<!--                          名称\统一社会信用代码-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="企业名称：" prop="enterpriseName">
                                        <el-input clearable v-model="businessForm.enterpriseName" placeholder="请填写50字以内的企业名称"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                        <el-input clearable v-model="businessForm.socialCreditCode" placeholder="请填写18位信用代码"  v-on:blur="selectIsPcwpUser(businessForm.socialCreditCode,'individualRegistration')"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          供方类型-纳税人类别-->
                            <el-row>
                            <el-col :span="11">
                              <el-form-item label="供方类型：" prop="supplierType">
                                <div style="line-height: 50px;"><el-radio v-model="businessForm.supplierType" label="1">生产商</el-radio>
                                  <el-radio v-model="businessForm.supplierType" label="2">贸易商</el-radio></div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="纳税人类别：" prop="taxpayerType">
                                <el-select v-model="businessForm.taxpayerType" placeholder="请选择">
                                  <el-option
                                      v-for="item in taxpayers"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value">
                                  </el-option>
                                </el-select>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          主营业务\法定代表人-->
                            <el-row>
                            <el-col :span="11">
                              <el-form-item label="主营业务：" prop="mainBusiness">
                                <el-input clearable v-model="businessForm.mainBusiness" placeholder="请填写与营业执照一致的主营业务"/>
                              </el-form-item>
                            </el-col>
                              <el-col :span="11" :offset="1">
                                <el-form-item class="licenseValidTime" label="法定代表人：" prop="legalRepresentative">
                                  <el-input clearable v-model="businessForm.legalRepresentative" placeholder="请填写50字以内的法定代表人姓名"/>
                                </el-form-item>
                              </el-col>
                          </el-row>
<!--                          经营者\经营场所-->
                            <!-- <el-row>
                                <el-col :span="11">
                                    <el-form-item label="经营者" prop="operator">
                                        <el-input clearable v-model="businessForm.operator" placeholder="请填写经营者姓名"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="2">
                                    <el-form-item label="经营场所：" prop="placeOfBusiness">
                                        <el-input clearable v-model="businessForm.placeOfBusiness" placeholder="请填写与营业执照一致的经营场所"/>
                                    </el-form-item>
                                </el-col>
                            </el-row> -->
<!--                          注册日期\营业执照有效期-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item prop="creationTime" label="注册时间：">
                                        <el-date-picker
                                          v-model="businessForm.creationTime"
                                          value-format="yyyy-MM-dd HH:mm:ss"
                                          align="right"
                                          type="date"
                                          placeholder="请选择注册时间"
                                          style="width: 100%">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item class="licenseValidTime" label="营业执照有效期：" prop="licenseTerm">
                                        <el-date-picker
                                            v-model="businessForm.licenseTerm"
                                            align="right"
                                            type="date"
                                            :value-format="dateFormat"
                                            placeholder="请选择营业执照有效期"
                                        />
                                        <el-checkbox label="长期" :indeterminate="false" v-model="longTerm"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          注册资本-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="注册资本(万元)：" prop="registeredCapital">
                                        <el-input clearable v-model="businessForm.registeredCapital" placeholder="请填写企业注册资本"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          企业注册地址-固定工作地址-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item class="registerAddress" label="企业注册地址：" prop="address">
                                <div>
                                  <el-tooltip
                                      class="item"
                                      effect="dark"
                                      :content="getProvinceNamegth(businessForm.provincesCode)"
                                      placement="top-start"
                                      :disabled="!isProvinceNameLonggth(businessForm.provincesCode)"
                                  >
                                    <el-select
                                        ref="selectLabel4"
                                        class="province"
                                        v-model="businessForm.provincesCode"
                                        placeholder="省份"
                                        @change="(code) => getSubDistrict(code, 1)"
                                    >
                                      <el-option
                                          v-for="item in addressOptions.province"
                                          :key="item.value"
                                          :label="item.districtName"
                                          :value="item.districtCode"
                                      />
                                    </el-select>
                                  </el-tooltip>
                                  <el-tooltip
                                      class="item"
                                      effect="dark"
                                      :content="getCityNamegth(businessForm.cityCode)"
                                      placement="top-start"
                                      :disabled="!isCityNameLonggth(businessForm.cityCode)"
                                  >
                                    <el-select
                                        ref="selectLabel5"
                                        class="city"
                                        v-model="businessForm.cityCode"
                                        placeholder="地级市"
                                        @change="(code) => getSubDistrict(code, 2)"
                                    >
                                      <el-option
                                          v-for="item in addressOptions.city"
                                          :key="item.value"
                                          :label="item.districtName"
                                          :value="item.districtCode"
                                      />
                                    </el-select>
                                  </el-tooltip>
                                  <el-tooltip
                                      class="item"
                                      effect="dark"
                                      :content="getCountyNamegth(businessForm.countyCode)"
                                      placement="top-start"
                                      :disabled="!isCountyNameLonggth(businessForm.countyCode)"
                                  >
                                    <el-select
                                        ref="selectLabel6"
                                        class="county"
                                        v-model="businessForm.countyCode"
                                        placeholder="区、县"
                                        @visible-change="addressChange"
                                    >
                                      <el-option
                                          v-for="item in addressOptions.district"
                                          :key="item.value"
                                          :label="item.districtName"
                                          :value="item.districtCode"
                                      />
                                    </el-select>
                                  </el-tooltip>
                                </div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item class="registerAddress" label="固定工作地址：" prop="address_gd">
                                <div>
                                  <el-tooltip
                                      class="item"
                                      effect="dark"
                                      :content="getGdProvinceNamegth(businessForm.provincesGdCode)"
                                      placement="top-start"
                                      :disabled="!isGdProvinceNameLonggth(businessForm.provincesGdCode)"
                                  >
                                    <el-select
                                        ref="selectLabel4_gd"
                                        class="province"
                                        v-model="businessForm.provincesGdCode"
                                        placeholder="省份"
                                        @change="(code) => getSubDistrict(code, 3)"
                                    >
                                      <el-option
                                          v-for="item in addressOptionsGd.province"
                                          :key="item.value"
                                          :label="item.districtName"
                                          :value="item.districtCode"
                                      />
                                    </el-select>
                                  </el-tooltip>
                                  <el-tooltip
                                      class="item"
                                      effect="dark"
                                      :content="getGdCityNamegth(businessForm.cityGdCode)"
                                      placement="top-start"
                                      :disabled="!isGdCityNameLonggth(businessForm.cityGdCode)"
                                  >
                                    <el-select
                                        ref="selectLabel5_gd"
                                        class="city"
                                        v-model="businessForm.cityGdCode"
                                        placeholder="地级市"
                                        @change="(code) => getSubDistrict(code, 4)"
                                    >
                                      <el-option
                                          v-for="item in addressOptionsGd.city"
                                          :key="item.value"
                                          :label="item.districtName"
                                          :value="item.districtCode"
                                      />
                                    </el-select>
                                  </el-tooltip>
                                  <el-tooltip
                                      class="item"
                                      effect="dark"
                                      :content="getGdCountyNamegth(businessForm.countyGdCode)"
                                      placement="top-start"
                                      :disabled="!isGdCountyNameLonggth(businessForm.countyGdCode)"
                                  >
                                    <el-select
                                        ref="selectLabel6_gd"
                                        class="county"
                                        v-model="businessForm.countyGdCode"
                                        placeholder="区、县"
                                        @visible-change="addressChange_gd"
                                    >
                                      <el-option
                                          v-for="item in addressOptionsGd.district"
                                          :key="item.value"
                                          :label="item.districtName"
                                          :value="item.districtCode"
                                      />
                                    </el-select>
                                  </el-tooltip>
                                </div>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          详细地址-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="企业注册详细地址：" prop="detailedAddress">
                                <el-input v-model="businessForm.detailedAddress" placeholder="请输入企业注册详细地址"/>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="固定工作详细地址：" prop="workXxdz" placeholder="请输入固定工作详细地址">
                                <el-input v-model="businessForm.workXxdz" />
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          资质信息-->
                          <div class="separ center"></div>
                          <div class="subtitle">资质信息</div>
                          <div class="separ center"></div>
<!--                          主要业绩-->
                          <h3 style="margin-bottom: 20px;">主要业绩</h3>
                          <el-button size="small" type="primary" class="addcardtop" @click="handleAddGth()">添加</el-button>
<!--                          表格-->
                          <div class="custom-table">
                            <el-table
                                :data="tableData_GTH"
                                stripe
                                border
                                style="width: 100%">
                              <!-- 操作列 -->
                              <el-table-column label="操作" width="60px" align="center">
                                <template slot-scope="scope" style="text-align: center">
                                  <el-button
                                      style="width: 30px;margin: 0 auto;padding: 0"
                                      type="text"
                                      icon="el-icon-delete"
                                      @click="handleDeleteGth(scope.$index)">
                                  </el-button>
                                </template>
                              </el-table-column>
                              <!-- 项目名称列 -->
                              <el-table-column prop="projectName" label="项目名称" width="200px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.projectName" placeholder="请输入内容" :border="false" style="outline: none;font-size: 18px">{{ scope.row.projectName }}</el-input>
                                </template>
                              </el-table-column>
                              <!-- 供应物资品类 -->
                              <el-table-column prop="supplyCategory" label="供应物资品类" width="200px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.supplyCategory" placeholder="请输入内容" style="border: none;font-size: 18px">{{ scope.row.supplyCategory }}</el-input>
                                </template>
                              </el-table-column>
                              <!-- 合同金额（万元） -->
                              <el-table-column prop="contractAmount" label="合同金额（万元）" width="120px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.contractAmount" placeholder="请输入内容" style="font-size: 18px">{{ scope.row.contractAmount }}</el-input>
                                </template>
                              </el-table-column>
                              <!-- 供货起止时间 -->
                              <el-table-column prop="ghdate" label="供货起止时间" width="400px" align="center">
                                <template slot-scope="scope">
                                  <el-date-picker
                                      v-model="scope.row.ghdate"
                                      type="daterange"
                                      :value-format="dateFormat"
                                      range-separator="至"
                                      start-placeholder="请选择"
                                      end-placeholder="请选择"  style="width: 100%">
                                  </el-date-picker>
                                </template>
                              </el-table-column>
                              <!-- 业绩证明人 -->
                              <el-table-column prop="proofPerson" label="业绩证明人" width="100px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.proofPerson"  style="border: none;font-size: 18px">{{ scope.row.proofPerson }}</el-input>
                                </template>
                              </el-table-column>
                              <!-- 证明人联系电话 -->
                              <el-table-column prop="proofPhone" label="证明人联系电话" width="200px" align="center">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.proofPhone"  style="border: none;font-size: 18px">{{ scope.row.proofPhone }}</el-input>
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
<!--                          质量认证-->
                          <h3 style="margin-top: 20px;margin-bottom: 20px;">质量认证</h3>
                          <div >
                            <el-checkbox-group v-model="businessForm.certificate" style="display: flex">
                              <el-checkbox label="ISO9001质量体系认证" value="1"></el-checkbox>
                              <el-checkbox label="铁路产品CRCC认证" value="2"></el-checkbox>
                              <el-checkbox label="交通产品CCPC认证" value="3"></el-checkbox>
                              <el-checkbox label="CCC认证" value="4"></el-checkbox>
                              <el-checkbox label="其他质量认证" @change="showQuality_GTH()" value="5"></el-checkbox>
                            </el-checkbox-group>
                            <textarea v-model="businessForm.certificateOther" v-show="OtherQualityCertifications_GTH" placeholder="请填写其他质量认证" style="width: 1140px;height: 100px;resize:none;"></textarea>
                          </div>
<!--                          企业基本情况-->
                          <h3 style="margin-top: 20px;margin-bottom: 20px;">企业基本情况</h3>
                          <div class="BasicInformation">
                            <div>企业概况</div>
                            <textarea v-model="businessForm.companyProfile"></textarea>
                          </div>
                          <div class="BasicInformation">
                            <div>财务情况</div>
                            <textarea v-model="businessForm.financialSituation"></textarea>
                          </div>
                          <div class="BasicInformation">
                            <div>诉讼情况</div>
                            <textarea v-model="businessForm.litigationSituation"></textarea>
                          </div>
<!--                          设置对公账户-->
                          <div class="separ center"></div>
                          <div class="setAccount">
                            <div class="subtitle">设置对公账户</div>
                            <div class="subtitletip">企业账户信息，填写内容将写入合同并涉及发票，请谨慎填写。</div>
                          </div>
<!--                          开户银行、银行账户-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="开户银行：" prop="bankName">
                                <el-input clearable v-model="businessForm.bankName"/>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="银行户名：" prop="accountName">
                                <el-input clearable v-model="businessForm.accountName"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          银行账号-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="银行账号：" prop="bankAccount">
                                <el-input clearable v-model="businessForm.bankAccount"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          开票备注-->
                          <el-row>
                            <el-col :span="24" >
                              <el-form-item label="开票备注：">
                                <el-input clearable v-model="businessForm.invoiceRemark"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          法定代表人-->
                          <div class="separ center"></div>
                          <div class="subtitle">法定代表人</div>
<!--                          双面身份证上传-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="身份证人像面照（推荐：420x180）：" prop="legalPersonFace">
                                <el-upload
                                    :before-upload="uploadBeforePicture"
                                    class="identityUpload face"
                                    action="fakeaction"
                                    :http-request="(res) => uploadIdentity(res, 3,2)"
                                    :show-file-list="false">
                                  <img class="identityUpload" v-if="businessForm.legalPersonFace" :src="businessForm.legalPersonFace"
                                       alt="">
                                  <img class="identityUpload" v-else src="../../../assets/images/userCenter/sfz_renmian.png" alt="">
                                </el-upload>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="身份证国徽面照（推荐：420x180）：" prop="legalPersonNational">
                                <el-upload
                                    :before-upload="uploadBeforePicture"
                                    class="identityUpload badge"
                                    action="fakeaction"
                                    :http-request="(res) => uploadIdentity(res, 4,2)"
                                    :show-file-list="false">
                                  <img class="identityUpload" v-if="businessForm.legalPersonNational" :src=" businessForm.legalPersonNational"
                                       alt="">
                                  <img class="identityUpload" v-else src="../../../assets/images/userCenter/upload_sfz.png" alt="">
                                </el-upload>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          姓名身份证号码-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="姓名：" prop="legalPersonName">
                                <el-input clearable v-model="businessForm.legalPersonName" placeholder="请填写真实姓名"/>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="身份证号码：" prop="legalPersonNum">
                                <el-input clearable v-model="businessForm.legalPersonNum" placeholder="请填写18位身份证号码"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          有效开始日期-结束日期-->
                          <el-row>
                            <el-col :span="12">
                              <el-form-item label="有效期开始日期：" prop="lpStartTime">
                                <el-date-picker
                                    v-model="businessForm.lpStartTime"
                                    align="right"
                                    type="date"
                                    :value-format="dateFormat"
                                    style="width: 100%"
                                    placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="12" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="lpEndTime">
                                <el-date-picker
                                    v-model="businessForm.lpEndTime"
                                    align="right"
                                    type="date"
                                    :value-format="dateFormat"
                                    placeholder="请选择"
                                />
                                <el-checkbox label="长期" :indeterminate="false" v-model="indLpLongTerm"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
                            <div class="separ center"></div>
                            <div class="subtitle">设置管理员</div>
<!--                          身份证人像面照\身份证国徽面照-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="身份证人像面照（推荐：420x180）：" prop="cardPortraitFace">
                                        <el-upload :before-upload="uploadBeforePicture" class="identityUpload face" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1,2)"
                                                   :show-file-list="false">
                                            <img class="identityUpload" v-if="businessForm.cardPortraitFace" :src="businessForm.cardPortraitFace"
                                                 alt="">
                                            <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="身份证国徽面照（推荐：420x180）：" prop="cardPortraitNationalEmblem">
                                        <el-upload :before-upload="uploadBeforePicture" class="identityUpload badge" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2,2)"
                                                   :show-file-list="false">
                                            <img class="identityUpload" v-if="businessForm.cardPortraitNationalEmblem" :src="businessForm.cardPortraitNationalEmblem"
                                                 alt="">
                                            <img class="identityUpload" v-else src="../../../assets/images/userCenter/upload_sfz.png" alt="">
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          姓名\身份证号码-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="姓名：" prop="adminName">
                                        <el-input clearable v-model="businessForm.adminName" placeholder="请填写真实姓名"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="身份证号码：" prop="adminNumber">
                                        <el-input clearable v-model="businessForm.adminNumber" placeholder="请填写18位身份证号码"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          有效开始日期-结束日期-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="有效期开始日期：" prop="adminPeriodStart">
                                <el-date-picker
                                  v-model="businessForm.adminPeriodStart"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  :picker-options="pickerOptions"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="12" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="adminPeriodEnd">
                                <el-date-picker
                                  v-model="businessForm.adminPeriodEnd"
                                  align="right"
                                  type="date"
                                  :value-format="dateFormat"
                                  :picker-options="pickerAfterOptions"
                                  placeholder="请选择"
                                />
                                <el-checkbox label="长期" :indeterminate="false" v-model="indAdminLongTerm"/>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          手机号码\验证码-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="手机号码：" prop="adminPhone">
                                        <el-input type="number"  clearable  v-model="businessForm.adminPhone" placeholder="请输入11位手机号码"/>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="11" :offset="1">
                                    <el-form-item label="验证码：" prop="verificationCode">
                                        <div class="verifyBox dfb">
                                            <el-input clearable v-model="businessForm.verificationCode" placeholder="请输入短信验证码"/>
                                            <el-button style="border-radius: 0;" @click="openPhoneCodeDialog">{{ verifyText3 }}</el-button>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
<!--                          登录密码-->
                            <el-row>
                                <el-col :span="11">
                                    <el-form-item label="登录密码：" prop="adminPassword">
                                        <el-input prefix-icon="el-icon-unlock"  clearable :type="pwType"  v-model="businessForm.adminPassword" show-password placeholder="请输入8-16位由数字，字母和特殊字符组成的密码"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                          <!--附件上传-->
                          <div class="separ center"></div>
                          <div class="subtitle">附件上传</div>
                          <div class="separ center"></div>
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="承诺书：" prop="powerOfAttorney">
                                <el-upload
                                    :limit="1"
                                    :on-exceed="UploadLimitCnsBusiness"
                                  :before-upload="uploadBeforeDocx"
                                  :http-request="uploadCns"
                                  class="upload-demo"
                                  :on-change="handleChangeCnsBusiness"
                                  :on-remove="handleRemoveAttorneyBusiness"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :file-list="cnsFileListBus">
                                  <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                                <div @click="downloadCns" style="cursor: pointer;">下载承诺书模板</div>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="授权委托书：" prop="propsqwts">
                                <el-upload
                                    :limit="1"
                                    :on-exceed="UploadLimitSqwtsBusiness"
                                    :before-upload="uploadBeforeDocx"
                                  :http-request="uploadSqwts"
                                  class="upload-demo"
                                  :on-remove="handleRemovePropsqwtsBusiness"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :on-change="handleChangeSqwtsBusiness"
                                  :file-list="sqwtsFileListBus">
                                  <el-button type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                                <div @click="downloadsqwts" style="cursor: pointer;">下载授权委托书模板</div>
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <!--个体户用户法定代表人身份证明-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="法定代表人身份证明：" prop="propfaren" class="single-line-label">
                                <div style="width: 300px;height: 70px;margin-left: 10px">
                                <el-upload
                                    :limit="1"
                                    :on-exceed="UploadLimitSfzmBusiness"
                                    :before-upload="uploadBeforeDocx"
                                  :http-request="uploadFrSfzm"
                                  class="upload-demo"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :on-change="handleChangeFrSfzmBusiness"
                                  :on-remove="handleRemovePropfarenBusiness"
                                  :file-list="fddbrsfzmFileListBus">
                                  <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                                <div @click="downloadFrSfzm" style="cursor: pointer;">下载法定代表人身份证明模板</div>
                                </div>
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <div class="separ center"></div>
                          <!--最近一期完税证明、税务评级证明-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="最近一期完税证明：" prop="zjyqwszm">
                                <el-upload
                                  :http-request="uploadWszm"
                                  :before-upload="handleBeforeUpload"
                                  :on-remove="handleRemoveZjyqwszmBusiness"
                                  class="upload-demo"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :on-change="handleChangeWszmBusiness"
                                  :file-list="zjyqwszmFileListBus">
                                  <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item label="中国执行信息公开网查询情况：" prop="zgzxxxgk" class="single-line-label">
                                <el-upload
                                  :http-request="uploadZxxx"
                                  :before-upload="handleBeforeUpload"
                                  :on-remove="handleRemoveZgzxxxgkBusiness"
                                  class="upload-demo2"
                                  action="https://jsonplaceholder.typicode.com/posts/"
                                  :on-change="handleChangeZxxxBusiness"
                                  :file-list="zgzxxxgkwFileListBus">
                                  <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                </el-upload>
                              </el-form-item>
                            </el-col>
                          </el-row>
<!--                          有效期开始日期-结束日期-->
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="有效期开始日期：" prop="tpcStartTime">
                                <el-date-picker
                                  v-model="businessForm.tpcStartTime"
                                  align="right"
                                  type="date"
                                  :value-format="dateFormat"
                                  style="width: 100%"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期开始日期：" prop="zxgkStartTime">
                                <el-date-picker
                                  v-model="businessForm.zxgkStartTime"
                                  align="right"
                                  type="date"
                                  :value-format="dateFormat"
                                  style="width: 100%"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-row>
                            <el-col :span="11">
                              <el-form-item label="有效期结束日期：" prop="tpcEndTime">
                                <el-date-picker
                                  v-model="businessForm.tpcEndTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="11" :offset="1">
                              <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="zxgkEndTime">
                                <el-date-picker
                                  v-model="businessForm.zxgkEndTime"
                                  align="right"
                                  type="date"
                                  style="width: 100%"
                                  :value-format="dateFormat"
                                  placeholder="请选择"
                                />
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <!--添加资质证书、添加其他资料-->
                          <div class="separ center"></div>
                          <div>
                            <el-button size="small" type="primary" class="addcardtop" @click="addZzzs">添加资质证书</el-button>
                            <el-button size="small" type="primary" class="addcardtop" @click="addQtzl">添加其他资料</el-button>
                          </div>
                          <div style="display: flex;margin-bottom: 30px">
<!--                          上传添加资质证书-->
                            <div class="addcard" v-if="isZzzs">
                              <div class="cardtop">
                                <div>资质证书</div>
                                <el-button class="addcarddel" size="small" type="primary" @click="deleteZzzs">删除</el-button>
                              </div>
                              <el-row><el-upload
                                :http-request="uploadZzzs"
                                :before-upload="uploadBeforePicture"
                                class="upload-demo"
                                action="https://jsonplaceholder.typicode.com/posts/"
                                :on-change="handleChange"
                                :file-list="zzzsFileListBus">
                                <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件</div>
                              </el-upload></el-row>
                              <el-row>
                                <el-form-item class="licenseValidTime" label="有效期开始日期：">
                                  <el-date-picker
                                    v-model="businessForm.qcStartTime"
                                    align="right"
                                    type="date"
                                    style="width: 61%"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    :picker-options="pickerOptions"
                                    placeholder="请选择"
                                  />
                                </el-form-item>
                              </el-row>
                              <el-row>
                                <el-form-item class="licenseValidTime" label="有效期结束日期：">
                                  <el-date-picker
                                    v-model="businessForm.qcEndTime"
                                    align="right"
                                    type="date"
                                    :value-format="dateFormat"
                                    :picker-options="pickerAfterOptions"
                                    placeholder="请选择"
                                  />
                                  <el-checkbox style="margin-right: 10px" label="永久有效" :indeterminate="false" v-model="szQcEndTime"/>
                                </el-form-item>
                              </el-row>
                            </div>
                            <!--                          上传添加资质证书-->
                            <div class="addcard" v-if="isQtzs">
                              <div class="cardtop">
                                <div>其他资料</div>
                                <el-button class="addcarddel" size="small" type="primary" @click="deleteQtzs">删除</el-button>
                              </div>
                              <el-row><el-upload
                                :http-request="uploadQtzs"
                                :before-upload="uploadBeforePicture"
                                class="upload-demo"
                                action="https://jsonplaceholder.typicode.com/posts/"
                                :on-change="handleChange"
                                :file-list="qtzsFileListBus">
                                <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件</div>
                              </el-upload></el-row>
                              <el-row>
                                <el-form-item class="licenseValidTime" label="有效期开始日期：">
                                  <el-date-picker
                                    v-model="businessForm.otherStartTime"
                                    align="right"
                                    type="date"
                                    style="width: 61%"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    :picker-options="pickerOptions"
                                    placeholder="请选择"
                                  />
                                </el-form-item>
                              </el-row>
                              <el-row>
                                <el-form-item class="licenseValidTime" label="有效期结束日期：">
                                  <el-date-picker
                                    v-model="businessForm.otherEndTime"
                                    align="right"
                                    type="date"
                                    :value-format="dateFormat"
                                    :picker-options="pickerAfterOptions"
                                    placeholder="请选择"
                                  />
                                  <el-checkbox style="margin-right: 10px" label="永久有效" :indeterminate="false" v-model="szOtherEndTime"/>
                                </el-form-item>
                              </el-row>
                            </div>
                          </div>
                          <div class="separ center"></div>
<!--                          附件资料-->
                            <!-- <el-row v-show="registerPcwpFiles.isPcwoSupplier=='unPcwpSupplier'">
                                <el-col :span="24" style="height: unset;" v-loading="fileLoading2">
                                    <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                                        <el-upload
                                            ref="multi-upload"
                                            class="multi-file-uploader"
                                            action="fakeaction"
                                            accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                            :on-remove="handleRemoveIsPcwp"
                                            multiple
                                            :limit="10"
                                            :before-upload="beforeOneOfFilesUpload"
                                            :http-request="uploadOneOfFilesB"
                                            :on-exceed="handleExceed"
                                            :file-list="businessPcwpFormFileList"
                                        >
                                            <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                            <div slot="tip" class="el-upload__tip"><span>请上传以下资料</span>
                                                <div class="file dfa pointer" v-for="file in registerPcwpFiles.files" :key="file.url">
                                                    <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                                </div>
                                            </div>
                                            <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row> -->
<!--                          附件资料-->
                            <!-- <el-row v-show="registerPcwpFiles.isPcwoSupplier=='isPcwpSupplier'">
                                <el-col :span="24" style="height: unset;" v-loading="fileLoading2">
                                    <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                                        <el-upload
                                            ref="multi-upload"
                                            class="multi-file-uploader"
                                            action="fakeaction"
                                            accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                            :on-remove="handleRemoveIsPcwp"
                                            multiple
                                            :limit="10"
                                            :before-upload="beforeOneOfFilesUpload"
                                            :http-request="uploadOneOfFiles"
                                            :on-exceed="handleExceed"
                                            :file-list="businessPcwpFormFileList"
                                        >
                                            <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                            <div slot="tip" class="el-upload__tip"><span>请上传以下资料</span>
                                                <div class="file dfa pointer" v-for="file in registerPcwpFiles.files" :key="file.url">
                                                    <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                                </div>
                                            </div>
                                            <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row> -->
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item prop="agreeTerm" label-width="0px">
                                        <el-checkbox v-model="businessForm.agreeTerm" :indeterminate="false">
                                            您确认阅读并接受<span @click="openAgreement">《物资采购平台企业认证协议》</span>
                                        </el-checkbox>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item class="loginBtn">
                                <el-button @click="handleBusinessReg()">注册</el-button>
                            </el-form-item>
                        </el-form>
                    </el-tab-pane>
                </el-tabs>
                <!-- 平台协议 -->
                <Dialog title="物资采购平台企业认证协议" :visible.sync="showTerm">
                    <div style="height: 600px;overflow: auto;"><span v-html="content"></span></div>
                    <span slot="footer">
                        <el-button @click="showTerm = false">取消</el-button>
                        <el-button type="primary" @click="showTerm = false">确定</el-button>
                    </span>
                </Dialog>
                <Dialog
                    :close-on-click-modal="false" width="40%" title="图形验证码" top="30vh"
                    :visible.sync="phoneCodeDialogVisible" @open="getPhoneVerificationImg"
                >
                    <div class="verifyBox dfc" style="padding-bottom: 30px">
                        <template v-if="tabNum === 0">
                            <el-input placeholder="请输入图形验证码" v-model="verification[0].verifyInput"/>
                            <img :src="verification[0].verifyImg" @click="getPhoneVerificationImg()" alt="">
                        </template>
                        <template v-if="tabNum === 1">
                            <el-input placeholder="请输入图形验证码" v-model="verification[1].verifyInput"/>
                            <img :src="verification[1].verifyImg" @click="getPhoneVerificationImg()" alt="">
                        </template>
                        <template v-if="tabNum === 2">
                            <el-input placeholder="请输入图形验证码" v-model="verification[2].verifyInput"/>
                            <img :src="verification[2].verifyImg" @click="getPhoneVerificationImg()" alt="">
                        </template>
                    </div>
                    <span slot="footer">
                        <el-button
                            class="codeDialogBtn" style="margin-right: 30px;" @click="phoneCodeDialogVisible = false"
                        >取消</el-button>
                        <el-button
                            class="codeDialogBtn" type="primary" @click="checkPhoneVerificationCode()"
                        >确定</el-button>
                    </span>
                </Dialog>
                <Dialog
                    :close-on-click-modal="false" width="40%" title="图形验证码" top="30vh"
                    :visible.sync="formCodeDialogVisible" @open="getFormVerificationPic"
                >
                    <div class="verifyBox dfc" style="padding-bottom: 30px">
                        <template v-if="tabNum === 0">
                            <el-input placeholder="请输入图形验证码" v-model="individualForm.verifyInput"/>
                            <img :src="individualForm.verifyImg" @click="getFormVerificationPic()" alt="">
                        </template>
                        <template v-if="tabNum === 1">
                            <el-input placeholder="请输入图形验证码" v-model="enterpriseForm.verifyInput"/>
                            <img :src="enterpriseForm.verifyImg" @click="getFormVerificationPic()" alt="">
                        </template>
                        <template v-if="tabNum === 2">
                            <el-input placeholder="请输入图形验证码" v-model="businessForm.verifyInput"/>
                            <img :src="businessForm.verifyImg" @click="getFormVerificationPic()" alt="">
                        </template>
                    </div>
                    <span slot="footer">
                        <el-button
                            class="codeDialogBtn" style="margin-right: 30px;" @click="formCodeDialogVisible = false"
                        >取消</el-button>
                        <el-button class="codeDialogBtn" type="primary" @click="confirmRegister">确定</el-button>
                    </span>
                </Dialog>
            </div>
<!--          注册成功页面-->
            <div class="tabBox" v-if="registerComplete">
                <enterpriseReg :title="activeTab"></enterpriseReg>
            </div>
<!--          注册失败页面-->
            <div class="tabBox" v-if="registerFail">
                <failReg @hideFail="hideFail" :title="activeTab" :msg="regFailMsg"></failReg>
            </div>
        </div>
    </main>
</template>

<script>
import icohide from '../../../assets/images/ico_hide.png'
import icoshow from '../../../assets/images/ico_show.png'
import Dialog from '@/pages/frontStage/components/dialog.vue'
import enterpriseReg from './enterpriseReg.vue'
import failReg from './fail.vue'
import { getCascaderOptions } from '@/api/platform/common/components'
import { registerEnterprise, registerPerson, selectIsPcwpUserByCode } from '@/api/frontStage/register'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { getVerificationImg, getSendCodeImg, checkSendCodeVerify, registerSendCode, getPrivateKeyId } from '@/api/frontStage/login'
import { createWFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { encrypt } from '@/utils/common'
import { findByProgramaKey } from '@/api/w/richContent'
import { mapState } from 'vuex'
import { getInfo } from '@/api/platform/shop/shopManager'

export default {
    components: { enterpriseReg, failReg, Dialog },
    data () {
        return {
            //营业执照示例图
            url: require('@/assets/images/userCenter/yyzz_demo.png'),
            srcList: [require('@/assets/images/userCenter/businessLicense.png')],
            businessLicenselist: [],
            businessLicenselistGth: [],
            //企业注册审批未通过原因
            isshowNoAdopt: false,
            noAdoptReason: '',
            //文本框是否显示
            OtherQualityCertifications: false,
            OtherQualityCertifications_GTH: false,
            //企业注册表格相关
            tableData: [
                { projectName: '', supplyCategory: '', contractAmount: '', ghdate: [], proofPerson: '', proofPhone: '', }
            ],
            //个体户注册表格相关
            tableData_GTH: [
                { projectName: '', supplyCategory: '', contractAmount: '', ghdate: [], proofPerson: '', proofPhone: '', }
            ],
            //纳税人类别
            taxpayers: [
                {
                    value: '1',
                    label: '一般纳税人'
                },
                {
                    value: '2',
                    label: '小规模纳税人'
                }
            ],
            value: '',
            fileList: [],
            //个体户pcwp入库附件资料
            businessPcwpFormFileList: [],
            enterpriseFormFileList: [],
            //企业pcwp入库附件资料
            enterprisePcwpFormFileList: [],
            PcwpFormFileList: [],
            fileLoading: false,
            fileLoading2: false,
            phoneCodeDialogVisible: false,
            formCodeDialogVisible: false,
            verification: [
                { verifyId: '', verifyInput: '', verifyImg: '' },
                { verifyId: '', verifyInput: '', verifyImg: '' },
                { verifyId: '', verifyInput: '', verifyImg: '' }
            ],
            content: '',
            regFailMsg: '',
            activeTabLabelTilte: 'individual',
            viewLoading: false,
            socialCreditCodeFile: [],
            isPCWP: '',
            longTerm: false,
            szQcEndTime: false,
            szOtherEndTime: false,
            verifyText1: '获取短信验证码',
            verifyText2: '获取短信验证码',
            verifyText3: '获取短信验证码',
            dateFormat: 'yyyy-MM-dd HH:mm:ss',
            activeTab: 'individual',
            tabs: ['individual', 'enterprise', 'business'],
            showPassword: false,
            showTerm: false,
            eyeOfPass: icohide,
            pwType: 'password',
            registerComplete: false, // 注册成功
            registerFail: false, // 注册失败
            // 个人注册表单
            individualForm: {
                userMobile: '',
                verificationCode: '',
                password: '',
                agreeTerm: false,
                mallType: 0,
                verifyImg: '',
                verifyId: '',
            },
            cnsFileList: [],
            sqwtsFileList: [],
            fddbrsfzmFileList: [],
            zjyqwszmFileList: [],
            zgzxxxgkwFileList: [],
            swpjzmFileList: [],
            xyzgbgFileList: [],
            zzzsFileList: [],
            qtzsFileList: [],
            cnsFileListBus: [],
            sqwtsFileListBus: [],
            fddbrsfzmFileListBus: [],
            zjyqwszmFileListBus: [],
            zgzxxxgkwFileListBus: [],
            zzzsFileListBus: [],
            qtzsFileListBus: [],
            // 企业注册表单、个体户注册表单
            enterpriseForm: {
                businessLicenseId: [],
                businessLicense: '',
                enterpriseName: '',
                socialCreditCode: '',
                enterpriseType: '',
                legalRepresentative: '',
                creationTime: '',
                licenseTerm: '',
                registeredCapital: '',
                provinces: '',
                city: '',
                county: '',
                provincesCode: '',
                cityCode: '',
                countyCode: '',
                provincesGdCode: '',
                cityGdCode: '',
                countyGdCode: '',
                detailedAddress: '',
                mainBusiness: '',
                taxRate: null,
                cardPortraitFace: '',
                cardPortraitNationalEmblem: '',
                adminPhone: '',
                verificationCode: '',
                adminName: '',
                adminPassword: '',
                adminNumber: '',
                agreeTerm: false,
                files: [],
                mallType: 0,
                verifyImg: '',
                verifyId: '',
                legalPersonFace: '',
                legalPersonFaceId: '',
                legalPersonNational: '',
                legalPersonNationalId: '',
                supplierType: '1',
                certificate: [],
                certificateOther: '',
                workXxdz: '',
                provincesGd: '',
                cityGd: '',
                countyGd: '',
                qualificationCertificate: null,
                qualificationCertificateId: null,
                qcStartTime: null,
                qcEndTime: null,
                other: null,
                otherId: null,
                otherStartTime: null,
                otherEndTime: null
            },
            businessForm: {
                businessLicenseId: [],
                businessLicense: '',
                enterpriseName: '',
                socialCreditCode: '',
                enterpriseType: '',
                operator: '',
                creationTime: '',
                provinces: '',
                city: '',
                county: '',
                provincesCode: '',
                cityCode: '',
                countyCode: '',
                provincesGdCode: '',
                cityGdCode: '',
                countyGdCode: '',
                taxRate: '',
                detailedAddress: '',
                placeOfBusiness: '',
                mainBusiness: '',
                cardPortraitFace: '',
                cardPortraitNationalEmblem: '',
                adminPhone: '',
                verificationCode: '',
                adminName: '',
                adminPassword: '',
                adminNumber: '',
                agreeTerm: false,
                files: [],
                mallType: 0,
                verifyImg: '',
                verifyId: '',
                //个体户新增
                //纳税人、供应商类型
                taxpayerType: '',
                supplierType: '1',
                //法定代表人
                legalRepresentative: '',
                //营业执照有效期
                licenseTerm: '',
                //注册资本
                registeredCapital: '',
                //详细地址
                workXxdz: '',
                //质量认证、其他质量认证
                certificateOther: '',
                certificate: [],
                //企业基本情况文本框
                litigationSituation: '',
                financialSituation: '',
                companyProfile: '',
                //开户银行、银行户名
                accountName: '',
                bankName: '',
                //银行账号
                bankAccount: '',
                //开票备注
                invoiceRemark: '',
                legalPersonFace: '',
                legalPersonFaceId: '',
                legalPersonNational: '',
                legalPersonNationalId: '',
                //法定代表人姓名身份证号
                legalPersonNum: '',
                legalPersonName: '',
                provincesGd: '',
                cityGd: '',
                countyGd: '',
                qualificationCertificate: null,
                qualificationCertificateId: null,
                qcStartTime: null,
                qcEndTime: null,
                other: null,
                otherId: null,
                otherStartTime: null,
                otherEndTime: null
            },
            registerPcwpFiles: {
                isPcwoSupplier: '',
                files: []
            },
            pcwpfileList: [],
            //个人表单数据验证
            individualFormRules: {
                userMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                verificationCode: [
                    { required: true, message: '请输入6位短信验证码', trigger: 'blur' },
                    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' },
                ],
                password: { required: true, validator: this.validatePassword, trigger: 'blur' },
            },
            //企业表单验证
            enterpriseFormRules: {
                // businessLicense: { required: true, message: '请上传营业执照!', trigger: 'blur' },
                businessLicense: { required: true, validator: this.validatebusinessLicense, trigger: 'blur' },
                powerOfAttorney: { required: true, validator: this.validatePowerOfAttorney, trigger: 'change' },
                propfaren: { required: true, validator: this.validatePropfaren, trigger: 'blur' },
                propsqwts: { required: true, validator: this.validatePropsqwts, trigger: 'change' },
                zjyqwszm: { required: true, validator: this.validateZjyqwszm, trigger: 'change' },
                swpjzm: { required: true, validator: this.validateSwpjzm, trigger: 'change' },
                xyzgbg: { required: true, validator: this.validateXyzgbg, trigger: 'change' },
                zgzxxxgk: { required: true, validator: this.validateZgzxxxgk, trigger: 'change' },
                //企业用户注册企业名称验证
                enterpriseName: [
                    { required: true, message: '请填写企业名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '请填写50字以内的企业名称', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('企业名称不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                //统一社会信用代码
                socialCreditCode: [
                    { required: true, message: '请输入统一信用代码', trigger: 'blur' },
                    { validator: this.validateSocialCreditCode, trigger: 'blur' }
                ],
                supplierType: [
                    { required: true, message: '请选择供方类型', trigger: 'blur' }
                ],
                taxpayerType: [
                    { required: true, message: '请选择纳税人类别', trigger: 'blur' }
                ],
                legalRepresentative: [
                    { required: true, message: '请填写企业法定代表人', trigger: 'blur' },
                    { min: 2, max: 10, message: '企业法定代表人姓名在2-20个字之间', trigger: 'blur' }
                ],
                //企业用户注册注册资本
                registeredCapital: [
                    { required: true, message: '请输入注册资本', trigger: 'blur' },
                    {
                        pattern: /^\d+(\.\d{1,3})?$/,
                        message: '只能输入数字',
                        trigger: 'blur'
                    },
                    {
                        min: 1,
                        max: 18,
                        message: '长度需在1-18位之间',
                        trigger: 'blur'
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('注册资本不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                licenseTerm: { required: true, validator: this.validateDate, trigger: 'blur' },
                lpEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                legalPersonFace: { required: true, message: '请上传法定代表人身份证人像面照!', trigger: 'blur' },
                //开户银行
                bankName: [
                    { required: true, message: '请输入开户银行',  trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('开户银行不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                //银行户名
                accountName: [
                    { required: true, message: '请输入银行户名',  trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('银行户名不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                //银行账号
                bankAccount: [
                    { required: true, message: '请输入银行账号',  trigger: 'blur' },
                    { min: 1, max: 20, message: '长度不能超过20个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('银行账号不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                creationTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                tpcStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                trcStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                tpcEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                trcEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                ccrStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                zxgkStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                lpStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                adminPeriodStart: { required: true, validator: this.validateDate, trigger: 'blur' },
                adminPeriodEnd: { required: true, validator: this.validateDate, trigger: 'blur' },
                ccrEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                zxgkEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                // licenseTerm: { required: true, validator: this.validateDate, trigger: 'blur' },
                address: { required: true, validator: this.validateAddress, trigger: 'change' },
                address_gd: { required: true, validator: this.validateAddress_gd, trigger: 'change' },
                //企业地址
                detailedAddress: [
                    { required: true, message: '请填写企业注册详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制，企业注册地址最长为100个字', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('企业地址不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                //固定工作地址
                workXxdz: [
                    { required: true, message: '请填写固定工作详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制，固定工作地址最长为100个字', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('固定工作地址不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                taxRate: [
                    { required: true, validator: this.validateTaxRate, trigger: 'blur' },
                ],
                //主营业务
                mainBusiness: [
                    { required: true, message: '请填写与营业执照相同的主营业务', trigger: 'blur' },
                    { min: 1, max: 1000, message: '超过限制，主营业务最长为1000个字', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('主营业务不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                // legalPersonFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                legalPersonNational: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                cardPortraitFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                cardPortraitNationalEmblem: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                adminName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('真实姓名不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                adminNumber: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                legalPersonName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('真实姓名不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                legalPersonNum: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                adminPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                verificationCode: [
                    { required: true, message: '请输入6位短信验证码', trigger: 'blur' },
                    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' },
                ],
                adminPassword: { min: 8, max: 16, required: true, validator: this.validatePassword, trigger: 'blur' },
            },
            //个体户表单验证
            businessFormRules: {
                // files:[
                //     { required: true, message: '请上传附件资料', trigger: 'blur' }
                // ],
                // businessLicense: { required: true, message: '请上传营业执照！', trigger: 'blur' },businessLicenseId
                businessLicenseId: [],
                businessLicense: { required: true, validator: this.validatebusinessLicenseGth, trigger: 'change' },
                propfaren: { required: true, validator: this.validatePropfarenBusiness, trigger: 'blur' },
                //企业名称
                enterpriseName: [
                    { required: true, message: '请填写企业名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '请填写50字以内的企业名称', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('企业名称不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                socialCreditCode: [
                    { required: true, message: '请输入统一信用代码', trigger: 'blur' },
                    { min: 18, max: 18, message: '请输入18位统一信用代码', trigger: 'blur' }
                ],
                supplierType: [
                    { required: true, message: '请选择供方类型', trigger: 'blur' }
                ],
                taxpayerType: [
                    { required: true, message: '请选择纳税人类别', trigger: 'blur' }
                ],
                operator: [
                    { required: true, message: '请输入经营者姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                creationTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                lpStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                adminPeriodStart: { required: true, validator: this.validateDate, trigger: 'blur' },
                adminPeriodEnd: { required: true, validator: this.validateDate, trigger: 'blur' },
                tpcStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                zxgkStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                tpcEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                zxgkEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                placeOfBusiness: [
                    { required: true, message: '请填写与营业执照一致的经营场所', trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' }
                ],
                taxRate: [
                    { required: true, validator: this.validateBusinessTaxRate, trigger: 'blur' },
                ],
                //主营业务
                mainBusiness: [
                    { required: true, message: '请填写与营业执照相同的主营业务', trigger: 'blur' },
                    { min: 1, max: 1000, message: '超过限制，主营业务最长为1000个字', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('主营业务不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                legalRepresentative: [
                    { required: true, message: '请填写企业法定代表人', trigger: 'blur' },
                    { min: 2, max: 20, message: '企业法定代表人姓名在2-20个字之间', trigger: 'blur' }
                ],
                //个体户注册注册资本
                registeredCapital: [
                    { required: true, message: '请输入注册资本',  trigger: 'blur' },
                    {
                        pattern: /^\d+(\.\d{1,3})?$/,
                        message: '只能输入数字',
                        trigger: 'blur'
                    },
                    {
                        min: 1,
                        max: 18,
                        message: '长度需在1-18位之间',
                        trigger: 'blur'
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('注册资本不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                address: { required: true, validator: this.validateBusinessAddress, trigger: 'change' },
                address_gd: { required: true, validator: this.validateBusinessAddress_gd, trigger: 'change' },
                //企业注册地址
                detailedAddress: [
                    { required: true, message: '请填写企业注册详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制，企业注册详细地址最长为100个字', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('企业地址不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                //固定工作地址
                workXxdz: [
                    { required: true, message: '请填写固定工作详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制，固定工作地址最长为100个字', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('固定工作地址不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                //开户银行
                bankName: [
                    { required: true, message: '请输入开户银行',  trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('开户银行不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                //银行户名
                accountName: [
                    { required: true, message: '请输入银行户名',  trigger: 'blur' },
                    { min: 1, max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('银行户名不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                //银行账号
                bankAccount: [
                    { required: true, message: '请输入银行账号',  trigger: 'blur' },
                    { min: 1, max: 20, message: '长度不能超过20个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('银行账号不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                legalPersonFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                legalPersonNational: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                cardPortraitFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                cardPortraitNationalEmblem: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                legalPersonName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('真实姓名不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                legalPersonNum: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                adminName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value.trim() === '') {
                                callback(new Error('真实姓名不能全为空格'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                powerOfAttorney: { required: true, validator: this.validatePowerOfAttorneyBusiness, trigger: 'change' },
                propsqwts: { required: true, validator: this.validatePropsqwtsBusiness, trigger: 'change' },
                zjyqwszm: { required: true, validator: this.validateZjyqwszmBusiness, trigger: 'change' },
                zgzxxxgk: { required: true, validator: this.validateZgzxxxgkBusiness, trigger: 'change' },
                adminNumber: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                adminPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                verificationCode: [
                    { required: true, message: '请输入6位短信验证码', trigger: 'blur' },
                    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' },
                ],
                adminPassword: {  min: 8, max: 16, required: true, validator: this.validatePassword, trigger: 'blur' },
            },
            pickerAfterOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            // 日期选择器选项
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() > Date.now()
                },
                shortcuts: [{
                    text: '今天',
                    onClick (picker) {
                        picker.$emit('pick', new Date())
                    }
                }, {
                    text: '昨天',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24)
                        picker.$emit('pick', date)
                    }
                }, {
                    text: '一周前',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
                        picker.$emit('pick', date)
                    }
                }]
            },
            // 地址选择器选项
            addressOptions: {
                province: [],
                city: [],
                district: []
            },
            addressOptionsGd: {
                province: [],
                city: [],
                district: []
            },
            isZzzs: false,
            isQtzs: false,
            zcstate: 0,
            szlpEndTime: false,
            // 企业法定代表人长期
            entLpLongTerm: false,
            // 企业管理员长期
            entAdminLongTerm: false,
            // 个体户法定代表人长期
            indLpLongTerm: false,
            // 个体户管理员长期
            indAdminLongTerm: false
        }
    },
    watch: {
        longTerm (val) {
            if(val) {
                this.enterpriseForm.licenseTerm = null
                this.businessForm.licenseTerm = null
            }
        },
        szQcEndTime (val) {
            if(val) {
                this.enterpriseForm.qcEndTime = null
                this.enterpriseForm.qcStartTime = null
                this.businessForm.qcEndTime = null
                this.businessForm.qcStartTime = null
            }
        },
        szOtherEndTime (val) {
            if(val) {
                this.enterpriseForm.otherStartTime = null
                this.enterpriseForm.otherEndTime = null
                this.businessForm.otherStartTime = null
                this.businessForm.otherEndTime = null
            }
        },
        szlpEndTime (val) {
            if(val) {
                this.enterpriseForm.lpEndTime = null
                this.businessForm.lpEndTime = null
            }
        },
        //日期和长期按钮变化
        // 企业法定代表人日期变化
        'enterpriseForm.lpEndTime' (val) {
            if (val) this.entLpLongTerm = false
        },
        // 企业管理员日期变化
        'enterpriseForm.adminPeriodEnd' (val) {
            if (val) this.entAdminLongTerm = false
        },
        // 个体户法定代表人日期变化
        'businessForm.lpEndTime' (val) {
            if (val) this.indLpLongTerm = false
        },
        // 个体户管理员日期变化
        'businessForm.adminPeriodEnd' (val) {
            if (val) this.indAdminLongTerm = false
        },
        // 企业法定代表人长期变化
        entLpLongTerm (newVal) {
            if (newVal) this.enterpriseForm.lpEndTime = null
        },
        // 企业管理员长期变化
        entAdminLongTerm (newVal) {
            if (newVal) this.enterpriseForm.adminPeriodEnd = null
        },
        // 个体户法定代表人长期变化
        indLpLongTerm (newVal) {
            if (newVal) this.businessForm.lpEndTime = null
        },
        // 个体户管理员长期变化
        indAdminLongTerm (newVal) {
            if (newVal) this.businessForm.adminPeriodEnd = null
        },
        'enterpriseForm.licenseTerm': {
            handler (val) {
                if(val) {
                    this.longTerm = false
                }
            }
        },
        'businessForm.licenseTerm': {
            handler (val) {
                if(val) {
                    this.longTerm = false
                }
            }
        },
        'enterpriseForm.qcEndTime': {
            handler (val) {
                if(val) {
                    this.szQcEndTime = false
                }
            }
        },
        'businessForm.qcEndTime': {
            handler (val) {
                if(val) {
                    this.szQcEndTime = false
                }
            }
        },
        'enterpriseForm.otherEndTime': {
            handler (val) {
                if(val) {
                    this.szOtherEndTime = false
                }
            }
        },
        'businessForm.otherEndTime': {
            handler (val) {
                if(val) {
                    this.szOtherEndTime = false
                }
            }
        },
        showPassword: {
            handler (newVal) {
                // 切换密码是否显示
                if (newVal) {
                    this.eyeOfPass = icoshow
                    this.pwType = 'text'
                    return
                }
                this.eyeOfPass = icohide
                this.pwType = 'password'
            }
        },
        activeTab: {
            handler () {
                this.getPhoneVerificationImg()
            },
            immediate: true
        }
    },
    computed: {
        ...mapState(['userInfo']),
        tabNum () {
            let num = this.tabs.indexOf(this.activeTab)
            return num < 0 ? 2 : num
        },
    },
    methods: {
        //企业用户注册企业注册地址显示完整地址
        getProvinceName (code) {
            if (!code) return ''
            const province = this.addressOptions.province.find(p => p.districtCode === code)
            return province ? province.districtName : ''
        },
        isProvinceNameLong (code) {
            const name = this.getProvinceName(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        getCityName (code) {
            if (!code) return ''
            const city = this.addressOptions.city.find(c => c.districtCode === code)
            return city ? city.districtName : ''
        },
        isCityNameLong (code) {
            const name = this.getCityName(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        getCountyName (code) {
            if (!code) return ''
            const county = this.addressOptions.district.find(c => c.districtCode === code)
            return county ? county.districtName : ''
        },
        isCountyNameLong (code) {
            const name = this.getCountyName(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        //企业用户注册企业注册固定地址显示完整地址
        getGdProvinceName (code) {
            if (!code) return ''
            const province = this.addressOptionsGd.province.find(p => p.districtCode === code)
            return province ? province.districtName : ''
        },
        isGdProvinceNameLong (code) {
            const name = this.getGdProvinceName(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        getGdCityName (code) {
            if (!code) return ''
            const city = this.addressOptionsGd.city.find(c => c.districtCode === code)
            return city ? city.districtName : ''
        },
        isGdCityNameLong (code) {
            const name = this.getGdCityName(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        getGdCountyName (code) {
            if (!code) return ''
            const county = this.addressOptionsGd.district.find(c => c.districtCode === code)
            return county ? county.districtName : ''
        },
        isGdCountyNameLong (code) {
            const name = this.getGdCountyName(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        //个体户注册企业注册地址显示完整地址
        getProvinceNamegth (code) {
            if (!code) return ''
            const province = this.addressOptions.province.find(p => p.districtCode === code)
            return province ? province.districtName : ''
        },
        isProvinceNameLonggth (code) {
            const name = this.getProvinceNamegth(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        getCityNamegth (code) {
            if (!code) return ''
            const city = this.addressOptions.city.find(c => c.districtCode === code)
            return city ? city.districtName : ''
        },
        isCityNameLonggth (code) {
            const name = this.getCityNamegth(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        getCountyNamegth (code) {
            if (!code) return ''
            const county = this.addressOptions.district.find(c => c.districtCode === code)
            return county ? county.districtName : ''
        },
        isCountyNameLonggth (code) {
            const name = this.getCountyNamegth(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        //个体户注册企业注册固定地址显示完整地址
        getGdProvinceNamegth (code) {
            if (!code) return ''
            const province = this.addressOptionsGd.province.find(p => p.districtCode === code)
            return province ? province.districtName : ''
        },
        isGdProvinceNameLonggth (code) {
            const name = this.getGdProvinceNamegth(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        getGdCityNamegth (code) {
            if (!code) return ''
            const city = this.addressOptionsGd.city.find(c => c.districtCode === code)
            return city ? city.districtName : ''
        },
        isGdCityNameLonggth (code) {
            const name = this.getGdCityNamegth(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        getGdCountyNamegth (code) {
            if (!code) return ''
            const county = this.addressOptionsGd.district.find(c => c.districtCode === code)
            return county ? county.districtName : ''
        },
        isGdCountyNameLonggth (code) {
            const name = this.getGdCountyNamegth(code)
            if (name.length == 0) {
                return 0
            }else{
                return 1
            }
        },
        //点击示例图
        bigpicture () {
            this.$refs.sltpreview.clickHandler()
        },
        showQuality () {
            if(this.enterpriseForm.certificate.includes('其他质量认证')) {
                this.OtherQualityCertifications = true
            }else {
                this.OtherQualityCertifications = false
                this.enterpriseForm.certificateOther = null
            }
        },
        showQuality_GTH () {
            if(this.businessForm.certificate.includes('其他质量认证')) {
                this.OtherQualityCertifications_GTH = true
            }else {
                this.OtherQualityCertifications_GTH = false
                this.businessForm.certificateOther = null
            }
        },
        selectIsPcwpUser (socialCreditCode, programaKey) {
            if (socialCreditCode.length === 18) {
                selectIsPcwpUserByCode({ socialCreditCode, programaKey }).then(res => {
                    console.log('API Response:', res)
                    this.registerPcwpFiles = res.files
                    if (res.isPcwoSupplier === 'unPcwpSupplier') {
                        this.$alert('该企业限制注册，如有疑问请联系我们', '提示', {
                            confirmButtonText: '确定',
                            callback: () => {
                                // 清空信用代码输入框
                                if (this.activeTab === 'enterprise') {
                                    this.enterpriseForm.socialCreditCode = ''
                                } else if (this.activeTab === 'business') {
                                    this.businessForm.socialCreditCode = ''
                                }
                            }
                        })
                    }else if (res.isPcwoSupplier === 'isPcwpSupplier') {
                        const hasRestrictedCredit = res.files.some(file =>
                            file.creditLevel === 3 || file.creditLevel === 4 // C级=3, D级=4
                        )
                        if (hasRestrictedCredit) {
                            this.$alert('该企业限制注册，如有疑问请联系我们', '提示', {
                                confirmButtonText: '确定',
                                callback: () => {
                                    if (this.activeTab === 'enterprise') {
                                        this.enterpriseForm.socialCreditCode = ''
                                    } else if (this.activeTab === 'business') {
                                        this.businessForm.socialCreditCode = ''
                                    }
                                }
                            })
                        }
                    }
                })
            }

        },
        openAgreement () {
            this.showTerm = true
        },
        openPhoneCodeDialog () {
            if (this.activeTab === 'individual') { // 个人用户获取手机验证码前的验证码

                if (this.verifyText1 !== '获取短信验证码') return
                this.$refs['individualForm'].validateField('userMobile', errMsg => {
                    if (!errMsg) this.phoneCodeDialogVisible = true
                })

            } else if (this.activeTab === 'enterprise') { // 企业用户获取手机验证码前的验证码

                if (this.verifyText2 !== '获取短信验证码') return
                // 取出表单中除verificationCode和adminPassword以外要验证的字段
                let propsToValidate = Object.keys(this.enterpriseForm).filter(key => !['verificationCode', 'adminPassword'].includes(key))
                let validateState = true
                // 判断是否有未填写的字段
                propsToValidate.forEach(prop => {
                    this.$refs['enterpriseForm'].validateField(prop, errMsg => errMsg ? validateState = false : null)
                })
                if (!validateState) return this.$message.error('请检查是否有未填的信息')
                this.phoneCodeDialogVisible = true

            } else if (this.activeTab === 'business') { // 个体户获取手机验证码前的验证码

                if (this.verifyText3 !== '获取短信验证码') return
                // 取出表单中除verificationCode和adminPassword以外要验证的字段
                let propsToValidate = Object.keys(this.businessForm).filter(key => !['verificationCode', 'adminPassword'].includes(key))
                let validateState = true
                // 判断是否有未填写的字段
                propsToValidate.forEach(prop => {
                    this.$refs['businessForm'].validateField(prop, errMsg => errMsg ? validateState = false : null)
                })
                if (!validateState) return this.$message.error('请检查是否有未填的信息')
                this.phoneCodeDialogVisible = true

            }
        },
        // 获取短信验证码前置图形验证码
        getPhoneVerificationImg () {
            getSendCodeImg().then(res => {
                this.verification[this.tabNum].verifyId = res.headers.verifyid
                this.verification[this.tabNum].verifyImg = this.blobToUrl(res.data)
                this.verification[this.tabNum].verifyInput = ''
            })
        },
        // 获取表单图形验证码图片
        getFormVerificationPic () {
            getVerificationImg().then(res => {
                switch (this.activeTab) {
                case 'individual': // 个人注册图形验证码
                    this.individualForm.verifyId = res.headers.verifyid
                    this.individualForm.verifyImg = this.blobToUrl(res.data)
                    break
                case 'enterprise': // 企业注册图形验证码
                    this.enterpriseForm.verifyId = res.headers.verifyid
                    this.enterpriseForm.verifyImg = this.blobToUrl(res.data)
                    break
                case 'business': // 个体户注册图形验证码
                    this.businessForm.verifyId = res.headers.verifyid
                    this.businessForm.verifyImg = this.blobToUrl(res.data)
                    break
                }
            })
        },
        // 校验短信验证码前置图形验证码
        checkPhoneVerificationCode () {
            let { verifyId, verifyInput } = this.verification[this.tabNum]
            checkSendCodeVerify({ id: verifyId, verifyInput }).then(res => {
                if (res.code !== 200) return
                this.phoneCodeDialogVisible = false
                this.getPhoneCode()
            })
        },
        // 发送获取短信验证码请求
        async getPhoneCode () {
            this.viewLoading = true
            let phone = {
                individual: this.individualForm.userMobile,
                enterprise: this.enterpriseForm.adminPhone,
                business: this.businessForm.adminPhone
            }[this.activeTab] // 获取当前注册用户类型的手机号码
            let privateKeyId = await this.getPrivateKey(phone)
            if (!privateKeyId) return
            registerSendCode({ phone, privateKeyId }).then(res => {
                if (res.code !== 200) return
                this.$message.success('发送成功')
                this.handleCountdown()
            }).finally(() => {
                this.viewLoading = false
            })
        },
        // 二进制数据转url
        blobToUrl (data) {
            let blob = new Blob([data], { type: 'image/jpeg' })
            return window.URL.createObjectURL(blob)
        },
        // 获取请求短信验证码需要的参数privateKeyId
        async getPrivateKey (phone) {
            let res = await getPrivateKeyId({ phone })
            if(typeof res !== 'string' || !res.length > 0) return ''
            return encrypt(res.verification)
        },
        // 注册
        confirmRegister () {
            if (this.tabNum === 0) {
                this.handleIndividualReg()
            } else if (this.tabNum === 1) {
                this.handleEnterpriseReg()
            } else if (this.tabNum === 2) {
                this.handleBusinessReg()
            }
        },
        // 下载文件
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        // 清空表单中的图形验证码
        hideFail () {
            this.enterpriseForm.verifyInput = ''
            this.businessForm.verifyInput = ''
            this.individualForm.verifyInput = ''
            this.registerFail = false
        },
        handleExceed () {
            this.$message.error('文件个数不能超出10个')
            return false
        },
        // 多文件上传
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.businessPcwpFormFileList.push(file)
                this.businessPcwpFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                this.businessPcwpFormFileList.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    programaKeyTwo: this.registerPcwpFiles.isPcwoSupplier,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,
                })
            }
        },
        async uploadOneOfFilesC (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.enterprisePcwpFormFileList.push(file)
                this.enterprisePcwpFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                this.enterprisePcwpFormFileList.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    programaKeyTwo: this.registerPcwpFiles.isPcwoSupplier,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,
                })
            }
        },
        async uploadOneOfFilesB (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading2 = true
            let uploadRes = await uploadFile(form)
            this.fileLoading2 = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.businessPcwpFormFileList.push(file)
                this.businessPcwpFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                this.businessPcwpFormFileList.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    programaKeyTwo: this.registerPcwpFiles.isPcwoSupplier,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,
                })
            }
        },
        async uploadOneOfFilesD (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading2 = true
            let uploadRes = await uploadFile(form)
            this.fileLoading2 = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.enterprisePcwpFormFileList.push(file)
                this.enterprisePcwpFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                this.enterprisePcwpFormFileList.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    programaKeyTwo: this.registerPcwpFiles.isPcwoSupplier,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,
                })
            }
        },
        // 校验文件大小
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if(size > 100) {
                this.$message.error('文件大小不能超过100M')
                return false
            }
            return true
        },
        filterFiles (files, name) {
            let recordId = null
            let filteredFiles = files.filter(t =>{
                if(t.name != name) return true
                recordId = t.fileFarId
                return false
            })
            return [filteredFiles, recordId]
        },
        handleRemove (file) {
            this.fileLoading = true
            let [files, recordId] = this.filterFiles(this.enterpriseForm.files, file.name)
            createWFileRecordDelete({ recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.enterpriseForm.files = files
                }
            }).finally(() => this.fileLoading = false)
        },
        handleRemoveIsPcwp (file) {
            this.fileLoading = true
            let [files, recordId] = this.filterFiles(this.businessPcwpFormFileList, file.name)
            createWFileRecordDelete({ recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.businessPcwpFormFileList = files
                }
            }).finally(() => this.fileLoading = false)
        },
        handleRemoveNoPcwp (file) {
            this.fileLoading = true
            let [files, recordId] = this.filterFiles(this.enterprisePcwpFormFileList, file.name)
            createWFileRecordDelete({ recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.enterprisePcwpFormFileList = files
                }
            }).finally(() => this.fileLoading = false)
        },
        handleRemove2 (file) {
            this.fileLoading2 = true
            let [files, recordId] = this.filterFiles(this.businessForm.files, file.name)
            createWFileRecordDelete({ recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.businessForm.files = files
                }
            }).finally(() => this.fileLoading2 = false)
        },
        getRegisterAgree () {
            findByProgramaKey({ programaKey: 'userRegistration' }).then(res => {
                this.content = res.content
                this.showTerm = true
            })
        },
        getRegisterAgreeUser (programaKey) {
            findByProgramaKey({ programaKey }).then(res => {
                // this.fileList = res.files.filter(item => (item.programaKeyTwo == null))
                //this.fileList = res.files
                this.content = res.content
                this.registerPcwpFiles.isPcwoSupplier = ''
                this.registerPcwpFiles.files = []
            })
        },
        print () {
            this.enterpriseForm.licenseTerm = '0000-00-00 00:00:00'
        },
        // 获取地址选择器数据
        async getAddressPickerOptions () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.addressOptions.province = res
            this.addressOptionsGd.province = res
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            if (layer === 1) {
                this.enterpriseForm.city = ''
                this.enterpriseForm.county = ''
                this.businessForm.city = ''
                this.businessForm.county = ''
                this.enterpriseForm.cityCode = ''
                this.enterpriseForm.countyCode = ''
                this.businessForm.cityCode = ''
                this.businessForm.countyCode = ''
                this.enterpriseForm.detailedAddress = ''
                this.businessForm.detailedAddress = ''
            }else if (layer === 2) {
                this.enterpriseForm.county = ''
                this.businessForm.county = ''
                this.enterpriseForm.countyCode = ''
                this.businessForm.countyCode = ''
                this.enterpriseForm.detailedAddress = ''
                this.businessForm.detailedAddress = ''
            }else if (layer === 3) {
                this.enterpriseForm.cityGd = ''
                this.enterpriseForm.countyGd = ''
                this.businessForm.cityGd = ''
                this.businessForm.countyGd = ''
                this.enterpriseForm.cityGdCode = ''
                this.enterpriseForm.countyGdCode = ''
                this.businessForm.cityGdCode = ''
                this.businessForm.countyGdCode = ''
                this.enterpriseForm.workXxdz = ''
                this.businessForm.workXxdz = ''
            }else if (layer === 4) {
                this.enterpriseForm.countyGd = ''
                this.businessForm.countyGd = ''
                this.enterpriseForm.countyGdCode = ''
                this.businessForm.countyGdCode = ''
                this.enterpriseForm.workXxdz = ''
                this.businessForm.workXxdz = ''
            }
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1 || layer === 2) {
                    layer === 1 ? this.addressOptions.city = res : this.addressOptions.district = res
                }else if (layer === 3 || layer === 4) {
                    layer === 3 ? this.addressOptionsGd.city = res : this.addressOptionsGd.district = res
                }
            })
        },
        emptyForm (tab) {
            this.activeTabLabelTilte = tab.label
            if (this.activeTabLabelTilte === '个体户') {
                this.deleteZzzs()
                this.deleteQtzs()
                this.getRegisterAgreeUser('individualRegistration')
            }else  if (this.activeTabLabelTilte === '个人用户') {
                this.getRegisterAgreeUser('userRegistration')
            }else {
                this.deleteZzzs()
                this.deleteQtzs()
                this.getRegisterAgreeUser('companyRegistration')
            }
        },
        // 正整数
        checkInt (rule, value, callback) {
            if (Number(value) && value % 1 === 0 && value >= 0) {
                callback()
            } else {
                return callback(new Error('请输入阿拉伯数字正整数！'))
            }
        },
        // 校验税率
        // eslint-disable-next-line
        validateTaxRate (rule, value, callback) {
            if(isNaN(Number(value))) {
                this.enterpriseForm.taxRate = ''
                return callback(new Error('请输入正确的税率'))
            }
            if(!value) return callback(new Error('请输入税率'))
            let lessThan = parseInt(value) < 0
            let biggerThan = parseInt(value) > 100
            lessThan ? this.enterpriseForm.taxRate = 0 : ''
            biggerThan ? this.enterpriseForm.taxRate = 100 : ''
            if(lessThan || biggerThan) return callback(new Error('超出限制'))
            let longFloatStr = typeof value === 'string' && value.includes('.') && value.split('.')[1].length > 2
            if(longFloatStr) this.enterpriseForm.taxRate = parseFloat(parseFloat(value).toFixed(2))
            callback()
        },
        // 校验个体户税率
        // eslint-disable-next-line
        validateBusinessTaxRate (rule, value, callback) {
            if(!value) return callback(new Error('请输入税率'))
            let lessThan = parseInt(value) < 0
            let biggerThan = parseInt(value) > 100
            lessThan ? this.businessForm.taxRate = 0 : ''
            biggerThan ? this.businessForm.taxRate = 100 : ''
            if(lessThan || biggerThan) return callback(new Error('超出限制'))
            if(value.includes('.') && value.split('.')[1].length > 2) this.businessForm.taxRate = parseFloat(parseFloat(value).toFixed(2))
            callback()
        },
        //密码验证
        validatePassword (rule, value, callback) {
            let len = value.trim().length
            if (len < 8 || len > 16) {
                return callback(new Error('请输入8到16位的密码！'))
            }
            if (!new RegExp(/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9])(.{8,20})$/).test(value)) {
                return callback(new Error('密码必须同时包含数字，字母和特殊字符！'))
            }
            callback()
        },
        //企业用户营业执照是否上传验证
        validatebusinessLicense (rule, value, callback) {
            if (!this.enterpriseForm.businessLicense || !this.enterpriseForm.businessLicenseId) {
                callback(new Error('请上传营业执照图片!'))
            } else {
                callback()
            }
        },
        //个体户营业执照是否上传验证
        validatebusinessLicenseGth (rule, value, callback) {
            if (!this.businessForm.businessLicense || !this.businessForm.businessLicenseId) {
                callback(new Error('请上传营业执照图片!'))
            } else {
                callback()
            }
        },
        validatePropfaren (rule, value, callback) {
            if (this.fddbrsfzmFileList.length === 0) {
                callback(new Error('请上传法定代表人身份证明!'))
            } else {
                callback()
            }
        },
        validatePropfarenBusiness (rule, value, callback) {
            if (this.fddbrsfzmFileListBus.length === 0) {
                callback(new Error('请上传法定代表人身份证明!'))
            } else {
                callback()
            }
        },
        validatePowerOfAttorney (rule, value, callback) {
            if (this.cnsFileList.length === 0) {
                callback(new Error('请上传承诺书!'))
            } else {
                callback()
            }
        },
        validatePropsqwts (rule, value, callback) {
            if (this.sqwtsFileList.length === 0) {
                callback(new Error('请上传授权委托书!'))
            } else {
                callback()
            }
        },
        validateZjyqwszm (rule, value, callback) {
            if (this.zjyqwszmFileList.length === 0) {
                callback(new Error('请上传最近一期完税证明!'))
            } else {
                callback()
            }
        },
        validateZgzxxxgk (rule, value, callback) {
            if (this.zgzxxxgkwFileList.length === 0) {
                callback(new Error('请上传中国执行信息公开网查询情况!'))
            } else {
                callback()
            }
        },
        validateSwpjzm (rule, value, callback) {
            if (this.swpjzmFileList.length === 0) {
                callback(new Error('请上传税务评级证明!'))
            } else {
                callback()
            }
        },
        validateXyzgbg (rule, value, callback) {
            if (this.xyzgbgFileList.length === 0) {
                callback(new Error('请上传信用中国报告!'))
            } else {
                callback()
            }
        },
        validatePowerOfAttorneyBusiness (rule, value, callback) {
            if (this.cnsFileListBus.length === 0) {
                callback(new Error('请上传承诺书!'))
            } else {
                callback()
            }
        },
        validatePropsqwtsBusiness (rule, value, callback) {
            if (this.sqwtsFileListBus.length === 0) {
                callback(new Error('请上传授权委托书!'))
            } else {
                callback()
            }
        },
        validateZjyqwszmBusiness (rule, value, callback) {
            if (this.zjyqwszmFileListBus.length === 0) {
                callback(new Error('请上传最近一期完税证明!'))
            } else {
                callback()
            }
        },
        validateZgzxxxgkBusiness (rule, value, callback) {
            if (this.zgzxxxgkwFileListBus.length === 0) {
                callback(new Error('请上传中国执行信息公开网查询情况!'))
            } else {
                callback()
            }
        },
        //统一信用代码验证
        validateSocialCreditCode (rule, value, callback) {
            let len = value.trim().length
            if (len != 18) {
                return callback(new Error('请输入18位统一信用代码！'))
            }
            callback()
        },
        //时间验证
        validateDate (rule, value, callback) {
            if ( value == null || value == '' ) {
                return callback(new Error('请选择时间！'))
            }
            callback()
        },
        // 校验地址信息
        validateAddress (rule, value, callback) {
            const specialRegions = [810000, 820000, 710000]
            // 特殊行政区判断香港澳门台湾
            if (specialRegions.includes(Number(this.enterpriseForm.provincesCode))) {
                callback()
            } else {
                if (!this.enterpriseForm.provincesCode) {
                    callback(new Error('请选择省份！'))
                } else if (!this.enterpriseForm.cityCode) {
                    callback(new Error('请选择市级！'))
                } else if (!this.enterpriseForm.countyCode) {
                    callback(new Error('请选择县、区！'))
                } else {
                    callback()
                }
            }
        },
        // 校验地址信息
        validateAddress_gd (rule, value, callback) {
            const specialRegions = [810000, 820000, 710000]
            if (specialRegions.includes(Number(this.enterpriseForm.provincesGdCode))) {
                callback()
            } else {
                if (!this.enterpriseForm.provincesGdCode) {
                    callback(new Error('请选择省份！'))
                } else if (!this.enterpriseForm.cityGdCode) {
                    callback(new Error('请选择市级！'))
                } else if (!this.enterpriseForm.countyGdCode) {
                    callback(new Error('请选择县、区！'))
                } else {
                    callback()
                }
            }
        },
        // 校验地址信息
        validateBusinessAddress (rule, value, callback) {
            const specialRegions = [810000, 820000, 710000]
            if (specialRegions.includes(Number(this.businessForm.provincesCode))) {
                callback()
            } else {
                if (!this.businessForm.provincesCode) {
                    callback(new Error('请选择省份！'))
                } else if (!this.businessForm.cityCode) {
                    callback(new Error('请选择市级！'))
                } else if (!this.businessForm.countyCode) {
                    callback(new Error('请选择县、区！'))
                } else {
                    callback()
                }
            }
        },
        // 校验地址信息
        validateBusinessAddress_gd (rule, value, callback) {
            const specialRegions = [810000, 820000, 710000]
            if (specialRegions.includes(Number(this.businessForm.provincesGdCode))) {
                callback()
            } else {
                if (!this.businessForm.provincesGdCode) {
                    callback(new Error('请选择省份！'))
                } else if (!this.businessForm.cityGdCode) {
                    callback(new Error('请选择市级！'))
                } else if (!this.businessForm.countyGdCode) {
                    callback(new Error('请选择县、区！'))
                } else {
                    callback()
                }
            }
        },
        // 获取短信验证码
        getVerificationCode (type) {
            if(type === 'individualForm') { // 个人
                if(this.verifyText1 !== '获取短信验证码') return
                this.$refs['individualForm'].validateField('userMobile', phoneError => {
                    if(phoneError) return
                    this.getCode()
                })
            }
            if(type === 'enterpriseForm') { // 企业
                if(this.verifyText2 !== '获取短信验证码') return
                let propsToValidate = Object.keys(this.enterpriseForm).filter(key => !['verificationCode', 'adminPassword'].includes(key))
                let validateState = true
                propsToValidate.forEach(prop => {
                    this.$refs['enterpriseForm'].validateField(prop, errMsg => errMsg ? validateState = false : null)
                })
                if(!validateState) return

                this.getCode()
            }
            if(type === 'businessForm') { // 个体户
                if (this.verifyText3 !== '获取短信验证码') return
                let propsToValidate = Object.keys(this.enterpriseForm).filter(key => !['verificationCode', 'adminPassword'].includes(key))
                let validateState = true
                propsToValidate.forEach(prop => {
                    this.$refs['businessForm'].validateField(prop, errMsg => errMsg ? validateState = false : null)
                })
                if(!validateState) return

                this.getCode()
            }
        },
        // 获取短信验证码
        async getCode () {
            let tel = {
                'individual': this.individualForm.userMobile,
                'enterprise': this.enterpriseForm.adminPhone,
                'business': this.businessForm.adminPhone,
            }
            let privateKeyId = await this.getPrivateKey(tel[this.activeTab])
            if(!privateKeyId) return
            this.viewLoading = true
            registerSendCode({ phone: tel[this.activeTab], privateKeyId }).then(res => {
                if(res.code !== 200) return
                this.$message.success('发送成功')
                this.handleCountdown()
            }).finally(()=>{
                this.viewLoading = false
            })
        },
        // 获取验证码按钮倒计时
        handleCountdown () {
            if(this.activeTab === 'individual') {

                let countdown = 60
                let timer1 = setInterval(() => {
                    if(countdown === 0) {
                        this.verifyText1 = '获取短信验证码'
                        return clearInterval(timer1)
                    }
                    this.verifyText1 = `倒计时 ${countdown}`
                    countdown -= 1
                }, 1000)

            }else if(this.activeTab === 'enterprise') {

                let countdown = 60
                let timer2 = setInterval(() => {
                    if(countdown === 0) {
                        this.verifyText2 = '获取短信验证码'
                        return clearInterval(timer2)
                    }
                    this.verifyText2 = `倒计时 ${countdown}`
                    countdown -= 1
                }, 1000)

            }else if(this.activeTab === 'business') {

                let countdown = 60
                let timer3 = setInterval(() => {
                    if(countdown === 0) {
                        this.verifyText3 = '获取短信验证码'
                        return clearInterval(timer3)
                    }
                    this.verifyText3 = `倒计时 ${countdown}`
                    countdown -= 1
                }, 1000)

            }
        },
        // 消息提示
        handleMessage (res) {
            if(res.code === 200) {
                this.viewLoading = false
                if (this.activeTab === 'individual') {
                    this.$message.success('注册成功')
                    setTimeout(function () {
                        location.href = '/login'
                    }, 2000)
                }else{
                    this.registerComplete = true
                    this.zcstate = 1
                }
            }else {
                this.viewLoading = false
                this.regFailMsg = res.message
                this.registerFail = true
            }
        },
        // 个人注册
        handleIndividualReg () {
            console.log('个人点击注册')
            this.$refs['individualForm'].validate(valid => {
                if (!valid) return
                if (!this.individualForm.agreeTerm) return this.$message.error('请查看协议后勾选')
                if (!this.individualForm.verifyInput) return this.formCodeDialogVisible = true

                this.viewLoading = true
                let newPassword = encrypt(this.individualForm.password)
                registerPerson({ ...this.individualForm, password: newPassword }).then(res => {
                    this.handleMessage(res)
                    if(res.message.includes('验证码错误')) this.verification[0].verifyInput = ''
                }).finally(() => {
                    this.viewLoading = false
                })
            })
        },
        // 地址更改触发
        addressChange (val) {
            if(!val) {
                this.enterpriseForm.provinces = this.$refs.selectLabel1.selectedLabel
                this.enterpriseForm.city = this.$refs.selectLabel2.selectedLabel
                this.enterpriseForm.county = this.$refs.selectLabel3.selectedLabel
                this.enterpriseForm.detailedAddress = this.enterpriseForm.provinces + this.enterpriseForm.city + this.enterpriseForm.county
                //个体户
                this.businessForm.provinces = this.$refs.selectLabel4.selectedLabel
                this.businessForm.city = this.$refs.selectLabel5.selectedLabel
                this.businessForm.county = this.$refs.selectLabel6.selectedLabel
                this.businessForm.detailedAddress = this.businessForm.provinces + this.businessForm.city + this.businessForm.county
            }
        },
        // 地址更改触发
        addressChange_gd (val) {
            if(!val) {
                this.enterpriseForm.provincesGd = this.$refs.selectLabel1_gd.selectedLabel
                this.enterpriseForm.cityGd = this.$refs.selectLabel2_gd.selectedLabel
                this.enterpriseForm.countyGd = this.$refs.selectLabel3_gd.selectedLabel
                this.enterpriseForm.workXxdz = this.enterpriseForm.provincesGd + this.enterpriseForm.cityGd + this.enterpriseForm.countyGd
                //个体户
                this.businessForm.provincesGd = this.$refs.selectLabel4_gd.selectedLabel
                this.businessForm.cityGd = this.$refs.selectLabel5_gd.selectedLabel
                this.businessForm.countyGd = this.$refs.selectLabel6_gd.selectedLabel
                this.businessForm.workXxdz = this.businessForm.provincesGd + this.businessForm.cityGd + this.businessForm.countyGd
            }
        },
        // 企业用户注册
        handleEnterpriseReg () {
            if (!this.validatePerformanceTable(this.tableData, '企业')) {
                console.log('注册')
            }
            this.$refs['enterpriseForm'].validate(valid => {
                if(!valid) return
                if (!this.enterpriseForm.agreeTerm) return this.$message.error('请查看协议后勾选')
                if (!this.enterpriseForm.verifyInput) return this.formCodeDialogVisible = true
                if (this.entAdminLongTerm) {
                    this.enterpriseForm.adminPeriodEnd = null
                }
                this.viewLoading = true
                this.enterpriseForm.enterpriseType = 1
                this.enterpriseForm.epLists = this.tableData
                this.enterpriseForm.zcstate = 0
                this.enterpriseForm.isFileModify = 0
                //this.enterpriseForm.files = [...this.enterpriseForm.files, ...this.enterprisePcwpFormFileList]
                let newPassword = encrypt(this.enterpriseForm.adminPassword)
                this.clientPop('info', '提交后请加QQ：840469283，便于审查部分未上传的资料并邮寄原件。', async () => {
                    console.log(this.enterpriseForm, newPassword)
                    registerEnterprise( { ...this.enterpriseForm, adminPassword: newPassword }).then(res => {
                        this.handleMessage(res)
                        if(res.message.includes('验证码错误')) this.verification[1].verifyInput = ''
                    }).finally(() => {
                        this.viewLoading = false
                    })
                }).finally(() => {
                    this.viewLoading = false
                })
            })
        },
        //个体户表单验证
        handleBusinessReg () {
            if (!this.validatePerformanceTable(this.tableData_GTH, '个体户')) {
                console.log('个体户点击注册')
            }
            //if (this.businessPcwpFormFileList.length < this.registerPcwpFiles.files.length) return this.$message.error('附件信息缺少，请上传附件')
            this.$refs['businessForm'].validate(valid => {
                if (!valid) return
                if (!this.businessForm.agreeTerm) return this.$message.error('请查看协议后勾选')
                if (!this.businessForm.verifyInput) return this.formCodeDialogVisible = true
                if (this.indAdminLongTerm) {
                    this.businessForm.adminPeriodEnd = null
                }
                this.viewLoading = true
                this.businessForm.enterpriseType = 0
                this.businessForm.epLists = this.tableData_GTH
                this.businessForm.zcstate = 0
                this.businessForm.isFileModify = 0
                let newPassword = encrypt(this.businessForm.adminPassword)
                // this.enterpriseForm.files = [...this.businessForm.files, ...this.businessPcwpFormFileList]
                //this.businessForm.files = this.businessPcwpFormFileList
                this.clientPop('info', '提交后请加QQ：840469283，便于审查部分未上传的资料并邮寄原件。', async () => {
                    console.log(this.businessForm, newPassword)
                    registerEnterprise({ ...this.businessForm, adminPassword: newPassword }).then(res => {
                        this.handleMessage(res)
                        if(res.message.includes('验证码错误')) this.verification[2].verifyInput = ''
                    }).finally(() => {
                        this.viewLoading = false
                    })
                }).finally(() => {
                    this.viewLoading = false
                })
            })
        },
        // 判断上传的文件大小
        handleBeforeUpload (file) {
            const validTypes = ['image/jpeg', 'image/png']
            if (!validTypes.includes(file.type)) {
                this.$message.error('请上传10MB以内的PNG，JPG格式图片！')
                return false
            }
            const sizeOk = file.size / 1024 / 1024 < 10
            if(!sizeOk) {
                this.$message.error('上传的文件大小不能超过10MB!')
            }
            return sizeOk
        },
        //上传营业执照格式验证
        handleBeforeUploadBusinessLicense (file) {
            const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/x-windows-bmp']
            if (!validTypes.includes(file.type)) {
                this.$message.error('请上传10MB以内的PNG，JPG、GIF格式图片！')
                return false
            }
            const sizeOk = file.size / 1024 / 1024 < 10
            if(!sizeOk) {
                this.$message.error('上传的文件大小不能超过10MB!')
            }
            return sizeOk
        },
        //判断是否为docx文件
        uploadBeforeDocx (file) {
            const extension = file.name.split('.').pop().toLowerCase()
            const validMimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            if (extension !== 'docx' || file.type !== validMimeType) {
                this.$message.error('仅支持.docx格式的Word文档')
                return false
            }
            const maxSize = 10 * 1024 * 1024
            if (file.size > maxSize) {
                this.$message.error('文件大小不能超过10MB！')
                return false
            }
            return true
        },
        //资质证书和其他证书判断是否为jpg、png格式的图片
        uploadBeforePicture (file) {
            const validTypes = ['image/jpeg', 'image/png']
            if (!validTypes.includes(file.type)) {
                this.$message.error('请上传10MB以内的PNG，JPG格式图片！')
                return false
            }
            const isLt = file.size / 1024 / 1024 < 10
            if (!isLt) {
                this.$message.error('文件大小不能超过10MB！')
                return false
            }
            return true
        },
        handleError (err) {
            console.error('上传失败', err)
        },
        handleRemoveAttorney (file, fileList) {
            this.cnsFileList = fileList
            this.$nextTick(() => {
                this.$refs.enterpriseForm.validateField('powerOfAttorney')
            })
        },
        handleRemoveBusinessLicense (file, fileList) {
            this.businessLicenselist = fileList
            this.$nextTick(() => {
                this.$refs.enterpriseForm.validateField('businessLicense')
            })
        },
        handleRemoveBusinessLicenseGth (file, fileList) {
            this.businessLicenselistGth = fileList
            this.$nextTick(() => {
                this.$refs.businessForm.validateField('businessLicense')
            })
        },
        handleRemovePropsqwts (file, fileList) {
            this.sqwtsFileList = fileList
            this.$nextTick(() => {
                this.$refs.enterpriseForm.validateField('propsqwts')
            })
        },
        handleRemovePropfaren (file, fileList) {
            this.fddbrsfzmFileList = fileList
            this.$nextTick(() => {
                this.$refs.enterpriseForm.validateField('propfaren')
            })
        },
        handleRemoveZjyqwszm (file, fileList) {
            this.zjyqwszmFileList = fileList
            this.$nextTick(() => {
                this.$refs.enterpriseForm.validateField('zjyqwszm')
            })
        },
        handleRemoveSwpjzm (file, fileList) {
            this.swpjzmFileList = fileList
            this.$nextTick(() => {
                this.$refs.enterpriseForm.validateField('swpjzm')
            })
        },
        handleRemoveZgzxxxgk (file, fileList) {
            this.zgzxxxgkwFileList = fileList
            this.$nextTick(() => {
                this.$refs.enterpriseForm.validateField('zgzxxxgk')
            })
        },
        handleRemoveXyzgbg (file, fileList) {
            this.xyzgbgFileList = fileList
            this.$nextTick(() => {
                this.$refs.enterpriseForm.validateField('xyzgbg')
            })
        },
        handleRemoveAttorneyBusiness (file, fileList) {
            this.cnsFileListBus = fileList
            this.$nextTick(() => {
                this.$refs.businessForm.validateField('powerOfAttorney')
            })
        },
        handleRemovePropsqwtsBusiness (file, fileList) {
            this.sqwtsFileListBus = fileList
            this.$nextTick(() => {
                this.$refs.businessForm.validateField('propsqwts')
            })
        },
        handleRemoveZjyqwszmBusiness (file, fileList) {
            this.zjyqwszmFileListBus = fileList
            this.$nextTick(() => {
                this.$refs.businessForm.validateField('zjyqwszm')
            })
        },
        handleRemoveZgzxxxgkBusiness (file, fileList) {
            this.zgzxxxgkwFileListBus = fileList
            this.$nextTick(() => {
                this.$refs.businessForm.validateField('zgzxxxgk')
            })
        },
        handleRemovePropfarenBusiness (file, fileList) {
            this.fddbrsfzmFileListBus = fileList
            this.$nextTick(() => {
                this.$refs.businessForm.validateField('propfaren')
            })
        },
        fileRemove (recordId) {
            createWFileRecordDelete({ recordId: recordId })
        },
        handleUploadChange () {
        },
        // 企业上传营业执照
        async uploadLicenseEnterprise (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.businessLicense = url
                    this.$nextTick(() => {
                        this.$refs.enterpriseForm.validateField('businessLicense')
                    })
                })
                this.enterpriseForm.businessLicenseId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.addEnterpriseFiles(res[0].objectName, '营业执照', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        // 个体户上传营业执照
        async uploadLicenseBusiness (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.businessLicense = url
                    this.$nextTick(() => {
                        this.$refs.businessForm.validateField('businessLicense')
                    })
                })
                this.businessForm.businessLicenseId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.addBusinessFiles(res[0].objectName, '营业执照', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        // 上传身份证
        async uploadIdentity (params, num, type) {
            //上传管理员身份证人像面
            if(num === 1 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    if(type == 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.enterpriseForm.cardPortraitFace = window.URL.createObjectURL(res)
                            this.$nextTick(()=>{
                                this.$refs.enterpriseForm.validateField('cardPortraitFace')
                            })
                        })
                        this.enterpriseForm.cardPortraitFaceId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '管理员身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                    if(type == 2) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.businessForm.cardPortraitFace = window.URL.createObjectURL(res)
                            this.$nextTick(()=>{
                                this.$refs.businessForm.validateField('cardPortraitFace')
                            })
                        })
                        this.businessForm.cardPortraitFaceId = res[0].recordId
                        this.addBusinessFiles(res[0].objectName, '管理员身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传管理员身份证国徽面
            if(num === 2 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    if(type === 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.enterpriseForm.cardPortraitNationalEmblem = url
                            this.$nextTick(()=>{
                                this.$refs.enterpriseForm.validateField('cardPortraitNationalEmblem')
                            })
                        })
                        this.enterpriseForm.cardPortraitNationalEmblemId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '管理员身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                    if(type === 2 ) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.businessForm.cardPortraitNationalEmblem = url
                            this.$nextTick(()=>{
                                this.$refs.businessForm.validateField('cardPortraitNationalEmblem')
                            })
                        })
                        this.businessForm.cardPortraitNationalEmblemId = res[0].recordId
                        this.addBusinessFiles(res[0].objectName, '管理员身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传法人身份证人像面
            if(num === 3 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    //企业注册
                    if(type == 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.enterpriseForm.legalPersonFace = window.URL.createObjectURL(res)
                            this.$nextTick(()=>{
                                this.$refs.enterpriseForm.validateField('legalPersonFace')
                            })
                        })
                        this.enterpriseForm.legalPersonFaceId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '法定代表人身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                    //个体户注册
                    if(type == 2) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.businessForm.legalPersonFace = window.URL.createObjectURL(res)
                            this.$nextTick(()=>{
                                this.$refs.businessForm.validateField('legalPersonFace')
                            })
                        })
                        this.businessForm.legalPersonFaceId = res[0].recordId
                        this.addBusinessFiles(res[0].objectName, '法定代表人身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传法人身份证国徽面
            if(num === 4 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    //企业注册
                    if(type === 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.enterpriseForm.legalPersonNational = url
                            this.$nextTick(()=>{
                                this.$refs.enterpriseForm.validateField('legalPersonNational')
                            })
                        })
                        this.enterpriseForm.legalPersonNationalId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '法定代表人身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                    //个体户注册
                    if(type === 2 ) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.businessForm.legalPersonNational = url
                            this.$nextTick(()=>{
                                this.$refs.businessForm.validateField('legalPersonNational')
                            })
                        })
                        this.businessForm.legalPersonNationalId = res[0].recordId
                        this.addBusinessFiles(res[0].objectName, '法定代表人身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
        },
        createUploadFile (file) {
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            return form
        },
        //表格相关
        // 格式化日期
        formatDate (date) {
            return new Date(date).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            })
        },
        // 删除记录
        handleDelete (index) {
            this.$confirm('确定要删除这条记录吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData.splice(index, 1)
                this.$message.success('删除成功!')
            })
        },
        // 删除记录
        handleDeleteGth (index) {
            this.$confirm('确定要删除这条记录吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData_GTH.splice(index, 1)
                this.$message.success('删除成功!')
            })
        },
        // 新增记录
        handleAdd () {
            this.tableData.push({})
        },
        handleAddGth () {
            this.tableData_GTH.push({})
        },
        // 备注修改
        handleRemarkChange (row) {
            console.log('备注已更新:', row)
        },
        async uploadSqwts (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                if(this.activeTab === 'enterprise') {
                    //this.enterpriseFormRules.propsqwts.required = false
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.adminAuthorize = url
                    })
                    //this.enterpriseForm.adminAuthorize = res[0].nonIpObjectPath
                    this.enterpriseForm.adminAuthorizeId = res[0].recordId
                    this.addEnterpriseFiles(res[0].objectName, '授权委托书', res[0].nonIpObjectPath, res[0].recordId)
                }
                else if(this.activeTab === 'business') {
                    //this.businessFormRules.propsqwts.required = false
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.adminAuthorize = url
                    })
                    //this.businessForm.adminAuthorize = res[0].nonIpObjectPath
                    this.businessForm.adminAuthorizeId = res[0].recordId
                    this.addBusinessFiles(res[0].objectName, '授权委托书', res[0].nonIpObjectPath, res[0].recordId)
                }
            })
            this.$nextTick(() => {
                if (this.activeTab === 'enterprise') {
                    this.$refs.enterpriseForm.validateField('propsqwts')
                } else if (this.activeTab === 'business') {
                    this.$refs.businessForm.validateField('propsqwts')
                }
            })
        },
        async uploadFrSfzm (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                if(this.activeTab === 'enterprise') {
                    //this.enterpriseFormRules.propfaren.required = false
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.lpIdentification = url
                    })
                    //this.enterpriseForm.lpIdentification = res[0].nonIpObjectPath
                    this.enterpriseForm.lpIdentificationId = res[0].recordId
                    this.addEnterpriseFiles(res[0].objectName, '法定代表人身份证明', res[0].nonIpObjectPath, res[0].recordId)
                }
                else if(this.activeTab === 'business') {
                    //this.businessFormRules.propfaren.required = false
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.lpIdentification = url
                    })
                    //this.businessForm.lpIdentification = res[0].nonIpObjectPath
                    this.businessForm.lpIdentificationId = res[0].recordId
                    this.addBusinessFiles(res[0].objectName, '法定代表人身份证明', res[0].nonIpObjectPath, res[0].recordId)
                }
            })
            this.$nextTick(() => {
                if (this.activeTab === 'enterprise') {
                    this.$refs.enterpriseForm.validateField('propfaren')
                } else if (this.activeTab === 'business') {
                    this.$refs.businessForm.validateField('propfaren')
                }
            })
        },
        async uploadQtzs (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                if(this.activeTab === 'enterprise') {
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.other = url
                    })
                    //this.enterpriseForm.other = res[0].nonIpObjectPath
                    this.enterpriseForm.otherId = res[0].recordId
                    this.addEnterpriseFiles(res[0].objectName, '其他证书', res[0].nonIpObjectPath, res[0].recordId)
                }
                else if(this.activeTab === 'business') {
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.other = url
                    })
                    //this.businessForm.other = res[0].nonIpObjectPath
                    this.businessForm.otherId = res[0].recordId
                    this.addBusinessFiles(res[0].objectName, '其他证书', res[0].nonIpObjectPath, res[0].recordId)
                }
            })
        },
        async uploadZzzs (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                if(this.activeTab === 'enterprise') {
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.qualificationCertificate = url
                    })
                    //this.enterpriseForm.qualificationCertificate = res[0].nonIpObjectPath
                    this.enterpriseForm.qualificationCertificateId = res[0].recordId
                    this.addEnterpriseFiles(res[0].objectName, '资质证书', res[0].nonIpObjectPath, res[0].recordId)
                }
                else if(this.activeTab === 'business') {
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.qualificationCertificate = url
                    })
                    //this.businessForm.qualificationCertificate = res[0].nonIpObjectPath
                    this.businessForm.qualificationCertificateId = res[0].recordId
                    this.addBusinessFiles(res[0].objectName, '资质证书', res[0].nonIpObjectPath, res[0].recordId)
                }
            })
        },
        async uploadZxxx (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                if(this.activeTab === 'enterprise') {
                    //this.enterpriseFormRules.zgzxxxgk.required = false
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.zxgk = url
                    })
                    //this.enterpriseForm.zxgk = res[0].nonIpObjectPath
                    this.enterpriseForm.zxgkId = res[0].recordId
                    this.addEnterpriseFiles(res[0].objectName, '中国执行信息公开网查询情况', res[0].nonIpObjectPath, res[0].recordId)
                }
                else if(this.activeTab === 'business') {
                    //this.businessFormRules.zgzxxxgk.required = false
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.zxgk = url
                    })
                    //this.businessForm.zxgk = res[0].nonIpObjectPath
                    this.businessForm.zxgkId = res[0].recordId
                    this.addBusinessFiles(res[0].objectName, '中国执行信息公开网查询情况', res[0].nonIpObjectPath, res[0].recordId)
                }
            })
            this.$nextTick(() => {
                if (this.activeTab === 'enterprise') {
                    this.$refs.enterpriseForm.validateField('zgzxxxgk')
                } else if (this.activeTab === 'business') {
                    this.$refs.businessForm.validateField('zgzxxxgk')
                }
            })
        },
        async uploadXyzgbg (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                //this.enterpriseFormRules.xyzgbg.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.creditChinaReport = url
                })
                //this.enterpriseForm.creditChinaReport = res[0].nonIpObjectPath
                this.enterpriseForm.creditChinaReportId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.addEnterpriseFiles(res[0].objectName, '信用中国报告', res[0].nonIpObjectPath, res[0].recordId)
            })
            this.$nextTick(() => {
                if (this.activeTab === 'enterprise') {
                    this.$refs.enterpriseForm.validateField('xyzgbg')
                } else if (this.activeTab === 'business') {
                    this.$refs.businessForm.validateField('xyzgbg')
                }
            })
        },
        async uploadSwpjzm (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                //this.enterpriseFormRules.swpjzm.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.taxRatingCertificate = url
                })
                //this.enterpriseForm.taxRatingCertificate = res[0].nonIpObjectPath
                this.enterpriseForm.taxRatingCertificateId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.addEnterpriseFiles(res[0].objectName, '税务评级证明', res[0].nonIpObjectPath, res[0].recordId)
            })
            this.$nextTick(() => {
                if (this.activeTab === 'enterprise') {
                    this.$refs.enterpriseForm.validateField('swpjzm')
                } else if (this.activeTab === 'business') {
                    this.$refs.businessForm.validateField('swpjzm')
                }
            })
        },
        async uploadWszm (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                if(this.activeTab === 'enterprise') {
                    //this.enterpriseFormRules.zjyqwszm.required = false
                    this.enterpriseForm.taxPaymentCertificateId = res[0].recordId
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.taxPaymentCertificate = url
                    })
                    //this.enterpriseForm.taxPaymentCertificate = res[0].nonIpObjectPath
                    this.addEnterpriseFiles(res[0].objectName, '最近一期完税证明', res[0].nonIpObjectPath, res[0].recordId)
                }
                else if(this.activeTab === 'business') {
                    //this.businessFormRules.zjyqwszm.required = false
                    this.businessForm.taxPaymentCertificateId = res[0].recordId
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.taxPaymentCertificate = url
                    })
                    //this.businessForm.taxPaymentCertificate = res[0].nonIpObjectPath
                    this.addBusinessFiles(res[0].objectName, '最近一期完税证明', res[0].nonIpObjectPath, res[0].recordId)
                }
            })
            this.$nextTick(() => {
                if (this.activeTab === 'enterprise') {
                    this.$refs.enterpriseForm.validateField('zjyqwszm')
                } else if (this.activeTab === 'business') {
                    this.$refs.businessForm.validateField('zjyqwszm')
                }
            })
        },
        async uploadCns (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                if(this.activeTab === 'enterprise') {
                    this.enterpriseForm.letterCommitmentId = res[0].recordId
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.letterCommitment = url
                    })
                    this.addEnterpriseFiles(res[0].objectName, '承诺书', res[0].nonIpObjectPath, res[0].recordId)
                }else if(this.activeTab === 'business') {
                    this.businessForm.letterCommitmentId = res[0].recordId
                    previewFile({ recordId: res[0].recordId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.businessForm.letterCommitment = url
                    })
                    this.addBusinessFiles(res[0].objectName, '承诺书', res[0].nonIpObjectPath, res[0].recordId)
                }
            })
            this.$nextTick(() => {
                if (this.activeTab === 'enterprise') {
                    this.$refs.enterpriseForm.validateField('powerOfAttorney')
                } else if (this.activeTab === 'business') {
                    this.$refs.businessForm.validateField('powerOfAttorney')
                }
            })
        },
        addZzzs () {
            this.isZzzs = true
        },
        addQtzl () {
            this.isQtzs = true
        },
        //资质证书删除按钮
        deleteZzzs () {
            // console.log('szQcEndTime', this.szQcEndTime)
            this.szQcEndTime = false
            this.isZzzs = false
            this.enterpriseForm.qualificationCertificate = null
            this.enterpriseForm.qualificationCertificateId = null
            this.enterpriseForm.qcStartTime = null
            this.enterpriseForm.qcEndTime = null
            this.businessForm.qualificationCertificate = null
            this.businessForm.qualificationCertificateId = null
            this.businessForm.qcStartTime = null
            this.businessForm.qcEndTime = null
        },
        //其他证书删除按钮
        deleteQtzs () {
            this.szOtherEndTime = false
            this.isQtzs = false
            this.enterpriseForm.other = null
            this.enterpriseForm.otherId = null
            this.enterpriseForm.otherStartTime = null
            this.enterpriseForm.otherEndTime = null
            this.businessForm.other = null
            this.businessForm.otherId = null
            this.businessForm.otherStartTime = null
            this.businessForm.otherEndTime = null
        },
        //企业注册添加附件
        addEnterpriseFiles (objectName, fileName, nonIpObjectPath, recordId) {
            let fileSuffix = objectName.substr(objectName.lastIndexOf('.') + 1)
            this.enterpriseForm.files.push({
                name: fileName + '.' + fileSuffix,
                relevanceType: 8,
                url: nonIpObjectPath,
                fileType: 3,
                fileFarId: recordId,
                category: fileName
            })
        },
        addBusinessFiles (objectName, fileName, nonIpObjectPath, recordId) {
            let fileSuffix = objectName.substr(objectName.lastIndexOf('.') + 1)
            this.businessForm.files.push({
                name: fileName + '.' + fileSuffix,
                relevanceType: 8,
                url: nonIpObjectPath,
                fileType: 3,
                fileFarId: recordId,
                category: fileName
            })
        },
        //下载承诺书模板
        downloadCns () {
            this.handleDownload( { fileFarId: '1924375289770504193', name: '承诺书模板.docx' } )
        },
        //下载法定代表人身份证明模板
        downloadFrSfzm () {
            this.handleDownload( { fileFarId: '1924378621633327106', name: '法定代表人身份证明模板.docx' } )
        },
        //下载授权委托书模板
        downloadsqwts () {
            this.handleDownload( { fileFarId: '1924378853041467394', name: '授权委托书模板.docx' } )
        },
        handleChangeCns (file, fileList) {
            this.cnsFileList = fileList
        },
        //承诺书超出限制后判断文件类型后替换为最新的文件
        UploadLimitCns (file) {
            if (file[0].type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                this.cnsFileList = [file[0]]
            }else {
                this.$message.error('仅支持.docx格式的Word文档')
            }
        },
        handleChangeSqwts (file, fileList) {
            this.sqwtsFileList = fileList
        },
        //授权委托书超出限制后替换为最新的文件
        UploadLimitSqwts (file) {
            if (file[0].type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                this.sqwtsFileList = [file[0]]
            }else {
                this.$message.error('仅支持.docx格式的Word文档')
            }
        },
        handleChangeFrSfzm  (file, fileList) {
            this.fddbrsfzmFileList = fileList
        },
        //身份证明超出限制后替换为最新的文件
        UploadLimitSfzm (file) {
            if (file[0].type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                this.fddbrsfzmFileList = [file[0]]
            }else {
                this.$message.error('仅支持.docx格式的Word文档')
            }
        },
        handleChangeWszm (file, fileList) {
            this.zjyqwszmFileList = fileList
        },
        handleChangeSwpjzm (file, fileList) {
            this.swpjzmFileList = fileList
        },
        handleChangeZxxx (file, fileList) {
            this.zgzxxxgkwFileList = fileList
        },
        handleChangeXyzgbg (file, fileList) {
            this.xyzgbgFileList = fileList
        },
        handleChangeCnsBusiness (file, fileList) {
            this.cnsFileListBus = fileList
        },
        //承诺书超出限制后替换为最新的文件
        UploadLimitCnsBusiness (file) {
            if (file[0].type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                this.cnsFileListBus = [file[0]]
            }else {
                this.$message.error('仅支持.docx格式的Word文档')
            }
        },
        handleChangeSqwtsBusiness (file, fileList) {
            this.sqwtsFileListBus = fileList
        },
        //授权委托书超出限制后替换为最新的文件
        UploadLimitSqwtsBusiness (file) {
            if (file[0].type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                this.sqwtsFileListBus = [file[0]]
            }else {
                this.$message.error('仅支持.docx格式的Word文档')
            }
        },
        handleChangeFrSfzmBusiness  (file, fileList) {
            this.fddbrsfzmFileListBus = fileList
        },
        //法定代表人身份证明超出限制后替换为最新的文件
        UploadLimitSfzmBusiness (file) {
            if (file[0].type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                this.fddbrsfzmFileListBus = [file[0]]
            }else {
                this.$message.error('仅支持.docx格式的Word文档')
            }
        },
        handleChangeWszmBusiness (file, fileList) {
            this.zjyqwszmFileListBus = fileList
        },
        handleChangeZxxxBusiness (file, fileList) {
            this.zgzxxxgkwFileListBus = fileList
        },
        handleChange () {
        },
        getQyInfo (enterpriseId) {
            if (enterpriseId == undefined) {
                return
            }
            getInfo( { enterpriseId: enterpriseId } ).then(res => {
                if (res.zcstate == 0 && res.auditFailReason == null) {
                    this.registerComplete = true
                    this.zcstate = 1
                }else if (res.zcstate == 0 && (res.auditFailReason != null || res.auditFailReason.length > 0 )) {
                    this.regFailMsg = res.auditFailReason
                    this.registerFail = true
                }
            })
        },
        //主要业绩表格验证
        validatePerformanceTable (tableData, tableName = '企业') {
            // 检查表格是否为空
            if (!tableData || tableData.length === 0) {
                this.$message.error(`${tableName}业绩信息不能为空，请至少添加一项业绩`)
                return false
            }
            let hasErrors = false
            tableData.forEach((row, index) => {
                const rowNumber = index + 1
                // 1. 项目名称验证
                if (!row.projectName || row.projectName.trim() === '') {
                    this.showError(`第${rowNumber}行项目名称不能为空`, index)
                    hasErrors = true
                } else if (row.projectName.length > 100) {
                    this.showError(`第${rowNumber}行项目名称不能超过100个字符`, index)
                    hasErrors = true
                }
                // 2. 供应物资品类验证
                if (!row.supplyCategory || row.supplyCategory.trim() === '') {
                    this.showError(`第${rowNumber}行供应物资品类不能为空`, index)
                    hasErrors = true
                } else if (row.supplyCategory.length > 100) {
                    this.showError(`第${rowNumber}行供应物资品类不能超过100个字符`, index)
                    hasErrors = true
                }
                // 3. 合同金额验证
                if (!row.contractAmount) {
                    this.showError(`第${rowNumber}行合同金额不能为空`, index)
                    hasErrors = true
                } else {
                    // 允许小数，最多2位小数
                    const amount = parseFloat(row.contractAmount)
                    if (isNaN(amount)) {
                        this.showError(`第${rowNumber}行合同金额必须为有效数字`, index)
                        hasErrors = true
                    } else if (amount <= 0) {
                        this.showError(`第${rowNumber}行合同金额必须大于0`, index)
                        hasErrors = true
                    } else if (row.contractAmount.length > 20) {
                        this.showError(`第${rowNumber}行合同金额不能超过20个字符`, index)
                        hasErrors = true
                    } else if (!/^\d+(\.\d{1,2})?$/.test(row.contractAmount)) {
                        this.showError(`第${rowNumber}行合同金额格式错误（最多2位小数）`, index)
                        hasErrors = true
                    }
                }
                // 4. 业绩证明人验证
                if (!row.proofPerson || row.proofPerson.trim() === '') {
                    this.showError(`第${rowNumber}行业绩证明人不能为空`, index)
                    hasErrors = true
                } else if (row.proofPerson.length > 20) {
                    this.showError(`第${rowNumber}行业绩证明人不能超过20个字符`, index)
                    hasErrors = true
                }
                // 5. 证明人联系电话验证
                if (!row.proofPhone || row.proofPhone.trim() === '') {
                    this.showError(`第${rowNumber}行证明人联系电话不能为空`, index)
                    hasErrors = true
                } else if (
                    // 手机号（1开头11位）
                    !/^1[3-9]\d{9}$/.test(row.proofPhone) &&
                    // 固定电话（区号+号码，支持短横线）
                    !/^0\d{2,3}-?\d{7,8}$/.test(row.proofPhone)
                ) {
                    this.showError(`第${rowNumber}行证明人联系电话格式不正确（手机号或固定电话）`, index)
                    hasErrors = true
                }
                // 6. 供货起止时间验证
                if (row.ghdate && row.ghdate.length === 2) {
                    const [start, end] = row.ghdate
                    if (!start || !end) {
                        this.showError(`第${rowNumber}行供货时间范围不完整`, index)
                        hasErrors = true
                    } else if (new Date(start) > new Date(end)) {
                        this.showError(`第${rowNumber}行供货结束时间不能早于开始时间`, index)
                        hasErrors = true
                    }
                }
            })
            return !hasErrors
        },
        showError (message, index) {
            setTimeout(() => {
                this.$message({
                    message,
                    type: 'error',
                    duration: 3000,
                    offset: 40 + (index * 60),
                    customClass: 'custom-error-message',
                    showClose: true
                })
            }, index * 300)
        }
    },
    created () {
        this.getAddressPickerOptions()
        this.getRegisterAgreeUser('userRegistration')
        this.getQyInfo(this.userInfo.enterpriseId)
    },
}
</script>

<style scoped lang="scss">
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
    -moz-appearance: textfield !important;
}
/deep/ input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
$font: 'Source Han Sans CN';

main {
    width: 100%;
    padding: 20px 0;
    border-top: 1px solid #e6e6e6;

    .contentBox {
        width: 1326px;
        // border: 1px solid #e5e5e5;
        border: 1px solid rgba(230, 230, 230, 1);
    }
}

.tabBox {
    height: 100%;
    min-height: 622px;
    font-family: $font;
    position: relative;
}

/deep/ .inProgress .el-tabs__nav-scroll {
    height: 60px;
    font-family: $font;
    background-color: #fafafa;
    display: flex;
    justify-content: center;

    .el-tabs__item.is-top {
        width: 160px;
        height: 60px;
        font-size: 18px;
        line-height: 60px;
        margin: 0;
        padding: 0;
        text-align: center;
        z-index: 1;

        &.is-active {
            color: #333;
            background-color: #fff;
        }
    }

    .el-tabs__active-bar {
        height: 3px;
        background-color: #226FC7;
        // top: 0;
        z-index: 2;
    }
}

/deep/ .el-tab-pane {
    min-height: 562px;
    padding-bottom: 50px;
}

.goLogin {
    font-size: 16px;
    color: #666;
    position: absolute;
    right: 20px;
    top: 19px;
    z-index: 1;
    user-select: none;

    span {
        color: #216EC6;
        cursor: pointer;
    }
}

h2 {
    margin-bottom: 40px;
    font-size: 26px;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
}

/deep/ .el-form {
    padding-top: 60px;

    .el-form-item {
        margin-bottom: 25px;

        .el-form-item__label {
            height: 100%;
            padding-right: 6px;
            line-height: 50px;
            font-size: 16px;
            color: #333;
        }

        .el-input__inner {
            height: 50px;
            font-size: 16px;
            border-radius: 0;
            border: 1px solid rgba(204, 204, 204, 1);
        }
    }
}

/deep/ .el-checkbox {
    display: flex;
    align-items: center;

    .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(33, 110, 198, 1);
    }

    .el-checkbox__label {
        font-size: 16px;
        color: #333;

        span {
            color: #216EC6;
        }
    }
}

/deep/ .loginBtn .el-button {
    width: 400px;
    height: 50px;
    margin: 0 auto !important;
    font-size: 22px;
    border-radius: 0;
    color: #fff;
    background-color: #216EC6;
}

.individualForm {
    width: 400px;

    /deep/ span.el-input__prefix {
        width: 86px;
        line-height: 50px;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
        font-weight: 400;
    }

    /deep/ span.el-input__suffix {
        width: 30px;
        height: 50px;
    }

    .telInput,
    .passInput {
        /deep/ .el-input__inner {
            padding-left: 89px;
        }
    }

    .passInput {
        /deep/ .el-input__inner {
            padding-right: 40px;
        }
    }

    .verifyBox {
        width: 100%;

        /deep/ .el-input {
            width: 250px;

            .el-input__inner {
                width: 250px;
            }
        }

        /deep/ .el-button {
            width: 140px !important;
            height: 50px;
            line-height: 50px;
            padding: 0 0;
            text-align: center;
            font-size: 16px;
            border: 1px solid rgba(33, 110, 198, 1);
            border-radius: 0;
            color: rgba(33, 110, 198, 1);
            background-color: #fff;
        }
    }
}

.enterpriseForm, .businessForm {
    width: 1160px;
  /deep/ .el-col-11 {
    min-width: 500px; /* 确保有足够宽度 */
  }
    /deep/ .el-col:not(.el-col-24) {
        width: 47%;

        &.el-col-offset-2 {width: 568px;margin-left: 40px;}
    }
    /deep/ .avatar-uploader .el-upload {
        width: 138px;
        height: 138px;
        border: 1px dashed rgba(217, 217, 217, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    /deep/ .el-form-item__error {
        width: 80%;
        margin-top: 0px;
    }
    .licenseUploader {
        font-size: 40px;
        color: #8c939d;
        width: 138px;
        height: 138px;
        //margin-right: 20px;
        line-height: 140px;
        display: inline;
        /deep/ .el-form-item__error {
            width: 500px;
        }
    }

    .avatar {
        width: 140px;
        height: 140px;
        display: block;
    }

    .uploadDemo {
        width: 138px;
        height: 138px;
        padding: 5px;
        margin-left: 40px;
        font-size: 16px;
        color: #999;
        border: 1px dashed rgba(217, 217, 217, 1);
        flex-direction: column;

        img {
            width: 130px;
            height: 95px;
        }

        span {
            margin-right: 5px;
        }
    }

    .uploadTip {
        font-size: 14px;
        margin-top: 165px;
        margin-bottom: 10px;
        margin-left: -108px;
        width: 380px;
        color: #808080;
    }

    /deep/ .el-select {
        width: 100%;
    }
    /deep/ .licenseValidTime {
        .el-form-item__content {display: flex;}
        .el-date-editor {width: 300px;margin-right: 20px;}
        .el-checkbox {height: 50px; margin-bottom: 0;}
        .el-checkbox__inner {border: 1px solid rgba(217,217,217,1);}
    }
    /deep/ .registerAddress {
        .el-select {
            width: 120px;
            margin-right: 8px;
            &:last-child{margin-right: 0;}
        }
        .el-form-item__error {
            margin-left: 0px;
            //margin-top: -50px;
        }
    }
    .separ {
        width: 1160px;
        height: 1px;
        margin-bottom: 40px;
        margin-top: 40px;
        border-top: 1px dashed rgba(204, 204, 204, 1);
    }

    .subtitle {
        margin-bottom: 30px;
        font-size: 20px;
        font-weight: 500;
        color: #216EC6;
    }

    .identityUpload {
        width: 160px;
        height: 100px;
    }
    /deep/ .verifyBox .el-button {
        width: 130px;
        height: 50px;
        padding: 0;
        font-size: 16px;
        line-height: 50px;
        color: #216EC6;
        background: #FFFFFF;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 5px;
    }

    .verifyBox {
        .el-input, .el-input__inner {width: 250px;}
    }

    .el-checkbox {
        margin-top: -3px;
        margin-bottom: 50px;
    }

    /deep/ .loginBtn.el-form-item .el-form-item__content {
        margin-left: 0 !important;
        text-align: center;
    }

    // /deep/ .el-col-12 .el-input__inner {width: 400px;}
    // /deep/ .el-col-24 .el-input__inner {width: 1027px;}
}

/deep/ .el-button.codeDialogBtn {
    width: 90px;
    line-height: 40px;
    font-size: 20px;
    height: 40px;
    border-radius: 0;
}

.verifyBox {
    width: 100%;

    /deep/ .el-input {
        width: 250px;
        height: 50px;
        border-radius: 0;

        .el-input__inner {
            width: 250px;
            height: 50px;
            border-radius: 0;
        }
    }

    img {
        width: 140px;
        height: 50px;
        border: 1px solid lightgray;
    }

    /deep/ .el-button {
        padding: 0 0;
        text-align: center;
        font-size: 16px;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 0;
        color: rgba(33, 110, 198, 1);
        background-color: #fff;
    }
}
//步骤条
.steps{
  //border-style: solid;
  margin: 0 auto;
  height: 10%;
  padding:50px 0;
  width: 800px;
  margin-bottom: -80px;
}
//未通过原因
.failReason{
  //background-color: rgb(255,235,229);
  //color: rgb(255,51,113);
  border: 2px dashed rgb(255,51,11);
  width: 300px;
  height: 50px;
  margin: 0 auto;
  line-height: 50px;
  text-align: center;
  margin-top: -10px;
  margin-bottom: 50px;
}
//企业账户信息，填写内容将写入合同并涉及发票，请谨慎填写。
.setAccount{
  height: 30px;
  display: flex;
  line-height: 30px;
  margin-bottom: 30px;
}
.subtitletip{
  //border-style: dashed;
  color: #e53e30;
  font-size: 14px;
  margin-left: 50px;
}
//上传文件的按钮样式
.uploadbtn{
  display: inline-flex;
  align-items: center;
  background-color: rgb(255,118,0);
  border: 1px solid rgb(255,118,0);
  text-align: center;
  width: 120px;
  height: 35px;
  line-height: 35px;
}
//上传证书和资料
.addcardtop{
  width: 150px;
  height: 50px;
  background-color: rgb(0,155,237);
  font-size: 16px;
  margin-bottom: 30px;
}
.addcard{
  overflow-y: scroll;
  padding: 10px;
  margin-top: 20px;
  margin-right: 30px;
  border: 1px solid #cccccc;
  width: 500px;
  height: 250px;
}
.cardtop{
  display: flex;
  justify-content: space-between;
}
.addcarddel{
  color: rgb(255,118,0);
  border-color: rgb(255,118,0);
  background-color: white;
}
//表格
.custom-table {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.add-btn-container {
  text-align: center;
  margin-top: 20px;
}

.el-table {
  margin-top: 20px;
}

.el-tag {
  cursor: pointer;
}

.el-icon-time {
  color: #409EFF;
}

.el-button--danger {
  padding: 8px;
}
.bigpicture{
  cursor: pointer;
}
.single-line-label {
  /deep/ .el-form-item__label {
    white-space: nowrap; /* 防止文字换行 */
    padding-right: 10px; /* 增加右侧间距 */
  }

  /deep/ .el-form-item__content {
    display: flex;
    flex-wrap: nowrap; /* 防止内容换行 */
  }
}
.single-line-label {
  /deep/ .el-form-item__label {
    font-size: 14px; /* 稍微减小字体 */
  }
}
.upload-demo2{
  margin-left: 70px;
}
.upload-item-wrapper {
  /deep/ .el-form-item__content {
    display: block !important;
  }
}

.upload-container {
  display: flex;
  flex-direction: column;
}

.upload-demo {
  margin-bottom: 10px;
}

.download-link {
  cursor: pointer;
  color: #216EC6;
  font-size: 14px;
  padding: 5px 0;
  display: block;
  width: 100%;
}
.custom-error-message {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(145deg, #fff6f6, #ffecec);
  border-left: 4px solid #f56c6c;
  padding: 12px 16px;
  font-size: 14px;
  color: #f56c6c;
}
/* 确保选择器输入框显示省略号 */
.province .el-input__inner {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Tooltip样式 */
.el-tooltip__popper {
  max-width: 300px;
  word-break: break-all;
  white-space: normal;
}
/deep/ .el-table__header th .cell {
  font-size: 18px !important;
  font-weight: 200 !important;
  color: black;
}
.BasicInformation{
  margin-bottom: 20px;
  width: 1140px;
  height: 70px;
  display: flex;
  div{
    width: 170px;
    height: 70px;
    line-height: 70px;
    text-align: center;
  }
  textarea{
    width: 970px;
    height: 70px;
    resize:none;
    font-size: 18px;
    padding: 5px
  }
}
</style>
