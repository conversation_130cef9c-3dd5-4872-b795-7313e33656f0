import service from '@/utils/request'

const { httpPost } = service

const getAttachments = params => {
    return httpPost({
        url: '/materialMall/w/file/listByEntity',
        params
    })
}

const selectFileList = params => {
    return httpPost({
        url: '/materialMall/file/listByEntity',
        params
    })
}

const getEnterPriseFileList = params => {
    return httpPost({
        url: '/materialMall/file/listByEntity',
        params
    })
}

const getPcwpFileList = params => {
    return httpPost({
        url: '/materialMall/file/getPcwpFileList',
        params
    })
}

export {
    getAttachments,
    selectFileList,
    getEnterPriseFileList,
    getPcwpFileList
}