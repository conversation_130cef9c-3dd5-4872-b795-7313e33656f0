import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service
//=============================================装备交接审核===============================================================
//获取审核历史
const history = params =>{
    return httpPost({
        url: '/facilityconnect/flowEngin/get/audit/history',
        params
    })
}
//获取流程按钮显示状态
const getCurrentUnitInfo = params =>{
    return httpPost({
        url: '/facilityconnect/flowEngin/getCurrentUnitInfo',
        params
    })
}
const request  = {
    //审核
    audit (params) {
        return httpPost({
            url: '/facilityconnect/flowEngin/audit',
            params
        })
    },
    //提交
    commit (params) {
        return httpPost({
            url: '/facilityconnect/flowEngin/commit',
            params
        })
    },
    //获取审核历史
    history (params) {
        return httpPost({
            url: '/facilityconnect/flowEngin/get/audit/history',
            params
        })
    },
    //获取流程按钮显示状态
    getCurrentUnitInfo (params) {
        return httpPost({
            url: '/facilityconnect/flowEngin/getCurrentUnitInfo',
            params
        })
    },
    //撤回
    undoworkt (params) {
        return httpPost({
            url: '/facilityconnect/flowEngin/undowork',
            params
        })
    },
    //作废
    nullify (params) {
        return httpPost({
            url: '/facilityconnect/common/nullify',
            params
        })
    }
}
export {
    history,
    getCurrentUnitInfo,
}
export default request
