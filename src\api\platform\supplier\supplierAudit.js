import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/findByConditionByPage',
        params
    })
}
const getSupplierShopsList = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/findBySupplierShopsList',
        params
    })
}

const getListAll = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/findByConditionByPageAll',
        params
    })
}

const getListLedger = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/ledger',
        params
    })
}
const pcwpOrg = params => {
    return httpGet({
        url: '/materialMall/pcwpOrg/getTree',
        params
    })
}
const getInUserLedger = params => {
    return httpPost({
        url: '/materialMall/pcwpOrg/getInUserLedger',
        params
    })
}
const getOutUserLedger = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/outPurchaserLedger',
        params
    })
}
const changeIsSupplier = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/updateIsSupplier',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/delete',
        params
    })
}

const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/deleteBatch',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/updateByPublish',
        params
    })
}

const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/updateNotPublish',
        params
    })
}

const getEnterFileList = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/getEnterFileList',
        params
    })
}

const updateIsSupplierById = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/updateIsSupplierById',
        params
    })
}
const getTwoSupplierInfo = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/getTwoSupplierInfo',
        params
    })
}

const updateArrearageBatch = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/updateArrearageBatch',
        params
    })
}
const  getSdStatus = params => {
    return httpGet({
        url: '/materialMall/platform/enterpriseInfo/sd/sdStatus',
        params,
    })
}

const  updateBatchArrearage = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/sd/updateBatchArrearage',
        params,
    })
}

const  resetPwd = id => {
    return httpPost({
        url: `/materialMall/platform/enterpriseInfo/sd/resetPwd/${id}`
    })
}
export {
    getList,
    updateBatchArrearage,
    getSdStatus,
    updateArrearageBatch,
    getListAll,
    getListLedger,
    getOutUserLedger,
    changeIsSupplier,
    create,
    edit,
    del,
    batchPublish,
    batchNotPublish,
    batchDelete,
    getEnterFileList,
    updateIsSupplierById,
    getTwoSupplierInfo,
    getSupplierShopsList,
    resetPwd,
    pcwpOrg,
    getInUserLedger,
}
