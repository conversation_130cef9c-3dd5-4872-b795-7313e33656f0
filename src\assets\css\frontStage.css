nav,
main,
footer,
.content-box {
  min-width: 1400px;
}

nav .content-box > div:last-child {
  padding: 32px 0;
}

nav .content-box .btns-user {
  height: 45px;
  font-family: "SourceHanSansCN-Regular";
  font-size: 14px;
  background-color: #f7f7f7;
}

nav .content-box .btns-user img,
nav .content-box .btns-user span {
  cursor: pointer;
}

nav .content-box .btns-user .user-left {
  height: 14px;
  display: flex;
  align-items: center;
}

nav .content-box .btns-user .user-left > div:first-child {
  height: 16px;
  display: flex;
  align-items: center;
}

nav .content-box .btns-user .user-left > div:first-child img {
  margin-right: 9px;
}

nav .content-box .btns-user .user-left > div:first-child span {
  height: 16px;
  margin-right: 40px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
}

nav .content-box .btns-user .user-left .login-btns {
  width: 322px;
  height: 14px;
  line-height: 16px;
  font-size: 14px;
  color: #216ec6;
  letter-spacing: 0;
  font-weight: 400;
}

nav .content-box .btns-user .user-right {
  height: 14px;
  color: #216ec6;
}

nav .content-box .btns-user .user-right img {
  margin-right: 6px;
}

nav .content-box .logo img {
  width: 366px;
}

nav .content-box .search-box {
  width: unset;
  height: 40px;
  display: flex;
}

nav .content-box .search-box .input-box {
  padding: 0 18px;
  border: 1px solid #dedede;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

nav .content-box .search-box .input-box img {
  width: 16px;
  height: 16px;
  margin-right: 18px;
}

nav .content-box .search-box .input-box input {
  height: 36px;
  font-size: 18px;
  flex-grow: 1;
}

nav .content-box .search-box button {
  width: 80px;
  color: #fff;
  background-color: #216ec6;
}

footer {
  height: 316px;
  background-color: #0e1926;
}

footer .content-box {
  height: inherit;
  color: #fff;
  display: flex;
  justify-content: space-between;
}

footer .content-box .contact-info {
  width: 480px;
  height: 200px;
  margin-top: 54px;
}

footer .content-box .contact-info .title {
  height: 56px;
  padding-left: 46px;
  font-size: 40px;
  font-family: 'PingFangSC-Semibold';
  line-height: 56px;
  position: relative;
}

footer .content-box .contact-info .title img {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 6px;
  left: 0;
}

footer .content-box .contact-info .tel {
  height: 82px;
  font-size: 70px;
  line-height: 82px;
}

footer .content-box .contact-info .active-time,
footer .content-box .contact-info .record {
  height: 30px;
  font-size: 22px;
  line-height: 30px;
  font-weight: lighter;
  font-family: 'PingFang-SC-Regular';
}

footer .content-box .navigation {
  width: 660px;
  font-family: 'PingFangSC-Semibold';
  display: flex;
  justify-content: space-between;
  align-items: center;
}

footer .content-box .navigation a {
  font-size: 28px;
  font-family: 'PingFangSC-Semibold';
}

footer .content-box .navigation a:hover {
  color: #216ec6;
}

.content-box {
  width: 1354px;
  margin: 0 auto;
}

/deep/ .el-dialog {
  width: 800px;
  margin-top: 261px !important;
}

/deep/ .el-dialog .el-dialog__body {
  padding: 0;
}

/deep/ .el-dialog .el-dialog__header,
/deep/ .el-dialog .el-dialog__close.el-icon.el-icon-close {
  display: none;
}

.dialog-header {
  height: 41px;
  font-family: "SourceHanSansCN-Medium";
  font-size: 20px;
  font-weight: 500;
  color: #333333;
}

.dialog-header .dialog-header-top {
  height: 20px;
  margin-bottom: 20px;
  font-weight: 500;
}

.dialog-header .dialog-header-top .dialog-title > div:first-child {
  height: 20px;
  width: 3px;
  margin-right: 9px;
  background: #216ec6;
}

.dialog-header .dialog-header-top .dialog-close {
  cursor: pointer;
}

.dialog-header > div:last-child {
  width: inhert;
  height: 1px;
  background: #d8d8d8;
}

.dialog-login .login-box {
  width: 400px;
  margin-top: 78px;
}

.dialog-login .login-box /deep/ .el-tabs.el-tabs--top {
  width: 400px !important;
  margin: 0 auto;
  font-size: 20px !important;
}

.dialog-login .login-box /deep/ .el-tabs.el-tabs--top .el-tabs__header {
  width: 270px !important;
  margin: 0 65px 30px 65px;
  border: none !important;
}

.dialog-login .login-box /deep/ .el-tabs.el-tabs--top .el-tabs__item.is-top {
  padding-top: 0;
  font-size: 20px;
  font-family: "SourceHanSansCN-Regular";
  color: #333333;
}

.dialog-login .login-box /deep/ .el-tabs.el-tabs--top .el-tabs__nav.is-top {
  padding-bottom: 8px !important;
  border: none !important;
}

.dialog-login .login-box /deep/ .el-tabs.el-tabs--top .el-tabs__active-bar.is-top {
  height: 3px;
  margin: 0 auto !important;
  background: #226fc7;
}

.dialog-login .login-box /deep/ .el-tabs.el-tabs--top #tab-first.el-tabs__item.is-top:active .el-tabs__active-bar.is-top {
  transform: translateX(8px) !important;
}

.dialog-login .login-box /deep/ .el-tabs.el-tabs--top #tab-second.el-tabs__item.is-top:active .el-tabs__active-bar {
  transform: translateX(166px) !important;
}

.dialog-login .login-box .input-box {
  width: 400px;
  height: 55px;
  margin-bottom: 30px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
}

.dialog-login .login-box .input-box img {
  width: 15px;
  height: 20px;
  margin-right: 10px;
}

.dialog-login .login-box .input-box img:last-child {
  width: unset;
  height: unset;
  margin: 0 0 0 10px;
}

.dialog-login .login-box .input-box input {
  height: 53px;
  font-size: 20px;
  flex-grow: 1;
}

.dialog-login .login-box .input-box ::-webkit-input-placeholder {
  font-size: 20px;
  color: #d9d9d9;
}

.dialog-login .login-box .verify-box {
  height: 55px;
  margin-bottom: 30px;
}

.dialog-login .login-box .verify-box input {
  width: 219px !important;
}

.dialog-login .login-box .verify-box .input-box {
  width: 270px;
  margin-bottom: unset;
}

.dialog-login .login-box .verify-box button {
  width: 120px;
  font-size: 16px;
  border: 1px solid #216ec6;
  background: #ffffff;
  color: #216ec6;
  border-radius: 5px;
}

.dialog-login .login-box .verify-box button:active {
  color: #fff;
  background-color: #216ec6;
}

.dialog-login .login-box button {
  width: 400px;
  height: 55px;
  font-size: 20px;
  font-family: "SourceHanSansCN-Regular";
  color: #fff;
  background-color: #216ec6;
}

@media screen and (max-width: 1354px) {
  .content-box {
    width: 100%;
  }
}
