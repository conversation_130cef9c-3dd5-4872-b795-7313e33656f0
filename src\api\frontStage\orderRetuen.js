import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

// 退货
const create = params => {
    return httpPost({
        url: '/materialMall/userCenter/orderReturn/create',
        params,
    })
}
const getMaxOrderReturnQty = params => {
    return httpGet({
        url: '/materialMall/userCenter/orderReturn/getMaxOrderReturnQty',
        params,
    })
}
const getOrderReturnPageList = params => {
    return httpPost({
        url: '/materialMall/userCenter/orderReturn/getOrderReturnPageList',
        params,
    })
}
const ChangReturnState = params => {
    return httpPost({
        url: '/materialMall/userCenter/orderReturn/ChangReturnState',
        params,
    })
}
export {
    create,
    getMaxOrderReturnQty,
    getOrderReturnPageList,
    ChangReturnState
}