import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

const getRmbRate = params => {
    return httpGet({
        url: '/config/cy/getRmbRate',
        params
    })
}
//获取流程按钮显示状态
const getCurrentUnitInfo =  params=> {
    return httpPost({
        url: '/facilityplan/flowEngin/getCurrentUnitInfo',
        params
    })
}
const request = {
    // 获取机构下某币种的人民币汇率
    getRmbRate (params) {
        return httpGet({
            url: '/config/cy/getRmbRate',
            params
        })
    },
    //审核
    audit (params) {
        return httpPost({
            url: '/facilityplan/flowEngin/audit',
            params
        })
    },
    //提交
    commit (params) {
        return httpPost({
            url: '/facilityplan/flowEngin/commit',
            params
        })
    },
    //获取审核历史
    history (params) {
        return httpPost({
            url: '/facilityplan/flowEngin/get/audit/history',
            params
        })
    },
    //获取流程按钮显示状态
    getCurrentUnitInfo (params) {
        return httpPost({
            url: '/facilityplan/flowEngin/getCurrentUnitInfo',
            params
        })
    },
    //撤回
    undoworkt (params) {
        return httpPost({
            url: '/facilityplan/flowEngin/undowork',
            params
        })
    },
    //作废
    nullify (params) {
        return httpPost({
            url: '/facilityplan/common/nullify',
            params
        })
    }
}
export {
    getRmbRate,
    getCurrentUnitInfo
}
export default request
