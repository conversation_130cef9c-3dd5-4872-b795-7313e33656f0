<template>
    <div class="productListPage">
        <main>
            <div class="product-box center front">
                <filterBox :form="filterArr" :list="checkedList" @checkChange="checkChange"></filterBox>
                <div class="dfa filterBox_top">
<!--                    <el-radio-group v-model="queryAll.checkedRadio" @change="checkboxChange">-->
<!--                        <el-radio v-for="item in checkboxList" :label="item.value" :value="item.value" :key="item.value">-->
<!--                            {{ item.label }}-->
<!--                        </el-radio>-->
<!--                    </el-radio-group>-->
                    <div :class="sortObj.tag == 0 ? 'selected sortPrice sortBtn ml10 pointer' : 'sortPrice sortBtn ml10 pointer'"
                         @click="selectSort(0)">
                        <span style="margin-right: 0;">综合排序</span>
                    </div>
                    <div :class="sortObj.tag == 1 ? 'selected sortPrice sortBtn pointer' : 'sortPrice sortBtn pointer'" @click="selectSort(1)">
                        <span>价格</span><img :src="priceArrow" alt="">
                    </div>
                    <div :class="sortObj.tag == 3 ? 'selected sortBtn pointer' : 'sortBtn pointer'" @click="selectSort(2)">
                        <span>销量</span><img :src="timeArrow" alt="">
                    </div>
                    <div :class="sortObj.tag == 2 ? 'selected sortBtn pointer' : 'sortBtn pointer'" @click="selectSort(2)">
                        <span>更新时间</span><img :src="timeArrow" alt="">
                    </div>
                    <div :class="['sortBtn','pointer', sortObj.tag === 4 ? 'selected' : '']"  @click="selectSort(4)">
                        <span>综合评价</span><img :src="ComprehensiveEvaluation" alt="">
                    </div>
                </div>
                <div class="row dfa filterBox_bottom">
                    <div class="priceFilter">￥<input type="text" v-model="queryAll.price.min"></div>
                    <div class="bar"></div>
                    <div class="priceFilter">￥<input type="text" v-model="queryAll.price.max"></div>
                    <div class="searchInput df">
                        <div class="dfa">
                            <img src="@/assets/images/search1.png" alt="">
                            <input type="text" placeholder="搜索商品" v-model="queryAll.keyword" />
                        </div>
                        <button @click="onSearch">搜索</button>
                    </div>
                </div>
                <div class="product-list">
                    <!--@click="$router.push({ path: '/mFront/productDetail', query: { productId: item.productId, shopId: item.shopId } })" :key="i">-->
                    <div class="item dfa mb20" v-for="(item, i) in productList"
                         @click="goProductPage(item)" :key="i">
                        <img :src="item.productMinImg ? imgUrlPrefixAdd + item.productMinImg : require('@/assets/images/img/queshen5.png')" alt="">
                        <span class="title textOverflow2">{{ item.productName }}</span>
                        <span class="description textOverflow1">{{ item.skuName }}</span>
                        <span class="price">￥{{ item.productMinPrice.toFixed(2) }}</span>
                    </div>
                </div>
                <Pagination
                    :total="totalCount"
                    :current-page="queryAll.page"
                    :page-size.sync="queryAll.limit"
                    :page-sizes="[40, 60, 80]"
                    @currentChange="handlePageChange"
                    @sizeChange="handleSizeChange"
                />
            </div>
        </main>
    </div>
</template>
<script>
// import addrCom from '@/components/common/addrComp'
import filterBox from '../../components/searchBox'
import Pagination from '@/pages/frontStage/components/simplePagination'
import arrow from '@/assets/images/arrow.png'
import arrow_up from '@/assets/images/arrow_up.png'
import arrow2 from '@/assets/images/arrow2.png'
import arrow2_up from '@/assets/images/arrow2_up.png'
import { getBrand } from '@/api/frontStage/brand'
import { getLoginMaterialPageList, getMaterialPageList } from '@/api/frontStage/productList'
import { getBrandMU } from '@/utils/common'
import { mapState } from 'vuex'
export default {
    components: {
        filterBox, Pagination
    },
    props: ['page'],
    data () {
        return {
            currentTab: null,
            init: {
                // 商品类型传99表示两种商品都查
                productType: 99,
                mallType: 0
            },
            queryAll: {
                page: 1,
                limit: 20,
                label: {},
                price: {
                    min: null, max: null,
                },
                keyword: null,
                checkedRadio: 0,
                shopId: null,
            },
            tagFilterStr: '0-desc',
            productList: [],
            arrow,
            arrow_up,
            arrow2,
            arrow2_up,
            priceArrow: arrow,
            timeArrow: arrow,
            ComprehensiveEvaluation: arrow,
            checkedList: [],
            filterArr: [],
            checkboxList: [
                { label: '全部', value: null },
                { label: '平台自营', value: 1 },
                { label: '路桥内部店', value: 2 },
                { label: '其它', value: 3 },
            ],
            sortObj: {
                tag: 0,
                descend: true,
            },
            flag: true,
            totalCount: 100
        }
    },
    watch: {
        'sortObj.tag': {
            handler (newVal, oldVal) {
                if (oldVal == 2) {
                    if (this.sortObj.descend) {
                        return this.timeArrow = this.arrow
                    }
                    this.timeArrow = this.arrow_up
                } else if (oldVal == 1) {
                    if (this.sortObj.descend) {
                        return this.priceArrow = this.arrow
                    }
                    this.priceArrow = this.arrow_up
                }
                else if (oldVal == 3) {
                    if (this.sortObj.descend) {
                        return this.priceArrow = this.arrow
                    }
                    this.priceArrow = this.arrow_up
                }else if (oldVal == 4) {
                    if (this.sortObj.descend) {
                        return this.ComprehensiveEvaluation = this.arrow
                    }
                    this.ComprehensiveEvaluation = this.arrow_up
                }
            }
        },
        tagFilterStr (newVal) {
            switch (newVal) {
            case '1-asc':
                this.priceArrow = this.arrow2_up
                break
            case '1-desc':
                this.priceArrow = this.arrow2
                break
            case '2-asc':
                this.timeArrow = this.arrow2_up
                break
            case '2-desc':
                this.timeArrow = this.arrow2
                break
            case '3-asc':
                this.timeArrow = this.arrow2_up
                break
            case '3-desc':
                this.timeArrow = this.arrow2
                break
            case '4-asc':
                this.ComprehensiveEvaluation = this.arrow2_up
                break
            case '4-desc':
                this.ComprehensiveEvaluation = this.arrow2
                break
            }
        },

    },
    computed: { ...mapState(['userInfo']) },
    created () {
        this.currentTab = this.$route.query.currentTab
        if (this.currentTab == 3) {
            this.queryAll.shopId = '1878734518961074177'
        } else {
            this.queryAll.shopId = this.$route.query.shopId
        }
        if(this.page > 1) this.queryAll.page = this.page
        this.getBrandM()
        this.getMaterialPageListM()
    },
    methods: {
        goProductPage (item) {
            sessionStorage.setItem('bRoute', 'productDetail-' + this.queryAll.page)
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/mFront/productDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                query: { productId: item.productId, shopId: item.shopId }
            })
        },
        handlePageChange (page) {
            this.queryAll.page = page
            this.getMaterialPageListM()
        },
        handleSizeChange (size) {
            this.queryAll.limit = size
            this.getMaterialPageListM()
        },
        // 获取品牌
        async getBrandM () {
            let res = await getBrand({ page: 1, limit: 20 })
            let brands = await getBrandMU(res)
            // let list = {
            //     name: '分类', type: 'brand2', options: [
            //         { label: '不限', value: null, type: 'brand2' },
            //         { label: '安全类', value: '安全类', type: 'brand2', options: [
            //             { label: '安全类', value: '安全类', type: 'brand2' },
            //             { label: '安全类1', value: '安全类1', type: 'brand2' },
            //             { label: '安全类2', value: '安全类2', type: 'brand2' },
            //             { label: '安全类3', value: '安全类3', type: 'brand2' },
            //         ] },
            //         { label: '工具类', value: '工具类', type: 'brand2', options: [
            //             { label: '工具类', value: '工具类', type: 'brand2' },
            //             { label: '工具类1', value: '工具类1', type: 'brand2' },
            //             { label: '工具类2', value: '工具类2', type: 'brand2' },
            //             { label: '工具类3', value: '工具类3', type: 'brand2' },
            //         ] },
            //         { label: '机电类', value: '机电类', type: 'brand2', options: [
            //             { label: '机电类', value: '机电类', type: 'brand2' },
            //             { label: '机电类1', value: '机电类1', type: 'brand2' },
            //             { label: '机电类2', value: '机电类2', type: 'brand2' },
            //             { label: '机电类3', value: '机电类3', type: 'brand2' },
            //         ] }
            //     ]
            // }
            // this.filterArr = [list, brands]
            this.filterArr = [brands]
        },
        // 获取商品列表
        getMaterialPageListM () {
            let params = this.initParams()
            if(this.userInfo.token) {
                getLoginMaterialPageList(params).then(res => {
                    this.queryAll.page = res.currPage
                    this.queryAll.limit = res.pageSize
                    this.productList = res.list
                    this.totalCount = res.totalCount
                })
            }else {
                getMaterialPageList(params).then(res => {
                    this.queryAll.page = res.currPage
                    this.queryAll.limit = res.pageSize
                    this.productList = res.list
                    this.totalCount = res.totalCount
                })
            }

        },
        // 初始化参数
        initParams () {
            let params = {
                productType: this.init.productType,
                page: this.queryAll.page,
                limit: this.queryAll.limit,
                orderBy: this.tagFilterStr,
            }
            let { keyword, label, price, shopId, classId } = this.queryAll
            if (classId != null) {
                params.classId = classId
            }
            // 关键字
            if (keyword != null) {
                params.keywords = keyword
            }
            // 品牌
            if (label.brand != null) {
                params.brandId = label.brand.value
                params.brandName = label.brand.label
            }
            // 城市
            if (label.city != null) {
                params.city = label.city.value
            }
            // 以下价格
            if (price.max != null) {
                params.belowPrice = price.max
            }
            // 以上价格
            if (price.min != null) {
                params.abovePrice = price.min
            }
            // 店铺id
            if(shopId != null) {
                params.shopId = shopId
            }
            return params
        },
        getFilterStr (obj) {
            if (obj.descend) {
                return this.tagFilterStr = `${obj.tag}-desc`
            }
            this.tagFilterStr = `${obj.tag}-asc`
        },
        // 条件列表
        checkChange (obj, obj2) {
            console.log(obj, obj2) // Obj2是分类的参数
            this.queryAll.label = obj
            this.queryAll.classId = obj2.classId
            this.getMaterialPageListM()
        },
        // 按钮点击
        checkboxChange () {
            this.getMaterialPageListM()
        },
        onSearch () {
            this.getMaterialPageListM()
        },
        // 排序
        selectSort (tag) {
            if (this.sortObj.tag == tag) {
                this.sortObj.descend = !this.sortObj.descend
            }
            this.sortObj.tag = tag
            this.getFilterStr(this.sortObj)
            this.getMaterialPageListM()
        },
    }
}
</script>
<style scoped lang="scss">
.productListPage {

    .tabs-bar {
        width: 100%;
        height: 60px;

        .top_content {
            width: 1326px;
            min-width: 1326px;
            height: inherit;

            &>div {
                width: 200px;
                height: inherit;
                font-size: 18px;
                font-weight: 500;
                text-align: center;
                line-height: 60px;
                color: rgba(51, 51, 51, 1);
                position: relative;
                cursor: pointer;

                .icon {
                    width: 18px;
                    height: 18px;
                    position: absolute;
                    left: 23px;
                    top: 20px;
                    background-image: url(../../../../assets/images/productList/分类.png);
                    background-size: 18px 18px;
                }
            }

            &>div:first-child {
                color: #fff;
                background: rgba(33, 110, 198, 1);
            }
        }
    }

    main {
        padding: 20px 0 60px 0;
        background-color: #f5f5f5;
    }

    .product-box {
        width: 1326px;
        min-width: 1326px;

        .product-list {
            width: inherit;
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;

            .item {
                width: 250px;
                height: 300px;
                font-size: 14px;
                flex-direction: column;
                background-color: #fff;

                &:not(:nth-of-type(5n)) {
                    margin-right: 19px;
                }

                img {
                    width: 250px;
                    height: 200px;
                    object-fit: cover;
                }

                span {
                    margin-bottom: 5px;
                }

                .title {
                    height: 40px;
                    margin: 0 10px;
                    text-align: center;
                    line-height: 20px;
                    color: rgba(51, 51, 51, 1);
                }

                .description {
                    height: 20px;
                    line-height: 20px;
                    color: rgba(153, 153, 153, 1);
                }

                .price {
                    font-size: 16px;
                    line-height: 23px;
                    color: rgba(212, 48, 48, 1);
                }
            }
        }
    }
}
</style>