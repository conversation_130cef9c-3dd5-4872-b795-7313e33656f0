<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" >
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn">
            </div>
          </div>
          <div class="search_box">
            <el-input clearable type="text" @keyup.enter.native="onSearch" placeholder="单位名称/对账单编号" v-model="keywords">
              <img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
            </div>
          </div>
        </div>
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" v-loading="isLoading" :style="{ width: '100%' }">
        <el-table class="table" :height="rightTableHeight" :data="tableData" border
                  @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <span class="action" @click="handleView(scope)">查看</span>
            </template>
          </el-table-column>
          <el-table-column label="对账单编号"   prop="billNo">
          </el-table-column>
          <el-table-column label="单位名称" width="220">
            <template slot-scope="scope">
              {{scope.row.orgName}}
            </template>
          </el-table-column>
<!--          <el-table-column label="到货总数量" width="">-->
<!--            <template slot-scope="scope" >-->
<!--              {{scope.row.gmtModified}}-->
<!--            </template>-->
<!--          </el-table-column>-->

          <el-table-column label="金额合计"   prop="totalAmount">
          </el-table-column>

          <el-table-column label="对账日期"  width="120"  prop="accountDate">
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                     @currentChange="currentChange" @sizeChange="sizeChange" />
    </div>
    <!-- ----------------查询弹框---------------- -->
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="55%" :close-on-click-modal="true" :before-close="closeDialog">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
        <el-row>
          <el-col :span="12">
            <el-form-item width="50px" label="对账单编号：" prop="orgName">
              <el-input clearable v-model="filterData.billNo" placeholder="对账单编号" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item width="150px" label="单位名称：" prop="orgName">
              <el-input clearable v-model="filterData.orgName" placeholder="请输入单位名称（模糊查询）" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
         <el-row>
        <el-col :span="12" :offset="0">
          <el-form-item label="最低价格以上：">
            <el-input clearable type="number" v-model="filterData.lowTotalAmount" placeholder="请输入价格区间" style="width: 200px"></el-input>
          </el-form-item>
          <el-form-item label="最高价格以下：">
            <el-input clearable type="number" v-model="filterData.tallTotalAmount" placeholder="请输入价格区间" style="width: 200px"></el-input>
          </el-form-item>
        </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="签订日期：">
              <el-date-picker
                  value-format="yyyy-MM-dd"
                  v-model="filterData.accountDates"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">返回</el-button>
            </span>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { getDataList } from '@/api/supplierSys/accountStatement/accountStatement'
export default {
    components: {
        ComPagination
    },
    watch: {

    },
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertName: '对账单',
            queryVisible: false,
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            isLoading: false,
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                billNo: '',
                orgName: '',
                tallTotalAmount: '',
                lowTotalAmount: '',
                accountDates: []
            },
            tableData: [],
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
            },
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},

            // 表单校验规则
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))

    },
    created () {
        this.isLoading = true
        this.getTableData()
    },
    methods: {
    // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                billNo: '',
                orgName: '',
                tallTotalAmount: '',
                lowTotalAmount: '',
                accountDates: []
            },
            done()
        },
        //高级查询返回
        hideDialog () {
            this.filterData = {
                billNo: '',
                orgName: '',
                tallTotalAmount: '',
                lowTotalAmount: '',
                accountDates: []
            },
            this.queryVisible = false
        },
        // 获取页面参数

        // 高级查询
        advancedQuery () {
            this.keywords == null
            this.getTableData()
            this.filterData = {
                billNo: '',
                orgName: '',
                tallTotalAmount: '',
                lowTotalAmount: '',
                accountDates: []
            },
            this.queryVisible = false
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        handleClose () { },
        handleView (scope) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/pcwp1/accountStatementDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'pcwp1accountStatement',
                params: {
                    row: scope.row
                }
            })
        },
        // 获取列表数据
        async getTableData () {
            this.isLoading = true
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.billNo != null) {
                params.billNo = this.filterData.billNo
            }
            if (this.filterData.orgName != null) {
                params.orgName = this.filterData.orgName
            }
            if (this.filterData.accountDates.length != 0) {
                params.startTime = this.filterData.accountDates[0]
                params.endTime = this.filterData.accountDates[1]
            }
            if (this.filterData.lowTotalAmount != null) {
                params.lowTotalAmount = this.filterData.lowTotalAmount
            }
            if (this.filterData.tallTotalAmount != null) {
                params.tallTotalAmount = this.filterData.tallTotalAmount
            }
            getDataList(params).then(res => {
                this.pages = res
                this.tableData = res.list
                this.isLoading = false
            }).catch(() => {
                this.formLoading = false
            })
            this.viewList = true
        },
        // 关键词搜索
        onSearch () {
            this.getTableData()
            // 参数
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}
.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}
</style>
