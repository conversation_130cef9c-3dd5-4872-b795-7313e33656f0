import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/links/findByConditionByPage',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/platform/links/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/links/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/links/delete',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/links/updatePublish',
        params
    })
}
//das
const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/links/updateNotPublish',
        params
    })
}
//
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/links/deleteBatch',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/platform/links/updateBatchById',
        params
    })
}

export {
    getList,
    edit,
    create,
    del,
    batchPublish,
    batchDelete,
    batchNotPublish,
    changeSortValue
}
