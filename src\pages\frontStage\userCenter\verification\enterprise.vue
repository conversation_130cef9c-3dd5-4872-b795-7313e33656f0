<!--<template>-->
<!--    <main>-->
<!--        <div class="list-title df mb20">企业/个体户认证</div>-->
<!--        <el-tabs v-model="activeTab">-->
<!--            <el-tab-pane label="我是企业" name="enterprise">-->
<!--                <el-form :model="enterpriseForm" :rules="enterpriseFormRules" ref="enterpriseForm" label-width="168px" class="enterpriseForm"-->
<!--                    :inline="false">-->
<!--                    <div class="df">-->
<!--                        <el-form-item class="licenseUploader" label="营业执照图片：" prop="businessLicense">-->
<!--                            <el-upload class="avatar-uploader" action="fakeaction" :before-upload="handleBeforeUpload" name="img" :auto-upload="true"-->
<!--                                :show-file-list="false" :on-change="handleUploadChange" :http-request="uploadLicenseEnterprise">-->
<!--                                <img v-if="enterpriseForm.businessLicense" :src="imgUrlPrefixAdd + enterpriseForm.businessLicense" class="avatar">-->
<!--                                <div v-else class="licenseUploader">-->
<!--                                    <img src="@/assets/images/userCenter/upload_yyzz.png" />-->
<!--                                </div>-->
<!--                            </el-upload>-->
<!--                        </el-form-item>-->
<!--                        <el-form-item class="licenseUploader">-->
<!--                            <div class="uploadDemo dfa">-->
<!--                                <img src="@/assets/images/userCenter/yyzz_demo.png" alt="">-->
<!--                                <div><span>示例图</span><i class="el-icon-zoom-in"></i></div>-->
<!--                            </div>-->
<!--                        </el-form-item>-->
<!--                        <div class="uploadTip">请上传10MB以内的PNG，JPG，GIF，BMP格式图片</div>-->
<!--                    </div>-->
<!--                    <el-row>-->
<!--                        <el-col :span="11" :offset="0">-->
<!--                            <el-form-item label="企业名称：" prop="enterpriseName">-->
<!--                                <el-input clearable v-model="enterpriseForm.enterpriseName" placeholder="请填写50字以内的企业名称"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                        <el-col :span="11" :offset="1">-->
<!--                            <el-form-item label="统一社会信用代码：" prop="socialCreditCode">-->
<!--                                <el-input clearable v-model="enterpriseForm.socialCreditCode" placeholder="请填写18位或15位信用代码"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <el-row>-->
<!--                        <el-col :span="11" :offset="0">-->
<!--                            <el-form-item label="企业类型：" prop="enterpriseType">-->
<!--                                &lt;!&ndash; <el-input clearable v-model="enterpriseForm.enterpriseType" placeholder="请选择企业类型"></el-input> &ndash;&gt;-->
<!--                                <el-select v-model="enterpriseForm.enterpriseType" placeholder="请选择企业类型">-->
<!--                                    <el-option label="有限公司" value="有限公司"></el-option>-->
<!--                                    <el-option label="企业类型二" value="2"></el-option>-->
<!--                                </el-select>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                        <el-col :span="11" :offset="1">-->
<!--                            <el-form-item class="licenseValidTime" label="法定代表人：" prop="legalRepresentative">-->
<!--                                <el-input clearable v-model="enterpriseForm.legalRepresentative" placeholder="请填写50字以内的法定代表人姓名"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <el-row>-->
<!--                        <el-col :span="12" :offset="0">-->
<!--                            <el-form-item label="注册时间：" prop="creationTime">-->
<!--                                &lt;!&ndash;                                        <el-input clearable v-model="enterpriseForm.creationTime" type="date" placeholder="请选择企业注册日期" :picker-options="pickerOptions"></el-input>&ndash;&gt;-->
<!--                                <el-date-picker v-model="enterpriseForm.creationTime" align="right" type="date" value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                                    placeholder="请选择企业注册日期" :picker-options="pickerOptions">-->
<!--                                </el-date-picker>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                        <el-col :span="12" :offset="1">-->
<!--                            <el-form-item class="licenseValidTime" label="营业执照有效期：" prop="licenseTerm">-->
<!--                                <el-date-picker v-model="enterpriseForm.licenseTerm" align="right" type="date" :value-format="dateFormat"-->
<!--                                    placeholder="请选择营业执照有效期" :picker-options="pickerAfterOptions">-->
<!--                                </el-date-picker>-->
<!--&lt;!&ndash;                                <el-checkbox label="长期" :indeterminate="false" v-model="longTerm"></el-checkbox>&ndash;&gt;-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <el-row>-->
<!--                        <el-col :span="12" :offset="0">-->
<!--                            <el-form-item label="注册资本：" prop="registeredCapital">-->
<!--                                <el-input clearable v-model="enterpriseForm.registeredCapital" placeholder="请填写企业注册资本"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                        <el-col :span="12" :offset="1">-->
<!--                            <el-form-item class="registerAddress" label="企业注册地址：" prop="address">-->
<!--                                <div>-->
<!--                                    <el-select class="province" v-model="enterpriseForm.provinces" value-key="" placeholder="省份"-->
<!--                                        @change="(code) => getSubDistrict(code, 1)">-->
<!--                                        <el-option v-for="item in addressOptions.province" :key="item.value" :label="item.districtName"-->
<!--                                            :value="item.districtCode">-->
<!--                                        </el-option>-->
<!--                                    </el-select>-->
<!--                                    <el-select class="city" v-model="enterpriseForm.city" value-key="" placeholder="地级市"-->
<!--                                        @change="(code) => getSubDistrict(code, 2)">-->
<!--                                        <el-option v-for="item in addressOptions.city" :key="item.value" :label="item.districtName"-->
<!--                                            :value="item.districtCode">-->
<!--                                        </el-option>-->
<!--                                    </el-select>-->
<!--                                    <el-select class="county" v-model="enterpriseForm.county" value-key="" placeholder="区、县">-->
<!--                                        <el-option v-for="item in addressOptions.district" :key="item.value" :label="item.districtName"-->
<!--                                            :value="item.districtCode">-->
<!--                                        </el-option>-->
<!--                                    </el-select>-->
<!--                                </div>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <el-row>-->
<!--                        <el-col :span="24">-->
<!--                            <el-form-item label="详细地址：" prop="detailedAddress">-->
<!--                                <el-input clearable v-model="enterpriseForm.detailedAddress" placeholder="请填写营业执照注册详细地址"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <el-row>-->
<!--                        <el-col :span="24">-->
<!--                            <el-form-item label="主营业务：" prop="mainBusiness">-->
<!--                                <el-input clearable v-model="enterpriseForm.mainBusiness" placeholder="请填写与营业执照相同的主营业务"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <div class="separ center"></div>-->
<!--                    <div class="subtitle">设置管理员</div>-->
<!--                    <el-row>-->
<!--                        <el-col :span="11" :offset="0">-->
<!--                            <el-form-item label="身份证人像面照：" prop="cardPortraitFace">-->
<!--                                <el-upload class="identityUpload face" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1, 1)"-->
<!--                                    :show-file-list="false">-->
<!--                                    <img class="identityUpload" v-if="enterpriseForm.cardPortraitFace"-->
<!--                                        :src="imgUrlPrefixAdd + enterpriseForm.cardPortraitFace" alt="">-->
<!--                                    <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">-->
<!--                                </el-upload>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                        <el-col :span="11" :offset="1">-->
<!--                            <el-form-item label="身份证国徽面照：" prop="cardPortraitNationalEmblem">-->
<!--                                <el-upload class="identityUpload badge" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2, 1)"-->
<!--                                    :show-file-list="false">-->
<!--                                    <img class="identityUpload" v-if="enterpriseForm.cardPortraitNationalEmblem"-->
<!--                                        :src="imgUrlPrefixAdd + enterpriseForm.cardPortraitNationalEmblem" alt="">-->
<!--                                    <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">-->
<!--                                </el-upload>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <el-row>-->
<!--                        <el-col :span="11" :offset="0">-->
<!--                            <el-form-item label="手机号码：" prop="adminPhone">-->
<!--                                <el-input clearable v-model="enterpriseForm.adminPhone" placeholder="请输入11位手机号码"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                        <el-col :span="11" :offset="1">-->
<!--                            <el-form-item label="验证码：" prop="verificationCode">-->
<!--                                <div class="verifyBox dfb">-->
<!--                                    <el-input clearable v-model="enterpriseForm.verificationCode" placeholder="请输入短信验证码"></el-input>-->
<!--                                    <el-button @click="getVerificationCode()">获取短信验证码</el-button>-->
<!--                                </div>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                    <el-row>-->
<!--                        <el-col :span="11" :offset="0">-->
<!--                            <el-form-item label="姓名：" prop="adminName">-->
<!--                                <el-input clearable v-model="enterpriseForm.adminName" placeholder="请填写真实姓名"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                        <el-col :span="11" :offset="1">-->
<!--                            <el-form-item label="身份证号码：" prop="adminNumber">-->
<!--                                <el-input clearable v-model="enterpriseForm.adminNumber" placeholder="请填写18位身份证号码"></el-input>-->
<!--                            </el-form-item>-->
<!--                        </el-col>-->
<!--                    </el-row>-->
<!--                </el-form>-->
<!--                <div class="btns center dfb">-->
<!--                    <button @click="$router.go(-1)">返回</button>-->
<!--                    <button @click="onSubmit">提交</button>-->
<!--                </div>-->
<!--            </el-tab-pane>-->
<!--            <el-tab-pane label="我是个体户" name="business">-->
<!--                <el-form class="businessForm" :model="businessForm" ref="businessForm" :rules="businessFormRules" label-width="168px" :inline="false">-->
<!--                            <div class="df">-->
<!--                                <el-form-item class="licenseUploader" label="营业执照图片：" prop="businessLicense">-->
<!--                                    <el-upload class="avatar-uploader" action="fakeaction"-->
<!--                                               :before-upload="handleBeforeUpload" name="img"-->
<!--                                               :auto-upload="true"-->
<!--                                               :show-file-list="false"-->
<!--                                               :on-change="handleUploadChange"-->
<!--                                               :http-request="uploadLicenseBusiness">-->
<!--                                        <img v-if="businessForm.businessLicense" :src="imgUrlPrefixAdd + businessForm.businessLicense" class="avatar" >-->
<!--                                        <div v-else class="licenseUploader">-->
<!--                                            <img src="@/assets/images/userCenter/upload_yyzz.png" />-->
<!--                                        </div>-->
<!--                                    </el-upload>-->
<!--                                </el-form-item>-->
<!--                                <el-form-item class="licenseUploader">-->
<!--                                    <div class="uploadDemo dfa" >-->
<!--                                        <img src="@/assets/images/userCenter/yyzz_demo.png" alt="">-->
<!--                                        <div ><span>示例图</span><i class="el-icon-zoom-in"></i></div>-->
<!--                                    </div>-->
<!--                                </el-form-item>-->
<!--                                <div class="uploadTip">请上传10MB以内的PNG，JPG，GIF，BMP格式图片</div>-->
<!--                            </div>-->
<!--                            <el-row>-->
<!--                                <el-col :span="11" :offset="0">-->
<!--                                    <el-form-item label="名称：" prop="enterpriseName">-->
<!--                                        <el-input clearable v-model="businessForm.enterpriseName" placeholder="请填写50字以内的企业名称"></el-input>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                                <el-col :span="11" :offset="1">-->
<!--                                    <el-form-item label="统一社会信用代码：" prop="socialCreditCode">-->
<!--                                        <el-input clearable v-model="businessForm.socialCreditCode" placeholder="请填写18位或15位信用代码"></el-input>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->
<!--                            <el-row>-->
<!--                                <el-col :span="11">-->
<!--                                    <el-form-item label="经营者" prop="operator">-->
<!--                                        <el-input clearable v-model="businessForm.operator" placeholder="请填写经营者姓名"></el-input>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                                <el-col :span="11" :offset="1">-->
<!--                                    <el-form-item label="经营场所：" prop="placeOfBusiness">-->
<!--                                        <el-input clearable v-model="businessForm.placeOfBusiness" placeholder="请填写与营业执照一致的经营场所"></el-input>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->
<!--                            <el-row>-->
<!--                                <el-col :span="11">-->
<!--                                    <el-form-item class="licenseValidTime" prop="creationTime" label="注册日期：">-->
<!--                                        <el-date-picker v-model="businessForm.creationTime" value-format="yyyy-MM-dd HH:mm:ss" align="right" type="date" placeholder="请选择注册日期"-->
<!--                                            :picker-options="pickerOptions">-->
<!--                                        </el-date-picker>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->
<!--                            <el-row>-->
<!--                                <el-col :span="24">-->
<!--                                    <el-form-item label="经营范围：" prop="mainBusiness">-->
<!--                                        <el-input clearable v-model="businessForm.mainBusiness" placeholder="请填写与营业执照一致的经营范围"></el-input>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->
<!--                            <div class="separ center"></div>-->
<!--                            <div class="subtitle">设置管理员</div>-->
<!--                            <el-row>-->
<!--                                <el-col :span="11" :offset="0">-->
<!--                                    <el-form-item label="身份证人像面照：" prop="cardPortraitFace">-->
<!--                                        <el-upload class="identityUpload face" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1,2)"-->
<!--                                            :show-file-list="false">-->
<!--                                            <img class="identityUpload" v-if="businessForm.cardPortraitFace" :src="imgUrlPrefixAdd + businessForm.cardPortraitFace"-->
<!--                                                alt="">-->
<!--                                            <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">-->
<!--                                        </el-upload>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                                <el-col :span="11" :offset="1">-->
<!--                                    <el-form-item label="身份证国徽面照：" prop="cardPortraitNationalEmblem">-->
<!--                                        <el-upload class="identityUpload badge" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2,2)"-->
<!--                                            :show-file-list="false">-->
<!--                                            <img class="identityUpload" v-if="businessForm.cardPortraitNationalEmblem" :src="imgUrlPrefixAdd + businessForm.cardPortraitNationalEmblem"-->
<!--                                                alt="">-->
<!--                                            <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">-->
<!--                                        </el-upload>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->
<!--                            <el-row>-->
<!--                                <el-col :span="11" :offset="0">-->
<!--                                    <el-form-item label="手机号码：" prop="adminPhone">-->
<!--                                        <el-input clearable v-model="businessForm.adminPhone" placeholder="请输入11位手机号码"></el-input>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                                <el-col :span="11" :offset="1">-->
<!--                                    <el-form-item label="验证码：" prop="verificationCode">-->
<!--                                        <div class="verifyBox dfb">-->
<!--                                            <el-input clearable v-model="businessForm.verificationCode" placeholder="请输入短信验证码"></el-input>-->
<!--                                            <el-button @click="getVerificationCode()">获取短信验证码</el-button>-->
<!--                                        </div>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->
<!--                            <el-row>-->
<!--                                <el-col :span="11" :offset="0">-->
<!--                                    <el-form-item label="姓名：" prop="adminName">-->
<!--                                        <el-input clearable v-model="businessForm.adminName" placeholder="请填写真实姓名"></el-input>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                                <el-col :span="11" :offset="1">-->
<!--                                    <el-form-item label="身份证号码：" prop="adminNumber">-->
<!--                                        <el-input clearable v-model="businessForm.adminNumber" placeholder="请填写18位身份证号码"></el-input>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->
<!--                        </el-form>-->
<!--                <div class="btns center dfb">-->
<!--                    <button @click="$router.go(-1)">返回</button>-->
<!--                    <button @click="onSubmit">提交</button>-->
<!--                </div>-->
<!--            </el-tab-pane>-->
<!--        </el-tabs>-->
<!--    </main>-->
<!--</template>-->
<!--<script>-->
<!--import { uploadFile } from '@/api/platform/common/file'-->
<!--import { getCascaderOptions } from '@/api/platform/common/components'-->
<!--export default {-->
<!--    data () {-->
<!--        return {-->
<!--            activeTab: 'enterprise',-->
<!--            dateFormat: 'yyyy-MM-dd HH:mm:ss',-->
<!--            showTerm: false,-->
<!--            longTerm: false,-->
<!--            // 地址选择器选项-->
<!--            addressOptions: {-->
<!--                province: [],-->
<!--                city: [],-->
<!--                district: []-->
<!--            },-->
<!--            pickerAfterOptions: {-->
<!--                disabledDate (time) {-->
<!--                    return time.getTime() < Date.now()-->
<!--                },-->
<!--            },-->
<!--            enterpriseForm: {-->
<!--                businessLicense: '',-->
<!--                enterpriseName: '',-->
<!--                socialCreditCode: '',-->
<!--                enterpriseType: '',-->
<!--                legalRepresentative: '',-->
<!--                operator: '',-->
<!--                creationTime: '',-->
<!--                licenseTerm: '',-->
<!--                registeredCapital: '',-->
<!--                provinces: '',-->
<!--                city: '',-->
<!--                county: '',-->
<!--                detailedAddress: '',-->
<!--                placeOfBusiness: '',-->
<!--                mainBusiness: '',-->
<!--                cardPortraitFace: '',-->
<!--                cardPortraitNationalEmblem: '',-->
<!--                adminPhone: '',-->
<!--                verificationCode: '',-->
<!--                adminName: '',-->
<!--                adminPassword: '',-->
<!--                adminNumber: '',-->
<!--                agreeTerm: false,-->
<!--            },-->
<!--            businessForm: {-->
<!--                businessLicense: '',-->
<!--                enterpriseName: '',-->
<!--                socialCreditCode: '',-->
<!--                enterpriseType: 0,-->
<!--                legalRepresentative: '',-->
<!--                operator: '',-->
<!--                creationTime: '',-->
<!--                licenseTerm: '',-->
<!--                registeredCapital: '',-->
<!--                provinces: '',-->
<!--                city: '',-->
<!--                county: '',-->
<!--                detailedAddress: '',-->
<!--                placeOfBusiness: '',-->
<!--                mainBusiness: '',-->
<!--                cardPortraitFace: '',-->
<!--                cardPortraitNationalEmblem: '',-->
<!--                adminPhone: '',-->
<!--                verificationCode: '',-->
<!--                adminName: '',-->
<!--                adminPassword: '',-->
<!--                adminNumber: '',-->
<!--                agreeTerm: false,-->
<!--            },-->
<!--            //企业表单验证-->
<!--            enterpriseFormRules: {-->
<!--                businessLicense: { min: 4, required: true, message: '请上传营业执照!', trigger: 'blur' },-->
<!--                enterpriseName: { min: 2, required: true, message: '请填写50字以内的企业名称!', trigger: 'blur' },-->
<!--                socialCreditCode: { required: true, validator: this.validateSocialCreditCode, message: '请输入15位或18位统一信用代码!', trigger: 'blur' },-->
<!--                enterpriseType: { required: true, message: '请选企业类型!', trigger: 'blur' },-->
<!--                legalRepresentative: { min: 2, max: 10, required: true, message: '请填写企业法定代表人!', trigger: 'blur' },-->
<!--                operator: { min: 2, max: 10, required: true, message: '请输入经营者姓名!', trigger: 'blur' },-->
<!--                creationTime: { required: true, validator: this.validateDate, trigger: 'blur' },-->
<!--                licenseTerm: { required: true, validator: this.validateDate, trigger: 'blur' },-->
<!--                address: { required: true, validator: this.validateAddress, trigger: 'change' },-->
<!--                detailedAddress: { min: 2, required: true, message: '请填写详细地址!', trigger: 'blur' },-->
<!--                placeOfBusiness: { min: 2, max: 10, required: true, message: '请输入真实姓名!', trigger: 'blur' },-->
<!--                mainBusiness: { min: 2, required: true, message: '请填写与营业执照相同的主营业务!', trigger: 'blur' },-->
<!--                cardPortraitFace: { min: 2, required: true, message: '请上传身份证人像面照!', trigger: 'blur' },-->
<!--                cardPortraitNationalEmblem: { min: 2, required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },-->
<!--                adminName: { min: 2, max: 10, required: true, message: '请输入真实姓名!', trigger: 'blur' },-->
<!--                adminNumber: { min: 18, max: 18, required: true, message: '请输入18位身份证号码!', trigger: 'blur' },-->
<!--                adminPhone: { min: 11, max: 11, required: true, message: '请输入11位手机号!', trigger: 'blur' },-->
<!--                verificationCode: { min: 6, max: 6, required: true, message: '请输入6位短信验证码!', trigger: 'blur' },-->
<!--                adminPassword: { min: 6, max: 14, required: true, validator: this.validatePassword, trigger: 'blur' },-->
<!--                agreeTerm: { required: true, message: '请勾选协议！', trigger: 'blur' },-->
<!--            },-->
<!--            //个体户表单验证-->
<!--            businessFormRules: {-->
<!--                businessLicense: { min: 4, required: true, message: '请上传营业执照！', trigger: 'blur' },-->
<!--                enterpriseName: { min: 2, required: true, message: '请填写50字以内的企业名称！', trigger: 'blur' },-->
<!--                socialCreditCode: { required: true, validator: this.validateSocialCreditCode, message: '请输入15位或18位统一信用代码！', trigger: 'blur' },-->
<!--                operator: { min: 2, max: 10, required: true, message: '请输入经营者姓名！', trigger: 'blur' },-->
<!--                creationTime: { required: true, validator: this.validateDate, trigger: 'blur' },-->
<!--                placeOfBusiness: { min: 2, max: 10, required: true, message: '请填写与营业执照一致的经营场所!', trigger: 'blur' },-->
<!--                mainBusiness: { min: 2, required: true, message: '请填写与营业执照相同的主营业务!', trigger: 'blur' },-->
<!--                cardPortraitFace: { min: 2, required: true, message: '请上传身份证人像面照!', trigger: 'blur' },-->
<!--                cardPortraitNationalEmblem: { min: 2, required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },-->
<!--                adminName: { min: 2, max: 10, required: true, message: '请输入真实姓名!', trigger: 'blur' },-->
<!--                adminNumber: { min: 18, max: 18, required: true, message: '请输入18位身份证号码!', trigger: 'blur' },-->
<!--                adminPhone: { min: 11, max: 11, required: true, message: '请输入11位手机号!', trigger: 'blur' },-->
<!--                verificationCode: { min: 6, max: 6, required: true, message: '请输入6位短信验证码!', trigger: 'blur' },-->
<!--                adminPassword: { min: 6, max: 14, required: true, validator: this.validatePassword, trigger: 'blur' },-->
<!--                agreeTerm: { required: true, message: '请勾选协议！', trigger: 'blur' },-->
<!--            },-->
<!--            // 日期选择器选项-->
<!--            pickerOptions: {-->
<!--                disabledDate (time) {-->
<!--                    return time.getTime() > Date.now()-->
<!--                },-->
<!--                shortcuts: [{-->
<!--                    text: '今天',-->
<!--                    onClick (picker) {-->
<!--                        picker.$emit('pick', new Date())-->
<!--                    }-->
<!--                }, {-->
<!--                    text: '昨天',-->
<!--                    onClick (picker) {-->
<!--                        const date = new Date()-->
<!--                        date.setTime(date.getTime() - 3600 * 1000 * 24)-->
<!--                        picker.$emit('pick', date)-->
<!--                    }-->
<!--                }, {-->
<!--                    text: '一周前',-->
<!--                    onClick (picker) {-->
<!--                        const date = new Date()-->
<!--                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)-->
<!--                        picker.$emit('pick', date)-->
<!--                    }-->
<!--                }]-->
<!--            },-->
<!--        }-->
<!--    },-->
<!--    created () { },-->
<!--    mounted () { },-->
<!--    methods: {-->
<!--        // 获取子级地区-->
<!--        getSubDistrict (code, layer) {-->
<!--            this.enterpriseForm.district = ''-->
<!--            if(layer === 1) {-->
<!--                this.enterpriseForm.city = ''-->
<!--            }-->
<!--            getCascaderOptions({ distCode: code }).then(res => {-->
<!--                if(layer === 1) {-->
<!--                    return this.addressOptions.city = res-->
<!--                }-->
<!--                this.addressOptions.district = res-->
<!--            })-->
<!--        },-->
<!--        // 获取验证码-->
<!--        getVerificationCode () {},-->
<!--        //时间验证-->
<!--        validateDate (rule, value, callback) {-->
<!--            if (value == null || value == '') {-->
<!--                return callback(new Error('请选择时间！'))-->
<!--            }-->
<!--            callback()-->
<!--        },-->
<!--        //密码验证-->
<!--        validatePassword (rule, value, callback) {-->
<!--            let len = value.trim().length-->
<!--            if (len < 6 || len > 14) {-->
<!--                return callback(new Error('请输入6到14位的密码！'))-->
<!--            }-->
<!--            if (!new RegExp(/^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9]{6,20})$/).test(value)) {-->
<!--                return callback(new Error('密码必须同时包含数字和密码！'))-->
<!--            }-->
<!--            callback()-->
<!--        },-->
<!--        //统一信用代码验证-->
<!--        validateSocialCreditCode (rule, value, callback) {-->
<!--            let len = value.trim().length-->
<!--            if (len != 18 && len != 15) {-->
<!--                return callback(new Error('请填写15位或18位统一信用代码！'))-->
<!--            }-->
<!--            callback()-->
<!--        },-->
<!--        // 判断上传的图片大小-->
<!--        handleBeforeUpload (file) {-->
<!--            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize-->
<!--            if (!sizeOk) {-->
<!--                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)-->
<!--            }-->
<!--            return sizeOk-->
<!--        },-->
<!--        handleUploadChange (file, list) {-->
<!--            console.log('list', list)-->
<!--            if (file.status == 'ready') {-->
<!--                this.uploadInProgress = true-->
<!--                this.uploadPercentage = 0-->
<!--                const interval = setInterval(() => {-->
<!--                    if (this.uploadPercentage >= 99) {-->
<!--                        clearInterval(interval)-->
<!--                        return-->
<!--                    }-->
<!--                    this.uploadPercentage += 1-->
<!--                }, 20)-->
<!--            }-->
<!--            if (file.status == 'success') {-->
<!--                this.uploadPercentage = 100-->
<!--                setTimeout(() => {-->
<!--                    this.uploadInProgress = false-->
<!--                }, 300)-->
<!--            }-->
<!--        },-->
<!--        async uploadLicenseEnterprise (params) {-->
<!--            console.log(params)-->
<!--            let file = params.file-->
<!--            const form = new FormData()-->
<!--            form.append('files', file)-->
<!--            form.append('bucketName', 'mall') //存储桶名称-->
<!--            form.append('directory', 'material') // 商城类型-->
<!--            form.append('isChangeObjectName', true) // 是否修改文件名称-->
<!--            form.append('isTemplate', false)  //是否是模板-->
<!--            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）-->
<!--            form.append('relationId', '990116') // 关联ID-->
<!--            uploadFile(form).then(res => {-->
<!--                this.enterpriseForm.businessLicense = res[0].objectPath-->
<!--                this.enterpriseForm.businessLicenseId = res[0].recordId-->
<!--                this.$message({-->
<!--                    message: '上传成功',-->
<!--                    type: 'success'-->
<!--                })-->
<!--            })-->
<!--        },-->
<!--        // 个体户上传营业执照-->
<!--        async uploadLicenseBusiness (params) {-->
<!--            let file = params.file-->
<!--            const form = new FormData()-->
<!--            form.append('files', file)-->
<!--            form.append('bucketName', 'mall') //存储桶名称-->
<!--            form.append('directory', 'material') // 商城类型-->
<!--            form.append('isChangeObjectName', true) // 是否修改文件名称-->
<!--            form.append('isTemplate', false)  //是否是模板-->
<!--            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）-->
<!--            form.append('relationId', '990116') // 关联ID-->
<!--            uploadFile(form).then(res => {-->
<!--                this.businessForm.businessLicense = res[0].objectPath-->
<!--                this.businessForm.businessLicenseId = res[0].recordId-->
<!--            })-->
<!--        },-->
<!--        validateAddress (rule, value, callback) {-->
<!--            if ( this.enterpriseForm.provinces == null || this.enterpriseForm.provinces == '' ) {-->
<!--                return callback(new Error('请选择省份！'))-->
<!--            }-->
<!--            if ( this.enterpriseForm.city == null || this.enterpriseForm.city == '' ) {-->
<!--                return callback(new Error('请选择市级！'))-->
<!--            }-->
<!--            if ( this.enterpriseForm.provinces == null || this.enterpriseForm.provinces == '' ) {-->
<!--                return callback(new Error('请选择县、区！'))-->
<!--            }-->
<!--            callback()-->
<!--        },-->
<!--        onSubmit () {-->
<!--            this.$refs['enterpriseForm'].validate(valid => {-->
<!--                if(valid) {-->
<!--                    // 校验成功代码-->
<!--                }-->
<!--            })-->
<!--        },-->
<!--    },-->
<!--}-->
<!--</script>-->
<!--<style scoped lang="scss">-->
<!--main>div {-->
<!--    height: 100%;-->
<!--    border: 1px solid rgba(230, 230, 230, 1);-->
<!--}-->

<!--.list-title {-->
<!--    height: 50px;-->
<!--    padding: 15px 19px 15px 21px;-->
<!--    font-size: 20px;-->
<!--    line-height: 20px;-->

<!--    &::before {-->
<!--        width: 3px;-->
<!--        height: 20px;-->
<!--        margin-right: 10px;-->
<!--        content: '';-->
<!--        display: block;-->
<!--        background-color: rgba(33, 110, 198, 1);-->
<!--    }-->
<!--}-->

<!--/deep/ .el-tabs {-->
<!--    height: 1284px;-->

<!--    .el-tabs__nav-scroll {-->
<!--        height: 60px;-->
<!--        background-color: #fafafa;-->

<!--        .el-tabs__nav {-->
<!--            border: none;-->

<!--            .el-tabs__active-bar {-->
<!--                height: 3px;-->
<!--                background-color: #226FC7;-->
<!--                top: 0;-->
<!--                z-index: 2;-->
<!--            }-->
<!--        }-->

<!--        .el-tabs__item.is-top {-->
<!--            width: 160px;-->
<!--            height: 60px;-->
<!--            margin: 0;-->
<!--            padding: 0;-->
<!--            font-size: 18px;-->
<!--            line-height: 60px;-->
<!--            text-align: center;-->
<!--            border: none;-->
<!--            position: relative;-->
<!--            z-index: 1;-->

<!--            &.is-active {-->
<!--                color: #333;-->
<!--                background-color: #fff;-->
<!--            }-->
<!--        }-->

<!--    }-->

<!--    .el-tab-pane {-->
<!--        padding-top: 30px 20px 0;-->

<!--        .el-form-item {-->
<!--            margin-bottom: 25px;-->

<!--            .el-form-item__label {-->
<!--                height: 100%;-->
<!--                padding-right: 10px;-->
<!--                line-height: 50px;-->
<!--                font-size: 16px;-->
<!--                color: #333;-->
<!--            }-->

<!--            .el-input__inner {-->
<!--                height: 50px;-->
<!--                font-size: 16px;-->
<!--                border-radius: 0;-->
<!--                border: 1px solid rgba(204, 204, 204, 1);-->
<!--            }-->
<!--        }-->

<!--        .el-checkbox {-->
<!--            display: flex;-->
<!--            align-items: center;-->

<!--            .el-checkbox__inner {-->
<!--                width: 16px;-->
<!--                height: 16px;-->
<!--                border: 2px solid rgba(33, 110, 198, 1);-->
<!--            }-->

<!--            .el-checkbox__label {-->
<!--                font-size: 16px;-->
<!--                color: #333;-->

<!--                span {-->
<!--                    color: #216EC6;-->
<!--                }-->
<!--            }-->
<!--        }-->
<!--    }-->
<!--}-->
<!--/deep/.avatar-uploader .el-upload {-->
<!--    width: 138px;-->
<!--    height: 138px;-->
<!--    border: 1px dashed rgba(217, 217, 217, 1);-->
<!--    cursor: pointer;-->
<!--    position: relative;-->
<!--    overflow: hidden;-->
<!--}-->
<!--/deep/ .enterpriseForm, .businessForm {-->
<!--    width: 1066px;-->
<!--    margin-top: 30px;-->
<!--    .el-col:not(.el-col-24) {-->
<!--        width: 48.4%;-->
<!--        .el-input, .el-input__inner {width: 350px;}-->
<!--        &.el-col-offset-1 {margin-left: 20px;}-->
<!--    }-->
<!--    .el-col-24 .el-input__inner .el-input {width: 893px;}-->
<!--    .el-form-item__error {-->
<!--        width: 80%;-->
<!--        margin-top: -10px;-->
<!--    }-->
<!--    .licenseUploader {-->
<!--        font-size: 40px;-->
<!--        color: #8c939d;-->
<!--        width: 138px;-->
<!--        height: 138px;-->
<!--        //margin-right: 20px;-->
<!--        line-height: 140px;-->
<!--        display: inline;-->
<!--        .el-form-item__error {-->
<!--            width: 500px;-->
<!--        }-->
<!--    }-->

<!--    .avatar {-->
<!--        width: 140px;-->
<!--        height: 140px;-->
<!--        display: block;-->
<!--    }-->

<!--    .uploadDemo {-->
<!--        width: 138px;-->
<!--        height: 138px;-->
<!--        padding: 5px;-->
<!--        margin-left: 40px;-->
<!--        font-size: 16px;-->
<!--        color: #999;-->
<!--        border: 1px dashed rgba(217, 217, 217, 1);-->
<!--        flex-direction: column;-->

<!--        img {-->
<!--            width: 130px;-->
<!--            height: 95px;-->
<!--        }-->

<!--        span {-->
<!--            margin-right: 5px;-->
<!--        }-->
<!--    }-->

<!--    .uploadTip {-->
<!--        font-size: 14px;-->
<!--        margin-top: 155px;-->
<!--        margin-bottom: 20px;-->
<!--        margin-left: -108px;-->
<!--        width: 380px;-->
<!--        color: #808080;-->
<!--    }-->

<!--    .el-select {-->
<!--        width: 100%;-->
<!--    }-->
<!--    .licenseValidTime {-->
<!--        .el-form-item__content {display: flex;}-->
<!--        .el-date-editor {-->
<!--            width: 260px;-->
<!--            margin-right: 32px;-->
<!--            .el-input__inner {width: 260px;}-->
<!--        }-->
<!--        .el-checkbox {height: 50px; margin-bottom: 0;}-->
<!--        .el-checkbox__inner {border: 1px solid rgba(217,217,217,1);}-->
<!--    }-->
<!--    .registerAddress {-->
<!--        .el-select {-->
<!--            width: 100px;-->
<!--            margin-right: 10px;-->
<!--            &:last-child{margin-right: 0;}-->
<!--            .el-input, .el-input__inner {width: 100px;}-->
<!--        }-->
<!--        .el-form-item__error {-->
<!--            margin-left: 0px;-->
<!--            //margin-top: -50px;-->
<!--        }-->
<!--    }-->
<!--    .separ {-->
<!--        width: 1066px;-->
<!--        height: 1px;-->
<!--        margin-left: 20px;-->
<!--        margin-bottom: 30px;-->
<!--        border-top: 1px dashed rgba(204, 204, 204, 1);-->
<!--    }-->

<!--    .subtitle {-->
<!--        margin-left: 60px;-->
<!--        margin-bottom: 30px;-->
<!--        font-size: 20px;-->
<!--        font-weight: 500;-->
<!--        color: #216EC6;-->
<!--    }-->

<!--    .identityUpload {-->
<!--        width: 160px;-->
<!--        height: 100px;-->
<!--    }-->
<!--    .verifyBox .el-button {-->
<!--        width: 140px;-->
<!--        height: 50px;-->
<!--        padding: 0;-->
<!--        font-size: 16px;-->
<!--        line-height: 50px;-->
<!--        color: #216EC6;-->
<!--        background: #FFFFFF;-->
<!--        border: 1px solid rgba(33, 110, 198, 1);-->
<!--        border-radius: 0;-->
<!--    }-->
<!--    .verifyBox {-->
<!--        .el-input, .el-input__inner{-->
<!--            width: 195px !important;-->
<!--            margin-right: 15px;-->
<!--        }-->
<!--    }-->

<!--    .el-checkbox {-->
<!--        margin-top: -3px;-->
<!--        margin-bottom: 50px;-->
<!--        .is-checked .el-checkbox__inner {background-color: #216ec6;}-->
<!--    }-->
<!--}-->
<!--.btns {-->
<!--    width: 350px;-->
<!--    margin-top: 25px;-->
<!--    button {-->
<!--        width: 160px;-->
<!--        height: 50px;-->
<!--        font-size: 22px;-->
<!--    }-->
<!--    button:first-child {-->
<!--        color: rgba(33,110,198,1);;-->
<!--        border: 1px solid rgba(33,110,198,1);-->
<!--        background-color: #fff;-->
<!--    }-->
<!--    button:last-child {-->
<!--        color: #fff;-->
<!--        background-color: rgba(33,110,198,1);-->
<!--    }-->
<!--}-->
<!--</style>-->