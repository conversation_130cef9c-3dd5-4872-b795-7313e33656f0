<template>
    <div>
        <main>
            <div class="center noticeBox">
                <div class="list-title">招标公告</div>
                <div class="list">
                    <el-table :data="tableData" :cell-style="{ height: '50px', fontSize: '14px' }" :header-cell-style="{ fontSize: '16px', backgroundColor: '#f8fbff' }">
                        <el-table-column prop="name" label="招标名称"/>
                        <el-table-column prop="company" label="招标单位" width="300"/>
                        <el-table-column prop="publishTime" label="发布时间" width="120"/>
                        <el-table-column width="80">
                            <template v-slot="scope">
                                <span class="pointer" @click="goToDetail(scope.row)">查看详情</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <pagination
                    :pageSize="pages.pageSize"
                    :total="pages.total"
                    :totalPage="Math.ceil(pages.total / pages.pageSize)"
                    :currentPage="pages.currentPage"
                    :destination="pages.destination"
                    @currentChange="currentChange"
                    @sizeChange="sizeChange"
                ></pagination>
            </div>
        </main>
        <publicity></publicity>
    </div>
</template>

<script>
import pagination from '../components/pagination.vue'
import publicity from '../components/publicity.vue'
export default {
    name: 'notice',
    components: { publicity, pagination },
    data () {
        return {
            // tableData: [
            //     {
            //         id: 1,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 2,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 3,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 3,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 3,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 3,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 3,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 3,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 3,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     },
            //     {
            //         id: 3,
            //         name: '高坪区全域旅游基础设施建设项目大数据采购',
            //         company: '四川路桥建设集团有限公司',
            //         publishTime: '2022-06-05',
            //     }
            // ],
            tableData: [],
            pages: {
                currentPage: 1,
                total: 30,
                pageSize: 10,
                totalPages: 1,
                destination: 1,
            }
        }
    },
    created () {

    },
    methods: {

        goToDetail () {},
        currentChange (page) {
            console.log(page)
        },
        sizeChange (size) {
            console.log(size)
        },
    }
}
</script>

<style scoped lang="scss">
main {
    padding: 30px 0;
    background-color: #f5f5f5;
}
.tableHeader {
    background-color: #f8fbff;
}
.noticeBox {
    width: 1326px;
    //height: 500px;
    background-color: #fff;
    .list-title {
        height: 50px;
        padding-left: 20px;
        line-height: 50px;
        font-size: 18px;
        color: #333;
    }
    .list {
        min-height: 540px;
        padding: 0 20px;
    }
}
</style>