import service from '@/utils/request'

const { httpPost, httpGet } = service

// 新增企业
const createShudaoEnterprise = params => {
    return httpPost({
        url: '/materialMall/shudaoEnterprise/create',
        params
    })
}
const inquireShudaoEnterprise = params => {
    return httpPost({
        url: '/materialMall/shudaoEnterprise/listByEntity',
        params
    })
}
const dele = params => {
    return httpGet({
        url: '/materialMall/shudaoEnterprise/delete',
        params
    })
}
const deleteBatch = params => {
    return httpPost({
        url: '/materialMall/shudaoEnterprise/deleteBatch',
        params
    })
}
// 导出蜀道企业数据
const supplyOutputExcel = params => {
    return httpPost({
        url: '/materialMall/shudaoEnterprise/excel/OutputExcel',
        params,
        responseType: 'blob'
    })
}
// 导入蜀道企业excel
const uploadExcelFile = params => {
    return httpPost({
        url: '/materialMall/shudaoEnterprise/excel/uploadSdExcelFile',
        params,
        responseType: 'blob'
    })
}
const feeInputData = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/excel/feeInputData',
        params,
        responseType: 'blob'
    })
}
// 下载导入模板
const excelTemplate = params => {
    return httpGet({
        url: '/materialMall/shudaoEnterprise/excel/sdTemplate',
        params,
        responseType: 'blob'
    })
}

const feeInputTemplate = params => {
    return httpGet({
        url: '/materialMall/platformYearFeeRecord/excel/feeInputTemplate',
        params,
        responseType: 'blob'
    })
}
// 导出excel
export {
    createShudaoEnterprise,
    inquireShudaoEnterprise,
    dele,
    deleteBatch,
    excelTemplate,
    feeInputData,
    feeInputTemplate,
    supplyOutputExcel,
    uploadExcelFile
}