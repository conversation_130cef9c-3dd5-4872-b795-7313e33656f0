<template>
    <div class="base-page" v-if="showSetOrg">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- 搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <!-- 搜索条件 -->
                        <el-select v-model="filterData.pictureType" clearable placeholder="图片链接类型">
                            <el-option v-for="item in picTypeFilter" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                        <el-select v-model="filterData.state" clearable placeholder="图片发布状态">
                            <el-option v-for="item in stateFilter" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                        <el-select v-model="filterData.useType" clearable placeholder="图片显示位置">
                            <el-option v-for="item in useTypeFiter" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                        <!-- 高级搜索 -->
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="handleFilter">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="e-table" :style="{ width: '100%' }">
                <el-table class="table" :height="rightTableHeight" :data="tableData" border :cell-class-name="cellClsNm" highlight-current-row
                    @current-change="handleCurrentChange">
                    <el-table-column label="操作" width="120">
                        <template slot-scope="scope">
                            <span class="action" @click="onDel(scope)">删除</span>
                        </template>
                    </el-table-column>
                    <!-- 图片 -->
                    <el-table-column label="图片" width="180" type="index">
                        <template slot-scope="scope">
                            <el-image style="width: 120px; height: 100px" :src="scope.row.pictureUrl"></el-image>
                        </template>
                    </el-table-column>
                    <!-- 图片链接类型 -->
                    <el-table-column label="图片链接类型" width="160">
                        <template slot-scope="scope">
                            <span v-if="scope.row.pictureType == '0'">无</span>
                            <span v-else-if="scope.row.pictureType == '1'">内部链接</span>
                            <span v-else-if="scope.row.pictureType == '2'">外部链接</span>
                        </template>
                    </el-table-column>
                    <!-- 图片面显示位置 -->
                    <el-table-column label="图片面显示位置" width="160">
                        <template slot-scope="scope">
                            <span v-if="scope.row.useType == '0'">homepage</span>
                            <span v-else-if="scope.row.useType == '1'">homepage</span>
                            <span v-else-if="scope.row.useType == '2'">homepage</span>
                        </template>
                    </el-table-column>
                    <!-- 图片发布状态 -->
                    <el-table-column label="图片发布状态" width="160">
                        <template slot-scope="scope">
                            {{ scope.row.state == 1 ? '已发布' : '未发布' }}
                        </template>
                    </el-table-column>
                    <!-- 图片链接地址 -->
                    <el-table-column label="图片链接地址" width="424" type="index">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.pictureLinkAddress }}
                            </span>
                        </template>
                    </el-table-column>
                    <!-- 图片排序值 -->
                    <el-table-column label="图片排序值" width="100" prop="orderValue" />
                    <!-- 图片修改时间 -->
                    <el-table-column label="图片修改时间" width="140" prop="gmtModified" />
                    <!-- 图片添加时间 -->
                    <el-table-column label="图片添加时间" width="140" prop="gmtCreate" />
                </el-table>
            </div>
            <!-- <pagination
                :total="100"
                :pageSize.sync="10"
                :currentPage.sync="1"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            /> -->
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- 分类 -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">{{ dialogTitle }}</div>
                <el-form :rules="formRules" ref="form" :model="formClass" label-width="150px">
                    <!-- <el-row>
                        <el-col :span="12" v-if="!isAddTop && formClass.parentClassId !== '0' && formClass.parentClassId !== ''">
                            <el-form-item label="上级类别名称" prop="parentClassName">
                                {{ formClass.parentClassName }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="formClass.billId">
                            <el-form-item label="分类编号" prop="billNo">
                                {{ formClass.billNo }}
                            </el-form-item>
                        </el-col>
                    </el-row> -->
                    <el-row>
                        <el-col :span="12">
                            <el-form-item class="uploader" label="图片地址：" prop="pictureUrl">
                                <el-upload class="avatar-uploader" action="http://api_devss.wanxikeji.cn/api/savePic" :show-file-list="false"
                                    :on-success="handleUploadSuccess" name="img">
                                    <img v-if="imageUrl" :src="imageUrl" class="avatar">
                                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="图片链接类型：">
                                <el-select v-model="formData.pictureType" placeholder="图片链接类型">
                                    <el-option v-for="item in picTypeFilter" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="图片显示位置：">
                                <el-select v-model="formData.useType" placeholder="图片显示位置">
                                    <el-option v-for="item in useTypeFiter" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="图片链接地址：" prop="pictureLinkAddress">
                                <el-input v-model="formData.pictureLinkAddress" :disabled="true" placeholder="填写图片链接地址">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="图片使用状态：">
                                <el-select v-model="formData.state" placeholder="是否启用">
                                    <el-option v-for="item in levelForm" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="图片备注信息：">
                                <el-input type="textarea" v-model="formData.remark" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button type="primary" size="mini" @click="onSave()">保存</el-button>
                    <el-button size="mini" @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios'
import '@/utils/jquery.scrollTo.min'
// import SelectMaterialClass from '../../../components/selectMaterialClass'
// import pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
import { getMaterialLs, getMaterialById, getMaterialDetail, addMaterial, modifyMaterial, delMaterial, addMaterialClass, modifyMaterialClass, delMaterialClass, saveOrUpdatePermission, getOrgClassId, getChildrenOrg } from '@/api/base/material'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapState, mapActions, mapMutations } from 'vuex'
// import { getOrgName } from '@/api/materials/materialsMarketPrice'
export default {
    components: {
        // SelectMaterialClass,
        // pagination
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) {
                return this.screenHeight - 170
            }
            return this.screenHeight - 214
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            dialogTitle: '新增图片',
            classType: '0',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            isAddTop: true, // 新增顶级
            showSetOrg: true, //设置使用机构
            searchKey: '',
            treeData: [],
            currentClass: null,
            currentRow: null,
            currentPic: {},
            picTypeFilter: [
                //{ value: 0, label: '' },
                { value: 1, label: '无链接' },
                { value: 2, label: '内部链接' },
                { value: 3, label: '外部链接' },
            ],
            stateFilter: [
                // { value: 0, label: '全部' },
                { value: 1, label: '发布' },
                { value: 2, label: '未发布' },
            ],
            useTypeFiter: [
                // { value: 0, label: '全部' },
                { value: 1, label: 'two' },
                { value: 2, label: 'three' },
            ],
            filterData: {
                pictureType: null,
                state: null,
                useType: null,
                limit: 10,
                page: 1,
            },
            formRules: {
                className: [
                    { required: true, message: '请输入分类名称', trigger: 'blur' },
                ]
            },
            levelForm: [
                { value: 1, label: '使用中' },
                { value: 2, label: '未使用' }
            ],
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            classData: {},
            tableData: [
                {
                    'orderValue': 0, // 图片排序用
                    'pictureId': '',
                    'pictureLinkAddress': 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg', // 图片点击跳转的地址
                    'pictureType': '1',
                    'pictureUrl': 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg', // 服务器图片地址
                    'remark': '测试', // 备注
                    'state': 1, // 发布状态 1使用中 2未使用
                    'useType': 0 // 图片展示位置
                },
                {
                    'orderValue': 0, // 图片排序用
                    'pictureId': '',
                    'pictureLinkAddress': 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg', // 图片点击跳转的地址
                    'pictureType': '1',
                    'pictureUrl': 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg', // 服务器图片地址
                    'remark': '测试', // 备注
                    'state': 1, // 发布状态 1使用中 2未使用
                    'useType': 0 // 图片展示位置
                },
                {
                    'orderValue': 0, // 图片排序用
                    'pictureId': '',
                    'pictureLinkAddress': 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg', // 图片点击跳转的地址
                    'pictureType': '1',
                    'pictureUrl': 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg', // 服务器图片地址
                    'remark': '测试', // 备注
                    'state': 1, // 发布状态 1使用中 2未使用
                    'useType': 0 // 图片展示位置
                }
            ],
            orgDataTable: [],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            allData: [],
            mapObj: null,
            formClass: {
                'billId': '',
                'billNo': '',
                'className': '',
                'estimatedMaterialLock': 0,
                'founderId': '',
                'founderName': '',
                'gmtCreate': '',
                'gmtModified': '',
                'isCentralizedPurchasing': 0,
                'isDetailed': 0,
                'isEnable': 0,
                'levelPathId': '',
                'levelPathName': '',
                'materialType': 0,
                'parentClassId': '',
                'parentClassName': '',
                'unit': ''
            },
            formData: {
                'orderValue': 0, // 图片排序用
                'pictureId': '',
                'pictureLinkAddress': '', // 图片点击跳转的地址
                'pictureType': '1',
                'pictureUrl': '', // 服务器图片地址
                'remark': '测试', // 备注
                'state': 1, // 发布状态 1使用中 2未使用
                'useType': 0 // 图片展示位置
            },
            imageUrl: '',
            currencyOptions: [],
            tabsName: 'baseInfo',
            tableData2: [],
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            idsTop: [],
            checkAllDetail: false,
            isIndeterminate: false,
            arr: [],
            Array: [],
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        let data = JSON.parse(localStorage.getItem('vuex'))
        this.orgId = data.userInfo.orgInfo.orgId
        this.getTableData()
    },
    methods: {
        ...mapActions('equip', ['setUnitMeasur']),
        addSave () { },
        // 上传图片
        // eslint-disable-next-line
        handleUploadSuccess (res, file) {
            this.imageUrl = /*前缀 + */res.data
            this.formData.pictureUrl = /*前缀 + */res.data
            console.log(this.formData)
        },
        handlePicRemove () { },
        handleClose () { },
        handleFilter () {
            axios({
                method: 'post',
                url: '/productCategoryTest/adPicture/findByConditionByPage',
                params: this.filterData
            }).then(res => {
                this.tableData = res.data.data
            })
        },
        Save () {
            this.showSetOrg = true
        },
        Close () {
            this.showSetOrg = true
        },
        // 提交图片
        handleSubmit () {
            axios({
                method: ''
            })
        },
        handleView (scope) {
            this.viewList = 'class'
            this.dialogTitle = '查看图片'
            this.formData = JSON.parse(JSON.stringify(scope.row))
        },
        handleEdit (scope) {
            this.viewList = 'class'
            this.dialogTitle = '编辑图片'
            this.formData = JSON.parse(JSON.stringify(scope.row))
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.dialogTitle = '新增图片'
        },
        // 复选框选中事件
        selectFun (selection, row) {
            console.log('selection>>>', selection)
            this.setRowIsSelect(row)

        },
        // 复选框点击事件
        setRowIsSelect (row) {
            if (row.isCheck) {
                this.Array.push(row)
                this.Array.forEach(item => {
                    if (item.children) {
                        item.children.forEach(val => {
                            this.Array.push(val)
                        })
                    }
                })
            } else {
                const index = this.Array.findIndex(x => x.orgId === row.orgId)
                if (index !== -1) {
                    this.Array.splice(index, 1)
                }
            }
            //当点击父级点复选框时，当前的状态可能为未知状态，所以当前行状态设为false并选中，即可实现子级点全选效果
            // if (row.isCheck === null) {
            //     row.isCheck = false
            //     this.$refs.multipleTable.toggleRowSelection(row, true)
            // }
            let that = this
            // 选中父级时候，选中父级及全部子集
            function selectAllChildrens (data) {
                treeToList(data, item => {
                    item.isCheck = row.isCheck
                })
                // console.log('arr', arr)
                // arr.forEach(item=>{
                //     item.isCheck = row.isCheck
                // })
                // data.forEach(item => {
                //     item.isCheck = row.isCheck
                //     // that.$refs.multipleTable.toggleRowSelection(
                //     //     item,
                //     //     row.isCheck
                //     // )
                //     if (item.children && item.children.length) {
                //         selectAllChildrens(item.children)
                //     }
                // })
            }
            // 判断子集是否全部选中
            function getSelectStatus (selectStatuaArr, data) {
                data.forEach(childrenItem => {
                    selectStatuaArr.push(childrenItem.isCheck)
                    if (childrenItem.children && childrenItem.children.length) {
                        getSelectStatus(selectStatuaArr, childrenItem.children)
                    }
                })
                return selectStatuaArr
            }
            // 判断点击的是哪个节点
            function getLevelStatus (row) {
                //如果当前节点的parantId =0 并且有子节点，则为1
                //如果当前节点的parantId !=0 并且子节点没有子节点 则为3
                if (row.parentId == 0) {
                    if (row.children && row.children.length) {
                        return 1
                    } else {
                        return 4
                    }
                } else {
                    if (!row.children || !row.children.length) {
                        return 3
                    } else {
                        return 2
                    }
                }
            }
            let result = {}
            //获取明确的节点
            function getExplicitNode (data, parentId) {
                data.forEach(item => {
                    if (item.dtlId == parentId) {
                        result = item
                    }
                    if (item.children && item.children.length) {
                        getExplicitNode(item.children, parentId)
                    }
                })
                return result
            }
            // 选中的子集，来操作父节点
            function operateLastLeve (row) {
                //操作的是子节点  1、获取父节点  2、判断子节点选中个数，如果全部选中则父节点设为选中状态，如果都不选中，则为不选中状态，如果部分选择，则设为不明确状态
                let selectStatuaArr = []
                let item = getExplicitNode(
                    that.orgDataTable,
                    row.parentId
                )
                selectStatuaArr = getSelectStatus(
                    selectStatuaArr,
                    item.children
                )
                if (
                    // 全选
                    selectStatuaArr.every(selectItem => {
                        return true == selectItem
                    })
                ) {
                    item.isCheck = true
                    that.$refs.multipleTable.toggleRowSelection(item, true)
                } else if (
                    // 全不选
                    selectStatuaArr.every(selectItem => {
                        return false == selectItem
                    })
                ) {
                    item.isCheck = false
                    that.$refs.multipleTable.toggleRowSelection(item, false)
                } else {
                    item.isCheck = null
                }
                //则还有父级
                if (item.parentId != 0) {
                    operateLastLeve(item)
                }
            }
            //判断操作的是子级点复选框还是父级点复选框，如果是父级点，则控制子级点的全选和不全选

            //1、只是父级 2、既是子集，又是父级 3、只是子级
            let levelSataus = getLevelStatus(row)
            if (levelSataus == 1) {
                selectAllChildrens(row.children)
            } else if (levelSataus == 2) {
                selectAllChildrens(row.children)
                operateLastLeve(row)
            } else if (levelSataus == 3) {
                operateLastLeve(row)
            }
        },
        // 检测表格数据是否全选
        checkIsAllSelect () {
            this.oneProductIsSelect = []
            this.orgDataTable.forEach(item => {
                this.oneProductIsSelect.push(item.isCheck)
            })
            //判断一级产品是否是全选.如果一级产品全为true，则设置为取消全选，否则全选
            let isAllSelect = this.oneProductIsSelect.every(
                selectStatusItem => {
                    return true == selectStatusItem
                }
            )
            return isAllSelect
        },
        // 表格全选事件
        selectAllFun (selection) {
            let isAllSelect = this.checkIsAllSelect()
            this.orgDataTable.forEach(item => {
                item.isCheck = !isAllSelect
                this.selectFun(selection, item)
            })
        },
        //表格标题样式 当一级目录有为不明确状态时，添加样式，使其全选复选框为不明确状态样式
        headerRowClassName () {
            let oneProductIsSelect = []
            this.orgDataTable.forEach(item => {
                oneProductIsSelect.push(item.isCheck)
            })
            if (oneProductIsSelect.includes('')) {
                return 'indeterminates'
            }
            return ''
        },

        // //表格样式当当前行的状态为不明确状态时，添加样式，使其复选框为不明确状态样式
        rowClassNameFun ({ row }) {
            if (row.isCheck === null) {
                return 'indeterminates'
            }
        },
        ...mapMutations(['setSelectedInfo']),
        //显示设置机构页面
        setOrg () {
            showLoading()
            this.showSetOrg = false
            this.getOrgData()

            //先获取权限机构  如果有就回显
            if (this.formClass.billId == '') {
                console.log('第一次不回显')
            } else {
                let classId = this.formClass.billId
                getOrgClassId({ classId }).then(res => {
                    console.log('回显', res)
                    this.orgDataTable.forEach(item => {
                        if (!item.children) {
                            item.children = []
                        }
                        item.isCheck = true
                    })

                })
            }

        },
        //懒加载树形机构数据
        async getOrgData () {
            let orgId = this.orgId
            const data = await getChildrenOrg({ orgId })
            this.orgDataTable = parseList(data, 'orgId', 'parentId', '0')
            hideLoading()
        },

        //关闭设置机构页面
        setOrgClose () {
            this.showSetOrg = true
        },
        async getTableData () {
            axios({
                method: 'get',
                url: '/productCategoryTest/adPicture/findByCondition',
                params: { pictureType: '1', state: 0, useType: 0 }
            }).then(res => {
                console.log(res)
                if (res.data.code === 200) {
                    console.log(2)
                    this.tableData = res.data.data
                } else {
                    this.$message({
                        message: '获取图片列表失败，请重试！',
                        type: 'error',
                    })
                }
            })
            this.viewList = true
            // let data
            // if (params) {
            //     if (typeof params === 'object') {
            //         data = params.data
            //         if (params.mapObj) {
            //             this.mapObj = params.mapObj
            //         }
            //         console.log(data, this.mapObj)
            //     } else {
            //         data = params
            //     }
            //     this.currentClass = data
            // } else {
            //     data = this.currentClass
            // }

            // showLoading()
            // try {
            //     const resData = await getMaterialLs({
            //         classId: data ? data.billId : '0',
            //         materialName: this.searchKey,
            //         limit: this.paginationInfo.pageSize,
            //         page: this.paginationInfo.currentPage
            //     })
            //     // this.tableData = resData.list
            //     this.allData = JSON.parse(JSON.stringify(resData.list))
            //     this.paginationInfo.total = resData.totalCount
            //     this.currentRow = null
            //     hideLoading()
            // } catch (error) {
            //     hideLoading()
            // }
        },
        async queryTableData () {
            const resData = this.allData.filter(item => {
                return item.materialName.includes(this.searchKey)
            })
            this.tableData = resData
            this.paginationInfo.total = 0
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        onSearch (e) {
            let searchKey = e
            if (typeof e === 'object') {
                searchKey = this.searchKey
            }
            console.log('searchKey: ', searchKey)
            this.queryTableData()
        },
        onAdvanceSearch () {
            advanceSearch().then(res => {
                alert(JSON.stringify(res))
            })
        },
        goList () {
            this.viewList = true
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 添加顶级
        onAddTopLevel () {
            this.viewList = 'class'
            this.isAddTop = true
            this.emptyForm()
            this.formData.classLevel = 0
            this.formClass.isDetailed = 0
        },
        onAddLevel () {
            this.viewList = 'class'
            this.isAddTop = false
            // const levelPath = this.getLevelPath()
            let id = this.formData.classId
            console.log(id)
            console.log(this.formData.parentId)
            this.formClass.isLeaf = true
            // this.formClass.levelPathId = levelPath.id.join('/')
            // this.formClass.levelPathName = levelPath.name.join('/')
            this.formClass.parentClassId = this.currentClass.billId
            this.formClass.parentClassName = this.currentClass.className
        },
        // 新增物资明细
        onAddMaterial () {
            this.viewList = 'material'
            this.currentRow = null
            const levelPath = this.getLevelPath()
            for (let key in this.formData) {
                this.formData[key] = ''
            }
            this.formData.classId = this.currentClass.billId
            this.formData.className = this.currentClass.className
            this.formData.classIdPath = levelPath.id.join('/')
            this.formData.classNamePath = levelPath.name.join('/')
        },
        // 编辑物资明细
        async onEditMaterial (row) {
            this.currentRow = row
            this.viewList = 'material'
            const data = await getMaterialDetail({
                id: row.billId
            })
            this.formData = data
            console.log(row, data)
        },
        // 添加子级
        onAddMaterialClass (data) {
            // 设置 currentClass
            this.getMapObj(data)
            this.onAddLevel()
        },
        // 修改物资类别
        async onModifyMaterialClass (data) {
            const detail = await getMaterialById({
                id: data.billId
            })
            this.viewList = 'class'
            this.isAddTop = false
            this.formClass = detail
        },
        // 删除物资类别
        onDelMaterialClass (data) {
            let arr = [data.classId]
            console.log(arr)
            this.viewList = true
            this.currentClass = data
            this.onDel(data)
        },
        // 保存
        onSave () {
            let url = '/productCategoryTest/adPicture/create'
            let data = {}
            if (this.dialogTitle === '编辑图片') {
                url = '/productCategoryTest/adPicture/update'
                data = this.formData
            }
            axios({
                method: 'post',
                url,
                params: data
            }).then(res => {
                console.log(res.data)
                this.viewList = true
                this.getTableData()
            })
            // this.emptyForm()

            // if (this.viewList === 'class') {
            //     this.saveMaterialClass()
            // } else {
            //     this.saveMaterial()
            // }
        },
        async saveMaterialClass () {
            try {
                let str
                if (!this.formClass.billId) {
                    this.formClass.isDetailed = 1
                    const billId = await addMaterialClass(this.formClass)

                    if (this.isAddTop) {
                        this.formClass.parentClassId = '0'
                    }
                    this.formClass.billId = billId
                    str = 'add'
                    this.Array.forEach(item => {
                        const obj = {
                            'orgId': item.orgId,
                            'orgName': item.orgName,
                        }
                        this.arr.push(obj)
                    })
                    //保存或更新权限机构信息
                    saveOrUpdatePermission({
                        'classId': this.formClass.billId,
                        'className': this.formClass.className,
                        'orgList': this.arr
                    })

                } else {
                    await modifyMaterialClass(this.formClass)
                    str = 'modify'
                }
                hideLoading()
                this.clientPop('suc', '保存成功', () => {
                    // 刷新树
                    this.refreshTree(str)
                    this.getTableData()
                })
            } catch (err) {
                console.log(err.message)
                hideLoading()
            }
        },
        async saveMaterial () {
            try {
                if (!this.currentRow) {
                    await addMaterial(this.formData)
                } else {
                    await modifyMaterial(this.formData)
                }
                hideLoading()
                this.clientPop('suc', '保存成功', () => {
                    this.getTableData()
                })
            } catch (error) {
                hideLoading()
            }
        },
        onDel (scope) {
            this.clientPop('info', `${this.viewList === 'class' ? '您确定要删除该图片吗？' : '您确定要删除该物资明细吗？'}数据删除后不可恢复，请谨慎操作！`, async () => {
                showLoading()
                try {
                    let id
                    axios({
                        method: 'get',
                        url: '/productCategoryTest/adPicture/delete',
                        params: { id: scope.row.pictureId }
                    }).then(res => {
                        console.log(res)
                        this.getTableData()
                    })
                    id = this.currentClass.billId
                    await delMaterialClass({
                        id
                    })
                    hideLoading()
                    this.clientPop('suc', '删除成功', () => {
                        // 刷新树
                        if (this.viewList === 'class' || (this.viewList !== 'class' && !this.currentRow)) {
                            this.refreshTree('del')
                        }
                        this.getTableData()
                    })
                } catch (err) {
                    console.log(err.message)
                    hideLoading()
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        getLevelPath (id = [], name = [], curClass) {
            if (!curClass) {
                curClass = this.currentClass
            }
            id.unshift(curClass.billId)
            name.unshift(curClass.className)
            if (curClass.parentClassId !== '0' && curClass.parentClassId !== '') {
                return this.getLevelPath(id, name, this.mapObj[curClass.parentClassId])
            } else {
                return {
                    id,
                    name
                }
            }
        },
        getMapObj (data) {
            for (let key in this.formData) {
                this.formData[key] = null
            }
            this.formData.parentId = data.classId
            this.formData.classLevel = data.classLevel + 1
            if (!this.mapObj) {
                this.mapObj = this.$refs.materialClassRef.mapObj
            }
            if (!this.mapObj[data.billId]) {
                this.mapObj[data.billId] = data
            }
            this.currentClass = data
        },
        // 刷新树
        refreshTree (str) {
            const tree = this.$refs.materialClassRef
            // 点击编辑类别时的按钮
            if (this.viewList === 'class') {
                if (str === 'add') {
                    if (this.isAddTop) {
                        tree.refreshTreeAddTop(this.formClass)
                    } else {
                        tree.refreshTree(this.formClass, this.currentClass)
                    }
                } else if (str === 'modify') {
                    tree.refreshTreeModify(this.formClass, this.currentClass)
                } else if (str === 'del') {
                    tree.refreshTreeDel(this.formClass)
                }
            } else {
                // 直接点击树上的图标删除
                tree.refreshTreeDel(this.currentClass)
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/.el-form-item.uploader {
    height: 200px;
}

/deep/ .el-table__header-wrapper {
    .el-checkbox {
        display: none;
    }
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

.e-form {padding: 0 20px;}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload
.avatar-uploader {
    /deep/.el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
