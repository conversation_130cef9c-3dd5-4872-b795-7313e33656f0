import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getCategoryColumns = params => {
    return httpGet({
        url: '/materialMall/w/categoryColumns/listBySize',
        params,
    })
}

const getFixedFloor = params => {
    return httpGet({
        url: '/materialMall/w/categoryColumns/fixedListBySize',
        params
    })
}

const getRevolvingMaterials = params => {
    return httpGet({
        url: '/materialMall/w/categoryColumns/revolvingMaterials',
        params
    })
}

export {
    getCategoryColumns,
    getFixedFloor,
    getRevolvingMaterials
}