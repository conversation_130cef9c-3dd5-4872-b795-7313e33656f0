/*
const filters = {
    state: (
        value,
        that // 状态
    ) => {
        if (value === null || value === undefined || value === '') {
            return ''
        }
        const state = new Map(Object.entries(that.options.state))
        const arr = []
        state.forEach((item, index) => {
            arr[index] = item
        })
        return arr.find((item, index) => {
            if (value === index) {
                return item
            }
        })
    },
    tenderClass: (
        value,
        that // 招标类别{}
    ) => {
        if (value === null || value === undefined || value === '') {
            return ''
        }
        return that.options.tenderClass.find((item, index) => {
            return value === index + 1
        }).label
    },
    tenderForm: (
        value,
        that // 招标方式
    ) => {
        if (value === null || value === undefined || value === '') {
            return ''
        }
        console.log('that.options.tenderForm', that.options.tenderForm)
        return that.options.tenderForm.find(item => {
            return item.value === String(value)
        })?.label
    },
    releaseState: (
        value,
        that // 发布状态
    ) =>
        that.options.releaseState.find((item, index) => {
            return value === index
        }),
    tenderState: (
        value,
        that // 招标状态
    ) =>
        that.options.tenderState.find((item, index) => {
            return value === index
        }),
    budgetName: () => {
        '暂无接口'
    },
    currencyId: (value, that) => {
        // 币种
        if (value === null || value === undefined || value === '') {
            return ''
        }
        return that.options.currency.find(item => {
            return value === item.kvId
        }).kvKey
    },
    // serviceType: (value, that) => {
    //     // 服务类型
    //     if (value === null || value === undefined || value === '') {
    //         return ''
    //     }
    //     that.options.serviceType.find((item, index) => {
    //         return value === index
    //     })
    // },
    leaseType: (
        value,
        that // 租赁类型
    ) => {
        if (value === null || value === undefined || value === '') {
            return ''
        }
        return that.options.leaseType.find((item, index) => {
            return value === index + 1
        })
    },
    tenderType: (
        value,
        that // 招标类型
    ) => {
        if (value === null || value === undefined || value === '') {
            return ''
        }
        console.log('value', value)
        return that.options.tenderType.find(item => {
            return value === item.key
        })?.value
    },
    serviceType: (
        value,
        that // 服务类型
    ) => {
        if (value === null || value === undefined || value === '') {
            return ''
        }
        return that.options.serviceType2.find((item, index) => {
            return value === index + 1
        })
    },
    serviceType3: (
        value,
        that // 收入类型
    ) => {
        if (value === null || value === undefined || value === '') {
            return ''
        }
        return that.options.serviceType3.find((item, index) => {
            return value === index + 1
        })
    }
    // leaseType: (
    //     value,
    //     that // 收入类型
    // ) => {
    //     if (value === null || value === undefined || value === '') {
    //         return ''
    //     }
    //     return that.options.serviceType3.find((item, index) => {
    //         return value === index + 1
    //     })
    // }
}

export default filters
*/
