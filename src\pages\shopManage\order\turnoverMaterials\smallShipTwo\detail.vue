<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="订单信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="订单商品" name="productInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="拆分订单" name="twoOrderList" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">订单信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单号：">
                                            <span>{{ formData.orderSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商名称：">
                                            <span>{{ formData.supplierName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="店铺名称：">
                                            <span>{{ formData.shopName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="项目部名称：">
                                            <span>{{ formData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="商品名称：">
                                            <span>{{ formData.untitled }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="订单类型：">
                                            <el-tag v-if="formData.orderClass ===1">普通订单</el-tag>
                                            <el-tag v-if="formData.orderClass === 2">多供方订单</el-tag>
                                            <el-tag v-if="formData.orderClass === 3">二级子订单</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="清单类型：">
                                            <el-tag v-if="formData.billType ===1">浮动价格</el-tag>
                                            <el-tag v-if="formData.billType === 2">固定价格</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收件人：">
                                            <span>{{ formData.receiverName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="收件人手机号：">
                                            <span>{{ formData.receiverMobile }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收件地址：">
                                            <span>{{ formData.receiverAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单状态：">
                                            <el-tag v-if="formData.state == 0">草稿</el-tag>
                                            <el-tag v-if="formData.state == 1">已提交</el-tag>
                                            <el-tag v-if="formData.state == 2">待确认</el-tag>
                                            <el-tag v-if="formData.state == 3">已确认</el-tag>
                                            <el-tag v-if="formData.state == 4">待签订合</el-tag>
                                            <el-tag v-if="formData.state == 5">已签合同</el-tag>
                                            <el-tag v-if="formData.state == 6">待发货</el-tag>
                                            <el-tag v-if="formData.state == 7">已关闭</el-tag>
                                            <el-tag v-if="formData.state == 8">发货中</el-tag>
                                            <el-tag v-if="formData.state == 9">待收货</el-tag>
                                            <el-tag type="success" v-if="formData.state == 10">已完成</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="商品类型：">
                                            <el-tag v-if="formData.productType == 12">大宗材料</el-tag>
                                            <el-tag v-if="formData.productType == 13">大宗临购</el-tag>
                                            <el-tag v-if="formData.productType == 14">周转材料</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="是否开发票：">
                                            <el-tag v-if="formData.orderBillState === 0">初始</el-tag>
                                            <el-tag v-if="formData.orderBillState === 1">已申请</el-tag>
                                            <el-tag v-if="formData.orderBillState === 2" type="success">已开票</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="结算类型：">
                                            <el-tag v-if="formData.payWay === 1">线上支付</el-tag>
                                            <el-tag v-if="formData.payWay === 2">内部结算</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>

                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单完成时间：">
                                            <span>{{ formData.successDate }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="发货时间：">
                                            <span>{{ formData.deliveryTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="计划编号：">
                                            <span>{{ formData.billNo }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="总金额（含税）：">
                                            <span>{{ formData.actualAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="订单备注：">
                                            <span>{{ formData.orderRemark }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 订单商品-->
                    <div id="productInfo" class="con" v-loading="tableLoading">
                        <div class="tabs-title" id="contractList">订单商品</div>
                        <div class="e-table"  style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-input type="text" @blur="getTableData" placeholder="输入搜索关键字" v-model="keywords">
                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData" />
                                    </el-input>
                                    <div class="search_box" style="margin-left: 20px">
                                        <el-button  type="primary" class="" @click = "selectSupplierM">选择供方</el-button>
<!--                                        <el-button  v-if="formData.state == 6" type="primary" class="" @click = "updateOrderCostPriceM">修改供方综合单价</el-button>-->
                                        <el-button v-if="userInfo.isBusiness != null && userInfo.isBusiness == 1 && formData.state == 6" type="primary" class="" @click = "createBidingClick">生成竞价单</el-button>
<!--                                        <el-button v-if="userInfo.isBusiness != null && userInfo.isBusiness == 1 && formData.state == 6 && showDevFunc" type="primary" class="" @click = "updateToBidingStateClick">设置为待竞价</el-button>-->
<!--                                        <el-button v-if="userInfo.isBusiness != null && userInfo.isBusiness == 1 && formData.state == 6 && showDevFunc"  type="primary" class="btn-delete" @click = "clearBidingClick">取消待竞价</el-button>-->
                                    </div>
                                </div>
                            </div>
                            <el-table ref="masterOrderItemRef"
                                      border
                                      @selection-change="masterOrderItemSelectRow"
                                      style="width: 100%"
                                      :data="tableData"
                                      class="table"
                                      @row-click="masterOrderItemRowClick"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column type="selection" width="40"></el-table-column>
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="state" label="订单明细状态" width="120">
                                    <template v-slot="scope">
                                        <el-tag v-if="scope.row.state == 2" type="info">待分配</el-tag>
                                        <el-tag v-if="scope.row.state == 1">已选择供方</el-tag>
                                        <el-tag v-if="scope.row.state == 3" type="info">待分配竞价</el-tag>
                                        <el-tag v-if="scope.row.state == 4">已生成竞价</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="productSn" label="商品编号" width="230px"></el-table-column>
                                <el-table-column prop="productName" label="商品名称" width="200px"></el-table-column>
                                <el-table-column prop="relevanceName" label="物资名称" width="200px"></el-table-column>
                                <el-table-column prop="productImg" label="商品图片" width="130">
                                    <template slot-scope="scope">
                                        <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="skuName" label="规格" width="200px"></el-table-column>
                                <el-table-column prop="texture" label="材质" width=""></el-table-column>
                                <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                                <el-table-column prop="costPrice" label="供方单价" width="200px">
                                    <!--<template v-slot="scope">-->
                                    <!--    <el-input-number v-model="scope.row.costPrice"-->
                                    <!--                     size="mini"-->
                                    <!--                     :min="0"-->
                                    <!--                     :precision="2" :step="1"-->
                                    <!--    >-->
                                    <!--    </el-input-number>-->
                                    <!--</template>-->
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 1" prop="netPrice" label="网价" width="100">
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 1" prop="fixationPrice" label="固定费" width="100">
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 2" prop="outFactoryPrice" label="出厂价" width="100">
                                </el-table-column>
                                <el-table-column v-if="formData.billType === 2" prop="transportPrice" label="运杂费" width="100">
                                </el-table-column>
                                <el-table-column prop="buyCounts" label="购买数量" width=""></el-table-column>
                                <el-table-column  prop="shipCounts" label="已发货数量" width="160px"></el-table-column>
                                <el-table-column  prop="confirmCounts" label="确认收货数量" width="160px"></el-table-column>
                                <el-table-column  prop="returnCounts" label="商城退货数量" width="100"></el-table-column>
                                <el-table-column  prop="pcwpReturn" label="pcwp退货数量" width="100"></el-table-column>
                                <el-table-column prop="productPrice" label="含税单价" width="200px"></el-table-column>
                                <el-table-column prop="totalAmount" label="含税金额" width="200px"></el-table-column>
                                <el-table-column prop="noRateAmount" label="不含税金额" width="200px"></el-table-column>
                                <el-table-column prop="isComment" label="评论状态" width="">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.isComment === 0">未评价</el-tag>
                                        <el-tag v-if="scope.row.isComment === 1" type="success">已评价</el-tag>
                                    </template>
                                </el-table-column>
<!--                                <el-table-column prop="returnCounts" label="退货数量" width=""></el-table-column>-->
                                <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                            </el-table>
                        </div>
                        <!--            分页-->
                        <Pagination
                            v-show="tableData != null || tableData.length != 0"
                            :total="paginationInfo.total"
                            :pageSize.sync="paginationInfo.pageSize"
                            :currentPage.sync="paginationInfo.currentPage"
                            @currentChange="getTableData"
                            @sizeChange="getTableData"
                        />
                    </div>
                    <div id="twoOrderList" class="con" v-loading="tableLoading2">
                        <div class="tabs-title" id="contractList">拆分订单</div>
                        <div class="e-table"  style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-input type="text" @blur="getTableData2" placeholder="输入搜索关键字" v-model="keywords2">
                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData2"  alt=""/>
                                    </el-input>
                                </div>
                            </div>
                            <el-table ref="tableRef"
                                      border
                                      style="width: 100%"
                                      :data="tableData2"
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="" label="操作" width="200">
                                    <template v-slot="scope">
                                        <template v-if="scope.row.supplierId">
                                            <span class="pointer ml10" v-if="scope.row.masterAffirmState == 0" style="color: #216ec6;" @click="affirmTwoOrderM(scope.row.orderId)">确认</span>
                                            <span style="margin: 0 5px;" v-if="scope.row.masterAffirmState == 0 && scope.row.affirmState != 1">|</span>
                                            <!--                                        <span class="pointer ml10" v-if="scope.row.supplierId == null" style="color: #216ec6;" @click="addTwoOrderSupplier(scope.row.orderId)">选择供应商</span>-->
                                            <span class="pointer ml10" v-if="scope.row.affirmState != 1" style="color: #e53e30;" @click="updateTwoOrderSupplier(scope.row)">清除供应商</span>
                                            <!--/*                                        <span class="pointer ml10" v-if="scope.row.state == 6 " style="color: #216ec6;" @click="goToBidding(scope.row.orderId)">走竞价</span>*/-->
                                        </template>
                                    </template>
                                </el-table-column>
                                <el-table-column label="订单号" width="240" prop="orderSn">
                                    <template v-slot="scope">
                                        <span class="action" @click="showTwoOrder(scope.row)">{{scope.row.orderSn}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="确认状态" width="100" prop="affirmState">
                                    <template v-slot="scope">
                                        <el-tag v-if="scope.row.masterAffirmState == 0">待确认</el-tag>
                                        <el-tag type="success" v-if="scope.row.masterAffirmState == 1">已确认</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="供方确认状态" width="110" prop="affirmState">
                                    <template v-slot="scope">
                                        <el-tag v-if="scope.row.affirmState == 0">待确认</el-tag>
                                        <el-tag type="success" v-if="scope.row.affirmState == 1">已确认</el-tag>
                                        <el-tag type="danger" v-if="scope.row.affirmState == 2">已拒绝</el-tag>
                                        <el-tag type="danger" v-if="scope.row.affirmState == 3">未选择供方</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="订单生成来源" width="120" prop="orderSourceType">
                                    <template v-slot="scope">
                                        <el-tag v-if="scope.row.orderSourceType == 1">商品供应商</el-tag>
                                        <el-tag v-if="scope.row.orderSourceType == 2">竞价采购</el-tag>
                                        <el-tag v-if="scope.row.orderSourceType == 3">大宗月供</el-tag>
                                        <el-tag v-if="scope.row.orderSourceType == 4">大宗临购计划</el-tag>
                                        <el-tag v-if="scope.row.orderSourceType == 5">大宗临购</el-tag>
                                        <el-tag v-if="scope.row.orderSourceType == 6">大宗月供</el-tag>
                                        <el-tag v-if="scope.row.orderSourceType == 7">周转材料</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="状态" width="75" prop="state">
                                    <template v-slot="scope">
                                        <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                                        <span>
                                            {{ ['已提交', '待确认', '已确认','待签订合同', '已签合同', '待发货', '已关闭', '已发货', '待收货', '已完成'][scope.row.state - 1] }}
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="供应商名称" width="300" prop="supplierName" />
                                <el-table-column label="商品名称" width="200" prop="untitled"></el-table-column>
                                <el-table-column label="不含税总金额" width="200" prop="noRateAmount"></el-table-column>
                                <el-table-column label="含税总金额" width="200" prop="actualAmount"></el-table-column>
                                <el-table-column label="收件人" width="" prop="receiverName"></el-table-column>
                                <el-table-column label="收件人手机号" width="150" prop="receiverMobile" />
                                <el-table-column label="收件地址" width="200" prop="receiverAddress" />
                                <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                                <!--                                <el-table-column label="订单提交时间" width="160" prop="flishTime" />-->
                                <el-table-column label="订单备注" width="300" prop="orderRemark"></el-table-column>
                            </el-table>
                        </div>
                        <!--            分页-->
                        <Pagination
                            v-show="tableData2 != null || tableData2.length != 0"
                            :total="paginationInfo2.total"
                            :pageSize.sync="paginationInfo2.pageSize"
                            :currentPage.sync="paginationInfo2.currentPage"
                            @currentChange="getTableData2"
                            @sizeChange="getTableData2"
                        />
                    </div>
                </div>
            </el-tabs>
        </div>
        <el-dialog v-loading="createOrderLoading" v-dialogDrag id="supplierDialog"  title="生成订单" :visible.sync="showSelectSupplierOrder"  width="80%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div class="tabs-title" id="contractList">选择供方</div>
            <div class="con" v-loading="tableLoading5">
                <div class="e-table"  style="background-color: #fff">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div class="left">
                            <el-input clearable type="text" @blur="getTableData5" placeholder="输入搜索关键字" v-model="keywords5">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData5" />
                            </el-input>
                        </div>
                    </div>
                    <el-table
                        ref="eltableCurrentRow"
                        border
                        style="width: 100%"
                        :data="tableData5"
                        class="table"
                        @selection-change="selectSupplierOneM"
                        :max-height="$store.state.tableHeight"
                        @row-click="handleCurrentInventoryClick"
                    >
                        <el-table-column type="selection" width="40"></el-table-column>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="supplierName" label="供应商名称" width=""></el-table-column>
                        <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <Pagination
                    v-show="tableData5 != null || tableData5.length != 0"
                    :total="paginationInfo5.total"
                    :pageSize.sync="paginationInfo5.pageSize"
                    :currentPage.sync="paginationInfo5.currentPage"
                    @currentChange="getTableData5"
                    @sizeChange="getTableData5"
                />
            </div>
            <div class="tabs-title" id="contractList">订单商品</div>
            <div class="e-table"  style="background-color: #fff">
                <el-table ref="tableRef"
                          border
                          style="width: 100%"
                          :data="createTwoOrderRowData"
                          class="table"
                          :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="productName" label="商品名称" width="200px"></el-table-column>
                    <el-table-column prop="relevanceName" label="物资名称" width="200px"></el-table-column>
                    <el-table-column prop="productImg" label="商品图片" width="130">
                        <template slot-scope="scope">
                            <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="productSn" label="商品编号" width="230px"></el-table-column>
                    <el-table-column prop="skuName" label="规格" width="200px"></el-table-column>
                    <el-table-column prop="buyCounts" label="数量" width=""></el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                </el-table>
            </div>
            <div class="buttons">
                <el-button class="btn-blue" @click="createSupplierOrderM">生成订单</el-button>
                <el-button @click="showSelectSupplierOrder = false">返回</el-button>
            </div>
        </el-dialog>
        <el-dialog v-dialogDrag  title="订单详情" :visible.sync="showTwoOrderDialog"  width="80%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div class="con">
                <div class="tabs-title" id="baseInfo">订单信息</div>
                <div style="width: 100%" class="form">
                    <el-form :model="formData2" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="订单号：">
                                    <span>{{ formData2.orderSn }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="采购方：">
                                    <span>{{ formData2.twoEnterpriseName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="供方名称：">
                                    <span>{{ formData2.supplierName }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="供方确认状态：">
                                    <el-tag v-if="formData2.affirmState == 0">待确认</el-tag>
                                    <el-tag type="success" v-if="formData2.affirmState == 1">已确认</el-tag>
                                    <el-tag type="danger" v-if="formData2.affirmState == 2">已拒绝</el-tag>
                                    <el-tag type="danger" v-if="formData2.affirmState == 3">未选择供方</el-tag>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="商品名称：">
                                    <span>{{ formData2.untitled }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="含税总金额：">
                                    <span>{{ formData2.actualAmount }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="不含税总金额：">
                                    <span>{{ formData2.noRateAmount }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="收件人：">
                                    <span>{{ formData2.receiverName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="收件人手机号：">
                                    <span>{{ formData2.receiverMobile }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="收件地址：">
                                    <span>{{ formData2.receiverAddress }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="是否开发票：">
                                    <el-tag :type="formData2.orderBillState == 2 ? 'success' : ''">
                                        {{ ['初始', '已申请', '已开票'][formData2.orderBillState] }}
                                    </el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="支付类型：">
                                    <el-tag v-if="formData2.payWay === 1">线上支付</el-tag>
                                    <el-tag v-if="formData2.payWay === 2">内部结算</el-tag>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="商品类型：">
                                    <el-tag v-if="formData2.productType == 0">物资</el-tag>
                                    <el-tag v-if="formData2.productType == 10">低值易耗品</el-tag>
                                    <el-tag v-if="formData2.productType == 12">大宗物料</el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="确认状态：">
                                    <el-tag v-if="formData2.masterAffirmState == 0">待确认</el-tag>
                                    <el-tag type="success" v-if="formData2.masterAffirmState == 1">已确认</el-tag>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="订单状态：">
                                    <el-tag>
                                        {{ ['草稿', '已提交', '待确认','已确认', '待签订合', '已签合同', '待发货', '已关闭', '已发货', '待收货', '已完成'][formData2.state] }}
                                    </el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="订单类型：">
                                    <el-tag v-if="formData.orderClass ===1">普通订单</el-tag>
                                    <el-tag v-if="formData.orderClass === 2">多供方订单</el-tag>
                                    <el-tag v-if="formData.orderClass === 3">二级子订单</el-tag>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="订单备注：">
                                    <span>{{ formData2.orderRemark }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
            <div  class="con" v-loading="tableLoading3">
                <div class="tabs-title" id="contractList">订单商品</div>
                <div class="e-table"  style="background-color: #fff">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div class="left">
                            <el-input clearable type="text" @blur="getTableData3" placeholder="输入搜索关键字" v-model="keywords3">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData3" />
                            </el-input>
                            <div class="left-btn" style="margin-left: 20px">
                            </div>
                        </div>
                    </div>
                    <el-table
                        ref="tableRef"
                        border
                        style="width: 100%"
                        :data="tableData3"
                        class="table"
                        :max-height="$store.state.tableHeight"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="productName" label="商品名称" width="200px"></el-table-column>
                        <el-table-column prop="relevanceName" label="物资名称" width="200px"></el-table-column>
                        <el-table-column prop="productImg" label="商品图片" width="130">
                            <template slot-scope="scope">
                                <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                            </template>
                        </el-table-column>
                        <el-table-column prop="productSn" label="商品编号" width="230px"></el-table-column>
                        <el-table-column prop="skuName" label="规格" width="200px"></el-table-column>
                        <el-table-column prop="buyCounts" label="购买数量" width=""></el-table-column>
                        <el-table-column  prop="shipCounts" label="已发货数量" width="160px"></el-table-column>
                        <el-table-column  prop="confirmCounts" label="确认收货数量" width="160px"></el-table-column>
                        <el-table-column  prop="returnCounts" label="商城退货数量" width="100"></el-table-column>
                        <el-table-column  prop="pcwpReturn" label="pcwp退货数量" width="100"></el-table-column>
                        <el-table-column prop="productPrice" label="含税单价" width="200px"></el-table-column>
                        <el-table-column prop="totalAmount" label="含税总金额" width="200px"></el-table-column>
                        <el-table-column prop="isComment" label="评论状态" width="">
                            <template v-slot="scope">
                                <el-tag v-if="scope.row.isComment === 0">未评价</el-tag>
                                <el-tag v-if="scope.row.isComment === 1" type="success">已评价</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <Pagination
                    v-show="tableData3 != null || tableData3.length !== 0"
                    :total="paginationInfo3.total"
                    :pageSize.sync="paginationInfo3.pageSize"
                    :currentPage.sync="paginationInfo3.currentPage"
                    @currentChange="getTableData3"
                    @sizeChange="getTableData3"
                />
            </div>
            <el-button class="btn-blue" style="margin-top: 20px;margin-left: 50%" @click="showTwoOrderDialog = false">关闭</el-button>
        </el-dialog>
        <el-dialog v-loading="bidingFormLoading" v-dialogDrag id="supplierDialog"  title="生成竞价" :visible.sync="showBidingForm"  width="80%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div class="tabs-title" id="contractList">竞价信息</div>
            <el-form :model="bidingForm" :rules="bidingFormRules" label-width="200px" ref="bidingFormRef" :disabled="false" class="demo-ruleForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="标题：" prop="title">
                            <el-input maxlength="200" placeholder="请输入标题" clearable  v-model="bidingForm.title"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="截止时间：" prop="endTime">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="bidingForm.endTime"
                                align="right"
                                type="date"
                                placeholder="选择日期"
                                :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="联系人：" prop="linkName">
                            <el-input maxlength="10" placeholder="请输入联系人" clearable  v-model="bidingForm.linkName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话：" prop="linkPhone">
                            <el-input type="number" clearable  v-model="bidingForm.linkPhone" placeholder="请输入11位手机号码"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="竞价类型：" prop="type">
                            <el-radio-group v-model="bidingForm.type">
                                <el-radio :label="1">公开竞价</el-radio>
                                <span> <el-radio :label="2" >邀请竞价</el-radio></span>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="bidingForm.type===2">
                        <el-form-item label="选择供应商：" prop="">
                            <el-button type="primary"  @click="showSupplierDialog">选择供应商</el-button>
                            <!--                            <el-select style="width: 80%" name="name" no-data-text="请选择供应商" v-model="bidingForm.suppliers"  multiple collapse-tags  placeholder="请选择">-->
                            <!--                                <el-option-->
                            <!--                                    v-for="item in bidingForm.suppliers"-->
                            <!--                                    :key="item.enterpriseId"-->
                            <!--                                    :label="item.enterpriseName"-->
                            <!--                                    :value="item.enterpriseId"-->
                            <!--                                    :disabled="item.disabled">-->
                            <!--                                </el-option>-->
                            <!--                            </el-select>-->
                        </el-form-item>

                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="说明：" prop="biddingExplain">
                            <editor v-model="bidingForm.biddingExplain"></editor>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="24">
                        <el-form-item label="竞价函说明：" prop="biddingNotice">
                            <el-input type="textarea"
                                      :rows="6" v-model="bidingForm.biddingNotice"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="tabs-title" id="contractList">订单商品</div>
            <div class="e-table"  style="background-color: #fff">
                <el-table ref="tableRef"
                          border
                          style="width: 100%"
                          :data="bidingForm.orderItems"
                          class="table"
                          :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="productName" label="商品名称" width="200px"></el-table-column>
                    <el-table-column prop="relevanceName" label="物资名称" width="200px"></el-table-column>
                    <el-table-column prop="productImg" label="商品图片" width="130">
                        <template v-slot="scope">
                            <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="buyCounts" label="数量" width=""></el-table-column>
                    <!--                    <el-table-column prop="maxPrice" label="最高单价" width="">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <el-input-number-->
                    <!--                                :controls="false"-->
                    <!--                                :precision="2"-->
                    <!--                                v-model="scope.row.maxPrice"-->
                    <!--                                ></el-input-number>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column prop="skuName" label="规格" width="200px"></el-table-column>
                    <el-table-column prop="texture" label="材质" width=""></el-table-column>
                    <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                </el-table>
            </div>
            <div class="buttons">
                <el-button class="btn-blue" @click="createBidingM">生成竞价</el-button>
                <el-button @click="showBidingForm = false">返回</el-button>
            </div>
        </el-dialog>
        <div class="buttons">
<!--            <el-button v-if="formData.state >= 8 && formData.state < 10" class="btn-blue" @click="orderCloseClick">完结订单</el-button>-->
            <el-button class="btn-blue" @click="outPutPDF">导出pdf</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
        <!--        选择供应商-->
        <!--选择供应商发送-->
        <el-dialog v-dialogDrag title="选择供应商" id="supplierDialog2"  v-loading="showTableLoading" heigh :visible.sync="showSupplierList" width="80%" >
            <div style="display: flex;flex-direction: row;justify-content: space-between">
                <div class="box-left" style="max-width: 100%">
                    <div class="e-table" style="background-color: #fff">
                        <div class="top" style="height: 30px">
                            <div class="left-btn1">
                                <!--               Shop和supplier反着的       -->
                                <!--                        <el-checkbox v-model="checkAllShop" label="选择所有供应商" :indeterminate="false" @change="toggleSelectAllShop"/>-->
                            </div>
                            <div class="search_box">
                                <el-input style="width: 400px" clearable type="text" @keyup.enter.native="getshopLists" placeholder="输入搜索关键字" v-model="shopList.keywords">
                                    <img src="@/assets/search.png" slot="suffix" @click="getshopLists" alt=""/>
                                </el-input>
                            </div>
                            <span style="color: #f1083b">双击选择供应商！</span>
                        </div>
                        <!--                    @selection-change="handleSelectionChangeShop"-->
                        <!--                    @row-click="handleCurrentInventoryClickShop"-->
                        <el-table
                            ref="tableShop1"
                            highlight-current-row border
                            :data="shopList.tableData"
                            class="table"
                            :height="rightTableHeight"
                            @row-dblclick="handleCurrentInventoryClickShop"
                        >
<!--                            <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
                            <el-table-column label="序号" type="index" width="60"/>
                            <el-table-column prop="enterpriseName" width="" label="企业名称"/>

                        </el-table>
                        <Pagination
                            v-show="shopList.tableData || shopList.tableData.length > 0"
                            :total="shopList.paginationInfo.total"
                            :pageSize.sync="shopList.paginationInfo.pageSize"
                            :currentPage.sync="shopList.paginationInfo.currentPage"
                            @currentChange="currentChange"
                            @sizeChange="sizeChange"
                        />
                    </div>
                </div>
                <div class="box-right" style="width: 100%">
                    <div class="e-table">
                        <div class="top" style="height: 30px">
                            <span style="color: #ea083a">双击移除供应商！</span>
                        </div>
                        <!--                                            @selection-change="handleSelectionChangeShop"
                                                @row-click="handleCurrentInventoryClickShop"-->
                        <el-table
                            ref="tableShop2"
                            highlight-current-row border
                            :data="selectedSupplierList"
                            class="table"
                            :height="rightTableHeight"
                            @row-dblclick="handleRemoveCurrentClickShop"
                        >
<!--                            <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
                            <el-table-column label="序号" type="index" width="60"/>
                            <el-table-column prop="enterpriseName" width="" label="企业名称"/>

                        </el-table>
                    </div>
                </div>
            </div>
            <span slot="footer">
                <el-button type="primary" style="margin-top: 20px" @click="confirmSupplierDialog">确定</el-button>
                <el-button style="margin-top: 20px" @click="closeSupplierDialog">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import editor from '@/components/quillEditor'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
import {
    batchCreateLinXingTwoOrder,
    listSupplierByShopId,
    orderItemList,
    listContractTwoOrderList,
    closeOrderSupplier,
    masterAffirmTwoOrder,
    createBidingByOrder,
    updateToBidingState,
    updateNotUseState, orderCloseClick, getOrderInfoOutPutPdf
} from '@/api/platform/order/orders'
import { findByOrderSn, updateOrderCostPrice } from '@/api/shopManage/order/order'
import { getShopList } from '@/api/platform/mail/inbox'
import { getSupplierShopsList } from '@/api/platform/supplier/supplierAudit'

export default {
    data () {
        return {
            // 邀请竞价
            // 第二个选择框的供应商
            selectedSupplierList: [],
            materialSelectRow: [],
            // 选中供应商
            selectedSupplierRow: [],
            // 选中供应商名称
            selectedSupplierRowName: [],
            checkAllShop: false, //全选控制
            shopList: {
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            showTableLoading: false,
            showSupplierList: false,
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            createOrderLoading: false,
            tableLoading5: false,
            tableData5: [],
            keywords5: null,
            paginationInfo5: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            showSelectSupplierOrder: false,
            keywords2: null,
            tableLoading3: false,
            tableLoading2: false,
            showTwoOrderDialog: false,
            formData2: [],
            tableData3: [],
            tableData2: [],
            createTwoOrderRowData: [],
            selectSupplierOne: null,
            masterOrderItemSelectRowData: [],
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            keywords3: null,
            paginationInfo3: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            formLoading: false,
            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedRow: [],
            changedRowNum: [],
            tableLoading: false,
            bidingForm: {
                biddingSourceType: 1,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                orderItems: [],
                productType: 1,
                suppliers: [],
                biddingNotice: `说明：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准；
2、“到场含税单价”包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费、开卷校平等一切费用；税率13%，若因国家税率发生变化，按国家最新税率执行，发票开具均采用一票制；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
4、结算与支付方式：
5、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件上传至物资采购平台竞价专区，报价截止时间为'{year}'年'{month}'月'{day}'日'{minutes}'分，逾期送达的视为报价无效。
6、竞价函启封比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
7、报价方申明（若有必要）： `
            },
            bidingFormRules: {
                title: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
                endTime: [
                    { required: true, message: '请选择截止时间', trigger: 'blur' },
                ],
                linkPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
            },
            bidingFormLoading: false,
            showBidingForm: false,
        }
    },
    components: {
        Pagination,
        editor
    },
    created () {
        this.findByOrderSnM()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        'bidingForm.endTime': {
            handler () {
                this.formatStr()
                this.$forceUpdate()
            }
        },
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        formatStr () {
            let year = this.bidingForm.endTime.substring(0, 4)
            let month = this.bidingForm.endTime.substring(5, 7)
            let day = this.bidingForm.endTime.substring(8, 10)
            let minutes = this.bidingForm.endTime.substring(11, 16)
            this.bidingForm.biddingNotice = `说明：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准；
2、“到场含税单价”包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费、开卷校平等一切费用；税率13%，若因国家税率发生变化，按国家最新税率执行，发票开具均采用一票制；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
4、结算与支付方式：
5、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件上传至物资采购平台竞价专区，报价截止时间为${year}年${month}月${day}日${minutes}分，逾期送达的视为报价无效。
6、竞价函启封比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。'
7、报价方申明（若有必要）： `
        },
        sizeChange () {
            this.getshopLists()
        },
        currentChange () {
            this.getshopLists()
        },
        getShopList,
        getshopLists () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.pageSize,
                orderBy: 1,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            this.inventoryTableLoading = true
            getSupplierShopsList(params).then(res => {
                this.shopList.tableData = res.list || []
                this.shopList.paginationInfo.currentPage = res.currPage
                this.shopList.paginationInfo.pageSize = res.pageSize
                this.shopList.paginationInfo.total = res.totalCount
            })
            this.inventoryTableLoading = false
        },
        async showSupplierDialog () {
            await this.getshopLists ()
            this.showSupplierList = true
        },
        confirmSupplierDialog () {
            // console.log(this.selectedSupplierRow)
            // this.bidingForm.supplierId =  this.selectedSupplierRow.map(item=>{
            //     console.log(item)
            //     return  item.enterpriseId
            // })
            // 循环已选择的供应商列表
            this.selectedSupplierRow = this.selectedSupplierList || []
            this.selectedSupplierRowName = this.selectedSupplierRow.map(item=>{
                return   { enterpriseId: item.enterpriseId, enterpriseName: item.enterpriseName, disabled: true }
            })
            this.bidingForm.suppliers = this.selectedSupplierRow.map(item=>{
                return   { supplierId: item.enterpriseId, supplierName: item.enterpriseName, disabled: true }
            }) || []
            this.infoStr = '已选' + this.selectedSupplierRow.length + '个供应商'
            this.showSupplierList = false
        },
        // 取消选择
        handleRemoveCurrentClickShop (row) {
            // 从已选择的供应商数组中移除该供应商
            this.selectedSupplierList = this.selectedSupplierList.filter(t => t.enterpriseId != row.enterpriseId)
        },
        closeSupplierDialog () {
            this.showSupplierList = false
            this.selectedSupplierList = []
        },
        // 双击选择
        handleCurrentInventoryClickShop (row) {
            // 根据id排除已有的供应商
            for (let i = 0; i < this.selectedSupplierList.length; i++) {
                let t = this.selectedSupplierList[i]
                if(t.enterpriseId == row.enterpriseId) {
                    return this.$message.warning('该供应商已选择！')
                }
            }
            this.selectedSupplierList.push(row)
            this.$message.success('选择成功！')

        },
        handleSelectionChangeShop (list) {
            this.selectedSupplierRow = list || []
        },
        clearBidingClick () {
            let data = this.masterOrderItemSelectRowData.filter(t => {
                if(t.state == 3) {
                    return true
                }else {
                    return false
                }
            })
            if(data.length == 0) {
                return this.$message.error('请选择待竞价的订单数据')
            }
            let itemsIds = this.masterOrderItemSelectRowData.map(t => t.orderItemId)
            this.clientPop('info', '您确定要取消待竞价吗！', async () => {
                this.tableLoading = true
                updateNotUseState(itemsIds).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('修改成功')
                        this.getTableData()
                        this.masterOrderItemSelectRowData = []
                    }
                    this.tableLoading = false
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        updateToBidingStateClick () {
            let data = this.masterOrderItemSelectRowData.filter(t => {
                if(t.state == 2 || t.state == 3) {
                    return true
                }else {
                    return false
                }
            })
            if(data.length == 0) {
                return this.$message.error('请选择待分配的订单数据')
            }
            let itemsIds = this.masterOrderItemSelectRowData.map(t => t.orderItemId)
            this.clientPop('info', '您确定修改待竞价状态吗！', async () => {
                this.tableLoading = true
                updateToBidingState(itemsIds).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('修改成功')
                        this.getTableData()
                        this.masterOrderItemSelectRowData = []
                    }
                    this.tableLoading = false
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        createBidingM () {
            this.$refs.bidingFormRef.validate(valid => {
                if (valid) {
                    // 邀请竞价必须选择供应商
                    if (this.bidingForm.type === 2) {
                        if (this.bidingForm.suppliers == null || this.bidingForm.suppliers.length == 0) {
                            return this.$message.error('请选择供应商！')
                        }
                    }
                    this.clientPop('info', '您确定要生成竞价吗！', async () => {
                        // for (let i = 0; i < this.bidingForm.orderItems.length; i++) {
                        //     let t = this.bidingForm.orderItems[i]
                        //     if(t.maxPrice == null || t.maxPrice == 0 || t.maxPrice == '') {
                        //         return this.$message.error('商品：【' + t.productName + '】未设置最高单价！')
                        //     }
                        // }
                        this.bidingFormLoading = true
                        this.bidingForm.billType = this.formData.billType
                        createBidingByOrder(this.bidingForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('生成成功')
                                this.getTableData()
                                this.showBidingForm = false
                                this.bidingForm = {
                                    biddingSourceType: 1,
                                    title: null,
                                    type: 1,
                                    endTime: null,
                                    linkName: null,
                                    linkPhone: null,
                                    biddingExplain: null,
                                    orderItems: [],
                                    productType: 1,
                                }
                            }
                        }).finally(() => {
                            this.bidingFormLoading = false
                        })
                    })
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
        createBidingClick () {
            // 去掉不是未分配的订单
            let data = this.masterOrderItemSelectRowData.filter(t => {
                if(t.state == 2 || t.state == 3) {
                    return true
                }else {
                    return false
                }
            })
            if(data.length == 0) {
                return this.$message.error('请选择待分配的订单数据')
            }
            this.bidingForm.orderItems = data
            this.showBidingForm = true
        },
        updateOrderCostPriceM () {
            let data =  this.masterOrderItemSelectRowData.filter(t => {
                if(t.state == 2 || t.state == 3) {
                    return true
                }else {
                    return false
                }
            })
            if(data.length == 0) {
                return this.$message.error('请选择待分配的订单商品')
            }
            const newArray = data.map(obj => {
                const { orderItemId, costPrice } = obj
                return {
                    // 根据需求选择要提取的属性，这里只提取了id和name属性
                    orderItemId,
                    costPrice,
                }})
            this.clientPop('info', '您确定修改供方综合单价吗？', async () => {
                this.tableLoading = true
                updateOrderCostPrice(newArray).then(res=>{
                    if (res.code === 200) {
                        this.tableLoading = false
                        this.getData()
                        this.$message.success('修改供方综合单价成功！')
                    }else {
                        this.$message.error('修改供方综合单价失败！')
                        this.tableLoading = false
                    }
                })
            })
        },
        createSupplierOrderM () {
            if(this.selectSupplierOne == null) {
                return this.$message.error('请选择供应商！')
            }
            if(this.createTwoOrderRowData.length == 0) {
                return this.$message.error('未选择订单信息！')
            }
            let itemsIds = this.createTwoOrderRowData.map(t => t.orderItemId)
            let params = {
                orderItemId: itemsIds,
                supplierId: this.selectSupplierOne.supplierId,
                supplierName: this.selectSupplierOne.supplierName,
            }
            this.clientPop('info', '您确定要生成订单吗！', async () => {
                this.createOrderLoading = true
                batchCreateLinXingTwoOrder(params).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('生成成功')
                        this.getTableData2()
                        this.getTableData()
                        this.selectSupplierOne = null
                        this.createTwoOrderRowData = []
                        this.showSelectSupplierOrder = false
                    }
                }).finally(() => {
                    this.createOrderLoading = false
                })
            })
        },
        // 点击二级订单
        showTwoOrder (row) {
            this.formData2 = row
            this.getTableData3()
            this.showTwoOrderDialog = true
        },
        // 获取表格数据
        getTableData3 () {
            let params = {
                orderId: this.formData2.orderId,
                page: this.paginationInfo3.currentPage,
                limit: this.paginationInfo3.pageSize,
            }
            if(this.keywords3 != null) {
                params.keywords = this.keywords3
            }
            this.tableLoading3 = true
            orderItemList(params).then(res => {
                this.tableData3 = res.list
                this.paginationInfo3.total = res.totalCount
                this.paginationInfo3.pageSize = res.pageSize
                this.paginationInfo3.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading3 = false
            })
        },
        affirmTwoOrderM (orderId) {
            this.clientPop('info', '确认后供方可确认、可查看，您确认该二级订单吗？', async () => {
                this.tableLoading2 = true
                masterAffirmTwoOrder({ orderIds: [orderId], isAffirm: true }).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功！')
                        this.getTableData2()
                    }
                }).finally(() => {
                    this.tableLoading2 = false
                })
            })
        },
        // 修改订单供应商
        updateTwoOrderSupplier (row) {
            if(row.affirmState == 1) {
                return this.$message.error('供方已确认不能确认清除！')
            }
            this.clientPop('info', '您确认清除订单供应商吗？该操作会清除订单！', async () => {
                this.tableLoading2 = true
                closeOrderSupplier({ orderId: row.orderId }).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功！')
                        this.getTableData2()
                        this.getTableData()
                    }
                }).finally(() => {
                    this.tableLoading2 = false
                })
            })
        },
        findByOrderSnM () {
            this.formLoading = true
            findByOrderSn({ orderSn: this.$route.query.orderSn }).then(res => {
                this.formData = res
                this.getTableData()
                this.getTableData2()
            }).finally(() => {
                this.formLoading = false
            })
        },
        getTableData2 () {
            let params = {
                parentOrderId: this.formData.orderId,
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                isQueryTwoOrder: true
            }
            if(this.keywords2 != null) {
                params.keywords = this.keywords2
            }
            this.tableLoading2 = true
            listContractTwoOrderList(params).then(res => {
                this.tableData2 = res.list
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading2 = false
            })
        },
        selectSupplierM () {
            // 去掉不是未分配的订单
            let data =  this.masterOrderItemSelectRowData.filter(t => {
                if(t.state == 2 || t.state == 3) {
                    return true
                }else {
                    return false
                }
            })
            if(data.length == 0) {
                return this.$message.error('请选择待分配的订单商品')
            }
            this.createTwoOrderRowData = data
            this.getTableData5()
            this.showSelectSupplierOrder = true
        },
        masterOrderItemSelectRow (value) {
            this.masterOrderItemSelectRowData = value
        },
        masterOrderItemRowClick (row) {
            row.flag = !row.flag
            this.$refs.masterOrderItemRef.toggleRowSelection(row, row.flag)
        },
        selectSupplierOneM (rows) {
            this.selectSupplierOne = rows[0]
            if (rows.length > 1) {
                this.$refs.eltableCurrentRow.clearSelection()
                this.$refs.eltableCurrentRow.toggleRowSelection(rows.pop())
            }
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            if (this.selectSupplierOne == row) { // 选中的是已有的 取消选中
                this.selectSupplierOne = null
                this.$refs.eltableCurrentRow.clearSelection()
            } else {
                this.selectSupplierOne = [row]
                this.$refs.eltableCurrentRow.clearSelection()
                this.$refs.eltableCurrentRow.toggleRowSelection(row, true)
            }
        },
        getTableData5 () {
            let params = {
                page: this.paginationInfo5.currentPage,
                limit: this.paginationInfo5.pageSize,
            }
            if(this.keywords5 != null) {
                params.keywords = this.keywords5
            }
            this.tableLoading5 = true
            listSupplierByShopId(params).then(res => {
                this.tableData5 = res.list
                this.paginationInfo5.total = res.totalCount
                this.paginationInfo5.pageSize = res.pageSize
                this.paginationInfo5.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading5 = false
            })
        },
        // 获取表格数据
        getTableData () {
            let params = {
                orderId: this.formData.orderId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.keywords != null) {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            orderItemList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        addData () {},
        deleteData () {},
        orderCloseClick () {
            this.clientPop('info', '您确定要完结订单吗？不能再发货操作！', async () => {
                this.formLoading = true
                orderCloseClick({ orderId: this.formData.orderId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.findByOrderSnM()
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        outPutPDF () {
            this.formLoading = true
            getOrderInfoOutPutPdf({ orderId: this.formData.orderId }).then(res => {
                const blob = new Blob([res], { type: 'application/pdf' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = '周转材料订单.pdf'
                a.click()
                window.URL.revokeObjectURL(url)
                this.$message.success('操作成功')
            }).finally(() => {
                this.formLoading = false
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 780px;
        margin-top: 0px;
    }
}
/deep/ #supplierDialog2 {
    .el-dialog__body {
        height: 500px;
        margin-top: 0px;
    }
}
/deep/ #supplierDialog {
    .el-dialog__body {
        height: 580px;
        margin-top: 0px;
    }
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
</style>
