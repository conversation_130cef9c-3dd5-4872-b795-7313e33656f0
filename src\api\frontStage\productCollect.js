import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service
const addShopCollect = params => {
    return httpPost({
        url: '/materialMall/productCollect/createByProductId',
        params,
    })
}
const getCollectListApi = params => {
    return httpPost({
        url: '/materialMall/productCollect/listByEntity',
        params,
    })
}
const deleById = params => {
    return httpGet({
        url: '/materialMall/productCollect/delete',
        params,
    })
}
const getProductList = params => {
    return httpPost({
        url: '/materialMall/productCollect/selCollectData',
        params,
    })
}
const isCollect = params => {
    return httpPost({
        url: '/materialMall/productCollect/isCollect',
        params,
    })
}
const getCollectNum = params => {
    return httpPost({
        url: '/materialMall/productCollect/getCollectNum',
        params,
    })
}
// 根据商品主键批量添加
const addBatch = params => {
    return httpPost({
        url: '/materialMall/productCollect/addBatch',
        params,
    })
}
export {
    addShopCollect,
    getCollectListApi,
    deleById,
    getProductList,
    isCollect,
    getCollectNum,
    addBatch
}