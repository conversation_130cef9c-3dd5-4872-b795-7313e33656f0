<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <!-- <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> -->
    <link rel="stylesheet" href="<%= BASE_URL %>cdn/element-ui.css">
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>

    <!-- <script src="https://unpkg.com/vue@2.6.11/dist/vue.runtime.min.js"></script>
    <script src="https://unpkg.com/vuex@3.2.0/dist/vuex.min.js"></script>
    <script src="https://unpkg.com/vue-router@3.2.0/dist/vue-router.min.js"></script>
    <script src="https://unpkg.com/axios@0.21.4/dist/axios.min.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.6/lib/index.js"></script>
    <script src="http://code.jquery.com/jquery-2.1.1.min.js"></script> -->
    <!-- <script src="http://***************/cdn/vue.runtime.min.js"></script> -->
    <script src="<%= BASE_URL %>cdn/vue.js"></script>
    <script src="<%= BASE_URL %>cdn/vuex.min.js"></script>
    <script src="<%= BASE_URL %>cdn/vue-router.min.js"></script>
    <script src="<%= BASE_URL %>cdn/axios.min.js"></script>
    <script src="<%= BASE_URL %>cdn/element-ui.js"></script>
    <script src="<%= BASE_URL %>cdn/jquery-2.1.1.min.js"></script>
    <!-- built files will be auto injected -->
  </body>
</html>
