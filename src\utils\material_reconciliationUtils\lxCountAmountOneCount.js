import { capitalsAndNum } from '@/utils/material_reconciliationUtils/compute'
import { toFixed } from '@/utils/common'
function  fixed2 (num) {
    return toFixed(num, 2)
}
export function  oneAmount (tableData, taxRate) {
    let  params = {
        tableData: [],
        reconciliationAmount: 0,
        reconciliationNoRateAmount: 0,
        taxAmount: 0
    }
    let taxAmount = 0
    let reconciliationAmount = 0
    let reconciliationNoRateAmount = 0
    for (let i = 0; i < tableData.length; i++) {
        let t = this.tableData[i]
        t.taxAmount =  fixed2(Number(t.acceptanceNoRateAmount) * Number(taxRate))
        taxAmount = fixed2(Number(taxRate) + Number(t.taxAmount))
        reconciliationAmount = fixed2(Number(reconciliationAmount) + Number(t.acceptanceAmount))
        reconciliationNoRateAmount = fixed2(Number(reconciliationNoRateAmount) + Number(t.acceptanceNoRateAmount))
    }
    params.taxAmount = taxAmount
    this.reconciliationForm.reconciliationAmount = reconciliationAmount
    this.reconciliationForm.reconciliationNoRateAmount = capitalsAndNum(reconciliationAmount, reconciliationNoRateAmount, this.reconciliationForm.taxRate)
}

// 最终计算
