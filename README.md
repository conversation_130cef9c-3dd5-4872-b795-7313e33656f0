### 命令行指令
```
Git 全局设置
git config --global user.name "username"
git config --global user.email "email addr"
```
### 已存在的文件夹或 Git 仓库
```
cd existing_folder
git init
git remote <NAME_EMAIL>
git add .
git commit
git push -u origin master
```
### 忽略本地修改
```
git update-index --assume-unchanged vue.config.js
git update-index --no-assume-unchanged
```
### 生成本机ssh key
```
ssh-keygen -t rsa -C <EMAIL>
三次回车
cat ~/.ssh/id_rsa.pub
拷贝生成的字符串到设置界面中有ssh公钥中
```


### 设置淘宝镜像
```
npm config set registry https://registry.npmmirror.com

yarn config set registry https://registry.npmmirror.com
```

### 设置sass的安装镜像
```
npm i node-sass --sass_binary_site=https://npm.taobao.org/mirrors/node-sass/

yarn config set sass_binary_site http://cdn.npm.taobao.org/dist/node-sass -g
yarn -D add node-sass
```

### vs code 设置代码格式
```
安装ESLint扩展
安装Prettier扩展
打开vs code设置，选择“工作区”
点击编辑区状态栏右下角ESLint为启用状态
右击编辑区，选择“使用...格式化文档”-->“配置默认格式化程序”，选择“ESLint”，以后VUE文件可以直接先使用格式化，再保存，就可以自动统一代码规范了
```

### form rules 动态判断
```js
startMonth: [
    { required: true, message: '请选择日期', trigger: ['blur', 'change'] },
    { validator: (rule, value, callback)=>{
        if(this.formDataDetail.startDay) {
            callback()
        }else{
            callback(new Error('请选择日'))
        }
    }, trigger: ['blur', 'change'] },
],
```

### 后台接口文档地址
http://http://182.151.206.110:15103//doc.html#/%E6%8B%9B%E6%A0%87/%E6%8B%9B%E6%A0%87%E7%94%B3%E8%AF%B7/saveUsingPOST_1
### 接口调用地址
http://http://182.151.206.110:15103//${模块名称}/${接口名称}/${参数}

### 前端操作和显示统一规范
1、操作列固定到第一列显示，按钮颜色，需按效果图来   
> 默认不显示选择框，如果点了批量操作(如批量删除)，才显示   
  如果不显示的时候，按钮列就是第一列，否则为第二列（前面两列均为冻结固定显示）    

2、列宽设定    
> 设置固定列宽，以标题和内容 文字多的作为标准
  特别长的列不设宽度（自适应），但需要设置一个min-width   

3、查看数据信息，点击编号，统一单击编号查看   
> 此处不显示编辑按钮，而是通过判断当前单据的状态来确定跳转“查看”还是“编辑”   

4、新增时，保存成功后返回列表；    
> 编辑页保存成功后，统一保持在当前页面，不跳转   
  点击“保存并提交”按钮后，才需返回到列表页

6、调用客户端公共方法：import { showLoading, hideLoading, showPromptPop, showSuccessPop, showErrorPop } from '@/utils/common'   
> (1)、showLoading(txt) 显示loading    
  (2)、hideLoading() 隐藏loading   
  (3)、showPromptPop(msg) 弹出提示信息窗   
  (4)、showSuccessPop(msg) 弹出成功信息窗   
  (5)、showErrorPop(msg) 弹出错误信息窗   
  &ensp;&ensp;&ensp;&ensp;页面中如果需要执行高级搜索和信息提示窗的回调，则添加如下代码：   
```js
    this.clientPop('suc', '成功提示信息', this.callback)
    this.clientPop('err', '删除提示信息', this.callback)
    this.clientPop('info', '确认提示信息', this.callback, null, this.cancelCallBack) // 如果没有参数，且没有 取消 回调事件，则不用传后面两个参数
    this.clientPop('errDt', msg, this.callback, null, this.cancelCallBack,title) // 错误详情弹框，msg为错误详情，title为错误标题
    this.clientPop('warn', '警告提示信息', this.callback)
```

7、表单样式   
> 标签宽度一致，右对齐，输入框对齐，标签文字添加中文冒号    

8、附件   
> 附件统一传billid，通过url直接取billid参数

9、url上参数名统一全小写
> 如接口是大写，传给接口的按后端来，就传
```js
    {
        billId: billid
    }
```

10、按钮，要使用效果图中对应颜色的按钮      
> 黄绿色按钮 class="btn-greenYellow"    
  蓝色按钮 class="btn-blue"    
  红色按钮 class="btn-delete"    

11、页面滚动条，默认不显示，自适应高度，如果特殊情况要加，统一样式   
> 滚动条 class="scroll_bar"   

12、公共方法
```js
import { selectDataClient, openAppClient, advanceSearch } from '@/utils/common'

...mapMutations(['setSelectedInfo']),
onSelect () {
    // 弹窗选择数据
    selectDataClient({
        SelectServiceToken: '/hr/org/getOrgByUserId', // 客户端对应方法名
        IsSingleMode: true, // 是否单选 true 单选 false 多选
        SelectedIDs: ['1439990097591140358'], // 已选中id(注意：是上一次已选中的id，不是要传的参数，如果第一次选，则传[])
        InitParamsStr: JSON.stringify({name: ''}),    // 接口参数(name为模拟参数)
        Title: '选择机构用户' // 标题
    }).then(val => {
        // 保存用户选择返回数据到store
        this.setSelectedInfo(val)
        // 客户端弹窗预览结果
        // 参数依次为: 打开页面地址、标题、回调函数
        openAppClient('/showSelectedData', '选择结果', function () {
            // alert('关闭后回调')
        })
    })
    
    // 客户端弹窗弹出本地页面
    openAppClient('/showSelectedData', '选择结果', function () {
        //函数内若用了 this ，请使用箭头函数，否则内部无法访问外部this
        // alert('关闭后回调')
    })
},
// 高级搜索方法
    // url: 高级搜索路由
    advanceSearch(url).then(res => {
        alert(JSON.stringify(res))
    })

```

13、公共地址组件
> Demo:  /list

```html
<SelectAddr v-model="formData.address1"
    lazy
    showCountry
></SelectAddr>
```

14、客户端弹窗接口地址   
https://docs.qq.com/sheet/DS1NzZlpFY3FLb2J4?tab=BB08J2

15、列表跳转流程   
> 在列表点击标题，先判断当前单据状态值，如果为-1 || 2 || 5 时(对应状态参见17)，直接跳转查看页；   
否则调每个模块自己的对应的getCurrentUnitInfo接口（在接口文档中查找 审核 - 获取流程按钮显示状态）   
如果返回canModify字段为真，则跳转到编辑页面，否则跳转到查看页面   
在页面中再根据canAudit, canUndoAudit字段的值判断是否显示“审核”，“撤回”等流程按钮

16、字体统一使用14px大小，样式统一写了
table要加 class
```html
<div class="e-table">
    <el-table class="table"></el-table>
</div>
```

17、流程状态统一对应值，store/modules/equip.js 已定义，可直接使用
```js
[ 
    {value: -1,label: '已删除'}, 
    {value: 0,label: '草稿'}, 
    {value: 1,label: '审核中'}, 
    {value: 2,label: '已审核'}, 
    {value: 5,label: '已作废'}, 
    {value: 9,label: '共享审核中'}, 
    {value: 10,label: '已增加'}, 
    {value: 20,label: '已锁定'} 
]
```

18、变更列表，都有增、删、改的颜色状态   
>   点击删除时，如果是后台返回的数据，则提示，并修改changeState   
   如果是新增的，则直接删除

19、点“取消”按钮、以及涉及流程的按钮（如审核、撤回、作废等）统一返回对应单据主列表页面，用$router.replace

20、保存并提交，是需要在保存功能全部成功完成以后，再调提交接口

21、提测之前，先把客户端地址修改为测试地址(http://***************) ，自己点击测试通过后，再给测试说转测

22、代码尽量精炼，能直接绑定取值的，避免每次都写一长串获取，如$store...，可以直接使用mapState、mapMutationts等进行映射到vue实例上   
&ensp;&ensp;&ensp;&ensp;如$route，也可通过computed 进行返回对应字段，以避免每次都重复通过$route获取

23、进行数据操作时，多使用对象进行缓存，减少遍历次数

24、在写业务过程中，如多个功能页面，均使用到相同功能时，在完成当前业务模块后，可提取出公共模块为后续业务共用，前提是在不耽搁当前业务开发，完成当前业务后，再行提取封装   
&ensp;&ensp;&ensp;&ensp;如有多个界面均使用到相同方法，避免每次都要批量替换大量相同代码

25、公共模块如 components，一定要别的模块能使用，内部只能写公用功能，业务功能（如调对应模块接口）放在参数中，或通过调用外部方法实现自定义；   
&ensp;&ensp;&ensp;&ensp;不要在公共模块内部写具体某个业务模块的功能   
&ensp;&ensp;&ensp;&ensp;如审核公共组件   

26、变更列表批量操作   
&ensp;&ensp;&ensp;&ensp;批量删除，当 对选中项的changType === 0 时，修改 changType === -1，并判断 源单 id 是否为空，为空则隐藏，不可撤销；否则显示并标红   
&ensp;&ensp;&ensp;&ensp;批量撤销（删除），对选中项的changType === -1 修改为 2

27、查看页有可编辑时   
&ensp;&ensp;&ensp;&ensp;当 canModify: false 时，进入查看界面，有的是审核中可编辑，此时点击“审核”按钮时，先调用“保存”接口，成功后再弹出填写审核意见弹窗，无需单独添加“保存”按钮

28、新增单据   
&ensp;&ensp;&ensp;&ensp;点击“保存”按钮时，成功后弹出“保存成功，是否继续？”，点击“确定”后，则跳转编辑页面，并且跳转到第二个选项卡，如果点击“取消”按钮，则返回列表页

29、表单标题   
&ensp;&ensp;&ensp;&ensp;表单中的标签统一加中文“：”结尾，如：
```html
    <label>标题一：</label><input value="输入框" /></div>
```

30、审核弹窗确定后，使用后端返回的错误信息   

31、流程按钮   
&ensp;&ensp;&ensp;&ensp;与流程相关的按钮，调用接口后，成功回调返回列表页

32、查看别的页面    
&ensp;&ensp;&ensp;&ensp;在有的页面中需要“查看”别的页面时，统一使用客户端弹窗方式打开，同时链接地址后面跟上参数"isview=true"   
&ensp;&ensp;&ensp;&ensp;比如周材单据中，需要查看合同，合同的查看页判断，如果有该参数，则隐藏底部按钮

33、对于是否启用、禁用之类的，都统一使用ElementUi的开关样式   

34、定义变量，统一设置初始值   
&ensp;&ensp;&ensp;&ensp; 定义变量初始值时，非字符串型的设默认值为null，字符串型设为 ''

35、向上汇总迭代方法
```
import { getItemOfTreeArr } from '@/utils/common'   

const data = this.tableList
// 在树中找出当前元素的位置 结构
const stack = getItemOfTreeArr(data, 'uuid', row.uuid)
// 从父级开始汇总，向上到顶级
stack.pop() // 当前级出栈
let parent = stack.pop()
while (parent) {
    parent.settlementQuantity = parent.children.reduce((count, prev) => count + Number(prev.settlementQuantity), 0)
    parent = stack.pop()
}

36、将数据储存在客户端并获取的方法

import { setItem, getItem, removeItem } from '@/utils/common'

setItem('value',[1,2,3]) //存储值
getItem('value') //获取值
removeItem('value') //删除值

37、文件批量下载的方法
import { batchDownload } from '@/utils/common'

batchDownload(list) //传入objectName，objectPath对象集合
例: batchDownload([
    {
        "objectName":"u=1814268193,3619863984&fm=253&fmt=auto&app=138&f=JPEG-20220630093937.jpg",
        "objectPath":"http://***************:9000/pcwp2.0/subcontract/SRBC/20220630/u=1814268193,3619863984&fm=253&fmt=auto&app=138&f=JPEG-20220630093937.jpg"
    },
    {
        "objectName":"1-20220630093945.png",
        "objectPath":"http://***************:9000/pcwp2.0/subcontract/SRBC/20220630/1-20220630093945.png"
    },
    {
        "objectName":"QQ图片20220630114226-20220630093949.png",
        "objectPath":"http://***************:9000/pcwp2.0/subcontract/SRBC/20220630/QQ图片20220630114226-20220630093949.png"
    }
])

38、各种图标路径

删除：@/assets/btn/delete.png
取消删除：@/assets/btn/cancelDelete.png
下载：@/assets/btn/downLoad.png
    //树形列表外面需用div包裹并添加oparation_box类名，每个图标上面添加add_sub类名
    例：<div class="oparation_box">
            <img src="@/assets/btn/downLoad.png" class="add_sub"
                @click="downloadFile(row)"
                title="下载"
            />
            <img src="@/assets/btn/delete.png" class="add_sub"
                @click="deleteEle(row)"
                v-if="!isView"
                title="删除"
            />
        </div>
    //普通列表外面需用div包裹并添加list_box类名，每个图标上面添加add_sub类名
    例：<div class="list_box">
            <img src="@/assets/btn/downLoad.png" class="add_sub"
                @click="downloadFile(row)"
                title="下载"
            />
            <img src="@/assets/btn/delete.png" class="add_sub"
                @click="deleteEle(row)"
                v-if="!isView"
                title="删除"
            />
        </div>

39、流程按钮组件
    流程按钮为全局组件，无需单独引用，所有使用了流程功能的都需使用
    /*
        auditState：为流程按钮状态
        state：为流程状态
        exas：为按钮回调函数，根据返回参数区分执行哪个事件，audit（审核），undoAudit（撤回），submitCommit（保存并提交），submit（保存），nullify（作废），deleteData（删除）
        founderId：为单据创始人id，由后端返回，需向对应的后端确认为哪个字段
    */
    例：<ComExaButtons :auditState="auditState" :state="formData.state" @exas="exas" :founderId="formData.founderId" />

40、主列表table的外层div需添加类名billList，表格需添加highlight-current-row实现选中高亮效果,如表格使用了fixed属性，则还需向表格添加height="0"
    例：<div class="e-table billList">
            <top-step :stepInfo="steps" />
            <div class="top">
            </div>
            <el-table
                ref="table"
                :data="tableData"
                border
                highlight-current-row
                class="table"
                height="0"
            >
            </el-table>
            <ComPagination
                :total="totalPage"
                :pageSize.sync="pagination.limit"
                :currentPage.sync="pagination.page"
                @currentChange="contractListByEntity"
                @sizeChange="contractListByEntity"
                class="bottomSetting"
            />
        </div>
    示例页面：\pages\subcontract\subcontractList.vue

41、表格对齐，列宽按照\PCWP2.0\5产品设计\2设计标准\四川路桥集团项目实时管控与成本预警平台2.0系统页面布局及通用设计标准V1.0.docx
    第二十三条
        根据内容长短是否固定采用不同对齐方式。
        列表：
            1.内容长短不固定：比如机构、单据名称（合同名称、招标名称等）、外包方名称等，采用左对齐；//代码默认为居中对齐
            2.内容固定（非数字类）：比如字典类字段、状态、日期等，采用居中对齐；
            3.内容固定（数字类）：比如金额、数量、单价等，采用右对齐。
    第二十四条
        5、列宽：
            1）内容长短不固定：最小宽度至少200px，动态自适应
            2）内容固定（非数字类）：比如字典类字段、状态、日期等，根据内容和标题较长者固定宽度
            3）内容固定（数字类）：比如金额、数量、单价，根据内容和标题较长者固定宽度

42、把不需要获取本位币的页面路由加到 src\store\index.js里的noGetBaseCyByOrgListurl变量里面
    例：noGetBaseCyByOrgListurl: [
            //合同类
            '/subcontractList',
            '/otherContractList',
            '/serviceProcurementList',
            '/ctRevolvMaterial',
            '/processingContract',
            '/ctMaterialPurchase',
            '/ctEquipmentLease',
            '/ctEquipmentPurchase',
            '/materialSalesContract',
            '/equimentLeaseIncomeContract',
        ],         

43、所有明细表格需添加:max-height="$store.state.tableHeight"  或者使用:max-height="tableHeight"然后在computed里面使用...mapState(['tableHeight'])
    例如：<el-table
            :max-height="$store.state.tableHeight"
            >
        </el-table>

44、所有单据详情编辑页面增加顶部标题组件，在页面里使用BillTop组件，接收两个参数
    title:页面标题，默认为路由的meta里设置的title;
    cancel:关闭按钮事件，同页面上的取消按钮事件;
    例： 
    <div class="e-form">
        <BillTop title="分包合同申请编辑" @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs">
        </div>
    </div>

    并且将页面上的这段代码
        this.screenWidth = document.documentElement.clientWidth
        this.screenHeight = document.documentElement.clientHeight
    修改为以下代码
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
    还有
        onChangeTab (e) {
            $.scrollTo(`#${e.name}`, 500)
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        }
    修改为
        onChangeTab (e) {
            const height  = $('#' + e.name).offset().top
            $.scrollTo(height - this.topHeight, 500)
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        }

45、所有列表页的状态需要动态改变颜色，使用v-status指令传入参数即可改变
    例：
        1、通用流程状态,v-status="流程状态"即可

            <span  v-status="state"></span>

        2、其他状态，例如合同 ，v-status=一个对象，对象里包含两个参数
            参数说明：
                （1）：value:状态值
                （2）：statusObj:{
                            invalid: [12], //作废类状态值
                            approved: [4, 9, 11], //审核完成类状态值
                            audit: [1, 2, 3, 5, 6, 7, 8, 10], //审核过程中类状态值
                            draft: [0], //草稿类状态值
                        }
                        
                举例：
                <span @click="showPage(scope.row)"
                    v-status="{
                        value: scope.row.contractState,
                        statusObj:{
                            invalid: [12], //作废类
                            approved: [4, 9, 11], //审核完成类
                            audit: [1, 2, 3, 5, 6, 7, 8, 10], //审核过程中类
                            draft: [0], //草稿类
                        }
                    }"
                >
                    {{options.stateArr[scope.row.contractState]}}
                </span>

46、切换查询机构功能
    获取台账列表的时候参数默认添加本地查询机构id，并且高级搜素的机构搜索也要默认带进参数
    localStorage.getItem('searchOrgId')
    localStorage.getItem('searchOrgName')

47、表格列头拖拽功能
    所有表格需添加 v-sortTable 指令来实现列头拖拽功能
    v-sortTable接收一个对象，该对象包含 tableData ，_this ，ref 三个属性
        tableData:当前表格的数据，
        _this：传入this即可
        ref：当前表格的ref名称，注意多个表格ref不要重复
    例如：
        <el-table
                ref="table"
                :data="tableData"
                border
                highlight-current-row
                class="table"
                height="0"
                v-sortTable="{ 'tableData': tableData, '_this': this, 'ref': 'table' }"
            >
        </el-table>

48、鼠标放在“审核中”类状态上，显示审核历史，范围：需要审核的所有功能
    在需要审核功能的列表页的状态使用ToolHistory组件，只有审核中类状态才显示
    例：
        <template slot-scope="scope">
            <ToolHistory v-if="scope.row.contractState === 10" :key="scope.$index" url="/subcontract/audit/getAuditHistory" :billId="scope.row.billId" :billType="7014">
                <span  @click="showPage(scope.row)" v-status="{ value: scope.row.contractState,statusObj:$store.state.contract.ctClassify.stateArrClass }">
                    {{options.stateArr[scope.row.contractState]}}
                </span>
            </ToolHistory>
            <span v-else @click="showPage(scope.row)" v-status="{ value: scope.row.contractState,statusObj:$store.state.contract.ctClassify.stateArrClass }">
                {{options.stateArr[scope.row.contractState]}}
            </span>
        </template>
    参数：
        url：当前模块的接口审核历史查询地址，必传
        billId：当前单据的billId，必传
        billType：当前单据的billType，必传
        <ToolHistory> 这里面放入需要包裹的内容，例如单据状态名称 </ToolHistory>

49、审核弹框改为此大小
    openAppClient('/exaDialog', '填写审核意见', () => {
        const auditParams = JSON.parse(localStorage.vuex).auditParams
        showLoading()
        auditRequest.audit(auditParams).then(() => {
            hideLoading()
            _this.clientPop('suc', '审核成功', _this.handleClose)
        }).catch(()=>{
            hideLoading()
        })
    }, null, null, null, 'small')
```
### 打印功能   
    1、在公共按钮组件上传入 :isPrint="条件" 条件为true即可显示打印按钮
        然后在exas回调函数里接收参数 等于 print及为打印
        在 common 里引入 webprintReport 方法 import { webprintReport } from '@/utils/common'
        webprintReport接收一个对象参数为：
                // params =  {
                //     'reportTitle': '', //报表名
                //     'orgName': '', //单据机构
                //     'reportQRUri': '', //报表内容URI
                //     'billNo': '', //单据编码
                //     'summaryInfoItems': [ //基本信息
                //         {
                //             'label': '标题：',
                //             'value': '内容',
                //             'isFullRow': true //是否占整行
                //         },
                //         {
                //             'label': '标题1：',
                //             'value': '内容2',
                //             'isFullRow': true //是否占整行
                //         }
                //     ],
                //     'tables': [ //表格
                //         {
                //             'tableTitle': '表名',
                //             'tableContent': {
                //                 '表头1': '字段名',
                //                 '表头2': '字段名',
                //                 '表头3': '字段名',
                //             },
                //             'tableData': [], //表格数据
                //         }
                //     ]
        拼接成该格式,传入即可实现打印 webprintReport(params)
        
        例：
        import ComExaButtons from '@/components/common/exaButtons'
    
        <ComExaButtons :auditState="auditState" :state="formData.state" @exas="exas" :founderId="formData.founderId" :isPrint="formData.state === 2"/>
        <el-button size="small" @click="handleClose">取消</el-button>
    
        async function  exas  (arg) {
            if (arg === 'print') {
                const printData = async ()=>{
                    showLoading()
                    try{
                        const  params =  {
                            'reportTitle': '', //报表名
                            'orgName': '', //单据机构
                            'reportQRUri': '', //报表内容URI
                            'billNo': '', //单据编码
                            'summaryInfoItems': [ //基本信息
                            ],
                            'tables': [ //表格
                            ]
                        }
                        const formData = this.formData
                        params.reportTitle = formData.contractName
                        params.orgName = formData.orgName
                        params.billNo = formData.contractNumber
                        const baseInfo = [
                            ['甲方：', '', false],
                            ['签订日期：', formData.signingDate, false],
                            ['乙方：', '', false],
                            ['合同有效期：', formData.signingStartTime + ' - ' + formData.signingEndTime, false],
                            ['合同类型：', formData.contractTypeName, false],
                            ['币种：', formData.currencyName, false],
                            ['税率：', formData.taxRate + '%', false],
                            ['不含税金额：', formData.taxExcludedAmount, false],
                            ['税额：', formData.taxAmount, false],
                            ['合同金额：', formData.contractAmount, false],
                            ['已交履约保证金：', formData.bondAmount, false],
                            ['预付款：', formData.advanceCharge, false],
                            ['传输财务共享：', this.financialSharingList[formData.financialSharing], false],
                            ['签约人：', formData.signingName, false],
                            ['经办人：', formData.founderName, false],
                            ['变更次数：', '', false],
                            ['签约地点：', this.showAddress(formData.address), true],
                            ['主要合同内容：', formData.contractContext, true],
                        ]
                        baseInfo.forEach(item=>{
                            const summaryInfoItem = {}
                            summaryInfoItem.label = item[0]
                            summaryInfoItem.value = item[1]
                            summaryInfoItem.isFullRow = item[2]
                            params.summaryInfoItems.push(summaryInfoItem)
                        })
                        let contractListData = await this.subcontract.contractListAll({ sourceId: '1567046222908944386' })
                        contractListData = contractListData.map(x=>{
                            x.unitPriceTypeName = ['全单价', '劳务单价'][x.unitPriceType - 1]
                            return x
                        })
                        const contractList = {
                            'tableTitle': '合同清单',
                            'tableContent': {
                                '细目编号': 'itemNo',
                                '细目名称': 'itemName',
                                '结构部位': 'structureNamePaths',
                                '计量单位': 'unit',
                                '单价类型': 'unitPriceTypeName',
                                '数量': 'quantity',
                                '单价': 'unitPrice',
                                '金额': 'amount',
                            },
                            'tableData': contractListData, //表格数据
                        }
                        params.tables.push(contractList)
                        hideLoading()
                        return params
                    }catch(err) {
                        console.log(err)
                        this.clientPop('err', '打印失败')
                        hideLoading()
                    }
                }
                const data = await printData()
                webprintReport(data)
            }
        }
        示例页面：src\pages\subcontract\contractSigning\contractSigningShow.vue

