export default [
    {
        path: '/performance',
        component: () => import('@/pages/performanceManage/index'),
        redirect: '/performance/trial',
        children: [
            {
                path: '/performanceManage/plan/monthPlan',
                name: 'monthPlan',
                component: () =>
                    import('@/pages/performanceManage/plan/month/index.vue'),
                meta: {
                    // keepAlive: true,
                    title: '大宗月供计划',
                },
            },
            {
                path: '/performanceManage/monthPlanDtl',
                name: 'performanceManageMonthPlanDtl',
                component: () =>
                    import('@/pages/performanceManage/plan/month/detail.vue'),
                meta: {
                    title: '月供计划明细',
                },
            },
            {
                path: '/performanceManage/updateChangeDetail',
                name: 'performanceManageUpdateChangeDetail',
                component: () =>
                    import('@/pages/performanceManage/plan/month/updateChangeDetail.vue'),
                meta: {
                    title: '月供计划变更明细',
                },
            },

            {
                path: '/orderPlanDetail',
                name: 'orderPlanDetail',
                component: () =>
                    import('@/pages/performanceManage/plan/detail.vue'),
                meta: {
                    title: '订单详情',
                },
            },
            {
                path: '/performance/trial',
                component: () => import('@/pages/performanceManage/trial/index'),
            },
            {
                path: '/performance/synthesisMaterialOrder',
                name: 'synthesisMaterialOrder',
                component: () => import('@/pages/performanceManage/userOrder/synthesisMaterialOrder.vue'),
                meta: {
                    keepAlive: true,
                    title: '大宗采购订单'
                },
            },
            {
                path: '/performanceManage/synthesisMaterialDetail',
                name: 'performanceManageSynthesisMaterialDetail',
                component: () => import('@/pages/performanceManage/userOrder/synthesisMaterialDetail.vue'),
                meta: {
                    title: '大宗材料订单详情'
                },
            },
            {
                path: '/performanceManage/individualApply',
                name: 'performanceManageIndividualApply',
                component: () => import('@/pages/performanceManage/userOrder/individualApply.vue'),
                meta: {
                    title: '大宗材料发票申请'
                },
            },
            // 收料员管理
            {
                path: '/performance/system/receiptPerson',
                component: () => import('@/pages/performanceManage/system/receiptPerson/index'),
                meta: {
                    title: '系统管理-收料员管理'
                }
            },
            {
                path: '/performance/sheet/sheet',
                component: () => import('@/pages/performanceManage/sheet/sheet/index'),
                meta: {
                    keepAlive: true,
                    title: '对账-对账单'
                }
            },
            {
                path: '/performance/invoice/invoice',
                name: 'invoiceApply',
                component: () => import('@/pages/performanceManage/invoice/apply/index'),
                meta: {
                    title: '对账-申请发票'
                }
            },
            {
                path: '/performance/sheet/synthesizeTemporary',
                component: () => import('@/pages/performanceManage/sheet/synthesizeTemporary/index'),
                meta: {
                    keepAlive: true,
                    title: '对账-对账单'
                }
            },
            {
                path: '/performance/sheet/synthesizeTemporaryDetail',
                component: () => import('@/pages/performanceManage/sheet/synthesizeTemporary/detail'),
                name: 'synthesizeTemporaryDetail',
                meta: {
                    title: '对账-对账单详情'
                }
            },
            {
                path: '/performance/sheet/synthesizeTemporaryFDetail',
                component: () => import('@/pages/performanceManage/sheet/synthesizeTemporary/floatDetailInfo.vue'),
                name: 'synthesizeTemporaryFDetail',
                meta: {
                    title: '对账-浮动价格对账详情'
                }
            },
            {
                path: '/performance/sheet/synthesizeTemporaryGDetail',
                component: () => import('@/pages/performanceManage/sheet/synthesizeTemporary/fixationDetailInfo.vue'),
                name: 'synthesizeTemporaryGDetail',
                meta: {
                    title: '对账-固定价格对账详情'
                }
            },
            {
                path: '/performance/blockSheet/sheet',
                component: () => import('@/pages/performanceManage/sheet/blockSheet/index'),
                meta: {
                    keepAlive: true,
                    title: '对账-大宗临购对账单'
                }
            },
            {
                path: '/performance/sheetDetail',
                component: () => import('@/pages/performanceManage/sheet/sheet/detail'),
                name: 'performanceSheetDetail',
                meta: {
                    title: '对账-对账单'
                }
            },
            {
                path: '/performance/sheet/floatDetail',
                component: () => import('@/pages/performanceManage/sheet/sheet/floatDetail.vue'),
                name: 'floatDetail',
                meta: {
                    title: '对账-浮动价格对账'
                }
            },
            {
                path: '/performance/sheet/floatDetailInfo',
                component: () => import('@/pages/performanceManage/sheet/sheet/floatDetailInfo.vue'),
                name: 'floatDetailInfo',
                meta: {
                    title: '对账-浮动价格对账详情'
                }
            },
            {
                path: '/performance/sheet/fixationDetail',
                component: () => import('@/pages/performanceManage/sheet/sheet/fixationDetail.vue'),
                name: 'fixationDetail',
                meta: {
                    title: '对账-固定价格对账'
                }
            },
            {
                path: '/performance/sheet/fixationDetailInfo',
                component: () => import('@/pages/performanceManage/sheet/sheet/fixationDetailInfo.vue'),
                name: 'fixationDetailInfo',
                meta: {
                    title: '对账-固定价格对账详情'
                }
            },
            // 发票抬头
            {
                path: '/performance/invoice/invoiceRiseApply',
                name: 'performInvoiceRiseApply',
                component: () => import('@/pages/performanceManage/invoice/invoiceRise/apply.vue'),
                meta: {
                    title: '发票抬头-新增'
                }
            },
            {
                path: '/performance/invoice/invoiceRiseDetail',
                name: 'invoiceRiseDetail',
                component: () => import('@/pages/performanceManage/invoice/invoiceRise/detail.vue'),
                meta: {
                    title: '发票抬头-详情'
                }
            },
            {
                path: '/performance/invoice/invoiceRise',
                component: () => import('@/pages/performanceManage/invoice/invoiceRise/index'),
                meta: {
                    title: '发票抬头'
                }
            },
            {
                path: '/performanceManage/invoice/invoice',
                component: () => import('@/pages/performanceManage/invoice/apply/index'),
                meta: {
                    title: '发票申请'
                }
            },
            {
                path: '/performance/invoice/record',
                component: () => import('@/pages/performanceManage/invoice/record/index'),
                meta: {
                    title: '发票记录'
                }
            },
            {
                path: '/performance/invoice/invoiceDetail',
                name: 'invoiceDetail',
                component: () => import('@/pages/performanceManage/invoice/record/detail.vue'),
                meta: {
                    title: '发票记录'
                }
            },
        ]
    }
]