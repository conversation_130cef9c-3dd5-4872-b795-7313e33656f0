<template>
<!--
使用说明：
1）、引入category-cascader.vue
2）、语法：<category-cascader :catelogPath.sync="catelogPath"></category-cascader>
    解释：
      catelogPath：指定的值是cascader初始化需要显示的值，应该和父组件的catelogPath绑定;
      由于有sync修饰符，所以cascader路径变化以后自动会修改父的catelogPath，这是结合子组件this.$emit("update:catelogPath",v)做的
-->
  <div>
    <el-cascader
        :style="customStyle"
        filterable
        clearable
        placeholder="请选择分类"
        v-model="paths"
        :options="categorys"
        :props="setting"
        v-bind="$props"
        :disabled="disabled"
        @change="handleChange"
    ></el-cascader>
  </div>
</template>

<script>
import { treeByName } from '@/api/platform/product/productCategory'
export default {
    //import引入的组件需要注入到对象中才能使用
    components: {},
    //接受父组件传来的值
    props: {
        productType: {
            type: Number,
            default () {
                return null
            }
        },
        catelogPath: {
            type: Array,
            default () {
                return []
            }
        },
        //  0 全部 1 大宗临购  2低值易耗 是否是大宗临购分类
        isLc: {
            type: Number,
            default () {
                return 0
            }
        },
        disabled: {
            type: Boolean,
            default () {
                return false
            }
        },
        className: {
            type: String,
            default () {
                return ''
            }
        },
        customStyle: {
            type: String,
            default: 'width: 370px'
        }
    },
    data () {
    //这里存放数据
        return {
            setting: {
                value: 'classId',
                label: 'className',
                children: 'children',
                checkStrictly: true,
                expandTrigger: 'hover'
            },
            categorys: [],
            paths: this.catelogPath,
            flatCategory: [],
        }
    },
    watch: {
        catelogPath (v) {
            // this.paths = this.catelogPath
            this.paths = v
        },
        paths (v) {
            this.$emit('update:classId', v[v.length - 1])
            this.$emit('update:classPath', v)
            //还可以使用pubsub-js进行传值
            // this.PubSub.publish('catPath', v)
        }
    },
    //方法集合
    methods: {
        async getCategorys () {
            let params = { state: 1 }
            if(this.productType != null) {
                params.productType = this.productType
            }
            // 大宗临购
            if(this.isLc != null) {
                params.isLc = this.isLc
            }
            let res = await treeByName(params)
            this.categorys = res
            this.flatCategory = this.flatChildren(res, 'children', 3)
        },
        // 扁平化children
        flatChildren (list, name, depth) {
            let result = list
            let len = list.length
            for(let i = 0; i < len; i++) {
                let arr = list[i][name] || []
                result = result.concat(arr)
            }
            if(depth === 1) return result
            return result.concat(this.flatChildren(result, name, depth - 1))
        },
        handleChange (value) {
            let uniqueArr = []
            let idArr = []
            this.flatCategory.forEach(item => {
                if(idArr.includes(item.classId)) return
                uniqueArr.push(item)
                idArr.push(item.classId)
            })
            let nameArr = []
            uniqueArr.forEach(item => {
                value.forEach(subItem => {
                    if(item.classId !== subItem) return
                    nameArr.push({ className: item.className, classId: subItem })
                })
            })
            this.$emit('change', nameArr)
        },
    },
    created () {
        this.getCategorys()
    }
}
</script>
