<template>
    <div class="root p20">
        <div class="pointer contentBox center dfb">
            <div class="userMenu">
                <el-menu default-active="1" class="el-menu-vertical-user" @open="handleOpen" @close="handleClose">
                    <!-- <el-menu-item class="materialControl_todo highlight" @click="loadIframe('/todoTop', '全部')" index="1">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>待办事项</span>
                            <img class="arrow" src="" alt="">
                        </template>
                    </el-menu-item> -->
                    <el-submenu index="1">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>待办事项</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item @click="loadIframe('/todoTop', '物资')" index="1-1">
                                <template slot="title">
                                    <div class="dot"></div> 物资管理
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('/todoTop', '招标')" index="1-2">
                                <template slot="title">
                                    <div class="dot"></div> 招标管理
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('/todoTop', '收支合同')" index="1-3">
                                <template slot="title">
                                    <div class="dot"></div> 收支合同管理
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('/todoTop', '结算')" index="1-4">
                                <template slot="title">
                                    <div class="dot"></div> 结算管理
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('/todoTop', '外包方')" index="1-5">
                                <template slot="title">
                                    <div class="dot"></div> 外包方管理
                                </template>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                    <!-- <el-menu-item class="materialControl_alert" @click="loadIframe('开发中', '')" index="2">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>异常预警</span>
                            <img class="arrow" src="" alt="">
                        </template>
                    </el-menu-item> -->
                    <el-submenu index="2">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>异常预警</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item @click="loadIframe('开发中', '')" index="2-1">
                                <template slot="title">
                                    <div class="dot"></div> 物资未扣款预警
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="2-2">
                                <template slot="title">
                                    <div class="dot"></div> 库存领料积压预警
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="2-3">
                                <template slot="title">
                                    <div class="dot"></div> 到期不消耗预警
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="2-4">
                                <template slot="title">
                                    <div class="dot"></div> 预付款未冲抵金额过大预警
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="2-5">
                                <template slot="title">
                                    <div class="dot"></div> 合同收料两占比提醒
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="2-6">
                                <template slot="title">
                                    <div class="dot"></div> 结算金额占比提醒
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="2-2">
                                <template slot="title">
                                    <div class="dot"></div> 审核流程预警
                                </template>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                    <el-menu-item class="materialControl_msg" @click="loadIframe('开发中', '')" index="3">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>系统通知</span>
                            <img class="arrow" src="" alt="">
                        </template>
                    </el-menu-item>
                    <el-submenu index="4">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>汇总分析报表</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-1">
                                <template slot="title">
                                    <div class="dot"></div> 物资集采计划汇总统计
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-2">
                                <template slot="title">
                                    <div class="dot"></div> 物资月度需用计划汇总统计
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-3">
                                <template slot="title">
                                    <div class="dot"></div> 物资采购对账单统计
                                </template>
                            </el-menu-item>
                            <el-menu-item class="highlight" @click="loadIframe('/subsummaryLedgerList', '全部')" index="4-4">
                                <template slot="title">
                                    <div class="dot"></div> 分类、汇总台账
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-5">
                                <template slot="title">
                                    <div class="dot"></div> 协作队伍物资领用、报销
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-6">
                                <template slot="title">
                                    <div class="dot"></div> 收、发料实物明细台账
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-7">
                                <template slot="title">
                                    <div class="dot"></div> 主材进场台账
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-8">
                                <template slot="title">
                                    <div class="dot"></div> 大宗物资单价汇总表
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-9">
                                <template slot="title">
                                    <div class="dot"></div> 主材理论消耗和实际耗用
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-10">
                                <template slot="title">
                                    <div class="dot"></div> 主要材料招标分析表
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-11">
                                <template slot="title">
                                    <div class="dot"></div> 物资收、发、存统计表
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-12">
                                <template slot="title">
                                    <div class="dot"></div> 物资耗用明细账表
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-13">
                                <template slot="title">
                                    <div class="dot"></div> 物资耗用明细账表
                                </template>
                            </el-menu-item>
                            <el-menu-item @click="loadIframe('开发中', '')" index="4-14">
                                <template slot="title">
                                    <div class="dot"></div> 采购情况及成本变动季度表
                                </template>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                    <el-menu-item class="materialControl_msg" @click="loadIframe('/ProjectDepartmentHome', '全部')" index="5">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>任务看板</span>
                            <img class="arrow" src="" alt="">
                        </template>
                    </el-menu-item>
                    <el-menu-item class="materialControl_msg" @click="loadIframe('/materialBaseLibManage', '全部')" index="6">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>物资基础库</span>
                            <img class="arrow" src="" alt="">
                        </template>
                    </el-menu-item>
                </el-menu>
            </div>
            <div class="content p20">
                <iframe class="frame" :src="activeLink" v-if="!inProgress" frameborder="0"></iframe>
                <div class="frame" style="text-align: center; line-height: 700px;font-size: 30px;" v-if="inProgress">页面正在开发中...</div>
            </div>
        </div>
    </div>
</template>
<script>
import { getUserMenu } from '../../../../api/frontStage/menu'
// import axios from 'axios'
export default {
    components: {},
    data () {
        return {
            activeLink: '',
            frameLinks: [],
            orgId: '',
            token: '',
            inProgress: false,
        }
    },
    methods: {
        handleOpen (key, keyPath) {
            console.log(key, keyPath)
        },
        handleClose (key, keyPath) {
            console.log(key, keyPath)
        },
        loadIframe (path, name) {
            if (path === '开发中') {
                return this.inProgress = true
            }
            this.inProgress = false
            // 生产环境换成 http://pcwp2-api.scrbg.com
            this.activeLink = `http://***************${path}?typename=${name}&showBar=0&token=${this.token}&orgId=${this.orgId}`
            console.log(this.activeLink)
        },
    },
    created () {
        this.orgId = localStorage.getItem('orgId')
        this.token = localStorage.getItem('token')
        this.activeLink = `http://***************/todoTop?typename=全部&token=${this.token}&orgId=${this.orgId}`
        // eslint-disable-next-line no-unused-vars
        getUserMenu().then(res => {})
    },
}
</script>
<style scoped lang="scss">
@import '../../../../assets/css/menuStyle.css';

.root {
    min-width: 1326px;
    background-color: #f5f5f5;

    .contentBox {
        width: 1326px;
        height: 763px;

        div {
            height: 100%;
            background-color: #fff;
        }

        .userMenu {
            width: 230px;
            overflow: overlay;
        }

        .content {
            width: 1076px;

            .frame {
                width: 100%;
                height: 100%;
            }
        }
    }
}

/deep/.el-menu-item {
    &.is-active {
        .arrow {
            background: url(../../../../assets/images/userCenter/go2.png);
        }
    }

    &.materialControl_todo {
        .icon {
            background: url(../../../../assets/images/userCenter/dbsx.png);
        }

        &.is-active {
            .icon {
                background: url(../../../../assets/images/userCenter/dbsx2.png);
            }
        }
    }

    &.materialControl_alert {
        .icon {
            background: url(../../../../assets/images/userCenter/ycyj.png);
        }

        &.is-active {
            .icon {
                background: url(../../../../assets/images/userCenter/ycyj2.png);
            }
        }
    }

    &.materialControl_msg {
        .icon {
            background: url(../../../../assets/images/userCenter/xttz.png);
        }

        &.is-active {
            .icon {
                background: url(../../../../assets/images/userCenter/xttz2.png);
            }
        }
    }
}

/deep/.el-submenu {
    .icon {
        background: url(../../../../assets/images/userCenter/hzfxbb2.png);
    }

    &.is-opened {
        .icon {
            background: url(../../../../assets/images/userCenter/hzfxbb.png);
        }
    }
}
</style>
<style>
.root .el-submenu__title {
    color: #333 !important;
    font-size: 16px !important;
    font-weight: 500 !important;
}

.root .el-menu-vertical-demo.el-menu .el-submenu.is-opened .el-submenu__title {
    background-color: #fff !important;
    font-weight: 500 !important;
}

.root .el-menu-vertical-demo.el-menu .el-submenu__title:hover,
.el-menu-vertical-demo.el-menu .el-menu-item:hover {
    background-color: #fff !important;
}

.root .el-menu-vertical-demo.el-menu .el-menu-item:hover {
    background-color: #fff !important;
}

.root .el-menu-vertical-demo.el-menu .el-submenu i.el-icon-arrow-down::before {
    background: 0 !important;
}
</style>