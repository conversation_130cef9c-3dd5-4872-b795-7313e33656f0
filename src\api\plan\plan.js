import service from '@/utils/request'

// eslint-disable-next-line
const { httpPost, httpGet } = service
const getPCWP1Interface = params => {
    return httpPost({
        url: '/pacwp1/json.rpc',
        params
    })
}
const getPCWP2InterfacePlanById = params => {
    return httpGet({
        url: '/PCWP2/thirdapi/sporadicPurchasePlan/getSporadicPurchasePlanById',
        params
    })
}

const getPlanDetailById = params => {
    return httpGet({
        url: '/materialMall/userCenter/plan/findId',
        params
    })
}
//计划信息修改接口
const updatePlan = params => {
    return httpPost({
        url: '/materialMall/userCenter/plan/update',
        params
    })
}

const getBulkRetailPlanById = params => {
    return httpGet({
        url: '/PCWP2/thirdapi/bulkRetailPlan/getBulkRetailPlanById',
        params
    })
}

const getPCWP2Interface = params => {
    return httpPost({
        url: '/PCWP2/thirdapi/sporadicPurchasePlan/querySporadicPurchasePlanExPage',
        params
    })
}

const getPCWPSynthesizeTemporaryPlan = params => {
    return httpPost({
        url: '/PCWP2/thirdapi/bulkRetailPlan/queryPageBulkRetailPlan',
        params
    })
}

// 物资采购合同接口
const getPCWP1BuyContract = params => {
    return httpPost({
        url: '/pacwp1/buyContract/json.rpc',
        params
    })
}

// 物资采购合同接口
const getPCWP2BuyContract = params => {
    return httpPost({
        url: '/PCWP2/thirdapi/material/contractInfo',
        params
    })
}

const getPCWP2BuyContractDtl = params => {
    return httpPost({
        url: '/PCWP2/thirdapi/material/contractInfoList',
        params
    })
}

// 查询月供计划
const materialMonthSupplyPlanList = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/listByEntity',
        params
    })
}

//完结计划
const closeMaterialMonthSupplyPlanByPlanNo = params => {
    return httpGet({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/closeMaterialMonthSupplyPlanByPlanNo',
        params
    })
}

// //完结计划
// const closeMaterialMonthSupplyPlanByPlanNo = params => {
//     return httpGet({
//         url: '/materialMall/materialMonthSupplyPlan/closeMaterialMonthSupplyPlanByPlanNo',
//         params
//     })
// }

const getPlanDtlOrderQty = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/getPlanDtlOrderQty',
        params
    })
}
const getSynthesizeTemporaryPlanDtial = params => {
    return httpGet({
        url: '/materialMall/userCenter/synthesizeTemporary/getSynthesizeTemporaryPlanDetail',
        params
    })
}

/**
 * 供应商查看月供应计划
 * @param params
 * @returns {*}
 */
const shopMangeMaterialMonthSupplyPlanList = params => {
    return httpPost({
        url: '/materialMall/materialMonthSupplyPlan/shopMange/listByEntity',
        params
    })
}

const secondLevelPlanMonthList = params => {
    return httpPost({
        url: '/materialMall/materialMonthSupplyPlan/shopMange/secondLevelPlanMonthList',
        params
    })
}
const getPlanDtlInfoByPlanNo = params => {
    return httpGet({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/getPlanDtlInfoByPlanNo',
        params
    })
}
/**
 * 二级供应商查看计划详情
 * @param params
 * @returns {*}
 */
const getSecondLevelPlanDtlInfoByPlanNo = params => {
    return httpGet({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/getSecondLevelPlanDtlInfoByPlanNo',
        params
    })
}

/**
 * 取消大宗月供订单到二级供应商
 * @param params
 * @returns {*}
 */
const closePushDtl = params => {
    return httpGet({
        url: '/materialMall/materialMonthSupplyPlanDtl/closePushDtl',
        params
    })
}
/**
 * 导出大宗月供应计划模板
 * @param params
 * @returns {*}
 */
const planExport = params => {
    return httpGet({
        url: '/materialMall/materialMonthSupplyPlan/planExport',
        params,
        responseType: 'blob'
    })
}

/**
 * 导出二级供应商大宗月供应计划模板
 * @param params
 * @returns {*}
 */
const planSecondLevelExport = params => {
    return httpGet({
        url: '/materialMall/materialMonthSupplyPlan/planSecondLevelExport',
        params,
        responseType: 'blob'
    })
}
/**
 * 推送大宗月供订单到二级供应商
 * @param params
 * @returns {*}
 */
const pushPlanToSupplier = params => {
    return httpPost({
        url: '/materialMall/materialMonthSupplyPlanDtl/pushPlanToSupplier',
        params
    })
}
/**
 * 个人中心-查询物资商城的计划列表(1、零星采购计划 2、大宗临购 3、周转材料)
 * @param params
 * @returns {*}
 */
const getPlanList = params => {
    return httpGet({
        url: '/materialMall/userCenter/plan/page',
        params
    })
}
const deletePlanAndDetails = id => {
    return httpPost({
        url: `/materialMall/userCenter/plan/delete_plan_and_details/${id}`
    })
}
const changePlanState = (id, state, params) => {
    return httpPost({
        url: `/materialMall/userCenter/plan/${id}/state/${state}`,
        params
    })
}

const getAuditInfos = planId => {
    return httpGet({
        url: `/materialMall/userCenter/plan/${planId}/audit_infos`
    })
}

const getPlanChangeDtlInfoByPlanNo = params => {
    return httpGet({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/getPlanChangeDtlInfoByPlanNo',
        params
    })
}

const createChangePlanAndPlanDtl = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/createChangePlanAndPlanDtl',
        params
    })
}
const submitMonthPlanOrder = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/submitMonthPlanOrder',
        params
    })
}

const submitSynthesizeTemporaryOrder = params => {
    return httpPost({
        // url: '/materialMall/userCenter/orders/submitSynthesizeTemporaryOrder',
        url: '/materialMall/userCenter/orders/submitSynthesizeTemporaryInterOrder',
        params
    })
}
const batchSubmitPlan = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/batchSubmitPlan',
        params
    })
}
const batchDeletePlan = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/batchDeletePlan',
        params
    })
}

const deletePlanInfo = params => {
    return httpGet({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/deletePlanInfo',
        params
    })
}
const cancellationPlan = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/cancellationPlan',
        params
    })
}
const cancellationChangePlan = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/cancellationChangePlan',
        params
    })
}

const auditPlan = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/auditPlan',
        params
    })
}
const auditChangePlan = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/auditChangePlan',
        params
    })
}
const checkTotalNum = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/checkTotalNum',
        params
    })
}
// 新增计划
const createPlanAndPlanDtl = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/createPlanAndPlanDtl',
        params
    })
}

const updatePlanDtlByPlanId = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/updatePlanDtlByPlanId',
        params
    })
}
const updatePlanChangeDtlByPlanId = params => {
    return httpPost({
        url: '/materialMall/userCenter/materialMonthSupplyPlan/updatePlanChangeDtlByPlanId',
        params
    })
}
// 检查推送计划情况
const checkSubmitPlanProductCondition = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/checkSubmitPlanProductCondition',
        params
    })
}

// 提交订单
const submitPlanAndOrder = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/submitPlanAndOrder',
        params
    })
}

const getPlanById = params => {
    return httpGet({
        url: '/materialMall/plan/findId',
        params
    })
}
const checkAndFetchPlanDetails = planId => {
    return httpGet({
        url: `/materialMall/userCenter/plans/${planId}/order/check`
    })
}
const submitOrder = ( params, planId) => {
    return httpPost({
        url: `/materialMall/userCenter/plans/${planId}/order`,
        params
    })
}
const exportExcel = params => {
    return httpGet({
        url: '/materialMall/userCenter/plan/excel',
        params,
        responseType: 'blob',
    })
}

export {
    getPCWP1Interface,
    getPCWP2InterfacePlanById,
    checkSubmitPlanProductCondition,
    submitPlanAndOrder,
    getPCWP1BuyContract,
    getPCWP2BuyContract,
    materialMonthSupplyPlanList,
    closeMaterialMonthSupplyPlanByPlanNo,
    createPlanAndPlanDtl,
    batchSubmitPlan,
    checkTotalNum,
    getBulkRetailPlanById,
    batchDeletePlan,
    getPCWPSynthesizeTemporaryPlan,
    submitSynthesizeTemporaryOrder,
    getSecondLevelPlanDtlInfoByPlanNo,
    getPlanDtlInfoByPlanNo,
    updatePlanDtlByPlanId,
    cancellationPlan,
    getSynthesizeTemporaryPlanDtial,
    getPlanDtlOrderQty,
    submitMonthPlanOrder,
    createChangePlanAndPlanDtl,
    auditPlan,
    updatePlanChangeDtlByPlanId,
    auditChangePlan,
    getPlanChangeDtlInfoByPlanNo,
    cancellationChangePlan,
    getPCWP2Interface,
    deletePlanInfo,
    pushPlanToSupplier,
    closePushDtl,
    planExport,
    planSecondLevelExport,
    getPCWP2BuyContractDtl,
    shopMangeMaterialMonthSupplyPlanList,
    secondLevelPlanMonthList,
    getPlanList,
    getPlanDetailById,
    updatePlan,
    getPlanById,
    checkAndFetchPlanDetails,
    submitOrder,
    deletePlanAndDetails,
    exportExcel,
    changePlanState,
    getAuditInfos
}