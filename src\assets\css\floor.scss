.main {
	.titleBox {
		height: 80px;

		.left {
			div {
				font-size: 24px;
				font-weight: 400;
				color: rgba(0, 0, 0, 1);
			}

			span {
				font-size: 14px;
				font-weight: 400;
				color: rgba(56, 56, 56, 1);
				padding-top: 8px;
			}
		}

		.right {
			color: #333;
			font-size: 14px;
			cursor: pointer;
		}
	}

	.qyList {
		flex-wrap: wrap;

		& > div {
			width: 250px;
			height: 79px;
			padding: 12px;
			background-color: #fff;
		}

		& > div:not(:nth-of-type(5n)) {
			margin-right: 19px;
		}

		/deep/ .el-image {
			height: 100%;
			margin-right: 15px;
			aspect-ratio: 1/1;
			object-fit: cover;
		}
	}

	.goodList {
		flex-wrap: wrap;

		& > div:last-child {
			width: 1057px;
			flex-wrap: wrap;
		}

		/deep/ .el-image {
			object-fit: cover;
		}

		.floorImg {
			width: 250px;
			height: 620px;
			margin-right: 19px;
			margin-bottom: 20px;
			color: #fff;
			position: relative;

			/deep/ .el-image, .floorTitle, .floorSubTitle {
				position: absolute;
			}

			/deep/ .el-image:first-of-type {
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
			}

			/deep/ .el-image:last-of-type {
				width: 190px;
				margin-left: -95px;
				margin-top: -50%;
				z-index: 1;
				top: 50%;
				left: 50%;
			}

			.floorTitle, .floorSubTitle {
				width: 100%;
				text-align: center;
				z-index: 1;
			}

			.floorTitle {
				font-size: 20px;
				top: 41px;
			}

			.floorSubTitle {
				font-size: 14px;
				top: 74px;
			}
		}

		.goodItem {
			width: 250px;
			height: 300px;
			background: rgba(255, 255, 255, 1);
			flex-direction: column;
			display: flex;
			align-items: center;
			cursor: pointer;
			overflow: hidden;

			&:not(:nth-of-type(4n)) {
				margin-right: 19px;
			}

			img {
				width: 250px;
				height: 200px;
			}

			.name {
				width: 94%;
				font-size: 14px;
				font-weight: 400;
				// text-align: center;
				color: rgba(51, 51, 51, 1);
				margin-left: 3%;
				margin-top: 5px;
			}

			.type {
				margin:  10px 10px 5px;
				font-size: 14px;
				font-weight: 400;
				color: rgba(153, 153, 153, 1);
				align-items: center;
				word-wrap: break-word;
			}

			.price {
				font-size: 12px;width: 90px;
				font-weight: 400;
				color: rgba(212, 48, 48, 1);
				span {font-size: 20px;}
			}
		}
	}
}