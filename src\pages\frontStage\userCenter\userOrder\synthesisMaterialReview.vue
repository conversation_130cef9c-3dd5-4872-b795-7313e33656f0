<template>
  <main class="userCenter" v-loading="showLoading">
    <div class="title">评价晒单</div>
    <div class="content p20">
      <div class="tabs dfb mb10">
        <div class="tab df">
          <div :class="activeTab == 0 ? 'active' : ''" @click="activeTab = 0">待评价({{ commentTotal }})</div>
          <div :class="activeTab == 1 ? 'active' : ''" @click="activeTab = 1">已评价</div>
          <button class="searchBtn" @click="handleReviewPl">评价</button>
        </div>
      </div>
      <div class="titleBar dfa">
        <el-select v-model="selectedVal" value-key="" @change="handleFilter">
          <el-option v-for="item in selectOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
        <span>订单详情</span>
        <span>计划编号</span>
        <span>收货人</span>
        <span>金额</span>
        <span>状态</span>
        <span>操作</span>
        <span>机构信息</span>
      </div>
      <!-- 列表 -->
      <div class="reviewList">
        <div class="item" v-for="item in list" :key="item.orderId">
          <div class="itemHead dfa">
            <span>{{ item.shopName }}</span>
          </div>
          <div class="itemContent df">
            <div class="checkbox"><el-checkbox @change="(checked) => checkChange({ checked, item: item })"></el-checkbox></div>
            <div class="quantity">订单号：{{ item.orderNum }}</div>
            <div :class="item.billNo!=null?'billNo':'billNoK'">{{ item.billNo }}</div>
            <div class="receiver">{{ item.receiver }}</div>
            <div class="price">￥{{ item.price }}</div>
            <div class="status">
              <div class="mb10">
                  <span v-if="item.status == 0">草稿</span>
                  <span v-if="item.status == 1">已提交</span>
                  <span v-if="item.status == 2">待确认</span>
                  <span v-if="item.status == 3">已确认</span>
                  <span v-if="item.status == 4">待签订合</span>
                  <span v-if="item.status == 5">已签合同</span>
                  <span v-if="item.status == 6">待发货</span>
                  <span v-if="item.status == 7">已关闭</span>
                  <span v-if="item.status == 8">发货中</span>
                  <span v-if="item.status == 9">待收货</span>
                  <span v-if="item.status == 10">已完成</span>
              </div>
              <div class="pointer"  style="color: #226fc7;" @click="handleViewDetail(item.orderNum)">订单详情</div>
            </div>
            <div class="actions">
              <div v-if="activeTab != 1" class="mb10" @click="handleReview(item)">评价</div>
              <div v-if="activeTab == 1" class="mb10" @click="deleteProductComment(item)">删除评价</div>
              <div v-if="activeTab == 1" class="mb10" @click="updateProductComment(item)">修改评论</div>
            </div>
            <div class="enterprise">{{ item.enterpriseName }}</div>
          </div>
        </div>
      </div>
      <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"
                  :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange">
      </pagination>
    </div>
    <CommentModal v-if="commentModal.open" class="comment_modal"
    :orderData="commentModal.orderData" @close="commentModal.open = false"
    :orderIds = "commentModal.orderIds" :orderNums = "commentModal.orderNums"
    :createTime = "commentModal.createTime"
    @comment_back="comment_back"/>
  </main>
</template>
<script>
import pagination from '@/pages/frontStage/components/pagination'
import { getUserOrderComment } from '@/api/frontStage/order'
import CommentModal from '@/pages/frontStage/userCenter/userOrder/CommentModal'
import { deleteCommentById } from '@/api/frontStage/productComment'
export default {
    components: { pagination, CommentModal },
    data () {
        return {
            showModal: false,
            showLoading: false,
            commentTotal: 0,
            pagination: {
                currPage: 1, //当前页
                destination: null,
                pageSize: 3, // 显示数量
                totalNum: null,
                totalPage: null,
            },
            activeTab: 0,
            anonymous: true,
            list: [],
            state: 10,
            selectedVal: 0,
            selectOptions: [
                { label: '近一个月订单', value: 0 },
                { label: '近三个月订单', value: 1 },
                { label: '近半年订单', value: 2 },
                { label: '全部订单', value: 3 },
            ],
            isComment: 0,
            page: {
                totalCount: 30,
                currPage: 1,
                pageSize: 3,
            },
            destination: 2,
            commentModal: {
                open: false,
                orderIds: [],
                orderNums: [],
                orderData: {},
                createTime: null,
            },
            orderIdsChecked: [],
            orderNumsChecked: [],
        }
    },
    watch: {
        activeTab (num) {
            if(num == 0) {
                this.state = 10
                this.isComment = 0
            }
            if(num == 1) {
                this.state = 10
                this.isComment = 1
            }
            this.getUserOrderPageListM()
        },
    },
    created () {
        this.getUserOrderPageListM()
    },
    mounted () { },
    methods: {
        // 修改评论
        updateProductComment (item) {
            this.commentModal.open = true
            this.commentModal.orderIds = [item.orderId]
            this.commentModal.orderNums = [item.orderNum]
            this.commentModal.createTime = item.createTime
            this.commentModal.orderData = item
        },
        // 删除评价
        deleteProductComment (item) {
            this.clientPop('info', '您确定要删除该评价吗？', async () => {
                deleteCommentById({ orderId: item.orderId }).then(res => {
                    if(res.code == 200) {
                        this.commentTotal++
                        this.$message.success('删除成功')
                        this.getUserOrderPageListM()
                    }
                })
            })
        },
        // 下拉筛选
        handleFilter () {
            this.getUserOrderPageListM()
        },
        getUserOrderPageListM () {
            let params = {
                page: this.pagination.currPage,
                limit: this.pagination.pageSize,
                state: this.state,
                productType: 13,
            }
            if(this.isComment != null) {
                params.isComment = this.isComment
            }
            if(this.keyword != null) {
                params.keywords = this.keyword
            }
            if(this.selectedVal === 0) {
                let dateObj = this.getLastMonth()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if(this.selectedVal === 1) {
                let dateObj = this.getLast3Month()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if(this.selectedVal === 2) {
                let dateObj = this.getLast6Month()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            this.showLoading = true
            getUserOrderComment(params).then(res => {
                this.list = []
                res.list.forEach(t => {
                    this.list.push({
                        shopName: t.shopName,
                        orderId: t.orderId,
                        orderNum: t.orderSn,
                        title: t.untitled,
                        quantity: t.buyCounts,
                        receiver: t.receiverName,
                        price: t.actualAmount,
                        status: t.state,
                        createTime: t.gmtCreate,
                        enterpriseName: t.enterpriseName,
                        billNo: t.billNo,
                    })
                })
                if(this.isComment == 0) {
                    this.commentTotal = res.totalCount
                }
                this.pagination.currPage = res.currPage
                this.pagination.pageSize = res.pageSize
                this.pagination.totalNum =  res.totalCount
                this.pagination.totalPage =  res.totalPage
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        getLastMonth () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 1 <= 0) { //如果是1月，年数往前推一年<br>
                dateObj.last = (year - 1) + '-' + 12 + '-' + day
            }else{
                let lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate()
                if(lastMonthDay < day) {    // 1个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数
                        dateObj.last = year + '-' + (month - 1) + '-' + (lastMonthDay - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 1) + '-' + lastMonthDay
                    }
                }else{
                    dateObj.last = year + '-' + (month - 1) + '-' + day
                }
            }
            return dateObj
        },
        getLast3Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 3 <= 0) { // 如果是1、2、3月，年数往前推一年
                var last3MonthDay1 = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate()    // 3个月前所在月的总天数
                if(last3MonthDay1 < day) {    // 3个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + last3MonthDay1
                }else{
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + day
                }
            }else{
                let last3MonthDay2 = new Date(year, (parseInt(month) - 3), 0).getDate()    //3个月前所在月的总天数
                if(last3MonthDay2 < day) {    //3个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 3) + '-' + (last3MonthDay2 - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 3) + '-' + last3MonthDay2
                    }
                }else{
                    dateObj.last = year + '-' + (month - 3) + '-' + day
                }
            }
            return dateObj
        },
        getLast6Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 6 <= 0) { // 年数往前推一年
                let last6MonthDay1 = new Date((year - 1), (12 - (6 - parseInt(month))), 0).getDate()    // 6个月前所在月的总天数
                if(last6MonthDay1 < day) {    // 6个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + last6MonthDay1
                }else{
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + day
                }
            }else{
                let last6MonthDay2 = new Date(year, (parseInt(month) - 6), 0).getDate()    //6个月前所在月的总天数
                if(last6MonthDay2 < day) {    //6个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 6) + '-' + (last6MonthDay2 - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 6) + '-' + last6MonthDay2
                    }
                }else{
                    dateObj.last = year + '-' + (month - 6) + '-' + day
                }
            }
            return dateObj
        },
        // 评价订单
        handleReview (item) {
            this.commentModal.open = true
            this.commentModal.orderIds = [item.orderId]
            this.commentModal.orderNums = [item.orderNum]
            this.commentModal.createTime = item.createTime
            this.commentModal.orderData = {}
        },
        handleReviewPl () {
            if(this.orderIdsChecked == null || this.orderIdsChecked.length === 0) {
                this.$message({ message: '未选中数据', type: 'info' })
                return
            }
            this.commentModal.open = true
            this.commentModal.orderIds = this.orderIdsChecked
            this.commentModal.orderNums = this.orderNumsChecked
            if(this.commentModal.orderIds.length > 1) {
                this.commentModal.createTime = null
            }
            this.commentModal.orderData = {}
        },
        comment_back (isEdit) {
            if(isEdit) {
                this.getUserOrderPageListM()
            }
            this.commentModal.open = false
            this.orderIdsChecked == []
            this.commentModal.orderIds = []
            this.commentModal.orderNums = []
        },
        // 修改单个订单选择的方法
        checkChange ({ item, checked }) {
            if (checked) {
                this.orderIdsChecked.push(item.orderId)
                this.orderNumsChecked.push(item.orderNum)
                if(this.orderIdsChecked.length == 1) {
                    this.commentModal.createTime = item.createTime
                }
            }else{
                this.orderIdsChecked.filter(item => item !== item.orderId)
                this.orderNumsChecked.filter(item => item !== item.orderNum)
            }
        },
        // 跳转订单详情页面
        handleViewDetail (id) {
            this.$router.push({ path: '/user/orderPlanDetail', query: { orderSn: id } })
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getUserOrderPageListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getUserOrderPageListM()
        },
        checkBox (status) {
            console.log(status)
        },
    },
}
</script>
<style scoped lang="scss">
main {
  height: 874px;
  border: 1px solid rgba(230, 230, 230, 1);
}
.searchBtn {
  height: 30px;
  text-align: center;
  line-height: 30px;
  background: #216ec6;
  width: 60px;
  color: #fff;
  margin-left: 850px;
}
.titleBar {
  height: 50px;
  padding: 0 20px;
  border: 1px solid rgba(230, 230, 230, 1);
  background-color: rgba(250, 250, 250, 1);
  /deep/ .el-select {
    //margin-right: 54px;
    &, .el-input {width: 104px;}
    .el-input__inner {
      padding-left: 0;
      padding-right: 20px;
      border: 0;
      color: rgba(0, 0, 0, 1);
    }
    .el-select__caret {
      background-image: url(../../../../assets/images/userCenter/arrow_up.png);
      background-position: 50% 50%;
      background-repeat: no-repeat;
      &::before{content: '';}
    }
  }
  span:nth-of-type(1) {margin-right: 200px !important;}
  span:nth-of-type(2) {margin-right: 140px !important;}
  span:not(:last-of-type) {margin-right: 80px;}
}

.reviewList {
  height: 576px;
  .item {
    border: 1px solid rgba(230, 230, 230, 1);
    .checkbox{
      padding: 0 10px 0 10px;
    }
    .itemHead {
      height: 40px;
      padding: 0 20px;
      border-bottom: 1px solid rgba(230, 230, 230, 1);
      color: rgba(51, 51, 51, 1);
      background-color: rgba(250, 250, 250, 1);

      span {
        margin-right: 24px;
      }
    }

    .itemContent {
      height: 140px;
      padding: 30px 0px;
      img {
        width: 80px;
        height: 80px;
        margin-right: 16px;
      }

      p {
        width: 236px;
        margin-right: 20px;
        color: rgba(51, 51, 51, 1);
      }

      .quantity {
        margin-right: 32px;
        display: inline-block;
      }
      .billNo {margin-right: 32px;}
      .billNoK {margin-right: 315px;}
      .receiver, .price, .enterprise, .status {width: 100px;margin-right: 11px;}
      .actions {
        width: 110px;
        div {cursor: pointer; }
        .mb10 {color: rgba(34, 111, 199, 1); }
      }
    }
  }
}
.comment_modal{}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>