<template>
  <div class="base-page">
    <div class="e-form" >
      <BillTop @cancel="handleClose"></BillTop>
      <div class="tabs warningTabs" style="padding-top: 70px;">
        <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
          <el-tab-pane label="基本信息" name="accountStatement" :disabled="clickTabFlag">
          </el-tab-pane>
          <el-tab-pane label="对账清单" name="accountStatementDtl" :disabled="clickTabFlag">
          </el-tab-pane>
          <div id="tabs-content">
            <!-- 基本信息 -->
            <div id="columnInfoCon" class="con">
              <div class="tabs-title" id="accountStatement">基本信息</div>
              <div style="width: 100%" class="form">
                <el-form :model="formDate" :label-width="formLabelWidth" ref="formEdit2"
                         class="demo-ruleForm">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="对账单编号：" >
                        <span>{{ formDate.billNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="公司名称：" prop="orgName">
                        <span>{{ formDate.orgName }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="收料单编号：" prop="billNo">
                        <span>{{ formDate.billNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="收料人员名称：" prop="orgName">
                        <span>{{ formDate.receiverName }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="经办人名称：" prop="billNo">
                        <span>{{ formDate.picker }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="金额合计：" prop="orgName">
                        <span>{{ formDate.totalAmount }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>

<!--                  <el-row>-->
<!--                    <el-col :span="12">-->
<!--                      <el-form-item label="对账单来源：" >-->
<!--                        <span v-show=" formDate.accountSource==1">收料</span>-->
<!--                        <span v-show=" formDate.accountSource==2">退货</span>-->
<!--                      </el-form-item>-->
<!--                    </el-col>-->
<!--                    <el-col   v-if="formDate.accountSource==1" :span="12">-->
<!--                      <el-form-item   label="收料日期：" prop="orgName">-->
<!--                        <span>{{ formDate.generationDate }}</span>-->
<!--                      </el-form-item>-->
<!--                    </el-col>-->
<!--                      <el-col  v-if="formDate.accountSource==2" :span="12">-->
<!--                      <el-form-item  label="退货日期：" prop="orgName">-->
<!--                        <span>{{ formDate.generationDate }}</span>-->
<!--                      </el-form-item>-->
<!--                    </el-col>-->
<!--                  </el-row>-->
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="对账日期：" prop="orgName">
                        <span>{{ formDate.billDate }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>

                </el-form>
              </div>
            </div>
            <!--对账清单-->
            <div id="floorInfoCon" class="con">
              <div class="tabs-title" id="accountStatementDtl">对账清单</div>
              <div class="e-table" style="background-color: #ffffff">
                <div class="top">
                  <!-- 新增按钮 -->
                  <div class="left">
                    <div class="left-btn">
                    </div>
                  </div>
                  <div class="search_box">
                    <el-input clearable type="text" @keyup.enter.native="onSearch" placeholder="物资名称/物资类别名称" v-model="keywords">
                      <img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
                    <div class="adverse">
                      <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                    </div>
                  </div>
                </div>
                <div>
                  <el-table @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" v-loading="isLoading" border style="width: 100%" :data="accountStatementDtlTable" class="accountStatementDtlTable" :max-height="$store.state.tableHeight"
                            @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="物资名称" width="100" prop="itemName"></el-table-column>
                    <el-table-column label="类别名称" width="100" prop="itemClassname"></el-table-column>
                    <el-table-column label="规格型号" width="100" prop="itemModel"></el-table-column>
                    <el-table-column label="材质" width="100" prop="caiZhi"></el-table-column>
                    <el-table-column label="计量单位" width="100" prop="itemUnit"></el-table-column>
                    <el-table-column label="收料数量" width="100" prop="qty"></el-table-column>
                    <el-table-column label="挂牌价" width="100" prop="listingPrice"></el-table-column>
                    <el-table-column label="下浮费用" width="100" prop="floatingPrice"></el-table-column>
                    <el-table-column label="固定费" width="100" prop="fixedPrice"></el-table-column>
                    <el-table-column label="合计单价" width="100" prop="price"></el-table-column>
                    <el-table-column label="合计金额" width="100" prop="amount"></el-table-column>
                    <el-table-column label="卸货地址" width="100" prop="address"></el-table-column>
                  </el-table>
                </div>
                <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                               @currentChange="currentChange" @sizeChange="sizeChange" />
              </div>
            </div>
          </div>
        </el-tabs>
      </div>
      <div class="buttons" v-if="!isview">
          <el-button  type="primary" @click="queueAccount(1
          )">通过</el-button>
          <el-button type="warning" @click="queueAccount(0)">不通过</el-button>
        <el-button @click="handleClose">返回</el-button>
      </div>
    </div>
    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="55%" :close-on-click-modal="true" :before-close="closeDialog">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
        <el-row>
          <el-col :span="12">
            <el-form-item width="150px" label="物资名称：" prop="orgName">
              <el-input clearable v-model="filterData.materialName" placeholder="请输入物资名称" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item width="50px" label="物资类别名称：" prop="materialClassName">
              <el-input clearable v-model="filterData.materialClassName" placeholder="请输入物资类别名称" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item width="150px" label="材质：" prop="orgName">
              <el-input clearable  v-model="filterData.texture" placeholder="材质" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item width="150px" label="规格型号：" prop="orgName">
              <el-input clearable v-model="filterData.spec" placeholder="规格型号" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="收料数量以上：">
              <el-input clearable type="number" v-model="filterData.tallquantity" placeholder="请输入收料数量以上" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="收料数量以下：">
              <el-input clearable type="number" v-model="filterData.lowquantity" placeholder="请输入收料数量以下" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" :offset="0">
            <el-form-item label="合同最低价格以上：">
              <el-input clearable type="number" v-model="filterData.lowTotalAmount" placeholder="请输入价格区间" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="合同最高价格以下：">
              <el-input clearable type="number" v-model="filterData.tallTotalAmount" placeholder="请输入价格区间" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import $ from 'jquery'
import { getDataDtlList, changAccountState } from '@/api/supplierSys/accountStatement/pcwp1/account'
import ComPagination from '@/components/pagination/pagination.vue'
export default {
    components: {
        ComPagination
    },
    data () {
        return {
            accountStatementDtlTable: [],
            pages: {
                currPage: 1,
                pageSize: 20,
                totalCount: 20
            },
            isLoading: false,
            keywords: '',
            // 表格数据
            // 高级查询数据对象
            filterData: {
                materialClassName: '',
                spec: '',
                texture: '',
                materialName: '',
                tallAtotalAmount: '',
                lowAtotalAmount: '',
                lowquantity: '',
                tallquantity: '',
            },
            queryVisible: false,
            //选中数据
            selectedRows: [],
            formLabelWidth: '195px',
            tabsName: 'columnInfo',
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            disabled: false, //是否禁止
            isview: false, //是否隐藏底部按钮
            formDate: {},
        }
    },
    created () {
    //接收上一个页面传递的参数
        this.formDate =  this.$route.params.row
        this.getTableDtlData()
    },
    async mounted () {
    },
    computed: {
    },
    watch: {
    },
    methods: {
        ///高级查询确定
        advancedQuery () {
            this.keywords == null
            this.getTableDtlData()
            this.hideDialog
        },
        //高级查询返回
        hideDialog () {
            this.filterData = {
                materialClassName: '',
                spec: '',
                texture: '',
                materialName: '',
                tallAtotalAmount: '',
                lowAtotalAmount: '',
                lowquantity: '',
                tallquantity: '',
            },
            this.queryVisible = false
        },
        // 消息提示
        closeDialog (done) {
            this.filterData = {
                materialClassName: '',
                spec: '',
                texture: '',
                materialName: '',
                tallAtotalAmount: '',
                lowAtotalAmount: '',
                lowquantity: '',
                tallquantity: '',
            },
            done()
        },
        message (res) {
            if(res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }else {
                this.$message({
                    message: res.message,
                    type: 'error'
                })
            }
        },
        onSearch () {
            this.emptyForm()
            this.getTableDtlData()
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 获取列表数据
        async getTableDtlData () {
            this.isLoading = true
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                billId: this.formDate.billId
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords}
            if (this.filterData.spec != null && this.filterData.spec != '') {
                params.spec = this.filterData.spec
            }
            if (this.filterData.materialClassName != null && this.filterData.materialClassName != '') {
                params.materialClassName = this.filterData.materialClassName
            }
            if (this.filterData.texture != null && this.filterData.texture != '') {
                params.texture = this.filterData.texture
            }
            if (this.filterData.materialName != null && this.filterData.materialName != '') {
                params.materialName = this.filterData.materialName
            }
            if (this.filterData.tallAtotalAmount != null && this.filterData.tallAtotalAmount != '') {
                params.tallAtotalAmount = this.filterData.tallAtotalAmount
            }
            if (this.filterData.lowAtotalAmount != null && this.filterData.lowAtotalAmount != '') {
                params.lowAtotalAmount = this.filterData.lowAtotalAmount
            }
            if (this.filterData.lowquantity != null && this.filterData.lowquantity != '') {
                params.lowquantity = this.filterData.lowquantity
            }
            if (this.filterData.tallquantity != null && this.filterData.tallquantity != '') {
                params.tallquantity = this.filterData.tallquantity
            }
            getDataDtlList(params).then(res => {
                this.accountStatementDtlTable = res.list
                console.log('this.accountStatementDtlTable', this.accountStatementDtlTable)
                this.isLoading = false
                this.pages = res
            }).catch(() => {
                this.isLoading = false
            })
            this.viewList = true
        },

        currentChange (vaule) {
            this.pages.currPage = vaule
            this.getTableDtlData()
        },
        sizeChange (vaule) {
            this.pages.pageSize = vaule
            this.getTableDtlData()
        },

        //取消
        handleClose () {
            this.$router.go(-1)
        },
        queueAccount (shopState) {
            let params = {
                billId: this.formDate.billId,
                shopState: shopState
            }
            changAccountState(params).then(res=>{
                if (res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'warning',
                    })
                }
            })
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            // console.log($('.tabs-content'))
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
    }
}
</script>

<style lang='scss' scoped>

.e-table {
  min-height: auto;
  background: #fff;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-tabs__content {
  // overflow: hidden;
  &::-webkit-scrollbar {
    width: 0;
  }
}
/deep/ .e-form .el-form-item .el-form-item__content {
  height: unset !important;
  display: flex;
  align-items: center;
}

// upload
.avatar-uploader {
  /deep/ .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}
.avatar-uploader {
  /deep/ .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}

/deep/ .avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
/deep/ .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.btns-box {
  padding: 6px 4px;
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
</style>

