<template>
    <div class="root">
        <div class="progress box center mb20">
            <div class="list-title dfa">订单详情</div>
            <div class="content">
                <div class="progress-box df">
                    <div class="step dfa">
                        <img src="../../../../assets/images/userCenter/确认订单.png" alt="">
                        <span>确认订单信息</span>
                    </div>
                    <img src="../../../../assets/images/userCenter/进度.png" alt="">
                    <div class="step dfa">
                        <img src="../../../../assets/images/userCenter/关联任务.png" alt="">
                        <span>关联计划单</span>
                    </div>
                    <img src="../../../../assets/images/userCenter/进度.png" alt="">
                    <div class="step dfa">
                        <img src="../../../../assets/images/userCenter/发货.png" alt="">
                        <span>通知供方发货</span>
                    </div>
                    <img src="../../../../assets/images/userCenter/进度.png" alt="">
                    <div class="step dfa">
                        <img src="../../../../assets/images/userCenter/收货成功.png" alt="">
                        <span>确认收货</span>
                    </div>
                    <img src="../../../../assets/images/userCenter/进度.png" alt="">
                    <div class="step dfa">
                        <img src="../../../../assets/images/userCenter/财务结算.png" alt="">
                        <span>系统结算</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="sheet box center mb20">
            <div class="list-title dfa">采购计划单</div>
            <div class="content p20">
                <el-table :data="purchaseSheet" :span-method="objectSpanMethod" border>
                    <el-table-column prop="id" label="计划单编号" width="377"></el-table-column>
                    <el-table-column prop="name" label="装备名称"></el-table-column>
                    <el-table-column prop="quantity" label="采购数量" width="232"></el-table-column>
                </el-table>

            </div>
        </div>
        <div class="order-info box center">
            <div class="list-title dfa">订单信息</div>
            <div class="content p20">
                <h3>卖家信息</h3>
                <div class="info">
                    <el-row>
                        <el-col :span="6" :offset="0">昵称：{{sellerInfo.shopName}}</el-col>
                        <el-col :span="6" :offset="0">真实姓名：{{sellerInfo.realName}}</el-col>
                        <el-col :span="6" :offset="0">城市：{{sellerInfo.city}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" :offset="0">联系电话：{{sellerInfo.tel}}</el-col>
                    </el-row>
                </div>
            </div>
            <div class="content order p20">
                <h3>订单信息</h3>
                <div class="info">
                    <el-row>
                        <el-col :span="6" :offset="0">订单编号：{{orderInfo.id}}</el-col>
                        <el-col :span="6" :offset="0">关联的计划单号：{{orderInfo.planId}}</el-col>
                        <el-col :span="6" :offset="0">支付方式：{{orderInfo.payMethod}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" :offset="0">确认时间：{{orderInfo.time}}</el-col>
                        <el-col :span="6" :offset="0">确认时间：{{orderInfo.confirmTime}}</el-col>
                    </el-row>
                </div>
            </div>
            <div class="p20 order-list">
                <el-table :data="orderList">
                    <el-table-column label="商品清单" width="793">
                        <template v-slot="scope">
                            <div class="info df">
                                <img :src="scope.row.img" alt="">
                                <div class="detail">
                                    <h4>{{ scope.row.name }}</h4>
                                    <div>品牌：{{ scope.row.brand }}</div>
                                    <div>规格：{{ scope.row.specs }}</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="单价" width="194">
                        <template v-slot="scope">
                            <div class="price">
                                <div>￥{{ scope.row.price }}</div>
                                <div>单位：{{ scope.row.unit }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="数量" width="149">
                        <template v-slot="scope">
                            <div class="num">
                                x{{scope.row.quantity}}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="总价">
                        <template v-slot="scope">
                            <div class="total">￥{{scope.row.total}}</div>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="sum mt20">商品合计：¥{{totalPrice}}</div>
            </div>
            <div class="logistic p20">
                <h3>订单信息</h3>
                <div class="info">
                    <el-row>
                        <el-col :span="24" :offset="0">收货地址：{{orderInfo.reciever}}  {{orderInfo.tel}}  {{orderInfo.addr}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" :offset="0">运送方式：{{orderInfo.shipMethod}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" :offset="0">物流公司：{{orderInfo.company}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" :offset="0">物流单号：{{orderInfo.sheetId}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" :offset="0">备注：{{orderInfo.remark}}</el-col>
                    </el-row>
                </div>
            </div>
        </div>
        <div class="confirm-btn"><button @click="handleConfirm">确认</button></div>
        <publicity></publicity>
    </div>
</template>
<script>
import publicity from '../../components/publicity.vue'
export default {
    components: { publicity },
    data () {
        return {
            sellerInfo: {
                shopName: '昵称：蛮蛮的店铺',
                realName: '',
                city: '成都',
                tel: '12312312312',
            },
            orderInfo: {
                id: '12364897745',
                planId: 'P000001',
                payMethod: '路桥结算',
                time: '2022-11-21 12:00:00',
                confirmTime: '2022-11-21 12:04:00',
                reciever: '蛮蛮',
                tel: '189***4589',
                addr: '四川省成都市武侯区',
                shipMethod: '物流',
                company: '邮政',
                sheetId: '1264589',
                remark: ''
            },
            purchaseSheet: [
                {
                    id: 'P00001',
                    name: '品牌挖掘机型号KH3189091功能介绍特色折扣价格优惠',
                    quantity: 5,
                },
                {
                    id: 'P00002',
                    name: '品牌挖掘机型号KH3189091功能介绍特色折扣价格优惠',
                    quantity: 5,
                },
                {
                    id: 'P00002',
                    name: '品牌挖掘机型号KH3189091功能介绍特色折扣价格优惠',
                    quantity: 5,
                },
                {
                    id: 'P00002',
                    name: '品牌挖掘机型号KH3189091功能介绍特色折扣价格优惠',
                    quantity: 5,
                },
                {
                    id: 'P00003',
                    name: '品牌挖掘机型号KH3189091功能介绍特色折扣价格优惠',
                    quantity: 5,
                },
            ],
            orderList: [
                {
                    img: '',
                    name: '挖掘机',
                    brand: '中联',
                    specs: '不详',
                    price: '130000.00',
                    unit: '台',
                    quantity: 1,
                    total: '130000.00'
                },
            ],
            totalPrice: '130000.00',
            spanArr: [],
            pos: null
        }
    },
    methods: {
        handleConfirm () {},
        // 表格合并行的方法
        getSpanArr (data) {
            this.spanArr = []
            for (var i = 0; i < data.length; i++) {
                if (i === 0) {
                    this.spanArr.push(1)  //空数组，用来记录需要合并的行数
                    this.pos = 0  //标识
                } else {
                    // 判断当前元素与上一个元素是否相同⬇️
                    if (data[i].id === data[i - 1].id) {
                        this.spanArr[this.pos] += 1
                        this.spanArr.push(0)    //相同则上一行合并数+1，本行合并数为0
                    } else {
                        this.spanArr.push(1)   //不相同则另起一行
                        this.pos = i
                    }
                }
            }
        },
        objectSpanMethod ({ rowIndex, columnIndex }) {
            if (columnIndex === 0) {    //如果是第一列
                const _row = this.spanArr[rowIndex]   //将每一行传入上述方法，获取到每一行的合并行数
                const _col = _row > 0 ? 1 : 0
                return {
                    rowspan: _row,   //合并行
                    colspan: _col    //合并列
                }
            }
        },
    },
    created () {
        this.getSpanArr(this.purchaseSheet)
    }
}
</script>
<style scoped lang="scss">
.root {
    height: 100%;
    padding-top: 20px;
    background-color: #f5f5f5;

    .box {
        width: 1326px;
        background-color: #fff;
    }
}

.list-title {
    height: 50px;
    padding: 15px 20px;
    font-size: 18px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.progress {
    .content {
        padding: 40px 20px;
    }

    .progress-box {
        padding: 0 40px;
        align-items: flex-start;
        justify-content: center;

        &>img {
            margin-top: 6px;
        }

        .step {
            min-width: 68px;
            flex-direction: column;

            img {
                width: 28px;
                height: 28px;
                margin-bottom: 20px;
            }
        }
    }
}

.sheet {
    /deep/ .el-table {
        border: 1px solid rgba(230, 230, 230, 1);

        thead th {
            height: 45px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            background-color: rgba(247, 247, 247, 1);
        }

        .el-table__body {
            .el-table__row {
                height: 46px;
            }
        }

        .el-table-cell {
            color: rgba(51, 51, 51, 1);
        }
    }
}

.order-info {
    margin-bottom: 20px;

    h3 {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 500;
    }

    .el-row {
        margin-bottom: 16px;
        font-size: 14px;
        color: rgba(51, 51, 51, 1);
    }

    .content:not(.order) {
        border-bottom: 1px solid rgba(230, 230, 230, 1);
    }
    .sum {
        font-size: 14px;
        text-align: right;
        color: rgba(51, 51, 51, 1);
    }
    .order-list {
        border-bottom: 1px solid rgba(230, 230, 230, 1);
    }
}
.confirm-btn {
    padding-bottom: 40px;
    text-align: center;
    button {
        width: 150px;
        height: 40px;
        color: #fff;
        font-size: 18px;
        background-color: rgba(212, 48, 48, 1);
    }
}
</style>