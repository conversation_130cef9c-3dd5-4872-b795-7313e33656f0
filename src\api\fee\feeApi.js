import service from '@/utils/request'

// eslint-disable-next-line no-unused-vars
const { httpPost, httpGet } = service

const myQueryListByEntity = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/myQueryListByEntity',
        params
    })
}
const myQueryPayFreeListByEntity = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/myQueryPayFreeListByEntity',
        params
    })
}
const platformDealFeeList = params => {
    return httpPost({
        url: '/materialMall/platform/platformDealFeeRecord/listByEntity',
        params
    })
}
const supplierDealFeeList = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFeeRecord/listByEntity',
        params
    })
}
const feeManagementList = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/feeManagementList',
        params
    })
}
const queryByShopName = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/queryByShopName',
        params
    })
}
const platformBalanceOperateBatchQuery = params => {
    return httpPost({
        url: '/materialMall/platformBalanceOperate/batchQuery',
        params
    })
}
const platformDealRecordList = params => {
    return httpPost({
        url: '/materialMall/platform/platformDealFeeRecord/listByEntity',
        params
    })
}
const supplierPlatformDealFee = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFee/supplierListByEntity',
        params
    })
}

/**
 * 平台查看交易记录
 * @param params
 * @returns {*}
 */
const platformDealFeeJYList = params => {
    return httpPost({
        url: '/materialMall/platform/platformDealFee/listByEntity',
        params
    })
}

/**
 * 平台查询平台年费表
 * @param params
 * @returns {*}
 */
const platformYearFeeList = params => {
    return httpPost({
        url: '/materialMall/platformYearFee/listByEntity',
        params
    })
}
const platformTotalCountFree = params => {
    return httpPost({
        url: '/materialMall/platformYearFee/platform/totalCountFree',
        params
    })
}
const supplierYearFeeList = params => {
    return httpPost({
        url: '/materialMall/platformYearFee/supplier/listByEntity',
        params
    })
}
/**
 * 供应商查询平台年费表
 * @param params
 * @returns {*}
 */
const supplierPlatformYearFeeList = params => {
    return httpPost({
        url: '/materialMall/platformYearFee/supplier/listByEntity',
        params
    })
}

const findYearFeeBySn = params => {
    return httpGet({
        url: '/materialMall/platformYearFee/findBySn',
        params
    })
}
const platformFindYearFeeBySn = params => {
    return httpGet({
        url: '/materialMall/platformYearFee/platform/findBySn',
        params
    })
}

const platformQueryListByEntity = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/platformQueryListByEntity',
        params
    })
}
const platformQueryYearAndDealFreeListByEntity = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/platformQueryYearAndDealFreeListByEntity',
        params
    })
}
const platformDealFeeRecordDtlListByEntity = params => {
    return httpPost({
        url: '/materialMall/platform/platformDealFeeRecordDtl/listByEntity',
        params
    })
}
const supplierDealFeeRecordDtlListByEntity = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFeeRecordDtl/listByEntity',
        params
    })
}
const supplierCreateFee = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/supplier/createFee',
        params
    })
}
const supplierCreateOrUpdateFee = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord',
        params
    })
}
const createFeeAndDealFree = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/supplier/createFeeAndDealFree',
        params
    })
}
const supplierCreateDealFee = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFeeRecord/create',
        params
    })
}
const supplierChangeDealFeeState = (id, state, params) => {
    return httpPost({
        url: `/materialMall/supplier/platformDealFeeRecord/${id}/state/${state}`,
        params
    })
}
const supplierFetchDealFeeFile = (id, type) => {
    return httpGet({
        url: `/materialMall/supplier/platformDealFeeRecord/${id}/file/${type}`
    })
}
const supplierFetchSystemParams = () => {
    return httpGet({
        url: '/materialMall/supplier/platformDealFeeRecord/system_param'
    })
}
const supplierFetchAudits = id => {
    return httpGet({
        url: `/materialMall/supplier/platformDealFeeRecord/${id}/audits`
    })
}
const supplierFetchDealRecons = id => {
    return httpGet({
        url: `/materialMall/supplier/platformDealFeeRecord/${id}/recons`
    })
}
const findBySn = params => {
    return httpGet({
        url: '/materialMall/platformYearFeeRecord/findBySn',
        params
    })
}
const findDealFeeBySn = params => {
    return httpGet({
        url: '/materialMall/platformDealFeeRecord/findBySn',
        params
    })
}

const findDealFeeDateBySn = params => {
    return httpGet({
        url: '/materialMall/platformDealFee/findBySn',
        params
    })
}

const getDealFeeDateList = params => {
    return httpPost({
        url: '/materialMall/platform/platformDealFeeRecordDtl/listByEntity',
        params
    })
}

const updateFee = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/supplier/updateFee',
        params
    })
}
const platformDealFeeRecordUpdate = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFeeRecord/update',
        params
    })
}
const deleteFee = params => {
    return httpGet({
        url: '/materialMall/platformYearFeeRecord/delete',
        params
    })
}
const deleteDealFeeRecord = params => {
    return httpGet({
        url: '/materialMall/supplier/platformDealFeeRecord/deleteDealFeeRecord',
        params
    })
}
const platformDealFeeRecordDtlBatchDelete = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFeeRecordDtl/batchDelete',
        params
    })
}
const auditFee = params => {
    return httpPost({
        url: '/materialMall/platformYearFeeRecord/platform/auditFee',
        params
    })
}
const reCreateContract = params => {
    return httpGet({
        url: '/materialMall/platformYearFeeRecord/reCreateContract',
        params
    })
}
const platformDealFeeRecordAudit = params => {
    return httpPost({
        url: '/materialMall/platform/platformDealFeeRecord/audit',
        params
    })
}
const platformDealFeeRecordDtlBatchCreate = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFeeRecordDtl/batchCreate',
        params
    })
}
const platformExportExcelFreeDtl = params => {
    return httpPost({
        url: '/materialMall/platform/platformDealFeeRecordDtl/exportExcelFreeDtl',
        params,
        responseType: 'blob'
    })
}
const supplierExportExcelFreeDtl = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFeeRecordDtl/exportExcelFreeDtl',
        params,
        responseType: 'blob'
    })
}
const platformExportExcelYearFree = params => {
    return httpPost({
        url: '/materialMall/platformYearFee/platform/exportExcelYearFree',
        params,
        responseType: 'blob'
    })
}
const supplierExportExcelYearFree = params => {
    return httpPost({
        url: '/materialMall/platformYearFee/supplier/exportExcelYearFree',
        params,
        responseType: 'blob'
    })
}
const notPayDealFeeDtlList = params => {
    return httpPost({
        url: '/materialMall/supplier/platformDealFeeDtl/notPayDealFeeDtlList',
        params
    })
}

const getRelevanceFile = (type, relevanceId) => {
    return httpGet({
        url: `/materialMall/platformFeeFile/relevance/${type}/${relevanceId}`
    })
}
export {
    myQueryListByEntity,
    platformDealFeeRecordUpdate,
    supplierCreateFee,
    supplierCreateOrUpdateFee,
    supplierCreateDealFee,
    supplierDealFeeList,
    feeManagementList,
    queryByShopName,
    platformBalanceOperateBatchQuery,
    platformDealFeeRecordAudit,
    supplierPlatformDealFee,
    platformDealFeeJYList,
    platformExportExcelFreeDtl,
    platformYearFeeList,
    supplierPlatformYearFeeList,
    platformDealFeeRecordDtlBatchCreate,
    platformDealFeeList,
    platformDealRecordList,
    myQueryPayFreeListByEntity,
    notPayDealFeeDtlList,
    findBySn,
    platformDealFeeRecordDtlBatchDelete,
    platformQueryListByEntity,
    deleteFee,
    platformExportExcelYearFree,
    platformFindYearFeeBySn,
    supplierDealFeeRecordDtlListByEntity,
    platformTotalCountFree,
    supplierYearFeeList,
    deleteDealFeeRecord,
    platformDealFeeRecordDtlListByEntity,
    auditFee,
    createFeeAndDealFree,
    supplierExportExcelYearFree,
    platformQueryYearAndDealFreeListByEntity,
    findDealFeeBySn,
    findDealFeeDateBySn,
    supplierExportExcelFreeDtl,
    findYearFeeBySn,
    updateFee,
    getDealFeeDateList,
    getRelevanceFile,
    supplierChangeDealFeeState,
    supplierFetchDealFeeFile,
    reCreateContract,
    supplierFetchSystemParams,
    supplierFetchAudits,
    supplierFetchDealRecons,
}
