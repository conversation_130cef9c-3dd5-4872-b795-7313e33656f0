import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

// 招标公告模板分页
const biddingAnnouncementList = params => {
    return httpPost({
        url: '/tender/basePublic/listByEntity',
        params
    })
}
//招标公告模板根据主键查询
const biddingAnnouncementBase = params => {
    return httpGet({
        url: `/tender/basePublic/findById/${params}`,
    })
}
//招标公告模板根据id删除
const biddingAnnouncementDelete = params => {
    return httpGet({
        url: `/tender/basePublic/delete/${params}`,
        params
    })
}
//招标公告模板新增
const biddingAnnouncementAdd = params => {
    return httpPost({
        url: '/tender/basePublic/create',
        params
    })
}
//招标公告模板修改
const biddingAnnouncementUpdate = params => {
    return httpPost({
        url: '/tender/basePublic/update',
        params
    })
}

//中标通知书模板分页
const bidWinningNoticeList = params => {
    return httpPost({
        url: '/tender/baseBid/listByEntity',
        params
    })
}
//中标通知书根据主键查询
const bidWinningNoticeBase = params => {
    return httpGet({
        url: `/tender/baseBid/findById/${params}`,
    })
}
//中标通知书根据id删除
const bidWinningNoticeDelete = params => {
    return httpGet({
        url: `/tender/baseBid/delete/${params}`,
        params
    })
}
//中标通知书新增
const bidWinningNoticeAdd = params => {
    return httpPost({
        url: '/tender/baseBid/create',
        params
    })
}
//中标通知书修改
const bidWinningNoticeUpdate = params => {
    return httpPost({
        url: '/tender/baseBid/update',
        params
    })
}
export {
    biddingAnnouncementList,
    biddingAnnouncementBase,
    biddingAnnouncementDelete,
    biddingAnnouncementAdd,
    biddingAnnouncementUpdate,
    bidWinningNoticeList,
    bidWinningNoticeBase,
    bidWinningNoticeDelete,
    bidWinningNoticeAdd,
    bidWinningNoticeUpdate
}
