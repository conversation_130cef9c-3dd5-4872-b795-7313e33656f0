<template>
    <div class="page" v-loading="menuLoading">
        <div class="full" style="padding-right: 11px;">
            <div class="menu full" v-show="showMenu">
                <div class="title">
                    <img class="logo" src="../../assets/images/wuzi_supplier.png" alt=""/>
                </div>
                <el-menu :default-active="defaultActive" class="el-menu-vertical-demo" mode="vertical"
                         background-color="#ffffff00"
                         text-color="#fff" active-text-color="#FFD41C" :unique-opened="true" menu-trigger="click"
                         :router="true">
                    <template v-for="item in menu">
                        <template v-if="item.show">
                            <!-- 包含子菜单 -->
                            <template v-if="item.children">
                                <el-submenu
                                    :key="item.menuId"
                                    :index="item.menuId"
                                    @click="changePath(1, item.menuName)"
                                >
                                    <template slot="title">
                                        <div class="bar"></div>
                                        <img :src="images.gear2" style="margin-right: 10px" alt=""/>
                                        <span>{{ item.menuName }}</span>
                                    </template>
                                    <div class="menu-item-box">
                                        <el-menu-item
                                            v-show="subItem.show"
                                            v-for="subItem in item.children"
                                            @click="changePath(2, subItem.menuName, item.menuName)"
                                            :key="subItem.menuId"
                                            :index="subItem.menuId"
                                            :route="subItem.route"
                                        >
                                            <template slot="title">
                                                <img :src="images.dot" alt=""/>
                                                <span>{{ subItem.menuName }}</span>
                                            </template>
                                        </el-menu-item>
                                    </div>
                                </el-submenu>
                            </template>
                            <!-- 不包含子菜单 -->
                            <template v-else>
                                <el-menu-item
                                    :key="item.menuId"
                                    :index="item.menuId"
                                    :route="item.route"
                                    @click="changePath(0, item.menuName)"
                                >
                                    <div class="bar"></div>
                                    <img :src="images.gear2" style="margin-right: 10px" alt=""/>
                                    <span slot="title">{{ item.menuName }}</span>
                                </el-menu-item>
                            </template>
                    </template>
                    </template>
                </el-menu>
            </div>
            <div id="fold-btn" @click="showMenu = !showMenu"></div>
        </div>
        <div class="table-box">
            <div class="history">
                <top-btn-bar></top-btn-bar>
            </div>
            <div style="background-color: #fff;">
                <div class="router-box">
                    <top-step :stepInfo="steps" v-show="showSteps" />
                    <keep-alive>
                        <router-view v-if="$route.meta.keepAlive"></router-view>
                    </keep-alive>
                    <router-view v-if="!$route.meta.keepAlive"></router-view>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import topStep from '../../components/topstep/topstep'
import topBtnBar from '../../components/topSuppliesButtonBar'
import foldBtn from '../../assets/menu_close.png'
import openBtn from '../../assets/menu_open.png'
import { getShopStateByUserId, isTwoSupper } from '@/api/frontStage/mallWebHeader'
import { mapState, mapActions } from 'vuex'
// import { showLoading, hideLoading } from '@/utils/common'
import { getParamsByCode } from '@/api/platform/system/systemParam'

export default {
    components: { topStep, topBtnBar },
    watch: {
        showMenu: {
            handler (newVal) {
                let btnImg = newVal ? `url(${foldBtn})` : `url(${openBtn})`
                $('#fold-btn').css('background-image', btnImg)
            }
        },
        steps (newVal) {
            this.changeSteps(newVal)
        },
        $route: {
            handler () {
                this.setDefaultActive()
            },
            deep: true,
            immediate: true
        },
    },
    computed: {
        ...mapState([ 'userInfo']),
    },
    data () {
        return {
            defaultActive: '1',
            menuLoading: false,
            showMenu: true,
            showSteps: false,
            images: {
                gearActive: require('@/assets/images/zbgl2.png'),
                gear: require('@/assets/images/zbgl.png'),
                gear2: require('@/assets/images/ershou.png'),
                gear2Active: require('@/assets/images/zbgl.png'),
                rental: require('@/assets/images/zlgl.png'),
                rentalActive: require('@/assets/images/zlgl2.png'),
                repair: require('@/assets/images/wxbyfw.png'),
                repairActive: require('@/assets/images/wxbyfw2.png'),
                order: require('@/assets/images/ddgl.png'),
                orderActive: require('@/assets/images/ddgl2.png'),
                backTitle: require('@/assets/images/logodpgl.png'),
                dot: require('@/assets/images/dot219.png'),
                arrow: require('@/assets/images/tragle829.png')
            },
            steps: [
                { description: '', },
                { description: '', }
            ],
            submenuName: '',
            // 导航菜单数据
            // authList表示属于什么，0：供应商 1：店铺通过审核，2：pcwp入库供应商
            menu: [
                {
                    menuName: '消息管理',
                    menuId: '1',
                    authList: [0, 1],
                    show: false,
                    children: [
                        // { show: true, menuName: '发件箱', menuId: '发件箱', route: { path: '/shopManage/mail/outBox', } },
                        { show: true, menuName: '收件箱', menuId: '1-1', route: { path: '/supplierSys/product/inBox', } },
                        // { show: true, menuName: '招标提醒', menuId: '招标提醒', route: { path: '/supplierSys/myReminder/reminder', } },
                    ],
                },
                {
                    menuName: '招标提醒',
                    menuId: '2',
                    authList: [0, 2],
                    show: false,
                    route: { path: '/supplierSys/myReminder/reminder', }
                },
                // {
                //     menuName: '合同管理',
                //     menuId: '3',
                //     authList: [0, 2],
                //     show: false,
                //     children: [
                //         { show: true, menuName: '合同管理', menuId: '3-1', route: { path: '/supplierSys/pcwp1/contract', } },
                //     ],
                // },
                {
                    menuName: '投标管理',
                    menuId: '4',
                    authList: [0, 2],
                    show: false,
                    children: [
                        { show: true, menuName: '参与的投标', menuId: '4-1', route: { path: '/supplierSys/bidManage/attendBiding', } },
                        { show: true, menuName: '中标的投标', menuId: '4-2', route: { path: '/supplierSys/bidManage/bidManage', } },
                    ],
                },
                // {
                //     menuName: '对账单',
                //     menuId: '5',
                //     authList: [0],
                //     show: false,
                //     children: [
                //         // { show: true, menuName: 'pcwp1对账单', menuId: '4-1', route: { path: '/supplierSys/pcwp1/accountStatement', } },
                //         { show: true, menuName: '大宗物资供应商对账', menuId: '4-2', route: { path: '/supplierSys/pcwp2/accountStatement', } },
                //         { show: true, menuName: '零星采购供应商对账', menuId: '4-3', route: { path: '/supplierSys/pcwp1/fragmentaryaccountStatement', } },
                //
                //     ]
                // },
                // {
                //     menuName: '发货管理',
                //     menuId: '5-1',
                //     authList: [0],
                //     show: true,
                //     children: [
                //         { show: true, menuName: '要货计划', menuId: '要货计划', route: { path: '/supplierSys/purchaseMentProject', } },
                //         { show: true, menuName: '发货计划', menuId: '发货计划', route: { path: '/supplierSys/shipmentPlan', } },
                //     ],
                // },
                {
                    menuName: '商品管理',
                    menuId: '5',
                    authList: [0],
                    show: false,
                    children: [
                        { show: true, menuName: '已确认商品', menuId: '5-1', route: { path: '/supplierSys/product/materialSupplierYesAffirm', } },
                        { show: true, menuName: '待提交的商品', menuId: '5-2', route: { path: '/supplierSys/product/materialSupplierNotSubmit', } },
                        { show: true, menuName: '未确认的商品', menuId: '5-3', route: { path: '/supplierSys/product/materialSupplierNotAffirm', } },
                    ],
                },
                {
                    menuName: '店铺商品管理',
                    menuId: '6',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '出售中商品', menuId: '6-1', route: { path: '/supplierSys/product/materialManage', } },
                        { show: true, menuName: '仓库中的商品', menuId: '6-2', route: { path: '/supplierSys/product/materialWarehouse', } },
                        { show: true, menuName: '审核的商品', menuId: '6-3', route: { path: '/supplierSys/product/materialCheck', } },
                        { show: true, menuName: '待确认的商品', menuId: '6-4', route: { path: '/supplierSys/product/materialSupplierAffirm', } },
                    ],
                },
                {
                    menuName: '二级订单管理',
                    menuId: '7',
                    authList: [0],
                    show: false,
                    children: [
                        { show: true, menuName: '待确认零星采购订单', menuId: '7-1', route: { path: '/supplierSys/order/searchOrder/twoOrder', } },
                        { show: true, menuName: '已确认的零星采购订单', menuId: '7-2', route: { path: '/supplierSys/order/searchOrder/twoOrder/okIndex', } },
                        { show: true, menuName: '零星采购发货单', menuId: '7-3', route: { path: '/supplierSys/order/searchOrder/twoOrder/shiped', } },

                    ],
                },
                {
                    menuName: '二级订单退货',
                    menuId: '8',
                    authList: [0],
                    show: false,
                    children: [
                        { show: true, menuName: '零星采购退货申请', menuId: '8-1', route: { path: '/supplierSys/returnGoods/twoApply', } },
                        { show: true, menuName: '零星采购退货记录', menuId: '8-2', route: { path: '/supplierSys/returnGoods/twoRecord', } }
                    ],
                },
                {
                    menuName: '订单管理',
                    menuId: '9',
                    authList: [0],
                    show: false,
                    children: [
                        // { show: true, menuName: '全部订单', menuId: '已支付订单', route: { path: '/supplierSys/order/searchOrder', } },
                        // { show: true, menuName: '办公用品订单', menuId: '办公用品订单', route: { path: '/supplierSys/order/searchOrder/officeIndex', } },
                        { show: true, menuName: '零星采购订单', menuId: '9-1', route: { path: '/supplierSys/order/smallShipTwo/searchOrder', } },
                        { show: true, menuName: '零星采购发货单', menuId: '9-2', route: { path: '/supplierSys/order/shiped/searchOrder', } },
                        { show: true, menuName: '零星采购订单', menuId: '9-3', route: { path: '/supplierSys/order/searchOrder/towIndex',  } },
                        { show: true, menuName: '零星采购发货单', menuId: '9-4', route: { path: '/supplierSys/order/searchOrder/twoOrder/twoship', } },
                        { show: true, menuName: '大宗订单', menuId: '9-5', route: { path: '/supplierSys/order/searchOrder/monthPlanOrder',  } },
                        { show: true, menuName: '大宗发货单', menuId: '9-6', route: { path: '/supplierSys/order/searchOrder/monthPlanShipOrder',  } },
                    ],
                },
                {
                    menuName: '竞价管理',
                    menuId: '10',
                    authList: [0],
                    show: false,
                    children: [
                        { show: true, menuName: '我参与的竞价', menuId: '10-1', route: { path: '/supplierSys/bidManage/myBidding', } },
                        { show: true, menuName: '我的竞价列表', menuId: '10-2', route: { path: '/supplierSys/bidManage/bidingList', } },
                    ],
                },
                {
                    menuName: '对账',
                    menuId: '11',
                    authList: [0],
                    show: false,
                    children: [
                        { show: true, menuName: '对账单', menuId: '11-1', route: { path: '/supplierSys/sheet/sheet' } },
                    ]
                },
                {
                    menuName: '退货管理',
                    menuId: '12',
                    authList: [0],
                    // authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '零星采购退货申请', menuId: '12-1', route: { path: '/supplierSys/returnGoods/invoice', } },
                        { show: true, menuName: '零星采购退货记录', menuId: '12-2', route: { path: '/supplierSys/returnGoods/record', } }
                    ],
                },
                {
                    menuName: '发票管理',
                    menuId: '13',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '零星采购发票申请', menuId: '13-1', route: { path: '/supplierSys/invoice/invoice', } },
                        { show: true, menuName: '零星采购发票记录', menuId: '13-2', route: { path: '/supplierSys/invoice/record', } }
                    ],
                },
                {
                    menuName: '店铺管理',
                    menuId: '14',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '店铺信息', menuId: '14-1', route: { path: '/supplierSys/shop/shopInfoManage', } },
                        { show: true, menuName: '供方管理', menuId: '14-2', route: { path: '/supplierSys/shop/selectSupplier', } },
                    ],
                },
                {
                    menuName: '统计分析',
                    menuId: '15',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '订单统计', menuId: '15-1', route: { path: '/supplierSys/analysis/order', } },
                        { show: true, menuName: '商品统计', menuId: '15-2', route: { path: '/supplierSys/analysis/product', } }
                    ],
                },
                {
                    menuName: '报表管理',
                    menuId: '16',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '上架商品报表', menuId: '16-1', route: { path: '/supplierSys/analysis/productReportForms', } },
                        { show: true, menuName: '零星采购交易量报表', menuId: '16-2', route: { path: '/supplierSys/analysis/shopreport', } },
                        { menuName: '物资结算报表', menuId: '16-3', route: { path: '/supplierSys/analysis/productSheet', } },
                        { show: true, menuName: '零星采购结算报表', menuId: '16-4', route: { path: '/supplierSys/analysis/orderStatement', } }
                    ],
                },
            ],
        }
    },
    methods: {
        ...mapActions(['changeSteps']),
        setDefaultActive () {
            let path = this.$route.path
            this.menu.forEach(item => {
                if(item.route && item.route.path === path) {
                    this.steps = [{ description: item.menuName }]
                    this.defaultActive = item.menuId
                }
                if(!item.children) return
                item.children.forEach(subItem => {
                    if(subItem.route.path !== path) return
                    this.defaultActive = subItem.menuId
                    this.submenuName = item.menuName
                    this.steps[0].description = item.menuName
                    this.steps[1].description = subItem.menuName
                })
            })
        },
        getParamsByCodeM () {
            getParamsByCode({ code: 'materialUnit', size: 100 }).then(res => {
                if(res.length !== 0) {
                    let materialUnit = res.map(item => ({ label: item.keyValue, value: item.keyValue2 }))
                    this.$store.commit('setMaterialUnit', materialUnit)
                }
            })
        },
        // 修改路径显示
        changePath (num, name) {
            !this.showSteps ? this.showSteps = true : null
            if(this.steps.length === 1) {
                num === 0 ? this.steps[0].description = name : this.steps.push({ description: name })
            }else{
                if(num === 0) {
                    this.steps.pop()
                    this.steps[0].description = name
                }else{
                    this.steps[0].description = this.submenuName
                    this.steps[1].description = name
                }
            }
            this.changeSteps(this.steps)
        },
    },
    async created () {
        this.menuLoading = true
        // 获取计量单位
        this.getParamsByCodeM()
        // 获取是否二级供方
        let twoSupplierAuth = await isTwoSupper()
        // 店铺是否通过审核
        let { auditStatus } = await getShopStateByUserId()
        for (let i = 0; i < this.menu.length; i++) {
            let menuItem = this.menu[i]
            // 是供应商并且入了pcwp库
            let isSupplierAndPcwp = [0, 2]
            let isSupplierAndPcwpB = isSupplierAndPcwp.every(e => menuItem.authList.includes(e))
            if(isSupplierAndPcwpB) {
                if(this.userInfo.isPcwp != null && this.userInfo.isPcwp == 1) {
                    menuItem.show = true
                }else {
                    if(menuItem.menuName === '合同管理') {
                        if(this.userInfo.isInterior === 1) {
                            menuItem.show = true
                            continue
                        }
                    }
                    menuItem.show = false
                }
                // 跳过循环
                continue
            }
            // 店铺显示
            let isShopArr = [0, 1]
            let isShop = isShopArr.every(e => menuItem.authList.includes(e))
            if(isShop) {
                if(auditStatus === 1 && this.userInfo.shopId != null) {
                    menuItem.show = true
                    // 自定义菜单显示
                    if(this.userInfo.shopClass != 2) {
                        if(menuItem.menuName === '店铺管理') {
                            // 隐藏供方管理
                            menuItem.children[1].show = false
                        }
                        if(menuItem.menuName === '店铺商品管理') {
                            // 隐藏待确认的商品
                            menuItem.children[3].show = false
                        }
                        // 订单不在这里处理，放供应商
                        // if(menuItem.menuName === '订单管理') {
                        //     // 隐藏多供方订单信息
                        //     menuItem.children[2].show = false
                        //     menuItem.children[3].show = false
                        // }
                    }
                    // if(this.userInfo.shopClass == 2) {
                    //     if(menuItem.menuName === '订单管理') {
                    //         // 隐藏多供方订单信息
                    //         menuItem.children[0].show = false
                    //     }
                    // }
                }else {
                    menuItem.show = false
                }
                // 如果是店铺直接跳过循环
                continue
            }
            // 供应商显示
            let isSupplierArr = [0]
            let isSup = isSupplierArr.every(e => menuItem.authList.includes(e))
            if(isSup) {
                menuItem.show = true
                // 自定义菜单显示
                if(menuItem.menuName === '商品管理' || menuItem.menuName === '二级订单管理' || menuItem.menuName === '二级订单退货') {
                    // 如果不是二级供方隐藏菜单
                    if(twoSupplierAuth.code == 200 && twoSupplierAuth.data == null) {
                        menuItem.show = false
                    }
                }
                if(menuItem.menuName === '订单管理') {
                    if(auditStatus === 1 && this.userInfo.shopId != null) {
                        if(this.userInfo.shopClass != 2) {
                            // 隐藏多供方订单信息
                            menuItem.children[2].show = false
                            menuItem.children[3].show = false
                        }
                        if(this.userInfo.shopClass == 2) {
                            // 隐藏多供方订单信息
                            menuItem.children[0].show = false
                            menuItem.children[1].show = false
                        }
                    }else {
                        menuItem.children[0].show = false
                        menuItem.children[1].show = false
                        menuItem.children[2].show = false
                        menuItem.children[3].show = false
                    }
                }
            }else {
                menuItem.show = false
            }
        }
        this.menuLoading = false
        // for (let i = 0; i < this.menu.length; i++) {
        //     if(this.menu[i].show == true) {
        //         this.steps.push({ description: this.menu[i].menuName })
        //         this.steps.push({ description: this.menu[i].children[0].menuName })
        //         this.$router.push({ path: this.menu[i].children[0].route.path })
        //         this.defaultActive = this.menu[i].children[0].menuId
        //         return
        //     }
        // }
    },
}
</script>
<style scoped lang="scss">
@import '../../assets/css/backStage.css';

/deep/ .el-dialog__header {
    background: url(../../assets/test.png);
}

.page {
    display: flex;
    font-family: 'SourceHanSansCN-Regular';
    height: 100%;
}

.table-box {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    box-sizing: border-box;
    background-color: #eff2f6;

    &::-webkit-scrollbar {
        display: none;
    }

    .history {
        font-weight: bold;
        font-size: 17px;

        & > div {
            line-height: 84px;

            span {
                color: gray;
            }
        }
    }

    .router-box {
        height: 100%;
        display: flex;
        flex-direction: column;

        & > *:last-child {
            flex-grow: 1;
        }
    }
}
</style>
