<template>
  <div>
    <div class="tab_top" style="margin-left: 1%;">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="待审核" name="first">
          <shop-check-material ref="shopCheckMaterial"></shop-check-material>
        </el-tab-pane>
        <el-tab-pane label="商品管理" name="second">
          <!-- 零星采购 店铺商品管理 下架操作 -->
          <div class="base-page">
            <div class="left" style="height: 80vh;">
              <select-material-class  ref="materialClassRef" :productType = "0" />
            </div>
            <!-- 列表 -->
            <div class="right">
              <div class="e-table">
                <div class="top">
                  <div class="left">
                    <div class="left-btn">
                      <el-button type="primary" @click="updateStateBatch(2)" class="btn-delete">批量下架</el-button>
                      <el-button type="primary" class="btn-greenYellow" @click = "changeSortValue">批量修改排序值</el-button>
                    </div>
                  </div>
                  <el-dropdown @command="handleChangeSort" trigger="click" placement="bottom">
                        <span class="pointer">
                            排序方式<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="1" :style="{ color: init.orderBy == 1 ? '#2e61d7' : '' }">
                        排序值
                      </el-dropdown-item>
                      <el-dropdown-item :command="0" :style="{ color: init.orderBy == 0 ? '#2e61d7' : '' }">
                        上架时间
                      </el-dropdown-item>
                      <el-dropdown-item :command="2" :style="{ color: init.orderBy == 2 ? '#2e61d7' : '' }">
                        创建时间
                      </el-dropdown-item>
                      <el-dropdown-item :command="3" :style="{ color: init.orderBy == 3 ? '#2e61d7' : '' }">
                        修改时间
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <div class="search_box" style="width: 400px">
                    <el-input clearable type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="init.keywords">
                      <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch"  alt=""/>
                    </el-input>
                    <div class="adverse">
                      <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                    </div>
                  </div>
                </div>
              </div>
              <!--表格-->
              <div class="e-table" style="width: 100%;">
                <el-table v-loading="tableLoading" class="table" :height="rightTableHeight" :data="tableData"  border
                          @selection-change="selectionChangeHandle" @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow">
                  <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                  <el-table-column label="序号" type="index" width="60"></el-table-column>
                  <el-table-column label="操作" width="130">
                    <template v-slot="scope" >
                      <el-button style="padding:0 8px;" v-if="scope.row.state===1"
                                 size="mini"
                                 type="danger"
                                 @click="updateState(scope.row,2,'下架')">下架</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column label="名称" width="200">
                    <template v-slot="scope">
                      <span class="action" @click="handleView(scope.row)">{{scope.row.relevanceName}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="物资分类" width="240" prop="classPath"/>
                  <el-table-column label="商品编码" width="240" prop="serialNum" />
                  <el-table-column label="店铺名称" width="160" prop="shopName" />
                  <el-table-column label="图片" width="120" type="index">
                    <template v-slot="scope">
                      <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg" fit="cover"></el-image>
                    </template>
                  </el-table-column>
                  <el-table-column label="状态" width="" prop="state">
                    <template v-slot="scope">
                      <el-tag v-if="scope.row.state===0">待上架</el-tag>
                      <el-tag v-if="scope.row.state===1" type="success">已上架</el-tag>
                      <el-tag v-if="scope.row.state===2" type="danger">已下架</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="销售价" width="" prop="productMinPrice"></el-table-column>
                  <el-table-column label="税率" width="" prop="taxRate"></el-table-column>
                  <el-table-column label="加成率" width="" prop="markUpNum"></el-table-column>
                  <el-table-column label="排序值" width="120" type="index">
                    <template v-slot="scope">
                      <el-input type="number" clearable v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="上架时间" width="160" prop="putawayDate" />
                  <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                  <el-table-column label="修改时间" width="160" prop="gmtModified" />
                </el-table>
              </div>
              <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
              />
            </div>
            <!--高级查询-->
            <el-dialog v-dialogDrag  title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="true">
              <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                  <el-col :span="12" :offset="0">
                    <el-form-item label="商品名称：" >
                      <el-input clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.productName"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12" :offset="0">
                    <el-form-item label="商品编码：" >
                      <el-input clearable maxlength="100" placeholder="请输入商品编码" v-model="filterData.serialNum"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12" :offset="0">
                    <el-form-item label="店铺名称：" >
                      <el-input clearable maxlength="100" placeholder="请输入店铺名称" v-model="filterData.shopName"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12" :offset="0">
                    <el-form-item label="价格以上：">
                      <el-input clearable v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                    </el-form-item>
                    <el-form-item label="价格以下：">
                      <el-input clearable type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12" :offset="0">
                    <el-form-item label="上架时间：">
                      <el-date-picker
                        value-format="yyyy-MM-dd HH:mm:ss"
                        v-model="filterData.putawayDate"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12" :offset="0">
                    <el-form-item label="创建时间：">
                      <el-date-picker
                        value-format="yyyy-MM-dd HH:mm:ss"
                        v-model="filterData.createDate"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12" :offset="0">
                    <el-form-item label="修改时间：">
                      <el-date-picker
                        value-format="yyyy-MM-dd HH:mm:ss"
                        v-model="filterData.modifiedDate"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24" :offset="0">
                    <el-form-item label="排序：">
                      <el-radio v-model="init.orderBy" :label="0">按上架时间排序</el-radio>
                      <el-radio v-model="init.orderBy" :label="1">按排序值排序</el-radio>
                      <el-radio v-model="init.orderBy" :label="2">按创建时间排序</el-radio>
                      <el-radio v-model="init.orderBy" :label="3">按修改时间排序</el-radio>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <span slot="footer">
                <el-button type="primary" @click = "confirmSearch">查询</el-button>
                <el-button @click = "resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
            </el-dialog>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import Pagination from '@/components/pagination/pagination'
import ShopCheckMaterial from '@/pages/platform/product/shopCheckMaterial/index'
import { debounce } from '@/utils/common'
import { mapState, mapMutations } from 'vuex'
import {
    updateProductState,
    updateBatch,
    listMaterialPage,
} from '@/api/platform/product/materialManage'
export default {
    components: {
        SelectMaterialClass, Pagination, ShopCheckMaterial
    },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                state: [1],
                productType: 0,
                orderBy: 0,
                classId: null,
                keywords: null,
                classPath: [],
            },
            // 商品库
            showDeviceDialog: false,
            activeName: 'first',
            inventory: {
                selectRow: [],
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            dataListSelections: [], //表格选中的数据
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                shopName: null,
                serialNum: null,
                productName: null,
                stateCheckAll: false, // 选择全局
                state: [], // 状态
                belowPrice: null,
                abovePrice: null,
                putawayDate: [], // 上架时间
                createDate: [], // 创建时间
                modifiedDate: [], // 修改时间
                stateOptions: [
                    { value: 0, label: '待上架' },
                    { value: 1, label: '已上架' },
                    { value: 2, label: '已下架' }
                ],
            },
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.activeName = 'first'
        this.getTableData()
    },
    created () {
        let data = JSON.parse(localStorage.getItem('vuex'))
        this.orgId = data.userInfo.orgInfo.orgId
    },
    methods: {
        handleChangeSort (value) {
            this.init.orderBy = value
        },
        // 开始导入
        inventoryImportBatch () {
            if(this.inventory.selectRow.length === 0) {
                this.$message('请勾选要导入的数据！')
            }
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        // 选择
        selectDeviceRow (value) {
            this.inventory.selectRow = value
        },
        // 批量修改排序
        changeSortValue () {
            if(this.changedRow.length === 0) {
                return this.$message('未修改列表当中的排序值！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                updateBatch(this.changedRow).then(res => {
                    this.getTableData()
                    this.changedRow = []
                    this.tableLoading = false
                    this.message(res)
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if(row.sort <= 0 ) {
                row.sort = 0
            }
            if(this.changedRow.length == 0) {
                this.changedRow.push({ productId: row.productId, sort: row.sort })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if(t.productId == row.productId) {
                    t.sort = row.sort
                    flag = true
                }
            })
            if(!flag) {
                this.changedRow.push({ productId: row.productId, sort: row.sort })
            }
            flag = true
        },
        skipView (data) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/product/shopMaterialManageDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shopMaterialManageDetail',
                params: { row: data }
            })
        },
        //新增
        add () {
            let data = {}
            data.viewType = 'add'
            data.classPath = this.classPath
            this.skipView(data)
        },
        // 物资详情
        handleView (row) {
            row.classPath = this.classPath
            this.skipView(row)
        },
        // 清空高级搜索表单
        resetSearchConditions () {
            this.filterData.shopName = null
            this.filterData.serialNum = null
            this.filterData.productName = null
            this.filterData.putawayDate = []
            this.filterData.createDate = []
            this.filterData.modifiedDate = []
            this.filterData.belowPrice = null
            this.filterData.abovePrice = null
            this.filterData.state = []
            this.filterData.stateCheckAll = false
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 高级搜索确认
        confirmSearch () {
            this.init.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateOptions.length
        },
        // 状态全选
        stateAllSelect (value) {
            if(value) {
                this.filterData.state = this.filterData.stateOptions.map(t => {
                    return t.value
                })
            }else {
                this.filterData.state = []
            }
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 单个上架
        updateState (row, state, title) {
            let productName = row.productName
            let params = {
                productIds: [row.productId],
                state: state
            }
            this.clientPop('info', '您确定要对物资【' + productName + '】进行【' + title + '】操作吗?', async () => {
                updateProductState(params).then(res => {
                    this.getTableData()
                    this.message(res)
                })
            })
        },
        handleClick (tab) {
            if (tab.label === '待审核') {
                this.$refs.shopCheckMaterial.init.keywords = null
                this.$refs.shopCheckMaterial.resetSearchConditions()
                this.$refs.shopCheckMaterial.getTableData()
            } else {
                this.init.keywords = null
                this.resetSearchConditions()
                this.getTableData()
            }
        },
        // 批量修改上下架状态
        updateStateBatch (state) {
            if(this.dataListSelections.length === 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let params = {
                productIds: this.dataListSelections.map(item => {
                    return item.productId
                }),
                state: state
            }
            this.clientPop('info', '您确定要批量上下/下架这些物资吗！', async () => {
                this.tableLoading = true
                updateProductState(params).then(res => {
                    this.getTableData()
                    this.dataListSelections = []
                    this.message(res)
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.classPath = nodePath
            this.getTableData()
        },
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                state: this.init.state,
                shopId: this.init.shopId,
                //productType: this.init.productType,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if(this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            if(this.filterData.serialNum != null) {
                params.serialNum = this.filterData.serialNum
            }
            if(this.filterData.state.length !== 0) {
                params.state = this.filterData.state
            }
            if(this.init.classId != null) {
                params.classId = this.init.classId
            }
            if(this.init.keywords != null) {

                params.keywords = this.init.keywords
            }
            if(this.filterData.modifiedDate != null) {
                params.startModifiedDate = this.filterData.modifiedDate[0],
                params.endModifiedDate = this.filterData.modifiedDate[1]
            }
            if(this.filterData.createDate != null) {
                params.startCreateDate = this.filterData.createDate[0],
                params.endCreateDate = this.filterData.createDate[1]
            }
            if(this.filterData.putawayDate != null) {
                params.startPutawayDate = this.filterData.putawayDate[0],
                params.endPutawayDate = this.filterData.putawayDate[1]
            }
            if(this.init.orderBy != null) {
                params.orderBy = this.init.orderBy
            }
            if(this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if(this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            this.tableLoading = true
            listMaterialPage(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if(res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
.el-dialog__body{
    margin: 220px;
}
.base-page .left {
  min-width: 200px;
  height: 100%;
  padding: 0;
  overflow: scroll;
}
.base-page {
    width: 100%;
    height: 100%;
}
/deep/ .el-dropdown {
  min-width: 75px;
  margin-right: 20px;
}
/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;
    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

//.e-form {
//    padding: 0 20px;
//
//    .tabs-title::before {
//        content: '';
//        height: 22px;
//        width: 8px;
//        border-radius: 40px;
//        background-color: #2e61d7;
//        display: block;
//        position: absolute;
//        left: 20px;
//        margin-right: 20px;
//    }
//}

.e-table {min-height: auto;}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog.dlg {
    height: 800px;
    .el-dialog__header {
        margin-bottom: 0;
    }

    .el-dialog__body {
        height: 674px;
        margin: 10px;
        display: flex;

        &>div {
            .e-pagination {
                background-color: unset;
            }
            //height: 670px;
            .title {
                height: 22px;
                margin-bottom: 10px;
                padding-left: 26px;
                text-align: left;
                line-height: 22px;
                color: #2e61d7;
                font-weight: bold;
                position: relative;
                display: flex;

                &::before {
                    content: '';
                    display: block;
                    width: 10px;
                    height: inherit;
                    border-radius: 5px;
                    background-color: blue;
                    position: absolute;
                    left: 10px;
                    top: 0;
                }
            }
        }

        .el-input__inner {
            border: 1px solid blue;
            border-radius: 6px;
        }

        .el-input__suffix {
            width: 20px;
        }
        .e-table{
            flex-grow: 1;
            .table{
                height: 100%;
            }
        }

        .box-left {
            width: 370px;
            display: flex;
            flex-direction: column;
            .search {
                padding: 0 10px;
            }
        }

        .box-right {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            &>div{
                display: flex;
                flex-direction: column;
            }
            .top {
                height: 374px;
                margin: 0;
                border-radius: 0;
                box-shadow: unset;
            }
            .bottom{
                flex-grow: 1;
            }
        }
    }

    .el-dialog__footer {
        background-color: #eff2f6;
    }
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
