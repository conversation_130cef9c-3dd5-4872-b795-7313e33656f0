<template>
    <!-- 新 平台初审 企业信息提交，后台未审核，企业账号页面 -->
    <div class="df">
        <div class="boxTop">
            <div class="title center">{{caption}}</div>
        </div>
      <!--步骤条-->
      <div class="steps">
        <el-steps :active="1" align-center>
          <el-step title="注册"></el-step>
          <el-step title="平台初审"></el-step>
          <el-step title="申请开店"></el-step>
          <el-step title="合同签约及缴费"></el-step>
          <el-step title="平台复审"></el-step>
          <el-step title="完成"></el-step>
        </el-steps>
      </div>
      <div class="successtxt">
        <p>开店申请提交成功。平台初审中（一般在3个工作日内完成），请等待。审核结果平台将发送短消息通知，请注意查收！！！</p>
      </div>
<!--        <div class="boxBottom">-->
<!--            <div class="icon center"></div>-->
<!--            <div class="msg1">注册成功</div>-->
<!--            <div class="msg2">即将跳转至登陆…</div>-->
<!--            <button class="center" @click="directTo('/index')">返回主页</button>-->
<!--            <button style="margin-left: 20px;" @click="openShop">我要开店</button>-->
<!--        </div>-->
    </div>
</template>
<script>

export default {
    props: ['title'],
    data () {
        return {}
    },
    computed: {
        caption () {
            if(this.title === 'individual') {
                return '个人用户注册'
            }
            if(this.title === 'enterprise') {
                return '企业用户注册'
            }
            if(this.title === 'business') {
                return '个体户注册'
            }
            return ''
        },
    },
    methods: {
        directTo (url) {
            this.openWindowTab(url)
        },
        openShop () {
            this.$router.push('/mFront/openShop')
        },
    },
    mounted () {
        // setTimeout(() => {
        //     this.$router.push('/mFront/openShop')
        // }, 3000)
    }
}
</script>
<style scoped lang="scss">
.df {
    flex-direction: column;
}
.boxTop {
    height: 87px;
    border-bottom: 1px solid #D9D9D9;
    .title {
        width: 200px;
        height: 100%;
        font-size: 26px;
        font-weight: 500;
        line-height: 87px;
        text-align: center;
        border-bottom: 4px solid #216EC6;
        color: #333;
        user-select: none;
    }
}
//.boxBottom {
//    text-align: center;
//    flex-grow: 1;
//    .icon {
//        width: 100px;
//        height: 100px;
//        margin-top: 120px;
//        background: url(../../../assets/images/userCenter/zc_chenggong.png);
//    }
//    .msg1 {
//        margin: 30px 0 15px 0;
//        font-size: 22px;
//        color: #333;
//        text-align: center;
//        font-weight: 400;
//    }
//    .msg2 {
//        margin-bottom: 72px;
//        font-size: 16px;
//        text-align: center;
//        color: #999;
//    }
//    button {
//        width: 150px;
//        height: 50px;
//        font-size: 22px;
//        color: #fff;
//        background-color: #216EC6;
//    }
//}
.steps{
  //border-style: solid;
  margin: 0 auto;
  height: 10%;
  padding:50px 0;
  width: 800px;
  margin-bottom: -80px;
}
.successtxt{
  padding-left: 200px;
  padding-right: 200px;
  font-size: 20px;
  margin-top: 100px;
  width: 100%;
  text-align: center;
  //border: 1px solid red;
  color: red;
}
</style>
