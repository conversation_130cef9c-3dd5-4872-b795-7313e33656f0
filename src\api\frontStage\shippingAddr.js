import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/userCenter/address/listByEntity',
        params,
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/userCenter/address/createByUserId',
        params,
    })
}

const update = params => {
    return httpPost({
        url: '/materialMall/userCenter/address/update',
        params,
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/userCenter/address/delete',
        params,
    })
}

const isUserAdrees = params => {
    return httpGet({
        url: '/materialMall/userCenter/address/isUserAdrees',
        params,
    })
}

const setDefaultAddress = params => {
    return httpGet({
        url: '/materialMall/userCenter/address/setDefaultAddress',
        params,
    })
}
const findById = params => {
    return httpGet({
        url: '/materialMall/userCenter/address/findById',
        params,
    })
}
const getDefaultAddress = params => {
    return httpGet({
        url: '/materialMall/userCenter/address/getDefaultAddress',
        params,
    })
}
export {
    getList,
    setDefaultAddress,
    del,
    update,
    create,
    findById,
    getDefaultAddress,
    isUserAdrees,
}