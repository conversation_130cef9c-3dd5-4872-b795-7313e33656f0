import service from '@/utils/request'

const { httpPost } = service

const getDataList = params => {
    return httpPost({
        url: '/supplierSys/account/listByEntity',
        params
    })
}
const getDataDtlList = params => {
    return httpPost({
        url: '/supplierSys/accountDtl/listByEntity',
        params
    })
}
const changAccountState = params => {
    return httpPost({
        url: '/supplierSys/account/changAccountShopState',
        params
    })
}

export {
    getDataList,
    getDataDtlList,
    changAccountState

}