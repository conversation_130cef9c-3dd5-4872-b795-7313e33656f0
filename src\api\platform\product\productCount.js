import service from '@/utils/request'

const { httpPost } = service

// 商品统计(后台)
const getProductCountList = params => {
    return httpPost({
        url: '/materialMall/product/getProductCountList',
        params
    })
}

// 商品统计(后台)（导出）
const getProductCountListExcel = params => {
    return httpPost({
        url: '/materialMall/product/getProductCountListExcel',
        params,
        responseType: 'blob',
    })
}
// 商品操作数统计(后台)
const getOperand = params => {
    return httpPost({
        url: '/materialMall/product/operand',
        params
    })
}
// 商铺管理平台
const getSupplierProductCountList = params => {
    return httpPost({
        url: '/materialMall/product/getSupplierProductCountList',
        params
    })
}
// 商品上架报表
const getProductFromList = params => {
    return httpPost({
        url: '/materialMall/reportForms/productFromList',
        params
    })
}
const exportData = params => {
    return httpPost({
        url: '/materialMall/reportForms/productFromListLoad',
        params,
        responseType: 'blob',
    })
}

const exportOrderPDF = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/listByParametersExport',
        params,
        responseType: 'blob',
    })
}
const exportSecondaryOrder = params => {
    return httpPost({
        url: '/materialMall/shopManage/supplier/export_orders',
        params,
        responseType: 'blob',
    })
}
const exportOrderLCPDF = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/listByParametersLCExport',
        params,
        responseType: 'blob',
    })
}
/**
 * 平台交易量报表
 * @param params
 * @returns {*}
 */
const platformOutputExcel = params => {
    return httpPost({
        url: '/materialMall/reportForms/platform/productFromListLoad',
        params,
        responseType: 'blob',
    })
}
/**
 * 供应商交易量报表
 * @param params
 * @returns {*}
 */
const supplierOutputExcel = params => {
    return httpPost({
        url: '/materialMall/reportForms/shopManage/supplierOutputExcel',
        params,
        responseType: 'blob',
    })
}
const getPlatformProductFromList = params => {
    return httpPost({
        url: '/materialMall/reportForms/platform/productFromList',
        params
    })
}

/**
 * 平台结算报表
 * @param params
 * @returns {*}
 */
const getPlatformSettlementFromList = params => {
    return httpPost({
        url: '/materialMall/reportForms/platform/getPlatformSettlementFromList',
        params
    })
}
/**
 * 物资交易量统计(供应商方)
 * @param params
 * @returns {*}
 */
const getShopManageMaterial = params => {
    return httpPost({
        url: '/materialMall/reportForms/shopManage/getShopManageMaterial',
        params
    })
}

export {
    getPlatformSettlementFromList,
    getProductCountList,
    getProductFromList,
    exportData,
    exportOrderPDF,
    exportOrderLCPDF,
    getPlatformProductFromList,
    platformOutputExcel,
    supplierOutputExcel,
    getShopManageMaterial,
    getSupplierProductCountList,
    getOperand,
    exportSecondaryOrder,
    getProductCountListExcel
}