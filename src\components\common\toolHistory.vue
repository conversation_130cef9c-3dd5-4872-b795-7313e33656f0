<template>
  <div>
    <el-popover
        placement="left"
        title=""
        width="610"
        trigger="hover"
        @show="showTable"
    >
        <div slot="reference">
            <slot></slot>
        </div>
        <div>
            <el-table
                ref="history"
                :data="tableData"
                border
                style="width: 100%"
                v-sortTable="{ 'tableData': tableData, '_this': this, 'ref': 'history' }"
                v-loading="loading"
                class="historyTable"
                max-height="200"
            >
                <el-table-column show-overflow-tooltip prop="unitName" label="审核级次" width="100">
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="assignUser" label="审核人" width="100">
                </el-table-column>
                <el-table-column
                    show-overflow-tooltip
                    prop="userTitle"
                    label="审核人岗位"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    show-overflow-tooltip
                    prop="notifyTime"
                    label="通知时间"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    show-overflow-tooltip
                    prop="completeTime"
                    label="审核时间"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    show-overflow-tooltip
                    prop="opinion"
                    label="审核意见"
                    min-width="100"
                >
                </el-table-column>
            </el-table>
        </div>
    </el-popover>
  </div>
</template>

<script>
import service from '@/utils/request'
export default {
    props: {
        url: {
            type: String,
            default: ''
        },
        billId: {
            type: String,
            default: ''
        },
        billType: {
            type: Number,
            default: 0
        }
    },
    data () {
        return{
            tableData: [],
            loading: false,
            auditParams: {
                billId: this.billId,
                billType: this.billType,
                freeNextUnitIndex: 0,
                freeNextUser: '',
                isPass: true,
                isShareInAudit: true,
                opinion: ''
            },
        }
    },
    methods: {
        showTable () {
            if(this.tableData.length === 0) {
                this.getTableData()
            }
        },
        getTableData () {
            if(!this.billId) return
            this.loading = true
            service.httpPost({
                url: this.url,
                params: this.auditParams
            }).then(res=>{
                this.tableData = res
                this.loading = false
            }).finally(()=>{
                this.loading = false
            })
        }
    }
}
</script>

<style lang="scss" scoped>
    .historyTable{
        font-size: 10px;
    }
</style>