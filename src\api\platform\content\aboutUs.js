import service from '@/utils/request'

const { httpPost, httpGet } = service

const mc = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/create',
        params
    })
}

const getList = params => {
    return httpGet({
        url: '/materialMall/platform/richContent/findById',
        params
    })
}

const getInfo = params => {
    return httpGet({
        url: '/materialMall/platform/richContent/findByProgramaKey',
        params
    })
}

const update = params => {
    return httpPost({
        url: '/materialMall/platform/richContent/update',
        params
    })
}

export {
    mc,
    getList,
    getInfo,
    update
}