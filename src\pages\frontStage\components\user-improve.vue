<template>
  <div>
    <el-dialog
      class="fixed-width-dialog"
      :visible.sync="userInfoShowDialog"
      width="650px"
      :show-close="!showClose"
      :close-on-click-modal="false"
    >
      <template #title>
        <div>
          <el-row>
            <el-col :span="4">
              <div class="tabs-title">基本信息</div>
            </el-col>
            <el-col :span="19">
              <span class="tabs-tip" style="color: #d9272c"
                >温馨提示：信息确认后可作为后续下单采购得默认收获地址</span
              >
            </el-col>
            <el-col :span="1">
              <span @click="userInfoShowDialog = false"
                ><i
                  style="cursor: pointer; margin-left: 20px"
                  class="el-icon-close"
                ></i
              ></span>
            </el-col>
          </el-row>
        </div>
      </template>
      <el-form :model="form" :rules="rules" style="margin-right: 30px">
        <el-form-item
          label="机构名称："
          style="margin-top: 30px"
          label-width="100px"
        >
          <el-tooltip
            effect="light"
            class="item"
            :content="form.enterpriseName"
            placement="top"
          >
            <el-button style="width: 100%" type="text" class="ellipsis-text">
              {{ form.enterpriseName }}
            </el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="收货人：" label-width="100px" prop="receiverName">
          <el-input v-model="form.receiverName" placeholder="请输入收货人姓名"></el-input>
        </el-form-item>
        <el-form-item label="所在地区：" label-width="100px" prop="area">
          <el-cascader
            size="large"
            style="width: 100%"
            :options="addressData"
            v-model="form.area"
            @change="handleAddressChange"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item
          label="详细地址："
          label-width="100px"
          prop="detailAddress"
        >
          <el-input v-model="form.detailAddress" placeholder="请输入详细收货地址"></el-input>
        </el-form-item>
        <el-form-item label="手机号码：" label-width="100px" prop="receiverMobile">
          <span style="width: 10%" class="el-form-item__label">+86</span>
          <el-input
            style="width: 90%"
            v-model="form.receiverMobile"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            style="padding: 0px 20px"
            type="primary"
            @click="handleConfirm"
            >保存</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { theFirstLogin, changeOrganLogin  } from '@/api/frontStage/purchase.js'
import { regionData, CodeToText } from 'element-china-area-data'
export default {
    data () {
        return {
            userInfoShowDialog: false,
            addressData: regionData,
            isHaveDefault: false,
            form: {
                enterpriseName: '',
                receiverName: '',
                area: [],
                detailAddress: '',
                receiverMobile: '',
                province: '',
                city: '',
                county: '',
                fixedTelephone: '',
                email: '',
                default: false,
                aliasAddress: '',
            },
            rules: {
                receiverName: [
                    { required: true, message: '请输入收件人名称', trigger: 'blur' },
                ],
                area: [{ required: true, message: '请选择所在地区', trigger: 'blur' }],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                ],
                receiverMobile: [
                    { required: true, message: '请输入手机号码', trigger: 'blur' },

                ],
            },
            showClose: true,
            loginData: {},
        }
    },
    mounted () {
        this.$bus.$on('refreshUserInfo', () => (this.loginData = this.userInfo))
    },
    methods: {
        handleAddressChange () {
            if (this.form.area != null && this.form.area.length != 0) {
                let addArr = this.form.area
                let province = CodeToText[addArr[0]]
                let city = CodeToText[addArr[1]]
                let county = CodeToText[addArr[2]]
                this.form.province = province
                this.form.city = city
                this.form.county = county
                // this.form.detailAddress = province + city + county
            }
        },
        init (data) {
            this.loginData = data
            this.form = data
            if (data.firstLogin === 1) {
                this.userInfoShowDialog = true
            } else{
                changeOrganLogin().then(data => {
                    if (data.data === 0) {
                        this.userInfoShowDialog = true
                    }
                })
            }
        },
        handleConfirm () {
            theFirstLogin({
                id: this.loginData.userId,
                enterpriseName: this.form.enterpriseName,
                receiverName: this.form.receiverName,
                area: this.form.area,
                detailAddress: this.form.detailAddress,
                receiverMobile: this.form.receiverMobile,
                province: this.form.province,
                city: this.form.city,
                county: this.form.county,
            }).then(() => {
                this.$message.success('保存成功')
                this.userInfoShowDialog = false
            })
        },
    },
}
</script>

<style scoped lang="scss">
/deep/ .el-dialog__header {
  padding: 0px 0px 0px;
}
.tabs-title::before {
  content: '';
  height: 15px;
  width: 6px;
  border-radius: 2px;
  background-color: #2e61d7;
  display: block;
  position: absolute;
  left: -8px;
  margin-top: 2px;
  margin-right: 20px;
}

.fixed-width-dialog {
  :deep(.el-dialog) {
    width: 600px !important;
    height: 400px !important;
  }
  .ellipsis-text {
    display: inline-block;
    max-width: 450px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    position: relative;
    z-index: 1;
  }
}
:deep(.el-button) {
  padding: 0px 20px !important;
}
:deep(.el-button--text) {
  color: #3b3c3d !important;
}
</style>