import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/brand/findByBrandLogo',
        params
    })
}
const getDataById = params => {
    return httpGet({
        url: '/materialMall//platform/brand/findById',
        params
    })
}

const edit = params => {
    return httpPost({
        url: '/materialMall/platform/brand/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/brand/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/brand/delete',
        params
    })
}

const batchPublish = params => {
    return httpPost({
        url: '/materialMall/platform/brand/updateByPublish',
        params
    })
}
//das
const batchNotPublish = params => {
    return httpPost({
        url: '/materialMall/platform/brand/updateNotPublish',
        params
    })
}
//
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/platform/brand/deleteBatch',
        params
    })
}

const changeSortValue = params => {
    return httpPost({
        url: '/materialMall/platform/brand/updateBatchById',
        params
    })
}

export {
    getList,
    edit,
    create,
    del,
    batchDelete,
    batchPublish,
    batchNotPublish,
    changeSortValue,
    getDataById
}
