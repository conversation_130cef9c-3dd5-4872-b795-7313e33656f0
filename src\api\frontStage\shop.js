import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getShopInfo = params => {
    return httpGet({
        url: '/materialMall/w/shop/getShopInfo',
        params,
    })
}
const getShopPhone = params => {
    return httpGet({
        url: '/materialMall/w/shop/getShopPhone',
        params,
    })
}
const listShopInfo = params => {
    return httpPost({
        url: '/materialMall/w/shop/listShopInfo',
        params,
    })
}
// 审核失败开店回显
const getRestartOpenShopInfo = params => {
    return httpGet({
        url: '/materialMall/userCenter/shop/getRestartOpenShopInfo',
        params,
    })
}
const getShopInfoById = params => {
    return httpGet({
        url: '/materialMall/userCenter/shop/findById',
        params,
    })
}
export {
    getShopInfo, getShopPhone, listShopInfo, getRestartOpenShopInfo, getShopInfoById
}