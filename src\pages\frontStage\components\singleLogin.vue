<template>
    <main>
        <div class="text">请稍后...</div>
    </main>
</template>
<script>
export default {
    data () {
        return {}
    },
    created () {
        setTimeout(() => {
            this.$router.replace('/index')
        }, 1000)
    },
    methods: {},
}
</script>
<style scoped>
main {
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}
.text {
    font-size: 22px;
    color: #333;
}
</style>