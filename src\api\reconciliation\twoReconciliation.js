import service from '@/utils/request'

// eslint-disable-next-line no-unused-vars
const { httpPost, httpGet } = service
const materialReconciliationPageList = params => {
    return httpPost({
        url: '/materialMall/materialReconciliation/listByEntity',
        params
    })
}
const supplierListByEntity = params => {
    return httpPost({
        url: '/materialMall/supplierReconciliation/listByEntity',
        params
    })
}
const getReconciliationDtlList = params => {
    return httpPost({
        url: '/materialMall/supplierReconciliationDtl/getReconciliationDtlList',
        params
    })
}
//物资公司二级订单查询订单信息
const getTwoMaterialOrderList = params => {
    return httpPost({
        url: '/materialMall/orders/getTwoMaterialOrderList',
        params
    })
}

//物资公司二级订单查询订单信息（新接口）
const getHaveTwoMaterialOrderList = params => {
    return httpPost({
        url: '/materialMall/orders/getHaveTwoMaterialOrderList',
        params
    })
}
//二级订单供应商查询订单信息
const getTwoSupplierMaterialOrderList = params => {
    return httpPost({
        url: '/materialMall/orders/getTwoSupplierMaterialOrderList',
        params
    })
}

/**
 * 查询发货单或者对账单生成二级对账单
 * @param params
 * @returns {*}
 */
const getTwoSupplierList = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/listByShopId',
        params
    })
}

/**
 * 供应商保存并提交二级对账单
 * @param params
 * @returns {*}
 */
const saveAndUpdate = params => {
    return httpPost({
        url: '/materialMall/supplierReconciliation/saveAndUpdate',
        params
    })
}
const ReconciliationSupplierCreate = params => {
    return httpPost({
        url: '/materialMall/supplierReconciliation/create',
        params
    })
}
const updateState = params => {
    return httpGet({
        url: '/materialMall/supplierReconciliation/updateState',
        params
    })
}
const update = params => {
    return httpPost({
        url: '/materialMall/supplierReconciliation/update',
        params
    })
}
const deleteById = params => {
    return httpGet({
        url: '/materialMall/supplierReconciliation/delete',
        params
    })
}

const supplierReconciliationSupplierAffirm = params => {
    return httpGet({
        url: '/materialMall/supplierReconciliation/supplierReconciliationSupplierAffirm',
        params
    })
}

const twoSupplierReconciliationSupplierAffirm = params => {
    return httpGet({
        url: '/materialMall/supplierReconciliation/twoSupplierReconciliationSupplierAffirm',
        params
    })
}
const findByNo = params => {
    return httpGet({
        url: '/materialMall/supplierReconciliation/findByNo',
        params
    })
}
const outputExcel = params => {
    return httpGet({
        url: '/materialMall/supplierReconciliation/outputExcel',
        params,
        responseType: 'blob',
    })
}

const materialReconciliationSubmit = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSubmit',
        params
    })
}

export {
    materialReconciliationPageList,
    ReconciliationSupplierCreate,
    getReconciliationDtlList,
    getTwoMaterialOrderList,
    getHaveTwoMaterialOrderList,
    getTwoSupplierMaterialOrderList,
    deleteById,
    supplierListByEntity,
    getTwoSupplierList,
    updateState,
    materialReconciliationSubmit,
    outputExcel,
    update,
    supplierReconciliationSupplierAffirm,
    twoSupplierReconciliationSupplierAffirm,
    findByNo,
    saveAndUpdate
}
