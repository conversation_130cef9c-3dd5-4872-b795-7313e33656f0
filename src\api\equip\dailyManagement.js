import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

//装备管理 - 日常管理 接口
const request = {
    //===============================================================================================运行记录接口

    //高级查询运行记录
    operationLogList (params) {
        return httpPost({
            url: '/facilitymanagement/running/record/list/advanced',
            params
        })
    },
    //获取运行记录基本信息
    getOperationLogInfo (billId) {
        return httpGet({
            url: '/facilitymanagement/running/record/get',
            params: {
                id: billId,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //新增运行记录基本信息
    operationLogAdd (params) {
        return httpPost({
            url: '/facilitymanagement/running/record/add',
            params
        })
    },
    //更新运行记录基本信息
    operationLogUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/running/record/update',
            params
        })
    },
    //删除运行记录
    operationLogDelete (billid) {
        return httpGet({
            url: '/facilitymanagement/running/record/delete',
            params: {
                id: billid
            }
        })
    },
    //获取运行记录明细
    getOperationLogDtl (billId) {
        return httpPostForm({
            url: '/facilitymanagement/running/record/dtl/list',
            params: {
                id: billId,
            },
        })
    },
    //运行明细新增、编辑、修改 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    operationLogDtlUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/running/record/dtl/update',
            params
        })
    },
    //===============================================================================================报废申请接口

    //高级查询报废申请
    scrapApplicationList (params) {
        return httpPost({
            url: '/facilitymanagement/scrap/apply/list/advanced',
            params
        })
    },
    //添加报废申请
    scrapApplicationAdd (params) {
        return httpPost({
            url: '/facilitymanagement/scrap/apply/add',
            params
        })
    },
    //提交报废申请
    scrapApplicationCommit (params) {
        return httpPostForm({
            url: '/facilitymanagement/scrap/apply/commit',
            params
        })
    },
    //删除报废申请
    scrapApplicationDelete (params) {
        return httpPostForm({
            url: '/facilitymanagement/scrap/apply/delete',
            params
        })
    },
    //作废报废申请
    scrapApplicationNullify (params) {
        return httpPostForm({
            url: '/facilitymanagement/scrap/apply/cancel',
            params
        })
    },
    //修改报废申请
    scrapApplicationUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/scrap/apply/update',
            params
        })
    },
    //获取报废申请基础信息
    scrapApplicationBaseInfo (params) {
        return httpPostForm({
            url: '/facilitymanagement/scrap/apply/get',
            params
        })
    },
    //获取报废申请明细
    scrapApplicationPlanInfo (params) {
        return httpGet({
            url: '/facilitymanagement/detail/list/scrap/apply/dtl',
            params
        })
    },
    //报废申请明细新增、编辑、修改 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    scrapApplicationDtlUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/detail/update/scrap/apply/dtl',
            params
        })
    },

    //===============================================================================================报废处置登记接口

    //高级查询报废处置登记
    scrapDisposalList (params) {
        return httpPost({
            url: '/facilitymanagement/scrap/register/list/advanced',
            params
        })
    },
    //获取报废处置登记信息
    scrapDisposalBaseInfo (params) {
        return httpPostForm({
            url: '/facilitymanagement/scrap/register/get',
            params
        })
    },
    //获取报废处置登记明细
    scrapDisposalPlanInfo (params) {
        return httpGet({
            url: '/facilitymanagement/detail/list/scrap/register/dtl',
            params
        })
    },

    //===============================================================================================固定资产盘点接口

    //高级查询固定资产盘点
    assetsInventoryList (params) {
        return httpPost({
            url: '/facilitymanagement/fixed/assets/check/list/advanced',
            params
        })
    },
    //添加固定资产盘点
    assetsInventoryAdd (params) {
        return httpPost({
            url: '/facilitymanagement/fixed/assets/check/add',
            params
        })
    },
    //提交固定资产盘点
    assetsInventoryCommit (params) {
        return httpPostForm({
            url: '/facilitymanagement/fixed/assets/check/commit',
            params
        })
    },
    //删除固定资产盘点
    assetsInventoryDelete (params) {
        return httpPostForm({
            url: '/facilitymanagement/fixed/assets/check/delete',
            params
        })
    },
    //作废固定资产盘点
    assetsInventoryNullify (params) {
        return httpPostForm({
            url: '/facilitymanagement/fixed/assets/check/cancel',
            params
        })
    },
    //修改固定资产盘点
    assetsInventoryUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/fixed/assets/check/update',
            params
        })
    },
    //获取固定资产盘点基础信息
    assetsInventoryBaseInfo (params) {
        return httpPost({
            url: '/facilitymanagement/fixed/assets/check/get',
            params
        })
    },
    //获取固定资产盘点明细
    assetsInventoryPlanInfo (params) {
        return httpGet({
            url: '/facilitymanagement/detail/list/fixed/assets/check/dtl',
            params
        })
    },
    //固定资产盘点明细汇总
    assetsInventorSmmary (params) {
        return httpPostForm({
            url: '/facilitymanagement/fixed/assets/check/collect',
            params
        })
    },

    //===============================================================================================维修保养记录接口

    //高级查询维修保养记录
    maintenanceRecordsList (params) {
        return httpPost({
            url: '/facilitymanagement/maintenance/log/list/advanced',
            params
        })
    },
    //添加维修保养记录
    maintenanceRecordsAdd (params) {
        return httpPost({
            url: '/facilitymanagement/maintenance/log/add',
            params
        })
    },
    //提交维修保养记录
    maintenanceRecordsCommit (params) {
        return httpPostForm({
            url: '/facilitymanagement/maintenance/log/commit',
            params
        })
    },
    //删除维修保养记录
    maintenanceRecordsDelete (params) {
        return httpPostForm({
            url: '/facilitymanagement/maintenance/log/delete',
            params
        })
    },
    //修改维修保养记录
    maintenanceRecordsUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/maintenance/log/update',
            params
        })
    },
    //获取维修保养记录基础信息
    maintenanceRecordsBaseInfo (billId) {
        return httpGet({
            url: '/facilitymanagement/maintenance/log/get',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            params: {
                id: billId,
            }
        })
    },
    //获取维修保养记录明细
    maintenanceRecordsPlanInfo (billId) {
        return httpGet({
            url: '/facilitymanagement/detail/list/maintenance/log/dtl',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            params: {
                id: billId,
            }
        })
    },
    //维修保养记录明细新增、修改、删除
    maintenanceRecordsDtlUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/maintenance/log/dtl/update',
            params
        })
    },
    //===============================================================================================维修保养申请接口

    //高级查询维修保养申请
    maintenanceApplicationList (params) {
        return httpPost({
            url: '/facilitymanagement/maintenance/application/summary/list/advanced',
            params
        })
    },
    //添加维修保养申请
    maintenanceApplicationAdd (params) {
        return httpPost({
            url: '/facilitymanagement/maintenance/application/summary/add',
            params
        })
    },
    //提交维修保养申请
    maintenanceApplicationCommit (params) {
        return httpPostForm({
            url: '/facilitymanagement/maintenance/application/summary/commit',
            params
        })
    },
    //删除维修保养申请
    maintenanceApplicationDelete (params) {
        return httpPost({
            url: '/facilitymanagement/maintenance/application/summary/delete',
            params
        })
    },
    //修改维修保养申请
    maintenanceApplicationUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/maintenance/application/summary/update',
            params
        })
    },
    //获取维修保养申请基础信息
    maintenanceApplicationBaseInfo (params) {
        return httpGet({
            url: '/facilitymanagement/maintenance/application/summary/get',
            params
        })
    },
    //获取维修保养申请明细
    maintenanceApplicationPlanInfo (params) {
        return httpGet({
            url: '/facilitymanagement/detail/list/maintenance/summary/dtl',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            params
        })
    },
    //维修保养申请明细新增、编辑、修改 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    maintenanceApplicationDtlUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/detail/update/maintenance/summary/dtl',
            params
        })
    },

    //===============================================================================================其他接口

    //高级查询其他
    otherList (params) {
        return httpPost({
            url: '/facilitymanagement/rests/list/advanced',
            params
        })
    },
    //添加其他
    otherAdd (params) {
        return httpPost({
            url: '/facilitymanagement/rests/add',
            params
        })
    },
    //提交其他
    otherCommit (params) {
        return httpPostForm({
            url: '/facilitymanagement/rests/commit',
            params
        })
    },
    //删除其他
    otherDelete (params) {
        return httpPostForm({
            url: '/facilitymanagement/rests/delete',
            params
        })
    },
    //修改其他
    otherUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/rests/update',
            params
        })
    },
    //获取其他基础信息
    otherBaseInfo (params) {
        return httpPostForm({
            url: '/facilitymanagement/rests/get',
            params
        })
    },
    //获取其他明细
    otherPlanInfo (params) {
        return httpGet({
            url: '/facilitymanagement/detail/rests/dtl',
            params
        })
    },
}

export default request
