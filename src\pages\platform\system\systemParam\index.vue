<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" v-show="viewList === true">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <div>
            <div class="left">
              <div class="left-btn">
                  <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                  <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
              </div>
            </div>
          </div>
          <!-- 新增按钮 -->
          <div class="search_box">
            <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" :style="{ width: '100%' }">
        <el-table class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange" row-key="systemId">
          <el-table-column type="selection" width="40" :reserve-selection="true"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""></span>
            </template>
          </el-table-column>
          <!-- 名称 -->
          <el-table-column label="参数名称" width="">
            <template slot-scope="scope">
              <span class="action" @click="handleView(scope)">{{scope.row.name}}</span>
            </template>
          </el-table-column>
          <!-- 值 -->
          <el-table-column label="值" width="">
            <template slot-scope="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.keyValue }}
                            </span>
            </template>
            <!-- 说明 -->
          </el-table-column>
          <el-table-column label="说明" width="">
            <template slot-scope="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.remarks }}
                            </span>
            </template>
          </el-table-column>

          <el-table-column label="修改时间" width="">
            <template slot-scope="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.gmtModified }}
                            </span>
            </template>
          </el-table-column>

        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                     :currentPage.sync="pages.currPage"
                     @currentChange="currentChange" @sizeChange="sizeChange" />
    </div>
    <div class="right" v-show="viewList !== true">
      <!-- ---------------------新增编辑窗口--------------------- -->
      <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
        <div class="tabs-title">基本信息</div>
        <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="12">
              <el-form-item width="150px" label="参数名称：" prop="name">
                <el-input v-model="formData.name" placeholder="" clearable></el-input>
<!--                <span>{{formData.name}}</span>-->
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item width="150px" label="说明：" prop="remarks">
                <el-input v-model="formData.remarks" placeholder="" clearable></el-input>
<!--                <span>{{formData.remarks}}</span>-->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
          <el-col :span="24">
            <el-form-item width="150px" label="值：" prop="keyValue">
              <el-input v-model="formData.keyValue" placeholder="" clearable></el-input>
            </el-form-item>
          </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="footer">
        <div class="right-btn">
          <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
          <el-button @click="onCancel">取消</el-button>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getListSys, update, createSysParam, batchDelete, del } from '@/api/platform/system/systemParam'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapActions } from 'vuex'
export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getListSys(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertname: '消息',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            tableData: [],
            formRules: {
                name: [{ required: true, message: '请输入名称', validator: this.validate, trigger: 'blur' },
                    { max: 32, message: '名称长度不能超过32个字符', trigger: 'blur' }
                ],
                keyValue: [{ required: true, message: '请输入值', validator: this.validate, trigger: 'blur' },
                    { max: 64, message: '值长度不能超过64个字符', trigger: 'blur' }
                ],
                remarks: [{ max: 128, message: '说明长度不能超过128个字符', trigger: 'blur' }]
            },
            formData: {
                name: '',
                remarks: '',
                keyValue: '',
            },
            mapObj: null,
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            filterData: { name: '',
                orderBy: 3,
                type: 0,
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            orderBy: this.filterData.orderBy,
            type: this.filterData.type
        }
        getListSys(params).then(res => {
            this.isLoading = false
            this.pages = res
            this.tableData = res.list
            /*let arr = []
            res.list.forEach(item => {
                if(item.type == 0) arr.push(item)
            })
            this.pages = res
            this.tableData = arr*/
        })
        this.getParams()
    },
    methods: {
        validate (rule, value, callback) {
            let len = value.trim().length
            if (len <= 0) {
                return callback(new Error('请不要输入纯空格！'))
            }
            callback()
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该信息吗？', async () => {
                showLoading()
                del({ id: scope.row.systemId }).then(res => {
                    if(res.message === '操作成功') {
                        /*this.clientPop('suc', '删除成功', () => {
                            this.onSearch()
                        })*/
                        this.$message.success('删除成功')
                        this.onSearch()
                    }else{
                        this.clientPop('warn', res.message, () => {})
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if(!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {})
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.systemId
                })
                batchDelete(arr).then(res => {
                    if(res.message === '操作成功') {
                        /* this.clientPop('suc', '删除成功', () => {
                            this.onSearch()
                        })*/
                        this.$message.success('删除成功')
                        this.onSearch()
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                Name: '',
                title: '',
                orderBy: 2
            },
            done()
        },
        hideDialog () {
            this.filterData = {
                name: '',
                orderBy: 2
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },

        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () { },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = scope.row
            this.action = '编辑'
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            getListSys(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                this.pages = res
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            this.selectedRows = []
            this.pages.currPage = 1
            // 参数
            this.getParams()
            getListSys(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                    this.pages.totalCount = res.totalCount
                    this.pages.pageSize = res.pageSize
                    this.pages.currPage = res.currPage
                } else {
                    this.clientPop('warn', res.message, () => {})
                }
            })
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    this.clientPop('info', '确认保存数据吗？', () => {
                        if (this.action === '编辑') {
                            return this.handleEditData()
                        }
                        this.handleCreateData()
                    })
                }
            })
        },
        // 修改数据
        handleEditData () {
            update(this.formData).then(res => {
                if (res.message == '操作成功') {
                    /*return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })*/
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        // 保存数据
        handleCreateData () {
            createSysParam(this.formData).then(res => {
                if (res.message == '操作成功') {
                    /*return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })*/
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.right .top {padding-right: 10px}
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__explain {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}
.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

</style>
