import service from '@/utils/request'

const { httpPost, httpGet } = service

const getSysList = params => {
    return httpGet({
        url: '/materialMall/bgmanage/system/menu/syslist',
        params
    })
}

const saveMenu = params => {
    return httpPost({
        url: '/materialMall/bgmanage/system/menu/add',
        params
    })
}

const getMenuTree = params => {
    return httpGet({
        url: '/materialMall/bgmanage/system/menu/tree',
        params
    })
}

const updateMenu = params => {
    return httpPost({
        url: '/materialMall/bgmanage/system/menu/update',
        params
    })
}

const deleteMenu = (params, urlPath) => {
    return httpPost({
        url: '/materialMall/bgmanage/system/menu/delete/' + urlPath,
        params
    })
}

export {
    getSysList, saveMenu, getMenuTree, updateMenu, deleteMenu
}