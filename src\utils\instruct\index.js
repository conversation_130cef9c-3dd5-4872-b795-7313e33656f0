/** v-has-permi
 * {platform:'supplierManage',auth:'submit-bid'} 供应商平台
 * {platform:'oneselfShopManage',auth:'submit-bid'} 自营店供应商平台
 * {platform:'performanceManage',auth:'sheet-submit'} 履约管理平台
 * 是否有权限 自定义指令
 */
import store from '@/store'
const hasPermi =  {
    inserted (el, binding, vnode) { // eslint-disable-line
        // 如果是数zu
        // if (Array.isArray(binding.value)) {
        //     // 存在其中一个即可
        //     const authArr = binding.value.map(item=>item.auth)
        //     // 检查binding.value中的任意一个元素是否存在于permissionArr中
        //     console.log(authArr)
        //     console.log(permissionArr)
        //     const isPermission = permissionArr.some(val => authArr.some(arr => arr === val.auth))
        //     if (!isPermission) {
        //         el.parentNode && el.parentNode.removeChild(el)
        //         return
        //     }else {
        //         return
        //     }
        // }
        const { platform, auth } = binding.value
        const userInfo = store.state.userInfo
        if (userInfo.shopName != '四川路桥自营店') return
        const menuKey = platform + 'Menu'
        const permissionArr = userInfo.mapSysMenu[menuKey] || []
        const  hasPermission = permissionArr.includes(auth)
        if (!hasPermission && this.showDevFunc) {
            el.parentNode && el.parentNode.removeChild(el)
        }
    }
}
export  default hasPermi
