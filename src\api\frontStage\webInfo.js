import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getWebInfo = params => {
    return httpPost({
        url: '/materialMall/w/homePage/more/findContenByCondition',
        params,
    })
}

const getWebInfoDetail = params => {
    return httpGet({
        url: '/materialMall/w/homePage/more/findContenById',
        params,
    })
}
const getInfoById = params => {
    return httpGet({
        url: '/materialMall/w/homePage/more/findContenById',
        params,
    })
}
// 历史交易
const getOrderHistory = params => {
    return httpPost({
        url: '/materialMall/userCenter/orders/history/orderItem',
        params,
    })
}
export {
    getWebInfo, getWebInfoDetail, getInfoById, getOrderHistory
}