function numAutoPlus (target, xiaoshu, options) {
    options = options || {}
    // 获取dom元素
    let currentElement = document.querySelector(
    `.data_title_module .data .item .box .${target}`
    )
    // 动画时长(毫秒数)
    // 也可以将需要的参数写在dom上如：[data-XXX]
    let time = options.time || currentElement.getAttribute('data-time')
    // 最终要显示的数字
    let finalNum = options.num || currentElement.getAttribute('data-time')
    // 调节器(毫秒数) 改变数字增加速度
    let rate = options.rate || currentElement.getAttribute('data-rate')
    // 步长
    let step = finalNum / (time / rate)
    // 计数器
    let count = 0
    // 初始值
    let initNum = 0
    // 定时器
    let timer = setInterval(function () {
        count = count + step
        if (count >= finalNum) {
            clearInterval(timer)
            count = finalNum
        }
        // t未发生改变的话就直接返回
        // 减少dom操作 提高DOM性能
        // let t = Math.floor(count)
        let t = count.toFixed(xiaoshu)
        if (t === initNum) return
        initNum = t
        currentElement.innerHTML = initNum
    }, 30)
}

numAutoPlus('number1', 0, {
    time: 2000,
    num: 4299,
    rate: 40,
})

numAutoPlus('number2', 0, {
    time: 2000,
    num: 5199,
    rate: 40,
})

numAutoPlus('number3', 0, {
    time: 2000,
    num: 4799,
    rate: 40,
})

numAutoPlus('number4', 0, {
    time: 2000,
    num: 5399,
    rate: 40,
})
