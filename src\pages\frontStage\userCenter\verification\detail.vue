<template>
    <main>
        <!-- 标题栏 -->
        <div class="list-title mb20 df">{{ title }}
            <div class="edit" @click="handleEdit"><img src="@/assets/images/userCenter/ico_edit.png" alt="">修改</div>
        </div>
        <div class="content p20" v-loading="formLoading">
            <!-- 查看企业详情 -->
            <el-form :model="enterpriseAuthInfo" ref="enterpriseForm" label-width="144px" :inline="false"
                     v-if="title == '查看企业详情'">
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item class="license" label="营业执照图片：">
                            <img
                                :src="enterpriseAuthInfo.businessLicense ?enterpriseAuthInfo.businessLicense : require('@/assets/images/userCenter/yyzz_demo.png')"
                                alt="">
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="企业名称：">
                            <span>{{ enterpriseAuthInfo.enterpriseName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="统一社会信用代码：">
                            <span>{{ enterpriseAuthInfo.socialCreditCode }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <!--                    <el-col :span="12">-->
                    <!--                        <el-form-item label="企业类型：">-->
                    <!--                            <span>{{ enterpriseAuthInfo.socialCreditCode }}</span>-->
                    <!--                        </el-form-item>-->
                    <!--                    </el-col>-->
                    <el-col :span="12">
                        <el-form-item label="法定代表人：">
                            <span>{{ enterpriseAuthInfo.legalRepresentative }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="成立时间：">
                            <span>{{ enterpriseAuthInfo.creationTime | dateStr }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="营业执照有效期：">
                            <span>{{ enterpriseAuthInfo.licenseTerm | dateStr }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="注册资本(万元)：">
                            <span>{{ enterpriseAuthInfo.registeredCapital }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="税率：">
                            <span>{{ enterpriseAuthInfo.taxRate }}%</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="注册地址：">
                            <span>{{ enterpriseAuthInfo.detailedAddress }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="主营业务：">
                            <span>{{ enterpriseAuthInfo.mainBusiness }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="separ"></div>
                <div class="admin">管理员信息</div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="手机号码：">
                            <span>{{ enterpriseAuthInfo.adminPhone }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="姓名：">
                            <span>{{ enterpriseAuthInfo.adminName }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="身份证号码：">
                            <span>{{ enterpriseAuthInfo.adminNumber }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="separ"></div>
                <div class="admin">附件资料</div>
                <el-table
                    v-loading="openShopLoading"
                    ref="msgTable"
                    :data="enterpriseAuthInfo.files"
                    style="min-height: 472px"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="name" label="附件名称" width="">
                    </el-table-column>
                    <el-table-column prop="" label="操作" width="90">
                        <template slot-scope="scope">
                            <el-button size="small" type="primary" @click="handleDownload(scope.row)">下载</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <!-- 查看个体户详情 -->
            <el-form :model="businessForm" ref="businessForm" label-width="144px" :inline="false"
                     v-if="title == '查看个体户详情'">
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item class="license" label="营业执照图片：">
                            <img
                                :src="businessForm.businessLicense ? businessForm.businessLicense : require('@/assets/images/userCenter/yyzz_demo.png')"
                                alt="">
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="名称：">
                            <span>{{ businessForm.enterpriseName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="统一社会信用代码：">
                            <span>{{ businessForm.socialCreditCode }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="经营者：">
                            <span>{{ businessForm.operator }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="注册日期：">
                            <span>{{ businessForm.creationTime }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="税率：">
                            <span>{{ businessForm.taxRate }}%</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="经营场所：">
                            <span>{{ businessForm.placeOfBusiness }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="经营范围：">
                            <span>{{ businessForm.mainBusiness }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="separ"></div>
                <div class="admin">管理员信息</div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="手机号码：">
                            <span>{{ businessForm.adminPhone }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="姓名：">
                            <span>{{ businessForm.adminName }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="身份证号码：">
                            <span>{{ businessForm.adminNumber }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="separ"></div>
                <div class="admin">附件资料</div>
                <el-table
                    v-loading="openShopLoading"
                    ref="msgTable"
                    :data="businessForm.files"
                    style="min-height: 472px"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="name" label="附件名称" width="">
                    </el-table-column>
                    <el-table-column prop="" label="操作" width="90">
                        <template slot-scope="scope">
                            <el-button size="small" type="primary" @click="handleDownload(scope.row)">下载</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <!-- 查看个人详情 -->
            <div class="individual center" v-if="title == '查看个人详情'">
                <div class="row dfa">
                    <div class="rowLabel">身份证照片：</div>
                    <div class="rowContent">
                        <img
                            :src="individualForm.cardPortraitFace ? individualForm.cardPortraitFace : require('@/assets/images/img/queshen3.png')"
                            alt="">
                        <img
                            :src="individualForm.cardPortraitNationalEmblem ?individualForm.cardPortraitNationalEmblem : require('@/assets/images/img/queshen3.png')"
                            alt="">
                    </div>
                </div>
                <div class="row dfa">
                    <div class="rowLabel">姓名：</div>
                    <div class="rowContent">{{ individualForm.adminName }}</div>
                </div>
                <div class="row dfa">
                    <div class="rowLabel">身份证号：</div>
                    <div class="rowContent">{{ individualForm.adminNumber }}</div>
                </div>
                <div class="row dfa">
                    <div class="rowLabel">手机号：</div>
                    <div class="rowContent">{{ individualForm.adminPhone }}</div>
                </div>
            </div>
        </div>
    </main>
</template>
<script>
import { getEnterpriseAuthInfo } from '@/api/frontStage/verification'
import { previewFile } from '@/api/platform/common/file'

export default {
    data () {
        return {
            openShopLoading: false,
            formLoading: false,
            title: '',
            // 个人详情表单
            individualForm: {},
            // 企业详情表单
            enterpriseAuthInfo: {},
            // 个体户详情表单
            // 个体户详情表单
            businessForm: {}
        }
    },
    created () {
        this.formLoading = true
        if (this.$route.query.type == 'individual') {
            this.title = '查看个人详情'
            this.getEnterpriseAuthInfoM3()
        } else if (this.$route.query.type == 'enterprise') {
            this.title = '查看企业详情'
            this.getEnterpriseAuthInfoM()
        } else if (this.$route.query.type == 'business') {
            this.title = '查看个体户详情'
            this.getEnterpriseAuthInfoM2()
        }
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            let newDateSr = dateStr.split('-')
            let day = newDateSr[2].split(' ')[0]
            return newDateSr[0] + '年' + newDateSr[1] + '月' + day + '日'
        }
    },
    mounted () {
    },
    methods: {
        async handleDownload (file) {
            this.openShopLoading = true
            previewFile({ recordId: file.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = file.name
                a.click()
                window.URL.revokeObjectURL(url)
                this.openShopLoading = false
            }).catch(() => {
                this.openShopLoading = false
            })
        },
        getEnterpriseAuthInfoM3 () {
            getEnterpriseAuthInfo({}).then(res => {
                this.individualForm = res
                if (this.individualForm.cardPortraitFaceId != null) {
                    previewFile({ recordId: this.individualForm.cardPortraitFaceId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.individualForm.cardPortraitFace = url
                    })
                }
                if (this.individualForm.cardPortraitNationalEmblemId != null) {
                    previewFile({ recordId: this.individualForm.cardPortraitNationalEmblemId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.individualForm.cardPortraitNationalEmblem = url
                    })
                }
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        getEnterpriseAuthInfoM2 () {
            getEnterpriseAuthInfo({}).then(res => {
                this.businessForm = res
                previewFile({ recordId: this.businessForm.businessLicenseId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.businessLicense = url
                })
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        getEnterpriseAuthInfoM () {
            getEnterpriseAuthInfo({}).then(res => {
                this.enterpriseAuthInfo = res
                previewFile({ recordId: this.enterpriseAuthInfo.businessLicenseId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseAuthInfo.businessLicense = url
                })
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 跳转修改页面
        handleEdit () {
            if (this.title == '查看企业详情') {
                return this.$router.push('/user/reverify/enterprise')
            }
            if (this.title == '查看个体户详情') {
                return this.$router.push('/user/reverify/business')
            }
            this.$router.push('/user/reverify/individual')
        },
    },
}
</script>
<style scoped lang="scss">
main > div {
    height: 100%;
    border: 1px solid rgba(230, 230, 230, 1);
}

.content {
    min-height: 634px;
}

.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;
    position: relative;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }

    .edit {
        font-size: 16px;
        color: #216EC6;
        position: absolute;
        right: 30px;
        cursor: pointer;

        img {
            width: 14px;
            height: 14px;
            margin-right: 6px;
        }
    }
}

// 个人详情
.individual {
    width: 512px;

    .row {
        margin-bottom: 30px;
        font-size: 16px;
        color: #333;
    }

    .rowLabel {
        width: 147px;
        margin-right: 10px;
        text-align: right;
    }

    .rowContent {
        img {
            width: 160px;
            height: 100px;
            object-fit: cover;
        }

        img:first-of-type {
            margin-right: 30px;
        }
    }
}

// 表单
/deep/ .el-form-item__label {
    margin-right: 24px;
    // margin-bottom: 30px;
    padding-right: 0;
    font-size: 16px;
    color: #333;

    &.license {
        margin-bottom: 38px;
    }
}

/deep/ .el-form-item__content {
    font-size: 16px;
    color: #333;

    img {
        width: 120px;
        height: 90px;
        object-fit: cover;
    }
}

.separ {
    width: 1066px;
    height: 1px;
    margin: 18px 0 13px;
    border-top: 1px dashed rgba(204, 204, 204, 1);
}

.admin {
    margin-left: 40px;
    margin-bottom: 30px;
    font-size: 20px;
    color: #216EC6;
}
</style>