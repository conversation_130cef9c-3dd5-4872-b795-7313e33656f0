export default [
    {
        path: '/inspection',
        component: () => import('@/pages/inspection/index'),
        redirect: '/inspection/biddingManage/openTendering',
        children: [
            //内容管理
            // {
            //     path: '/platform/content/richContent',
            //     component: () => import('@/pages/platform/content/richContent/index'),
            //     meta: {
            //         title: '物资贸易管理-新闻'
            //     }
            // },
            {
                path: '/inspection/biddingManage/openTendering',
                component: () => import('@/pages/inspection/biddingManage/openTendering.vue'),
                meta: {
                    title: '招标管理-公开招标'
                }
            },
            {
                path: '/inspection/biddingManage/invitebiding',
                component: () => import('@/pages/inspection/biddingManage/invitebiding.vue'),
                meta: {
                    title: '招标管理-邀请招标'
                }
            },
            {
                path: '/inspection/biddingManage/enquirybiding',
                component: () => import('@/pages/inspection/biddingManage/enquirybiding.vue'),
                meta: {
                    title: '招标管理-询价'
                }
            },
            {
                path: '/inspection/biddingManage/oneTender',
                component: () => import('@/pages/inspection/biddingManage/oneTender.vue'),
                meta: {
                    title: '招标管理-单一性来源'
                }
            },
            {
                path: '/inspection/biddingManage/competebiding',
                component: () => import('@/pages/inspection/biddingManage/competebiding.vue'),
                meta: {
                    title: '招标管理-竞争性招标'
                }
            },

            {
                path: '/inspection/biddingManage/openTenderingDetial',
                name: 'openTenderingDetial',
                component: () => import('@/pages/inspection/biddingManage/detail/openTenderingDetail.vue'),
                meta: {
                    title: '招标管理-公开招标'
                }
            },
            {
                path: '/inspection/biddingManage/invitebidingDetial',
                name: 'invitebidingDetial',
                component: () => import('@/pages/inspection/biddingManage/detail/invitebidingDetail.vue'),
                meta: {
                    title: '招标管理-邀请招标'
                }
            },
            {
                path: '/inspection/biddingManage/enquirybidingDetial',
                name: 'enquirybidingDetial',
                component: () => import('@/pages/inspection/biddingManage/detail/enquirybidingDatial.vue'),
                meta: {
                    title: '招标管理-询价'
                }
            },
            {
                path: '/inspection/biddingManage/oneTenderDetial',
                name: 'oneTenderDetial',
                component: () => import('@/pages/inspection/biddingManage/detail/oneTenderDetail.vue'),
                meta: {
                    title: '招标管理-单一性来源'
                }
            },
            {
                path: '/inspection/biddingManage/competebidingDetial',
                name: 'competebidingDetial',
                component: () => import('@/pages/inspection/biddingManage/detail/competebidingDetail.vue'),
                meta: {
                    title: '招标管理-竞争性招标'
                }
            },

        ]
    }
]
