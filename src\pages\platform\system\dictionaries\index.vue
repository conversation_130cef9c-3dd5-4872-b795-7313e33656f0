<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                            <el-button type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                          @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="80">
                        <template slot-scope="scope">
                            <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""></span>
                        </template>
                    </el-table-column>
                    <!-- 名称 -->
                    <el-table-column label="字典名称" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">{{scope.row.name}}</span>
                        </template>
                    </el-table-column>
                    <!-- 值 -->
                    <el-table-column label="名称" width="120" type="index">
                        <template v-slot="scope">
                            <el-input v-model="scope.row.keyValue" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input v-model="scope.row.keyValue2" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="说明" width="120" type="index">
                        <template v-slot="scope">
                            <el-input v-model="scope.row.remarks" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input type="number" v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>

                    <el-table-column label="修改时间" width="">
                        <template slot-scope="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.gmtModified }}
                            </span>
                        </template>
                    </el-table-column>

                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                           :currentPage.sync="pages.currPage"
                           @currentChange="currentChange" @sizeChange="sizeChange" />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新增编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <!--              <el-form-item width="150px" label="字典名称：" prop="name">-->
                            <!--                <el-input v-model="formData.name" placeholder="" clearable></el-input>-->
                            <!--              </el-form-item>-->
                            <el-form-item width="150px" label="字典名称：" prop="code">
                                <el-select ref="selectLabel1" v-model="formData.code" placeholder="字典名称" >
                                    <el-option v-for="item in initSysParamFilter" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item width="150px" label="名称：" prop="keyValue">
                                <el-input v-model="formData.keyValue" placeholder="" clearable></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="值：" prop="keyValue2">
                                <el-input v-model="formData.keyValue2" placeholder="" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input v-model="formData.sort" type="number" placeholder="填写排序值">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item width="150px" label="说明：" prop="remarks">
                                <el-input v-model="formData.remarks" placeholder="" clearable></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>

    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getList, update, create, updateByBatch, batchDelete, del, getListSystemInitVos } from '@/api/platform/system/systemParam'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapActions } from 'vuex'
export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler (val) {
                console.log(val)
                this.getParams()
                getList(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }
        },
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertname: '字典',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            tableData: [],
            formRules: {
                keyValue: [{ required: true, message: '请输入名称', trigger: 'blur' }],
                code: [{ required: true, message: '请选择字典名称', trigger: 'blur' }],
                keyValue2: [{ required: true, message: '请输入值', trigger: 'blur' }],
            },
            formData: {
                name: '',
                code: '',
                remarks: '',
                keyValue: '',
                keyValue2: '',
                sort: '',
            },
            initSysParamFilter: [],
            mapObj: null,
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            filterData: { name: '',
                orderBy: 3,
                type: 1,
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        this.getParams()
        let params = this.requestParams
        getList(params).then(res => {
            let arr = []
            res.list.forEach(item => {
                if(item.type == 1) arr.push(item)
            })
            this.pages = res
            this.tableData = arr
        })
        getListSystemInitVos(params).then(res => {
            this.initSysParamFilter = res
        })

    },
    methods: {
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                Name: '',
                title: '',
            },
            done()
        },
        hideDialog () {
            this.filterData = {
                name: '',
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData,
                type: 1
            }
            this.requestParams.page = this.pages.currPage
        },

        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () { },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = scope.row
            this.action = '编辑'
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该信息吗？', async () => {
                showLoading()
                del({ id: scope.row.systemId }).then(res => {
                    if(res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }else{
                        this.clientPop('warn', res.message, () => {})
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if(!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {})
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.systemId
                })
                batchDelete(arr).then(res => {
                    if(res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if(this.changedRow.length == 0) {
                this.changedRow.push(row)
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if(t.systemId == row.systemId) {
                    t = row
                    flag = true
                }
            })
            if(!flag) {
                this.changedRow.push(row)
            }
            flag = true
        //     if(!this.changedRow[0]) {
        //         return this.changedRow.push({ systemId: row.systemId, keyValue: parseInt(row.keyValue), keyValue2: parseInt(row.keyValue2), sort: parseInt(row.sort), remarks: parseInt(row.remarks) })
        //     }
        //     let sortArr = this.changedRow.map((item, i) => {
        //         if(item.systemId === row.systemId) return i
        //     })
        //     let keyValueArr = this.changedRow.map((item, i) => {
        //         if(item.keyValue === row.keyValue) return i
        //     })
        //     let keyValue2Arr = this.changedRow.map((item, i) => {
        //         if(item.keyValue2 === row.keyValue2) return i
        //     })
        //     let remarksArr = this.changedRow.map((item, i) => {
        //         if(item.remarks === row.remarks) return i
        //     })
        //     if(sortArr[0]) {
        //         return this.changedRow[sortArr[0]].sort = row.sort
        //     } else if(keyValueArr[0]) {
        //         return this.changedRow[keyValueArr[0]].keyValue = row.keyValue
        //     }else if(keyValue2Arr[0]) {
        //         return this.changedRow[keyValue2Arr[0]].keyValue2 = row.keyValue2
        //     }else if(remarksArr[0]) {
        //         return this.changedRow[remarksArr[0]].remarks = row.remarks
        //     }
        //     return this.changedRow.push({ systemId: row.systemId, keyValue: parseInt(row.keyValue), keyValue2: parseInt(row.keyValue2), sort: parseInt(row.sort), remarks: parseInt(row.remarks) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                let msg = '当前没有值被修改！'
                return this.clientPop('warn', msg, () => { })
            }
            let warnMsg = '您确定要修改‘这些’的值吗？'
            this.clientPop('info', warnMsg, async () => {
                updateByBatch(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.changedRow = []
                        this.clientPop('suc', '修改成功', () => {
                            this.getTableData()
                        })
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            getList(this.requestParams).then(res => {
                let arr = []
                res.list.forEach(item => {
                    if(item.type == 1) arr.push(item)
                })
                this.pages = res
                this.tableData = arr
            })
            // getList(this.requestParams).then(res => {
            //     console.log(res.list)
            //     if (res.list) {
            //         this.tableData = res.list
            //     } else {
            //         this.clientPop('warn', res.message, () => { })
            //     }
            //     console.log(res)
            //     this.pages = res
            // })
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {})
                }
            })
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    this.clientPop('info', '确认保存数据吗？', () => {
                        if (this.action === '编辑') {
                            return this.handleEditData()
                        }
                        this.handleCreateData()
                    })
                }
            })
        },
        // 修改数据
        handleEditData () {
            update(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 保存数据
        handleCreateData () {
            this.formData.name = this.$refs.selectLabel1.selectedLabel
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.right .top {padding-right: 10px}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__explain {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}
.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
