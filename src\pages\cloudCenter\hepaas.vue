<template>
  <!-- <iframe id="frame" :src="url" frameborder="0"></iframe> -->
  <WuJie :url="url" name="hepaas"></WuJie>
</template>
<script>
import <PERSON><PERSON><PERSON> from 'wujie-vue2'
export default {
    name: 'hepaas',
    components: { <PERSON><PERSON><PERSON> },
    data () {
        return {
            url: 'http://*************:9022',
        }
    },
    methods: {
        sendMessage () {
            let frame = document.getElementById('frame')
            frame.onload = () => {
                console.log(frame.contentWindow)
                setTimeout(() => frame.contentWindow.postMessage('this is a msg from parent', '*'), 500)
            }
        },
    },
    mounted () {
        this.sendMessage()
    },
}
</script>
<style lang="scss">
#frame {
  width: 100%;
  height: 100%;
}
</style>
