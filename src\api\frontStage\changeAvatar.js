import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const getList = params => {
    return httpGet({
        url: '/materialMall/platform/user/findById',
        params,
    })
}

const update = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/update',
        params,
    })
}
const getUserData = params => {
    return httpGet({
        url: '/materialMall/userCenter/user/getUserData',
        params,
    })
}
const updateImg = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/updateImgByUserId',
        params,
    })
}
export {
    getList,
    update,
    getUserData,
    updateImg
}