<!--<template>-->
<!--    <div class="root">-->
<!--        <div class="box center p20">-->
<!--            <div class="addr-bar dfb">-->
<!--                <div>全部商品 {{totalProductCount}}</div>-->
<!--                <div>-->
<!--                    <span>配送至：</span>-->
<!--                    <el-select v-model="selectedAddr" placeholder="请选择配送地址">-->
<!--                        <el-option v-for="item in addrList" :key="item.id" :label="item.addr" :value="item.id">-->
<!--                        </el-option>-->
<!--                    </el-select>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="title dfa">-->
<!--                <el-checkbox v-model="selectAllProduct" label="全选" :indeterminate="false" @change="toggleSelectAll"></el-checkbox>-->
<!--                <div>商品</div>-->
<!--                <div>单价</div>-->
<!--                <div>数量</div>-->
<!--                <div>小计</div>-->
<!--                <div>操作</div>-->
<!--            </div>-->
<!--            <div class="product">-->
<!--                <div class="shop" v-for="(item, i) in rearrangedList" :key="i">-->
<!--                    <div class="shop-name dfa">-->
<!--                        <el-checkbox v-model="item.checked" :label="item.shopName" :indeterminate="false" @change="selectAllInShop(i)"></el-checkbox>-->
<!--                        <img :src="item.list[0].icon" alt="">-->
<!--                    </div>-->
<!--                    <div class="product-item dfa" v-for="(product, index) in item.list" :key="index">-->
<!--                        <div class="checkbox"><el-checkbox v-model="product.checked"></el-checkbox></div>-->
<!--                        <img :src="product.img" alt="">-->
<!--                        <div class="title-box">-->
<!--                            <div class="dfa"><div class="tag">平台自营</div><div class="name">{{product.productName}}</div></div>-->
<!--                            <div>{{product.specs}}</div>-->
<!--                        </div>-->
<!--                        <div class="price">￥{{product.price}}</div>-->
<!--                        <div class="num-box">-->
<!--                            <div class="numCalc df">-->
<!--                                <div @click="changeNum('minus', product)">-</div><input v-model="product.quantity" type="text"><div @click="changeNum('plus', product)">+</div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="total">￥{{product.total}}</div>-->
<!--                        <div class="operate">-->
<!--                            <div @click="deleteProduct(product.id)">删除</div>-->
<!--                            <div @click="addToCollection">加入收藏</div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="bottom-bar dfb">-->
<!--                <div class="bar-left dfa">-->
<!--                    <el-checkbox v-model="selectAllProduct" label="全选" :indeterminate="false" @change="toggleSelectAll"></el-checkbox>-->
<!--                    <span @click="deleteSelected">删除选中的商品</span>-->
<!--                    <span>移入收藏</span>-->
<!--                    <span>清理购物车</span>-->
<!--                </div>-->
<!--                <div class="bar-right df">-->
<!--                    <div><span>已选择 {{totalSelected}} 件商品</span><i class="el-icon-arrow-up"></i></div>-->
<!--                    <div class="bar-right-price dfa"><span>总价：</span><span>￥{{totalPrice}}</span><img src="../../../../assets/images/userCenter/提示.png" alt=""></div>-->
<!--                    <button @click="submitOrder">去结算</button>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--</template>-->
<!--<script>-->
<!--export default {-->
<!--    data () {-->
<!--        return {-->
<!--            selectedAddr: null,-->
<!--            totalPrice: '',-->
<!--            addrList: [-->
<!--                { id: 0, addr: '四川成都市成华区保和街道' },-->
<!--            ],-->
<!--            selectAllProduct: false,-->
<!--            productList: [-->
<!--                {-->
<!--                    id: 456,-->
<!--                    checked: false,-->
<!--                    shopName: '新疆安博致鑫贸易旗舰店',-->
<!--                    icon: require('../../../../assets/images/userCenter/客服.png'),-->
<!--                    img: '',-->
<!--                    productName: '安徽省蚌埠市出租SY135履带式挖掘机',-->
<!--                    price: '399.00',-->
<!--                    quantity: 1,-->
<!--                    total: '399.00',-->
<!--                    specs: '操作重量13吨 铲斗容量0.6立方米'-->

<!--                },-->
<!--                {-->
<!--                    id: 45,-->
<!--                    checked: false,-->
<!--                    shopName: '成都安博致鑫贸易旗舰店',-->
<!--                    icon: require('../../../../assets/images/userCenter/客服.png'),-->
<!--                    img: '',-->
<!--                    productName: '安徽省蚌埠市出租SY135履带式挖掘机',-->
<!--                    price: '399.00',-->
<!--                    quantity: 1,-->
<!--                    total: '399.00',-->
<!--                    specs: ''-->
<!--                },-->
<!--                {-->
<!--                    id: 56,-->
<!--                    checked: false,-->
<!--                    shopName: '新疆安博致鑫贸易旗舰店',-->
<!--                    icon: require('../../../../assets/images/userCenter/客服.png'),-->
<!--                    img: '',-->
<!--                    productName: '安徽省蚌埠市出租SY135履带式挖掘机',-->
<!--                    price: '399.00',-->
<!--                    quantity: 1,-->
<!--                    total: '399.00',-->
<!--                    specs: '操作重量13吨 铲斗容量0.6立方米'-->
<!--                },-->
<!--            ],-->
<!--        }-->
<!--    },-->
<!--    watch: {-->
<!--        rearrangedList: {-->
<!--            handler (newVal) {-->
<!--                let checkAll = true-->
<!--                newVal.forEach(newItem => {-->
<!--                    newItem.checked = newItem.list.every(subItem => subItem.checked)-->
<!--                    if(!newItem.checked) checkAll = false-->
<!--                    newItem.list.forEach(subItem => {-->
<!--                        if(isNaN(Number(subItem.quantity)) || subItem.quantity < 1) subItem.quantity = 1-->
<!--                        subItem.total = (Number(subItem.price) * subItem.quantity).toFixed(2)-->
<!--                    })-->
<!--                })-->
<!--                this.selectAllProduct = checkAll-->
<!--                this.calcTotalPrice()-->
<!--            },-->
<!--            deep: true-->
<!--        }-->
<!--    },-->
<!--    computed: {-->
<!--        // 商品总数量-->
<!--        totalProductCount () {-->
<!--            return this.productList.length-->
<!--        },-->
<!--        // 全选的商品数量-->
<!--        totalSelected () {-->
<!--            let count = 0-->
<!--            this.rearrangedList.forEach(item => {-->
<!--                if(item.checked) { // 店铺是否全选-->
<!--                    return count += item.list.length-->
<!--                }-->
<!--                item.list.forEach(subItem => {-->
<!--                    subItem.checked ? count += 1 : null-->
<!--                })-->
<!--            })-->
<!--            return count-->
<!--        },-->
<!--        // 按照店铺分组后的商品列表-->
<!--        rearrangedList () {-->
<!--            const list = []-->
<!--            this.productList.forEach((item, i) => {-->
<!--                if(i === 0) {-->
<!--                    return list.push({ checked: false, shopName: item.shopName, list: [item] })-->
<!--                }-->
<!--                let flag = false-->
<!--                list.forEach(newItem => {-->
<!--                    if(item.shopName === newItem.shopName) {-->
<!--                        flag = true-->
<!--                        return newItem.list.push(item)-->
<!--                    }-->
<!--                })-->
<!--                !flag ? list.push({ checked: false, shopName: item.shopName, list: [item] }) : null-->
<!--            })-->
<!--            return list-->
<!--        }-->
<!--    },-->
<!--    methods: {-->
<!--        // 全选所有商品-->
<!--        toggleSelectAll () {-->
<!--            this.productList.forEach(item => {-->
<!--                item.checked = this.selectAllProduct-->
<!--            })-->
<!--            // console.log(this.selectAllProduct)-->
<!--        },-->
<!--        // 全选店铺下的商品-->
<!--        selectAllInShop (i) {-->
<!--            this.rearrangedList[i].list.forEach(item => {-->
<!--                item.checked = this.rearrangedList[i].checked-->
<!--            })-->
<!--        },-->
<!--        // 获取选中的所有商品-->
<!--        getSelectedProduct () {-->
<!--            let arr = []-->
<!--            this.rearrangedList.forEach(item => {-->
<!--                if(item.checked) { // 店铺是否全选-->
<!--                    return arr = arr.concat(item.list)-->
<!--                }-->
<!--                item.list.forEach(subItem => {-->
<!--                    subItem.checked ? arr.push(subItem) : null-->
<!--                })-->
<!--            })-->
<!--            return arr-->
<!--        },-->
<!--        changeNum (action, item) {-->
<!--            if(action === 'minus') {-->
<!--                if(item.quantity === 1) return-->
<!--                item.quantity&#45;&#45;-->
<!--            } else {-->
<!--                item.quantity++-->
<!--            }-->
<!--        },-->
<!--        // 删除商品-->
<!--        deleteProduct (id) {-->
<!--            this.clientPop('warn', '您确定删除此商品吗？', () => {-->
<!--                this.productList.forEach((item, i, arr) => {-->
<!--                    if(item.id === id) {-->
<!--                        arr.splice(i, 1)-->
<!--                    }-->
<!--                })-->
<!--                console.log(this.rearrangedList)-->
<!--            })-->
<!--        },-->
<!--        deleteSelected () {-->
<!--            this.getSelectedProduct()-->
<!--        },-->
<!--        // 添加到收藏-->
<!--        addToCollection () {},-->
<!--        // 计算订单总价-->
<!--        calcTotalPrice () {-->
<!--            let total = 0-->
<!--            this.productList.forEach(item => {-->
<!--                total += parseFloat(item.total)-->
<!--            })-->
<!--            this.totalPrice = total.toFixed(2)-->
<!--        },-->
<!--        submitOrder () {-->
<!--            let list = this.getSelectedProduct()-->
<!--            if(list.length === 0) return-->
<!--            this.$router.push('/mFront/submitOrder')-->
<!--        },-->
<!--    },-->
<!--    created () {-->
<!--        this.calcTotalPrice()-->
<!--    },-->
<!--    mounted () {-->
<!--        console.log(this.rearrangedList)-->
<!--    }-->
<!--}-->
<!--</script>-->
<!--<style scoped lang="scss">-->
<!--.root {-->
<!--    height: 100%;-->
<!--    padding: 20px 0 104px;-->
<!--    background-color: #f5f5f5;-->

<!--    .box {-->
<!--        width: 1326px;-->
<!--        background-color: #fff;-->
<!--    }-->
<!--}-->
<!--/deep/ .el-checkbox {-->
<!--    color: rgba(51, 51, 51, 1);-->
<!--    .el-checkbox__inner {-->
<!--        border: 1px solid rgba(204, 204, 204, 1);-->
<!--    }-->
<!--}-->
<!--.addr-bar {-->
<!--    height: 30px;-->
<!--    &>div:first-child {-->
<!--        font-size: 20px;-->
<!--        font-weight: 500;-->
<!--        color: rgba(212, 48, 48, 1);-->
<!--    }-->
<!--    &>div:last-child {-->
<!--        color: rgba(51, 51, 51, 1);-->
<!--    }-->
<!--    /deep/ .el-input__inner {-->
<!--        width: 216px;-->
<!--        height: 26px;-->
<!--    }-->
<!--}-->
<!--.title {-->
<!--    height: 52px;-->
<!--    margin-bottom: 12px;-->
<!--    padding-left: 20px;-->
<!--    color: rgba(51, 51, 51, 1);-->
<!--    background-color: rgba(250, 250, 250, 1);-->
<!--    /deep/ .el-checkbox {width: 158px;}-->
<!--    &>div:nth-child(2) {width: 660px;}-->
<!--    &>div:nth-child(3) {width: 129px;}-->
<!--    &>div:nth-child(4) {width: 109px;}-->
<!--    &>div:nth-child(5) {width: 108px;}-->
<!--}-->
<!--.product {-->
<!--    margin-bottom: 30px;-->
<!--    .shop-name {-->
<!--        padding: 22px 0 14px 20px;-->
<!--        img {-->
<!--            width: 22px;-->
<!--            height: 22px;-->
<!--            margin-left: 3px;-->
<!--        }-->
<!--    }-->
<!--    & .product-item:not(:last-of-type) {margin-bottom: 10px;}-->
<!--    .product-item {-->
<!--        height: 144px;-->
<!--        border: 1px solid rgba(230, 230, 230, 1);-->
<!--        img {-->
<!--            width: 100px;-->
<!--            height: 100px;-->
<!--        }-->
<!--        &>div {height: 100px;}-->
<!--        .checkbox {-->
<!--            padding: 0 30px 0 20px;-->
<!--        }-->
<!--        .title-box {-->
<!--            width: 609px;-->
<!--            padding-left: 12px;-->
<!--            .tag {-->
<!--                width: 60px;-->
<!--                height: 20px;-->
<!--                margin: 0 10px 7px 0;-->
<!--                font-size: 12px;-->
<!--                line-height: 20px;-->
<!--                text-align: center;-->
<!--                color: #fff;-->
<!--                background-color: rgba(255, 195, 0, 1);-->
<!--            }-->
<!--        }-->
<!--        .price {-->
<!--            width: 157px;-->
<!--            text-align: center;-->
<!--        }-->
<!--        .num-box {-->
<!--            width: 101px;-->
<!--            .numCalc {-->
<!--                width: 100%;-->
<!--                border: 1px solid rgba(204, 204, 204, 1);-->
<!--                div, input {-->
<!--                    height: 26px;-->
<!--                    text-align: center;-->
<!--                }-->
<!--                div {-->
<!--                    width: 26px;-->
<!--                    line-height: 26px;-->
<!--                    background-color: rgba(230, 230, 230, 1);-->
<!--                    cursor: pointer;-->
<!--                    &:first-child {border-right: 1px solid rgba(204, 204, 204, 1);}-->
<!--                    &:last-child {border-left: 1px solid rgba(204, 204, 204, 1);}-->
<!--                }-->
<!--                input {-->
<!--                    width: 49px;-->
<!--                }-->
<!--            }-->
<!--        }-->
<!--        .total {-->
<!--            width: 134px;-->
<!--            text-align: center;-->
<!--        }-->
<!--        .operate {-->
<!--            font-size: 10px;-->
<!--            color: rgba(102, 102, 102, 1);-->
<!--            div {margin-bottom: 10px;cursor: pointer;}-->
<!--        }-->
<!--    }-->
<!--}-->
<!--.bottom-bar {-->
<!--    height: 60px;-->
<!--    padding-left: 20px;-->
<!--    border: 1px solid rgba(230, 230, 230, 1);-->
<!--    &>div{height: 100%;}-->
<!--    /deep/ .el-checkbox {-->
<!--        margin-right: 30px;-->
<!--    }-->
<!--    .bar-left {-->
<!--        color: rgba(102, 102, 102, 1);-->
<!--        span {margin-right: 20px;cursor: pointer;}-->
<!--    }-->
<!--    .bar-right {-->
<!--        &>div:first-child {-->
<!--            margin: 7px 9px 0 0;-->
<!--            color: rgba(153, 153, 153, 1);-->
<!--            cursor: pointer;-->
<!--            span {margin-right: 9px;}-->
<!--        }-->
<!--        .bar-right-price {-->
<!--            height: 27px;-->
<!--            margin: 4px 22px 0 0;-->
<!--            & span:first-child {color: rgba(153, 153, 153, 1);}-->
<!--            & span:nth-child(2) {-->
<!--                margin-right: 12px;-->
<!--                font-size: 18px;-->
<!--                font-weight: 700;-->
<!--                color: rgba(212, 48, 48, 1);-->
<!--            }-->
<!--            img {-->
<!--                width: 16px;-->
<!--                height: 16px;-->
<!--            }-->
<!--        }-->
<!--        button {-->
<!--            width: 128px;-->
<!--            height: 60px;-->
<!--            font-size: 24px;-->
<!--            font-weight: 400;-->
<!--            text-align: center;-->
<!--            line-height: auto;-->
<!--            color: rgba(255, 255, 255, 1);-->
<!--            background-color: rgba(212, 48, 48, 1);-->
<!--        }-->
<!--    }-->
<!--}-->
<!--</style>-->