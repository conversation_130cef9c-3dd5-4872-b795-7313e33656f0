<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" v-show="viewList === true">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <!-- 新增按钮 -->
          <div class="left">
            <div class="left-btn">
              <el-button style="padding: 0 16px;" type="primary" size="mini" @click="handleNew" class="btn-greenYellow">
                新增
              </el-button>
              <el-button style="padding: 0 16px;" type="primary" size="mini" :disabled="selectedRows.length == 0"
                         @click="handleDelete" class="btn-delete">
                批量删除
              </el-button>
              <el-button style="padding: 0 16px;" type="primary" size="mini" :disabled="selectedRows.length != 1"
                         @click="handleSupplierType">
                设置供应商类型
              </el-button>
              <el-button style="padding: 0 16px;" type="primary" size="mini" :disabled="selectedRows.length == 0"
                         @click="handleSupplierGroup"
                         class="btn-blue">设置群组
              </el-button>
              <!--                            <el-button type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改</el-button>-->
            </div>
          </div>
          <div class="search_box">
            <el-select v-model="searchForm.supplierType"
                       clearable filterable
                       placeholder="供应商类型" style="width: 70%">
              <el-option v-for="item in supplierTypeSearchList" :key="item.value" :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
            <el-select v-model="searchForm.templateWarehousedIs"
                       clearable filterable
                       placeholder="定制化模式" style="width: 40%">
              <el-option v-for="item in templateWarehousedList" :key="item.value" :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
            <el-cascader :options="supplierGroupList"
                         style="width: 70%"
                         collapse-tags
                         collapse-tags-tooltip
                         placeholder="群组"
                         v-model="searchForm.supplierGroup"
                         :props="groupSearchProps"
                         clearable filterable
                         :show-all-levels="false"></el-cascader>
            <el-option v-for="item in supplierGroupList" :key="item.value" :label="item.label"
                       :value="item.value">
            </el-option>
            <el-input clearable
                type="text" @keyup.enter.native="onSearch" style="width: 50%;" placeholder="输入搜索关键字"
                v-model="keywords"
            ><img src="@/assets/search.png" slot="suffix" @click="onSearch"/>
            </el-input>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table">
        <el-table
            @row-click="handleCurrentInventoryClick" ref="tableRef" class="table"
            :height="rightTableHeight" :data="tableData" border highlight-current-row
            @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="80">
            <template v-slot="scope">
                            <span class="action" @click="onDel(scope)"><img
                                src="../../../../assets/btn/delete.png"
                                alt=""
                            ></span>
            </template>
          </el-table-column>
          <!-- 名称 -->
          <el-table-column label="供应商企业名称" width="">
            <template v-slot="scope">
              <span class="action" @click="handleView(scope)">{{ scope.row.supplierName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="供应商类型" width="">
            <template v-slot="scope">
              <el-text v-if="scope.row.supplierType == 1">零星自营店供应商库</el-text>
              <el-text v-if="scope.row.supplierType == 2">大宗临购专区供应商库</el-text>
              <el-text v-if="scope.row.supplierType == 3">周转材料供应商库</el-text>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="gmtCreate">
          </el-table-column>
          <el-table-column label="是否拥有上架商品权限" width="160">
            <template v-slot="scope">
              <el-tag v-if="scope.row.listPermissions == 1" type="success">是</el-tag>
              <el-tag v-if="scope.row.listPermissions == 0" type="danger">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审核状态" width="180">
            <template v-slot="scope">
              <el-tag v-if="scope.row.auditStatus == 0">无</el-tag>
              <el-tag v-if="scope.row.auditStatus == 1" type="info">待审核</el-tag>
              <el-tag v-if="scope.row.auditStatus == 2" type="success">审核通过</el-tag>
              <el-tag v-if="scope.row.auditStatus == 3" type="danger">驳回</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="商品权限" width="120" type="">
            <template v-slot="scope">
              <span v-if="scope.row.listPermissions == 0">无商品权限</span>
              <span v-if="scope.row.listPermissions == 1">
                              {{ getProductPermission(scope) }}
                            </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <Pagination
          :total="pages.totalCount" :pageSize.sync="pages.pageSize"
          :currentPage.sync="pages.currPage"
          @currentChange="currentChange" @sizeChange="sizeChange"
      />
    </div>
    <div class="right" v-show="viewList !== true">
      <!-- ---------------------新增编辑窗口--------------------- -->
      <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
        <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="12">
              <el-form-item width="150px" label="供方企业名称：" prop="supplierId">
                <el-select
                    v-if="action === '新增'"
                    ref="selectLabel1"
                    v-model="formData.supplierId"
                    filterable
                    remote
                    reserve-keyword
                    @change="getTaxRate"
                    :remote-method="remoteMethod"
                    :loading="loading"
                    placeholder="请输入供应商名称"
                >
                  <el-option
                      v-for="item in supplierList"
                      :key="item.enterpriseId"
                      :label="item.enterpriseName"
                      :value="item.enterpriseId"
                  >
                  </el-option>
                </el-select>
                <el-input v-else v-model="formData.supplierName" placeholder="" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序值：" prop="sort">
                <el-input v-model="formData.sort" :disabled="isView" type="number" :min="0" placeholder="填写排序值">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item width="150px" label="说明：" prop="remarks">
                <el-input v-model="formData.remarks" :disabled="isView" placeholder="" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item width="150px" label="是否拥有上架权限：" prop="remarks">
                <el-radio :disabled="isView" v-model="formData.listPermissions" label="1" @click="changeListPermissions">是
                </el-radio>
                <el-radio :disabled="isView" v-model="formData.listPermissions" label="0" @click="changeListPermissions">否
                </el-radio>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="formData.listPermissions == 1">
            <el-form-item label="商品权限：">
              <el-checkbox-group :disabled="isView" v-model="permissionList">
                <el-checkbox label="低值易耗">低值易耗</el-checkbox>
                <el-checkbox label="大宗商品">大宗商品</el-checkbox>
                <el-checkbox label="周转材料">周转材料</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-row>
          <el-row v-if="action != '新增' && formData.supplierType">
            <el-form-item label="供应商类型：">
              <el-radio-group v-model="formData.supplierType" style="width: 50%" :disabled="isView">
                <el-col :span="8" v-for="item in supplierTypeSearchList" :key="item.value">
                  <el-radio :label="item.value" :value="item.value" :key="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row v-if="action != '新增' && formData.templateWarehousedIs">
            <el-form-item label="是否采用定制化模式入库：">
              <el-radio-group v-model="formData.templateWarehousedIs"
                              :disabled="isView"
                              @change="isTemplateChange">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row v-if="action != '新增' && formData.templateWarehousedIs && formData.warehousedType">
            <el-col :span="24">
            <el-form-item label="入库类型：">
              <el-checkbox-group v-model="formData.warehousedType" style="width: 100%" :disabled="isView">
                <el-col :span="8" v-for="city in warehousedTypeList" :key="city">
                  <el-checkbox :label="city">{{ city }}</el-checkbox>
                </el-col>
                <el-checkbox :label="'其他'">{{ '其他' }}</el-checkbox>
                <el-input :disabled="isView" v-if="formData.warehousedType.includes('其他')"
                          v-model="formData.warehousedTypeStr"
                          @input="inputChange($event)"
                          style="margin-left: 10px;width: 30%"></el-input>
              </el-checkbox-group>
            </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="action != '新增' && formData.warehousedDesc">
            <el-form-item label="入库审批内容简述：">
              <el-input :disabled="isView"
                  type="textarea"
                  :rows="3"
                  style="width: 100%"
                  v-model="formData.warehousedDesc">
              </el-input>
            </el-form-item>
          </el-row>
          <el-row v-if="action != '新增' && formData.files && formData.files.length > 0">
            <el-form-item label="证明材料：">
              <el-upload
                  :disabled="isView"
                  v-model="formData.warehousedFile"
                  action="fakeaction"
                  v-loading="addLoading"
                  list-type="picture-card"
                  :file-list="formData.files"
                  :on-exceed="handleExceed"
                  :on-preview="handlePictureCardPreview"
                  :on-change="handleUploadChange"
                  :http-request="uploadWarehousedFile"
                  :on-remove="handleRemove">
                <i class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
              </el-dialog>
            </el-form-item>
          </el-row>
          <el-row v-if="action != '新增' && formData.supplierGroup">
            <el-form-item label="群组：">
              <el-cascader :options="supplierGroupList"
                           style="width: 50%"
                           :disabled="isView"
                           v-model="formData.supplierGroup"
                           :props="groupProps"
                           collapse-tags
                           collapse-tags-tooltip
                           :show-all-levels="false">
              </el-cascader>
            </el-form-item>
          </el-row>
        </el-form>
        <div
            class="tabs-title"
            v-if="formData.listPermissions == 1&&permissionList.includes('低值易耗')"
        >零星商品
        </div>
        <div
            class="dfb timePicker"
            v-if="formData.listPermissions == 1&&permissionList.includes('低值易耗')"
        >
          <el-input
              @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="lxKeyword" style="width: 240px"
          >
            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch"/>
          </el-input>
          <i class="el-icon el-icon-refresh pointer" @click="getPlatformOrdersCountM"></i>
        </div>
        <el-collapse v-model="activeName">
          <el-collapse-item title="展开" name="1"
                            v-if="formData.listPermissions == 1&&permissionList.includes('低值易耗')">
            <div class="e-table"
                 v-if="formData.listPermissions == 1&&permissionList.includes('低值易耗')">
              <el-table
                  class="table"
                  ref="tableRef"
                  style="width: 100%"
                  :max-height="$store.state.tableHeight"
                  v-loading="showLxLoading" :data="lxtableData" border
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column label="名称" width="200" prop="productName">
                  <template v-slot="scope">
                    <span class="action" @click="handleViewProduct(scope.row)">{{ scope.row.productName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品编码" width="240" prop="serialNum">
                </el-table-column>
                <el-table-column label="图片" width="120" type="index">
                  <template v-slot="scope">
                    <el-image
                        style="width: 90px; height: 60px"
                        :src="imgUrlPrefixAdd + scope.row.productMinImg"
                        fit="cover"
                    ></el-image>
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="state">
                  <template v-slot="scope">
                    <el-tag v-if="scope.row.state==0">待上架</el-tag>
                    <el-tag v-if="scope.row.state==1" type="success">已上架</el-tag>
                    <el-tag v-if="scope.row.state==2" type="danger">已下架</el-tag>
                    <el-tag v-if="scope.row.state==3">审核中</el-tag>
                    <el-tag v-if="scope.row.state==4" type="danger">审核失败</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="规格" width="240" prop="skuName">
                </el-table-column>
                <el-table-column label="销售价格" prop="sellPrice"></el-table-column>
                <el-table-column label="原价" prop="originalPrice"/>
                <el-table-column label="成本价" prop="costPrice"/>
                <el-table-column label="差价" prop="profitPrice"/>
                <el-table-column label="库存" prop="stock"/>
              </el-table>
            </div>
            <Pagination
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getPlatformOrdersCountM"
                @sizeChange="getPlatformOrdersCountM"
            />
          </el-collapse-item>
        </el-collapse>
        <div
            class="tabs-title"
            v-if="formData.listPermissions == 1&&permissionList.includes('大宗商品')"
        >大宗商品
        </div>
        <div
            class="dfb timePicker"
            v-if="formData.listPermissions == 1&&permissionList.includes('大宗商品')"
        >
          <el-input
              @blur="handleInputdzSearch" placeholder="输入搜索关键字" v-model="dzKeyword"
              style="width: 240px"
          >
            <img src="@/assets/search.png" slot="suffix" @click="getPlatformOrdersdzCountM"/>
          </el-input>
        </div>
        <el-collapse
            v-model="activeName1"
            v-if="formData.listPermissions == 1&&permissionList.includes('大宗商品')"
        >
          <el-collapse-item
              title="展开" name="1"
              v-if="formData.listPermissions == 1&&permissionList.includes('大宗商品')"
          >
            <div class="e-table">
              <el-table
                  class="table"
                  ref="tableRef"
                  style="width: 100%"
                  :max-height="$store.state.tableHeight"
                  v-loading="dzshowLoading" :data="dztableData" border
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column label="名称" width="200" prop="productName">
                  <template v-slot="scope">
                    <span class="action" @click="handleViewProduct(scope.row)">{{ scope.row.productName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品编码" width="240" prop="serialNum">
                </el-table-column>
                <el-table-column label="图片" width="120" type="index">
                  <template v-slot="scope">
                    <el-image
                        style="width: 90px; height: 60px"
                        :src="imgUrlPrefixAdd + scope.row.productMinImg"
                        fit="cover"
                    ></el-image>
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="state">
                  <template v-slot="scope">
                    <el-tag v-if="scope.row.state==0">待上架</el-tag>
                    <el-tag v-if="scope.row.state==1" type="success">已上架</el-tag>
                    <el-tag v-if="scope.row.state==2" type="danger">已下架</el-tag>
                    <el-tag v-if="scope.row.state==3">审核中</el-tag>
                    <el-tag v-if="scope.row.state==4" type="danger">审核失败</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="规格" width="240" prop="skuName">
                </el-table-column>
                <el-table-column label="销售价格" prop="sellPrice"></el-table-column>
                <el-table-column label="原价" prop="originalPrice"/>
                <el-table-column label="成本价" prop="costPrice"/>
                <el-table-column label="差价" prop="profitPrice"/>
                <el-table-column label="库存" prop="stock"/>
              </el-table>
            </div>
            <Pagination
                v-show="dztableData != null "
                :total="paginationInfo2.total"
                :pageSize.sync="paginationInfo2.pageSize"
                :currentPage.sync="paginationInfo2.currentPage"
                @currentChange="getPlatformOrdersdzCountM"
                @sizeChange="getPlatformOrdersdzCountM"
            />
          </el-collapse-item>
        </el-collapse>
        <div
            class="tabs-title"
            v-if="formData.listPermissions == 1&&permissionList.includes('周转材料')"
        >周转材料
        </div>
        <div
            class="dfb timePicker"
            v-if="formData.listPermissions == 1&&permissionList.includes('周转材料')"
        >
          <el-input
              @blur="handleInputdzSearch" placeholder="输入搜索关键字" v-model="zzKeyword"
              style="width: 240px"
          >
            <img src="@/assets/search.png" slot="suffix" @click="getPlatformOrderszzCountM"/>
          </el-input>
        </div>
        <el-collapse
            v-model="activeName2"
            v-if="formData.listPermissions == 1&&permissionList.includes('周转材料')"
        >
          <el-collapse-item
              title="展开" name="1"
              v-if="formData.listPermissions == 1&&permissionList.includes('周转材料')"
          >
            <div class="e-table">
              <el-table
                  class="table"
                  ref="tableRef"
                  style="width: 100%"
                  :max-height="$store.state.tableHeight"
                  v-loading="zzshowLoading" :data="zztableData" border
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column label="名称" width="200" prop="productName">
                  <template v-slot="scope">
                    <span class="action" @click="handleViewProduct(scope.row)">{{ scope.row.productName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品编码" width="240" prop="serialNum">
                </el-table-column>
                <el-table-column label="图片" width="120" type="index">
                  <template v-slot="scope">
                    <el-image
                        style="width: 90px; height: 60px"
                        :src="imgUrlPrefixAdd + scope.row.productMinImg"
                        fit="cover"
                    ></el-image>
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="state">
                  <template v-slot="scope">
                    <el-tag v-if="scope.row.state==0">待上架</el-tag>
                    <el-tag v-if="scope.row.state==1" type="success">已上架</el-tag>
                    <el-tag v-if="scope.row.state==2" type="danger">已下架</el-tag>
                    <el-tag v-if="scope.row.state==3">审核中</el-tag>
                    <el-tag v-if="scope.row.state==4" type="danger">审核失败</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="规格" width="240" prop="skuName">
                </el-table-column>
                <el-table-column label="销售价格" prop="sellPrice"></el-table-column>
                <el-table-column label="原价" prop="originalPrice"/>
                <el-table-column label="成本价" prop="costPrice"/>
                <el-table-column label="差价" prop="profitPrice"/>
                <el-table-column label="库存" prop="stock"/>
              </el-table>
            </div>
            <Pagination
                v-show="zztableData != null "
                :total="paginationInfo3.total"
                :pageSize.sync="paginationInfo3.pageSize"
                :currentPage.sync="paginationInfo3.currentPage"
                @currentChange="getPlatformOrdersdzCountM"
                @sizeChange="getPlatformOrdersdzCountM"
            />
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="footer">
        <div class="right-btn">
          <el-button v-if="this.action == '新增' || isEdit"  native-type="button" type="primary" @click="onSave()">保存</el-button>
          <el-button v-if="this.action == '新增'" @click="onCancel">取消</el-button>
          <el-button v-if="this.action == '编辑' && formData.auditStatus != 1 && isView "
                     native-type="button" type="primary" @click="()=>{isEdit = true;isView = false}">编辑</el-button>
<!--          <el-button v-if="formData.auditStatus == 1 " native-type="button" type="primary" @click="onRevocation()">撤回</el-button>-->
          <el-button v-if="formData.auditStatus == 1 " native-type="button" type="primary" @click="onPass()">通过</el-button>
          <el-button v-if="formData.auditStatus == 1 " native-type="button" type="primary" @click="onReject()">驳回</el-button>
          <el-button v-if="this.action == '编辑'" @click="onCancel">返回</el-button>
        </div>
      </div>
    </div>
    <!--  设置供应商类型弹窗    -->
    <el-dialog :visible.sync="handleSupplierVisible" title="供应商类型设置" width="60%">
      <el-form ref="supplierTypeForm" :inline="false" :model="supplierData" label-width="180px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="供应商类型：">
              <el-radio-group v-model="supplierData.supplierType" @change="supplierTypeChange" style="width: 100%">
                <el-col :span="8" v-for="item in supplierTypeList" :key="item.value">
                  <el-radio :label="item.value" :value="item.value" :key="item.value">{{ item.label }}</el-radio>
                </el-col>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="是否采用定制化模式入库：">
              <el-radio-group v-model="supplierData.templateWarehousedIs" @change="isTemplateChange">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="supplierData.templateWarehousedIs == 1">
          <el-col :span="24">
            <el-form-item label="入库类型：">
              <el-checkbox-group v-model="supplierData.warehousedType" style="width: 100%">
                <el-col :span="8" v-for="city in warehousedTypeList" :key="city">
                  <el-checkbox :label="city">{{ city }}</el-checkbox>
                </el-col>
                <el-checkbox :label="'其他'">{{ '其他' }}</el-checkbox>
                <el-input v-if="supplierData.warehousedType && supplierData.warehousedType.includes('其他')"
                          v-model="supplierData.warehousedTypeStr"
                          @input="inputChange($event)"
                          style="margin-left: 10px;width: 50%"></el-input>
              </el-checkbox-group>

            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="supplierData.templateWarehousedIs == 1">
          <el-col :span="24">
            <el-form-item label="入库审批内容简述：">
              <el-input
                  type="textarea"
                  :rows="3"
                  style="width: 100%"
                  v-model="supplierData.warehousedDesc">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="supplierData.templateWarehousedIs == 1">
          <el-col :span="24">
            <el-form-item label="证明材料：">
              <el-upload
                  v-model="supplierData.warehousedFile"
                  class="avatar-uploader"
                  action="fakeaction"
                  v-loading="addLoading"
                  accept=".pdf, .doc, .docx"
                  :limit="3"
                  :file-list="fileList"
                  :on-exceed="handleExceed"
                  :auto-upload="true"
                  :before-upload="handleBeforeUpload"
                  :on-remove="handleRemove"
                  :on-change="handleUploadChange"
                  :http-request="uploadWarehousedFile">
                <el-button size="small" type="primary">上传附件</el-button>
                <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx图片文件</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button @click="supplierTypeSave">保存</el-button>
                <el-button @click="handleSupplierVisible = false">取消</el-button>
            </span>
    </el-dialog>
    <el-dialog :visible.sync="supplierGroupVisible" title="群组设置" width="50%">
      <el-form ref="groupForm" :inline="false" :model="supplierGroupData" label-width="180px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="群组：">
              <div style="display:flex">
                <el-cascader :options="supplierGroupList"
                             style="width: 100%"
                             v-model="supplierGroupData.supplierGroup"
                             collapse-tags
                             collapse-tags-tooltip
                             :props="groupProps"
                             :show-all-levels="false"></el-cascader>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button @click="supplierGroupSave">保存</el-button>
            </span>
    </el-dialog>
  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import {
    create,
    del,
    getList,
    selectListByEnterPriseName,
    update,
    batchDelete,
    updateByBatch,
    setSupplierGroup,
    setSupplierType,
    auditSupplierType,
    getSupplierFileList
} from '@/api/shopManage/shop/shopSupplierRele'
import { treeByName } from '@/api/platform/product/productCategory'
import { debounce, hideLoading, showLoading, } from '@/utils/common'
import { mapActions, mapState } from 'vuex'
import { getMaterialPageList } from '@/api/shopManage/product/materialManage'
import $ from 'jquery'
import { uploadFile } from '@/api/platform/common/file'
import { createWFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'

export default {
    components: {
        Pagination,
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            loading: false,
            queryVisible: false,
            action: '编辑',
            isEdit: false,
            isView: false,
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            searchForm: {
                supplierType: null,
                templateWarehousedIs: null,
                supplierGroup: null
            },
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            tableData: [],
            permissionList: [],
            scope: '',
            formRules: {
                supplierName: [{ required: true, message: '供方企业名称', trigger: 'blur' }],
                supplierId: [{ required: true, message: '供方企业名称', trigger: 'blur' }],
                taxRate: { required: true, validator: this.validateTaxRate, trigger: 'blur' }

            },
            tableLoading: false,
            dzshowLoading: false,
            zzshowLoading: false,
            a: '1',
            lxtableData: [],
            dztableData: [],
            zztableData: [],
            paginationInfo: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            dzKeyword: null,
            zzKeyword: null,
            showLxLoading: false,
            showDzLoading: false,
            showZzLoading: false,
            paginationInfo2: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo3: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            activeName1: 0,
            activeName: 0,
            activeName2: 0,
            lxKeyword: null,
            formData: {
                supplierId: '',
                supplierName: '',
                remarks: '',
                sort: '',
                listPermissions: '',
                supplierType: '',
                warehousedType: '',
                templateWarehousedIs: '',
                warehousedDesc: '',
                supplierGroup: '',
                permissionsLowValue: '',
                permissionsCommodities: '',
                permissionsTurnover: '',
            },
            dialogImageUrl: '',
            dialogVisible: false,
            supplierList: [],
            supplierId: '',
            initSysParamFilter: [],
            mapObj: null,
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            handleSupplierVisible: false,
            supplierGroupVisible: false,
            addLoading: false,
            supplierData: {
                ids: [],
                supplierType: null,
                supplierGroup: null,
                templateWarehousedIs: null,
                warehousedType: [],
                warehousedTypeStr: '',
                warehousedDesc: '',
                files: []

            },
            supplierGroupData: {
                ids: [],
                supplierGroup: null
            },
            warehousedTypeList: ['战略合作类型', '既有合作类型',
                '内部客户推荐类型', '产业协同类型', '竞争入围类型', '市场调查类型'],
            supplierTypeList: [
                {
                    label: '零星自营店供应商库',
                    value: 1,
                },
                {
                    label: '大宗临购专区供应商库',
                    value: 2
                },
                {
                    label: '周转材料供应商库',
                    value: 3
                }
            ],
            supplierTypeSearchList: [
                {
                    label: '零星自营店供应商库',
                    value: '1',
                },
                {
                    label: '大宗临购专区供应商库',
                    value: '2'
                },
                {
                    label: '周转材料供应商库',
                    value: '3'
                }
            ],
            templateWarehousedList: [
                {
                    label: '是',
                    value: 1,
                },
                {
                    label: '否',
                    value: 2
                },
            ],
            supplierGroupList: [],
            groupProps: {
                multiple: true,
                expandTrigger: 'hover',
                children: 'children',
                label: 'className',
                value: 'classId',
            },
            groupSearchProps: {
                expandTrigger: 'hover',
                children: 'children',
                label: 'className',
                value: 'classId',
            },
            fileList: [],
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },

    created () {
        if (this.$route.query.scope != null) {
            this.handleView(this.$route.query.scope)
        } else {
            this.getGroupList()
            // this.getTableData()

        }
    },
    watch: {
        viewList (newVal) {
            if (newVal) {
                this.tableData.forEach(item => {
                    if (item.shopId === this.formData.shopId) item.listPermissionsTypeList = this.permissionList
                })
            }
        }
    },
    methods: {
    // 根据选择的供方ID获取其税率
        getTaxRate (val) {
            this.supplierList.forEach(item => {
                if (item.enterpriseId === val) {
                    this.formData.taxRate = item.taxRate ? item.taxRate : 0
                }
            })
        },
        // eslint-disable-next-line
    validateTaxRate(rule, value, callback) {
            if (isNaN(Number(value))) {
                this.formData.taxRate = ''
                return callback(new Error('请输入正确的税率'))
            }
            if (!value) return callback(new Error('请输入税率'))
            let lessThan = parseInt(value) < 0
            let biggerThan = parseInt(value) > 100
            if (lessThan || biggerThan) {
                lessThan ? this.formData.taxRate = 0 : ''
                biggerThan ? this.formData.taxRate = 100 : ''
                return callback(new Error('超出限制'))
            }
            let longFloatStr = typeof value === 'string' && value.includes('.') && value.split('.')[1].length > 2
            if (longFloatStr) this.formData.taxRate = parseFloat(parseFloat(value).toFixed(2))
            callback()
        },
        getProductPermission ({ row }) {
            let { permissionsCommodities, permissionsLowValue, permissionsTurnover } = row
            let p = []
            if (permissionsCommodities == 1) p.push('大宗商品')
            if (permissionsLowValue == 1) p.push('低值易耗')
            if (permissionsTurnover == 1) p.push('周转材料')
            return p.join('，')
        },
        changeListPermissions (value) {
            if (value === '1') {
                this.listPermissions = true
            }
            if (value === '0') {
                this.listPermissions = false
            }
        },
        remoteMethod (value) {
            if (value === '' || value == null) return
            selectListByEnterPriseName({ enterpriseName: value }).then(res => this.supplierList = res)
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },

        handleInputSearch () {
            this.getPlatformOrdersCountM()
        },
        handleViewProduct (row) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/product/materialWarehouseDisDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'materialWarehouseDisDetail',
                params: {
                    row: row,
                    scope: this.scope
                }
            })
        },
        refreshLxData () {
            this.getStaticsData()
            $('.el-icon-refresh').animate({ deg: '+=180', }, {
                duration: 500,
                step: function (now) {
                    $(this).css({ transform: 'rotate(' + now + 'deg)' })
                },
                complete: function () {
                    $(this).css({ // 重置元素的旋转角度和动画时间
                        transform: 'rotate(0deg)',
                        transitionDuration: '0s'
                    })
                }
            })
        },
        handleInputdzSearch () {
            this.getPlatformOrdersdzCountM()
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        getPlatformOrdersCountM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                supplierName: this.formData.supplierName,
                productType: 0,
                state: [1]
            }
            if (this.lxKeyword != null) {
                params.keywords = this.lxKeyword
            }
            this.showLoading = true
            getMaterialPageList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.lxtableData = res.list
                if (res.list.length != 0) {
                    this.labelTitle = res.list[0].labelTitle
                    this.count = res.list[0].count
                } else {
                    this.count = []
                }
                this.initCharts()
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        getPlatformOrdersdzCountM () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                supplierName: this.formData.supplierName,
                productType: 1,
                state: [1]
            }
            if (this.dzKeyword != null) {
                params.keywords = this.dzKeyword
            }
            this.dzshowLoading = true
            getMaterialPageList(params).then(res => {
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
                this.dztableData = res.list
                if (res.list.length != 0) {
                    this.labelTitle = res.list[0].labelTitle
                    this.count = res.list[0].count
                } else {
                    this.count = []
                }
                this.dzshowLoading = false
            }).catch(() => {
                this.dzshowLoading = false
            })
        },
        getPlatformOrderszzCountM () {
            let params = {
                page: this.paginationInfo3.currentPage,
                limit: this.paginationInfo3.pageSize,
                supplierName: this.formData.supplierName,
                productType: 2,
                state: [1]
            }
            if (this.zzKeyword != null) {
                params.keywords = this.zzKeyword
            }
            this.zzshowLoading = true
            getMaterialPageList(params).then(res => {
                this.paginationInfo3.total = res.totalCount
                this.paginationInfo3.pageSize = res.pageSize
                this.paginationInfo3.currentPage = res.currPage
                this.zztableData = res.list
                if (res.list.length != 0) {
                    this.labelTitle = res.list[0].labelTitle
                    this.count = res.list[0].count
                } else {
                    this.count = []
                }
                this.zzshowLoading = false
            }).catch(() => {
                this.zzshowLoading = false
            })
        },
        // getPlatformOrdersCountdzM () {
        //     let params = {
        //         page: this.paginationInfo2.currentPage,
        //         limit: this.paginationInfo2.pageSize,
        //         isShop: 1,
        //     }
        //     this.showLoading = true
        //     getMaterialPageList(params).then(res => {
        //         this.paginationInfo.total = res.totalCount
        //         this.paginationInfo.pageSize = res.pageSize
        //         this.paginationInfo.currentPage = res.currPage
        //         this.dztableData = res.list
        //         if(res.list.length != 0) {
        //             this.labelTitle = res.list[0].labelTitle
        //             this.count = res.list[0].count
        //         }else {
        //             this.count = []
        //         }
        //         this.initCharts()
        //         this.showLoading = false
        //     }).catch(() => {
        //         this.showLoading = false
        //     })
        // },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () {
        },
        findFullPathById (options, targetId, props = { value: 'classId', children: 'children' }) {
            const { value: valueKey, children: childrenKey } = props
            function dfs (node, path = []) {
                const currentPath = [...path, node[valueKey]]

                if (!node[childrenKey] && node[valueKey] === targetId) {
                    return currentPath
                }

                if (node[childrenKey]) {
                    for (let child of node[childrenKey]) {
                        const result = dfs(child, currentPath)
                        if (result) return result
                    }
                }

                return null
            }

            for (let option of options) {
                const fullPath = dfs(option)
                if (fullPath) return fullPath
            }

            return []
        },

        handleView (scope) {
            this.isView = true
            this.isEdit = false
            this.scope = scope
            this.permissionList = this.getProductPermission(scope).split('，') // 回显编辑页面的权限多选
            this.viewList = 'class'
            this.formData = scope.row
            if(scope.row.templateWarehousedIs && scope.row.warehousedType) {
                this.formData.warehousedType = scope.row.warehousedType.split(',')
                var target = this.formData.warehousedType.filter(item => !this.warehousedTypeList.includes(item))
                if(target) {
                    this.formData.warehousedTypeStr = target[0]
                    this.formData.warehousedType.splice(target[0], 1)
                    this.formData.warehousedType.push('其他')
                }
            }
            if(scope.row.supplierGroup) {
                this.formData.supplierGroup = scope.row.supplierGroup.split(',')
                this.formData.supplierGroup = this.formData.supplierGroup.map(leafValue => this.findFullPath(leafValue))
            }
            this.getFileList(scope.row.shopSupplierReleId)
            this.formData.templateWarehousedIs = scope.row.templateWarehousedIs ? 1 : 0
            if (this.formData.listPermissions == 1) {
                if (this.permissionList.includes('低值易耗')) {
                    this.getPlatformOrdersCountM()
                }
                if (this.permissionList.includes('大宗商品')) {
                    this.getPlatformOrdersdzCountM()
                }
                if (this.permissionList.includes('周转材料')) {
                    this.getPlatformOrderszzCountM()
                }
            }
            this.action = '编辑'
        },
        getFileList (id) {
            getSupplierFileList(id).then(res=>{
                this.formData.files = res
            })
        },
        handleNew () {
            this.viewList = 'class'
            this.action = '新增'
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该信息吗？', async () => {
                showLoading()
                del({ id: scope.row.shopSupplierReleId }).then(res => {
                    if (res.message === '操作成功') {
                        this.getTableData()
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                }).finally(() => hideLoading())
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => item.shopSupplierReleId)
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.$message({
                            message: res.message,
                            type: 'success'
                        })
                        this.getTableData()
                    }
                }).finally(() => hideLoading())
            })
        },
        //
        handleSupplierType () {
            this.supplierData = {
                ids: this.selectedRows.map(item => item.shopSupplierReleId),
                supplierType: null,
                supplierGroup: null,
                templateWarehousedIs: null,
                warehousedType: [],
                warehousedTypeStr: '',
                warehousedDesc: '',
                warehousedFile: null
            }
            this.handleSupplierVisible = true
        },
        //
        handleSupplierGroup () {
            this.supplierGroupData = {
                ids: this.selectedRows.map(item => item.shopSupplierReleId),
                supplierGroup: null,
            }
            this.supplierGroupVisible = true
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (this.changedRow.length === 0) {
                return this.changedRow.push(row)
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.systemId === row.systemId) {
                    t = row
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push(row)
            }
            flag = true
            //     if(!this.changedRow[0]) {
            //         return this.changedRow.push({ systemId: row.systemId, keyValue: parseInt(row.keyValue), keyValue2: parseInt(row.keyValue2), sort: parseInt(row.sort), remarks: parseInt(row.remarks) })
            //     }
            //     let sortArr = this.changedRow.map((item, i) => {
            //         if(item.systemId === row.systemId) return i
            //     })
            //     let keyValueArr = this.changedRow.map((item, i) => {
            //         if(item.keyValue === row.keyValue) return i
            //     })
            //     let keyValue2Arr = this.changedRow.map((item, i) => {
            //         if(item.keyValue2 === row.keyValue2) return i
            //     })
            //     let remarksArr = this.changedRow.map((item, i) => {
            //         if(item.remarks === row.remarks) return i
            //     })
            //     if(sortArr[0]) {
            //         return this.changedRow[sortArr[0]].sort = row.sort
            //     } else if(keyValueArr[0]) {
            //         return this.changedRow[keyValueArr[0]].keyValue = row.keyValue
            //     }else if(keyValue2Arr[0]) {
            //         return this.changedRow[keyValue2Arr[0]].keyValue2 = row.keyValue2
            //     }else if(remarksArr[0]) {
            //         return this.changedRow[remarksArr[0]].remarks = row.remarks
            //     }
            //     return this.changedRow.push({ systemId: row.systemId, keyValue: parseInt(row.keyValue), keyValue2: parseInt(row.keyValue2), sort: parseInt(row.sort), remarks: parseInt(row.remarks) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                let msg = '当前没有值被修改！'
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = '您确定要修改排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                updateByBatch(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.changedRow = []
                        this.$message({
                            message: '修改成功',
                            type: 'success'
                        })
                        this.getTableData()
                    }
                })
            })
        },
        //获取群组数据
        async getGroupList () {
            let params = {
                productType: 0
            }
            treeByName(params).then(res => {
                this.supplierGroupList = res
                this.getTableData()
            })
        },
        // 获取列表数据
        async getTableData () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                supplierType: this.searchForm.supplierType,
                templateWarehousedIs: this.searchForm.templateWarehousedIs ? this.searchForm.templateWarehousedIs == 1 : null,
                supplierGroup: this.searchForm.supplierGroup ? this.searchForm.supplierGroup[0] : null
            }
            if (this.keywords != null && this.keywords !== '') {
                params.keywords = this.keywords
            }
            getList(params).then(res => {
                this.pages = res
                this.tableData = res.list.map(item => {
                    item.permissionList = []
                    return item
                })
                this.listPermissions = res.listPermissions
                this.formData = {}
            })

            // getList(this.requestParams).then(res => {
            //     if (res.list) {
            //         this.tableData = res.list
            //     } else {
            //         this.clientPop('warn', res.message, () => { })
            //     }
            //     this.pages = res
            // })
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.formData.taxRate === 0) {
                        this.$message.error('税率请大于0')
                    }
                    if (this.action === '编辑') {
                        return this.handleEditData()
                    } else {
                        this.clientPop('info', '确认保存数据吗？', () => {
                            this.formData.permissionsCommodities = this.permissionList.includes('大宗商品') ? '1' : '0'
                            this.formData.permissionsLowValue = this.permissionList.includes('低值易耗') ? '1' : '0'
                            this.formData.permissionsTurnover = this.permissionList.includes('周转材料') ? '1' : '0'
                            create(this.formData).then(res => {
                                if (res.code !== 200) return this.viewList = true
                                this.$message({
                                    message: res.message,
                                    type: 'success'
                                })
                                this.viewList = true
                                this.getTableData()
                            })
                        })

                    }

                }
            })
        },
        onRevocation () {

        },
        onPass () {
            let params = {
                id: this.formData.id,
                state: 2
            }
            auditSupplierType(params).then(res=>{
                if (res.code !== 200) return this.viewList = true
                this.$message({
                    message: res.message,
                    type: 'success'
                })
                this.viewList = true
                this.getTableData()
            })
        },
        onReject () {
            let params = {
                id: this.formData.id,
                state: 3
            }
            auditSupplierType(params).then(res=>{
                if (res.code !== 200) return this.viewList = true
                this.$message({
                    message: res.message,
                    type: 'success'
                })
                this.viewList = true
                this.getTableData()
            })
        },
        findFullPath (targetValue) {
            const traverse = (nodes, path = []) => {
                for (const node of nodes) {
                    const currentPath = [...path, node.classId]
                    if (node.classId === targetValue) {
                        return currentPath
                    }
                    if (node.children) {
                        const result = traverse(node.children, currentPath)
                        if (result) return result
                    }
                }
                return null
            }
            return traverse(this.supplierGroupList)
        },
        supplierTypeSave () {
            this.$refs.supplierTypeForm.validate(valid => {
                if (valid) {
                    this.clientPop('info', '确认保存数据吗？', () => {
                        if (this.supplierData.warehousedType && this.supplierData.warehousedType.length > 0) {
                            this.supplierData.warehousedType = this.supplierData.warehousedType.map(item => item == '其他' ? this.supplierData.warehousedTypeStr : item)
                            this.supplierData.warehousedType = this.supplierData.warehousedType.join(',')
                        } else {
                            this.supplierData.warehousedType = null
                        }
                        setSupplierType(this.supplierData).then(res => {
                            if (res.code !== 200) return this.handleSupplierVisible = true
                            this.$message({
                                message: res.message,
                                type: 'success'
                            })
                            this.supplierData = {
                                ids: [],
                                supplierType: null,
                                supplierGroup: null,
                                templateWarehousedIs: null,
                                warehousedType: [],
                                warehousedTypeStr: '',
                                warehousedDesc: '',
                                files: []
                            }
                            this.getTableData()
                        }).finally(() => {
                            this.handleSupplierVisible = false
                        })
                    })
                }
            })
        },
        processSupplierGroup (group) {
            const data = JSON.parse(JSON.stringify(group))
            if (data && data instanceof Array && data.length > 0) {
                let arr = []
                data.forEach(item => {
                    if (item instanceof Array) {
                        arr.push(item[item.length - 1])
                    } else {
                        arr.push(item)
                    }
                })
                return arr.join(',')
            }
        },
        supplierGroupSave () {
            this.$refs.groupForm.validate(valid => {
                if (valid) {
                    this.clientPop('info', '确认保存数据吗？', () => {
                        if(!this.supplierGroupData.supplierGroup) {
                            return
                        }
                        this.supplierGroupData.supplierGroup = this.processSupplierGroup(this.supplierGroupData.supplierGroup)
                        setSupplierGroup(this.supplierGroupData).then(res => {
                            if (res.code !== 200) return this.supplierGroupVisible = true
                            this.$message({
                                message: res.message,
                                type: 'success'
                            })
                            this.supplierGroupData = {
                                ids: [],
                                supplierGroup: null
                            }
                            this.getTableData()
                        }).finally(() => {
                            this.supplierGroupVisible = false
                        })
                    })
                }
            })
        },
        supplierTypeChange () {

        },
        isTemplateChange () {
            this.supplierData.warehousedType = []
            this.supplierData.warehousedDesc = ''
            this.supplierData.warehousedTypeStr = ''
            this.supplierData.warehousedFile = null
        },
        editTemplateChange () {

        },
        inputChange () {
            this.$forceUpdate()
        },
        // eslint-disable-next-line no-unused-vars
        handleInput (value) {
            let timer = null
            return value => {
                if (timer) clearTimeout(timer)
                timer = setTimeout(() => {
                    this.supplierData.warehousedTypeStr = value
                }, 300)
            }
        },
        // 判断附件格式
        handleBeforeUpload (file) {
            console.log(file)
        },
        handleExceed (files, fileList) {
            this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleRemove (file) {
            this.fileLoading = true
            let files = this.supplierData.files
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createWFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code == 200) {
                    this.$message.success('删除成功！')
                    this.supplierData.files = newFiles
                }
                this.fileLoading = false
            }).catch(() => {
                this.fileLoading = false
            })
        },
        uploadWarehousedFile (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 3)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if (res.code != null && res.code != 200) {
                    this.fileList.push(file)
                    this.fileList.pop()
                } else {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    if(!this.supplierData.files) {
                        this.supplierData.files = []
                    }
                    this.supplierData.files.push({
                        name: res[0].objectName,
                        relevanceType: 5,
                        url: res[0].nonIpObjectPath,
                        fileType: 3,
                        fileFarId: res[0].recordId
                    })
                    alert(JSON.stringify(this.supplierData.files))
                }
            }).finally(() => {
                this.uploadLoading = false
            })
        },
        handleUploadChange (file) {
            if (file.status === 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status === 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        handleEditData () {
            this.formData.permissionsCommodities = this.permissionList.includes('大宗商品') ? '1' : '0'
            this.formData.permissionsLowValue = this.permissionList.includes('低值易耗') ? '1' : '0'
            this.formData.permissionsTurnover = this.permissionList.includes('周转材料') ? '1' : '0'
            update(this.formData).then(res => {
                if (res.message === '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
            this.formData = {}
            this.getTableData()
        },
        // 获取屏幕大小
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.right .top {
  padding-right: 10px
}

.base-page .left {
  width: 30%;
  padding: 0;
}

.base-page .center {
  width: 60%;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__explain {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}

.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
  -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
  -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}

.aaa {
  display: inline-block;
  padding-top: 20px;
  width: unset;
}
</style>
