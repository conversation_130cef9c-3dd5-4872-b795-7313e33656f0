<template>
    <div class="root">
        <div class="main">
            <div class="toolBox p20 mt20">
                <div class="boxTitle dfa">
                    <div></div>
                    <span>常用工具</span>
                </div>
                <div class="toolList dfb mt30">
                    <div class="toolItem dfc" style="flex-direction: column" v-for="(item, i) in toolList" :key="i">
                        <img :src="item.img" alt="" />
                        <div class="mt10">{{ item.name }}</div>
                    </div>
                </div>
            </div>
            <div class="bidBox p20 mt20">
                <div class="boxTitle dfa">
                    <div></div>
                    <span>投标信息</span>
                </div>
                <div class="bidList dfb mt30">
                    <div class="bidItem dfc">
                        <img src="../../../../assets/images/img/ico_caigou.png" alt="" />
                        <div class="bidItem_right ml10">
                            <div class="df bidItem_right_1">
                                <div>214</div>
                                <span>单</span>
                            </div>
                            <div class="bidItem_right_2 mt10">累计中标单数</div>
                        </div>
                    </div>
                    <div class="bidItem dfc">
                        <img src="../../../../assets/images/img/ico_caigou.png" alt="" />
                        <div class="bidItem_right ml10">
                            <div class="df bidItem_right_1">
                                <div>6</div>
                                <span>单</span>
                            </div>
                            <div class="bidItem_right_2 mt10">累计下单合同数</div>
                        </div>
                    </div>
                    <div class="bidItem dfc">
                        <img src="../../../../assets/images/img/ico_caigou.png" alt="" />
                        <div class="bidItem_right ml10">
                            <div class="df bidItem_right_1">
                                <div>314</div>
                                <span>万</span>
                            </div>
                            <div class="bidItem_right_2 mt10">累计采购金额</div>
                        </div>
                    </div>
                    <div class="bidItem dfc">
                        <img src="../../../../assets/images/img/ico_caigou.png" alt="" />
                        <div class="bidItem_right ml10">
                            <div class="df bidItem_right_1">
                                <div>2224</div>
                                <span>万</span>
                            </div>
                            <div class="bidItem_right_2 mt10">累计成交金额</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 商城订单 -->
            <div class="orderBox p20 mt20">
                <div class="boxTitle dfa">
                    <div></div>
                    <span>商城订单</span>
                </div>
                <div id="lineDom" style="width: 100%; height: 330px"></div>
            </div>
            <!-- 配送计划 -->
            <div class="planBox p20 mt20">
                <div class="boxTitle dfa">
                    <div></div>
                    <span>配送计划</span>
                </div>
                <div class="tableBox mt20">
                    <el-table :data="tableData" style="width: 100%"  :header-cell-style="{ background: '#DFEEFF',color:'#216EC6' }">
                        <el-table-column prop="date" label="计划编号" width="180"> </el-table-column>
                        <el-table-column prop="name" label="合同名称" width="180"> </el-table-column>
                        <el-table-column prop="address" label="要货机构"> </el-table-column>
                        <el-table-column prop="address" label="供货单位"> </el-table-column>
                        <el-table-column prop="address" label="要货类型"> </el-table-column>
                        <el-table-column prop="address" label="状态"> </el-table-column>
                        <el-table-column prop="address" label="变更状态"> </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
    components: {},
    data () {
        return {
            toolList: [
                {
                    name: '后台管理',
                    img: require('../../../../assets/images/img/ico_htgl.png'),
                },
                {
                    name: '电商',
                    img: require('../../../../assets/images/img/ico_ds.png'),
                },
                {
                    name: '个人中心',
                    img: require('../../../../assets/images/img/ico_grzx.png'),
                },
                {
                    name: 'PCWP',
                    img: require('../../../../assets/images/img/ico_pcwp.png'),
                },
                {
                    name: '其他栏目',
                    img: require('../../../../assets/images/img/ico_4.png'),
                },
                {
                    name: '其他栏目',
                    img: require('../../../../assets/images/img/ico_3.png'),
                },
                {
                    name: '其他栏目',
                    img: require('../../../../assets/images/img/ico_x3.png'),
                },
                {
                    name: '其他栏目',
                    img: require('../../../../assets/images/img/ico_x4.png'),
                },
            ],
            tableData: [
                {
                    date: '2016-05-02',
                    name: '王小虎',
                    address: '测试数组',
                },
                {
                    date: '2016-05-04',
                    name: '王小虎',
                    address: '上测试数组',
                },
                {
                    date: '2016-05-01',
                    name: '王小虎',
                    address: ' 1519 弄',
                },
                {
                    date: '2016-05-03',
                    name: '王小虎',
                    address: ' 1516 弄',
                },
            ],
        }
    },
    mounted () {
        this.initLine()
    },
    methods: {
        initLine () {
            var lineDom = echarts.init(document.getElementById('lineDom'))
            var option = {
                // title: {
                //     text: 'Step Line',
                // },
                tooltip: {
                    trigger: 'axis',
                },
                legend: {
                    data: ['销售金额', '退货金额'],
                    bottom: 5
                },
                grid: {
                    top: '16%',
                    left: '3%',
                    right: '4%',
                    bottom: '13%',
                    containLabel: true,
                },
                // toolbox: {
                //     feature: {
                //         saveAsImage: {},
                //     },
                // },
                xAxis: {
                    type: 'category',
                    data: ['2019-01', '2019-01', '2019-01', '2019-01', '2019-01', '2019-01', '2019-01'],
                },
                yAxis: {
                    type: 'value',
                },
                series: [
                    {
                        name: '销售金额',
                        type: 'line',
                        step: 'middle',
                        data: [220, 282, 201, 234, 290, 430, 410],
                        lineStyle: {
                            color: '#72C75A',
                        },
                        symbol: 'none', //取消折点圆圈
                    },
                    {
                        name: '退货金额',
                        type: 'line',
                        step: 'start',
                        data: [120, 132, 101, 134, 90, 230, 210],
                        lineStyle: {
                            color: '#FF99C3',
                        },
                        symbol: 'none', //取消折点圆圈
                    },
                ],
            }
            lineDom.setOption(option)
            window.addEventListener('resize', function () {
                lineDom.resize()
            })
        },
    },
}
</script>
<style scoped lang="scss">
div {
    line-height: 1;
    box-sizing: border-box;
}
.root {
    width: 100%;
    min-height: 100vh;
    background: #f5f5f5;
    padding-bottom: 20px;
    .main {
        width: 1326px;
        margin: 0 auto;
        .boxTitle {
            width: 100%;
            div {
                width: 3px;
                height: 20px;
                background: #216ec6;
                margin-right: 11px;
            }
            span {
                font-size: 20px;
                color: #333333;
                font-weight: 500;
            }
        }
        .toolBox {
            width: 100%;
            height: 174px;
            background: #fff;
            .toolList {
                padding: 0 98px;
                .toolItem {
                    img {
                        background: #ff565a;
                        border-radius: 10px;
                        width: 50px;
                        height: 50px;
                    }
                    div {
                        font-size: 14px;
                        color: #333333;
                        text-align: center;
                        font-weight: 400;
                    }
                }
            }
        }
        .bidBox {
            width: 100%;
            height: 197px;
            background: #fff;
            .bidList {
                padding: 0 98px;
                .bidItem {
                    img {
                        width: 70px;
                        height: 70px;
                    }
                    .bidItem_right {
                        .bidItem_right_1 {
                            div {
                                font-size: 45px;
                                color: #e9ba61;
                                font-weight: 700;
                                font-family: DINAlternate-Bold;
                            }
                            span {
                                font-size: 24px;
                                color: #e9ba61;
                                font-weight: 400;
                                padding-top: 3px;
                            }
                        }
                        .bidItem_right_2 {
                            font-family: SourceHanSansCN-Regular;
                            font-size: 14px;
                            color: #808080;
                            font-weight: 400;
                        }
                    }
                }
            }
        }
        .orderBox {
            width: 100%;
            height: 380px;
            background: #fff;
        }
        .planBox {
            width: 100%;
            // height: 380px;
            background: #fff;
        }
    }
}
</style>
