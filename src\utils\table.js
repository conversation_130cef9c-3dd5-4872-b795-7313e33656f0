// 表格公用方法
// 列表数据增加序号
export function transformTableIndex (tableData, pagination) {
    tableData.forEach((tableCol, index) => {
        if (pagination) {
            tableCol.index = index + 1 + pagination.pageSize * (pagination.currPage - 1)
        }else{
            tableCol.index = index + 1
        }
    })
    return tableData
}
// 递归模糊搜索【父子节点互相关联】
export function deepFuzzySearch (data, keyword, searchProps, childrenKey = 'children') {
    if (!keyword.trim()) return data
    const lowerKeyword = keyword.toLowerCase()
    const result = []

    data.forEach(item => {
        // 检查当前节点是否匹配
        const isMatch = searchProps.some(prop =>
            String(item[prop] || '').toLowerCase().includes(lowerKeyword)
        )

        // 递归处理子节点
        const children = item[childrenKey]
        let matchedChildren = []
        if (Array.isArray(children) && children.length) {
            matchedChildren = deepFuzzySearch(children, keyword, searchProps, childrenKey)
        }

        // 保留匹配节点或其子节点匹配的节点
        if (isMatch || matchedChildren.length) {
            const newItem = { ...item }
            if (matchedChildren.length) {
                newItem[childrenKey] = matchedChildren
            }
            result.push(newItem)
        }
    })

    return result
}
// 递归模糊搜索【子节点关联父节点、父节点不关联子节点】
export function filterTree (data, keyword, searchProps, childrenKey = 'children') {
    if (!keyword.trim()) return data
    const lowerKeyword = keyword.toLowerCase()

    return data.map(node => {
        // 递归处理子节点
        const children = node[childrenKey] ? filterTree(node[childrenKey], keyword, searchProps, childrenKey) : []

        // 判断节点保留条件
        const isSelfMatch = searchProps.some(prop =>
            String(node[prop] || '').toLowerCase().includes(lowerKeyword)
        )

        return (isSelfMatch || children.length) ? { ...node, [childrenKey]: children } : null
    }).filter(Boolean)
}

export default {
    transformTableIndex,
    deepFuzzySearch,
    filterTree,
}