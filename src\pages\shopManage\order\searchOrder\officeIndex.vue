<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" class="btn-greenYellow" @click = "changeShipments">批量修改物流信息</el-button>
<!--                            <el-select  @change="stateTopOptionsClick" v-model="stateOptionTitle" placeholder="请选择状态">-->
<!--                                <el-option-->
<!--                                    v-for="item in stateOptions"-->
<!--                                    :key="item.value"-->
<!--                                    :label="item.label"-->
<!--                                    :value="item.value">-->
<!--                                </el-option>-->
<!--                            </el-select>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <el-input type="text" @blur="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png"
                                                                                                                    slot="suffix" @click="onSearch" /></el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table class="table" :height="rightTableHeight" v-loading="tableLoading" :data="tableData"  border
                          @selection-change="selectionChangeHandle">
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="订单号" width="240" prop="orderSn">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row)">{{scope.row.orderSn}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" width="200" prop="untitled"></el-table-column>
<!--                    <el-table-column label="状态" width="" prop="state">-->
<!--                        <template slot-scope="scope">-->
<!--                            <el-tag v-if="showTableState(scope.row)">{{ tableStateTitle }}</el-tag>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column label="收件人" width="" prop="receiverName"></el-table-column>
                    <el-table-column label="收件人手机号" width="150" prop="receiverMobile" />
                    <el-table-column label="收件地址" width="200" prop="receiverAddress" />
                    <el-table-column label="总金额" width="120" prop="actualAmount" />
                    <el-table-column label="物流单号" width="200" prop="deliveryFlowId" >
                        <template v-slot="scope">
                            <el-input clearable v-model="scope.row.deliveryFlowId" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="物流公司" width="200" prop="logisticsCompany" >
                        <template v-slot="scope">
                            <el-input clearable v-model="scope.row.logisticsCompany" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                    <el-table-column label="发货时间" width="160" prop="deliveryTime" />
                    <el-table-column label="订单备注" width="300" prop="orderRemark"></el-table-column>
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="false" >
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
<!--                <el-row>-->
<!--                    <el-col :span="12" :offset="0">-->
<!--                        <el-form-item label="状态：">-->
<!--                            <el-select @change="stateOptionsClick" v-model="filterData.selectStateTitle" placeholder="请选择状态">-->
<!--                                <el-option-->
<!--                                    v-for="item in filterData.stateOptions"-->
<!--                                    :key="item.value"-->
<!--                                    :label="item.label"-->
<!--                                    :value="item.value">-->
<!--                                </el-option>-->
<!--                            </el-select>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                </el-row>-->
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="订单号：" >
                            <el-input clearable maxlength="100" placeholder="请输入订单号" v-model="filterData.orderSn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="商品名称：" >
                            <el-input clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.untitled"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="实际金额以上：">
                            <el-input type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                        <el-form-item label="实际金额以下：">
                            <el-input type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click = "expertSearch">确定</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapState } from 'vuex'
import { shopManageOrderList, updateBatch } from '@/api/platform/order/orders'
export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            changedRow: [],
            tableLoading: false,
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            stateOptionTitle: '', // 选中的状态标题
            stateOptions: [{
                value: null,
                label: '全部'
            }, {
                value: 0,
                label: '草稿'
            }, {
                value: 1,
                label: '已提交'
            }, {
                value: 2,
                label: '待确认'
            }, {
                value: 3,
                label: '已确认'
            }, {
                value: 4,
                label: '待签订合同'
            }, {
                value: 5,
                label: '已签合同'
            }, {
                value: 6,
                label: '已完成'
            }, {
                value: 7,
                label: '已关闭'
            }],
            // 表格数据
            tableStateTitle: null, // 表格的状态
            dataListSelections: [], //选中的数据
            className: null,
            classId: null, // 分类id
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                untitled: null,
                orderSn: null,
                belowPrice: null,
                abovePrice: null,
                dateValue: [], // 开始时间和结束时间
                selectStateTitle: null, // 选中的标题
                selectSateValue: null,  // 选中的值
                stateOptions: [{
                    value: null,
                    label: '全部'
                }, {
                    value: 1,
                    label: '待付款'
                }, {
                    value: 2,
                    label: '待发货'
                }, {
                    value: 3,
                    label: '待收货'
                }, {
                    value: 4,
                    label: '待评价'
                }, {
                    value: 5,
                    label: '已完成'
                }, {
                    value: 6,
                    label: '已关闭'
                }, {
                    value: 7,
                    label: '结算中'
                }],
                orderBy: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        // this.getTableData()
    },
    methods: {
        changeShipments () {
            if(this.changedRow.length == 0) {
                return this.$message('未修改列表当中的物流信息！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                updateBatch(this.changedRow).then(res => {
                    this.message(res)
                    this.changedRow = []
                    this.getTableData()
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        getChangedRow (row) {
            if(this.changedRow.length == 0) {
                this.changedRow.push({ orderId: row.orderId, deliveryFlowId: row.deliveryFlowId, logisticsCompany: row.logisticsCompany, state: 9 })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if(t.orderId == row.orderId) {
                    t.deliveryFlowId = row.deliveryFlowId
                    t.logisticsCompany = row.logisticsCompany
                    flag = true
                }
            })
            if(!flag) {
                this.changedRow.push({ orderId: row.orderId, deliveryFlowId: row.deliveryFlowId, logisticsCompany: row.logisticsCompany, state: 9 })
            }
        },
        // 显示状态
        showTableState ( row ) {
            let stateValue  = row.state
            if(stateValue === 7) {
                return false
            }
            for (let i = 0; i < this.stateOptions.length; i++) {
                if(stateValue === this.stateOptions[i].value) {
                    this.tableStateTitle = this.stateOptions[i].label
                    return true
                }
            }
        },
        // 选中状态进行查询
        stateTopOptionsClick (value) {
            this.selectOptionValue = value
            this.getTableData()
        },
        // 详情
        handleView (row) {
            //利用$router.push进行跳转
            this.$router.push({
                path: '/supplierSys/order/officeDetail',
                name: 'officeDetail',
                params: {
                    row: row
                }
            })
        },
        // 高级搜索
        expertSearch () {
            this.keywords = null
            this.selectOptionValue = null
            this.stateOptionTitle = ''
            this.getTableData()
            //重置数据
            this.filterData.belowPrice = null // 以下价格
            this.filterData.abovePrice = null // 以上价格
            this.filterData.untitled = null
            this.filterData.orderSn = null
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.selectStateTitle = null// 选中的标题
            this.filterData. selectSateValue = null // 选中的值
            this.queryVisible = false
        },
        // 高级搜索状态选中
        stateOptionsClick (value) {
            this.filterData.selectSateValue = value
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 获取表格数据
        getTableData () {
            let params = {
                classId: this.classId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: 11,
                orderClass: 1
            }
            if(this.selectOptionValue != null) {
                params.state = this.selectOptionValue
            }
            if(this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if(this.filterData.selectSateValue != null) {
                params.state = this.filterData.selectSateValue
            }
            if(this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0],
                params.endDate = this.filterData.dateValue[1]
            }
            if(this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if(this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if(this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if(this.filterData.untitled != null) {
                params.untitled = this.filterData.untitled
            }
            if(this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            shopManageOrderList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
            })
        },
        // 查询
        onSearch () {
            this.getTableData()
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {min-width: 200px; padding: 0;}
.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;
    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {min-height: auto;}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}
/deep/ .el-dialog__body {
    margin-top: 0;
}
</style>
