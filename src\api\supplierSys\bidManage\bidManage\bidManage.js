import service from '@/utils/request'

const { httpPost, httpGet } = service

const getTenderNoticeList = params => {
    return httpPost({
        url: '/tender/supplierSys/tenderNotice/listByEntity',
        params,
    })
}
//查询中标信息
const winTenderList = params => {
    return httpPost({
        url: '/tender/supplierSys/tender/winBidList',
        params,
    })
}
const getWinPackageList = params => {
    return httpPost({
        url: '/tender/supplierSys/tenderPackage/winBidPackageList',
        params,
    })
}
const getTenderById = params => {
    return httpGet({
        url: '/tender/supplierSys/tender/findById',
        params,
    })
}

export {
    getTenderNoticeList,
    winTenderList,
    getWinPackageList,
    getTenderById

}