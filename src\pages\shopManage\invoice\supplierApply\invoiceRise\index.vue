<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                        </div>
                    </div>
                    <!-- -搜索栏开始----------------------------搜索栏 -->
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-input
                            type="text" clearable placeholder="请输入关键字搜索" @keyup.enter.native="handleInputSearch"
                            v-model="keywords"
                        >
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                    <!-- 高级搜索弹出框开始 -->
                    <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%">
                        <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="是否默认：">
                                        <el-select v-model="filterData.state" clearable>
                                            <el-option
                                                v-for="item in invoiceState" :key="item.value" :label="item.label"
                                                :value="item.value"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="发票类型：">
                                        <el-select v-model="filterData.state" clearable>
                                            <el-option
                                                v-for="item in typeOptions" :key="item.value" :label="item.label"
                                                :value="item.value"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="单位名称：">
                                        <el-input
                                            clearable v-model="filterData.company" maxlength="11"
                                            placeholder="输入单位名称"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="单位税号：">
                                        <el-input
                                            clearable v-model="filterData.dutyParagraph" maxlength="11"
                                            placeholder="输入单位税号"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="开户银行：">
                                        <el-input
                                            clearable v-model="filterData.bank" maxlength="11"
                                            placeholder="输入开户银行"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="银行账号：">
                                        <el-input
                                            clearable v-model="filterData.bankAccount" maxlength="11"
                                            placeholder="输入银行账号"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="收票人联系电话：">
                                        <el-input
                                            clearable v-model="filterData.userPhone" type="number" maxlength="11"
                                            placeholder="输入手机号查询"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="收票人姓名：">
                                        <el-input
                                            clearable v-model="filterData.userName" placeholder="输入姓名查询"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <span slot="footer">
                            <el-button type="primary" @click="confirmSearch">查询</el-button>
                            <el-button @click="resetSearchConditions">清空</el-button>
                            <el-button @click="queryVisible = false">取消</el-button>
                        </span>
                    </el-dialog>
                    <!-- 高级搜索弹出框 -->
                    <!-- -搜索栏结束----------------------------搜索栏 -->
                </div>
            </div>
            <div class="e-table">
                <el-table
                    class="table"
                    :height="rightTableHeight"
                    v-loading="tableLoading"
                    :data="tableData" border highlight-current-row
                    @row-click="handleCurrentInventoryClick"
                    ref="eltableCurrentRow" @current-change="handleCurrentChange"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <!-- 收料人ID -->
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="220px">
                        <template slot-scope="scope">
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.state==1"
                                size="mini"
                                type="danger"
                                @click="updateStateM(scope.row,0)"
                            >取消默认
                            </el-button>
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.state==0"
                                size="mini"
                                type="success"
                                @click="updateStateM(scope.row,1)"
                            >设为默认
                            </el-button>

                            <el-button
                                size="mini"
                                type="danger"
                                @click="onDel(scope)"
                            >删除
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="单位名称" width="150px">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.company }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="单位税号" width="150px">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.dutyParagraph }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" width="150px">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.invoiceType === 0" type="success">增值税专用发票</el-tag>
                            <el-tag v-if="scope.row.invoiceType === 1" type="primary">增值税普通发票</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="抬头类型" width="">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.riseType === 0" type="success">个人</el-tag>
                            <el-tag v-if="scope.row.riseType === 1" type="success">单位</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="开户银行" width="200" prop="bank"></el-table-column>
                    <el-table-column label="银行账号" width="200" prop="bankAccount"></el-table-column>
                    <el-table-column label="收票人姓名" width="200" prop="userName"></el-table-column>
                    <el-table-column label="收票人联系电话" width="200" prop="userPhone"></el-table-column>
                    <el-table-column label="创建时间" width="200" prop="gmtCreate"></el-table-column>
                    <el-table-column label="备注" width="200" prop="remarks"></el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.total" :limit="20" :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <el-dialog id="addreconciliationId" v-dialogDrag :close-on-click-modal="false" :visible.sync="showReconciliationForm"
                   style="" title="默认开票抬头" width="40%" >
            <el-form ref="bidingFormRef" :disabled="false" :model="state" class="demo-ruleForm" label-width="300px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="默认开票抬头：" prop="state">
                            <el-radio v-model="state" :label="1">是</el-radio>
                            <el-radio v-model="state" :label="0">否</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="buttons" style="position: absolute; right: 20px; bottom: 20px;">
                <el-button class="btn-blue" @click="addAffirmM">确认</el-button>
                <el-button @click="showReconciliationForm = false">返回</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { batchDelete, create, del, edit, getList, updateState } from '@/api/supplierSys/invoice/invoiceRecord'
import { debounce, hideLoading, showLoading } from '@/utils/common'

export default {
    components: {
        ComPagination
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return this.screenHeight - 21 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return this.screenWidth - 302 + 'px'
        },
        // 列表高度
        rightTableHeight () {
            if (this.pages.total > 0) {
                return this.screenHeight - 244
            }
            return this.screenHeight - 291
        }
    },
    data () {
        return {
            state: 1,
            showReconciliationForm: false,
            tableLoading: false,
            screenWidth: 0,
            alertName: '收料员',
            screenHeight: 0,
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            queryVisible: false, // 是否显示高级搜索：true 显示，false不显示

            keywords: '',
            tableData: [],
            requestParams: {},

            pages: {
                total: 0,
                currPage: 1,
                pageSize: 20
            },
            // 高级查询-启停用状态
            stateFilter: [
                { label: '单位', value: 1 },
                { label: '个人', value: 0 },
            ],
            invoiceState: [
                { label: '默认', value: 1 },
                { label: '其他', value: 0 },
                { label: '全部', value: null },
            ],
            typeOptions: [
                { label: '全部', value: null },
                { label: '增值税专用发票', value: 0 },
                { label: '增值税普通发票', value: 1 },
            ],
            // 查询对象
            filterData: {
                orderBy: 0,
                state: null,
                invoiceType: null,
                userName: null,
                userAddress: null,
                userPhone: null,
                company: null,
                registerAddress: null,
                bank: null,
                bankAccount: null,

            },
            action: '编辑',
            //
            currentClass: null,
            currentRow: null,
            changedRow: [],
            selectedRows: [],

        }
    },

    created () {
        this.getTableData()
    },
    methods: {
        // 获取列表数据
        async getTableData () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                userType: 1
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.filterData.invoiceType != null) {
                params.invoiceType = this.filterData.invoiceType
            }
            if (this.filterData.invoiceCategory != null) {
                params.invoiceCategory = this.filterData.invoiceCategory
            }
            if (this.filterData.riseType != null) {
                params.riseType = this.filterData.riseType
            }
            if (this.filterData.userName != null) {
                params.userName = this.filterData.name
            }
            if (this.filterData.userAddress != null) {
                params.userAddress = this.filterData.userAddress
            }
            if (this.filterData.userPhone != null) {
                params.userPhone = this.filterData.userPhone
            }
            if (this.filterData.company != null) {
                params.company = this.filterData.company
            }
            if (this.filterData.dutyParagraph != null) {
                params.dutyParagraph = this.filterData.dutyParagraph
            }
            if (this.filterData.bank != null) {
                params.bank = this.filterData.bank
            }
            if (this.filterData.bankAccount != null) {
                params.bankAccount = this.filterData.bankAccount
            }
            if (this.keywords != null || this.keywords != '') {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            getList(params).then(res => {
                this.tableData = res.list
                this.pages.total = res.totalCount
                this.pages.pageSize = res.pageSize
                this.pages.currentPage = res.currPage
                // console.log(this.pages)
                // this.pages = res
            })
            this.tableLoading = false
            this.viewList = true
        },
        handleNew () {
            // if (this.tableData.length == 0) {
            this.$router.push({
                path: 'supplierApply/invoiceRiseApply',
                name: 'invoiceRiseApply',
                params: {
                    viewType: 'add',
                    state: 1
                }
            })
            // }else {
            //     this.showReconciliationForm = true
            // }
        },
        // 新增表单数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },
        // 编辑表单数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message === '操作成功') {
                    this.$message.success('操作成功')
                    this.getTableData()
                    this.viewList = true
                }
            })
        },

        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        addAffirmM () {
            this.$router.push({
                path: 'performance/invoice/invoiceRiseApply',
                name: 'invoiceRiseApply',
                params: {
                    viewType: 'add',
                    state: this.state

                }
            })
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        resetSearchConditions () {
            this.filterData.state = null,
            this.filterData.invoiceType = null,
            this.filterData.userName = null,
            this.filterData.userAddress = null,
            this.filterData.userPhone = null,
            this.filterData.company = null,
            this.filterData.registerAddress = null,
            this.filterData.bank = null,
            this.filterData.bankAccount = null
        },
        // 高级查询
        confirmSearch () {
            this.keywords = null
            this.getTableData()
            this.queryVisible = false
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        handleView (scope) {
            this.viewList = 'class'
            this.$router.push({
                path: 'performance/invoice/invoiceRiseApply',
                name: 'invoiceRiseApply',
                params: {
                    viewType: 'class',
                    invoiceRecordId: scope.row.invoiceRecordId
                }
            })
        },

        updateStateM (row, state) {
            let params = {
                invoiceRecordId: row.invoiceRecordId,
                state: state,
            }
            this.clientPop('info', '您确定此条' + row.invoiceRecordId + '设为默认发票抬头吗？', async () => {
                this.tableLoading = true
                updateState(params).then(res => {
                    if (res.code == 200) {
                        this.$message.success('操作成功')
                        this.getTableData()
                    }
                })
                this.tableLoading = false
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该' + scope.row.company + '的发票抬头吗？', async () => {
                showLoading()
                del({ id: scope.row.invoiceRecordId }).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                this.$message.info('未选择数据')
                return
            }
            this.clientPop('info', '您确定要删除选中的发票抬头吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.invoiceRecordId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('删除成功')
                        this.getTableData()
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        // this.setUnitMeasur()
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-form-item.uploader {
    height: 200px;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload
/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 200px;
        margin-top: 0px;
    }
}
/deep/ .el-dialog {
    //height: 500px !important;
    padding: 0;

    .el-dialog__header {
        margin-bottom: 20px;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        background: red url(../../../../../assets/test.png) no-repeat;

        .el-dialog__title {
            color: #fff;
        }
    }

    .el-dialog__body {
        height: 70%;
        margin-top: 20px;
    }

    .el-dialog__close.el-icon.el-icon-close::before {
        width: 44px;
        height: 44px;
    }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>
