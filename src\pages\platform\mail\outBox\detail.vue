<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs tab-position="left" :style="{ height: tabsContentHeight }" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="基本信息" name="baseInfo" :disabled="clickTabFlag"/>
                <el-tab-pane label="附件信息" name="filesInfo" :disabled="clickTabFlag"/>
                <el-tab-pane label="收件人信息" name="receive" :disabled="clickTabFlag" v-if="formData.checkState != 2"/>
                <div id="tabs-content">
                    <div id="baseInfoCon" class="con">
                        <div class="tabs-title" id="baseInfo">基本信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发件人名称：">
                                            <span>{{ formData.sendName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="标题:">
                                            <span>{{ formData.title }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="消息类型：">
                                            <span>{{ ['普通消息', '平台消息', '系统消息'][formData.messageType] }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发送时间：">
                                            <span>{{ formData.sendDate }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col v-if="showDevFunc" :span="12">
                                        <el-form-item label="是否提醒：" prop="remind">
                                            <el-radio-group disabled v-model="formData.remind">
                                                <el-radio :label="1">是</el-radio>
                                                <el-radio :label="0">否</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="消息内容：" prop="productDescribe">
                                            <editor v-model="formData.content" :disabled='true'></editor>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div id="filesInfoCon" class="con">
                        <div class="tabs-title" id="filesInfo">附件资料</div>
                        <div class="e-table" style="background-color: #fff">
                            <el-table
                                ref="tableRef"
                                border
                                :data="fileList"
                                class="table"
                            >
                                <el-table-column label="序号" type="index" width="60"/>
                                <el-table-column prop="name" label="附件名称"/>
                                <el-table-column label="操作">
                                    <template v-slot="scope">
                                        <el-button type="primary" @click="handleDownload(scope.row)">下载</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <div id="receiveCon" class="con" v-if="formData.checkState != 2">
                        <div class="tabs-title" id="receive">收件人信息</div>
                        <div class="e-table" style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <div class="left-btn">
                                        <el-select @change="getReceiveList" v-model="isRead" placeholder="请阅读状态">
                                            <el-option v-for="item in isReads" :key="item.value" :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </div>
                                </div>
                                <div class="search_box">
                                    <el-input clearable type="text" @blur="getReceiveList" placeholder="输入搜索关键字" v-model="receive.keywords">
                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getReceiveList"/>
                                    </el-input>
                                </div>
                            </div>
                            <el-table
                                ref="tableRef"
                                border
                                :data="receive.tableData"
                                class="table"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="receiveName" label="收件人名称"></el-table-column>
                                <el-table-column prop="receiveCode" label="收件人编号"></el-table-column>
                                <el-table-column label="收件人类型">
                                    <template v-slot="scope">
                                        <el-tag v-if="scope.row.receiveType==0">店铺</el-tag>
                                        <el-tag v-else-if="scope.row.receiveType==1">用户</el-tag>
                                        <el-tag v-else-if="scope.row.receiveType==3">供应商</el-tag>
                                        <el-tag v-else type="danger">已关闭</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="是否已读">
                                    <template v-slot="scope">
                                        <el-tag v-if="scope.row.isRead==0">未读</el-tag>
                                        <el-tag v-else-if="scope.row.isRead==1">已读</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="接收时间" width="160"></el-table-column>
                            </el-table>
                        </div>
                        <Pagination
                            v-show="receive.tableData && receive.tableData.length > 0"
                            :total="receive.paginationInfo.totalCount"
                            :pageSize.sync="receive.paginationInfo.pageSize"
                            :currentPage.sync="receive.paginationInfo.currentPage"
                            @currentChange="getReceiveList"
                            @sizeChange="getReceiveList"
                        />
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>

</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
import editor from '../../../../components/quillEditor'
import { receivePageList } from '@/api/platform/mail/outbox'
import { previewFile } from '@/api/platform/common/file'
import { selectFileList } from '@/api/base/file'

export default {

    data () {
        return {
            fileList: [],
            //基本信息表单数据
            formData: {},
            // 表格数据
            isRead: null,
            receive: {
                type: 1,
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    totalCount: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            receiveTypes: [
                {
                    value: 1,
                    label: '用户'
                },
                {
                    value: 0,
                    label: '店铺'
                }
            ],
            isReads: [
                {
                    value: null,
                    label: '全部'
                },
                {
                    value: 0,
                    label: '未读'
                },
                {
                    value: 1,
                    label: '已读'
                }],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    components: {
        Pagination,
        editor
    },
    created () {
        this.formData = this.$route.params.row
        this.getReceiveList()
        this.getFileInfos(this.formData.stationMessageId)
    },
    mounted () {
        // 保存所有tabName
        const arr = ['baseInfo', 'filesInfo', 'receive']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) return
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 90 + 'px !important'
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        getFileInfos (relevanceId) {
            let params = {
                relevanceId: relevanceId,
                relevanceType: 7,
            }
            selectFileList(params).then(res => {
                this.fileList = res.list
            })
        },
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        // 获取收件人信息
        getReceiveList () {
            let params = {
                page: this.receive.paginationInfo.currentPage,
                limit: this.receive.paginationInfo.pageSize,
                stationMessageId: this.formData.stationMessageId
            }
            if (this.isRead != null) {
                params.isRead = this.isRead
            }
            if (this.receive.keywords != null) {
                params.keywords = this.receive.keywords
            }
            receivePageList(params).then(res => {
                this.receive.tableData = res.list
                this.receive.paginationInfo = res
            })
        },

        //取消
        handleClose () {
            this.$router.replace('/platform/mail/outBox')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}
</style>
