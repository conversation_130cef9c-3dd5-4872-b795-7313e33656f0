<template>
    <div v-if="showSetOrg" class="base-page">
        <!-- 列表 -->
        <div v-show="viewList === true" class="right">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <el-button class="btn-greenYellow" type="primary" @click="handleNew">新增</el-button>
                            <el-button class="btn-greenYellow" type="primary" @click="changePublishState(1)">发布
                            </el-button>
                            <el-button class="btn-delete" type="primary" @click="changePublishState(2)">取消发布
                            </el-button>
                            <el-button class="btn-delete" type="primary" @click="handleDelete">批量删除</el-button>
                            <el-button class="btn-greenYellow" type="primary" @click="changeSortValue">批量修改排序值</el-button>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                        <el-input v-model="keywords" placeholder="输入搜索关键字" type="text" @keyup.enter.native="onSearch">
                            <img slot="suffix" src="@/assets/search.png" @click="onSearch" />
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table">
                <el-table
                    ref="mainTable" v-loading="isLoading" :data="tableData" :height="rightTableHeight" border class="table" highlight-current-row @row-click="handleCurrentInventoryClick"
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="操作" width="120">
                        <template v-slot="scope">
                            <span class="action" @click="onDel(scope)">
                                <img alt="" src="../../../../assets/btn/delete.png">
                            </span>
                        </template>
                    </el-table-column>
                    <!-- 图片 -->
                    <el-table-column label="图片" type="index" width="120">
                        <template v-slot="scope">
                            <el-image
                                :src="scope.row.pictureUrl"
                                class="pointer"
                                style="width: 90px; height: 60px"
                                @click="handleView(scope)"
                            />
                        </template>
                    </el-table-column>
                    <!-- 图片链接地址 -->
                    <el-table-column label="链接地址">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.pictureLinkAddress }}
                            </span>
                        </template>
                    </el-table-column>
                    <!-- 图片显示位置 -->
                    <el-table-column label="显示位置">
                        <template v-slot="scope">
                            <span v-if="scope.row.useType == '1'">平台首页</span>
                            <span v-else-if="scope.row.useType == '2'">商城首页</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="空间位置">
                        <template v-slot="scope">
                            <span v-if="scope.row.usePositioning == '1'">轮播图</span>
                            <span v-else-if="scope.row.usePositioning == '2'">底部</span>
                            <span v-else-if="scope.row.usePositioning == '0'">无定位</span>
                            <span v-else-if="scope.row.usePositioning == '3'">中部</span>
                        </template>
                    </el-table-column>
                    <!-- 图片发布状态 -->
                    <el-table-column label="发布状态">
                        <template v-slot="scope">
                            {{ scope.row.state == 1 ? '已发布' : '未发布' }}
                        </template>
                    </el-table-column>
                    <!-- 图片链接类型 -->
                    <el-table-column label="链接类型">
                        <template v-slot="scope">
                            <span v-if="scope.row.pictureType == '1'">无</span>
                            <span v-else-if="scope.row.pictureType == '3'">内部链接</span>
                            <span v-else-if="scope.row.pictureType == '2'">外部链接</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">
                                {{ scope.row.remarks }}
                            </span>
                        </template>
                    </el-table-column>
                    <!-- 图片排序值 -->
                    <el-table-column label="排序值" type="index" width="120">
                        <template v-slot="scope">
                            <el-input v-model="scope.row.sort" type="number" @change="getChangedRow(scope.row)"/>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :currentPage.sync="pages.currPage"
                :limit="20"
                :pageSize.sync="pages.pageSize"
                :total="pages.totalCount"
                @currentChange="currentChange"
                @sizeChange="sizeChange"
            />
        </div>
        <div v-show="viewList !== true" class="right">
            <!-- ---------------------新增编辑窗口--------------------- -->
            <div v-show="viewList === 'class'" class="e-form" style="padding: 0 10px 10px;">
                <div class="tabs-title">基本信息</div>
                <el-form ref="formEdit" :model="formData" :rules="formRules" label-width="150px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item class="uploader" label="图片地址（推荐：1920x700）：" prop="pictureUrl" required>
                                <div>
                                    <el-upload
                                        v-loading="uploadLoading" auto-upload :before-upload="handleBeforeUpload"
                                        :http-request="uploadImg"
                                        :on-change="handleUploadChange" :show-file-list="false"
                                        action="fakeaction"
                                        class="avatar-uploader"
                                        name="img"
                                    >
                                        <img v-if="formData.pictureUrl" :src="formData.pictureUrl" alt="" class="avatar">
                                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    </el-upload>
                                    <el-progress v-show="uploadInProgress" :percentage="uploadPercentage"/>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="图片链接类型：" prop="pictureType" width="150px">
                                <el-select v-model="formData.pictureType" placeholder="图片链接类型">
                                    <el-option v-for="item in picTypeFilter" :key="item.value" :label="item.label" :value="item.value"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="图片显示位置：" prop="useType" width="150px">
                                <el-select v-model="formData.useType" placeholder="图片显示位置">
                                    <el-option v-for="item in useTypeFiter" :key="item.value" :label="item.label" :value="item.value"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                           <el-form-item label="图片空间位置：" prop="usePositioning" width="150px">
                                <el-select v-model="formData.usePositioning" placeholder="图片空间位置">
                                    <el-option v-for="item in usePositioningType" :key="item.value" :label="item.label" :value="item.value"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input v-model="formData.sort" placeholder="填写排序值" type="number"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                             <el-form-item label="图片链接地址：" prop="pictureLinkAddress">
                                <el-input
                                    v-model="formData.pictureLinkAddress" :disabled="formData.pictureType == 1"
                                    placeholder="填写图片链接地址"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input v-model="formData.remarks" autocomplete="off" type="textarea"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag :visible.sync="queryVisible" title="高级查询" width="30%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="发布状态：">
                            <el-select v-model="filterData.state" clearable placeholder="图片发布状态">
                                <el-option v-for="item in stateFilter" :key="item.value" :label="item.label" :value="item.value"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="显示位置：">
                            <el-select v-model="filterData.useType" clearable placeholder="图片显示位置">
                                <el-option v-for="item in useTypeFiter" :key="item.value" :label="item.label" :value="item.value"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="链接类型：">
                            <el-select v-model="filterData.pictureType" clearable placeholder="图片链接类型">
                                <el-option v-for="item in picTypeFilter" :key="item.value" :label="item.label" :value="item.value"/>
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>

            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="advancedQuery">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import {
    batchDelete,
    batchNotPublish,
    batchPublish,
    changeSortValue,
    create,
    del,
    edit,
    getList
} from '@/api/platform/content/adImg'
import { uploadFile } from '@/api/platform/common/file'
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapActions } from 'vuex'

export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            if (this.paginationInfo.total > 0) {
                return this.screenHeight - 244
            }
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            imgUrlPrefixAdd: 'http://*************:9002',
            uploadLoading: false,
            alertName: '图片',
            queryVisible: false,
            action: '编辑',
            classType: '0',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            showSetOrg: true, //设置使用机构
            keywords: '',
            currentClass: null,
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            isLoading: false,
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询选项
            picTypeFilter: [
                { value: null, label: '全部' },
                { value: 3, label: '内部链接' },
                { value: 2, label: '外部链接' },
                { value: 1, label: '无链接' },
            ],
            mallFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '慧采商城' },
                { value: 0, label: '装备商城' },
            ],
            stateFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '发布' },
                { value: 2, label: '未发布' },
            ],
            useTypeFiter: [
                { value: null, label: '全部' },
                { value: 1, label: '平台首页（1920*700）' },
                { value: 2, label: '商城首页（1920*450）' },
            ],
            usePositioningType: [
                { value: 0, label: '首页' },
                { value: 1, label: '轮播图' },
                { value: 2, label: '底部' },
                { value: 3, label: '中部' },
            ],
            // 高级查询数据对象
            filterData: {
                pictureType: null,
                state: null,
                useType: null,
                usePositioning: null,
                mallType: 0,
                orderBy: 1
            },
            // 表单校验规则
            formRules: {
                // pictureUrl: [{ required: true, validator: this.validatePic, }], // 图片验证样式有问题
                pictureUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
                useType: [
                    { required: true, message: '请选择图片显示位置', trigger: 'blur' },
                ],
                usePositioning: [
                    { required: true, message: '请选择图片空间位置', trigger: 'blur' },
                ],
                pictureType: [{ required: true, message: '请选择图片链接类型', trigger: 'blur' }],
                pictureLinkAddress: [{ validator: this.validateUrl, type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入排序值', trigger: 'blur' }]
            },
            paginationInfo: {
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [],
            orgDataTable: [],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            allData: [],
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
                // 图片排序用
                orderValue: 0,
                //图片ID
                pictureId: '',
                // 图片点击跳转的地址
                pictureLinkAddress: '',
                //图片类型（有无链接）
                pictureType: '1',
                // 服务器图片地址
                pictureUrl: '',
                //图片记录ID
                pictureUrlId: '',
                // 备注
                remarks: '',
                // 发布状态 1发布 2未发布
                state: 2,
                // 图片展示位置
                useType: null,
                usePositioning: 1,
                // 商城类型
                mallType: 0
            },
            // pictureUrl: '',
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            Array: [],
            requestParams: {},
            uploadPercentage: 0,
            uploadInProgress: false
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        this.isLoading = true
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy
        }
        getList(params).then(res => {
            this.isLoading = false
            this.pages = res
            this.tableData = res.list
        })
        this.getParams()
    },
    methods: {
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 校验函数
        validateUrl (rule, value, callback) {
            if (this.formData.pictureType === 1 || this.formData.pictureLinkAddress !== '') {
                callback()
            } else {
                callback(new Error('请输入正确的地址格式!'))
            }
        },
        resetSearchConditions () {
            this.filterData.pictureType = null
            this.filterData.state = null
            this.filterData.useType = null
            this.filterData.usePositioning = 1
            this.filterData.orderBy = 1
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 高级查询
        advancedQuery () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.pictureId
            })
            if (!this.selectedRows[0]) {
                let msg = num === 1 ? '请选择要发布的' + this.alertName : '请选择要取消发布的' + this.alertName
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = num === 1 ? '您确定要发布选中的' + this.alertName + '吗？' : '您确定要取消发布选中的' + this.alertName + '吗？'
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '发布成功', () => {
                                getList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '批量取消发布成功', () => {
                                getList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该' + this.alertName + '吗？', async () => {
                showLoading()
                del({ id: scope.row.pictureId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的' + this.alertName, () => {
                })
            }
            this.clientPop('info', '您确定要删除选中的' + this.alertName + '吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.pictureId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 上传图片
        async uploadImg (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            this.uploadLoading = true
            uploadFile(form).then(res => {
                // this.formData.pictureUrl = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                this.formData.pictureUrl = res[0].objectPath
                this.formData.pictureUrlId = res[0].recordId
                this.$message.success('上传成功')
            }).finally(() => {
                this.uploadLoading = false
            })
        },
        // 上传显示进度条
        handleUploadChange (file) {
            if (file.status === 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status === 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        handleClose () {
        },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = scope.row
            this.action = '编辑'
            this.pictureUrl = scope.row.pictureUrl
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if (!this.changedRow[0]) {
                return this.changedRow.push({ pictureId: row.pictureId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if (item.pictureId === row.pictureId) {
                    return i
                }
            })
            if (arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ pictureId: row.pictureId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                let msg = '当前没有排序值被修改！'
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '修改成功', () => {
                            this.changedRow = []
                            this.getTableData()
                        })
                    }
                })
            })
        },
        // 获取列表数据
        async getTableData () {
            this.isLoading = true
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message)
                }
                this.isLoading = false
                this.pages = res
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            showLoading()
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
            }).finally(() => hideLoading())
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs['mainTable'].toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
            this.pictureUrl = ''
        },
        // 保存新增/删除
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (!valid) return
                if (this.action === '编辑') {
                    return this.handleEditData()
                }
                this.handleCreateData()
            })
        },
        // 修改数据
        handleEditData () {
            showLoading()
            edit(this.formData).then(res => {
                if (res.message === '操作成功') {
                    this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            }).finally(() => hideLoading())
        },
        // 保持数据
        handleCreateData () {
            showLoading()
            create(this.formData).then(res => {
                if (res.message === '操作成功') {
                    this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            }).finally(() => hideLoading())
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-form-item.uploader {
    height: 200px;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

// upload
.avatar-uploader {
    /deep/ .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/ .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/ .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

/deep/ .el-dialog {
    height: 500px !important;
    padding: 0;

    .el-dialog__header {
        margin-bottom: 20px;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        background: red url(../../../../assets/test.png) no-repeat;

        .el-dialog__title {
            color: #fff;

        }
    }

    .el-dialog__body {
        height: 280px;
        margin-top: 100px;
    }

    .el-dialog__close.el-icon.el-icon-close::before {
        width: 44px;
        height: 44px;
    }
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
