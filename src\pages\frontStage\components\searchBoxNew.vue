<template>
    <div class="searchBox" ref="box">
        <div class="searchBox_top dfb">
            <div class="dfa">
                <div class="title">筛选条件：</div>
                <div class="checkItem" v-if="typeObj.classId">
                    {{ typeObj.classPath }} <i class="el-icon-close pointer" @click="typeObj.classId='';typeObj.classPath='';typeObj.classPath2=''"></i>
                </div>
                <div class="checkItem" v-for="(item, i) in checkedList" :key="i">
                    {{ item.label || item }} <i class="el-icon-close pointer" @click="deleteItem(item.value)"></i>
                </div>
                <span class="pointer" v-if="checkedList.length > 0" @click="resetSearch">清空已选条件</span>
            </div>
            <div class="searchBox_top_right pointer" @click="(showSearch = !showSearch)">
                {{ showSearch ? "收起筛选" : "展开筛选" }}
                <i class="el-icon-arrow-down" v-if="!showSearch"></i>
                <i class="el-icon-arrow-up" v-else></i>
            </div>
        </div>
        <div v-show="showSearch">
            <div class="title" style="width: 108px;line-height: 47px;text-align: center;display: inline-block;vertical-align: top;">分类</div>
            <ThreeType style="width: calc(100% - 110px);display: inline-block;vertical-align: top;"
                :typeObj="typeObj"
                @onTabChange="onTabChange"
                ></ThreeType>
        </div>
        <div class="row" v-for="(item, index) in form" :key="index" v-show="showSearch">
            <div class="dfa">
                <div class="title">{{ item.name }}</div>
                <div class="item_box_close" :ref="`row${index}`">
                    <div class="item"
                        :style="{ color: currentRows[`row${index + 1}`] == form[index].options[i] ? '#226fc7' : '' }" v-for="(item1, i) in item.options"
                        :key="i">
                        <div class="showPDiv" v-if="item1.options && item1.options.length" @mouseleave="() => {typeCurrent = typeOCurrent; showPop = false}">
                           <div @mouseenter="showPopMenu(i, $event)">
                              <template v-if="currentRows[`row${index + 1}`] && currentRows[`row${index + 1}`].type == item1.type && typeOCurrent == i"><div style="color: #226fc7">{{ currentRows[`row${index + 1}`].label || currentRows[`row${index + 1}`] }}<i class="el-icon-arrow-down"></i></div></template>
                              <template v-else>{{ item1.label || item1 }}<i class="el-icon-arrow-down"></i></template>
                           </div>
                           <div v-show="showPop && typeCurrent == i" class="showDiv" :style="{left: showOx +'px', top: showOj +'px', height: item1.options.length * 32 + 12 + 'px'}">
                                <div @click="checkItem(index, i, item3)" v-for="(item3, i3) in item1.options" :key="i3">{{ item3.label || item3 }}</div>
                           </div>
                        </div>
                        <div v-else @click="checkItem(index, i)">{{ item1.label || item1 }}</div>
                    </div>
                </div>
            </div>
            <div :ref="`more${index}`" class="row_right dfc pointer" @click="toggleFold(index)">
                更多 <i :ref="`icon${index}`" class="el-icon-arrow-down"></i>
                <!-- <i class="el-icon-arrow-down" v-show="!openStatus[`row${index + 1}`]"></i> -->
            </div>
        </div>
    </div>
</template>
<script>
import { nextTick } from 'process'
import ThreeType from '../mall/components/threeType.vue'
export default {
    name: 'searchBox',
    components: { ThreeType },
    data () {
        return {
            typeObj: { classId: '', classPath: '', classPath2: '' },
            typeCurrent: null, typeOCurrent: null, showOx: 0, showOj: 0,
            showPop: false,
            showSearch: true,
            currentRows: {},
            openStatus: {},
            closeStyle: {
                height: '48px',
                overflow: 'hidden',
            },
            openStyle: {
                height: 'unset',
                overflow: 'visible',
            },
            checkedList: this.list
        }
    },
    props: ['form', 'list'],
    watch: {
        checkedList: {
            handler (newVal) {
                let obj = {}
                this.form.forEach(item => {
                    newVal.forEach(item1 => {
                        if(item.type == item1.type) {
                            return obj[item.type] = item1
                        }
                    })
                })
                this.$emit('checkChange', obj, this.typeObj)
            }
        },
        form (val) {
            for (let i = 0; i < val.length; i++) {
                this.currentRows[`row${i + 1}`] = null
                this.openStatus[`row${i + 1}`] = false
                this.checkOverflow(i)
            }
        }
    },
    computed: {},
    methods: {
        // 鼠标悬浮展示弹框信息
        showPopMenu (i, event) {
            this.typeCurrent !== i ? this.showPop = true : this.showPop = !this.showPop
            this.typeCurrent = i
            if(this.showPop) {
                let element = event.currentTarget
                let rect = element.getBoundingClientRect()
                this.showOx = rect.left - 11
                this.showOj = rect.top + 47
            }
        },
        // 删除某一筛选条件
        deleteItem (value) {
            this.checkedList.forEach((item, i) => {
                if(item.value == value) {
                    this.checkedList.splice(i, 1)
                }
            })
            for(let key in this.currentRows) {
                if(this.currentRows[key] != null && this.currentRows[key].value == value) {
                    this.currentRows[key] = null
                }
            }
        },
        // 清空筛选条件列表
        resetSearch () {
            for (let key in this.currentRows) {
                this.currentRows[key] = null
            }
            this.checkedList = []
        },
        // 点击筛选条件
        checkItem (index, i, item3) {
            this.showPop = false
            for (let key in this.currentRows) {
                if (key == `row${index + 1}`) {
                    if(item3) {
                        this.typeCurrent = i
                        this.typeOCurrent = i
                        this.currentRows[key] = item3
                    }else {
                        this.currentRows[key] = this.form[index].options[i]
                    }
                }
            }
            this.getCheckList()
        },
        // 将筛选条件放入列表中
        getCheckList () {
            let arr = []
            this.form.forEach((item, i, array) => {
                if(this.typeCurrent && array[i].type == 'brand2') {
                    let num = array[i].options[this.typeCurrent].options.lastIndexOf(this.currentRows[`row${i + 1}`])
                    if (num > -1) {
                        arr.push(item.options[this.typeCurrent].options[num])
                    }else {
                        this.typeCurrent = null
                        this.typeOCurrent = null
                    }
                }else {
                    let num = array[i].options.lastIndexOf(this.currentRows[`row${i + 1}`])
                    if (num > 0) {
                        arr.push(item.options[num])
                    }
                }
            })
            this.checkedList = arr
        },
        // 展开选项
        toggleFold (i) {
            this.openStatus[`row${i + 1}`] = !this.openStatus[`row${i + 1}`]
            let iconDom = this.$refs[`icon${i}`][0]
            let rowDom = this.$refs[`row${i}`][0]
            if (this.openStatus[`row${i + 1}`]) {
                iconDom.className = 'el-icon-arrow-up'
                rowDom.className = 'item_box_open'
                return
            }
            iconDom.className = 'el-icon-arrow-down'
            rowDom.className = 'item_box_close'
        },
        // 判断内容是否超过一行
        checkOverflow (i) {
            nextTick(() => {
                let el = this.$refs[`row${i}`][0]
                let childWidth = 0
                el.childNodes.forEach(item => {
                    childWidth += item.clientWidth + 36
                })
                // 如果不超过一行则不隐藏
                if (childWidth > this.$refs['box'].clientWidth - 108) return
                this.$refs[`more${i}`][0].style.display = 'none'
            })
        },
        // 切换tab时
        onTabChange (typeObj) {
            this.typeObj = typeObj
            let obj = {}
            this.form.forEach(item => {
                this.checkedList.forEach(item1 => {
                    if(item.type == item1.type) {
                        return obj[item.type] = item1
                    }
                })
            })
            this.$emit('checkChange', obj, this.typeObj)
        },
    },
}
</script>
<style scoped lang="scss">
.searchBox {
    width: 100%;
    //height: 254px;

    &>div {
        background-color: #fff;
    }

    .searchBox_top {
        height: 48px;

        .title {
            width: 108px;
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
        }

        span {
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
        }

        .checkItem {
            font-size: 14px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            padding: 6px 12px;
            background: rgba(250, 250, 250, 1);
            border: 1px solid rgba(229, 229, 229, 1);
            margin-right: 10px;
        }

        .searchBox_top_right {
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
            padding-right: 20px;
            user-select: none;
        }
    }

    & .row:last-child {
        .title {
            border-bottom: none;
        }
    }

    .row {
        height: unset;
        display: flex;
        align-items: baseline;
        border-top: 1px solid rgba(229, 229, 229, 1);

        &>.dfa {
            height: 100%;
            position: relative;
            padding-left: 108px;
        }

        .title {
            width: 108px;
            height: inherit;
            padding-top: 16px;
            opacity: 1;
            background: rgba(250, 250, 250, 1);
            font-size: 14px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            // display: flex;
            text-align: center;
            // justify-content: center;
            position: absolute;
            left: 0;
        }

        .item_box_open {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        .item_box_close {
            height: 47px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            overflow: hidden;
        }

        .item {
            height: 47px;
            font-size: 14px;
            font-weight: 400;
            line-height: 48px;
            color: rgba(51, 51, 51, 1);
            margin: 0 18px;
            cursor: pointer;
        }

        .row_right {
            width: 60px;
            // height: 28px;
            height: inherit !important;
            opacity: 1;
            background: rgba(255, 255, 255, 1);
            margin-right: 20px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
            user-select: none;
        }
    }

    .filterBox_top {
        width: 100%;
        height: 48px;
        padding-left: 20px;
        background-color: #fff;

        /deep/ .el-checkbox {
            margin-right: 20px;

            .el-checkbox__inner {
                border-radius: 0;
                border: 1px solid rgba(204, 204, 204, 1);
            }

            .el-checkbox__label {
                padding-left: 8px;
            }
        }

        .sortBtn {
            height: 26px;
            padding: 0 9px 0 10px;
            line-height: 26px;
            border: 1px solid rgba(229, 229, 229, 1);

            span {
                margin-right: 8px;
            }
        }

        .sortPrice {
            border-right: 0;
        }

        .sortTime {
            color: rgba(212, 48, 48, 1);
            border-color: rgba(212, 48, 48, 1);
        }
    }

    .filterBox_bottom {
        padding-left: 20px;

        .mailAddress {
            width: 196px;
            height: 26px;
            margin-right: 12px;
            color: rgba(51, 51, 51, 1);
            border: 1px solid rgba(229, 229, 229, 1);

            img {
                margin: 0 7px;
            }
        }

        .priceFilter {
            width: 94px;
            height: 26px;
            padding-left: 8px;
            color: rgba(102, 102, 102, 1);
            border: 1px solid rgba(229, 229, 229, 1);

            input {
                width: 66px;
                height: 24px;
                margin-left: 4px;
                border: none;
            }
        }

        .bar {
            width: 10px;
            height: 1px;
            margin: 8px;
            background-color: rgba(229, 229, 229, 1);
        }

        .searchInput {
            margin-left: 12px;

            &>div:first-child {
                width: 196px;
                height: 26px;
                padding-left: 8px;
                border: 1px solid rgba(229, 229, 229, 1);

                input {
                    width: 104px;
                    height: 21px;
                    margin-left: 4px;

                    &::placeholder {
                        color: rgba(204, 204, 204, 1);
                    }
                }
            }

            button {
                width: 52px;
                height: 26px;
                font-size: 14px;
                font-weight: 400;
                color: rgba(255, 255, 255, 1);
                background: rgba(212, 48, 48, 1);
            }
        }
    }
}
.showPDiv {position: relative;z-index: 2;}
.showDiv {
    position: fixed;top: 40px;left: 0;z-index: 2;max-height: 200px;width: 120px;overflow: auto;
    background: #fff;border: 1px solid #ccc;line-height: 32px;padding: 5px 10px;color: #333333;
}
</style>