<template>
    <!-- <div
        v-loading="loading"
        style="height: calc(100% - 10px)"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.7)"
    >
        <div>
            <p>{{!userInfo.userId ? '登录中...' : ''}}</p>
            login page
            <el-button @click="goIndex">index</el-button>
            <el-button @click="goList">list</el-button>
        </div>
    </div> -->
    <div class="login-page">
        <div class="login">
            <img src="@/assets/loginBg.png" class="loginBg">
            <div class="login-content">
                <el-tabs class="tabs" v-model="activeName" @tab-click="handleClick" stretch>
                    <el-tab-pane label="账户密码登录" name="first">
                        <div>
                            <el-form ref="userLogin" :model="userInfo" :rules="userRules">
                                <el-form-item prop="account">
                                    <el-input v-model="userInfo.account" placeholder="请输入账号"></el-input>
                                </el-form-item>
                                <el-form-item prop="password">
                                    <el-input v-if="visible" type="password" v-model="userInfo.password" placeholder="请输入密码" ref="password">
                                        <i slot="suffix"  class="el-icon-view hide" @click="changePass('show')"></i>
                                    </el-input>
                                    <el-input v-else type="text" v-model="userInfo.password" placeholder="请输入密码" ref="password">
                                        <i slot="suffix"  class="el-icon-view show" @click="changePass('hide')"></i>
                                    </el-input>
                                </el-form-item>
                            </el-form>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="验证码登录" name="second">
                        <div>
                            <el-form  :model="authCode" :rules="authCodeRules">
                                <el-form-item prop="phone">
                                    <el-input v-model="authCode.phone" placeholder="请输入手机号码"></el-input>
                                </el-form-item>
                                <el-form-item prop="code">
                                    <el-input v-model="authCode.code"  placeholder="请输入验证码"></el-input>
                                </el-form-item>
                            </el-form>
                        </div>
                    </el-tab-pane>
                </el-tabs>
                <div class="login-password" v-if="activeName ==='first'">
                    <div class="password-isRemember"
                    >
                        <el-radio
                            v-model="isRemember"
                            :label="true"
                            @click.native.prevent="radioChange(true)"
                            >记住密码</el-radio
                        >
                    </div>
                    <div class="password-forget">
                        忘记密码?
                    </div>
                </div>
                <div class="login-button">
                    <el-button class="btn-blue" @click="onLogin" @keyup.enter.native="enterLogin()">登录</el-button>
                </div>
                <div class="login-scan">
                    <img src="@/assets/scan_code.png" />
                    <span>扫码登录</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// import md5 from 'js-md5'
import { mapActions, mapState } from 'vuex'
const Base64 = require('js-base64').Base64
export default {
    props: ['user'],
    computed: {
        ...mapState(['passInfo'])
    },
    data () {
        return {
            loading: false,
            activeName: 'first',
            userInfo: {
                account: '1',
                password: '',
            },
            userRules: {
                account: [
                    { required: true, message: '请输入账号', trigger: 'blur' },
                ],
                password: [
                    { required: true, message: '清输入密码', trigger: 'blur' },
                ],
            },
            authCode: {
                phone: '',
                code: '',
            },
            authCodeRules: {},
            isRemember: false,
            timer: null,
            visible: true
        }
    },
    created () {
    // 在页面加载时从cookie获取登录信息
        let account = this.getCookie('account')
        let password = Base64.decode(this.getCookie('password'))
        // 如果存在赋值给表单，并且将记住密码勾选
        if(account) {
            this.userInfo.account = account
            this.userInfo.password = password
            this.isRemember = true
        }
        this.enterLogin()
    },
    methods: {
        ...mapActions(['getToken']),
        goIndex () {
            this.$router.replace('/')
        },
        goList () {
            this.$router.replace('/list')
        },
        handleClick (tab, event) {
            console.log(tab, event)
        },
        radioChange (e) {
            // 当点击已经选中的把 isRemember 置空，就是取消选中，并返回
            if (this.isRemember === e) {
                this.isRemember = false
                return
            }
            // 不是选中，选中当前点击 Radio
            this.isRemember = e
            // 选中操作
        },
        onLogin () {
            if(this.activeName === 'first') {
                this.$refs.userLogin.validate(valid=>{
                    if(valid) {
                        this.getToken(this.userInfo).then(()=>{
                            // 储存登录信息
                            this.setUserInfo()
                            // 跳转到首页
                            this.$router.push('/')
                        }).catch(err => {
                            console.log('🚀 ~ err', err)
                        })
                    }
                })
            }
        },
        enterLogin () {
            document.onkeydown = e => {
                e = window.event || e
                if(this.$route.path == '/login' && (e.key == 'Enter' || e.key == 'enter')) {
                    //调用登录事件方法
                    if(!this.timer) {
                        this.onLogin()
                        this.timer = setTimeout(() => {
                            clearTimeout(this.timer)
                            this.timer = null
                        }, 3000)
                    }
                }
            }
        },
        // 储存表单信息
        setUserInfo: function () {
            // 判断用户是否勾选记住密码，如果勾选，向cookie中储存登录信息，
            // 如果没有勾选，储存的信息为空
            if(this.isRemember) {
                this.setCookie('account', this.userInfo.account)
                // base64加密密码
                let passWord = Base64.encode(this.userInfo.password)
                this.setCookie('password', passWord)
            }else{
                this.setCookie('account', '')
                this.setCookie('password', '')
            }
        },
        // 获取cookie
        getCookie: function (key) {
            if (document.cookie.length > 0) {
                let start = document.cookie.indexOf(key + '=')
                if (start !== -1) {
                    start = start + key.length + 1
                    let end = document.cookie.indexOf(';', start)
                    if (end === -1) end = document.cookie.length
                    return unescape(document.cookie.substring(start, end))
                }
            }
            return ''
        },
        // 保存cookie
        setCookie: function (cName, value, expiredays) {
            var exdate = new Date()
            exdate.setDate(exdate.getDate() + expiredays)
            document.cookie = cName + '=' + decodeURIComponent(value) +
            ((expiredays == null) ? '' : ';expires=' + exdate.toGMTString())
        },
        changePass (value) {
            this.visible = !(value === 'show') //切换密码框的显示
            this.$nextTick(function () {//对焦密码框
                this.$refs['password'].focus()
            })
        }
    }
}
</script>
<style lang="scss" scoped>
    .login-page{
        background: #2b2d4a;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-width: 900px;
        .login{
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fff;
            width: 75%;
            border-radius: 4%;
            .loginBg{
                width: 50%;
                align-self: stretch;
            }
            .login-content{
                width: 50%;
                align-self: stretch;
                align-items: center;
                padding: 10%;
                .tabs{
                    width:100%;
                    /deep/ .el-tabs__item{
                        font-size: 16px;
                    }
                    .el-form{
                        padding-top: 10%;
                        /deep/ input.el-input__inner{
                            border: none;
                            border-bottom: 2px solid #f4f5f7;
                            padding: 18px 8px;
                            font-size: 16px;
                            &::-webkit-input-placeholder{
                                color: #888;
                            }
                        }
                        /deep/ .el-form-item.is-error .el-input__inner, .el-form-item.is-error .el-input__inner:focus, .el-form-item.is-error .el-textarea__inner, .el-form-item.is-error .el-textarea__inner:focus, .el-message-box__input input.invalid, .el-message-box__input input.invalid:focus {
                            border: 1px solid #F56C6C;
                        }
                        .el-icon-view{
                            cursor: pointer;
                            font-size: 14px;
                            &.show{
                                color: #2e61d7;
                            }
                        }
                    }

                }
                .login-password{
                    display: flex;
                    justify-content: space-between;
                    margin-top: 5%;
                    .password-forget{
                        color: #6b89df;
                        cursor: pointer;
                    }
                }
                .login-button{
                    width: 100%;
                    margin-top:5%;
                    .btn-blue{
                        width: 100%;
                        padding: 20px 0;
                        font-size: 18px;
                        background: #2e61d7;
                    }
                }
                .login-scan{
                    margin-top: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    img{
                        width: 30px;
                    }
                    span{
                        margin-left: 15px;
                        font-size: 14px;
                        color: #888888;
                    }
                }
            }
        }
    }
    /deep/ .el-tabs__nav-wrap::after {
        z-index: 1;
    }
    /deep/ .el-tabs__item.is-active{
        color: #2e61d7;
    }
    /deep/ .el-tabs__active-bar{
        height: 4px;
        background-color: #2e61d7;
    }
    /deep/ .el-radio{
        color: #888888;
    }
</style>
