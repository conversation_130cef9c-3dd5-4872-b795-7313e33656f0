<template>
    <div class="content p20">
        <div class="top-row mb10 p10 dfb">
            <div class="dfa">
                <el-button type="info"><i class="el-icon-refresh" @click="getList"></i></el-button>
                <el-button type="primary" @click="openSave">新增</el-button>
                <el-button type="primary" @click="changeSortValue">批量修改排序值</el-button>
                <!--                <el-button type="danger">批量删除</el-button>-->
                <el-select class="ml10" @change="stateTopOptionsClick(stateOptionTitle)" v-model="stateOptionTitle"
                           placeholder="请选择平台">
                    <el-option v-for="item in stateOptions" :key="item.value" :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="dfa">
                <el-input clearable type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字"
                          v-model="keywords">
                    <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                </el-input>
                <div class="adverse">
                    <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                </div>
            </div>
        </div>

        <el-table
            :data="tableData"
            row-key="menuId"
            v-loading="tableLoading"
            :height="tableHeight"
            lazy
            :load="loadChildren"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
            <el-table-column type="selection"></el-table-column>
            <el-table-column label="菜单名称" prop="title"></el-table-column>
            <el-table-column label="菜单编号" prop="code"></el-table-column>
            <el-table-column label="类型" prop="type">
                <template v-slot="scope">
                    <el-tag v-if="scope.row.type==1">菜单</el-tag>
                    <el-tag v-if="scope.row.type==2">按钮</el-tag>
                    <el-tag v-if="scope.row.type==3">资源</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否测试" prop="type">
                <template v-slot="scope">
                    <el-switch
                        v-model="scope.row.showDev"
                        active-color="#13ce66"
                        inactive-color="#ff4949"
                        :active-value="1"
                        :inactive-value="0"
                        @change="updateShowDevM(scope.row)"
                    ></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="所属平台" prop="classCode">
                <template v-slot="scope">
                    <el-tag v-if="scope.row.classCode==1">后台管理平台</el-tag>
                    <el-tag v-if="scope.row.classCode==2">供应商管理平台</el-tag>
                    <el-tag v-if="scope.row.classCode==3">履约管理管理平台</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="状态">
                <template v-slot="scope">
                    <el-switch
                        v-model="scope.row.state"
                        active-color="#13ce66"
                        inactive-color="#ff4949"
                        :active-value="1"
                        :inactive-value="0"
                        @change="updateStateM(scope.row)"
                    ></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="排序值" width="120" type="index">
                <template v-slot="scope">
                    <el-input type="number" v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="gmtCreate"></el-table-column>
            <el-table-column label="修改时间" prop="gmtModified"></el-table-column>
            <el-table-column label="操作">
                <template v-slot="scope">
                    <!--                    <el-button type="primary"   circle @click="editRow(scope.row)" icon="el-icon-edit"> </el-button>-->
                    <i class="el-icon-edit pointer iconStyle" @click="editRow(scope.row)"></i>
                    <i class="el-icon-delete-solid pointer iconStyle" @click="delRow(scope.row)"></i>
                    <i class="el-icon-plus pointer iconStyle" @click="openSonSave(scope.row)"></i>
                    <!--                    <el-button type="danger" circle  @click="delRow(scope.row)" icon="el-icon-delete"></el-button>-->
                    <!--                    <el-button type="danger" circle  @click="delRow(scope.row)" icon="el-icon-delete"></el-button>-->
                    <!--                    <el-button type="primary" circle  icon="el-icon-plus" @click="openSonSave(scope.row)"></el-button>-->
                </template>
            </el-table-column>
        </el-table>

        <!--        <el-pagination-->
        <!--            background-->
        <!--            layout="prev, pager, next"-->
        <!--            :current-page.sync="page.currPage"-->
        <!--            :total="page.totalCount"-->
        <!--            :page-size="20"-->
        <!--        />-->

        <el-dialog :title="title" :visible.sync="editDialogVisible">
            <el-form label-position="right" label-width="120px">
                <el-row>
                    <el-form-item label="上级菜单：">
                        <el-input disabled v-model="formData.parentMenuTitle"/>
                        <!--                        <el-select v-model="formData.parentMenuTitle">-->
                        <!--&lt;!&ndash;                            <el-option :label="formData.parentMenuTitle"  :key="formData.parentMenuId"/>&ndash;&gt;-->
                        <!--                        </el-select>-->
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="菜单名称：">
                        <el-input v-model="formData.title"/>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="菜单编号：">
                        <el-input v-model="formData.code"/>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="菜单路径：">
                        <el-input v-model="formData.pathUrl"/>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="菜单类型：">
                        <el-radio v-model="formData.type" :label="1" border>菜单</el-radio>
                        <el-radio v-model="formData.type" :label="2" border>按钮</el-radio>
                        <el-radio v-model="formData.type" :label="3" border>资源</el-radio>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="是否测试：">
                        <el-switch
                            v-model="formData.showDev"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="排序：">
                        <el-input-number v-model="formData.sort"/>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="规则备注：">
                        <el-input type="textarea" v-model="formData.remarks"/>
                    </el-form-item>
                </el-row>

                <el-row>
                    <el-form-item label="状态：">
                        <el-switch
                            v-model="formData.state"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            :active-value="1"
                            :inactive-value="0"
                        ></el-switch>
                    </el-form-item>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button @click="editDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveAndupdateM">确定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="false">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="菜单名称：">
                            <el-input v-model="filterData.title" placeholder="请输入菜单名称" clearable/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="菜单编号：">
                            <el-input v-model="filterData.code" placeholder="请输入菜单编号" clearable/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { updateState, updateShowDev, deleteById, updateMune, getDateList, save, selectParentMenus, changeSortValue } from '@/api/cloudCenter/menu.js'

export default {
    title: 'menuManage',
    data () {
        return {
            tableLoading: false,
            stateOptionTitle: 1,
            menuLevel: 1,
            menuLevelOptions: [
                {
                    value: 1,
                    label: '一级'
                }, {
                    value: 2,
                    label: '二级'
                }, {
                    value: 3,
                    label: '三级'
                },
            ],
            queryVisible: false,
            keywords: null,
            stateOptions: [
                {
                    value: 1,
                    label: '后台管理平台'
                }, {
                    value: 2,
                    label: '供应商管理平台'
                }, {
                    value: 3,
                    label: '履约管理管理平台'
                }, {
                    value: 4,
                    label: '自营店供应商'

                }
            ],
            editDialogVisible: false,
            title: '',
            view: 'add',
            saveDialogVisible: false,
            tableData: [
                // { menuId: 1, title: 1, children: [{ menuId: 2, title: 2 }] }
            ],
            children: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            parentMenuOptions: [],
            changedRow: [],
            formData: {
                showDev: 0,
                parentMenuTitle: null,
                parentMenuId: null,
                code: '',
                level: 1,
                classCode: 1,
                pathUrl: null,
                type: '',
                title: '',
                remarks: '',
                sort: 0,
                state: 1
            },
            filterData: {
                parentMenuId: 1,
                classCode: 1,
                code: '',
                type: '',
                title: '',
                remarks: '',
                rank: 1,
                state: 0
            },
            page: {
                currPage: 1,
                totalCount: 40
            },
            screenHeight: 0,
        }
    },
    computed: {
        tableHeight () {
            return this.screenHeight - 210
        },
    },
    methods: {
        loadChildren (tree, treeNode, resolve) {
            setTimeout(() => {
                resolve([])
            }, 1000)
        },
        stateTopOptionsClick (stateOptionTitle) {
            this.filterData.classCode = stateOptionTitle
            this.getTableDataM()
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableDataM()
        },
        confirmSearch () {
            this.keywords = null
            this.getTableDataM()
            this.queryVisible = false
        },
        resetSearchConditions () {
            this.filterData.title = null // 以下价格
            this.filterData.code = null // 以下价格
            this.filterData.type = null // 以下价格
            this.filterData.remarks = null // 以下价格
            this.filterData.state = null // 以下价格
        },
        openSave () {
            this.editDialogVisible = true
            this.formData.parentMenuId = '',
            this.formData.parentMenuTitle = '',
            this.title = '新增菜单'
            this.view = 'add'
        },
        saveAndupdateM () {
            this.formData.classCode = this.stateOptionTitle
            if (this.view === 'add') {
                save(this.formData).then(res => {
                    console.log('save', res)
                    if (res.code == 200) {
                        this.$message.success('保存成功')
                        this.editDialogVisible = false
                        this.getTableDataM()
                    }
                })
            } else {
                this.clientPop('info', '您确定要修改该菜单吗？', async () => {
                    this.tableLoading = true
                    updateMune(this.formData).then(res => {
                        if (res.code === 200) {
                            this.$message.success('修改成功')
                        }
                    }).finally(() => {
                        this.editDialogVisible = false
                        this.getTableDataM()
                        this.tableLoading = false
                    }
                    )
                })

            }

        },

        getParentMenuOptions () {
            let params = {
                level: this.formData.level,
                classCode: this.formData.classCode
            }
            selectParentMenus(params).then(res => {
                this.parentMenuOptions = res.list
            })
        },
        //查询数据
        getTableDataM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.classCode != null && this.filterData.classCode != '') {
                params.classCode = this.filterData.classCode
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.title != null && this.filterData.title != '') {
                params.title = this.filterData.title
            }

            if (this.filterData.level != null && this.filterData.level != '') {
                params.level = this.filterData.level
            }
            if (this.filterData.state != null && this.filterData.state != '') {
                params.state = this.filterData.state
            }
            if (this.filterData.type != null && this.filterData.type != '') {
                params.type = this.filterData.type
            }
            this.tableLoading = true
            getDateList(params).then(res => {
                this.tableData = res
            })
            this.tableLoading = false
        },

        del (menuId) {
            this.tableLoading = true
            deleteById({ id: menuId }).then(res => {
                if (res.code === 200) {
                    this.clientPop('suc', res.message, () => {
                        this.getDateList()
                    })

                }
                this.tableLoading = false
            })
        },
        getList () {
            this.tableLoading = true
            setTimeout(() => {
                this.getTableDataM()
                this.tableLoading = false
            }, 200)
        },

        getChangedRow (row) {
            if(!this.changedRow[0]) {
                return this.changedRow.push({ menuId: row.menuId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if(item.menuId === row.menuId) {
                    return i
                }
            })
            if(arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ menuId: row.menuId, sort: parseInt(row.sort) })
            console.log('this.changedRow', this.changedRow)
        },
        // 修改排序值
        changeSortValue () {
            if (!this.changedRow[0]) {
                this.$message.info('当前没有排序值被修改')
                return
            }
            let warnMsg = '您确定要修改‘这些’的排序值吗？'
            this.clientPop('info', warnMsg, async () => {
                changeSortValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('修改成功')
                        this.getTableDataM()
                    }
                })
            })
        },
        editRow (row) {
            this.editDialogVisible = true
            this.formData = row
            this.title = '编辑角色'
            this.view = 'update'
        },

        //修改订单状态
        updateStateM (row) {
            let params = {
                id: row.menuId,
                state: row.state
            }
            updateState(params).then(res => {
                if (res.code == 200) {
                    this.$message.success('修改状态成功')
                    this.getTableDataM()
                }
            })
        },
        updateShowDevM (row) {
            let params = {
                id: row.menuId,
                state: row.showDev
            }
            updateShowDev(params).then(res => {
                if (res.code == 200) {
                    this.$message.success('修改状态成功')
                    this.getTableDataM()
                }
            })
        },
        openSonSave (row) {
            this.editDialogVisible = true
            this.view = 'add'
            this.formData = {
                parentMenuId: row.menuId,
                parentMenuTitle: row.title,
                state: 1,
                level: row.level + 1,
                classCode: row.classCode
            }
            this.title = '新增'
        },
        delRow (row) {
            this.clientPop('info', `您确定要删除该${row.title}吗？`, async () => {
                this.tableLoading = true
                deleteById({ id: row.menuId }).then(res => {
                    if (res.code == 200) {
                        this.$message.success('删除成功')
                        this.getTableDataM()
                    } else {
                        this.$message.warning('删除失败')
                    }

                })
            }).finally(() => {
                this.tableLoading = false
            })
        },
        getScreenSize () {
            this.screenHeight = document.documentElement.clientHeight
        },
    },
    created () {
        this.getTableDataM()
        this.getParentMenuOptions()
    },
    mounted () {
        this.getScreenSize()
        window.addEventListener('resize', () => this.getScreenSize())
    },
    beforeDestroy () {
        window.removeEventListener('resize', () => this.getScreenSize())
    },
}
</script>

<style scoped lang="scss">
.top-row {
    //height: 50px;
    padding: 10px;
    background-color: #fff;
}

/deep/ .el-table {
    .el-icon-delete {
        font-size: 16px;
        color: #fff;
    }
}

.iconStyle {
    margin-right: 20px;
}
</style>