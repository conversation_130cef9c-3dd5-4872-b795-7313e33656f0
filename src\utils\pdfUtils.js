import * as pdfjsLib from 'pdfjs-dist/es5/build/pdf'
import pdfWorker from 'pdfjs-dist/es5/build/pdf.worker.entry'

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfWorker
pdfjsLib.GlobalWorkerOptions.isEvalSupported = false

/**
 * 生成 PDF 第一页的缩略图数据 URL。
 * @param {string | Uint8Array | ArrayBuffer | Blob} pdfSource PDF 文件的 URL 或二进制数据。
 * @param {number} scale 缩略图渲染比例。
 * @returns {Promise<string>} 缩略图的 Data URL (image/png)。
 */
export async function generatePdfThumbnailDataUrl (pdfSource, scale = 1) {
    let pdf = null
    try {
        const arrayBuffer = await pdfSource.arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)
        pdf = await pdfjsLib.getDocument({ data: uint8Array }).promise
        const page = await pdf.getPage(1) // 获取第一页
        const viewport = page.getViewport({ scale })
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        canvas.height = viewport.height
        canvas.width = viewport.width

        await page.render({ canvasContext: context, viewport: viewport }).promise

        return canvas.toDataURL('image/png')
    } finally {
        if (pdf) {
            pdf.destroy() // 销毁PDF文档对象，释放资源
        }
    }
}

/**
 * 销毁 PDF 文档对象，释放其占用的资源。
 * @param {Object} pdfDocument PDF 文档对象。
 */
export function destroyPdfDocument (pdfDocument) {
    if (pdfDocument && typeof pdfDocument.destroy === 'function') {
        pdfDocument.destroy()
    }
}
