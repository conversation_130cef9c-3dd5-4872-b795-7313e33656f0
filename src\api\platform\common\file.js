import service from '@/utils/request'

const { httpPost, httpGet } = service

const uploadFile = params => {
    return httpPost({
        url: '/oss/uploader1',
        params,
        headers: {
            'Content-Type': 'multipart/form-data',
        }
    })
}
// 图片浏览
const previewFile = params => {
    return httpGet({
        url: '/oss/downloader',
        params,
        responseType: 'blob',
        headers: {
            org: '%7B%22orgId%22%3A%22ad551eff9d03-8efe-2148-9ed8-64781e1e%22%2C%22orgName%22%3A%22%E5%9B%9B%E5%B7%9D%E8%B7%AF%E6%A1%A5%E5%BB%BA%E8%AE%BE%E9%9B%86%E5%9B%A2%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%22%2C%22shortCode%22%3A%22SRBC%22%2C%22orgType%22%3A1%7D',
        }
    })
}

const thumbnailImage = params => {
    return httpGet({
        url: '/oss/thumbnail',
        params,
        responseType: 'blob',
        headers: {
            org: '%7B%22orgId%22%3A%22ad551eff9d03-8efe-2148-9ed8-64781e1e%22%2C%22orgName%22%3A%22%E5%9B%9B%E5%B7%9D%E8%B7%AF%E6%A1%A5%E5%BB%BA%E8%AE%BE%E9%9B%86%E5%9B%A2%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%22%2C%22shortCode%22%3A%22SRBC%22%2C%22orgType%22%3A1%7D',
        }
    })
}
const privateUploadFile = params => {
    return httpPost({
        url: '/oss/uploader1',
        params,
        headers: {
            'Content-Type': 'multipart/form-data',
            token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VybnVtYmVyIjoiMDI0NjMxIiwiZXhwIjoxNjg4MDA1ODgzLCJ1c2VyaWQiOiJhYmI1MTg0ZDQzY2EtOTYwYy1iNjQwLWMzNmMtNDU0YTEwZjQiLCJ1c2VybmFtZSI6IlBDV1DmtYvor5UifQ.VhZWX4nRQiXGYWvcTDTKIaDLr8pRMe-Fm9BiphwfOuk'
        }
    })
}
const postN = params => {
    return httpGet({
        url: '/materialMall/platform/adPicture/findByConditionByPage',
        params
    })
}

export {
    uploadFile,
    postN,
    privateUploadFile,
    previewFile,
    thumbnailImage
}