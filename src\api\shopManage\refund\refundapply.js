import service from '@/utils/request'

const { httpPost, httpGet } = service

//查询退货订单

const getOrderReturnDateById = params => {
    return httpGet({
        url: '/materialMall/orderReturn/findById',
        params
    })
}
const shopMangeOrderReturnList = params => {
    return httpPost({
        url: '/materialMall/shopManage/orderReturn/listByEntity',
        params
    })
}
const shopMangeTwoOrderReturnList = params => {
    return httpPost({
        url: '/materialMall/shopManage/orderReturn/twoListByEntity',
        params
    })
}
const updateOrderReturnState = params => {
    return httpPost({
        url: '/materialMall/orderReturn/updateState',
        params
    })
}

const updateTwoOrderItemState = params => {
    return httpPost({
        url: '/materialMall/orderReturn/updateTwoOrderItemState',
        params
    })
}
const getOrderItemListByOrderReturmId = params => {
    return httpGet({
        url: '/materialMall/orderReturn/getOrderItemListByOrderReturnId',
        params
    })
}
const getListByOrderReturnId = params => {
    return httpGet({
        url: '/materialMall/orderReturnItem/getListByOrderReturnId',
        params
    })
}
const getOrderItemTwoListByOrderReturnId = params => {
    return httpGet({
        url: '/materialMall/orderReturn/getOrderItemTwoListByOrderReturnId',
        params
    })
}

export {
    getOrderReturnDateById,
    shopMangeOrderReturnList,
    shopMangeTwoOrderReturnList,
    updateOrderReturnState,
    getOrderItemListByOrderReturmId,
    getOrderItemTwoListByOrderReturnId,
    updateTwoOrderItemState,
    getListByOrderReturnId
}