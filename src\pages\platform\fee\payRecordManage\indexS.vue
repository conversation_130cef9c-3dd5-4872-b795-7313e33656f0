<template>
    <div class="base-page">
        <div class="right" v-loading="contentLoading">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <!-- <el-button type="primary2" @click="onDownload" class="btn-blue">下载导入缴费模版</el-button>
                            <importFeeExcel></importFeeExcel> -->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-dropdown @command="handleChangeSort" trigger="click" placement="bottom">
                        <span class="pointer">
                            排序方式<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="0" :style="{ color: filterData.orderBy == 1 ? '#2e61d7' : '' }">
                                    按缴费时间排序
                                </el-dropdown-item>
                                <el-dropdown-item :command="1" :style="{ color: filterData.orderBy == 2 ? '#2e61d7' : '' }">
                                    按修改时间排序
                                </el-dropdown-item>
                                <el-dropdown-item :command="2" :style="{ color: filterData.orderBy == 3 ? '#2e61d7' : '' }">
                                    按审核时间排序
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                        <el-input v-model="keywords" clearable placeholder="输入搜索关键字" style="width: 300px;margin-left: 20px" type="text" @blur="handleInputSearch">
                            <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    ref="tableRef"
                    v-loading="tableLoading"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                    class="table"
                    @selection-change="tableSelectM"
                    @row-click="tableRowClickM"
                >
<!--                    <el-table-column type="selection" width="40"></el-table-column>-->
                    <el-table-column label="序号" prop="index" width="60"></el-table-column>
                    <el-table-column label="编号" prop="paymentRecordUn" width="200">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.paymentRecordUn }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业名称" prop="enterpriseName" width=""/>
                    <el-table-column label="服务类型" prop="serviceTypeName" width="150">
                        <template v-slot="scope">
                            <el-tag>{{ scope.row.serviceTypeName }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺名称" prop="shopName" width=""/>
                    <el-table-column label="缴费方式" prop="payTypeName" width="100">
                        <template v-slot="scope">
                            {{ scope.row.payTypeName }}
                        </template>
                    </el-table-column>
                    <el-table-column label="缴费时长" prop="paymentDuration" width="100"/>
                    <el-table-column label="缴费时长单位" prop="paymentDurationTypeName" width="100">
                        <template v-slot="scope">
                            {{ scope.row.paymentDurationTypeName }}
                        </template>
                    </el-table-column>
                    <el-table-column label="缴费金额（元）" prop="payAmount" width="100">
                        <template v-slot="scope">
                            <span style="color: red">{{scope.row.payAmount}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="stateName" width="100">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">{{ scope.row.stateName }}</el-tag>
                            <el-tag v-if="scope.row.state == 1">{{ scope.row.stateName }}</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 2 || scope.row.state == 5">{{ scope.row.stateName }}</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 3 || scope.row.state == 6">{{ scope.row.stateName }}</el-tag>
                            <el-tag v-if="scope.row.state == 4">{{ scope.row.stateName }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="审核时间" prop="auditOpenTime" width="160"/>
                    <el-table-column label="缴费时间" prop="gmtCreate" width="160"/>
                    <el-table-column label="修改时间" prop="gmtModified" width="160"/>
                </el-table>
            </div>
            <Pagination
                v-show="tableData && tableData.length > 0"
                :currentPage.sync="paginationInfo.currentPage"
                :pageSize.sync="paginationInfo.pageSize"
                :total="paginationInfo.total"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费编号：">
                            <el-input clearable maxlength="100" placeholder="请输入缴费编号" v-model="filterData.paymentRecordUn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="企业名称：">
                            <el-input clearable maxlength="100" placeholder="请输入企业名称" v-model="filterData.enterpriseName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺名称：">
                            <el-input clearable maxlength="100" placeholder="请输入店铺名称" v-model="filterData.shopName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                下拉框方式选择状态-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="服务类型：">
                            <el-select v-model="filterData.recordType" placeholder="请选择服务类型">
                                <el-option
                                    v-for="item in filterData.recordTypeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="状态：">
                            <div style="display:flex">
                                <el-checkbox
                                    v-model="filterData.stateCheckAll" @change="stateAllSelect"
                                >全部
                                </el-checkbox>
                                <el-checkbox-group
                                    style="margin-left: 30px" v-model="filterData.states" @change="stateGroupChange">
                                    <el-checkbox
                                        v-for="option in filterData.stateSelect"
                                        :key="option.value"
                                        :label="option.value"
                                    >{{ option.label }}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.gmtCreate"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="修改时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.gmtModified"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="审核时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.auditOpenTime"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { debounce, hideLoading, showLoading, toFixed } from '@/utils/common'
// import importFeeExcel from '@/components/importFeeExcel.vue'
// eslint-disable-next-line no-unused-vars
import { supplierCreateFee, feeManagementList } from '@/api/fee/feeApi'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { mapState } from 'vuex'
import { feeInputTemplate } from '@/api/platform/product/shudaoEnterprise'

export default {
    components: {
        Pagination,
        // importFeeExcel
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState([ 'userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292 - 40
        },
    },
    data () {
        return {
            dialogImageUrl: '',
            dialogVisible: false,
            freeAmount: 2000,
            showAddFee: false,
            showAddFeeLoading: false,
            addFeeForm: {
                state: 0,
                payType: 1,
                recordType: 1,
                paymentDurationType: 4,
                paymentDuration: 0,
                files: [],
                payAmount: 0,
                remarks: null,
            },
            showAddFeeRoles: {
                receiverName: [
                    { required: true, message: '请输入收件人', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
            },
            tableSelectRow: [], // 多选框选择的数据
            tableLoading: false, // 加载
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            uploadImgSize: 10, // 上传文件大小
            tableData: [], // 表格数据
            filterData: { // 高级搜索
                stateCheckAll: false, // 选择全局
                state: null,
                states: [],
                paymentRecordUn: null,
                shopName: null,
                enterpriseName: null,
                recordType: null,
                stateSelect: [
                    // { value: 1, label: '待审核' },
                    { value: 1, label: '审核通过' },
                    { value: 2, label: '审核未通过' },
                ],
                recordTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '店铺年度服务费' },
                    { value: 2, label: '店铺交易服务费' },
                    // { value: 2, label: '电子招标年度服务费' },
                    // { value: 4, label: '合同履约交易服务费' },
                ],
                orderBy: 1,
                gmtCreate: [],
                gmtModified: [],
                auditOpenTime: [],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            uploadLoading: false,
            contentLoading: false,
            fileList: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 初始化加载数据
        this.getTableData()
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        handleChangeSort (value) {
            // 将下拉菜单的command值(0,1,2)转换为后端需要的值(1,2,3)
            this.filterData.orderBy = value + 1
        },
        onDownload () {
            this.contentLoading = true
            feeInputTemplate().then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' })// 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob)// 3.创建一个临时的url指向blob对象
                const a = document.createElement('a')// 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '导入缴费数据模板.xlsx'
                a.click()
                window.URL.revokeObjectURL(url)// 5.释放这个临时的对象url
            }).finally(()=>{
                this.contentLoading = false
            })
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload (file) {
            this.uploadLoading = true
            let image = this.addFeeForm.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        save (num) {
            if (this.addFeeForm.files.length == 0) {
                return this.$message.error('请上传缴费证明！')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.addFeeForm.submitAud = num
                this.showAddFeeLoading = true
                supplierCreateFee(this.addFeeForm).then(res => {
                    if (res.code == null) {
                        this.$message.success('保存成功')
                        this.addFeeForm = {
                            state: 0,
                            payType: 1,
                            recordType: 1,
                            paymentDurationType: 4,
                            paymentDuration: 0,
                            files: [],
                            payAmount: 0,
                            remarks: null,
                        }
                        this.fileList = []
                        this.showAddFee = false
                        this.$router.push({
                            path: '/supplierSys/fee/shop/yearDtl',
                            name: 'supplierSysFeeShopYearDtl',
                            query: {
                                sn: res
                            }
                        })
                    }
                }).finally(() => {
                    this.showAddFeeLoading = false
                })
            })
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.addFeeForm.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.addFeeForm.files = []
                this.fileList = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // 上传营业执照
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.addFeeForm.files = []
                    this.fileList = []
                }else {
                    let resO = res[0]
                    this.addFeeForm.files.push({
                        name: resO.objectName,
                        relevanceType: 1,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        payAmountChange () {
            if(this.addFeeForm.payAmount <= 0 || this.addFeeForm.payAmount >= 9999999999) {
                this.addFeeForm.payAmount = this.fixed2(1)
            }else {
                this.addFeeForm.payAmount = this.fixed2(this.addFeeForm.payAmount)
            }
        },
        checkInputQty () {
            const regex = /^-?\d+$/
            if (!regex.test(this.addFeeForm.paymentDuration)) {
                this.$message.error('请输入有效的数字')
                this.addFeeForm.payAmount = this.freeAmount
                return this.addFeeForm.paymentDuration = 1
            }
            if (this.addFeeForm.paymentDuration < 0 || this.addFeeForm.paymentDuration > 999) {
                this.$message.error('超过限制！')
                this.addFeeForm.payAmount = this.freeAmount
                return this.addFeeForm.paymentDuration = 1
            }
            this.addFeeForm.payAmount = this.addFeeForm.paymentDuration * this.freeAmount
        },
        // 多选框
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        // 行点击
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        // 跳转详情
        handleView (row) {
            if(row.paymentDurationType != null) {
                this.$router.push({
                    path: '/platform/fee/payRecordManageDtl',
                    name: 'platformFeePayRecordManageDtl',
                    query: {
                        sn: row.paymentRecordUn,
                        shopName: row.shopName
                    }
                })
            }else {
                this.$router.push({
                    path: '/platform/fee/payRecordManageDtl2',
                    name: 'platformFeePayRecordManageDtl2',
                    query: {
                        sn: row.paymentRecordUn,
                        shopName: row.shopName
                    }
                })
            }
        },
        //重置数据
        resetSearchConditions () {
            this.filterData.state = null
            this.filterData.recordType = null
            this.filterData.paymentRecordUn = null
            this.filterData.enterpriseName = null
            this.filterData.shopName = null
            this.filterData.stateCheckAll = false
            this.filterData.states = []
            this.filterData.gmtCreate = []
            this.filterData.gmtModified = []
            this.filterData.auditOpenTime = []
        },
        // 状态全选
        stateAllSelect (value) {
            if (value) {
                this.filterData.states = this.filterData.stateSelect.map(t => {
                    return t.value
                })
            } else {
                this.filterData.states = []
            }
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateSelect.length
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage || 1,
                limit: this.paginationInfo.pageSize || 20,
            }
            // 只保留后端接口支持的参数
            if (this.filterData.paymentRecordUn != null) {
                params.paymentRecordUn = this.filterData.paymentRecordUn
            }
            if (this.filterData.recordType != null) {
                params.serviceType = this.filterData.recordType
            }
            if (this.filterData.states != null && this.filterData.states.length > 0) {
                params.states = this.filterData.states
            } else {
                // 默认查询审核通过和审核未通过的记录
                params.states = [1, 2]
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.gmtCreate != null) {
                params.payTimeStart = this.filterData.gmtCreate[0]
                params.payTimeEnd = this.filterData.gmtCreate[1]
            }
            if (this.filterData.gmtModified != null) {
                params.modifyTimeStart = this.filterData.gmtModified[0]
                params.modifyTimeEnd = this.filterData.gmtModified[1]
            }
            if (this.filterData.auditOpenTime != null) {
                params.auditTimeStart = this.filterData.auditOpenTime[0]
                params.auditTimeEnd = this.filterData.auditOpenTime[1]
            }
            // 添加排序参数
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            params.orderDirection = 'DESC'
            params.shopName = this.filterData.shopName
            this.tableLoading = true
            feeManagementList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = this.publicFunc.tableFunc.transformTableIndex(res.list, res)
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
    appearance: textfield !important;
    -moz-appearance: textfield !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
/deep/ .addDia {
    .el-dialog__body {
        height: 600px;
    }
}

/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>
