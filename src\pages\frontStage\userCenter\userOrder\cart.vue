<template>
    <div class="root" v-loading="cartLoading">
        <div class="box center p20">
            <div class="addr-bar dfb">
                <div>共计 {{ totalSkuCount }} 件</div>
                <!--  <div>
                        <span>配送至：</span>
                        <el-select v-model="selectedAddr" placeholder="请选择配送地址">
                            <el-option v-for="item in addrList" :key="item.addressId" :label="item.province + item.city + item.county + item.detailAddress" :value="item.addressId">
                            </el-option>
                        </el-select>
                    </div>-->
            </div>
            <div class="title dfa">
                <el-checkbox v-model="selectAllProduct" label="全选" :indeterminate="false" @change="toggleSelectAll"/>
                <div>商品</div>
                <div>单价</div>
                <div>数量</div>
                <div>小计</div>
                <div>操作</div>
            </div>
            <div class="product">
                <div class="shop" v-for="(item, i) in cartList" :key="i">
                    <div class="shop-name dfa">
                        <el-checkbox v-model="item.checked" :label="item.shopName" :indeterminate="false" @change="selectAllInShop(i)"/>
                        <!-- <img :src="item.list[0].icon" alt=""> -->
                    </div>
                    <div class="product-item dfa" v-for="(product, index) in item.productInfo" :key="index">
                        <div class="checkbox">
                            <el-checkbox v-model="product.checked" @change="(checked) => checkChange({ checked, id: product.cartId })"/>
                        </div>
                        <img
                            @click="$router.push({ path: '/mFront/productDetail', query: { productId: product.productId } })"
                            :src="product.productMinImg ? imgUrlPrefixAdd + product.productMinImg : require('@/assets/images/img/queshen3.png')"
                            alt="">
                        <div class="title-box">
                            <!--                            <div class="dfa"><div class="tag">平台自营</div><div class="name">{{product.productName}}</div></div>-->
                            <div
                                @click="$router.push({ path: '/mFront/productDetail', query: { productId: product.productId } })">
                                {{ product.productName }}
                            </div>
                            <div style="margin-top: 8px">
                                规格型号：{{ product.skuName }}
                            </div>
                            <div style="margin-top: 8px">
                                剩余库存：{{ product.stock }}
                            </div>
                            <div style="margin-top: 8px">
                                计量单位：{{ product.unit }}
                            </div>
                        </div>
                        <div class="price">￥{{ product.sellPrice }}</div>
                        <div class="num-box">
                            <div class="numCalc df">
                                <div disabled @click="changeNum('minus', product)">-</div>
                                <input @change="cartNumChange(product)" v-model="product.cartNum" type="text">
                                <div @click="changeNum('plus', product)">+</div>
                            </div>
                        </div>
                        <div class="total" style="color: rgba(212, 48, 48, 1);">￥{{ product.numTotalPrice }}</div>
                        <div class="operate">
                            <div @click="deleteProduct(product.cartId)">删除</div>
                            <div v-if="!product.collect" @click="addToCollection(product,1)">加入收藏</div>
                            <div v-else @click="addToCollection(product,0)">取消收藏</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bottom-bar dfb">
                <div class="bar-left dfa">
                    <el-checkbox v-model="selectAllProduct" label="全选" :indeterminate="false"
                                 @change="toggleSelectAll"></el-checkbox>
                    <span @click="deleteBatch">删除选中的商品</span>
                    <span @click="addBatchCollect">移入收藏</span>
                    <span @click="clearCart">清空购物车</span>
                    <!--<span @click="submitPlan" v-show="userInfo.isExternal != 1">推送零星采购计划</span>-->
                </div>
                <div class="bar-right df">
                    <div><span>已选择 {{ totalSelected }} 件商品</span><i class="el-icon-arrow-up"></i></div>
                    <div class="bar-right-price dfa"><span>总价：</span><span>￥{{ totalPrice }}</span><img
                        src="../../../../assets/images/userCenter/提示.png" alt=""></div>
<!--                    <button @click="submitOrder">去结算</button>-->
                    <button @click="submitPlan" v-show="userInfo.isExternal != 1">推送零星采购计划</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { addBatch, addShopCollect } from '@/api/frontStage/productCollect'
import {
    changeCartNum,
    deleteSku,
    emptyCart,
    getShoppingCartList,
    pushCardProductsArrive,
    updateChecked
} from '@/api/frontStage/userCenter'
import { debounce, removeExtraDigit, toFixed } from '@/utils/common'
import { mapState } from 'vuex'
import BigNumber from 'bignumber.js'

export default {
    data () {
        return {
            cartLoading: false,
            selectedAddr: null,
            totalPrice: '',
            addrList: [
                { id: 0, addr: '四川成都市成华区保和街道' },
            ],
            selectAllProduct: false,
            cartList: [], // 购物车列表
            changedSku: null
        }
    },
    watch: {
        cartList: {
            handler (newVal) {
                let checkAll = true
                newVal.forEach(newItem => {
                    // 判断店铺全选
                    newItem.checked = newItem.productInfo.every(subItem => subItem.checked)
                    // 如果有店铺未全选则取消购物车全选状态
                    if (!newItem.checked) checkAll = false
                    newItem.productInfo.forEach(subItem => {
                        // 计算价格
                        if (isNaN(Number(subItem.cartNum))) subItem.cartNum = 1
                        subItem.numTotalPrice = (Number(subItem.sellPrice) * subItem.cartNum).toFixed(2)
                    })
                })
                this.selectAllProduct = checkAll
                this.calcTotalPrice()
            },
            deep: true
        },
        // 监听用户修改sku的数量
        totalProductCount () {
            if (JSON.stringify(this.changedSku) === '{}') return
            this.handleNumChange(this.changedSku)
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 商品总数
        totalProductCount () {
            let num = 0
            this.cartList.forEach(item => {
                item.productInfo.forEach(item1 => num += item1.cartNum)
            })
            return num
        },
        // 商品sku总数量
        totalSkuCount () {
            let len = 0
            this.cartList.forEach(item => len += item.productInfo.length)
            return len
        },
        // 全选的商品数量
        totalSelected () {
            let count = 0
            this.cartList.forEach(item => {
                if (item.checked) { // 店铺是否全选
                    return count += item.productInfo.length
                }
                item.productInfo.forEach(subItem => {
                    subItem.checked ? count += 1 : null
                })
            })
            return count
        },
    },
    methods: {
        async checkChange ({ id, checked }) {
            let { code, message } = await updateChecked({ id, checked: checked ? 1 : 0 })
            if(code !== 200) this.$message({ message, type: 'warning' })
            await this.getShoppingCartList()
        },
        async updateCheckedState () {
            let products = this.cartList.map(item => item.productInfo).flat(1)
            if(products.length === 0) return
            let requests = products.map(item => {
                let { cartId, checked } = item
                return new Promise(resolve => {
                    updateChecked({ id: cartId, checked: checked ? 1 : 0 }).then(res => resolve(res))
                })
            })
            await Promise.all(requests)
            await this.getShoppingCartList()
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        cartNumChange (item) {
            if(item.secondUnitNum != null && item.secondUnitNum > 0) {
                let max = this.fixed4(item.stock * item.secondUnitNum)
                if (item.cartNum > Number(max)) {
                    item.cartNum = Number(max)
                }
            }else {
                if (item.cartNum > item.stock) {
                    item.cartNum = item.stock
                }
            }
            this.handleNumChange(item)
        },
        // 批量收藏
        addBatchCollect () {
            let list = this.getSelectedProduct()
            if (list == null || list.length === 0) {
                this.$message({ message: '未选中数据', type: 'info' })
                return
            }
            let arr = list.map(item => item.productId)
            addBatch(arr).then(async res => {
                if (res.code !== 200) return
                this.$message({ message: '添加关注成功！', type: 'success' })
                this.cartLoading = true
                await this.getShoppingCartList()
                this.cartLoading = false
            })

        },
        // 获取购物车列表
        async getShoppingCartList () {
            this.cartList = await getShoppingCartList()
        },
        // 全选所有商品
        toggleSelectAll () {
            this.cartList.forEach(item => {
                item.productInfo.forEach(item1 => item1.checked = this.selectAllProduct)
            })
            this.updateCheckedState()
        },
        // 全选店铺下的商品
        selectAllInShop (i) {
            this.cartList[i].productInfo.forEach(item => item.checked = this.cartList[i].checked)
            this.updateCheckedState()
        },
        // 获取选中的所有商品
        getSelectedProduct () {
            let arr = []
            this.cartList.forEach(item => {
                if (item.checked) { // 店铺是否全选
                    return arr = arr.concat(item.productInfo)
                }
                item.productInfo.forEach(subItem => {
                    if(subItem.checked) arr.push(subItem)
                })
            })
            return arr
        },
        // 更改sku对应的商品数量
        changeNum (action, item) {
            this.changedSku = item
            if (action === 'minus') {
                if (item.cartNum === 1) return
                item.cartNum--
            } else {
                if (item.cartNum > item.stock) {
                    item.cartNum = item.stock
                } else {
                    item.cartNum++
                }
            }
            this.handleNumChange(item)
        },
        // 删除商品
        deleteProduct (id) {
            this.clientPop('info', '您确定删除此商品吗？', async () => {
                let res = await deleteSku([id])
                if (res.code !== 200) return
                this.$message({ message: '删除成功', type: 'success' })
                this.$bus.$emit('refreshCart')
                this.cartLoading = true
                await this.getShoppingCartList()
                this.cartLoading = false
            })
        },
        // 删除选中的商品
        deleteBatch () {
            let list = this.getSelectedProduct()
            if (list == null || list.length === 0) {
                this.$message({ message: '未选中数据', type: 'info' })
                return
            }
            let arr = list.map(item => item.cartId)
            deleteSku(arr).then(async res => {
                if (res.code !== 200) return
                this.$message({ message: '删除成功', type: 'success' })
                this.$bus.$emit('refreshCart')
                this.cartLoading = true
                await this.getShoppingCartList()
                this.cartLoading = false
            })
        },
        // 添加到收藏
        addToCollection (product, state) {
            if (state === 1) {
                let params = {
                    productId: product.productId,
                    collectType: 1,
                    productType: product.productType,
                    state: 1
                }
                addShopCollect(params).then(async res => {
                    if (res.code === 200) {
                        this.$message({ message: '收藏成功', type: 'success' })
                    }
                    this.cartLoading = true
                    await this.getShoppingCartList()
                    this.cartLoading = false
                })
            } else {
                let params = {
                    productId: product.productId,
                    collectType: 1,
                }
                addShopCollect(params).then(async res => {
                    if (res.code === 200) {
                        this.$message({ message: '取消收藏成功', type: 'success' })
                    }
                    this.cartLoading = true
                    await this.getShoppingCartList()
                    this.cartLoading = false
                })
            }
        },
        // 推送计划到pcwp
        submitPlan () {
            let { isSubmitOrder, isInterior } = this.userInfo
            if(isInterior === 1) {
                if(isSubmitOrder == null || isSubmitOrder === 0) {
                    this.$message.error('没有下单权限，请联系管理员！')
                    return
                }
            }
            this.$confirm('您确定要推送所选商品到计划吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let list = this.getSelectedProduct()
                if(list == null || list.length === 0) {
                    this.$message({ message: '未选中数据', type: 'info' })
                    return
                }
                let cardIds = list.map(item => item.cartId)
                this.cartLoading = true
                pushCardProductsArrive(cardIds).then(async res => {
                    if(res.code === 200) return
                    this.cartLoading = false
                    await this.getShoppingCartList()
                    this.cartLoading = false
                    this.$bus.$emit('refreshCart')
                    this.$alert('提交成功', '提示', {
                        confirmButtonText: '确定',
                        type: 'success'
                    })
                })
            })
        },
        // 清空购物车
        clearCart () {
            emptyCart().then(async res => {
                if (res.message === '操作成功') {
                    this.$message({ message: '清空成功', type: 'success' })
                    this.$bus.$emit('refreshCart')
                }
                this.cartLoading = true
                await this.getShoppingCartList()
                this.cartLoading = false
            })
        },
        // 计算订单总价
        calcTotalPrice () {
            let price = new BigNumber(0)
            this.cartList.forEach(item => {
                item.productInfo.forEach(item1 => {
                    if (item1.checked) {
                        price = price.plus(new BigNumber(item1.cartNum).times(item1.sellPrice))
                    }
                })
            })
            this.totalPrice = price.toFixed(2)
        },
        // 提交订单
        submitOrder () {
            let { isSubmitOrder, isInterior } = this.userInfo
            if(isInterior === 1) {
                if(isSubmitOrder == null || isSubmitOrder === 0) {
                    this.$message.error('没有下单权限，请联系管理员！')
                    return
                }
            }
            let list = this.getSelectedProduct()
            if (list.length === 0) {
                this.$message({ message: '未选择商品', type: 'info' })
                return
            }
            let cartIds = list.map(t => t.cartId)
            localStorage.setItem('materialCartIdSubmit', JSON.stringify(cartIds))
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/user/submitCartOrder',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'submitCartOrder',
            })
        },
        // 发送修改sku对应商品数量的请求
        updateCartNum (item) {
            if (!item.cartNum) return
            changeCartNum({ changeNum: removeExtraDigit(item.cartNum), id: item.cartId }).then(async () => {
                this.cartLoading = true
                await this.getShoppingCartList()
                this.cartLoading = false
            })
        }
    },
    async created () {
        this.cartLoading = true
        this.handleNumChange = debounce(this.updateCartNum)
        await this.getShoppingCartList()
        this.calcTotalPrice()
        this.cartLoading = false
    },
}
</script>
<style scoped lang="scss">
.root {
    height: 100%;
    border: 1px solid rgba(229, 229, 229, 1);

    .box {
        width: 100%;
        background-color: #fff;
    }
}

/deep/ .el-checkbox {
    color: rgba(51, 51, 51, 1);

    .el-checkbox__inner {
        border: 1px solid rgba(204, 204, 204, 1);
    }

    .el-checkbox__label {
        color: #666;
    }
}

.addr-bar {
    height: 30px;

    & > div:first-child {
        font-size: 20px;
        font-weight: 500;
        color: rgba(212, 48, 48, 1);
    }

    & > div:last-child {
        color: rgba(51, 51, 51, 1);
    }

    /deep/ .el-input__inner {
        width: 216px;
        height: 26px;
    }
}

.title {
    height: 52px;
    margin-bottom: 12px;
    padding-left: 20px;
    color: rgba(51, 51, 51, 1);
    background-color: rgba(250, 250, 250, 1);

    /deep/ .el-checkbox {
        width: 158px;
    }

    & > div:nth-child(2) {
        width: 450px;
    }

    & > div:nth-child(3) {
        width: 129px;
    }

    & > div:nth-child(4) {
        width: 109px;
    }

    & > div:nth-child(5) {
        width: 108px;
    }
}

.product {
    min-height: 600px;
    margin-bottom: 30px;

    .shop-name {
        padding: 22px 0 14px 20px;

        img {
            width: 22px;
            height: 22px;
            margin-left: 3px;
        }
    }

    & .product-item:not(:last-of-type) {
        margin-bottom: 10px;
    }

    .product-item {
        height: 144px;
        border: 1px solid rgba(230, 230, 230, 1);

        img {
            width: 100px;
            height: 100px;
            object-fit: cover;
        }

        & > div {
            height: 100px;
        }

        .checkbox {
            padding: 0 30px 0 20px;
        }

        .title-box {
            width: 400px;
            padding-left: 12px;

            .tag {
                width: 60px;
                height: 20px;
                margin: 0 10px 7px 0;
                font-size: 12px;
                line-height: 20px;
                text-align: center;
                color: #fff;
                background-color: rgba(255, 195, 0, 1);
            }
        }

        .price {
            width: 157px;
            text-align: center;
        }

        .num-box {
            width: 101px;

            .numCalc {
                width: 100%;
                border: 1px solid rgba(204, 204, 204, 1);

                div, input {
                    height: 26px;
                    text-align: center;
                }

                div {
                    width: 26px;
                    line-height: 26px;
                    background-color: rgba(230, 230, 230, 1);
                    cursor: pointer;
                    user-select: none;

                    &:first-child {
                        border-right: 1px solid rgba(204, 204, 204, 1);
                    }

                    &:last-child {
                        border-left: 1px solid rgba(204, 204, 204, 1);
                    }
                }

                input {
                    width: 49px;
                }
            }
        }

        .total {
            width: 134px;
            text-align: center;
        }

        .operate {
            font-size: 10px;
            color: rgba(102, 102, 102, 1);

            div {
                margin-bottom: 10px;
                cursor: pointer;
            }
        }
    }
}

.bottom-bar {
    height: 60px;
    padding-left: 20px;
    border: 1px solid rgba(230, 230, 230, 1);

    & > div {
        height: 100%;
    }

    /deep/ .el-checkbox {
        margin-right: 30px;
    }

    .bar-left {
        color: rgba(102, 102, 102, 1);

        span {
            margin-right: 20px;
            cursor: pointer;
        }
    }

    .bar-right {
        & > div:first-child {
            margin: 7px 9px 0 0;
            color: rgba(153, 153, 153, 1);
            cursor: pointer;

            span {
                margin-right: 9px;
            }
        }

        .bar-right-price {
            height: 27px;
            margin: 4px 22px 0 0;

            & span:first-child {
                color: rgba(153, 153, 153, 1);
            }

            & span:nth-child(2) {
                margin-right: 12px;
                font-size: 18px;
                font-weight: 700;
                color: rgba(212, 48, 48, 1);
            }

            img {
                width: 16px;
                height: 16px;
            }
        }

        button {
            width: 128px;
            height: 60px;
            font-size: 24px;
            font-weight: 400;
            text-align: center;
            line-height: auto;
            color: rgba(255, 255, 255, 1);
            background-color: rgba(212, 48, 48, 1);
        }
    }
}
</style>