<!--<template>-->
<!--    <div class="root" v-loading="showLoading">-->

<!--    </div>-->
<!--</template>-->
<!--<script>-->
<!--import { CodeToText, regionData, TextToCode } from 'element-china-area-data'-->

<!--import { getOrderDetail, updateOrderInfo } from '@/api/frontStage/order'-->
<!--import { create, getList } from '@/api/frontStage/shippingAddr'-->
<!--export default {-->

<!--    data () {-->
<!--        return {-->
<!--            addressLoading: false,-->
<!--            userAddressFormRules: {-->
<!--                receiverName: [-->
<!--                    { required: true, message: '请输入收件人', trigger: 'blur' },-->
<!--                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }-->
<!--                ],-->
<!--                receiverMobile: [-->
<!--                    { required: true, message: '请输入11位手机号', trigger: 'blur' },-->
<!--                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },-->
<!--                ],-->
<!--                detailAddress: [-->
<!--                    { required: true, message: '请输入详细地址', trigger: 'blur' },-->
<!--                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }-->
<!--                ],-->
<!--            },-->
<!--            // 地址-->
<!--            addressData: regionData, // 地址数据-->
<!--            addDetailDialog: false,-->
<!--            selectAddressOptions: [],-->
<!--            userAddressForm: { // 新增编辑地址表单-->
<!--                detailAddress: null,-->
<!--            },-->
<!--            receiver: {},-->
<!--            submitOrderLoading: false,-->
<!--            addrList: [],-->
<!--            addrDialogVisible: false,-->
<!--            showLoading: false,-->
<!--            orderInfo: {-->
<!--            },-->
<!--            totalPrice: '130000.00',-->
<!--            spanArr: [],-->
<!--            pos: null-->
<!--        }-->
<!--    },-->
<!--    methods: {-->
<!--        // 地址选择-->
<!--        handleAddressChange () {-->
<!--            let addArr = this.selectAddressOptions-->
<!--            let province = CodeToText[addArr[0]]-->
<!--            let city = CodeToText[addArr[1]]-->
<!--            let county = CodeToText[addArr[2]]-->
<!--            this.userAddressForm.province = province-->
<!--            this.userAddressForm.city = city-->
<!--            this.userAddressForm.county = county-->
<!--            this.userAddressForm.detailAddress = province + city + county-->
<!--        },-->
<!--        // 创建编辑地址统一接口-->
<!--        createAddressM () {-->
<!--            this.$refs.addAddressRef.validate(valid => {-->
<!--                if (valid) {-->
<!--                    create(this.userAddressForm).then(res => {-->
<!--                        if(res.code == 200) {-->
<!--                            this.$message({-->
<!--                                message: res.message,-->
<!--                                type: 'success'-->
<!--                            })-->
<!--                            this.getAddRess()-->
<!--                            this.addDetailDialog = false-->
<!--                        }-->
<!--                    })-->
<!--                }-->
<!--            })-->
<!--        },-->
<!--        // 创建-->
<!--        createAddress () {-->
<!--            this.userAddressForm = {-->
<!--                detailAddress: null,-->
<!--            },-->
<!--            this.selectAddressOptions = []-->
<!--            this.addDetailDialog = true-->
<!--        },-->
<!--        // 编辑地址-->
<!--        handleEditAddr (row) {-->
<!--            let obj = {-->
<!--                addressId: row.addressId,-->
<!--                detailAddress: row.addr,-->
<!--                receiverName: row.name,-->
<!--                receiverMobile: row.tel,-->
<!--            }-->
<!--            this.userAddressForm = obj-->
<!--            //地址选择器回显-->
<!--            let selected = TextToCode[row.province][row.city][row.county].code-->
<!--            let selected1 = JSON.stringify(selected).slice(1, 3)-->
<!--            let selected2 = JSON.stringify(selected).slice(3, 5)-->
<!--            let selected3 = JSON.stringify(selected).slice(5, -1)-->
<!--            let arr = []-->
<!--            arr.push(selected1 + '0000')-->
<!--            arr.push(selected1 + selected2 + '00')-->
<!--            arr.push(selected1 + selected2 + selected3)-->
<!--            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示-->
<!--            this.addDetailDialog = true-->
<!--        },-->
<!--        // 地址表格点击-->
<!--        handleCurrentInventoryClick (row) {-->
<!--            let params = {-->
<!--                orderId: this.orderInfo.orderId,-->
<!--                receiverName: row.name,-->
<!--                receiverMobile: row.tel,-->
<!--                receiverAddress: row.addr,-->
<!--            }-->
<!--            this.addressLoading = true-->
<!--            updateOrderInfo(params).then(res => {-->
<!--                if(res.code != null && res.code == 200) {-->
<!--                    this.$message.success('修改成功！')-->
<!--                    this.addrDialogVisible = false-->
<!--                    this.getOrderDetailM()-->
<!--                }-->
<!--                this.addressLoading = false-->
<!--            }).catch(() => {-->
<!--                this.addressLoading = false-->
<!--            })-->
<!--        },-->
<!--        // 获取地址-->
<!--        getAddRess () {-->
<!--            // 获取收货地址-->
<!--            this.addressLoading = true-->
<!--            getList({ page: 1, limit: 30 }).then(res => {-->
<!--                if(!res.list[0]) return-->
<!--                let address = []-->
<!--                // 显示默认地址-->
<!--                res.list.forEach(item => {-->
<!--                    let obj = {-->
<!--                        addressId: item.addressId,-->
<!--                        checked: false,-->
<!--                        addr: item.detailAddress,-->
<!--                        name: item.receiverName,-->
<!--                        tel: item.receiverMobile,-->
<!--                        province: item.province,-->
<!--                        city: item.city,-->
<!--                        county: item.county,-->
<!--                    }-->
<!--                    address.push(obj)-->
<!--                })-->
<!--                this.addrList = address-->
<!--                this.addressLoading = false-->
<!--            }).catch(() => {-->
<!--                this.addressLoading = false-->
<!--            })-->
<!--        },-->
<!--        // 切换地址-->
<!--        checkedAddressM () {-->
<!--            this.getAddRess()-->
<!--            this.addrDialogVisible = true-->
<!--        },-->
<!--        getOrderDetailM () {-->
<!--            this.showLoading = true-->
<!--            getOrderDetail({ orderSn: this.$route.query.orderSn }).then(res=>{-->
<!--                this.orderInfo = res-->
<!--                this.showLoading = false-->
<!--            }).catch(() => {-->
<!--                this.showLoading = false-->
<!--            })-->
<!--        },-->
<!--        handleConfirm () {-->
<!--            this.$router.go(-1)-->
<!--        },-->
<!--    },-->
<!--    created () {-->
<!--        this.getOrderDetailM()-->
<!--    }-->
<!--}-->
<!--</script>-->
<!--<style scoped lang="scss">-->
<!--.root {-->
<!--    height: 100%;-->
<!--    padding-top: 20px;-->
<!--    font-family: 'MiSans';-->
<!--    background-color: #f5f5f5;-->

<!--    .box {-->
<!--        width: 1326px;-->
<!--        background-color: #fff;-->
<!--    }-->
<!--}-->

<!--.list-title {-->
<!--    height: 50px;-->
<!--    padding: 15px 20px;-->
<!--    font-size: 18px;-->
<!--    line-height: 20px;-->
<!--    border-bottom: 1px solid rgba(230, 230, 230, 1);-->

<!--    &::before {-->
<!--        width: 3px;-->
<!--        height: 20px;-->
<!--        margin-right: 10px;-->
<!--        content: '';-->
<!--        display: block;-->
<!--        background-color: rgba(33, 110, 198, 1);-->
<!--    }-->
<!--}-->

<!--.progress {-->
<!--    .content {-->
<!--        padding: 40px 20px;-->
<!--    }-->

<!--    .progress-box {-->
<!--        padding: 0 40px;-->
<!--        align-items: flex-start;-->
<!--        justify-content: center;-->

<!--        &>img {-->
<!--            margin-top: 6px;-->
<!--        }-->

<!--        .step {-->
<!--            min-width: 68px;-->
<!--            flex-direction: column;-->

<!--            img {-->
<!--                width: 28px;-->
<!--                height: 28px;-->
<!--                margin-bottom: 20px;-->
<!--            }-->
<!--        }-->
<!--    }-->
<!--}-->

<!--.sheet {-->
<!--    /deep/ .el-table {-->
<!--        border: 1px solid rgba(230, 230, 230, 1);-->

<!--        thead th {-->
<!--            height: 45px;-->
<!--            font-weight: 400;-->
<!--            color: rgba(51, 51, 51, 1);-->
<!--            background-color: rgba(247, 247, 247, 1);-->
<!--        }-->

<!--        .el-table__body {-->
<!--            .el-table__row {-->
<!--                height: 46px;-->
<!--            }-->
<!--        }-->

<!--        .el-table-cell {-->
<!--            color: rgba(51, 51, 51, 1);-->
<!--        }-->
<!--    }-->
<!--}-->

<!--.order-info {-->
<!--    margin-bottom: 20px;-->

<!--    h3 {-->
<!--        margin-bottom: 16px;-->
<!--        font-size: 16px;-->
<!--        font-weight: 500;-->
<!--    }-->

<!--    .el-row {-->
<!--        margin-bottom: 16px;-->
<!--        font-size: 14px;-->
<!--        color: rgba(51, 51, 51, 1);-->
<!--    }-->

<!--    .content:not(.order) {-->
<!--        border-bottom: 1px solid rgba(230, 230, 230, 1);-->
<!--    }-->
<!--    .sum {-->
<!--        font-size: 14px;-->
<!--        text-align: right;-->
<!--        color: rgba(51, 51, 51, 1);-->
<!--    }-->
<!--    .order-list {-->
<!--        border-bottom: 1px solid rgba(230, 230, 230, 1);-->
<!--    }-->
<!--}-->
<!--.confirm-btn {-->
<!--    padding-bottom: 40px;-->
<!--    text-align: center;-->
<!--    button {-->
<!--        width: 150px;-->
<!--        height: 40px;-->
<!--        color: #fff;-->
<!--        font-size: 18px;-->
<!--        background-color: rgb(48, 100, 212);-->
<!--    }-->
<!--}-->

<!--/deep/ .el-dialog {-->
<!--    width: 1000px;-->
<!--    //width: 1120px;-->
<!--    min-height: 326px;-->
<!--    .dialog-body {-->
<!--        width: 500px;-->
<!--        padding-top: 30px;-->

<!--        .el-form-item {-->
<!--            margin-bottom: 14px;-->
<!--            &:last-of-type {margin-bottom: 20px;}-->
<!--        }-->

<!--        .el-form-item__label {-->
<!--            // height: 14px;-->
<!--            // margin-bottom: 20px;-->
<!--            padding-bottom: 0;-->
<!--            color: #999;-->
<!--        }-->

<!--        .el-input__inner {-->
<!--            width: 300px;-->
<!--            height: 35px;-->
<!--            border: 1px solid rgba(217, 217, 217, 1);-->
<!--            border-radius: 0;-->
<!--        }-->
<!--        .address .el-input__inner {width: 500px;}-->
<!--        .tel {-->
<!--            .el-form-item__content {-->
<!--                display: flex;-->
<!--                span {-->
<!--                    margin-right: 10px;-->
<!--                    color: #333;-->
<!--                }-->
<!--            }-->
<!--            .el-input, .el-input__inner {width: 266px;}-->
<!--        }-->
<!--    }-->
<!--    .butSub {-->
<!--        width: 80px;-->
<!--        height: 40px;-->
<!--        font-size: 16px;-->
<!--        color: #fff;-->
<!--        background-color: #216EC6;-->
<!--        margin-left: 100px;-->
<!--    }-->
<!--    .el-table {-->
<!--        margin-top: 20px;-->
<!--        border: 1px solid rgba(230, 230, 230, 1);-->
<!--        // border-bottom: 0;-->
<!--        font-size: 14px;-->
<!--        .edit-btn {-->
<!--            color: rgba(34, 111, 199, 1);-->
<!--            cursor: pointer;-->
<!--        }-->
<!--    }-->
<!--    .add {-->
<!--        width: 80px;-->
<!--        height: 30px;-->
<!--        margin: 18px 0 40px 0;-->
<!--        line-height: 30px;-->
<!--        text-align: center;-->
<!--        color: rgba(33, 110, 198, 1);-->
<!--        border: 1px solid rgba(33, 110, 198, 1);-->
<!--    }-->
<!--    .el-table__header {-->
<!--        .cell{-->
<!--            font-weight: 400;-->
<!--            color: rgba(51, 51, 51, 1);-->
<!--        }-->
<!--        .el-checkbox {display: none;}-->
<!--    }-->
<!--    .el-dialog__footer {-->
<!--        border: 0;-->
<!--        text-align: center;-->
<!--        button {-->
<!--            width: 80px;-->
<!--            height: 40px;-->
<!--        }-->
<!--        & button:first-child {-->
<!--            margin-right: 20px;-->
<!--            color: rgba(128, 128, 128, 1);-->
<!--            background-color: rgba(230, 230, 230, 1);-->
<!--        }-->
<!--        & button:last-child {-->
<!--            color: #fff;-->
<!--            background-color: rgba(33, 110, 198, 1);-->
<!--        }-->
<!--    }-->
<!--}-->
<!--</style>-->