<template>
  <div class="e-form">
    <BillTop @cancel="handleClose"/>
    <div v-loading='formLoading' class="tabs warningTabs" style="padding-top: 70px;">
      <el-tabs v-model="tabsName" tab-position="left" @tab-click="onChangeTab">
        <el-tab-pane :disabled="clickTabFlag" label="入库单详情" name="baseInfo">
        </el-tab-pane>
        <el-tab-pane :disabled="clickTabFlag" label="入库结算单明细" name="baseInfos">
        </el-tab-pane>
        <el-tab-pane label="审核记录" name="auditRecords" v-if="viewType != 'add'" :disabled="clickTabFlag"/>
        <div id="tabs-content">
          <div id="baseInfCon" class="con">
            <div id="baseInfo" class="tabs-title">入库结算单详情</div>
            <!--新增-->
            <div v-if="showForm" class="form">
              <el-form
                  ref="formEdit" :model="addForm.formData" :rules="formRules"
                  class="demo-ruleForm" label-width="200px"
              >
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="业务类型：" prop="supplierType">
                      <el-radio-group v-model="addForm.formData.supplierType" style="width: 100%">
                        <el-col :span="8" v-for="item in supplierTypeList" :key="item.value">
                          <el-radio :label="item.value" :value="item.value" :key="item.value">{{
                              item.label
                            }}
                          </el-radio>
                        </el-col>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="入库时间：" prop="storedWarehouseTime">
                      <el-date-picker
                          value-format="yyyy-MM-dd HH:mm:ss"
                          v-model="addForm.formData.storedWarehouseTime"
                          type="datetime"
                          placeholder="选择日期时间"
                          default-time="12:00:00">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="供货单位：" prop="supplierName">
                      <el-input placeholder="请选择供货单位" disabled
                                v-model="addForm.formData.supplierName"/>
                      <el-button
                          :disabled="addForm.formData.supplierType == null"
                          size="mini" type="primary" @click="selectedSupplier">选择供货单位
                      </el-button>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="物资名称：" prop="productName">
                        <el-button size="mini"
                                   :disabled="addForm.formData.supplierName == null ||  addForm.formData.supplierName == ''"
                                   type="primary"
                                   @click="selectMaterialBtnClick">选择物资
                        </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="库房选择：" prop="warehouseId">
                      <el-select
                          v-model="addForm.formData.warehouseId" filterable placeholder="请选择库房"
                      >
                        <el-option
                            v-for="item in warehouseList" :key="item.value"
                            :label="item.label" :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同编号：" prop="contractNo">
                      <el-input placeholder="请输入合同编号" disabled
                                v-model="addForm.formData.contractNo"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="采购含税进价：">
                      <el-input disabled
                          v-model="addForm.formData.rateAmount" clearable
                          oninput="if(value.length > 16)value = value.slice(0, 16)"
                          placeholder="请输入采购含税进价"
                          type="number"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="采购不含税进价：">
                      <el-input disabled
                          v-model="addForm.formData.noRateAmount" clearable
                          oninput="if(value.length > 16)value = value.slice(0, 16)"
                          placeholder="请输入采购不含税进价"
                          type="number"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="数量：" prop="num">
                      <el-input disabled
                          v-model="addForm.formData.num" clearable
                          oninput="if(value.length > 16)value = value.slice(0, 16)"
                          placeholder="请输入数量"
                          type="number"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="viewType != 'add'">
                    <el-form-item label="期数：" prop="accountPeriod">
                      <el-input disabled
                          v-model="addForm.formData.accountPeriod" clearable placeholder="请输入期数"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="发票字号：" prop="invoiceNum">
                      <el-input
                          v-model="addForm.formData.invoiceNum" clearable placeholder="请输入发票字号"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="备注：" prop="remark">
                      <el-input
                          type="textarea"
                          :rows="3"
                          style="width: 100%"
                          v-model="addForm.formData.remark">
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <!-- 自营店需要采购进价字段 -->
                  <el-col :span="12">
                    <el-form-item label="上传附件：" prop="originalPrice">
                      <el-upload
                          v-model="addForm.formData.attachmentFile"
                          action="fakeaction"
                          list-type="picture-card"
                          :file-list="formData.files">
                        <i class="el-icon-plus"></i>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div id="baseInfos" class="tabs-title">入库结算单明细</div>
            <div class="e-table">
              <el-table
                  ref="tableRef" class="table"
                  :height="rightTableHeight" :data="tableData" border highlight-current-row
                  v-loading="tableLoading"
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <!-- 名称 -->
                <el-table-column label="商品名称" width="" prop="productName">
                </el-table-column>
                <el-table-column label="规格型号" prop="spec"/>
                <el-table-column label="计量单位" prop="unit"/>
                <el-table-column label="数量" prop="quantity"/>
                <el-table-column label="采购含税单价" prop="price"/>
                <el-table-column label="采购含税金额" prop="totalAmount"/>
                <el-table-column label="采购不含税金额" prop="noRateAmount"/>
                <el-table-column label="备注" prop="desc">
                </el-table-column>
              </el-table>
              <!-- 分页器 -->
              <Pagination
                  :total="pages.totalCount" :pageSize.sync="pages.pageSize"
                  :currentPage.sync="pages.currPage"
                  @currentChange="currentChange" @sizeChange="sizeChange"/>
            </div>
            <div id="auditRecords" class="tabs-title" v-if="viewType != 'add'">审核记录</div>
            <div class="e-table" v-if="viewType != 'add'" style="background-color: #fff">
              <el-table
                  border
                  style="width: 100%"
                  :data="auditRecordList"
                  :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                  :row-style="{ fontSize: '14px', height: '48px' }"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="auditType" label="审核类型" width="220">
                </el-table-column>
                <el-table-column prop="auditPerson" label="审核人" width="220">
                </el-table-column>
                <el-table-column prop="userType" label="人员类型" width="220">
                </el-table-column>
                <el-table-column prop="auditTime" label="审核时间" width="230">
                </el-table-column>
                <el-table-column prop="founderName" label="审核意见" width="227">
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tabs>
      <div class="buttons">
        <template v-if="viewType != 'add'">
          <el-button
              v-if="[0, 2].includes(addForm.formData.state)"
              type="success"
              @click="updateStateBatch(3,'保存并上架')"
          >保存并上架
          </el-button>
          <el-button
              v-else-if="addForm.formData.state===1"
              type="danger"
              @click="updateStateBatch(2,'下架')"
          >下架
          </el-button>
        </template>
        <el-button v-if="viewType == 'add'" type="success" @click="save">保存</el-button>
        <el-button v-if="viewType == 'add'" type="success" @click="saveAndSubmit">保存并提交</el-button>
        <el-button v-if="viewType != 'add'" type="primary" @click="approved">通过</el-button>
        <el-button v-if="viewType != 'add'" type="danger" @click="reject">驳回</el-button>
        <el-button @click="handleClose">返回</el-button>
      </div>
    </div>
    <!--选择物资库-->
    <el-dialog
        v-dialogDrag custom-class="dlg" width="90%" title="物资选择"
        :visible.sync="selectMaterialShow"
    >
      <div class="box-left">
        <div class="e-table">
          <div class="top">
            <div style="width: 200px">
              <el-input type="text" @blur="getTwoMaterialOrderListM" placeholder="输入搜索关键字"
                        v-model="keywords2">
                <img :src="require('@/assets/search.png')" slot="suffix"
                     @click="getTwoMaterialOrderListM"/>
              </el-input>
            </div>
          </div>
          <el-table ref="bidingOrderItemRef"
                    border
                    max-height="340px"
                    highlight-current-row
                    @selection-change="selectContractOrPlanSelectM"
                    @row-click="selectContractOrPlanClickM"
                    :data="contractOrderTableDate"
                    v-loading="selectContractOrderLoading"
                    class="table"
          >
            <el-table-column type="selection" width="40"></el-table-column>
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column prop="supplierName" label="供应商名称" width=""></el-table-column>
            <el-table-column prop="orderSn" label="订单号" width=""></el-table-column>
            <el-table-column prop="gmtCreate" label="创建日期" width="">
              <template v-slot="scope">
                {{ scope.row.gmtCreate }}
              </template>
            </el-table-column>
          </el-table>
          <Pagination
              v-if="contractOrderTableDate != null && contractOrderTableDate.length > 0"
              :total="paginationInfo1.total"
              :pageSize.sync="paginationInfo1.pageSize"
              :currentPage.sync="paginationInfo1.currentPage"
              @currentChange="currentChangeUser1"
              @sizeChange="sizeChangeUser1"
          />
        </div>
      </div>
      <div class="box-right">
        <div class="e-table">
          <div class="top">
            <div style="width: 200px">
              <el-input type="text" @blur="getReconciliationDtlListM2" placeholder="输入搜索关键字"
                        v-model="keywords3">
                <img :src="require('@/assets/search.png')" slot="suffix"
                     @click="getReconciliationDtlListM2"/>
              </el-input>
            </div>
          </div>
          <el-table ref="siteReceivingTableRef"
                    v-loading="siteReceivingLoading"
                    border
                    max-height="390px"
                    @selection-change="siteReceivingTableSelectM"
                    @row-click="siteReceivingTableRowClickM"
                    :data="siteReceivingTableDate"
                    class="table"
          >
            <el-table-column type="selection" width="40"></el-table-column>
            <el-table-column label="序号" type="index" width="60" fixed="left"/>
            <el-table-column label="单据日期" prop="receivingDate" width="160">
              <template v-slot="scope">
                <span>{{ scope.row.receivingDate.split(' ')[0] }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" width="120" fixed="left">
              <template v-slot="scope">
                <el-tag type="danger" v-show="scope.row.reconciliationType===2">退货对账
                </el-tag>
                <el-tag type="success" v-show="scope.row.reconciliationType===1">发货对账
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="orderSn" label="订单号" width="220"/>
            <el-table-column prop="materialName" label="物资名称" width="200"/>
            <!-- <el-table-column prop="productName" label="商品名称" width="200"/> -->
            <el-table-column prop="spec" label="规格型号"/>
            <el-table-column prop="unit" label="单位"/>
            <el-table-column prop="quantity" label="对账数量"/>
            <el-table-column prop="price" label="含税单价"/>
            <el-table-column prop="noRatePrice" label="不含税单价"/>
            <el-table-column prop="totalAmount" label="含税金额"/>
            <el-table-column prop="noRateAmount" label="不含税金额"/>
            <el-table-column prop="taxAmount" label="税额" width="120px"/>
            <!--                            <el-table-column prop="totalQuantity" label="总数量" width=""></el-table-column>-->
          </el-table>
          <Pagination
              v-show="siteReceivingTableDate != null && siteReceivingTableDate.length > 0"
              :total="paginationInfo2.total"
              :pageSize.sync="paginationInfo2.pageSize"
              :currentPage.sync="paginationInfo2.currentPage"
              @currentChange="currentChangeUser2"
              @sizeChange="sizeChangeUser2"
          />
        </div>
      </div>
      <span slot="footer">
                <el-button type="primary" @click="siteReceivingTableDateSelectAffirmClick">确认选择</el-button>
                <el-button @click="selectMaterialShow = false">取消</el-button>
            </span>
    </el-dialog>
    <el-dialog :visible.sync="imgPreviewDialog" title="图片预览">
      <img class="center mb20" style="display: block" :src="previewImg" alt="">
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <el-dialog
        custom-class="dlg"
        v-dialogDrag :title="selectSupplierOrgNameTitle" :visible.sync="showSupplierOrgNameView" width="80%"
        style="margin-left: 10%;" :close-on-click-modal="false"
    >
      <div class="e-table" v-loading="selectSupplierOrgNameLoading">
        <div class="top dfa" style="height: 50px; padding-left: 10px">
          <el-input
              style="width: 200px; " type="text" @blur="getOrderPlanListM"
              placeholder="输入搜索关键字" v-model="supplierKeywords"
          >
            <img :src="require('@/assets/search.png')" slot="suffix" @click="getOrderPlanListM"/>
          </el-input>
        </div>
        <el-table
            ref="selectTwoSupplierR"
            border
            :data="supplierOrgNameTable"
            class="table"
            :max-height="$store.state.tableHeight"
        >
          <!--<el-table-column type="selection" width="40"></el-table-column>-->
          <el-table-column label="序号" type="index" width="60"/>
          <el-table-column label="操作">
            <template v-slot="scope">
              <div
                  class="pointer" style="color: rgba(33, 110, 198, 1);"
                  @click="submitSupplierOrgName(scope.row)"
              >选择供货单位
              </div>
            </template>
          </el-table-column>
          <el-table-column label="供货单位名称" prop="supplierName"/>
        </el-table>
      </div>
      <!--分页-->
      <span slot="footer">
                    <Pagination
                        :total="paginationInfo1.total"
                        :pageSize.sync="paginationInfo1.pageSize"
                        :currentPage.sync="paginationInfo1.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                </span>
    </el-dialog>
    <Cropper ref="cropperImage" @handleUploadSuccess="handleUploadSuccess"/>

  </div>

</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import Cropper from '@/components/cropper'
import { mapState } from 'vuex'
import $ from 'jquery'
import { spliceImgUrl, toFixed } from '@/utils/common'
import { updateMaterialAndState, updateProductState } from '@/api/platform/product/materialManage'
import { getEnterpriseInfoTaxRate } from '@/api/platform/shop/shopManager'
import { getList } from '@/api/shopManage/shop/shopSupplierRele'
import {
    supplierGetEnterprisePageList
} from '@/api/reconciliation/reconciliation'
import { getReconciliationDtlList, getTwoMaterialOrderList } from '@/api/reconciliation/twoReconciliation'
import { twoReconciliationAmountM, twoReconciliationFixAmountM } from '@/utils/material_reconciliationUtils/twoCompute'
import { saveInBound, saveAndSubmitInBound, updateInBound, updateAndSubmitOutBound, auditInBound } from '@/api/supplierSys/stock/inBoundManage'

export default {
    data () {
        return {
            regionTableIndex: 1,
            imgPreviewDialog: false,
            previewImg: '',
            uploadMax: 10,
            auditRecordList: [],
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
            },
            pages: {
                totalCount: 0,
                currPage: 1,
                pageSize: 20,
            },
            uploadType: null,
            mainImg: '', // 主图
            smallImg: '', // 小图
            dialogImageUrl: '', // 主图
            // 数据加载
            formLoading: false,
            dialogVisible: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            formRules: {
                supplierType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' },
                ],
                accountPeriod: [
                    { required: true, message: '请输入期数', trigger: 'blur' },
                ],
                storedWarehouseTime: [
                    { required: true, message: '请选择入库时间', trigger: 'blur' },
                ],
                invoiceNum: [
                    { required: true, message: '请输入发票字号', trigger: 'blur' },
                ],
                remark: [
                    { required: true, message: '请输入描述', trigger: 'blur' },
                ],
            },
            rowData: null, // 跳转过来的数据
            showForm: false,
            // 商品库
            inventory: {
                tableData: [],
                keyWord: null,
                classId: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 0,
                },
            },
            selectContractOrderLoading: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            uploadImgSize: 10, // 上传文件大小
            //表单数据
            formData: {},

            addForm: {
                formData: {
                    storedWarehouseTime: null,
                    supplierType: null,
                    supplierId: null,
                    supplierName: null,
                    taxRate: null,
                    warehouseId: 1,
                    contractNo: null,
                    rateAmount: null,
                    noRateAmount: null,
                    num: null,
                    accountPeriod: null,
                    invoiceNum: null,
                    attachmentFile: null,
                },
                adminFileLength: 0,
                minFileLength: 0,
                // 地址
                // 计量单位
                numUnitOptions: [],
            },
            warehouseList: [
                {
                    label: '库房一',
                    value: 1,
                },
            ],
            supplierTypeList: [
                {
                    label: '零星采购',
                    value: 1,
                },
                {
                    label: '大宗合同',
                    value: 2
                },
                {
                    label: '周转材料',
                    value: 3
                }
            ],
            // 品牌数据
            brand: {
                classId: null,
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableData: [],
            tableLoading: false,
            selectSupplierOrgNameTitle: null,
            selectPurchasingOrgNameLoading: false,
            selectSupplierOrgNameLoading: false,
            enterpriseKeywords: '',
            supplierKeywords: '',
            showPurchasingOrgNameView: false,
            showSupplierOrgNameView: false,
            purchasingOrgNameTable: [],
            supplierOrgNameTable: [],
            paginationInfo1: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo2: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            keywords2: '',
            keywords3: '',
            siteReceivingLoading: false,
            selectMaterialShow: false,
            contractOrderTableDate: [],
            siteReceivingTableDate: [],
        }
    },
    components: {
        Pagination,

        Cropper
    },
    created () {
        this.addForm.numUnitOptions = this.materialUnit
        this.rowData = this.$route.params.row
        if (this.rowData.viewType == 'add') {
            this.viewType = 'add'
            this.showForm = true
        } else {
            this.viewType = 'view'
            this.addForm.formData = this.rowData
            this.tableData = JSON.parse(this.rowData.settlementInfo)
            this.showForm = true
        }
    },
    mounted () {
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'baseInfos', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        // // const onResize = () => {
        // //     this.screenWidth = document.documentElement.clientWidth - this.topHeight
        // //     this.screenHeight = document.documentElement.clientHeight - this.topHeight
        // //     $idsTop = arr.map(item => {
        // //         console.log('111111', item)
        // //         const itemTop = document.getElementById(item).offsetTop
        // //         return itemTop
        // //     })
        // // }
        // // eslint-disable-next-line no-undef
        // this.winEvent.onResize = throttle(onResize, 500)
        // window.addEventListener('resize', this.winEvent.onResize)
        // if (this.$route.query.name) {
        //     setTimeout(() => {
        //         this.onChangeTab({
        //             name: this.$route.query.name
        //         })
        //         this.tabsName = this.$route.query.name
        //     }, 200)
        // }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['materialUnit']),
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
        'addForm.formData.isZone': {
            handler (val) {
                this.addForm.formData.isZone = parseInt(this.addForm.formData.isZone)
                if (val == 2) {
                    if (this.addForm.formData.zonePriceList == null) {
                        this.addForm.formData.zonePriceList = []
                    }

                }
            }
        }
    },
    methods: {
        getFixed () {
            getList({ keywords: '固定加成率' }).then(res => {
                this.addForm.formData.markUpNum = res.list[0].keyValue2
                this.showForm = true
            })
        },
        taxInPriceChange (index) {
            let regionOption = this.addForm.formData.regionTableData.filter(item => item.index === index)[0]
            regionOption.taxInPrice = this.fixed2(regionOption.taxInPrice)
            if (this.addForm.formData.taxRate != null) {
                regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
            } else {
                this.$message.error('税率不能为空')
                regionOption.taxInPrice = ''
            }
        },
        addRegion () {
            this.addForm.formData.regionTableData.push({
                index: this.regionTableIndex + 1,
                regionName: '区域' + this.regionTableIndex,
                selectAddressOptions: [],
                taxInPrice: '',
                price: '',
                detailAddress: []
            })
            this.regionTableIndex++
        },
        removeOneParamsData (row) {
            if (row.index === 1) {
                let region = this.addForm.formData.regionTableData[0]
                region.taxInPrice = ''
                region.price = ''
                region.selectAddressOptionsAll = []
                region.detailAddress = []
            } else {
                this.addForm.formData.regionTableData = this.addForm.formData.regionTableData.filter(obj => obj.index !== row.index)
            }
        },
        //获取当前企业的企业税率
        getEnterpriseInfoTaxRateM () {
            getEnterpriseInfoTaxRate().then(res => {
                this.addForm.formData.taxRate = res
            })
        },
        siteReceivingTableSelectM (value) {
            console.log(44444, value)
            this.siteReceivingTableSelectRowData = value
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        siteReceivingTableDateSelectAffirmClick () {
            if(!this.siteReceivingTableSelectRowData || this.siteReceivingTableSelectRowData.length == 0) {
                this.$message.warning('物资不能为空')
                return
            }
            this.selectMaterialShow = false
            this.tableData = this.siteReceivingTableSelectRowData
            let prams = twoReconciliationAmountM(this.tableData, this.addForm.formData.taxRate)
            this.tableData = prams.tableData
            this.addForm.formData.contractNo = this.tableData[0].orderSn
            this.addForm.formData.orderSn = this.tableData[0].orderSn
            this.addForm.formData.num = this.tableData.map(item => Number(item.quantity)).reduce((a, b) => a + b, 0)
            this.addForm.formData.taxAmount = prams.taxAmount
            this.addForm.formData.rateAmount = prams.reconciliationAmount
            this.addForm.formData.noRateAmount = prams.reconciliationNoRateAmount
        },
        getReconciliationDtlListM2 () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                twoSupplierOrgId: this.addForm.formData.supplierId,
                userType: 1,
                productType: 10
            }
            if (this.keywords3 != null) {
                params.keywords = this.keywords3
            }
            if (this.orderIds != null && this.orderIds.length > 0) {
                params.orderIds = this.orderIds
            }
            getReconciliationDtlList(params).then(res => {
                this.siteReceivingTableDate = res.list
                let prams = twoReconciliationFixAmountM(this.tableData, this.addForm.formData.taxRate)
                this.tableData = prams.tableData
                this.addForm.formData.taxAmount = prams.taxAmount
                this.addForm.formData.rateAmount = prams.reconciliationAmount
                this.addForm.formData.noRateAmount = prams.reconciliationNoRateAmount
            })
        },
        // 获取获取对应企业的订单货发货单数据数据
        getTwoMaterialOrderListM () {
            let params = {
                page: this.paginationInfo1.currentPage,
                limit: this.paginationInfo1.pageSize,
                supplierId: this.addForm.formData.supplierId,
                productType: 10
            }
            if (this.keywords2 != null) {
                params.keywords = this.keywords2
            }
            this.selectContractOrderLoading = true
            getTwoMaterialOrderList(params).then(res => {
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                this.contractOrderTableDate = res.list
                this.selectContractOrderLoading = false
            }).finally(() => {
                this.selectContractOrderLoading = false
            })
        },
        selectContractOrPlanSelectM (value) {
            this.selectContractOrderDate = value
            if (this.selectContractOrderDate.length > 0) {
                this.orderIds = this.selectContractOrderDate.map(item=>item.parentOrderId)
                this.getReconciliationDtlListM2()
            }else {
                this.siteReceivingTableDate = []
            }

        },
        selectContractOrPlanClickM (selectRow) {
            selectRow.flag = !selectRow.flag
            this.$refs.bidingOrderItemRef.toggleRowSelection(selectRow, selectRow.flag)
        },
        writeTaxRate () {
            if (this.addForm.formData.taxRate != null) {
                if (!(0 <= this.addForm.formData.taxRate && this.addForm.formData.taxRate <= 100)) {
                    this.$message.error('税率不能小于0或大于100')
                    this.addForm.formData.taxRate = 0
                } else {
                    this.addForm.formData.taxRate = this.fixed2(this.addForm.formData.taxRate)//税率保留2位小数
                    if (this.addForm.formData.regionTableData.length > 0) {
                        this.addForm.formData.regionTableData.forEach(r => {
                            r.price = this.fixed2(r.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
                        })
                    }
                }
            } else {
                this.$message.error('税率不能为空')
                this.addForm.formData.taxRate = 0
            }
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        limitZonePrice (row) {
            if (row.zonePrice <= 0 || row.zonePrice >= this.addForm.formData.originalPrice) {
                row.zonePrice = this.fixed2(0)
                this.$message.error('销售价格不能小于0或销售价格不能大于原价')
            } else {
                row.zonePrice = this.fixed2(row.zonePrice)
            }

        },
        handleImgPreview (name) {
            if (name === 'mainImg') {
                this.previewImg = this.mainImg
            } else if (name === 'minImg') {
                this.previewImg = this.smallImg
            }
            this.imgPreviewDialog = true
        },
        delImg (name) {
            if (name === 'mainImg') {
                this.mainImg = ''
                this.addForm.formData.adminFile.url = ''
            } else if (name === 'minImg') {
                this.smallImg = ''
                this.addForm.formData.minFile.url = ''
            }
        },
        // 裁剪完毕上传图片
        handleUploadSuccess (file) {
            if (this.uploadType === 0) {
                this.uploadAdminFile(file)
            } else if (this.uploadType === 1) {
                this.uploadMinFile(file)
            } else if (this.uploadType === 2) {
                this.uploadProductFile(file)
            }
        },
        handleExceed () {
            this.$message.warning('请最多上传 ' + this.uploadMax + ' 个文件。')
        },
        // 修改状态
        updateStateBatch (state, title) {
            let params = {
                productIds: [this.addForm.formData.productId],
                state: state,
                saveAndSubmit: 0
            }
            if (state === 3) {
                // 保存并上架
                this.$refs.formEdit.validate(valid => {
                    if (!valid) return this.$message.error('请检查非空输入框')
                    spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                    let newParams = {
                        productIds: [this.addForm.formData.productId],
                        state: state,
                        ...this.addForm.formData
                    }
                    newParams.state = state
                    params.saveAndSubmit = 1
                    this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                        updateMaterialAndState(newParams).then(res => {
                            this.handleClose()
                            this.message(res)
                        })
                    })
                })
            } else {
                // 下架
                this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                    updateProductState(params).then(res => {
                        this.handleClose()
                        this.message(res)
                    })
                })
            }

        },
        // 选择物资名称
        selectMaterialBtnClick () {
            this.siteReceivingTableDate = []
            this.selectMaterialShow = true
            this.getTwoMaterialOrderListM()
        },
        selectedSupplier () {
            this.selectSupplierOrgNameTitle = '选择供货单位'
            this.getOrderPlanListM()
            this.showSupplierOrgNameView = true
        },
        getOrderPlanListM () {
            let params = {
                page: this.paginationInfo1.currentPage,
                limit: this.paginationInfo1.pageSize,
            }
            if (this.supplierKeywords != null) {
                params.keywords = this.supplierKeywords
            }
            if (this.addForm.formData.supplierType != null) {
                params.productType = this.addForm.formData.supplierType
            }
            this.selectSupplierOrgNameLoading = true
            getList(params).then(res => {
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                this.supplierOrgNameTable = res.list
            }).finally(() => {
                this.selectSupplierOrgNameLoading = false
            })
        },
        //根据发货单位进行分组查询企业名称
        getOrderPlanByOrgNameListM () {
            let params = {
                page: this.paginationInfo1.currentPage,
                limit: this.paginationInfo1.pageSize,
            }
            if (this.enterpriseKeywords != null) {
                params.keywords = this.enterpriseKeywords
            }
            if (this.addForm.formData.supplierType != null) {
                params.productType = this.addForm.formData.supplierType
            }
            this.selectPurchasingOrgNameLoading = true
            supplierGetEnterprisePageList(params).then(res => {
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                this.purchasingOrgNameTable = res.list
            }).finally(() => {
                this.selectPurchasingOrgNameLoading = false
            })
        },

        selectOrgNameSelectM (value) {
            this.selectPurchasingOrgNameRowDate = value
        },
        //选择供货单位
        submitSupplierOrgName (row) {
            this.addForm.formData.supplierId = row.supplierId
            this.addForm.formData.supplierName = row.supplierName
            this.addForm.formData.taxRate = row.taxRate
            this.showSupplierOrgNameView = false
        },
        currentChangeUser (currPage) {
            this.paginationInfo1.currentPage = currPage
            this.getOrderPlanByOrgNameListM()
        },
        sizeChangeUser (pageSize) {
            this.paginationInfo1.pageSize = pageSize
            this.getOrderPlanByOrgNameListM()
        },
        currentChangeUser1 (currPage) {
            this.paginationInfo1.currentPage = currPage
            this.getOrderPlanListM()
        },
        sizeChangeUser1 (pageSize) {
            this.paginationInfo1.pageSize = pageSize
            this.getOrderPlanListM()
        },
        currentChangeUser2 (currPage) {
            this.paginationInfo2.currentPage = currPage
            this.getSiteReceivingTableDateM()
        },
        sizeChangeUser2 (pageSize) {
            this.paginationInfo2.pageSize = pageSize
            this.getSiteReceivingTableDateM()
        },
        // 提交
        save () {
            if(!this.tableData || this.tableData.length == 0) {
                this.$message.warning('物资不能为空')
                return
            }
            this.addForm.formData.settlementInfo = JSON.stringify(this.tableData)
            this.$refs.formEdit.validate(valid => {
                if (!valid) return this.$message.error('请检查非空输入框')
                if (this.viewType === 'add') {
                    this.formLoading = true
                    saveInBound(this.addForm.formData).then(res => {
                        if (res.code != null && res.code != 200) {
                            return this.formLoading = false
                        }
                        let classInfo = {
                            classPath: this.addForm.formData.classPath,
                            classId: this.addForm.formData.classId
                        }
                        // 重置
                        // this.resetFormData()
                        // 恢复分类
                        this.addForm.formData.classPath = classInfo.classPath
                        this.addForm.formData.classId = classInfo.classId
                        /*this.$refs.adminFileRef.clearFiles()
this.$refs.productFileRef.clearFiles()
this.$refs.minFileRef.clearFiles()*/
                        this.addForm.minFileLength = 0
                        this.addForm.adminFileLength = 0
                        this.message(res)
                    }).finally(() => {
                        this.formLoading = false
                    })
                } else {
                    this.formLoading = true
                    updateInBound(this.addForm.formData).then(res => {
                        this.getMaterialInfo()
                        this.message(res)
                    }).catch(() => {
                        this.getMaterialInfo()
                    }).finally(() => this.formLoading = false)
                }
            })
        },
        saveAndSubmit () {
            if(!this.tableData || this.tableData.length == 0) {
                this.$message.warning('物资不能为空')
                return
            }
            this.addForm.formData.settlementInfo = JSON.stringify(this.tableData)
            this.$refs.formEdit.validate(valid => {
                if (!valid) return this.$message.error('请检查非空输入框')
                if (this.viewType === 'add') {
                    this.formLoading = true
                    saveAndSubmitInBound(this.addForm.formData).then(res => {
                        if (res.code != null && res.code != 200) {
                            return this.formLoading = false
                        }
                        let classInfo = {
                            classPath: this.addForm.formData.classPath,
                            classId: this.addForm.formData.classId
                        }
                        // 重置
                        // this.resetFormData()
                        // 恢复分类
                        this.addForm.formData.classPath = classInfo.classPath
                        this.addForm.formData.classId = classInfo.classId
                        /*this.$refs.adminFileRef.clearFiles()
this.$refs.productFileRef.clearFiles()
this.$refs.minFileRef.clearFiles()*/
                        this.addForm.minFileLength = 0
                        this.addForm.adminFileLength = 0
                        this.message(res)
                    }).finally(() => {
                        this.formLoading = false
                    })
                } else {
                    this.formLoading = true
                    updateAndSubmitOutBound(this.addForm.formData).then(res => {
                        this.getMaterialInfo()
                        this.message(res)
                    }).catch(() => {
                        this.getMaterialInfo()
                    }).finally(() => this.formLoading = false)
                }
            })
        },
        reject () {
            let params = {
                id: this.addForm.formData.id,
                state: 3
            }
            auditInBound(params).then(res=>{
                this.getMaterialInfo()
                this.message(res)
            })
        },
        approved: function () {
            let params = {
                id: this.addForm.formData.id,
                state: 2
            }
            auditInBound(params).then(res => {
                this.getMaterialInfo()
                this.message(res)
            })
        },
        //取消
        handleClose () {
            this.$router.replace('/supplierSys/analysis/inboundManagement')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 重置数据
        resetFormData () {
            this.addForm.selectAddressOptions = []
            this.addForm.formData = {
                storedWarehouseTime: null,
                supplierType: null,
                supplierId: null,
                supplierName: null,
                taxRate: null,
                warehouseId: 1,
                contractNo: null,
                rateAmount: null,
                noRateAmount: null,
                num: null,
                accountPeriod: null,
                invoiceNum: null,
                attachmentFile: null,
            }
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        currentChange () {

        },
        sizeChange () {

        },
        handleCurrentChange () {

        },
        handleSelectionChange () {

        },
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.e-table {
  min-height: auto;
  background: #fff;
}

#tabs-content {
  padding-bottom: 70px !important;
}

/deep/ .el-dialog .el-dialog__body {
  height: unset !important;
  margin: 0 20px;
}

/deep/ .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  width: 148px;
  height: 148px;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  .el-icon-delete {
    margin-left: 10px;
    line-height: unset;
    color: #e9513e;
  }

  .cover {
    width: 100%;
    height: 100%;
    position: relative;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
  }

  &:hover {
    border-color: #409EFF;

    .cover {
      display: block;
    }
  }

  img, & > i {
    width: 148px;
    height: 148px;
  }

  i {
    color: #8c939d;
    line-height: 148px;
    text-align: center;
  }

  img {
    object-fit: cover;
    display: block;
  }
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
  display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
  display: none;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 300px;
    margin-top: 0;
  }
}
/deep/ .el-dialog.dlg {
  height: 600px;

  .el-dialog__header {
    margin-bottom: 0;
  }

  .el-dialog__body {
    height: 474px;
    margin: 10px;
    display: flex;

    & > div {
      .e-pagination {
        background-color: unset;
      }

      //height: 670px;
      .title {
        height: 22px;
        margin-bottom: 10px;
        padding-left: 26px;
        text-align: left;
        line-height: 22px;
        color: #2e61d7;
        font-weight: bold;
        position: relative;
        display: flex;

        &::before {
          content: '';
          display: block;
          width: 10px;
          height: inherit;
          border-radius: 5px;
          background-color: blue;
          position: absolute;
          left: 10px;
          top: 0;
        }
      }
    }

    .el-input__inner {
      border: 1px solid blue;
      border-radius: 6px;
    }

    .el-input__suffix {
      width: 20px;
    }

    .e-table {
      flex-grow: 1;

      .table {
        height: 100%;
      }
    }
    .el-table__empty-block {
      min-height: 500px;
    }

    .box-left {
      width: 660px;
      display: flex;
      flex-direction: column;

      .top {
        box-shadow: unset;
      }
    }

    .box-right {
      flex-grow: 1;
      display: flex;
      flex-direction: column;

      & > div {
        display: flex;
        flex-direction: column;
      }

      .top {
        justify-content: left;
        border-radius: 0;
        box-shadow: unset;
      }

      .bottom {
        flex-grow: 1;
      }
    }
  }

  .el-dialog__footer {
    background-color: #eff2f6;
  }
}

</style>
