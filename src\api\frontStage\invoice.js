import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

// 订单查询
const InvoiceList = params => {
    return httpPost({
        url: '/materialMall/userCenter/invoice/listByEntity',
        params,
    })
}
const addDate = params => {
    return httpPost({
        url: '/materialMall/userCenter/invoice/addDate',
        params,
    })
}
const getDate = params => {
    return httpGet({
        url: '/materialMall/userCenter/invoice/getDate',
        params,
    })
}
export {
    InvoiceList,
    addDate,
    getDate

}