{"name": "material-purchase", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "pushTest": "vue-cli-service build && scp -r dist/* root@***************:/home/<USER>/html", "test": "scp -r dist/* root@***************:/home/<USER>/html", "testHV": "scp -r dist/* root@**************:/home/<USER>/nginx/html", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.6.5", "bignumber.js": "^9.1.1", "crypto-js": "^4.1.1", "decimal.js": "^10.6.0", "echarts": "^4.9.0", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "html2pdf.js": "^0.10.3", "jquery": "^3.7.1", "js-base64": "^3.7.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.30.1", "pdfjs-dist": "2.7.570", "qrcodejs2": "0.0.2", "quill": "^1.3.7", "quill-image-drop-module": "^1.0.3", "quill-image-extend-module": "^1.1.2", "quill-image-resize-module": "^3.0.0", "sortablejs": "^1.15.0", "vue": "^2.6.11", "vue-print-nb": "^1.7.4", "vue-cropper": "^0.6.4", "vue-piczoom": "^1.0.6", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vuex": "^3.4.0", "vuex-persist": "^3.1.3", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "^4.5.19", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "6.1.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "glob-all": "^3.2.1", "lint-staged": "^9.5.0", "node-sass": "^4.14.1", "prettier": "^2.2.1", "purgecss-webpack-plugin": "^4.0.3", "sass-loader": "^8.0.2", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "^2.6.11"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "description": "```\r Git 全局设置\r git config --global user.name \"username\"\r git config --global user.email \"email addr\"\r ```\r ### 已存在的文件夹或 Git 仓库\r ```\r cd existing_folder\r git init\r git remote <NAME_EMAIL>\r git add .\r git commit\r git push -u origin master\r ```\r ### 忽略本地修改\r ```\r git update-index --assume-unchanged vue.config.js\r git update-index --no-assume-unchanged\r ```\r ### 生成本机ssh key\r ```\r ssh-keygen -t rsa -C <EMAIL>\r 三次回车\r cat ~/.ssh/id_rsa.pub\r 拷贝生成的字符串到设置界面中有ssh公钥中\r ```", "main": ".eslintrc.js", "author": "", "license": "ISC"}