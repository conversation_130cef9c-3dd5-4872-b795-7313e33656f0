import service from '@/utils/request'

const { httpPost, httpGet } = service

const treeByName = params => {
    return httpGet({
        url: '/materialMall/platform/productCategory/listByClassName',
        params
    })
}
const platforUploadMaterialExcelFile = params => {
    return httpPost({
        url: '/materialMall/platform/productCategory/platforUploadMaterialExcelFile',
        params,
    })
}
const edit = params => {
    return httpPost({
        url: '/materialMall/platform/productCategory/update',
        params
    })
}

const editBatch = params => {
    return httpPost({
        url: '/materialMall/platform/productCategory/update/sate',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/productCategory/create',
        params
    })
}

const del = params => {
    return httpPost({
        url: '/materialMall/platform/productCategory/deleteBatch',
        params
    })
}

export {
    edit,
    create,
    del,
    treeByName,
    platforUploadMaterialExcelFile,
    editBatch,
}