import service from '@/utils/request'

const { httpPost, httpGet } = service

const contractList = params => {
    return httpPost({
        url: '/supplierSys/outApi/listByLeaseBuy',
        params
    })
}
/* pcwp1物资采购合同*/
const contractPcwp1List = params => {
    return httpPost({
        url: '/supplierSys/outApi/pcwp1/wzContractList',
        params
    })
}
//合同服务清单
const getDataDtlList = params => {
    return httpGet({
        url: '/thirdapi/purchase/contract/getMaterialProcurementForServerList',
        params
    })
}
//合同物资清单
const getcontractMaterialsDtlTableList = params => {
    return httpGet({
        url: '/thirdapi/purchase/contract/getMaterialProcurementForMaterialList',
        params
    })
}

export {
    contractList,
    getDataDtlList,
    getcontractMaterialsDtlTableList,
    contractPcwp1List

}