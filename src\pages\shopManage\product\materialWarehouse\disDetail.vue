<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div v-loading='formLoading' class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs v-model="tabsName" tab-position="left" @tab-click="onChangeTab">
                <el-tab-pane :disabled="clickTabFlag" label="物资" name="baseInfo">
                </el-tab-pane>
                <div id="tabs-content">
                    <div id="baseInfCon" class="con">
                        <div id="baseInfo" class="tabs-title">物资</div>
                        <!--新增-->
                        <div v-if="showForm" class="form">
                            <el-form
                                ref="formEdit" :model="addForm.formData" :rules="formRules"
                                class="demo-ruleForm" label-width="200px"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="分类：" prop="classId">
                                            <category-cascader
                                                :disabled="viewType != 'add'"
                                                :catelogPath="addForm.formData.classPath"
                                                :classId.sync='addForm.formData.classId'
                                                :classPath.sync="addForm.formData.classPath" :productType="0"
                                                @change="resetRelevanceAndBrand"
                                            ></category-cascader>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="12">
                                        <el-form-item label="商品名称：" prop="productName">
                                            <el-input disabled
                                                v-model="addForm.formData.productName" clearable
                                                placeholder="请输入名称"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="关键字（,分隔）：">-->
                                    <!--                                            <el-input placeholder="请输入关键字" clearable  v-model="addForm.formData.productKeyword"></el-input>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="物资名称：" prop="relevanceName">
<!--                                              v-model="addForm.formData.relevanceName" disabled-->
                                            <el-input
                                                v-model="addForm.formData.relevanceName"
                                                placeholder="请选择物资"
                                            ></el-input>
                                            <el-button
                                                size="mini" type="primary" :disabled="viewType != 'add'"
                                                @click="importDeviceSelect"
                                            >选择
                                            </el-button>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" v-show="this.viewType != 'add'">
                                        <el-form-item label="商品编码：">
                                            <el-input disabled v-model="addForm.formData.serialNum"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="品牌：" prop="brandName">
                                            <el-input
                                                v-model="addForm.formData.brandName" disabled placeholder="请选择品牌"
                                            ></el-input>
                                            <el-button size="mini" type="primary" @click="brandDialog">选择</el-button>
                                        </el-form-item>
                                    </el-col>
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="结算价" prop="settlePrice">-->
                                    <!--                                            <el-input clearable type="number" oninput="if(value.length > 16)value = value.slice(0, 16)" v-model="addForm.formData.settlePrice"></el-input>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                    <el-col :span="12">
                                        <el-form-item label="排序：">
                                            <el-input disabled
                                                v-model="addForm.formData.shopSort" clearable
                                                oninput="if(value.length > 10)value = value.slice(0, 10)"
                                                placeholder="请输入排序"
                                                type="number"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="规格：" prop="skuName">
                                            <el-input disabled
                                                v-model="addForm.formData.skuName" clearable placeholder="请输入规格"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="成本价" prop="costPrice">
                                            <el-input disabled
                                                v-model="addForm.formData.costPrice" clearable
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                placeholder="请输入成本价"
                                                type="number"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="库存：" prop="stock">
                                            <el-input disabled
                                                v-model="addForm.formData.stock" clearable
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                placeholder="请输入库存"
                                                type="number"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="计量单位" prop="unit">
                                            <el-select disabled
                                                v-model="addForm.formData.unit" filterable placeholder="请选择计量单位"
                                                @change="numUnitChange"
                                            >
                                                <el-option
                                                    v-for="item in addForm.numUnitOptions" :key="item.value"
                                                    :label="item.label" :value="item.label"
                                                >
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="原价：" prop="originalPrice">
                                            <el-input disabled
                                                v-model="addForm.formData.originalPrice" clearable
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                placeholder="请输入原价"
                                                type="number"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="销售价格" prop="sellPrice">
                                            <el-input disabled
                                                v-model="addForm.formData.sellPrice" clearable
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                placeholder="请输入销售价格"
                                                type="number"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!--                                <el-row>-->
                                <!--                                    <el-col :span="12">-->
                                <!--                                        <el-form-item label="地址：" prop="province">-->
                                <!--                                            <el-cascader-->
                                <!--                                                size="large"-->
                                <!--                                                :options="addForm.addressData"-->
                                <!--                                                v-model="addForm.selectAddressOptions"-->
                                <!--                                                @change="handleAddressChange">-->
                                <!--                                            </el-cascader>-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                    <el-col :span="12">-->
                                <!--                                        <el-form-item label="详细地址" prop="detailedAddress">-->
                                <!--                                                <el-input placeholder="请输入详细地址" clearable v-model="addForm.formData.detailedAddress"></el-input>-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                </el-row>-->

                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item
                                            class="uploader" label="物资主图（推荐654x490）：" prop="adminFile" required
                                        >
                                            <el-upload disabled
                                                ref="adminFileRef"
                                                :auto-upload="false" :before-upload="handleBeforeUpload"
                                                :class="addForm.adminFileLength === 1 ? 'hide_box_admin' : ''"
                                                :file-list="addForm.formData.adminFile"
                                                :limit="1" :on-change="adminFileChange" :on-remove="adminFileRemove"
                                                action="fakeaction" list-type="picture-card"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item disabled
                                            class="uploader" label="物资图片（推荐654x490）：" prop="productFiles" required
                                        >
                                            <el-upload
                                                ref="productFileRef" :auto-upload="false"
                                                :before-upload="handleBeforeUpload"
                                                :file-list="addForm.formData.productFiles"
                                                :limit="uploadMax" :multiple="true"
                                                :on-change="productFileChange" :on-exceed="handleExceed"
                                                :on-remove="productFileRemove"
                                                action="fakeaction" list-type="picture-card"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item disabled
                                            class="uploader" label="物资小图 （推荐250x200）：" prop="minFile" required
                                        >
                                            <el-upload
                                                ref="minFileRef"
                                                :auto-upload="false" :before-upload="handleBeforeUpload"
                                                :class="addForm.minFileLength === 1 ? 'hide_box_min' : ''"
                                                :file-list="addForm.formData.minFile"
                                                :on-change="minFileChange" :on-remove="minFileRemove"
                                                action="fakeaction" list-type="picture-card"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="物资描述：" prop="productDescribe">
                                            <editor disabled v-model="addForm.formData.productDescribe"></editor>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-tabs>
            <div class="buttons">
                <!--<template v-if="viewType != 'add'">-->
                <!--    <el-button-->
                <!--        v-if="[0, 2].includes(addForm.formData.state)"-->
                <!--        type="success"-->
                <!--        @click="updateStateBatch(3,'保存并上架')"-->
                <!--    >保存并上架-->
                <!--    </el-button>-->
                <!--    <el-button-->
                <!--        v-else-if="addForm.formData.state===1"-->
                <!--        type="danger"-->
                <!--        @click="updateStateBatch(2,'下架')"-->
                <!--    >下架-->
                <!--    </el-button>-->
                <!--</template>-->
                <!--<el-button type="success" @click="submit">保存</el-button>-->
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
        <!--表格-->
        <el-dialog
            v-dialogDrag v-loading="brandTableLoading" :close-on-click-modal="false" :visible.sync="showBrandDialog"
            style="margin-left: 20%;" title="选择品牌" width="60%"
        >
            <!--<div class="box-left">-->
            <!--    <select-material-class @classNodeClick="classNodeClick" ref="materialClassRef" :productType = "0"/>-->
            <!--</div>-->
            <div class="e-table box-right" style="background-color: #fff">
                <div class="top" style="height: 50px;">
                    <div class="left">
                        <el-input
                            v-model="brand.keywords" placeholder="输入搜索关键字" style="width: 200px" type="text"
                            @blur="getBrandTableData"
                        >
                            <img slot="suffix" :src="require('@/assets/search.png')" @click="getBrandTableData"/>
                        </el-input>
                    </div>
                </div>
                <el-table
                    ref="tableRef" :data="brand.tableData" :max-height="$store.state.tableHeight" border class="table"
                    highlight-current-row style="width: 100%" @row-click="handleCurrentClick"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="品牌名" prop="name" width="150"></el-table-column>
                    <!--                    <el-table-column prop="logo" label="品牌logo" width="90"></el-table-column>-->
                    <el-table-column label="介绍" prop="descript"></el-table-column>
                    <el-table-column label="创建时间" prop="gmtCreate" width="160"></el-table-column>
                    <el-table-column label="更新时间" prop="gmtModified" width="160"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="brand.tableData && brand.tableData.length > 0"
                    :currentPage.sync="brand.paginationInfo.currentPage"
                    :pageSize.sync="brand.paginationInfo.pageSize" :total="brand.paginationInfo.total"
                    @currentChange="getBrandTableData" @sizeChange="getBrandTableData"
                />
                <el-button style="margin-top: 20px" @click="showBrandDialog = false">取消</el-button>
            </span>

        </el-dialog>
        <!--选择物资库-->
        <el-dialog
            v-dialogDrag v-loading="inventoryTableLoading" :close-on-click-modal="false"
            :visible.sync="showDeviceDialog"
            style="margin-left: 20%;" title="选择物资库" width="70%"
        >
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            v-model="inventory.keyWord" clearable placeholder="输入搜索关键字" type="text"
                            @blur="getDeviceInventory"
                        >
                            <img slot="suffix" :src="require('@/assets/search.png')" @click="getDeviceInventory"/>
                        </el-input>
                    </div>
                </div>
                <el-table
                    ref="tableRef" :data="inventory.tableData" :max-height="$store.state.tableHeight" border
                    class="table"
                    highlight-current-row style="width: 100%" @row-click="handleCurrentInventoryClick"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="编号" prop="billNo" width="200"></el-table-column>
                    <el-table-column label="名称" prop="materialName" width="200"></el-table-column>
                    <el-table-column label="规格型号" prop="spec"></el-table-column>
                    <el-table-column label="类别名称" prop="className"></el-table-column>
                    <el-table-column label="计量单位" prop="unit"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="inventory.tableData != null || inventory.tableData.length != 0"
                    :currentPage.sync="inventory.paginationInfo.currentPage"
                    :pageSize.sync="inventory.paginationInfo.pageSize"
                    :total="inventory.paginationInfo.total" @currentChange="getDeviceInventory"
                    @sizeChange="getDeviceInventory"
                />
                <el-button style="margin-top: 20px" @click="showDeviceDialog = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import CategoryCascader from '@/components/category-cascader'
import editor from '@/components/quillEditor'
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import $ from 'jquery'
import { addImgUrl, spliceImgUrl, throttle } from '@/utils/common'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { getBrandPageList } from '@/api/shopManage/brand/brand'
import { uploadFile } from '@/api/platform/common/file'
import { createMaterial, getMaterialInfo, updateMaterial } from '@/api/shopManage/product/materialManage'
import { queryPageMaterialDtl } from '@/api/shopManage/product/prodcutInventory'
import { updateMaterialAndState, updateProductState } from '@/api/platform/product/materialManage'

export default {
    data () {
        return {
            uploadMax: 10,
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
            },
            // 数据加载
            formLoading: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            formRules: {
                relevanceName: [
                    { required: true, message: '请选择名称', trigger: 'blur' },
                ],
                // brandName: [
                //     { required: true, message: '请选择品牌', trigger: 'blur' },
                // ],
                productName: [
                    { required: true, message: '请输入物资名称', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                classId: [
                    { required: true, message: '请选择分类', trigger: 'blur' },
                ],
                // productMinPrice: [
                //     { required: true, message: '请输入最低价格', trigger: 'blur' },
                //     { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                // ],
                settlePrice: [
                    { required: true, message: '请输入结算价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                skuName: [
                    { required: true, message: '请输入规格', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                stock: [
                    { required: true, message: '请输入库存', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,3})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                costPrice: [
                    { required: true, message: '请输入成本价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                unit: [
                    { required: true, message: '请选择计量单位', trigger: 'blur' },
                ],
                originalPrice: [
                    { required: true, message: '请输入原价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                sellPrice: [
                    { required: true, message: '请输入销售价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                // province: [
                //     { required: true, message: '请选择地址', trigger: 'blur' },
                // ],
                // detailedAddress: [
                //     { required: true, message: '请输入详细地址', trigger: 'blur' },
                //     { min: 1, max: 250, message: '超出范围', trigger: 'blur' }
                // ],
                adminFile: [
                    { required: true, message: '请上传文件', trigger: 'blur' },
                ],
                productFiles: [
                    { required: true, message: '请上传文件', trigger: 'blur' },
                ],
                minFile: [
                    { required: true, message: '请上传文件', trigger: 'blur' },
                ],
                productDescribe: [
                    { required: true, message: '请输入描述', trigger: 'blur' },
                ],
            },
            rowData: null, // 跳转过来的数据
            scope: null, // 跳转过来的数据
            showForm: false,
            // 商品库
            inventory: {
                tableData: [],
                keyWord: null,
                classId: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 0,
                },
            },
            showDeviceDialog: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            uploadImgSize: 10, // 上传文件大小
            //表单数据
            formData: {},
            addForm: {
                formData: {
                    productType: 0,
                    relevanceName: null,
                    productName: null,
                    productKeyword: null,
                    classId: null,
                    classPath: [],
                    productMinPrice: null,
                    brandId: null,
                    brandName: null,
                    shopSort: null,
                    skuName: null,
                    settlePrice: null,
                    costPrice: null,
                    stock: 1,
                    unit: null,
                    originalPrice: null,
                    sellPrice: null,
                    province: null,
                    city: null,
                    county: null,
                    detailedAddress: null,
                    productFiles: [],
                    minFile: [],
                    adminFile: [],
                    productDescribe: null,
                },
                adminFileLength: 0,
                minFileLength: 0,
                // 地址
                addressData: regionData, // 地址数据
                selectAddressOptions: [], // 地址选择
                // 计量单位
                numUnitOptions: [],
            },
            // 品牌数据
            brand: {
                classId: null,
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    components: {
        Pagination,
        CategoryCascader,
        editor,
    },
    created () {
        this.addForm.numUnitOptions = this.materialUnit
        this.rowData = this.$route.params.row
        this.scope = this.$route.params.scope
        if (this.rowData.viewType == 'add') {
            this.viewType = 'add'
            if (this.rowData.classPath != null) {
                this.addForm.formData.classPath = this.rowData.classPath
                this.addForm.formData.classId = this.rowData.classPath[this.rowData.classPath.length - 1]
            }
            this.showForm = true
        } else {
            this.getMaterialInfo()
        }
    },
    mounted () {
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['materialUnit']),
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        resetRelevanceAndBrand () {
            let arr = ['relevanceName', 'brandName', 'brandId', 'relevanceId']
            arr.forEach(item => this.addForm.formData[item] = '')
        },
        handleExceed () {
            this.$message.warning('请最多上传 ' + this.uploadMax + ' 个文件。')
        },
        // 修改状态
        updateStateBatch (state, title) {
            let params = {
                productIds: [this.addForm.formData.productId],
                state: state
            }
            params.saveAndSubmit = 0
            if (state === 3) {
                // 保存并上架
                this.$refs.formEdit.validate(valid => {
                    if (valid) {
                        spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                        let newParams = {
                            productIds: [this.addForm.formData.productId],
                            state: state,
                            ...this.addForm.formData
                        }
                        newParams.state = state
                        params.saveAndSubmit = 1
                        this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                            updateMaterialAndState(newParams).then(res => {
                                this.handleClose()
                                this.message(res)
                                return
                            })
                        })

                    } else {
                        this.$message({
                            message: '请检查非空输入框',
                            type: 'error'
                        })
                    }
                })
            } else {
                // 下架
                this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                    updateProductState(params).then(res => {
                        this.handleClose()
                        this.message(res)
                    })
                })

            }

        },
        // 获取物资详情
        getMaterialInfo () {
            let params = {
                productId: this.rowData.productId,
                productType: 0
            }
            this.formLoading = true
            getMaterialInfo(params).then(res => {
                this.addressFormatShow(res)
                addImgUrl(res, this.imgUrlPrefixAdd)
                this.addForm.minFileLength = res.minFile.length
                this.addForm.adminFileLength = res.adminFile.length
                this.addForm.formData = res
                this.inventory.classId = this.addForm.formData.classId
                this.showForm = true
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 计量单位选择
        numUnitChange (value) {
            this.addForm.formData.unit = value
        },
        // 地址回显
        addressFormatShow (row) {
            if (row.province == null) {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.addForm.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 物资库点击
        handleCurrentInventoryClick (row) {
            this.addForm.formData.relevanceName = row.materialName
            this.addForm.formData.relevanceId = row.billId
            this.addForm.formData.relevanceNo = row.billNo
            this.showDeviceDialog = false
        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                isActive: 1,
                pageIndex: this.inventory.paginationInfo.currentPage,
                pageSize: this.inventory.paginationInfo.pageSize,
            }
            if (this.inventory.classId != null) {
                params.classId = this.inventory.classId
            }
            if (this.inventory.keyWord != null) {
                params.keyWord = this.inventory.keyWord
            }
            this.inventoryTableLoading = true
            queryPageMaterialDtl(params).then(res => {
                this.inventory.tableData = res.list
                this.inventory.paginationInfo.total = res.totalCount
                this.inventory.paginationInfo.pageSize = res.pageSize
                this.inventory.paginationInfo.currentPage = res.currPage
                this.inventoryTableLoading = false
            }).catch(() => {
                this.inventoryTableLoading = false
            })
        },
        // 选择物资名称
        importDeviceSelect () {
            if (this.addForm.formData.classId == null || this.addForm.formData.classId == '') {
                return this.$message.error('请先选择分类')
            } else {
                this.inventory.classId = this.addForm.formData.classId
            }
            this.showDeviceDialog = true
            this.getDeviceInventory()
        },

        // 提交
        submit () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                    if (this.viewType === 'add') {
                        this.formLoading = true
                        createMaterial(this.addForm.formData).then(res => {
                            if (res.code != null && res.code != 200) {
                                addImgUrl(this.addForm.formData, this.imgUrlPrefixAdd)
                                this.formLoading = false
                                return
                            }
                            let classInfo = {
                                classPath: this.addForm.formData.classPath,
                                classId: this.addForm.formData.classId
                            }
                            // 重置
                            this.resetFormData()
                            // 恢复分类
                            this.addForm.formData.classPath = classInfo.classPath
                            this.addForm.formData.classId = classInfo.classId
                            this.$refs.adminFileRef.clearFiles()
                            this.$refs.productFileRef.clearFiles()
                            this.$refs.minFileRef.clearFiles()
                            this.addForm.minFileLength = 0
                            this.addForm.adminFileLength = 0
                            this.formLoading = false
                            this.message(res)
                        }).catch(() => {
                            this.formLoading = false
                        })
                    } else {
                        this.formLoading = true
                        updateMaterial(this.addForm.formData).then(res => {
                            this.getMaterialInfo()
                            this.formLoading = false
                            this.message(res)
                        }).catch(() => {
                            this.getMaterialInfo()
                            this.formLoading = false
                        })
                    }
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
        // 地区
        handleAddressChange () {
            let addArr = this.addForm.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.addForm.formData.province = province
            this.addForm.formData.city = city
            this.addForm.formData.county = county
            this.addForm.formData.detailedAddress = province + city + county
        },
        // 小图上传
        minFileChange (file, fileList) {
            if (!(file.size / 1024 / 1024 < this.uploadImgSize)) {
                fileList.pop()
                return this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            fileList.pop()
            this.uploadMinFile(file, fileList)
        },
        // 物资主图上传
        adminFileChange (file, fileList) {
            if (!(file.size / 1024 / 1024 < this.uploadImgSize)) {
                fileList.pop()
                return this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            fileList.pop()
            this.uploadAdminFile(file, fileList)
        },
        // 物资图片上传
        productFileChange (file, fileList) {
            if (!(file.size / 1024 / 1024 < this.uploadImgSize)) {
                fileList.pop()
                return this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            fileList.pop()
            this.uploadProductFile(file, fileList)
        },
        // 品牌单选
        handleCurrentClick (row) {
            this.addForm.formData.brandId = row.brandId
            this.addForm.formData.brandName = row.name
            this.showBrandDialog = false
        },
        // 分类点击
        classNodeClick (data) {
            this.brand.classId = data.classId
            this.getBrandTableData()
        },
        // 获取品牌表格
        getBrandTableData () {
            let params = {
                page: this.brand.paginationInfo.currentPage,
                limit: this.brand.paginationInfo.pageSize,
            }
            if (this.brand.keywords != null) {
                params.name = this.brand.keywords
            }
            if (this.brand.classId != null) {
                params.classId = this.brand.classId
            }
            this.brandTableLoading = true
            getBrandPageList(params).then(res => {
                this.brand.tableData = res.list
                this.brand.paginationInfo.total = res.totalCount
                this.brand.paginationInfo.pageSize = res.pageSize
                this.brand.paginationInfo.currentPage = res.currPage
                this.brandTableLoading = false
            }).catch(() => {
                this.brandTableLoading = false
            })
        },
        brandDialog () {
            if (this.addForm.formData.classId == null || this.addForm.formData.classId == '') {
                return this.$message.error('请先选择分类')
            } else {
                this.brand.classId = this.addForm.formData.classId
            }
            this.showBrandDialog = true
            this.getBrandTableData()
        },
        uploadFileInfo (params) {
            const form = new FormData()
            form.append('files', params.raw)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        // 上传主图
        uploadAdminFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.addForm.adminFileLength = 1
            this.formLoading = true
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 1
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 0
                this.addForm.formData.adminFile.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.formLoading = false
            }).catch(() => {
                this.addForm.adminFileLength = 0
                this.formLoading = false
            })
        },
        // 上传物资图片
        uploadProductFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.formLoading = true
            uploadFile(form).then(res => {
                let productFile = {}
                productFile.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                productFile.name = res[0].objectName
                productFile.isMain = 0
                productFile.relevanceType = 1
                productFile.fileType = 1
                productFile.fileFarId = res[0].recordId
                productFile.imgType = 0
                this.addForm.formData.productFiles.push(productFile)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 上传小图
        uploadMinFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.addForm.minFileLength = 1
            this.formLoading = true
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 0
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 1
                this.addForm.formData.minFile.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.formLoading = false
            }).catch(() => {
                this.addForm.minFileLength = 0
                this.formLoading = false
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 主图删除
        adminFileRemove (file, fileList) {
            this.addForm.adminFileLength = fileList.length
            let recordId = this.addForm.formData.adminFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.adminFile = []
            })
        },
        // 物资图片删除
        productFileRemove (file) {
            let files = this.addForm.formData.productFiles
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.productFiles = newFiles
            })

        },
        // 小图删除
        minFileRemove (file, fileList) {
            this.addForm.minFileLength = fileList.length
            let recordId = this.addForm.formData.minFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.minFile = []
            })
        },
        //取消
        handleClose () {
            this.$router.push({ path: '/supplierSys/shop/selectSupplier', query: { scope: this.scope } })
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 重置数据
        resetFormData () {
            this.addForm.selectAddressOptions = []
            this.addForm.formData = {
                productType: 0,
                relevanceName: null,
                productName: null,
                productKeyword: null,
                classId: null,
                classPath: [],
                productMinPrice: null,
                brandId: null,
                brandName: null,
                shopSort: null,
                skuName: null,
                settlePrice: null,
                costPrice: null,
                stock: 1,
                unit: null,
                originalPrice: null,
                sellPrice: null,
                province: null,
                city: null,
                county: null,
                detailedAddress: null,
                productFiles: [],
                adminFile: [],
                minFile: [],
                productDescribe: null,
            }
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

.e-table {
    min-height: auto;
    background: #fff;
}

#tabs-content {
    padding-bottom: 70px !important;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
    display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 300px;
        margin-top: 0px;
    }
}

</style>
