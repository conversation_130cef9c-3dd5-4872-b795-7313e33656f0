<template>
    <div class="base-page">
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                  <div class="left">
                    <div class="left-btn">
                      <el-button type="primary" @click="changePublishState(1)">批量展示</el-button>
                      <el-button type="primary"  class="btn-delete" @click="changePublishState(2)">批量取消展示</el-button>
                    </div>
                  </div>
                    <div class="search_box">
                        <el-input clearable type="text" @keyup.enter.native="getSendList" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="getSendList" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table">
                <el-table
                    class="table"
                    ref="receive"
                    :height="rightTableHeight"
                    :data="sendMessageTable"
                    border
                    highlight-current-row
                    @row-click="handleCurrentInventoryClick"
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="操作" width="80">
                        <template v-slot="scope">
                            <span class="action" @click="onDel(scope)">
                                <img src="../../../../assets/btn/delete.png" alt="">
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="留言" width="" prop="title">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.messagContent }}</span>
                        </template>
                    </el-table-column>
                  <el-table-column label="留言人名称" width="" prop="messageName"/>
<!--                  <el-table-column label="昵称" width="" prop=""/>-->
<!--                  <el-table-column label="是否内部" width="" prop=""/>-->
<!--                  <el-table-column label="公司名称" width="" prop=""/>-->
                  <el-table-column label="留言时间" width="" prop="messagDate"/>
                  <el-table-column label="回复时间" width="" prop="respondDate"/>
                  <el-table-column label="公开展示" width="">
                    <template v-slot="scope">
                      <div v-if="scope.row.publicDisplay==0"  >不展示</div>
                      <div v-if="scope.row.publicDisplay==1" >展示</div>
                    </template>
                  </el-table-column>
<!--                  <el-table-column label="回复" width="" prop="提交回复" @click="handleView(scope.row)"/>-->
                  <el-table-column label="状态"  width="120">
                    <template v-slot="scope">
                      <el-tag v-if="scope.row.state==0" type="danger" @click="changeState">未处理</el-tag>
                      <el-tag v-if="scope.row.state==1" @click="changeState">已处理</el-tag>
                    </template>
                  </el-table-column>
                    <!--          <el-table-column label="收件人消息账号" width="" prop="receiveCode">-->
                    <!--          </el-table-column>-->

                </el-table>
            </div>
            <!-- 分页器 -->
            <Pagination
                :total="pages.totalCount"
                :limit="20"
                :pageSize.sync="pages.pageSize"
               :currentPage.sync="pages.currPage"
                @currentChange="currentChangeUser"
                @sizeChange="sizeChangeUser"
            />
        </div>

        <!--      写消息-->
        <div class="right" v-show="viewList !== true">
            <div class="e-form" style="padding: 0 10px 10px;" v-if="viewList === 'class'">
                <div class="tabs-title">写消息</div>
                <el-form :rules="formRules" ref="writer" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="发送用户：" prop="receiveName">
                                <el-input disabled v-model="formData.receiveUserListTitle"></el-input>
                                <el-button size="mini" type="primary" @click="importDeviceSelect()">选择 </el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="发送商铺：" prop="shopName">
                                <el-input disabled v-model="formData.receiveShopListTitle"></el-input>
                                <el-button size="mini"
                                           type="primary"
                                           @click="shopDialog">选择
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!--          发送消息给用户-->
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="消息标题：" prop="title">
                                <el-input clearable clean v-model="formData.title" placeholder="请输入标题"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col class="editorCol" :span="24">
                            <el-form-item label="消息内容：" prop="content">
                                <editor v-model="formData.content"></editor>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="margin-top: 20px;">
                        <el-col :span="20" v-loading="fileLoading">
                            <el-form-item class="upload-item" label="附件资料：" prop="">
                                <el-upload
                                    action="fakeaction"
                                    multiple
                                    :limit="20"
                                    :show-file-list="true"
                                    :file-list="fileList"
                                    :before-upload="beforeOneOfFilesUpload"
                                    :http-request="uploadOneOfFiles"
                                    :on-remove="handleRemove"
                                    accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*, .mp4"
                                >
                                    <el-button size="small" type="primary">点击上传</el-button>
                                    <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件,mp4</div>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="buttons">
                    <el-button @click="viewList=true">返回</el-button>
                    <el-button v-if="viewList === 'class'" type="primary" @click="sendMessage">发送</el-button>
                </div>
            </div>
        </div>
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="false">
            <el-form :model="receiveMessagefifter" ref="form" label-width="120px" :inline="false" size="normal">
                <!--        <el-row>-->
                <!--          <el-col :span="12" :offset="0">-->
                <!--            <el-form-item label="是否全读：">-->
                <!--              <el-select v-model="filterData.allRead" >-->
                <!--                <el-option v-for="item in allReads" :key="item.value" :label="item.label" :value="item.value">-->
                <!--                </el-option>-->
                <!--              </el-select>-->
                <!--            </el-form-item>-->
                <!--          </el-col>-->
                <!--        </el-row>-->
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="留言内容：">
                            <el-input v-model="keywords" placeholder="请输入留言内容" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
              <el-row>
                <el-col :span="12" :offset="0">
                  <el-form-item label="处理状态：">
                    <el-button @click="state=1">已处理</el-button>
                    <el-button @click="state=0">未处理</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="发送时间：">
                            <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.dateValue"
                                            type="datetimerange"
                                            range-separator="至" start-placeholder="开始日期"
                                            end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
              <el-row>
                <el-col :span="12" :offset="0">
                  <el-form-item label="回复时间：">
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.dateValue2"
                                    type="datetimerange"
                                    range-separator="至" start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="expertSearch">确定</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
        </el-dialog>

        <!--        选择用户库-->
        <el-dialog title="选择收件人"
                   :v-show="formData.receiveType==1"
                   v-loading="inventoryTableLoading" v-dialogDrag
                   :visible.sync="receviceDialog"
                   width="70%" style="margin-left: 20%;"
                   :close-on-click-modal="true">
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left-btn1">
                        <el-checkbox v-model="checkAllUser" label="发送所有人" :indeterminate="false"
                                     @change="toggleSelectAllUser"></el-checkbox>
                    </div>
                    <div class="search_box">
                        <el-input clearable type="text" @keyup.enter.native="getUserList" placeholder="输入搜索关键字"
                                  v-model="userList.keywords"><img src="@/assets/search.png" slot="suffix"
                                                                   @click="getUserList"/></el-input>
                    </div>
                </div>
                <el-table ref="tableUser"
                          highlight-current-row
                          border
                          style="width: 100%"
                          :data="userList.tableData"
                          class="table"
                          @selection-change="handleSelectionChange"
                          @row-click="handleCurrentInventoryClickUser"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"/>
                    <el-table-column label="序号" type="index" width=""/>
                    <el-table-column prop="userNumber" label="用户编号" width=""/>
                    <el-table-column prop="realName" label="真实姓名" width=""/>
                    <el-table-column prop="nickName" label="昵称" width=""/>
                    <el-table-column prop="userMobile" label="手机号码" width=""/>

                    <!--          <el-table-column prop="userImg" label="头像" width=""/>-->

                </el-table>
            </div>
            <span slot="footer">
                    <Pagination
                        v-show="userList.tableData != null || userList.tableData.length != 0"
                        :total="userList.paginationInfo.total"
                        :pageSize.sync="userList.paginationInfo.pageSize"
                        :currentPage.sync="userList.paginationInfo.currentPage"
                        @currentChange="currentChangeUser" @sizeChange="sizeChangeUser"

                    />
                    <el-button style="margin-top: 20px" @click="receviceDialog = false">取消</el-button>
                    <el-button type="primary" style="margin-top: 20px" @click="onUserList">确定</el-button>
                </span>
        </el-dialog>

        <!--   选择商铺发送-->
        <el-dialog v-dialogDrag title="选择商铺" :v-show="formData.receiveType==0" v-loading="inventoryTableLoading"
                   :visible.sync="showDeviceDialog" width="70%" style="margin-left: 20%;" :close-on-click-modal="true">
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">

                    <div class="left-btn1">
                        <el-checkbox v-model="checkAllShop" label="发送所有商铺" :indeterminate="false"
                                     @change="toggleSelectAllShop"></el-checkbox>
                    </div>

                    <div class="search_box">
                        <el-input clearable type="text" @keyup.enter.native="getshopLists" placeholder="输入搜索关键字"
                                  v-model="shopList.keywords"><img src="@/assets/search.png" slot="suffix"
                                                                   @click="getshopLists"/></el-input>
                    </div>
                </div>

                <el-table ref="tableShop" highlight-current-row border
                          style="width: 100%"
                          :data="shopList.tableData"
                          class="table"
                          @currentChange="currentChangeUser" @sizeChange="sizeChangeUser"
                          @selection-change="handleSelectionChangeShop"
                          @row-click="handleCurrentInventoryClickShop"

                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column prop="serialNum" label="序列号" width="200"/>
                    <el-table-column prop="shopName" label="店铺名称" width="200"/>
                    <el-table-column prop="shopType" label="店铺类型" width="">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.shopType==0">个体户</el-tag>
                            <el-tag v-else-if="scope.row.shopType==1">企业</el-tag>
                            <el-tag v-else-if="scope.row.shopType==2">个人</el-tag>
                            <el-tag v-else type="danger">已关闭</el-tag>
                        </template>
                    </el-table-column>
                    <!--          <el-table-column prop="shopImg" label="店铺log" width="200"/>-->
                    <el-table-column prop="detailedAddress" label="详细地址" width=""/>
                </el-table>
            </div>
            <span slot="footer">
                    <Pagination
                        v-show="shopList.tableData || shopList.tableData.length > 0"
                        :total="shopList.paginationInfo.total"
                        :pageSize.sync="shopList.paginationInfo.pageSize"
                        :currentPage.sync="shopList.paginationInfo.currentPage"
                    />
                    <el-button style="margin-top: 20px" @click="showDeviceDialog = false">取消</el-button>
                    <el-button type="primary" style="margin-top: 20px" >确定</el-button>
                </span>
        </el-dialog>
    </div>

</template>
<script>
import {
    getList,
    sendMessageApi,
    queryAllPage,
    deleteMessage,
    updateNotDisplay,
    updateDisplay
} from '@/api/platform/mail/outbox'
import Pagination from '@/components/pagination/pagination'
import { getUserList } from '@/api/platform/user/userInquire'
import { getShopPassList } from '@/api/platform/shop/shopAudit'
import editor from '@/components/quillEditor'
import { uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'

export default {
    components: {
        editor,
        Pagination
    },
    data () {
        return {
            selectedRows: [],
            currentRow: [],
            fileLoading: false,
            fileList: [],
            checkAll: false, //全选控制
            checkAllUser: false, //全选控制
            checkAllShop: false, //全选控制

            // allReads: [
            //     {
            //         value: null,
            //         label: '全部'
            //     },
            //     {
            //         value: 0,
            //         label: '未全读'
            //     },
            //     {
            //         value: 1,
            //         label: '已读'
            //     }],
            // 数据加载
            keywords: null,
            // formLoading: false,
            // brandTableLoading: false,
            inventoryTableLoading: false,
            state: null,
            filterData: {
                messagContent: null,
                state: null,
                dateValue: [], // 开始时间和结束时间
                dateValue2: [], // 开始时间和结束时间
                floorName: '',
                columnName: '',
                floorNameText: '',
                floorProductType: '',
                keywords: '',
                orderBy: 1,
                mallType: null,
            },
            // 收件人高级查询
            receiveMessagefifter: {
                title: null,
                dateValue: [],
                dateValue2: [],
            },
            queryVisible: false,
            messageInfo: {},
            sendMessageTable: [], //接收消息列表
            viewList: true,
            stationMessageInfo: {},
            receiveUserList: [], //发送用户集合
            receiveShopList: [],  //发送商铺集合
            formRules: {
                title: { required: true, message: '请输入标题', trigger: 'blur' },
                content: { required: true, message: '请输入发送消息', trigger: 'blur' },
            },

            showDeviceDialog: false, // 选择收件商铺弹窗,
            receviceDialog: false, // 选择收件用户弹窗库
            requestParams: {},
            formData: {
                receiveType: '', // 收件人类型0店铺 1用户2平台
                receiveUserList: [], //收件人id,
                receiveShopList: [], //商铺id
                receiveUserListTitle: [], //用户展示字段收件人,
                receiveShopListTitle: [], //商铺展示字段收件人,
                title: null, //标题
                state: null,
                content: null,
                files: []
            },
            //用户数据
            userList: {
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 20,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            pages: {
                currPage: 1,
                pageSize: 20,
                totalCount: 200
            },
            shopList: {
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            screenHeight: 0,
        }
    },
    created () {
        this.isLoading = true
        this.getSendList()
        this.getParams()

    },
    mounted () {
        this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
    },
    computed: {
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    methods: {
        selectUserAll () {
            let params = {
                page: this.userList.paginationInfo.currentPage,
                limit: this.userList.paginationInfo.total,
                orderBy: this.filterData.orderBy,
            }
            if (this.userList.keywords != '' || this.userList.keywords != null) {
                params.keywords = this.userList.keywords
            }
            getUserList(params).then(res => {
                this.handleSelectionChange(res.list)
            })

        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if (size > 500) {
                this.$message.error('文件大小不能超过500M')
                return false
            }
            return true
        },

        // 多文件上传
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'device')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if (uploadRes.code != null && uploadRes.code != 200) {
                this.fileList.push(file)
                this.fileList.pop()
            } else {
                this.$message.success('上传成功')
                this.formData.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 7,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
            }
        },
        handleExceed () {
            this.$message.error('文件个数不能超出20个')
            return false
        },
        handleRemove (file) {
            let files = this.formData.files
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.formData.files = newFiles
                }
            })
        },
        selectShopAll () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.total,
                orderBy: this.filterData.orderBy,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            getShopPassList(params).then(res => {
                this.handleSelectionChangeShop(res.list)
            })
        },
        toggleSelectAllUser () {
            if (!this.checkAllUser) {
                this.formData.receiveUserListTitle = []
                return this.$refs['tableUser'].clearSelection()
            }
            this.$refs['tableUser'].toggleAllSelection()
            this.selectUserAll()
        },
        toggleSelectAllShop () {
            if (!this.checkAllShop) {
                this.formData.receiveShopListTitle = []
                return this.$refs['tableShop'].clearSelection()
            }
            this.selectShopAll()
            this.$refs['tableShop'].toggleAllSelection()
        },
        //点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.receive.toggleRowSelection(row, row.flag)
        },
        //点击选中
        handleCurrentInventoryClickShop (row) {
            row.flag = !row.flag
            this.$refs.tableShop.toggleRowSelection(row, row.flag)
        },
        //点击选中发送用户
        handleCurrentInventoryClickUser (row) {
            row.flag = !row.flag
            this.$refs.tableUser.toggleRowSelection(row, row.flag)
        },
        expertSearch () {
            this.getSendList()
            //重置数据
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.dateValue2 = [] // 开始时间和结束时间
            this.filterData.title = null// 选中的标题
            this.filterData.allRead = null
            this.filterData.receiveName = null
            this.queryVisible = false
            this.keywords = null
            this.state = null
        },
        //展示
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.messageInformationsId
            })
            if (!this.selectedRows[0]) {
                let msg = num == 1 ? '请选择要展示的留言' : '请选择要取消展示的留言'
                return this.clientPop('warn', msg, () => { })
            }
            let warnMsg = num === 1 ? '您确定要展示选中的留言吗？' : '您确定要取消展示选中的留言吗？'
            this.clientPop('info', warnMsg, async () => {
                switch (num)
                {
                case 1:
                    updateDisplay(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '展示成功', () => {
                                this.requestParams.pageSize = this.pageSize
                                queryAllPage(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.sendMessageTable = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => { })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 2:
                    updateNotDisplay(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '取消展示成功', () => {
                                queryAllPage(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.sendMessageTable = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {})
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
            this.getsendList()
        },
        onWriter () {
            this.emptyForm()
            this.viewList = 'class'
        },
        emptyForm () {
            for (let key in this.formData) {
                if(!key.includes('file')) this.formData[key] = null
            }
        },

        currentChangeUser (currPage) {
            this.userList.paginationInfo.currentPage = currPage
            this.getSendList()
        },
        sizeChangeUser (pageSize) {
            this.userList.paginationInfo.pageSize = pageSize
            this.getSendList()
        },
        currentChangeShop (currPage) {
            this.shopList.paginationInfo.currentPage = currPage
            this.getSendList()
        },
        sizeChangeshop (pageSize) {
            this.shopList.paginationInfo.pageSize = pageSize
            this.getUserList()
        },
        changeState (state) {
            state === '1' ? this.state = '0' : this.state = '1'
        },
        // 详情
        handleView (row) {
            //利用$router.push进行跳转
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/mail/messageDetail',
                //name后面跟跳转的路由名字（必须有，不使用命名路由会传参失败）
                name: 'messageDetail',
                params: {
                    row: row,
                }
            })
        },
        //收件人页码跳转
        currentChange (currPage) {
            this.pages.currPage = currPage
            this.getSendList()
        },
        sizeChange (pageSize) {
            this.pages.pageSize = pageSize
            this.getSendList()
        },

        //批量删除发件信息
        handleDelete () {},
        //收件人关键字查询
        onSearch () {},
        /**
         * 删除发送消息
         *
         */
        onDel (scope) {
            this.clientPop('info', '您确定要删除该留言吗？', async () => {
                // showLoading()
                deleteMessage({ id: scope.row.messageInformationsId }).then(res => {
                    this.message(res)
                    this.getSendList()
                })

                // showLoading()
            })
        },
        /**
         * 查询所有收件人列表
         *
         * 消息列表
         */
        async getSendList () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.state != null) {
                params.state = this.state
            }
            if (this.filterData.allRead != null) {
                params.allRead = this.filterData.allRead
            }
            if (this.filterData.title) {
                params.title = this.filterData.title
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            if (this.filterData.dateValue2 != null) {
                params.respondStartDate = this.filterData.dateValue2[0],
                params.respondEndDate = this.filterData.dateValue2[1]
            }
            queryAllPage(params).then(res => {
                this.sendMessageTable = res.list
                this.pages = res
            })
            let res = await queryAllPage(params)
            this.sendMessageTable = res.list || []
            this.pages = res
            this.pages = res
        },
        /**
         * 确定发送用户id集合
         */
        onUserList () {
            this.formData.receiveUserList = this.receiveUserList.map(item => {
                return item.userId
            })
            this.formData.receiveUserListTitle = this.receiveUserList.map(item => {
                return item.userId + '[' + item.realName + ']'
            })
            this.receviceDialog = false
        },
        // 收件人信息高级查询
        advancedQuery () {
            this.keywords = null
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                orderBy: this.filterData.orderBy,
                title: this.receiveMessagefifter.title,
            }
            getList(params).then(res => {
                this.message(res)
                this.sendMessageTable = res.list
                this.pages.total = res.totalCount
                this.pages.pageSize = res.pageSize
                this.pages.currentPage = res.currPage
                this.queryVisible = false
            })
        },
        //发送消息
        sendMessage () {
            this.$refs.writer.validate(valid => {
                if (!valid) return
                if (this.formData.receiveUserList != null || this.formData.receiveShopList != null) {
                    let params = {
                        title: this.formData.title,
                        content: this.formData.content,
                        receiveList: this.formData.receiveUserList,
                        shopIdList: this.formData.receiveShopList,
                        files: this.formData.files,
                        sendType: 2
                    }
                    sendMessageApi(params).then(res => {
                        if (res.code === 200) {
                            this.$message({ message: '发送成功', type: 'success' })
                        }
                        this.formData = {}
                        this.viewList = true
                        this.getSendList()

                    })
                } else {
                    this.$message({
                        message: '至少选择一个用户和商铺',
                        type: 'warning'
                    })
                }
            })
        },
        //获取用户信息 关键字搜索
        getUserList () {
            let params = {
                page: this.userList.paginationInfo.currentPage,
                limit: this.userList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            if (this.userList.keywords != null) {
                params.keywords = this.userList.keywords
            }
            this.inventoryTableLoading = true
            queryAllPage(params).then(res => {
                this.userList.tableData = res.list
                this.userList.paginationInfo.currentPage = res.currPage
                this.userList.paginationInfo.pageSize = res.pageSize
                this.userList.paginationInfo.total = res.totalCount

            })
            this.inventoryTableLoading = false
        },

        //获取商铺信息
        getshopLists () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            this.inventoryTableLoading = true
            getShopPassList(params).then(res => {
                this.shopList.tableData = res.list
                this.shopList.paginationInfo.currentPage = res.currPage
                this.shopList.paginationInfo.pageSize = res.pageSize
                this.shopList.paginationInfo.total = res.totalCount

            })
            this.inventoryTableLoading = false
        },
        // 打开收件人弹窗
        importDeviceSelect () {
            this.receviceDialog = true
            this.receiveUserList = []
            this.formData.receiveType = 1
            this.getUserList()

        },
        // 打开商店弹窗
        shopDialog () {
            this.receiveShopList = []
            this.showDeviceDialog = true
            this.formData.receiveType = 0
            this.getshopLists()
        },
        // addUserList () {
        //     this.showDeviceDialog = false
        // },
        handleSelectionChange (list) {
            if (list.length > 0) {
                this.receiveUserList = list
                // this.formData.receiveUserListTitle = list.map(item => {
                //     return item.userId + '[' + item.realName + ']'
                // })

            }
            this.selectedRows = list

        },
        /**
         * 表格勾选
         */

        handleCurrentChange (val) {
            this.currentRow = val
        },
        //收集表格数据
        handleSelectionChangeShop (list) {
            if (list.length > 0) {
                this.receiveShopList = list
                // this. formData.receiveShopList = list.map(item => {
                //     return item.shopId
                // })
                // this.formData.receiveShopListTitle = list.map(item => {
                //     return item.shopId + '[' + item.shopName + ']'
                // })

            }

        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
    }
}
</script>
<style lang="scss" scoped>
/deep/ .el-col.editorCol {
    .el-form-item__content {
        height: unset !important;
    }
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

.left-btn1 {
    margin: auto 2px;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0;
    }
}
</style>
