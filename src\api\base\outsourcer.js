import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

//根据条件分页查询评价期限
const evaluationPeriodList = params => {
    return httpPost({
        url: '/outer/creditratinglimit/listByEntity',
        params
    })
}
//新增评价期限
const addEvaluationPeriod = params => {
    return httpPost({
        url: '/outer/creditratinglimit/create',
        params
    })
}
//根据唯一id删除评价期限
const deleteEvaluationPeriod = params => {
    return httpGet({
        url: '/outer/creditratinglimit/delete',
        params
    })
}
//根据唯一id查询评价期限信息
const getEvaluationPeriod = params => {
    return httpGet({
        url: '/outer/creditratinglimit/findById',
        params
    })
}
//修改评价期限
const updatEevaluationPeriod = params => {
    return httpPost({
        url: '/outer/creditratinglimit/update',
        params
    })
}
//修改信用评价表模板
const updateCreditEvaluation = params => {
    return httpPost({
        url: '/outer/creditratingtemplate/update',
        params
    })
}
//新增信用评价表模板
const addCreditEvaluation = params => {
    return httpPost({
        url: '/outer/creditratingtemplate/create',
        params
    })
}
//删除信用评价表模板
const deleteCreditEvaluation = params => {
    return httpGet({
        url: '/outer/creditratingtemplate/delete',
        params
    })
}
//根据唯一id查询信用评价表模板
const detailCreditEvaluation = params => {
    return httpGet({
        url: '/outer/creditratingtemplate/findById',
        params
    })
}
//根据条件分页查询信用评价表模板
const creditEvaluationList = params => {
    return httpPost({
        url: '/outer/creditratingtemplate/listByEntity',
        params
    })
}
//根据类型查询信用评价期限
const creditEvaluationType = params => {
    return httpGet({
        url: '/outer/creditratinglimit/findByType',
        params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
//根据类型查询信用评价表模板
const creditTemplate = params => {
    return httpPost({
        url: '/outer/creditratingtemplate/findByType',
        params,
    })
}
// 初始化惩罚
const initPunishmentDetails = params => {
    return httpGet({
        url: '/outer/creditratingtemplate/initialization',
        params,
    })
}
const getBillId = num => {
    return httpGet({
        url: `/uid/builder?num=${num}`,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}
export {
    evaluationPeriodList,
    addEvaluationPeriod,
    deleteEvaluationPeriod,
    getEvaluationPeriod,
    updatEevaluationPeriod,
    updateCreditEvaluation,
    addCreditEvaluation,
    deleteCreditEvaluation,
    detailCreditEvaluation,
    creditEvaluationList,
    creditEvaluationType,
    creditTemplate,
    initPunishmentDetails,
    getBillId
}
