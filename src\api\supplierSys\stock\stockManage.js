import service from '@/utils/request'

// eslint-disable-next-line no-unused-vars
const { httpPost, httpGet } = service

const selfStockList = params => {
    return httpPost({
        url: '/materialMall/stock/selfOperatedLog/listByEntity',
        params
    })
}
const supplierStockList = params => {
    return httpPost({
        url: '/materialMall/stock/secondarySupplierLog/listByEntity',
        params
    })
}
const saveSelfStock = params => {
    return httpPost({
        url: '/materialMall/stock/selfOperatedLog/saveLog',
        params
    })
}
const selfOriginStock = params => {
    return httpPost({
        url: '/materialMall/stock/selfOperatedLog/getProductInfo',
        params
    })
}
const deleteSelfStock = id => {
    return httpGet({
        url: '/materialMall/stock/selfOperatedLog/delete/' + id,
    })
}
const getSelfStock = id => {
    return httpGet({
        url: '/materialMall/stock/selfOperatedLog/getSelfInfo/' + id,
    })
}
export {
    selfStockList,
    saveSelfStock,
    deleteSelfStock,
    supplierStockList,
    selfOriginStock,
    getSelfStock
}