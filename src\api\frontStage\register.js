import service from '@/utils/request'

const { httpPost, httpGet } = service

const registerPerson = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/w/user/registerPerson',
        params
        // headers: {
        //     'Content-Type': 'application/x-www-form-urlencoded'
        // }
    })
}

const registerEnterprise = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/w/user/registerEnterprise',
        params
    })
}
const selectIsPcwpUserByCode = params => {
    return httpGet({
        url: '/materialMall/w/user/selectIsPcwpUserByCode',
        params
    })
}

export {
    registerPerson,
    registerEnterprise, selectIsPcwpUserByCode
}