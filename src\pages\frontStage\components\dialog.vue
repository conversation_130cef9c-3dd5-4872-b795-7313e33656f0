<template>
    <el-dialog class="front" v-bind="$attrs" v-on="$listeners" @close="closeDialog">
        <div class="dialog-header">
            <div class="dialog-header-top search_bar">
                <div class="dialog-title search_bar">
                    <div></div>
                    <div>{{ $attrs.title }}</div>
                </div>
                <div class="dialog-close" @click="closeDialog">
                    <img src="@/assets/images/close.png" alt=""/>
                </div>
            </div>
            <div></div>
        </div>
        <div class="dialog-body center" :style="{ maxHeight: maxContentHeight }">
            <slot></slot>
        </div>
        <span slot="footer">
            <slot name="footer"></slot>
        </span>
    </el-dialog>
</template>
<script>
export default {
    name: 'frontDialog',
    data () {
        return {
            screenHeight: 0,
        }
    },
    watch: {
    },
    computed: {
        maxContentHeight () {
            return this.screenHeight - 80 + 'px'
        },
    },
    methods: {
        closeDialog () {
            this.$emit('update:visible', false)
        },
    },
    mounted () {
        this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
    },
}
</script>

<style scoped lang="scss">
.dialog-body {
    overflow: auto;
}
</style>