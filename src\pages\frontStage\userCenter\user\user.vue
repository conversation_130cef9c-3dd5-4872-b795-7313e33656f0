<template>
    <div class="main_right" v-loading="userloading">
        <div class="right_top dfb">
            <div class="left dfc" >
                <div class="left_1">
                    <img v-if="userInfo.userImg" :src="imgUrlPrefixAdd + userInfo.userImg"/>
                    <img v-else src="../../../../assets/images/img/touxiang.png" alt=""/>
                    <div class="textOverflow1">ID：{{ userInfo.userNumber }}</div>
                </div>
                <div class="left_2">
                    <span>{{userInfo.nickName}}</span>
                    <div class="dfc pointer" v-if="userInfo.shopId == null && userInfo.isShpAuthority === 1 && userInfo.enterpriseType != 2" @click="handleOpenShop">我要开店 <i class="el-icon-arrow-right"></i></div>
                    <!--                    店铺停用不显示-->
                    <template v-if="!showDevFunc">
                        <div class="dfc pointer" v-if="userInfo.shopId != null && userInfo.auditStatus == 1 && userInfo.isShpAuthority === 1" @click="handleOpenShopIndex">店铺主页 <i class="el-icon-arrow-right"></i></div>
                    </template>
                    <template v-if="showDevFunc">
                        <div class="dfc pointer" v-if="userInfo.shopId != null && userInfo.auditStatus == 1 && userInfo.isShpAuthority === 1 && this.shopStatus === 1" @click="handleOpenShopIndex">店铺主页 <i class="el-icon-arrow-right"></i></div>
                    </template>
                    <div class="dfc pointer"  @click="$router.push({path:'/user/statusFail',query:{shopId: userInfo.shopId}})" v-if="userInfo.shopId != null && userInfo.auditStatus == 3 && userInfo.isShpAuthority === 1">审核未通过 <i class="el-icon-arrow-right"></i></div>
                    <div class="dfc pointer" v-if="userInfo.shopId != null && userInfo.auditStatus == 2 && userInfo.isShpAuthority === 1">审核中<i class="el-icon-arrow-right"></i></div>
                </div>
            </div>
            <div class="shop_audit_status p20" v-if="shopAuditStatus === 0">
                <div class="mb10">供应商审核状态：
                    <el-link type="warning" v-show="supplierAudit==0">审核中</el-link>
                    <el-link type="success" v-show='supplierAudit==2'>审核成功</el-link>
                    <el-link type="warning" v-show="supplierAudit==1">审核失败</el-link>
                </div>
                <div class="df" v-show="supplierAudit==1">原因：
                    <div style="max-width: 300px;">{{auditFailReason}}</div>
                </div>
                <div class="pointer" @click="retryAudit" v-show="supplierAudit==1">重新审核</div>
            </div>

            <!--            <div class="right dfb">-->
<!--                <div class="item dfc" v-for="(item, i) in iconList" :key="i">-->
<!--                    <img :src="item.img" alt=""/>-->
<!--                    <span>{{ item.name }}</span>-->
<!--                    <div v-if="item.num" class="num">{{ item.num }}</div>-->
<!--                </div>-->
<!--            </div>-->
        </div>

        <div class="logisticsBox dfb mt20">
            <div class="left" v-loading="myLogistics">
                <div class="titleBox dfb">
                    <div class="dfa">
                        <div class="line"></div>
                        <div class="title">我的物流</div>
                    </div>
                </div>
                <div class="left_list">
                    <div v-if="userLogisticsOrderData.length === 0" style="color: #999; text-align: center; height: 200px; line-height: 200px;">没有数据</div>
                    <div class="left_item mt30 dfb" v-for="(item, i) in userLogisticsOrderData" :key="i">
                        <div class="item_1 dfa">
                            <img :src="item.productImg ? imgUrlPrefixAdd + item.productImg : require('@/assets/images/img/queshen5.png')" alt="" />
                            <div class="item_1_right">
                                <div>您的订单已发货…</div>
<!--                                <div class="mt10">物流号: {{item.deliveryFlowId}} | {{ item.logisticsCompany }} | {{item.deliveryTime}}</div>-->
                                <div class="mt10">发货时间: {{item.deliveryTime}}</div>
                            </div>
                        </div>
                        <div class="item_2 dfa">
                            <el-button style="padding: 0" type="text" @click="$router.push({path: '/user/orderPlanDetail', query: { orderSn: item.orderSn}})">查看详情</el-button>
<!--                            <span></span>-->
<!--                            <el-button type="text" style="padding: 0">查看发票</el-button>-->
                        </div>
                    </div>
                </div>
            </div>

            <div class="right">
                <div class="titleBox dfb">
                    <div class="dfa">
                        <div class="line"></div>
                        <div class="title">我的关注</div>
                    </div>
                </div>
                <div class="gzNum dfb">
                    <div class="dfc">
                        <div>{{ collectNum }}</div>
                        <span> 商品关注</span>
                    </div>
                    <!--                    <div class="dfc">-->
                    <!--                        <div>16</div>-->
                    <!--                        <span> 店铺关注</span>-->
                    <!--                    </div>-->
                    <!--                  <div class="dfc">-->
                    <!--                    <div>16</div>-->
                    <!--                    <span> 需求关注</span>-->
                    <!--                  </div>-->
                </div>
                <div class="spguanTitle dfb">
                    <div>商品关注</div>
                    <div @click="$router.push('/user/followingProduct') " class="pointer"> 更多 <i
                        class="el-icon-arrow-right"></i></div>
                </div>
                <div class="right_list dfa">
                    <div class="item" v-for="(item, i) in productList" :key="i"
                         @click="handleViewProduct(item.productId)">
                        <img
                            :src="item.productMinImg ? imgUrlPrefixAdd + item.productMinImg : require('@/assets/images/img/queshen5.png')"
                            alt="">
                        <div>{{ item.productName }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="cnxh">
            <div class="titleBox dfb" style="border: 0">
                <div class="dfa">
                    <div class="line"></div>
                    <div class="title">猜您喜欢</div>
                </div>
                <div @click="getMaterialPageListM" class="dfa" style="font-size: 14px">
                    <i class="el-icon-refresh-right" style="margin-right: 5px; font-weight: 600; font-size: 16px"></i>换一组
                </div>
            </div>
        </div>
        <!-- 猜您喜欢列表 -->
        <div class="goodlist " v-loading="likeProductLoad">
            <div @click="$router.push({path: '/mFront/productDetail',query: {productId: item.productId}})" class="goodItem dfc" v-for="(item, i) in likeProductList" :key="i">
                <img
                    :src="item.productMinImg ? imgUrlPrefixAdd + item.productMinImg : require('@/assets/images/img/queshen5.png')"
                    alt="">
                <div class="title">{{item.productName}}</div>
                <div class="desc">{{item.skuName}}</div>
                <div class="price">￥{{item.productMinPrice}}</div>
            </div>
        </div>
        <el-dialog class="front" :visible.sync="dialogVisible" :close-on-click-modal="false">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>内部用户开店</div>
                    </div>
                    <div class="dialog-close" @click="dialogVisible = false"><img src="@/assets/images/close.png"
                                                                                  alt=""/></div>
                </div>
                <div></div>
            </div>
            <div class="dialog-body center">
                <div class="company-info">
                    <div style="margin-bottom: 12px;"><span>企业名称：</span><span>{{ userInfo.orgName }}</span></div>
                </div>
                <el-form :model="form" ref="form" :rules="rules" label-position="top" :inline="false" size="normal">
                    <div class="row dfb">
                        <div class="col">
                            <el-form-item label="店铺名称：" prop="shopName">
                                <el-input clearable v-model="form.shopName" placeholder="请输入店铺名称"></el-input>
                            </el-form-item>
                        </div>
                        <div class="col">
                            <el-form-item label="联系人：" prop="contact">
                                <el-input clearable v-model="form.contact" placeholder="请输入联系人"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <el-form-item label="联系电话：" prop="tel">
                                <el-input clearable v-model="form.tel" placeholder="请输入联系电话"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <el-form-item label="详细地址：" prop="detailedAddress">
                            <el-input clearable type="textarea" :auto-resize="false" v-model="form.detailedAddress"
                                      placeholder="请输入详细地址"></el-input>
                        </el-form-item>
                    </div>
                    <div class="btn center" @click="handleSubmit">提交</div>
                </el-form>
            </div>
        </el-dialog>
        <el-dialog v-dialogDrag title="商品展示数量" :visible.sync="showNumDialog" width="70%" style="margin-left: 20%;"
                   :close-on-click-modal="true">
            <div class="search_box">
                <el-form ref="formEdit2" class="demo-ruleForm">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="展示数量：" prop="showNum">
                                <el-input type="number" v-model="showNum" placeholder="请输入数量" clearable></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onShow()">查询</el-button>
                    <el-button @click="showNumDialog=false">取消</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { createShopInside, getAuditState } from '@/api/frontStage/userCenter'
import { getProductList, getCollectNum } from '@/api/frontStage/productCollect'
import{ mapState } from 'vuex'
import { getUserLogisticsOrder } from '@/api/frontStage/order'
import { getMaterialPageList } from '@/api/frontStage/productList'
import { getShopStateByUserId } from '@/api/frontStage/mallWebHeader'

export default {
    data () {
        return {
            enterpriseType: null,
            supplierAudit: 0,
            shopAuditStatus: 0,
            // 店铺启停用状态
            shopStatus: 0,
            likeProductLoad: false,
            page: 1,
            userloading: false,
            userLogisticsOrderData: [],
            showNumDialog: false,
            dialogVisible: false,
            iconList: [],
            form: {
                shopName: '',
                contact: '',
                tel: '',
                detailedAddress: '',
            },
            rules: {
                shopName: [
                    { required: true, message: '请输入店铺名称', trigger: 'blur' },
                    { min: 1, max: 100, message: '超出范围', trigger: 'blur' }
                ],
                contact: [
                    { required: true, message: '请输入联系人', trigger: 'blur' },
                    { min: 1, max: 20, message: '超出范围', trigger: 'blur' }
                ],
                tel: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailedAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
            },
            likeProductList: [],
            productList: [],
            shopList: [],
            collectType: 1,
            collectNum: 0,
            showNum: 4,
            myLogistics: false,
            auditFailReason: ''

        }
    },
    watch: {
        dialogVisible () {
            this.$refs['form'].clearValidate()
        }
    },
    computed: {
        ...mapState(['userInfo']),
    },
    created () {
        this.getMaterialPageListM()
        this.getUserLogisticsOrderM()
        // 获取店铺状态
        this.onShow()
        this.getAuditState()
        this.getCollectNum()
        this.getShopStateByUserId()
        this.userloading = false
    },
    mounted () {
    },
    methods: {
        getShopStateByUserId () {
            getShopStateByUserId().then(res=>{
                // 店铺状态
                this.shopStatus = res.state
            })
        },
        getAuditState () {
            getAuditState().then(res=>{
                if (res.interiorId != null) {
                    this.shopAuditStatus = 1
                }
                if (res.isNoSupplierAudit == 1) {
                    this.enterpriseType = this.userInfo.enterpriseType
                    this.$notify({
                        type: 'error',
                        title: '审核失败',
                        message: res.auditFailReason
                    })
                    this.supplierAudit = 1
                    this.auditFailReason = res.auditFailReason
                }else {
                    if (res.isSupplier == 2) {
                        this.supplierAudit = 2
                    }else  if (res.isSupplier == 1) {
                        this.supplierAudit = 0
                    }else {
                        this.shopAuditStatus = 1
                    }
                }
            })
        },
        retryAudit () {
            console.log(this.enterpriseType)
            if(this.enterpriseType == 1) {
                return this.$router.push('/user/reverify/enterprise')
            }
            if(this.enterpriseType == 0) {
                return this.$router.push('/user/reverify/business')
            }
        },
        getMaterialPageListM () {
            this.likeProductLoad = true
            let params = {
                page: this.page,
                limit: 8
            }
            getMaterialPageList(params).then(res => {
                this.likeProductList = res.list
                if(res.totalPage != 0) {
                    let randPage = Math.floor(Math.random() * res.totalPage) + 1
                    this.page = randPage
                }
                this.likeProductLoad = false
            }).catch(() => {
                this.likeProductLoad = false
            })
        },
        getUserLogisticsOrderM () {
            this.myLogistics = true
            let params = {
                page: 1,
                limit: 3
            }
            getUserLogisticsOrder(params).then(res => {
                this.userLogisticsOrderData = res.list
                this.myLogistics = false
            }).catch(() => {
                this.myLogistics = false
            })
        },
        getCollectNum () {
            let params = {
                collectType: this.collectType,
            }
            getCollectNum(params).then(res => {
                if (typeof res != 'number') {
                    this.collectNum = 0
                } else {
                    this.collectNum = res
                }

            })
        },
        onShow () {
            this.showNumDialog = true
            let params = {
                collectType: this.collectType,
                showNum: this.showNum
            }
            getProductList(params).then(res => {
                this.productList = res
                this.showNumDialog = false

            })
            this.showNumDialog = false
        },
        // 消息提示
        // getControllerList () {
        //     // eslint-disable-next-line no-unused-vars
        //     let params = {
        //         collectType: this.collectType,
        //       showNum: this.showNum
        //     }
        //     getProductList(params).then(res=>{
        //         this.productList = res
        //         console.log('消息提示', this.productList)
        //     })
        // },
        // 查看商品
        handleViewProduct (id) {
            this.$router.push({ path: '/mFront/productDetail', query: { productId: id } })
        },
        message (res) {
            if (res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            } else {
                this.$message({
                    message: res.message,
                    type: 'error'
                })
            }
        },
        // 内部开店
        createShopInsideM () {
            createShopInside(this.form).then(res => {
                if (res.code == 200) {
                    this.$message({
                        message: '开店成功等待审核！',
                        type: 'success'
                    })
                    setTimeout(function () {
                        location.reload()
                    }, 1500)
                } else {
                    this.$message({
                        message: res.message,
                        type: 'error'
                    })
                }
            })
        },
        // 店铺首页
        handleOpenShopIndex () {
            let { href } = this.$router.resolve({
                query: { shopId: this.userInfo.shopId },
                path: '/mFront/shopIndex',
            })
            window.open(href, '_blank')
        },
        handleOpenShop () {
            // 是否内部用户
            let isInterior = this.userInfo.isInterior
            if (isInterior == 1) {
                // 是内部用户，让弹窗显示
                this.dialogVisible = true
            } else {
                // 不是内部用户
                this.$router.push('/mFront/becomeSeller')
            }
        },
        // 提交
        handleSubmit () {
            this.$refs['form'].validate(valid => {
                if (valid) {
                    this.createShopInsideM()
                }
            })
        },
    },
}
</script>
<style scoped lang="scss">
.main_right {
    width: 1106px;

    .right_top {
        width: 100%;
        height: 200px;

        .left {
            background: #216ec6;
            box-shadow: 4px 4px 10px 0px rgba(0, 133, 255, 0.29);
            width: 300px;
            height: 200px;

            .left_1 {
                img {
                    width: 100px;
                    height: 100px;
                }

                div {
                    width: 100px;
                    margin-top: 15px;
                    font-size: 14px;
                    color: #ffffff;
                    font-weight: 400;
                }
            }

            .left_2 {
                margin-left: 20px;

                span {
                    font-size: 16px;
                    color: #ffffff;
                    font-weight: 500;
                }

                div {
                    width: 100px;
                    height: 30px;
                    border: 1px solid rgba(255, 255, 255, 1);
                    font-size: 14px;
                    color: #ffffff;
                    font-weight: 500;
                    margin-top: 34px;
                }
            }
        }
        .shop_audit_status {
            height: 100%;
            position: relative;
            .success {
                color: limegreen;
            }
            .pointer {
                color: #216ec6;
                text-align: right;
                position: absolute;
                left: 20px;
                bottom: 4px;
                text-decoration: underline;
            }
            .fail {
                margin-right: 10px;
                color: orangered;
            }
            .df {
                line-height: 20px;
            }
        }

        .right {
            width: 806px;
            height: 180px;
            background: #fff;
            padding: 0 97px;

            .item {
                flex-direction: column;
                position: relative;

                span {
                    font-size: 14px;
                    color: #333333;
                    font-weight: 400;
                    display: inline-block;
                    margin-top: 20px;
                }

                .num {
                    position: absolute;
                    top: -10px;
                    right: -8px;
                    color: #fff;
                    width: 20px;
                    height: 20px;
                    background: #e22022;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 20px;
                }
            }
        }
    }

    .logisticsBox {
        .left {
            background: #ffffff;
            width: 700px;
            height: 350px;

            .left_list {
                padding: 0 20px;

                .left_item {
                    img {
                        width: 60px;
                        height: 60px;
                        margin-right: 20px;
                    }

                    .item_1_right {
                        font-size: 14px;
                        color: #666666;
                        letter-spacing: 0;
                        font-weight: 400;
                    }

                    .item_2 {
                        // width: 120px;
                        span {
                            width: 1px;
                            height: 18px;
                            background: #d9d9d9;
                            margin: 0 10px;
                            margin-top: 3px;
                        }
                    }
                }
            }
        }

        .right {
            background: #ffffff;
            width: 386px;
            height: 350px;

            .gzNum {
                width: 100%;
                height: 110px;
                border-bottom: solid 1px #e6e6e6;
                padding: 0 85px;

                .dfc {
                    flex-direction: column;

                    div {
                        font-size: 30px;
                        color: #333333;
                        text-align: center;
                        font-weight: 700;
                        margin-bottom: 10px;
                    }

                    span {
                        font-size: 14px;
                        color: #808080;
                        text-align: center;
                        font-weight: 400;
                    }
                }
            }

            .spguanTitle {
                width: 100%;
                height: 56px;
                padding: 0 20px;
            }

            .spguanTitle div:nth-child(1) {
                font-size: 16px;
                color: #333333;
                text-align: center;
                font-weight: 400;
            }

            .spguanTitle div:nth-child(2) {
                font-size: 14px;
                color: #216ec6;
                text-align: center;
                font-weight: 400;
            }

            .right_list {
                padding: 0 20px;
                width: 100%;

                img {
                    width: 60px;
                    height: 60px;
                    cursor: pointer;
                }

                .item {
                    div {
                        font-size: 14px;
                        color: #333333;
                        line-height: 18px;
                        font-weight: 400;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        word-break: break-all;
                        width: 70px;
                        margin-top: 3px;
                        cursor: pointer;
                    }
                }

                .item:not(:last-child) {
                    margin-right: 35px;
                }
            }
        }
    }

    .titleBox {
        padding: 0 20px;
        width: 100%;
        border-bottom: solid 1px #e6e6e6;
        height: 50px;

        .line {
            background: #216ec6;
            width: 3px;
            height: 20px;
            margin-right: 10px;
        }

        .title {
            font-size: 20px;
            color: #333333;
            font-weight: 500;
        }
    }

    .cnxh {
        width: 100%;
        background: #fff;
        height: 50px;
        margin: 20px 0;
    }

    .goodlist {
        display: flex;
        margin-top: 20px;
        flex-wrap: wrap;

        .goodItem {
            width: 260px;
            height: 300px;
            background: #fff;
            margin-bottom: 20px;
            flex-direction: column;

            img {
                width: 160px;
                height: 160px;
            }

            .title {
                font-size: 14px;
                color: #333333;
                letter-spacing: 0;
                text-align: center;
                font-weight: 400;
                margin-top: 20px;
            }

            .desc {
                font-size: 14px;
                color: #999999;
                text-align: center;
                font-weight: 400;
                margin: 10px 0;
            }

            .price {
                font-size: 16px;
                color: #d43030;
                text-align: center;
                line-height: 23px;
                font-weight: 400;
            }
        }
    }
}

/deep/ .el-dialog {
    width: 800px;

    .dialog-body {
        width: 630px;
        padding: 50px 0 10px;

        .company-info {
            height: 45px;
            margin-bottom: 30px;
            padding: 15px;
            background: #F7F7F7;

            div {
                font-size: 14px;

                span:first-child {
                    color: #999;
                }

                span:last-child {
                    color: #226FC7;
                }
            }
        }

        .row {
            margin-bottom: 30px;

            .col {
                width: 300px;
            }

            .el-input__inner {
                height: 35px;
                border-radius: 0;
            }

            .el-textarea__inner {
                height: 70px !important;
                padding: 11px 10px;
                border-radius: 0;
                resize: none;
            }
        }

        .el-form-item {
            margin-bottom: 0;
        }

        .el-form-item__label {
            height: 24px;
            line-height: 14px;
            color: #999;
        }

        .btn {
            width: 80px;
            height: 40px;
            line-height: 40px;
            font-size: 16px;
            text-align: center;
            color: #fff;
            background-color: #216EC6;
            cursor: pointer;
            user-select: none;
        }
    }
}
</style>