import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPost, httpGet } = service

const getShopById = params => {
    return httpGet({
        url: '/materialMall/platform/shop/getShopInfo',
        params
    })
}
const updateShopInfo = params => {
    return httpPost({
        url: '/materialMall/platform/shop/updateShopInfo',
        params
    })
}
const selectListByEnterPriseName = params => {
    return httpPost({
        url: '/materialMall/platform/enterpriseInfo/selectEnterpriseListByEnterPriseName',
        params
    })
}
const getList = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/listByShopId',
        params
    })
}
const create  = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/create',
        params
    })
}
const del  = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/create',
        params
    })
}

export {
    getShopById,
    updateShopInfo,
    selectListByEnterPriseName,
    getList,
    create,
    del,
}