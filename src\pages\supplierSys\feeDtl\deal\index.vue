<template>
    <div class="base-page">
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="0">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按修改时间排序</el-radio>
                        <el-input v-model="keywords" clearable placeholder="输入搜索关键字" style="width: 300px" type="text" @blur="handleInputSearch">
                            <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table
                    ref="tableRef"
                    v-loading="tableLoading"
                    :data="tableData"
                    :height="rightTableHeight"
                    border
                    class="table"
                    @selection-change="tableSelectM"
                    @row-click="tableRowClickM"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="编号" prop="platformDealFeeNu" width="200">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.platformDealFeeNu }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业名称" prop="enterpriseName" width=""></el-table-column>
                    <el-table-column label="剩余余额(元)" prop="remainingSum" width="">
                            <template v-slot="scope">
                                <span style="color: red">{{scope.row.remainingSum}}</span>
                            </template>
                    </el-table-column>
                    <el-table-column label="缴费记录类型" prop="serveType" width="130">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.serveType == 1">店铺交易服务费</el-tag>
                            <el-tag v-if="scope.row.serveType == 2">合同履约服务费用</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="待缴费金额" prop="residuePayFee" width="100">
                        <template v-slot="scope">
                            <span style="color: red">{{scope.row.residuePayFee}}元</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否欠费" prop="arrearageDateTime" width="100">
                        <template v-slot="scope">
                            <el-tag type="success" v-if="scope.row.arrearageDateTime == null">否</el-tag>
                            <el-tag type="danger" v-if="scope.row.arrearageDateTime != null">是</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="欠费时间" prop="arrearageDateTime" width="160">
                        <template v-slot="scope">
                            {{scope.row.arrearageDateTime | formatDate}}
                        </template>
                    </el-table-column>
                    <el-table-column label="是否停止服务" prop="stopServe" width="160">
                        <template v-slot="scope">
                            <el-tag type="success"  v-show="scope.row.stopServe==0">否</el-tag>
                            <el-tag type="danger"  v-show="scope.row.stopServe==1">是</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" prop="gmtCreate" width="160"/>
                    <el-table-column label="修改时间" prop="gmtModified" width="160"/>
                </el-table>
            </div>
            <Pagination
                v-show="tableData && tableData.length > 0"
                :currentPage.sync="paginationInfo.currentPage"
                :pageSize.sync="paginationInfo.pageSize"
                :total="paginationInfo.total"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="交易编号：">
                            <el-input clearable maxlength="100" placeholder="请输入缴费编号" v-model="filterData.platformDealFeeNu"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="企业名称：">
                            <el-input clearable maxlength="100" placeholder="请输入企业名称" v-model="filterData.enterpriseName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                <el-row>-->
<!--                    <el-col :span="12">-->
<!--                        <el-form-item label="店铺名称：">-->
<!--                            <el-input clearable maxlength="100" placeholder="请输入店铺名称" v-model="filterData.shopName"></el-input>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                </el-row>-->
<!--                下拉框方式选择状态-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费记录类型：">
                            <el-select v-model="filterData.serveType" placeholder="请选择缴费记录类型">
                                <el-option
                                    v-for="item in filterData.serveTypeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                <el-row>-->
<!--                    <el-col :span="24">-->
<!--                        <el-form-item label="状态：">-->
<!--                            <div style="display:flex">-->
<!--                                <el-checkbox-->
<!--                                    v-model="filterData.stateCheckAll" @change="stateAllSelect"-->
<!--                                >全部-->
<!--                                </el-checkbox>-->
<!--                                <el-checkbox-group-->
<!--                                    style="margin-left: 30px" v-model="filterData.states" @change="stateGroupChange">-->
<!--                                    <el-checkbox-->
<!--                                        v-for="option in filterData.stateSelect"-->
<!--                                        :key="option.value"-->
<!--                                        :label="option.value"-->
<!--                                    >{{ option.label }}</el-checkbox>-->
<!--                                </el-checkbox-group>-->
<!--                            </div>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                </el-row>-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.gmtCreate"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="修改时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.gmtModified"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { debounce, toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { supplierPlatformDealFee, supplierCreateDealFee, notPayDealFeeDtlList } from '@/api/fee/feeApi'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { mapState } from 'vuex'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState([ 'userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    data () {
        return {
            selectNotPayFeeLoading: false,
            showSelectNotPayFee: false,
            notPayFeeTableList: [],
            selectNotPayFeeTableList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            freeAmount: 2000,
            showAddFee: false,
            showAddFeeLoading: false,
            addFeeForm: {
                state: 0,
                payType: 1,
                serveType: 1,
                files: [],
                payAmount: 0.00,
                dtls: []
            },
            showAddFeeRoles: {
            },
            tableSelectRow: [], // 多选框选择的数据
            tableLoading: false, // 加载
            keywords: null, // 关键字
            keywords2: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            uploadImgSize: 10, // 上传文件大小
            tableData: [], // 表格数据
            filterData: { // 高级搜索
                stateCheckAll: false, // 选择全局
                state: null,
                states: [],
                platformDealFeeNu: null,
                shopName: null,
                enterpriseName: null,
                serveType: null,
                stateSelect: [
                    { value: 1, label: '待审核' },
                    { value: 2, label: '审核通过' },
                    { value: 3, label: '审核未通过' },
                ],
                serveTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '店铺交易服务费' },
                    { value: 2, label: '合同履约服务费用' },
                ],
                orderBy: 1,
                gmtCreate: [],
                gmtModified: [],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            uploadLoading: false,
            fileList: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('00', '00', '00')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    filters: {
        formatDate (str) {
            if(!str) return str
            return str.split(' ')[0]
        }
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        addFeeFormDtlDelete (row) {
            this.addFeeForm.dtls = this.addFeeForm.dtls.filter(t => {
                if(t.platformDealFeeDtlId != row.platformDealFeeDtlId) {
                    return true
                }else{
                    return false
                }
            })
        },
        selectNotPayFeeClick () {
            this.showSelectNotPayFee = true
            this.getNotPayFeeTableList()
        },
        getNotPayFeeTableList () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                serveType: this.addFeeForm.serveType,
            }
            if (this.keywords2 != null && this.keywords2 != '') {
                params.keywords = this.keywords2
            }
            this.selectNotPayFeeLoading = true
            notPayDealFeeDtlList(params).then(res => {
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
                this.notPayFeeTableList = res.list
            }).finally(() => {
                this.selectNotPayFeeLoading = false
            })
        },
        selectNotPayFeeAffirmClick () {
            if(this.selectNotPayFeeTableList.length == 0) {
                return this.$message.error('未选择数据！')
            }
            this.addFeeForm.dtls = this.selectNotPayFeeTableList
            this.countPayAmount()
            this.selectNotPayFeeTableList = []
            this.showSelectNotPayFee = false
        },
        countPayAmount () {
            let payAmount = 0
            for (let i = 0; i < this.addFeeForm.dtls.length; i++) {
                let t = this.addFeeForm.dtls[i]
                payAmount = payAmount + t.residuePayFee
            }
            this.addFeeForm.payAmount = this.fixed2(payAmount)
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        siteReceivingTableSelectM (value) {
            this.selectNotPayFeeTableList = value
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload (file) {
            this.uploadLoading = true
            let image = this.addFeeForm.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        save (num) {
            if (this.addFeeForm.files.length == 0) {
                return this.$message.error('请上传缴费证明！')
            }
            if (this.addFeeForm.dtls.length == 0) {
                return this.$message.error('未选择缴费明细！')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.addFeeForm.submitAud = num
                this.showAddFeeLoading = true
                let arr = []
                for (let i = 0; i < this.addFeeForm.dtls.length; i++) {
                    let t = this.addFeeForm.dtls[i]
                    arr.push({
                        platformDealFeeDtlId: t.platformDealFeeDtlId,
                        payAmount: t.residuePayFee
                    })
                }
                this.addFeeForm.dtls = arr
                supplierCreateDealFee(this.addFeeForm).then(res => {
                    if (res.code == null) {
                        this.$message.success('保存成功')
                        this.addFeeForm = {
                            state: 0,
                            payType: 1,
                            serveType: 1,
                            files: [],
                            payAmount: 0.00,
                            dtls: []
                        }
                        this.fileList = []
                        this.showAddFee = false
                        this.$router.push({
                            path: '/supplierSys/fee/dealDtl',
                            name: 'supplierSysFeeDealDtl',
                            query: {
                                sn: res
                            }
                        })
                    }
                }).finally(() => {
                    this.showAddFeeLoading = false
                })
            })
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.addFeeForm.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.addFeeForm.files = []
                this.fileList = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // 上传营业执照
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.addFeeForm.files = []
                    this.fileList = []
                }else {
                    let resO = res[0]
                    this.addFeeForm.files.push({
                        name: resO.objectName,
                        relevanceType: 2,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 多选框
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        // 行点击
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        // 跳转详情
        handleView (row) {
            this.$router.push({
                path: '/supplierSys/feeDtl/DealDtl',
                name: 'supplierSysFeeDtlDealDtl',
                query: {
                    sn: row.platformDealFeeNu
                }
            })
        },
        //重置数据
        resetSearchConditions () {
            this.filterData.state = null
            this.filterData.serveType = null
            this.filterData.platformDealFeeNu = null
            this.filterData.enterpriseName = null
            this.filterData.shopName = null
            this.filterData.stateCheckAll = false
            this.filterData.states = []
            this.filterData.gmtCreate = []
            this.filterData.gmtModified = []
        },
        // 状态全选
        stateAllSelect (value) {
            if (value) {
                this.filterData.states = this.filterData.stateSelect.map(t => {
                    return t.value
                })
            } else {
                this.filterData.states = []
            }
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateSelect.length
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.filterData.serveType != null) {
                params.serveType = this.filterData.serveType
            }
            if(this.filterData.states != null && this.filterData.states.length > 0) {
                params.states = this.filterData.states
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.platformDealFeeNu != null) {
                params.platformDealFeeNu = this.filterData.platformDealFeeNu
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.gmtCreate != null) {
                params.startGmtCreate = this.filterData.gmtCreate[0]
                params.endGmtCreate = this.filterData.gmtCreate[1]
            }
            if (this.filterData.gmtModified != null) {
                params.startGmtModified = this.filterData.gmtModified[0]
                params.endGmtModified = this.filterData.gmtModified[1]
            }

            this.tableLoading = true
            supplierPlatformDealFee(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
    appearance: textfield !important;
    -moz-appearance: textfield !important;
}
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
/deep/ .addDia {
    .el-dialog__body {
        height: 600px;
    }
}

/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>
