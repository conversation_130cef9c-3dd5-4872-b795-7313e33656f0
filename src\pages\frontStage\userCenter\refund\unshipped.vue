<template>
    <main>
        <div class="list-title df mb20">申请退货</div>
        <!--  商品信息  -->
        <div class="productInfo mb20">
            <div class="infoTop dfa">
                <div>订单编号</div>
                <div>商品信息</div>
                <div>品牌</div>
                <div>下单时间</div>
                <div>购买数量</div>
                <div>已退货数量</div>
            </div>
            <div class="infoCon dfa">
                <div>{{ orderInfo.orderNum }}</div>
                <div class="dfa">
                    <img
                        :src="orderInfo.pictureUrl?imgUrlPrefixAdd+orderInfo.pictureUrl:require('@/assets/images/img/queshen5.png')"
                        alt="">
                    <div>
                        <div class="tit textOverflow2">{{ orderInfo.title }}</div>

                    </div>
                </div>
                <div class="tc">{{ orderInfo.brand }}</div>
                <div class="tc">
                    <div class="mb10">{{ orderInfo.createTime.split(' ')[0] }}</div>
                    <div>{{ orderInfo.createTime.split(' ')[1] }}</div>
                </div>
                <div class="tc">{{ orderInfo.quantity }}</div>
                <div class="tc">{{ orderInfo.returnCount }}</div>
            </div>
        </div>
        <!--  商品信息结束  -->
        <el-form :model="form"  ref="form" :rules="rules" label-width="140px">
            <!--  退货信息  -->
            <div class="refundInfo mb20">
                <div class="row">
                    <div class="col">
                        <el-form-item label="服务类型：" prop="serviceType">
                            <el-radio v-model="form.serviceType" label="1">退货</el-radio>
                        </el-form-item>
                    </div>
                    <div class="col num">
                        <el-form-item label="提交数量：" prop="count">
                            <el-input-number v-model="form.count" :min="1" :max="orderInfo.quantity-orderInfo.returnCount"></el-input-number>
                            <span>您最多可提交数量为{{orderInfo.quantity-orderInfo.returnCount}}个</span>
                        </el-form-item>
                    </div>
                </div>
                <div class="row">
                    <div class="col reason">
                        <el-form-item label="提交原因：" prop="submitReason">
                            <el-select v-model="form.submitReason">
                                <el-option
                                    v-for="item in reasonOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div class="col">
                        <el-form-item label="问题描述：" prop="remarks">
                            <el-input type="textarea" :auto-resize="false" v-model="form.remarks"
                                      placeholder="请输入问题描述" maxlength="500" show-word-limit></el-input>
                        </el-form-item>
                    </div>
                </div>
            </div>
            <!--  退货信息结束  -->
            <!--  联系信息  -->
            <div class="subTitle"  v-if="orderInfo.state=10">确认信息{{orderInfo.state}}</div>
            <div class="contactInfo" v-if="orderInfo.state=10">
                <div class="row">
                    <el-form-item label="返回方式：" prop="returnMethod">
                        <el-radio-group v-model="form.returnMethod">
                            <el-radio :label="1">上门取件</el-radio>
                            <el-radio :label="2" >快递至卖家</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>
                <div v-show="form.returnMethod==1">

                    <div class="row">
                        <div class="col normal">
                            <el-form-item label="取件联系人：" prop="receiverName"
                                          :rules="form.returnMethod==1? rules.receiverName:[{
                                  required:false,
                                  message:'请输入注册地址'
                                }]">
                                <el-input v-model="form.receiverName" placeholder="请输入取件联系人"></el-input>
                            </el-form-item>
                        </div>
                        <div class="col normal">
                            <el-form-item label="取件联系电话：" prop="receiverMobile"
                                          :rules="form.returnMethod==1? rules.receiverMobile:[{
                                 required:false,
                                  message:'请输入注册地址'
                                }]">
                                <el-input v-model="form.receiverMobile" placeholder="请输入取件联系电话"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row addr">
                        <el-form-item label="取货地址：" prop="receiverAddress"
                                      :rules="form.returnMethod==1? rules.receiverAddress:[{
                                 required:false,
                                  message:'请输入注册地址'
                                }]">
                            <el-select v-model="address.province" @change="getSubDistrict(1)">
                                <el-option
                                    v-for="item in cascaderOptions.province"
                                    :key="item.districtId"
                                    :label="item.districtName"
                                    :value="item.districtName">
                                </el-option>
                            </el-select>
                            <el-select v-model="address.city" @change="getSubDistrict(2)">
                                <el-option
                                    v-for="item in cascaderOptions.city"
                                    :key="item.districtId"
                                    :label="item.districtName"
                                    :value="item.districtName">
                                </el-option>
                            </el-select>
                            <el-select v-model="address.district" @change="getSubDistrict('one')">
                                <el-option
                                    v-for="item in cascaderOptions.district"
                                    :key="item.districtId"
                                    :label="item.districtName"
                                    :value="item.districtName">
                                </el-option>
                            </el-select>
                            <el-input v-model="form.address" placeholder="请输入详细地址"  prop="address"></el-input>
                        </el-form-item>
                    </div>
                    <div class="separ"></div>
                </div>
                <div v-show="form.returnMethod==2">
                    <div class="row">
                        <div class="col normal">
                            <el-form-item label="物流公司：" prop="logisticsCompany"
                                          :rules="form.returnMethod==2? rules.logisticsCompany:[{
                                 required:false,
                                  message:'请输入物流公司'
                                }]">
                                <el-input v-model="form.logisticsCompany" placeholder="请输入物流公司"></el-input>
                            </el-form-item>
                        </div>
                        <div class="col normal">
                            <el-form-item label="物流单号：" prop="deliveryFlowId"
                                          :rules="form.returnMethod==2? rules.deliveryFlowId:[{
                                 required:false,
                                  message:'请输入物流单号'
                                }]">
                                <el-input v-model="form.logisticsNo" placeholder="请输入取件联系电话"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col normal">
                            <el-form-item label="收件联系人：" prop="sendName"
                                          :rules="form.returnMethod==2? rules.sendName:[{
                                 required:false,
                                  message:'请输入注册地址'
                                }]">
                                <el-input v-model="form.sendName" placeholder="请输入收件联系人"></el-input>
                            </el-form-item>
                        </div>
                        <div class="col normal">
                            <el-form-item label="收件联系电话：" prop="sendMobile"
                                          :rules="form.returnMethod==2? rules.sendMobile:[{
                                 required:false,
                                  message:'请输入注册地址'
                                }]">
                                <el-input v-model="form.sendMobile" placeholder="请输入收件联系电话"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="row addr">
                        <el-form-item label="收货地址：" prop="sendAddress"
                                      :rules="form.returnMethod==2? rules.sendAddress:[{
                                 required:false,
                                  message:'请输入注册地址'
                                }]">
                            <el-input v-model="form.sendAddress" placeholder="请输入收货详细地址"  prop="sendAddress"></el-input>
                        </el-form-item>
                    </div>

                </div>
            </div>
            <!--  联系信息结束  -->
        </el-form>
        <button @click="onSubmit">提交</button>
    </main>
</template>

<script>
import { getCascaderOptions } from '@/api/platform/common/components'
import { create } from '@/api/frontStage/orderRetuen'
import { getShopInfoById } from '@/api/frontStage/shop'

export default {
    data () {
        return {
            orderInfo: {
                // orderNum: '501369751',
                // pictureUrl: '',
                // title: 'F5L913活塞 活塞环组件 大修包 适用道依茨工程机械柴油发动机',
                // spec: '规格：pc360-8M0分流阀',
                // brand: '三一重工',
                // createTime: '2022/11/05 15:26:47',
                // quantity: 3
            },
            form: {
                serviceType: '1',
                count: 1,
                submitReason: '',
                remarks: '',
                returnMethod: 1,
                receiverName: '',
                receiverAddress: '',
                address: '',
                orderId: '',
                sendMobile: '',
                sendAddress: '',
                sendName: '',
                orderSn: '',
                state: 1

            },
            rules: {
                serviceType: { required: true, message: '请选择服务类型', trigger: 'blur' },
                count: { required: true, message: '请选择正确的提交数量', trigger: 'blur' },
                submitReason: { required: true, message: '请选择提交原因', trigger: 'blur' },
                remarks: { required: true, message: '请输入问题描述', trigger: 'blur' },
                returnMethod: { required: true, message: '请选择返回方式', trigger: 'blur' },
                receiverName: { required: true, message: '请输入取件联系人', trigger: 'blur' },
                sendMobile: [
                    { required: true, message: '请输入取件联系电话', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                receiverMobile: [
                    { required: true, message: '请输入取件联系电话', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                receiverAddress: { required: true, message: '请输入取件地址', trigger: 'blur' },
                sendAddress: { required: true, message: '请输入收件地址', trigger: 'blur' },
                deliveryFlowId: { required: true, message: '请输入物流单号', trigger: 'blur' },
                logisticsCompany: { required: true, message: '请输入物流公司', trigger: 'blur' },
                sendName: { required: true, message: '请输入收件人名称', trigger: 'blur' },
                address: { required: true, message: '请填写详细地址', trigger: 'blur' },
            },
            reasonOptions: [
                { value: 1, label: '7天无理由退货' },
                { value: 2, label: '其他' },
                { value: 3, label: '物流太慢' },
            ],
            cascaderOptions: {
                province: [],
                city: [],
                district: '',
            },
            cascaderOptions2: {
                province: [],
                city: [],
                district: '',
            },
            address: {
                province: '',
                city: '',
                district: '',
            },
        }
    },
    created () {
        this.orderInfo = this.$route.params.row
        console.log(this.orderInfo)
        this.getshopAddress(this.orderInfo.shopId)
        this.getCascader()
    },
    mounted () {
    },
    methods: {
        getshopAddress (shopId) {
            getShopInfoById({ id: shopId }).then(res=>{
                this.shop = res
                console.log(shopId)
                this.form.sendAddress = this.shop.returnAddress
                this.form.sendName = this.shop.returnRelationName
                this.form.sendMobile = this.shop.returnRelationNumber
            })
        },
        async getCascader () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.cascaderOptions.province = res
            this.cascaderOptions2.province = res
        },
        // 获取下级区域
        async getSubDistrict (i) {
            if (typeof i === 'number') {
                if (i < 3) {
                    this.address.district = ''
                }
            }
            let key, distCode
            if (i === 1) {
                this.address.city = ''
                key = 'city'
                this.cascaderOptions.province.forEach(item => {
                    if (item.districtName === this.address.province) distCode = item.districtCode
                })
            } else if (i === 2) {
                key = 'district'
                this.cascaderOptions.city.forEach(item => {
                    if (item.districtName === this.address.city) distCode = item.districtCode
                })
            }
            let res = await getCascaderOptions({ distCode, })
            if (i === 1 || i === 2 || i === 'one') {
                this.form.receiverAddress = `${this.address.province}${this.address.city}${this.address.district}`
                this.cascaderOptions[key] = res
            }
        },
        onSubmit () {
            let rule = 'form'
            this.$refs[rule].validate(valid => {
                if (valid) {
                    this.form.orderId = this.orderInfo.orderId
                    this.form.orderItemId = this.orderInfo.orderItemId
                    create(this.form).then(res=>{
                        if (res.code == 200) {
                            this.$message({
                                message: '申请退货成功',
                                type: 'success'
                            })
                            this.$router.go(-1)
                        }
                    })
                }
            })
        }
    },
}

</script>

<style scoped lang="scss">
.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.list-title, .productInfo, .refundInfo, .contactInfo {
    border: 1px solid rgba(230, 230, 230, 1);
}

.productInfo {
    color: #333;

    .infoTop {
        height: 40px;
        padding: 0 30px;
        border-bottom: 1px solid rgba(230, 230, 230, 1);
        background-color: #FAFAFA;

        div:first-child {
            width: 240px;
        }

        div:nth-child(2) {
            width: 248px;
        }

        div:nth-child(3) {
            width: 152px;
            text-align: center;
        }

        div:nth-child(4) {
            width: 152px;
            text-align: center;
        }
        div:nth-child(5) {
            width: 152px;
            text-align: center;
        }

        div:last-child {
            width: 152px;
            text-align: center;
        }
    }

    .infoCon {
        padding: 30px;

        & > div:first-child {
            width: 240px;
        }

        img {
            width: 80px;
            height: 80px;
            margin-right: 20px;
            object-fit: cover;
        }

        .dfa {
            width: 248px;
            padding-right: 10px;

            .tit {
                margin-bottom: 15px;
            }

            .spec {
                color: #999;
            }
        }

        .tc {
            width: 152px;
            text-align: center;
        }
    }
}

/deep/ .el-form {
    .row {
        width: 1044px;
        margin-bottom: 25px;
        display: flex;

        .col {
            width: 523px;
        }
    }

    .el-radio__inner {
        background-color: transparent;

        &::after {
            width: 9px;
            height: 9px;
            margin: 0 auto;
            background-color: #216EC6;
        }
    }

    .el-form-item {
        margin: 0;
    }

    .el-form-item__label {
        height: 50px;
        line-height: 50px;
        font-size: 16px;
        color: #333;
    }

    .el-form-item__content {
        line-height: 50px;
    }

    .num.col {
        .el-input {
            width: 101px;
            height: 26px;
        }

        span {
            margin-left: 20px;
            color: #999;
        }
    }

    .el-input-number {
        &, .el-input__inner {
            width: 101px;
            height: 26px;
        }

        .el-input-number__decrease, .el-input-number__increase {
            width: 24px;
            height: 24px;
            line-height: 24px;
            top: 2px !important;
        }
    }

    .col.reason {
        .el-input__inner {
            width: 320px;
        }
    }

    .el-textarea {
        width: 320px;
    }

    .el-textarea__inner {
        width: 320px;
        height: 120px;
        margin-right: 0;
        padding: 17px 15px;
        line-height: 20px;
        border-radius: 0;
        outline: none;
        resize: none;
        position: relative;

        ::-webkit-input-placeholder {
            color: #ccc;
        }

        span {
            position: absolute;
            right: 10px;
            bottom: 0;
        }
    }

    .el-input__inner {
        height: 50px;
        border-radius: 0;
        font-size: 16px;
    }

    .el-radio__label {
        color: #333;
    }

    .refundInfo {
        padding: 30px;
    }

    .subTitle {
        padding-left: 20px;
        margin-bottom: 15px;
        font-size: 18px;
        color: #333;
    }

    .contactInfo {
        padding: 30px;

        .col.normal {
            .el-input__inner {
                width: 320px;
            }
        }

        .el-select {
            width: 90px;
            margin-right: 10px;

            .el-input, .el-input__inner {
                width: 90px;
            }

            .el-input__inner {
                padding-left: 10px;
            }
        }

        .row.addr .el-form-item__content {
            display: flex;
            width: 844px;
        }

        .separ {
            width: 100%;
            height: 1px;
            margin-bottom: 30px;
            background-color: #E6E6E6;
        }
    }
}

button {
    width: 150px;
    height: 50px;
    margin: 30px auto 0;
    font-size: 22px;
    color: #fff;
    background-color: #216EC6;
    display: block;
}
</style>