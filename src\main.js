import ElementUI from '@/plugins/elementui'
import App from './App.vue'
import store from './store'
import '@/utils/verify.js'
import filters from './utils/filters.js'
import _ from 'lodash'
import router from './router'
import '@/assets/css/style.scss'
import '@/assets/css/style1.scss'
import '@/utils/directives.js'
import { getByCity } from '@/api/frontStage/systemParam'

import Print from 'vue-print-nb'
Vue.use(Print)

//将流程按钮组件注册为全局组件
import ComExaButtons from '@/components/common/exaButtons'
Vue.component('ComExaButtons', ComExaButtons)

//注册详细页面顶部组件
import BillTop from '@/components/common/billTop'
Vue.component('BillTop', BillTop)

//注册悬浮审核历史组件
import ToolHistory from '@/components/common/toolHistory'
Vue.component('ToolHistory', ToolHistory)

// 全局过滤器
Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key]) //插入过滤器名和对应方法
})

//单条删除提示

Vue.prototype.openWindowTab = function (path) {
    let href = typeof path === 'string' ? path : router.resolve(path).href
    window.open(href, '_blank')
}
import publicFunc from '@/utils/publicFunc.js'
Vue.prototype.publicFunc = publicFunc
Vue.prototype.showDevFunc = true // 是否显示开发中的功能，发布正式版本是改为false
Vue.prototype.materialFee = true //收费
Vue.prototype.closePlan = 1 //完结订单  0收料扣减  1完结
Vue.prototype.businessShopID = '1878734518961074177' //物资子公司自营店店铺id  测试
// Vue.prototype.businessShopID = '1858777977810120705' //物资子公司自营店店铺id  正式

// //
// Vue.prototype.showDevFunc = false // 是否显示开发中的功能，发布正式版本是改为false
// Vue.prototype.materialFee = false //收费
// Vue.prototype.inventoryBid = false //清单竞价
// Vue.prototype.closePlan = 1 //完结订单
//
Vue.prototype.uploadImgSize = 10 // 图片上传大小限制

// // // 上传地址前缀
// Vue.prototype.imgUrlPrefixDelete = 'http://192.168.91.11:9000' // 测试
// Vue.prototype.imgUrlPrefixAdd = 'http://192.168.91.16:9022' // 测试

// Vue.prototype.imgUrlPrefixDelete = 'http://192.168.91.1:9000' // 正式
// Vue.prototype.imgUrlPrefixAdd = 'https://mmcp.scrbg.com' // 正式
// Vue.prototype.imgUrlPrefixAdd = 'http://118.122.115.194:443/' // 正式
getByCity({ code: 'imgUrlPrefixDelete', size: 1 }).then(res=>{
    console.log( res, 'imgUrlPrefixDelete')
    if (res && res.length > 0) Vue.prototype.imgUrlPrefixDelete = res[0].keyValue
})
getByCity({ code: 'imgUrlPrefixAdd', size: 1 }).then(res=>{
    console.log( res[0].keyValue, 'imgUrlPrefixAdd')
    if (res && res.length > 0) Vue.prototype.imgUrlPrefixAdd = res[0].keyValue
})
//详情页顶部高度
Vue.prototype.topHeight = 70
//供wpf测试调用
window.test = params => alert(params)
Vue.prototype.$bus = new Vue()

Vue.config.productionTip = false

Vue.prototype.$ELEMENT = { size: 'small' }
Vue.prototype._ = _

const vm = new Vue({
    router,
    store,
    ElementUI,
    render: h => h(App)
}).$mount('#app')

export default vm