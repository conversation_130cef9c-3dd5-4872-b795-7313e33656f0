import service from '@/utils/request'

const { httpPost, httpGet } = service

const listMyCreateBiding = params => {
    return httpPost({
        url: '/materialMall/shopManage/biddingPurchase/listMyCreateBiding',
        params
    })
}
const listAllCreateBiding = params => {
    return httpPost({
        url: '/materialMall/platform/biddingPurchase/listAllCreateBiding',
        params
    })
}
// 查询可以选择的竞价采购的零星采购多供方订单明细
const listBidingOrderItemsList = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/listBidingOrderItemsList',
        params
    })
}
const listBidingOrderList = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/listBidingOrderList',
        params
    })
}
const listBidingOrderListIds = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/listBidingOrderListIds',
        params
    })
}
const bidingRecordListByEntity = params => {
    return httpPost({
        url: '/materialMall/shopManage/biddingBidRecordItem/getBidingRecordItemInfo',
        params
    })
}
const isHitBidingSubmit = params => {
    return httpGet({
        url: '/materialMall/shopManage/biddingPurchase/isHitBidingSubmit',
        params
    })
}
const platformBidingRecordListByEntity = params => {
    return httpPost({
        url: '/materialMall/platform/biddingBidRecordItem/getPlatformBidingRecordItemInfo',
        params
    })
}
const auditBidingInfo = params => {
    return httpPost({
        url: '/materialMall/platform/biddingPurchase/auditBidingInfo',
        params
    })
}
const auditHitBidding = params => {
    return httpPost({
        url: '/materialMall/platform/biddingPurchase/auditHitBidding',
        params
    })
}
const createBidingOrderItemsByBiddingId = params => {
    return httpPost({
        url: '/materialMall/shopManage/biddingPurchase/createBidingOrderItemsByBiddingId',
        params
    })
}
const deleteBidingOrderItemsByBiddingId = params => {
    return httpPost({
        url: '/materialMall/shopManage/biddingPurchase/deleteBidingOrderItemsByBiddingId',
        params
    })
}
const submitBidingByIds = params => {
    return httpPost({
        url: '/materialMall/shopManage/biddingPurchase/submitBidingByIds',
        params
    })
}
const deleteBidingByBiddingId = params => {
    return httpGet({
        url: '/materialMall/shopManage/biddingPurchase/deleteBidingByBiddingId',
        params
    })
}
const updateBidingByOrder = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/updateBidingByOrder',
        params
    })
}
const loseEfficacyBidding = params => {
    return httpGet({
        url: '/materialMall/shopManage/biddingPurchase/loseEfficacyBidding',
        params
    })
}
const batchUpdateBiddingItemInfo = params => {
    return httpPost({
        url: '/materialMall/shopManage/biddingPurchase/batchUpdateBiddingItemInfo',
        params
    })
}
const getBiddingPurchaseInfo = params => {
    return httpGet({
        url: '/materialMall/shopManage/biddingPurchase/getBiddingPurchaseInfo',
        params
    })
}

const getRecordBiddingInfo = params => {
    return httpGet({
        url: '/materialMall/biddingBidRecord/getBiddingRecordInfo',
        params
    })
}

const updateAuditStatus = params => {
    return httpPost({
        url: '/materialMall/biddingBidRecord/updateAuditStatus',
        params
    })
}

const getBiddingRecordInfo = params => {
    return httpPost({
        url: '/materialMall/shopManage/biddingPurchase/getBiddingRecordInfo',
        params
    })
}

const getPlatformBiddingPurchaseInfo = params => {
    return httpGet({
        url: '/materialMall/platform/biddingPurchase/getPlatformBiddingPurchaseInfo',
        params
    })
}
const createInventoryBidding = params => {
    return httpPost({
        url: '/materialMall/supplier/synthesizeTemporary/createBidding',
        params,
    })
}
const createInventoryBidingByBiddingId = params => {
    return httpPost({
        url: '/materialMall/shopManage/biddingPurchase/createInventoryBidingByBiddingId',
        params,
    })
}
const bidOpening = params => {
    return httpGet({
        url: '/materialMall/biddingPurchase/bidOpening',
        params,
    })
}

const deadlineTime = params => {
    return httpPost({
        url: '/materialMall/biddingPurchase/deadlineTime',
        params,
    })
}

export {
    updateAuditStatus,
    getRecordBiddingInfo,
    bidOpening,
    deadlineTime,
    listMyCreateBiding,
    listAllCreateBiding,
    listBidingOrderItemsList,
    createBidingOrderItemsByBiddingId,
    getBiddingPurchaseInfo,
    getBiddingRecordInfo,
    batchUpdateBiddingItemInfo,
    deleteBidingByBiddingId,
    submitBidingByIds,
    listBidingOrderList,
    updateBidingByOrder,
    getPlatformBiddingPurchaseInfo,
    auditBidingInfo,
    listBidingOrderListIds,
    deleteBidingOrderItemsByBiddingId,
    loseEfficacyBidding,
    platformBidingRecordListByEntity,
    isHitBidingSubmit,
    auditHitBidding,
    bidingRecordListByEntity,
    createInventoryBidding,
    createInventoryBidingByBiddingId
}