const count = {
    //===================维修保养记录  计算==============================
    //基础信息-材料费 = 装备维修清单-材料费（元）汇总
    onBaseUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.materialsFee
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //基础信息-人工费（元） = 装备维修清单-人工费（元）汇总 保留两位小数
    onBaseArtificialFee (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.artificialFee
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //基础信息-维保费（元） = 装备维修清单-维保费（元）汇总 保留两位小数
    onBaseMaintenancePrice (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.maintenanceFee
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //基础信息-辅助装备费（元） = 装备维修清单-辅助装备费（元）汇总 保留两位小数
    onBaseAuxiliaryFee (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.assistFee
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //装备维修清单-材料费（元） = 物资明细获取金额向上汇总 保留两位小数
    onUpCollect (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.sum
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //装备维修清单-维保费用（元） = 材料费（元）+人工费（元）+辅助装备费（元） 保留两位小数
    onMaintenanceFeet (materialsFee, artificialFee, assistFee) {
        let sum = 0
        sum = Number(materialsFee + artificialFee + assistFee).toFixed(2)
        return parseFloat(sum)
    },
    //装备维修清单-人工费（元） = 人工费向上汇总 保留两位小数
    onArtificialFee (tableData) {
        let sum = 0
        tableData.forEach(v=>{
            if(v.changeType !== -1) {
                sum += +v.fee
            }
        })
        return parseFloat(Number(sum).toFixed(2))
    },
    //物资明细单价 = 金额/数量 保留两位小数
    onMaterialsDtlPrice (amount, num) {
        let sum = 0
        if(num == 0) {
            sum = 0
        }else{
            sum = Number(amount / num).toFixed(2)
        }
        return parseFloat(sum)
    },
}
export default count
