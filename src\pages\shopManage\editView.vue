<template>
    <div class="edit-box">
        <div class="edit-title">{{ title }}</div>
        <div class="edit-content">
            <div class="page-nav">
                <div class="nav-item">基本信息</div>
            </div>
            <div class="edit-area">
                <div class="edit-area-top">
                    <div class="title"><div></div>基本信息</div>
                    <div class="edit-content-inner">
                        <el-form :model="form" ref="form" :rules="rules" label-width="80px" :inline="false" size="normal">
                            <el-form-item label="分类ID:">
                                <span>{{ form.classId }}</span>
                            </el-form-item>
                            <el-form-item label="分类名称:">
                                <el-input v-model="form.className" placeholder="请输入分类名称"></el-input>
                            </el-form-item>
                            <el-form-item label="分类层级:">
                                <el-select v-model="form.classLevel" placeholder="请选择">
                                    <el-option v-for="item in selectLevel" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="分类类型:">
                                <el-select v-model="form.classType" placeholder="请选择">
                                    <el-option v-for="item in selectType" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
                <div class="btns">thisisi</div>
            </div>
        </div>
    </div>
</template>
<script>
import axios from 'axios'
export default {
    data () {
        return {
            title: '新增',
            form: {
                classId: 112341,
                className: '',
                classLevel: null,
                classType: null,
            },
            selectLevel: [
                {
                    value: 1,
                    label: '一级分类'
                },
                {
                    value: 2,
                    label: '二级分类'
                },
                {
                    value: 3,
                    label: '三级分类'
                },
            ],
            selectType: [
                {
                    value: 0,
                    label: '物资'
                },
                {
                    value: 1,
                    label: '装备'
                },
                {
                    value: 2,
                    label: '周材（物资）'
                },
                {
                    value: 3,
                    label: '周材（装备）'
                },
                {
                    value: 4,
                    label: '二手装备'
                },
                {
                    value: 5,
                    label: '租赁装备'
                },
            ]
        }
    },
    methods: {
        handleSubmit () {
            console.log(this.form)
            let url
            if(this.title === '修改') {
                url = '/productCategoryTest/productCategory/update'
            }else{
                url = '/productCategoryTest/productCategory/create'
            }
            axios({
                method: 'post',
                url,
                data: this.form
            }).then(res => {
                console.log(res)
                this.$router.back()
            })
        }
    },
    mounted () {
        if(this.$route.query.data) {
            this.form = JSON.parse(this.$route.query.data)
            console.log(this.form)
            this.title = '修改'
        }
    }
}
</script>
<style scoped lang="scss">
.edit-box{
    display: flex;
    flex-direction: column;
    background-color: #eff2f6;
}
.edit-title{
    width: 100%;
    font-size: 20px;
    // background-color: beige;
    padding: 24px 16px;
}
.edit-content{
    display: flex;
    flex-grow: 1;
    &>div{
        height: 100%;
    }
    .page-nav{
        width: 200px;
        border-right: 2px solid rgb(225, 225, 225);
        .nav-item{padding: 14px 6px;}
        &>.nav-item:first-child{
            font-weight: bold;
            color: #409eff;
            background-color: #fff;
            user-select: none;
        }
    }
    .edit-area{
        flex-grow: 1;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        .edit-area-top{
            padding: 6px 12px;
            flex-grow: 1;
        }
        .title{
            margin-bottom: 16px;
            color: #409eff;
            display: flex;
            align-items: center;
            &>div:first-child{
                width: 6px;
                height: 20px;
                margin-right: 8px;
                border-radius: 3px;
                background-color: #409eff;
            }
        }
        .edit-content-inner{
            div, input{
                margin-bottom: 10px;
            }
            .el-input, .el-select{
                width: 400px;
                height: 36px;
            }
        }
        .btns{
            width: 100%;
            height: 70px;
            background-color: #eff2f6;
            display: flex;
            justify-content: right;
        }
    }
}
button{
    width: 100px;
    height: 35px;
    color: #fff;
    background-color: #216ec6;
}
</style>