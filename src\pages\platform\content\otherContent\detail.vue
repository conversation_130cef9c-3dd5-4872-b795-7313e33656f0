<template>
    <div class="base-page">

        <div class="right">
            <!-- ---------------------新增编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="发布状态：">
                                {{ formData.state == 1 ? '发布' : '未发布' }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="发布时间：">{{ formData.gmtRelease }}</el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="内容管理：">
                                <editor v-model="formData.content"></editor>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>

            <div class="footer">
                <div class="right-btn">
                    <el-button class="btn-delete" native-type="button" type="primary" v-if="formData.state == 2"
                        @click="handlePublish()">发布</el-button>
                    <el-button native-type="button" type="primary" v-else @click="handleNotPublish()">取消发布</el-button>
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
// eslint-disable-next-line
import { mc, getList, update, getInfo } from '@/api/platform/content/aboutUs'
import { batchPublish, batchNotPublish } from '../../../../api/platform/content/richContent'
import editor from '../../../../components/quillEditor'

// import { hideLoading, showLoading } from '../../../../utils/common'
export default {
    components: {
        editor
    },
    data () {
        return {
            alertName: '友情链接',
            picTypeFilter: [
                { value: 0, label: '选项1' }
            ],
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入链接名', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            // 新增编辑 表单数据
            formData: {
                name: '',
                address: '',
                tel: '',
                remarks: '',
                state: 1,
                content: '',
                gmtModified: ''
            },
            arr: [],
            requestParams: {},
            programaKey: '',
            storageState: false,
        }
    },
    watch: {
        $route: {
            handler (val) {
                this.programaKey = val.query.programaKey
                this.getData()
            }
        }
    },
    methods: {
        // 保存/取消保存
        onSave () {
            this.clientPop('info', '您确定要保存吗', () => {
                update({ ...this.formData, programaKey: this.$route.query.programaKey }).then(res => {
                    console.log(res)
                    if (res.code === 200) {
                        this.storageState = false
                        this.getData()
                        return this.clientPop('suc', '保存成功',)
                    }
                })
            })
        },
        // 发布/取消发布
        handleNotPublish () {
            this.clientPop('info', '您确定要取消发布吗？', () => {
                // 发布代码
                batchNotPublish([this.formData.contentId]).then(res => {
                    console.log(res)
                    if (res.code === 200) {
                        this.getData()
                        return this.clientPop('suc', '取消发布成功')
                    }
                })
            })
            this.storageState = false
            this.unPublishData()
        },
        publishData () {
            batchPublish([this.formData.contentId]).then(res => {
                console.log(res)
                if (res.code == 200) {
                    this.getData()
                    this.storageState = false
                    return this.clientPop('suc', '发布成功')
                }
            })
        },
        // 发布
        handlePublish () {
            if (!this.storageState) {
                return this.clientPop('info', '您还没有保存数据，确定继续发布吗？', () => {
                    this.publishData()
                })
            }
        },
        getData () {
            getInfo({ programaKey: this.programaKey }).then(res => {
                console.log(res)
                this.formData = res
                // alert('获取了数据')
            })
        },
    },
    created () {
        this.programaKey = this.$route.query.programaKey
        this.getData()
    },
    mounted () { },
}
</script>
<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-form {}

.right-btn {
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.avatar-uploader {
    /deep/.el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

/deep/.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

/deep/.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>