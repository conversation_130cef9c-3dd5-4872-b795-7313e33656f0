<template>
    <main>
        <div class="list-title df mb20">修改企业认证</div>
        <div class="content">
            <el-form :model="enterpriseForm" :rules="enterpriseFormRules" ref="enterpriseFormV" label-width="168px" class="enterpriseForm"
                    :inline="false">
                    <div class="df">
                        <!-- 营业执照 -->
                        <el-form-item class="licenseUploader" label="营业执照图片(推荐：750x420)：" prop="businessLicense">
                            <el-upload class="avatar-uploader" action="fakeaction" :before-upload="handleBeforeUpload" name="img" :auto-upload="true"
                                :show-file-list="false" :on-change="handleUploadChange" :http-request="uploadLicenseEnterprise">
                                <img v-if="enterpriseForm.businessLicense" :src="enterpriseForm.businessLicense" class="avatar">
                                <div v-else class="licenseUploader">
                                    <img src="@/assets/images/userCenter/upload_yyzz.png" />
                                </div>
                            </el-upload>
                        </el-form-item>
                        <el-form-item class="licenseUploader">
                            <div class="uploadDemo dfa">
                                <img src="@/assets/images/userCenter/yyzz_demo.png" alt="">
                                <div><span>示例图</span><i class="el-icon-zoom-in"></i></div>
                            </div>
                        </el-form-item>
                        <div class="uploadTip">请上传10MB以内的PNG，JPG，GIF，BMP格式图片</div>
                    </div>
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="企业名称：" prop="enterpriseName">
                                <el-input clearable v-model="enterpriseForm.enterpriseName" placeholder="请填写50字以内的企业名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                                <el-input clearable v-model="enterpriseForm.socialCreditCode" disabled></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 新增-供方类型 -->
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="供方类型：" prop="supplierType">
                                <div style="line-height: 50px;"><el-radio v-model="enterpriseForm.supplierType" label="1">生产商</el-radio>
                                <el-radio v-model="enterpriseForm.supplierType" label="2">贸易商</el-radio></div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item label="纳税人类别：" prop="taxpayerType">
                            <el-select v-model="enterpriseForm.taxpayerType" placeholder="请选择">
                                <el-option
                                    v-for="item in taxpayers"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="主营业务：" prop="mainBusiness">
                                <el-input clearable v-model="enterpriseForm.mainBusiness" placeholder="请填写与营业执照相同的主营业务"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item class="licenseValidTime" label="法定代表人：" prop="legalRepresentative">
                                <el-input clearable v-model="enterpriseForm.legalRepresentative" placeholder="请填写50字以内的法定代表人姓名"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="11" :offset="0">
                            <el-form-item label="企业注册时间：" prop="creationTime">
                                <el-date-picker v-model="enterpriseForm.creationTime" align="right" style="width: 100%" type="date" value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="请选择企业注册时间" :picker-options="pickerOptions" @change="handleLog">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" :offset="1">
                            <el-form-item class="licenseValidTime" label="营业执照有效期：" prop="licenseTerm">
                                <el-date-picker v-model="enterpriseForm.licenseTerm" align="right" type="date" :value-format="dateFormat"
                                    placeholder="请选择营业执照有效期" :picker-options="pickerAfterOptions">
                                </el-date-picker>
                                <el-checkbox label="长期" :indeterminate="false" v-model="longTerm"></el-checkbox>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="11">
                            <el-form-item label="注册资本(万元)：" prop="registeredCapital">
                                <el-input clearable v-model="enterpriseForm.registeredCapital"  pattern="[0-9]*" placeholder="请填写企业注册资本"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
              <!--企业注册地址-固定工作地址-->
              <el-row>
                <el-col :span="11">
                  <el-form-item  class="registerAddress" label="企业注册地址：" prop="address">
                    <div>
                      <el-select
                        ref="selectLabel1"
                        class="province"
                        v-model="enterpriseForm.provincesCode"
                        placeholder="省份"
                        @change="(code) => getSubDistrict(code, 1)"
                      >
                        <el-option
                          v-for="item in addressOptions.province"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                      <el-select ref="selectLabel2" class="city" v-model="enterpriseForm.cityCode" value-key="" placeholder="地级市"
                                 @change="(code) => getSubDistrict(code, 2)">
                        <el-option
                          v-for="item in addressOptions.city"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                      <el-select ref="selectLabel3" @visible-change="addressChange"  class="county" v-model="enterpriseForm.countyCode" value-key="" placeholder="区、县">
                        <el-option
                          v-for="item in addressOptions.district"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item  class="registerAddress" label="固定工作地址：" prop="address_gd">
                    <div>
                      <el-select
                        ref="selectLabel1_gd"
                        class="province"
                        v-model="enterpriseForm.provincesGdCode"
                        placeholder="省份"
                        @change="(code) => getSubDistrict(code, 3)"
                      >
                        <el-option
                          v-for="item in addressOptionsGd.province"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                      <el-select ref="selectLabel2_gd" class="city" v-model="enterpriseForm.cityGdCode" value-key="" placeholder="地级市"
                                 @change="(code) => getSubDistrict(code, 4)">
                        <el-option
                          v-for="item in addressOptionsGd.city"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                      <el-select ref="selectLabel3_gd" @visible-change="addressChange_gd"  class="county" v-model="enterpriseForm.countyGdCode" value-key="" placeholder="区、县">
                        <el-option
                          v-for="item in addressOptionsGd.district"
                          :key="item.value"
                          :label="item.districtName"
                          :value="item.districtCode"
                        />
                      </el-select>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--详细地址-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="企业注册详细地址：" prop="detailedAddress">
                    <el-input v-model="enterpriseForm.detailedAddress" placeholder="请输入企业注册详细地址"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="固定工作详细地址：" prop="workXxdz" placeholder="请输入固定工作详细地址">
                    <el-input v-model="enterpriseForm.workXxdz" />
                  </el-form-item>
                </el-col>
              </el-row>
              <!--资质信息-->
              <div class="separ center"></div>
              <div class="subtitle">资质信息</div>
              <div class="separ center"></div>
              <!--主要业绩-->
              <h3 style="margin-bottom: 20px;margin-left: 2%;">主要业绩</h3>
              <el-button size="small" type="primary" class="addcardtop" @click="handleAdd()">添加</el-button>
              <!--表格-->
              <div class="custom-table">
                <el-table
                  :data="tableData"
                  stripe
                  border
                  style="width: 100%">
                  <!-- 操作列 -->
                  <el-table-column label="操作" width="60px" align="center">
                    <template slot-scope="scope" style="text-align: center">
                      <el-button
                        style="width: 30px;margin: 0 auto;padding: 0"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDelete(scope.$index)">
                      </el-button>
                    </template>
                  </el-table-column>
                  <!-- 项目名称列 -->
                  <el-table-column prop="projectName" label="项目名称" width="200px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.projectName" placeholder="请输入内容" :border="false" style="outline: none;width: 178px !important;">{{ scope.row.projectName }}</el-input>
                    </template>
                  </el-table-column>
                  <!-- 供应物资品类 -->
                  <el-table-column prop="supplyCategory" label="供应物资品类" width="200px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.supplyCategory" placeholder="请输入内容" :border="false" style="outline: none;width: 178px !important;">{{ scope.row.supplyCategory }}</el-input>
                    </template>
                  </el-table-column>
                  <!-- 合同金额（万元） -->
                  <el-table-column prop="contractAmount" label="合同金额（万元）" width="120px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.contractAmount" placeholder="请输入内容" :border="false" style="outline: none;width: 98px !important;">{{ scope.row.contractAmount }}</el-input>
                    </template>
                  </el-table-column>
                  <!-- 供货起止时间 -->
                  <el-table-column prop="ghdate" label="供货起止时间" width="400px" align="center">
                    <template slot-scope="scope">
                      <el-date-picker
                        v-model="scope.row.ghdate"
                        type="daterange"
                        :value-format="dateFormat"
                        range-separator="至"
                        start-placeholder="请选择"
                        end-placeholder="请选择"  style="width: 100%;">
                      </el-date-picker>
                    </template>
                  </el-table-column>
                  <!-- 业绩证明人 -->
                  <el-table-column prop="proofPerson" label="业绩证明人" width="120px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.proofPerson"  :border="false" style="outline: none;width: 98px !important;">{{ scope.row.proofPerson }}</el-input>
                    </template>
                  </el-table-column>
                  <!-- 证明人联系电话 -->
                  <el-table-column prop="proofPhone" label="证明人联系电话" width="200px" align="center">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.proofPhone"  style="outline: none;width: 178px !important;">{{ scope.row.proofPhone }}</el-input>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <!--质量认证-->
              <h3 style="margin-top: 20px;margin-bottom: 20px;margin-left: 2%;">质量认证</h3>
              <div style="margin-left: 2%">
                <el-checkbox-group v-model="enterpriseForm.certificate" style="display: flex">
                  <el-checkbox label="ISO9001质量体系认证" value="1"></el-checkbox>
                  <el-checkbox label="铁路产品CRCC认证" value="2"></el-checkbox>
                  <el-checkbox label="交通产品CCPC认证" value="3"></el-checkbox>
                  <el-checkbox label="CCC认证" value="4"></el-checkbox>
                  <el-checkbox label="其他质量认证" @change="showQuality()" value="5"></el-checkbox>
                </el-checkbox-group>
                <textarea v-model="enterpriseForm.certificateOther" v-show="OtherQualityCertifications" placeholder="请填写其他质量认证" style="width: 900px;height: 100px;resize:none;margin-top: -3%"></textarea>
              </div>
              <!--企业基本情况-->
              <h3 style="margin-top: 20px;margin-bottom: 20px;margin-left: 2%">企业基本情况</h3>
              <div style="margin-left: 2%;margin-bottom: 20px;">
                <div style="font-size: 20px">企业概况</div>
                <textarea style="width: 900px;height: 70px;resize:none;" v-model="enterpriseForm.companyProfile"></textarea>
              </div>
              <div style="margin-left: 2%;margin-bottom: 20px;">
                <div style="font-size: 20px">财务情况</div>
                <textarea style="width: 900px;height: 70px;resize:none;" v-model="enterpriseForm.financialSituation"></textarea>
              </div>
              <div style="margin-bottom: 20px;margin-left: 2%;">
                <div style="font-size: 20px">诉讼情况</div>
                <textarea style="width: 900px;height: 70px;resize:none;" v-model="enterpriseForm.litigationSituation"></textarea>
              </div>
              <!--设置对公账户-->
              <div class="separ center"></div>
              <div class="setAccount">
                <div class="subtitle">设置对公账户</div>
                <div class="subtitletip">企业账户信息，填写内容将写入合同并涉及发票，请谨慎填写。</div>
              </div>
              <!--开户银行、银行账户-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="开户银行：" prop="bankName">
                    <el-input clearable v-model="enterpriseForm.bankName"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="银行户名：" prop="accountName">
                    <el-input clearable v-model="enterpriseForm.accountName"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--银行账号-->
              <el-row>
                <el-col :span="23">
                  <el-form-item label="银行账号：" prop="bankAccount">
                    <el-input clearable v-model="enterpriseForm.bankAccount"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--开票备注-->
              <el-row>
                <el-col :span="24" >
                  <el-form-item label="开票备注：">
                    <el-input clearable v-model="enterpriseForm.invoiceRemark"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 法定代表人-->
              <div class="separ center"></div>
              <div class="subtitle">法定代表人</div>
              <!--双面身份证上传-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="身份证人像面照（推荐：420x180）：" prop="legalPersonFace">
                    <el-upload
                      class="identityUpload face"
                      action="fakeaction"
                      :http-request="(res) => uploadIdentity(res, 3,1)"
                      :show-file-list="false">
                      <img class="identityUpload" v-if="enterpriseForm.legalPersonFace" :src="enterpriseForm.legalPersonFace"
                           alt="">
                      <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="身份证国徽面照（推荐：420x180）：" prop="legalPersonNational">
                    <el-upload
                      class="identityUpload badge"
                      action="fakeaction"
                      :http-request="(res) => uploadIdentity(res, 4,1)"
                      :show-file-list="false">
                      <img class="identityUpload" v-if="enterpriseForm.legalPersonNational" :src=" enterpriseForm.legalPersonNational"
                           alt="">
                      <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--姓名身份证号码-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="姓名：" prop="legalPersonName">
                    <el-input clearable v-model="enterpriseForm.legalPersonName" placeholder="请填写真实姓名"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="身份证号码：" prop="legalPersonNum">
                    <el-input clearable v-model="enterpriseForm.legalPersonNum" placeholder="请填写18位身份证号码"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--有效开始日期-结束日期-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="有效期开始日期：" prop="lpStartTime">
                    <el-date-picker
                      v-model="enterpriseForm.lpStartTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :picker-options="pickerOptions"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="lpEndTime">
                    <el-date-picker
                      v-model="enterpriseForm.lpEndTime"
                      align="right"
                      type="date"
                      :value-format="dateFormat"
                      :picker-options="pickerAfterOptions"
                      placeholder="请选择"
                    />
                    <el-checkbox label="长期" :indeterminate="false" v-model="szlpEndTime"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--设置管理员-->
              <div class="separ center"></div>
              <div class="subtitle">设置管理员</div>
              <!--双面身份证上传-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="身份证人像面照（推荐：420x180）：" prop="cardPortraitFace">
                    <el-upload
                      class="identityUpload face"
                      action="fakeaction"
                      :http-request="(res) => uploadIdentity(res, 1,1)"
                      :show-file-list="false">
                      <img class="identityUpload" v-if="enterpriseForm.cardPortraitFace" :src="enterpriseForm.cardPortraitFace"
                           alt="">
                      <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="身份证国徽面照（推荐：420x180）：" prop="cardPortraitNationalEmblem">
                    <el-upload
                      class="identityUpload badge"
                      action="fakeaction"
                      :http-request="(res) => uploadIdentity(res, 2,1)"
                      :show-file-list="false">
                      <img class="identityUpload" v-if="enterpriseForm.cardPortraitNationalEmblem" :src=" enterpriseForm.cardPortraitNationalEmblem"
                           alt="">
                      <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--姓名身份证号码-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="姓名：" prop="adminName">
                    <el-input clearable v-model="enterpriseForm.adminName" placeholder="请填写真实姓名"/>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="身份证号码：" prop="adminNumber">
                    <el-input clearable v-model="enterpriseForm.adminNumber" placeholder="请填写18位身份证号码"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--有效开始日期-结束日期-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="有效期开始日期：" prop="adminPeriodStart">
                    <el-date-picker
                      v-model="enterpriseForm.adminPeriodStart"
                      align="right"
                      type="date"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :picker-options="pickerOptions"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="有效期结束日期：" prop="adminPeriodEnd">
                    <el-date-picker
                      v-model="enterpriseForm.adminPeriodEnd"
                      align="right"
                      type="date"
                      style="width: 100%"
                      :value-format="dateFormat"
                      :picker-options="pickerAfterOptions"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!--手机号 短信验证码-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="手机号码：" prop="adminPhone">
                    <el-input type="number"  clearable v-model="enterpriseForm.adminPhone" placeholder="请输入11位手机号码"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--附件上传-->
              <div class="separ center"></div>
              <div class="subtitle">附件上传</div>
              <div class="separ center"></div>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="承诺书：" prop="powerOfAttorney">
                    <el-upload
                      :on-change="handleChangeCns"
                      :before-upload="handleBeforeUpload"
                      :http-request="uploadCns"
                      :on-remove="handleRemoveAttorney"
                      class="upload-demo"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :file-list="cnsFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                    <div @click="downloadCns">下载承诺书模板</div>
                  </el-form-item>
                </el-col>
                <!--授权委托书-->
                <el-col :span="11" :offset="1">
                  <el-form-item label="授权委托书：" prop="propsqwts">
                    <el-upload
                      :http-request="uploadSqwts"
                      class="upload-demo"
                      :on-remove="handleRemovePropsqwts"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeSqwts"
                      :file-list="sqwtsFileList">
                      <el-button type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                    <div @click="downloadsqwts">下载授权委托书模板</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--法定代表人身份证明-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="法定代表人身份证明：" prop="propfaren">
                    <el-upload
                      :http-request="uploadFrSfzm"
                      class="upload-demo"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeFrSfzm"
                      :on-remove="handleRemovePropfaren"
                      :file-list="fddbrsfzmFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                    <div @click="downloadFrSfzm">下载法定代表人身份证明模板</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="separ center"></div>
              <!--最近一期完税证明、税务评级证明-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="最近一期完税证明：" prop="zjyqwszm">
                    <el-upload
                      :http-request="uploadWszm"
                      :before-upload="handleBeforeUpload"
                      :on-remove="handleRemoveZjyqwszm"
                      class="upload-demo"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeWszm"
                      :file-list="zjyqwszmFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="税务评级证明：" prop="swpjzm">
                    <el-upload
                      :http-request="uploadSwpjzm"
                      :before-upload="handleBeforeUpload"
                      :on-remove="handleRemoveSwpjzm"
                      class="upload-demo"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeSwpjzm"
                      :file-list="swpjzmFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--有效期开始日期-结束日期-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="有效期开始日期：" prop="tpcStartTime">
                    <el-date-picker
                      v-model="enterpriseForm.tpcStartTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      :value-format="dateFormat"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item class="licenseValidTime" label="有效期开始日期：" prop="trcStartTime">
                    <el-date-picker
                      v-model="enterpriseForm.trcStartTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      :value-format="dateFormat"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="有效期结束日期：" prop="tpcEndTime">
                    <el-date-picker
                      v-model="enterpriseForm.tpcEndTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12" :offset="1">
                  <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="trcEndTime">
                    <el-date-picker
                      v-model="enterpriseForm.trcEndTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      :value-format="dateFormat"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="separ center"></div>
              <!--信用中国报告、中国执行信息公开网查询情况-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="信用中国报告：" prop="xyzgbg">
                    <el-upload
                      :http-request="uploadXyzgbg"
                      :before-upload="handleBeforeUpload"
                      class="upload-demo"
                      :on-remove="handleRemoveXyzgbg"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeXyzgbg"
                      :file-list="xyzgbgFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item label="中国执行信息公开网查询情况：" prop="zgzxxxgk">
                    <el-upload
                      :http-request="uploadZxxx"
                      :before-upload="handleBeforeUpload"
                      :on-remove="handleRemoveZgzxxxgk"
                      class="upload-demo"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :on-change="handleChangeZxxx"
                      :file-list="zgzxxxgkwFileList">
                      <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--有效期开始日期-结束日期-->
              <el-row>
                <el-col :span="11">
                  <el-form-item label="有效期开始日期：" prop="ccrStartTime">
                    <el-date-picker
                      v-model="enterpriseForm.ccrStartTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item class="licenseValidTime" label="有效期开始日期：" prop="zxgkStartTime">
                    <el-date-picker
                      v-model="enterpriseForm.zxgkStartTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      :value-format="dateFormat"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="11">
                  <el-form-item label="有效期结束日期：" prop="ccrEndTime">
                    <el-date-picker
                      v-model="enterpriseForm.ccrEndTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item class="licenseValidTime" label="有效期结束日期：" prop="zxgkEndTime">
                    <el-date-picker
                      v-model="enterpriseForm.zxgkEndTime"
                      align="right"
                      type="date"
                      style="width: 100%"
                      :value-format="dateFormat"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <!--添加资质证书、添加其他资料-->
              <div class="separ center"></div>
              <div>
                <el-button size="small" type="primary" class="addcardtop" @click="addZzzs">添加资质证书</el-button>
                <el-button size="small" type="primary" class="addcardtop" @click="addQtzl">添加其他资料</el-button>
              </div>
              <div style="display: flex;margin-bottom: 30px;">
                <!--上传添加资质证书-->
                <div class="addcard" v-if="isZzzs">
                  <div class="cardtop">
                    <div>资质证书</div>
                    <el-button class="addcarddel" size="small" type="primary" @click="deleteZzzs">删除</el-button>
                  </div>
                  <el-row><el-upload
                    :http-request="uploadZzzs"
                    :before-upload="handleBeforeUpload"
                    class="upload-demo"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :on-change="handleChange"
                    :file-list="zzzsFileList">
                    <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                  </el-upload></el-row>
                  <el-row>
                    <el-form-item class="licenseValidTime" label="有效期开始日期：">
                      <el-date-picker
                        v-model="enterpriseForm.qcStartTime"
                        align="right"
                        type="date"
                        style="width: 61%"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptions"
                        placeholder="请选择"
                      />
                    </el-form-item>
                  </el-row>
                  <el-row>
                    <el-form-item class="licenseValidTime" label="有效期结束日期：">
                      <el-date-picker
                        v-model="enterpriseForm.qcEndTime"
                        align="right"
                        type="date"
                        :value-format="dateFormat"
                        :picker-options="pickerAfterOptions"
                        placeholder="请选择"
                      />
                      <el-checkbox style="margin-right: 10px" label="永久有效" :indeterminate="false" v-model="szQcEndTime"/>
                    </el-form-item>
                  </el-row>
                </div>
                <!--                          上传添加资质证书-->
                <div class="addcard" v-if="isQtzs">
                  <div class="cardtop">
                    <div>其他资料</div>
                    <el-button class="addcarddel" size="small" type="primary" @click="deleteQtzs">删除</el-button>
                  </div>
                  <el-row><el-upload
                    :http-request="uploadQtzs"
                    :before-upload="handleBeforeUpload"
                    class="upload-demo"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :on-change="handleChange"
                    :file-list="qtzsFileList">
                    <el-button size="small" type="primary" class="uploadbtn" icon="el-icon-upload2">上传文件</el-button>
                  </el-upload></el-row>
                  <el-row>
                    <el-form-item class="licenseValidTime" label="有效期开始日期：">
                      <el-date-picker
                        v-model="enterpriseForm.otherStartTime"
                        align="right"
                        type="date"
                        style="width: 61%"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptions"
                        placeholder="请选择"
                      />
                    </el-form-item>
                  </el-row>
                  <el-row>
                    <el-form-item class="licenseValidTime" label="有效期结束日期：">
                      <el-date-picker
                        v-model="enterpriseForm.otherEndTime"
                        align="right"
                        type="date"
                        :value-format="dateFormat"
                        :picker-options="pickerAfterOptions"
                        placeholder="请选择"
                      />
                      <el-checkbox style="margin-right: 10px" label="永久有效" :indeterminate="false" v-model="szOtherEndTime"/>
                    </el-form-item>
                  </el-row>
                </div>
              </div>
              <div class="separ center"></div>
                <!-- <div class="separ center"></div>
                <div class="subtitle">附件资料</div>
                <el-row>
                    <el-col :span="24" style="height: unset;" v-loading="fileLoading">
                        <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                            <el-upload
                                ref="multi-upload"
                                class="multi-file-uploader"
                                action="fakeaction"
                                accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                :on-remove="handleRemove"
                                multiple
                                :limit="10"
                                :before-upload="beforeOneOfFilesUpload"
                                :http-request="uploadOneOfFiles"
                                :on-exceed="handleExceed"
                                :file-list="enterpriseForm.files"
                            >
                                <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip"><span>请上传</span>
                                    <div class="file dfa pointer" v-for="file in fileList" :key="file.url">
                                        <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                    </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row> -->
                    <el-row>
                        <el-col :span="24">
                        </el-col>
                    </el-row>
                </el-form>
                <div class="btns center dfb">
                    <button @click="$router.go(-1)">返回</button>
                    <button @click="onSubmit">提交</button>
                </div>
        </div>
    </main>
</template>
<script>
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { getCascaderOptions } from '@/api/platform/common/components'
import { getEnterpriseAuthInfo } from '@/api/frontStage/verification'
import { updateEnterprise } from '@/api/frontStage/verification'
import { createWFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { findByProgramaKey } from '@/api/w/richContent'
export default {
    data () {
        return {
            isZzzs: false,
            isQtzs: false,
            fileLoading: false,
            enterpriseFormFileList: [],
            fileList: [],
            cnsFileList: [],
            sqwtsFileList: [],
            fddbrsfzmFileList: [],
            zjyqwszmFileList: [],
            zgzxxxgkwFileList: [],
            swpjzmFileList: [],
            xyzgbgFileList: [],
            zzzsFileList: [],
            qtzsFileList: [],
            uploadImgSize: 4,
            agreeTerm: false,
            enterpriseForm: {
                businessLicense: '',
                enterpriseName: '',
                socialCreditCode: '',
                enterpriseType: '',
                legalRepresentative: '',
                creationTime: '',
                licenseTerm: '',
                registeredCapital: '',
                provinces: '',
                city: '',
                county: '',
                provincesCode: '',
                cityCode: '',
                countyCode: '',
                provincesGdCode: '',
                cityGdCode: '',
                countyGdCode: '',
                detailedAddress: '',
                mainBusiness: '',
                taxRate: null,
                cardPortraitFace: '',
                cardPortraitNationalEmblem: '',
                adminPhone: '',
                verificationCode: '',
                adminName: '',
                adminPassword: '',
                adminNumber: '',
                agreeTerm: false,
                files: [],
                mallType: 0,
                verifyImg: '',
                verifyId: '',
                legalPersonFace: '',
                legalPersonFaceId: '',
                legalPersonNational: '',
                legalPersonNationalId: '',
                supplierType: '1',
                certificate: [],
                certificateOther: '',
                workXxdz: '',
                provincesGd: '',
                cityGd: '',
                countyGd: '',
            },
            //企业表单验证
            enterpriseFormRules: {
                businessLicense: { required: true, message: '请上传营业执照!', trigger: 'blur' },
                /*powerOfAttorney: { required: true, message: '请上传承诺书!', trigger: 'blur' },*/
                powerOfAttorney: { required: true, validator: this.validatePowerOfAttorney, trigger: 'change' },
                /*propfaren: { required: true, message: '请上传法定代表人身份证明!', trigger: 'blur' },*/
                /*propsqwts: { required: true, message: '请上传授权委托书!', trigger: 'blur' },*/
                propsqwts: { required: true, validator: this.validatePropsqwts, trigger: 'change' },
                zjyqwszm: { required: true, validator: this.validateZjyqwszm, trigger: 'change' },
                zgzxxxgk: { required: true, validator: this.validateZgzxxxgk, trigger: 'change' },
                /*zjyqwszm: { required: true, message: '请上传最近一期完税证明!', trigger: 'blur' },*/
                /*swpjzm: { required: true, message: '请上传税务评级证明!', trigger: 'blur' },*/
                swpjzm: { required: true, validator: this.validateSwpjzm, trigger: 'change' },
                /*xyzgbg: { required: true, message: '请上传信用中国报告!', trigger: 'blur' },*/
                xyzgbg: { required: true, validator: this.validateXyzgbg, trigger: 'change' },
                /*zgzxxxgk: { required: true, message: '请上传中国执行信息公开网查询情况!', trigger: 'blur' },*/
                enterpriseName: [
                    { required: true, message: '请填写企业名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '请填写50字以内的企业名称', trigger: 'blur' }
                ],
                socialCreditCode: [
                    { required: true, message: '请输入统一信用代码', trigger: 'blur' },
                    { min: 18, max: 18, message: '请输入18位统一信用代码', trigger: 'blur' }
                ],
                supplierType: [
                    { required: true, message: '请选择供方类型', trigger: 'blur' }
                ],
                taxpayerType: [
                    { required: true, message: '请选择纳税人类别', trigger: 'blur' }
                ],
                legalRepresentative: [
                    { required: true, message: '请填写企业法定代表人', trigger: 'blur' },
                    { min: 2, max: 10, message: '请填写企业法定代表人', trigger: 'blur' }
                ],
                registeredCapital: [
                    { required: true, message: '请输入注册资本',  trigger: 'blur' }
                    // { validator: this.checkInt, trigger: 'blur' }
                ],
                bankName: [
                    { required: true, message: '请输入开户银行',  trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' }
                ],
                accountName: [
                    { required: true, message: '请输入银行户名',  trigger: 'blur' },
                    { min: 1, max: 50, message: '超过限制', trigger: 'blur' }
                ],
                bankAccount: [
                    { required: true, message: '请输入银行账号',  trigger: 'blur' },
                    { min: 1, max: 19, message: '超过限制', trigger: 'blur' }
                ],
                creationTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                tpcStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                trcStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                tpcEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                trcEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                ccrStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                zxgkStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                lpStartTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                adminPeriodStart: { required: true, validator: this.validateDate, trigger: 'blur' },
                adminPeriodEnd: { required: true, validator: this.validateDate, trigger: 'blur' },
                ccrEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                zxgkEndTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                // licenseTerm: { required: true, validator: this.validateDate, trigger: 'blur' },
                address: { required: true, validator: this.validateAddress, trigger: 'change' },
                address_gd: { required: true, validator: this.validateAddress_gd, trigger: 'change' },
                detailedAddress: [
                    { required: true, message: '请填写企业注册详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                workXxdz: [
                    { required: true, message: '请填写固定工作详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                taxRate: [
                    { required: true, validator: this.validateTaxRate, trigger: 'blur' },
                ],
                mainBusiness: [
                    { required: true, message: '请填写与营业执照相同的主营业务', trigger: 'blur' },
                    { min: 1, max: 1000, message: '超过限制', trigger: 'change' }
                ],
                legalPersonFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                legalPersonNational: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                cardPortraitFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                cardPortraitNationalEmblem: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                adminName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                adminNumber: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                adminPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                verificationCode: [
                    { required: true, message: '请输入6位短信验证码', trigger: 'blur' },
                    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' },
                ],
                adminPassword: { min: 8, max: 20, required: true, validator: this.validatePassword, trigger: 'blur' },
            },
            // 获取地址选择器数据
            async getAddressPickerOptions () {
                let res = await getCascaderOptions({ distCode: '100000' })
                this.addressOptions.province = res
                this.addressOptionsGd.province = res
            },
            pickerAfterOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            // 日期选择器选项
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() > Date.now()
                },
                shortcuts: [{
                    text: '今天',
                    onClick (picker) {
                        picker.$emit('pick', new Date())
                    }
                }, {
                    text: '昨天',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24)
                        picker.$emit('pick', date)
                    }
                }, {
                    text: '一周前',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
                        picker.$emit('pick', date)
                    }
                }]
            },
            // 地址选择器选项
            addressOptions: {
                province: [],
                city: [],
                district: []
            },
            dateFormat: 'yyyy-MM-dd HH:mm:ss',
            showTerm: false,
            longTerm: false,
            //纳税人类别
            taxpayers: [
                {
                    value: '1',
                    label: '一般纳税人'
                },
                {
                    value: '2',
                    label: '小规模纳税人'
                }
            ],
            addressOptionsGd: {
                province: [],
                city: [],
                district: []
            },
            szQcEndTime: false,
            szOtherEndTime: false,
            tableData: [],
            OtherQualityCertifications: false,
            szlpEndTime: false,
        }
    },
    created () {
        this.getEnterpriseAuthInfoM()
        this.getAddressPickerOptions()
        //this.getRegisterAgreeUser()
    },
    mounted () {},
    watch: {
        longTerm (val) {
            if(val) {
                this.enterpriseForm.licenseTerm = null
            }
        },
        'enterpriseForm.licenseTerm': {
            handler (val) {
                if(val) {
                    this.longTerm = false
                }else{
                    this.longTerm = true
                }
            }
        },
        szQcEndTime (val) {
            if(val) {
                this.enterpriseForm.qcEndTime = null
            }
        },
        'enterpriseForm.qcEndTime': {
            handler (val) {
                if(val) {
                    this.szQcEndTime = false
                }else{
                    this.szQcEndTime = true
                }
            }
        },
        szOtherEndTime (val) {
            if(val) {
                this.enterpriseForm.otherEndTime = null
            }
        },
        'enterpriseForm.otherEndTime': {
            handler (val) {
                if(val) {
                    this.szOtherEndTime = false
                }else{
                    this.szOtherEndTime = true
                }
            }
        },
        szlpEndTime (val) {
            if(val) {
                this.enterpriseForm.lpEndTime = null
            }
        },
        'enterpriseForm.lpEndTime': {
            handler (val) {
                if(val) {
                    this.szlpEndTime = false
                }else{
                    this.szlpEndTime = true
                }
            }
        }
    },
    methods: {
        downloadFrSfzm () {
            this.handleDownload( { fileFarId: '1924378621633327106', name: '法定代表人（单位负责人）授权委托书.docx' } )
        },
        async uploadFrSfzm (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.enterpriseFormRules.propfaren.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.lpIdentification = url
                })
                //this.enterpriseForm.lpIdentification = res[0].nonIpObjectPath
                this.enterpriseForm.lpIdentificationId = res[0].recordId
                this.addEnterpriseFiles(res[0].objectName, '法定代表人身份证明', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadIdentity (params, num, type) {
            //上传管理员身份证人像面
            if(num === 1 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    if(type == 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.enterpriseForm.cardPortraitFace = window.URL.createObjectURL(res)
                        })
                        this.enterpriseForm.cardPortraitFaceId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '管理员身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传管理员身份证国徽面
            if(num === 2 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    if(type === 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.enterpriseForm.cardPortraitNationalEmblem = url
                        })
                        this.enterpriseForm.cardPortraitNationalEmblemId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '管理员身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传法人身份证人像面
            if(num === 3 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    //企业注册
                    if(type == 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            this.enterpriseForm.legalPersonFace = window.URL.createObjectURL(res)
                        })
                        this.enterpriseForm.legalPersonFaceId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '法定代表人身份证人像面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
            //上传法人身份证国徽面
            if(num === 4 ) {
                let form = this.createUploadFile(params.file)
                uploadFile(form).then(res => {
                    this.$message({
                        message: '上传成功',
                        type: 'success'
                    })
                    //企业注册
                    if(type === 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.enterpriseForm.legalPersonNational = url
                        })
                        this.enterpriseForm.legalPersonNationalId = res[0].recordId
                        this.addEnterpriseFiles(res[0].objectName, '法定代表人身份证国徽面照', res[0].nonIpObjectPath, res[0].recordId)
                    }
                })
            }
        },
        showQuality () {
            if(this.enterpriseForm.certificate.includes('其他质量认证')) {
                this.OtherQualityCertifications = true
            }else {
                this.OtherQualityCertifications = false
                this.enterpriseForm.certificateOther = null
            }
        },
        handleDelete (index) {
            this.$confirm('确定要删除这条记录吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.tableData.splice(index, 1)
                this.$message.success('删除成功!')
            })
        },
        handleAdd () {
            this.tableData.push({})
        },
        validatePowerOfAttorney (rule, value, callback) {
            if (this.cnsFileList.length === 0) {
                callback(new Error('请上传承诺书!'))
            } else {
                callback()
            }
        },
        validatePropsqwts (rule, value, callback) {
            if (this.sqwtsFileList.length === 0) {
                callback(new Error('请上传授权委托书!'))
            } else {
                callback()
            }
        },
        validateZjyqwszm (rule, value, callback) {
            if (this.zjyqwszmFileList.length === 0) {
                callback(new Error('请上传最近一期完税证明!'))
            } else {
                callback()
            }
        },
        validateZgzxxxgk (rule, value, callback) {
            if (this.zgzxxxgkwFileList.length === 0) {
                callback(new Error('请上传中国执行信息公开网查询情况!'))
            } else {
                callback()
            }
        },
        validateSwpjzm (rule, value, callback) {
            if (this.swpjzmFileList.length === 0) {
                callback(new Error('请上传税务评级证明!'))
            } else {
                callback()
            }
        },
        validateXyzgbg (rule, value, callback) {
            if (this.xyzgbgFileList.length === 0) {
                callback(new Error('请上传信用中国报告!'))
            } else {
                callback()
            }
        },
        deleteQtzs () {
            this.isQtzs = false
            this.enterpriseForm.other = null
            this.enterpriseForm.otherId = null
            this.enterpriseForm.otherStartTime = null
            this.enterpriseForm.otherEndTime = null
        },
        deleteZzzs () {
            this.isZzzs = false
            this.enterpriseForm.qualificationCertificate = null
            this.enterpriseForm.qualificationCertificateId = null
            this.enterpriseForm.qcStartTime = null
            this.enterpriseForm.qcEndTime = null
        },
        downloadCns () {
            this.handleDownload( { fileFarId: '1924375289770504193', name: '承诺书.docx' } )
        },
        downloadsqwts () {
            this.handleDownload( { fileFarId: '1924378853041467394', name: '授权委托书.docx' } )
        },
        createUploadFile (file) {
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            return form
        },
        addEnterpriseFiles (objectName, fileName, nonIpObjectPath, recordId) {
            console.log(this.enterpriseForm.files)
            this.enterpriseForm.files.forEach( (e, index) => {
                if (e.category == fileName) {
                    this.enterpriseForm.files.splice(index, 1)
                }
            })
            console.log(this.enterpriseForm.files)
            let fileSuffix = objectName.substr(objectName.lastIndexOf('.') + 1)
            this.enterpriseForm.files.push({
                name: fileName + '.' + fileSuffix,
                relevanceType: 8,
                url: nonIpObjectPath,
                fileType: 3,
                fileFarId: recordId,
                category: fileName
            })
        },
        async uploadZzzs (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.qualificationCertificate = url
                })
                //this.enterpriseForm.qualificationCertificate = res[0].nonIpObjectPath
                this.enterpriseForm.qualificationCertificateId = res[0].recordId
                this.addEnterpriseFiles(res[0].objectName, '资质证书', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadWszm (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.enterpriseFormRules.zjyqwszm.required = false
                this.enterpriseForm.taxPaymentCertificateId = res[0].recordId
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.taxPaymentCertificate = url
                })
                //this.enterpriseForm.taxPaymentCertificate = res[0].nonIpObjectPath
                this.addEnterpriseFiles(res[0].objectName, '最近一期完税证明', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadCns (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.enterpriseFormRules.powerOfAttorney.required = false
                this.enterpriseForm.letterCommitmentId = res[0].recordId
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.letterCommitment = url
                })
                //this.enterpriseForm.letterCommitment = res[0].nonIpObjectPath
                this.addEnterpriseFiles(res[0].objectName, '承诺书', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadSqwts (params) {
            let form = this.createUploadFile(params.file)
            form.append('fileType', 3)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.enterpriseFormRules.propsqwts.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.adminAuthorize = url
                })
                //this.enterpriseForm.adminAuthorize = res[0].nonIpObjectPath
                this.enterpriseForm.adminAuthorizeId = res[0].recordId
                this.addEnterpriseFiles(res[0].objectName, '授权委托书', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadSwpjzm (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                //this.enterpriseFormRules.swpjzm.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.taxRatingCertificate = url
                })
                //this.enterpriseForm.taxRatingCertificate = res[0].nonIpObjectPath
                this.enterpriseForm.taxRatingCertificateId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.addEnterpriseFiles(res[0].objectName, '税务评级证明', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadXyzgbg (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                //this.enterpriseFormRules.xyzgbg.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.creditChinaReport = url
                })
                //this.enterpriseForm.creditChinaReport = res[0].nonIpObjectPath
                this.enterpriseForm.creditChinaReportId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.addEnterpriseFiles(res[0].objectName, '信用中国报告', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadZxxx (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                //this.enterpriseFormRules.zgzxxxgk.required = false
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.zxgk = url
                })
                //this.enterpriseForm.zxgk = res[0].nonIpObjectPath
                this.enterpriseForm.zxgkId = res[0].recordId
                this.addEnterpriseFiles(res[0].objectName, '中国执行信息公开网查询情况', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        async uploadQtzs (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.other = url
                })
                //this.enterpriseForm.other = res[0].nonIpObjectPath
                this.enterpriseForm.otherId = res[0].recordId
                this.addEnterpriseFiles(res[0].objectName, '其他证书', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        handleChangeCns (file, fileList) {
            this.cnsFileList = fileList
        },
        handleChangeSqwts (file, fileList) {
            this.sqwtsFileList = fileList
        },
        handleChangeFrSfzm  (file, fileList) {
            this.fddbrsfzmFileList = fileList
        },
        handleChangeWszm (file, fileList) {
            this.zjyqwszmFileList = fileList
        },
        handleChangeSwpjzm (file, fileList) {
            this.swpjzmFileList = fileList
        },
        handleChangeZxxx (file, fileList) {
            this.zgzxxxgkwFileList = fileList
        },
        handleChangeXyzgbg (file, fileList) {
            this.xyzgbgFileList = fileList
        },
        handleChange () {
        },
        handleRemoveAttorney (file, fileList) {
            this.cnsFileList = fileList
        },
        handleRemovePropsqwts (file, fileList) {
            this.sqwtsFileList = fileList
        },
        handleRemovePropfaren (file, fileList) {
            this.fddbrsfzmFileList = fileList
        },
        handleRemoveZjyqwszm (file, fileList) {
            this.zjyqwszmFileList = fileList
        },
        handleRemoveSwpjzm (file, fileList) {
            this.swpjzmFileList = fileList
        },
        handleRemoveZgzxxxgk (file, fileList) {
            this.zgzxxxgkwFileList = fileList
        },
        handleRemoveXyzgbg (file, fileList) {
            this.xyzgbgFileList = fileList
        },
        addZzzs () {
            this.isZzzs = true
        },
        addQtzl () {
            this.isQtzs = true
        },
        addressChange_gd (val) {
            if(!val) {
                this.enterpriseForm.provincesGd = this.$refs.selectLabel1_gd.selectedLabel
                this.enterpriseForm.cityGd = this.$refs.selectLabel2_gd.selectedLabel
                this.enterpriseForm.countyGd = this.$refs.selectLabel3_gd.selectedLabel
                this.enterpriseForm.workXxdz = this.enterpriseForm.provincesGd + this.enterpriseForm.cityGd + this.enterpriseForm.countyGd
            }
        },
        validateNumber (rule, value, callback) {
            if (value < 0 || value >= 100) {
                callback(new Error('供应商税率不能超过100'))
            } else {
                callback()
            }
        },
        writeTaxRate () {
            console.log(this.enterpriseForm.taxRate, 0 <= this.enterpriseForm.taxRate && this.enterpriseForm.taxRate <= 100)
            if (this.enterpriseForm.taxRate != null) {
                if (!(0 <= this.enterpriseForm.taxRate && this.enterpriseForm.taxRate <= 100)) {
                    this.$message.error('税率不能小于0或大于100')
                    this.enterpriseForm.taxRate = 0
                }
            }else {
                this.$message.error('税率不能为空')
                this.enterpriseForm.taxRate = 0
            }
        },
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading2 = true
            let uploadRes = await uploadFile(form)
            this.fileLoading2 = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.enterpriseFormFileList.push(file)
                this.enterpriseFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                this.enterpriseForm.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,
                })
            }
        },
        handleRemove (file) {
            this.fileLoading = true
            let files = this.enterpriseForm.files
            let recordId = null
            let newFiles = files.filter(t =>{
                if(file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                }else {
                    return true
                }
            })
            createWFileRecordDelete({ recordId: recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.enterpriseForm.files = newFiles
                }
                this.fileLoading = false
            }).catch(() => {
                this.fileLoading = false
            })
        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if(size > 100) {
                this.$message.error('文件大小不能超过100M')
                return false
            }
            return true
        },
        handleExceed () {
            this.$message.error('文件个数不能超出10个')
            return false
        },
        //获取成为个体户提所需下载附件资料
        getRegisterAgreeUser () {
            findByProgramaKey({ programaKey: 'companyRegistration' }).then(res => {
                this.fileList = res.files
            })
        },
        addressChange (val) {
            if(!val) {
                this.enterpriseForm.provinces = this.$refs.selectLabel1.selectedLabel
                this.enterpriseForm.city = this.$refs.selectLabel2.selectedLabel
                this.enterpriseForm.county = this.$refs.selectLabel3.selectedLabel
                let newAddress = this.enterpriseForm.provinces + this.enterpriseForm.city + this.enterpriseForm.county
                this.enterpriseForm.detailedAddress = newAddress
            }
        },
        // 获取企业信息
        getEnterpriseAuthInfoM () {
            getEnterpriseAuthInfo({}).then(res => {
                this.enterpriseForm = res
                previewFile({ recordId: this.enterpriseForm.businessLicenseId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.businessLicense = url
                })
                previewFile({ recordId: this.enterpriseForm.legalPersonFaceId }).then(res => {
                    this.enterpriseForm.legalPersonFace = window.URL.createObjectURL(res)
                })
                previewFile({ recordId: this.enterpriseForm.legalPersonNationalId }).then(res => {
                    this.enterpriseForm.legalPersonNational = window.URL.createObjectURL(res)
                })
                previewFile({ recordId: this.enterpriseForm.cardPortraitFaceId }).then(res => {
                    this.enterpriseForm.cardPortraitFace = window.URL.createObjectURL(res)
                })
                previewFile({ recordId: this.enterpriseForm.cardPortraitNationalEmblemId }).then(res => {
                    this.enterpriseForm.cardPortraitNationalEmblem = window.URL.createObjectURL(res)
                })
                this.enterpriseForm.registeredCapital += ''
                if(this.enterpriseForm.qcStartTime != null) {
                    this.isZzzs = true
                }
                if(this.enterpriseForm.otherStartTime != null) {
                    this.isQtzs = true
                }
                this.tableData = this.enterpriseForm.epLists
                console.log(this.enterpriseForm)
                this.getCityOrDistrict(res.provincesCode, 1)
                this.getCityOrDistrict(res.cityCode, 2)
                this.getCityOrDistrict(res.provincesGdCode, 3)
                this.getCityOrDistrict(res.cityGdCode, 4)
                this.showQuality()
                this.fileList = this.enterpriseForm.files
                this.cnsFileList = this.fileList.filter(item => item.category === '承诺书')
                this.sqwtsFileList = this.fileList.filter(item => item.category === '授权委托书')
                this.fddbrsfzmFileList = this.fileList.filter(item => item.category === '法定代表人身份证明')
                this.zjyqwszmFileList = this.fileList.filter(item => item.category === '最近一期完税证明')
                this.swpjzmFileList = this.fileList.filter(item => item.category === '税务评级证明')
                this.zgzxxxgkwFileList = this.fileList.filter(item => item.category === '中国执行信息公开网查询情况')
                this.xyzgbgFileList = this.fileList.filter(item => item.category === '信用中国报告')
                this.zzzsFileList = this.fileList.filter(item => item.category === '资质证书')
                this.qtzsFileList = this.fileList.filter(item => item.category === '其他证书')
                this.isQtzs = this.qtzsFileList.length > 0
                this.isZzzs = this.zzzsFileList.length > 0
            })
        },
        getCityOrDistrict (code, layer) {
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1 || layer === 2) {
                    layer === 1 ? this.addressOptions.city = res : this.addressOptions.district = res
                }else if (layer === 3 || layer === 4) {
                    layer === 3 ? this.addressOptionsGd.city = res : this.addressOptionsGd.district = res
                }
            })
        },
        handleLog () {
        },
        // 提交
        onSubmit () {
            this.$refs['enterpriseFormV'].validate(valid => {
                if(valid) {
                    this.enterpriseForm.epLists = this.tableData
                    this.enterpriseForm.isFileModify = 1
                    console.log(this.enterpriseForm)
                    updateEnterprise(this.enterpriseForm).then(res => {
                        if(res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            })
                            this.$router.push('/user/verification/enterpriseDetail')
                        }else {
                            this.$message({
                                message: '修改失败',
                                type: 'error'
                            })
                        }
                    })
                }
            })
        },
        // 获取验证码
        getVerificationCode () {},
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        handleUploadChange (file) {
            if (file.status == 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status == 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        async uploadLicenseEnterprise (params) {
            let form = this.createUploadFile(params.file)
            uploadFile(form).then(res => {
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.businessLicense = url
                })
                this.enterpriseForm.businessLicenseId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.addEnterpriseFiles(res[0].objectName, '营业执照', res[0].nonIpObjectPath, res[0].recordId)
            })
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            if (layer === 1) {
                this.enterpriseForm.city = ''
                this.enterpriseForm.county = ''
                this.enterpriseForm.cityCode = ''
                this.enterpriseForm.countyCode = ''
                this.enterpriseForm.detailedAddress = ''
            }else if (layer === 2) {
                this.enterpriseForm.county = ''
                this.enterpriseForm.countyCode = ''
                this.enterpriseForm.detailedAddress = ''
            }else if (layer === 3) {
                this.enterpriseForm.cityGd = ''
                this.enterpriseForm.countyGd = ''
                this.enterpriseForm.cityGdCode = ''
                this.enterpriseForm.countyGdCode = ''
                this.enterpriseForm.workXxdz = ''
            }else if (layer === 4) {
                this.enterpriseForm.countyGd = ''
                this.enterpriseForm.countyGdCode = ''
                this.enterpriseForm.workXxdz = ''
            }
            getCascaderOptions({ distCode: code }).then(res => {
                if (layer === 1 || layer === 2) {
                    layer === 1 ? this.addressOptions.city = res : this.addressOptions.district = res
                }else if (layer === 3 || layer === 4) {
                    layer === 3 ? this.addressOptionsGd.city = res : this.addressOptionsGd.district = res
                }
            })
        },
        //时间验证
        validateDate (rule, value, callback) {
            if (value == null || value == '') {
                return callback(new Error('请选择时间！'))
            }
            callback()
        },
        // 正整数
        checkInt (rule, value, callback) {
            if (Number(value) && value % 1 === 0 && value >= 0) {
                callback()
            } else {
                value = null
                return callback(new Error('请输入阿拉伯数字正整数！'))
            }
        },
        // 校验地址信息
        validateAddress (rule, value, callback) {
            if ( this.enterpriseForm.provincesCode == null || this.enterpriseForm.provincesCode == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.enterpriseForm.cityCode == null || this.enterpriseForm.cityCode == '' ) {
                return callback(new Error('请选择市级！'))
            }
            if ( this.enterpriseForm.countyCode == null || this.enterpriseForm.countyCode == '' ) {
                return callback(new Error('请选择县、区！'))
            }
            callback()
        },
        // 校验地址信息
        validateAddress_gd (rule, value, callback) {
            if ( this.enterpriseForm.provincesGdCode == null || this.enterpriseForm.provincesGdCode == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.enterpriseForm.cityGdCode == null || this.enterpriseForm.cityGdCode == '' ) {
                return callback(new Error('请选择市级！'))
            }
            if ( this.enterpriseForm.countyGdCode == null || this.enterpriseForm.countyGdCode == '' ) {
                return callback(new Error('请选择县、区！'))
            }
            callback()
        },
    },
}
</script>
<style scoped lang="scss">
main>div {
    height: 100%;
    border: 1px solid rgba(230, 230, 230, 1);
}
.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
.content { padding-bottom: 30px; }
/deep/ .el-form-item {
    margin-bottom: 25px;

    .el-form-item__label {
        height: 100%;
        padding-right: 10px;
        line-height: 50px;
        font-size: 16px;
        color: #333;
    }

    .el-input__inner {
        height: 50px;
        font-size: 16px;
        border-radius: 0;
        border: 1px solid rgba(204, 204, 204, 1);
    }
}

/deep/ .el-checkbox {
    display: flex;
    align-items: center;

    .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(33, 110, 198, 1);
    }

    .el-checkbox__label {
        font-size: 16px;
        color: #333;

        span {
            color: #216EC6;
        }
    }
}
/deep/.avatar-uploader .el-upload {
    width: 138px;
    height: 138px;
    border: 1px dashed rgba(217, 217, 217, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
/deep/ .enterpriseForm {
    width: 1066px;
    margin-top: 30px;
    .el-col:not(.el-col-24) {
        width: 48.4%;
        .el-input, .el-input__inner {width: 350px;}
        &.el-col-offset-1 {margin-left: 20px;}
    }
    .el-col-24 .el-input__inner, .el-input {width: 893px;}
    .el-input.is-disabled {background-color: #e6e6e6;color: #999;}
    .el-form-item__error {
        width: 80%;
        margin-top: -10px;
    }
    .licenseUploader {
        font-size: 40px;
        color: #8c939d;
        width: 138px;
        height: 138px;
        //margin-right: 20px;
        line-height: 140px;
        display: inline;
        .el-form-item__error {
            width: 500px;
        }
    }

    .avatar {
        width: 140px;
        height: 140px;
        display: block;
    }

    .uploadDemo {
        width: 138px;
        height: 138px;
        padding: 5px;
        margin-left: 40px;
        font-size: 16px;
        color: #999;
        border: 1px dashed rgba(217, 217, 217, 1);
        flex-direction: column;

        img {
            width: 130px;
            height: 95px;
        }

        span {
            margin-right: 5px;
        }
    }

    .uploadTip {
        font-size: 14px;
        margin-top: 155px;
        margin-bottom: 20px;
        margin-left: -108px;
        width: 380px;
        color: #808080;
    }

    .el-select {
        width: 100%;
    }
    .licenseValidTime {
        .el-form-item__content {display: flex;}
        .el-date-editor {
            width: 300px;
            margin-right: 20px;
        }
        .el-checkbox {height: 50px; margin-bottom: 0;}
        .el-checkbox__inner {border: 1px solid rgba(217,217,217,1);}
    }
    .registerAddress {
        .el-select {
            width: 100px;
            margin-right: 10px;
            &:last-child{margin-right: 0;}
            .el-input, .el-input__inner {width: 100px;}
        }
        .el-form-item__error {
            margin-left: 0px;
            //margin-top: -50px;
        }
    }
    .separ {
        width: 1066px;
        height: 1px;
        margin-left: 20px;
        margin-bottom: 30px;
        border-top: 1px dashed rgba(204, 204, 204, 1);
    }

    .subtitle {
        margin-left: 60px;
        margin-bottom: 30px;
        font-size: 20px;
        font-weight: 500;
        color: #216EC6;
    }

    .identityUpload {
        width: 160px;
        height: 100px;
    }
    .verifyBox .el-button {
        width: 140px;
        height: 50px;
        padding: 0;
        font-size: 16px;
        line-height: 50px;
        color: #216EC6;
        background: #FFFFFF;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 0;
    }
    .verifyBox {
        .el-input, .el-input__inner{
            width: 195px !important;
            margin-right: 15px;
        }
    }

    .el-checkbox {
        margin-top: -3px;
        margin-bottom: 50px;
        .is-checked .el-checkbox__inner {background-color: #216ec6;}
    }
}
.btns {
    width: 350px;
    margin-top: -25px;
    button {
        width: 160px;
        height: 50px;
        font-size: 22px;
    }
    button:first-child {
        color: rgba(33,110,198,1);;
        border: 1px solid rgba(33,110,198,1);
        background-color: #fff;
    }
    button:last-child {
        color: #fff;
        background-color: rgba(33,110,198,1);
    }
}
//上传证书和资料
.addcardtop{
  width: 150px;
  height: 50px;
  background-color: rgb(0,155,237);
  font-size: 16px;
  margin-bottom: 30px;
  margin-left: 2%;
}
//表格
.custom-table {
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  margin-left: 2%;
  width: 100%;
}
//企业账户信息，填写内容将写入合同并涉及发票，请谨慎填写。
.setAccount{
  height: 30px;
  display: flex;
  line-height: 30px;
  margin-bottom: 30px;
}
.subtitletip{
  //border-style: dashed;
  color: #e53e30;
  font-size: 14px;
  margin-left: 50px;
}
//上传文件的按钮样式
.uploadbtn{
  display: inline-flex;
  align-items: center;
  background-color: rgb(255,118,0);
  border: 1px solid rgb(255,118,0);
  text-align: center;
  width: 120px;
  height: 35px;
  line-height: 35px;
}
.addcard{
  padding: 10px;
  margin-top: 20px;
  margin-right: 30px;
  border: 1px solid #cccccc;
  width: 500px;
  height: 100%;
  margin-left: 2%;
}
.cardtop{
  display: flex;
  justify-content: space-between;
}
.addcarddel{
  color: rgb(255,118,0);
  border-color: rgb(255,118,0);
  background-color: white;
}
</style>