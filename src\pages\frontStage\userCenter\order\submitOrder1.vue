<template>
    <div class="root">
        <div class="order-box center mb20">
            <div class="list-title df mt10">购买清单</div>
            <div class="order-list p20">
                <div class="name-bar center dfa">
                    <div>商品清单</div><div>单价</div><div>数量</div><div>小计（元）</div>
                </div>
                <div class="shop-name dfa">
                    <span>中联机械有限公司</span>
                </div>
                <div class="list-item df" v-for="item in orderList" :key="item">
                    <div class="info df">
                        <img :src="item.img ? imgUrlPrefixAdd + item.img : require('@/assets/images/img/queshen5.png')" alt="">
                        <div class="detail">
                            <h4>{{item.name}}</h4>
                            <div>品牌：{{item.brand}}</div>
                            <div>规格：{{item.specs}}</div>
                        </div>
                    </div>
                    <div class="price">
                        <div>￥{{item.price}}</div>
                        <div>单位：{{item.unit}}</div>
                    </div>
                    <div class="num dfa">
                        <div @click="changeNum('minus', item)">-</div><input v-model="item.quantity" type="text"><div @click="changeNum('plus', item)">+</div>
                    </div>
                    <div class="total">￥{{item.total}}</div>
                </div>
                <div class="select-box mt30">
                    <div>
                        <span>结算方式</span>
                        <el-select v-model="payMethod" value-key="" placeholder="请选择支付方式">
                            <el-option v-for="item in payMethods" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <span>其他要求</span><input v-model="remarks" type="text" placeholder="填写要求"/>
                    </div>
                </div>
                <div class="sum">商品合计：{{totalPrice}}</div>
            </div>
        </div>
        <div class="addr-info center mb20">
            <div class="list-title dfb">
                <span>收货信息</span>
                <span @click="addrDialogVisible = true">切换收货地址</span>
            </div>
            <div class="addr-content">
                <p class="mb20">收货地址：{{reciever.addr}}（{{reciever.name}}收） </p>
                <p>收货人：{{reciever.name}}（{{reciever.tel}}）</p>
            </div>
        </div>
        <div class="submit df">
            <div class="submitBox df">
                <div>总计：<span>￥{{totalPrice}}</span></div>
<!--                <button @click="submitOrder">提交订单</button>-->
                <button>提交订单</button>
            </div>
        </div>
        <el-dialog class="front" title="" :visible.sync="addrDialogVisible">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>{{ dialogTitle }}</div>
                    </div>
                    <div class="dialog-close" @click="addrDialogVisible = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <!-- <div class="addr-list mt20">
                <div class="list-top"></div>
                <div class="list-item"></div>
            </div> -->
            <el-table :data="addrList">
                <el-table-column type="selection" label-width="74"></el-table-column>
                <el-table-column label="收货地址" label-width="560" prop="addr"></el-table-column>
                <el-table-column label="联系人" label-width="152" prop="name"></el-table-column>
                <el-table-column label="联系电话" label-width="110" prop="tel"></el-table-column>
                <el-table-column label="操作" label-width="">
                    <template v-slot="scope">
                        <span @click="handleEditAddr(scope.row)" class="edit-btn">编辑</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="add pointer">+ 新增</div>
            <span slot="footer">
                <button @click="addrDialogVisible = false">取消</button>
                <button>确定</button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import BigNumber from 'bignumber.js'
export default {
    data () {
        return {
            orderList: [
                {
                    img: '',
                    name: '挖掘机',
                    brand: '中联',
                    specs: '不详',
                    price: '130000.00',
                    unit: '台',
                    quantity: 1,
                    total: '130000.00',
                },
                {
                    img: '',
                    name: '挖掘机',
                    brand: '中联',
                    specs: '不详',
                    price: '130000.00',
                    unit: '台',
                    quantity: 1,
                    total: '130000.00',
                },
            ],
            payMethods: [
                { label: '路桥结算', value: 0 }
            ],
            payMethod: 0,
            remarks: '',
            totalPrice: '',
            reciever: {
                name: '蛮蛮',
                tel: '18548759621',
                addr: '四川省成都市青羊区沧浪胡同918号',
            },
            addrDialogVisible: false,
            addrList: [
                // { checked: true, addr: '江西省南昌市进贤县黄山街345号', name: '蛮蛮', tel: '18548759621', },
            ]
        }
    },
    computed: {},
    watch: {
        orderList: {
            handler (newVal) {
                newVal.forEach(item => {
                    if(isNaN(Number(item.quantity)) || item.quantity < 1) item.quantity = 1
                    item.total = (Number(item.price) * item.quantity).toFixed(2)
                })
                this.calcTotalPrice()
            },
            deep: true
        }
    },
    methods: {
        // 计算订单总价
        calcTotalPrice () {
            let sum = this.orderList.reduce((total, item) => new BigNumber(total).plus(item.total), new BigNumber(0))
            this.totalPrice = sum.toFixed(2)
        },
        handleEditAddr (row) {
            console.log(row)
        },
        changeNum (action, item) {
            if(action === 'minus') {
                if(item.quantity === 1) return
                item.quantity--
            } else {
                item.quantity++
            }
        },
        // 提交订单
        submitOrder () {
            this.$router.push('/mFront/order')
        },
    },
    created () {
        this.calcTotalPrice()
    },
}
</script>
<style scoped lang="scss">
.root {
    height: 100%;
    padding-top: 20px;
    background-color: #f5f5f5;

    .order-box {
        width: 1326px;
        background-color: #fff;
    }
}
.list-title {
    height: 50px;
    padding: 15px 20px;
    font-size: 18px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);
    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
.order-list {
    .name-bar {
        width: 1286px;
        height: 52px;
        padding-left: 40px;
        margin-bottom: 20px;
        font-size: 14px;
        border: 1px solid rgba(230, 230, 230, 1);
        color: rgba(51, 51, 51, 1);
        background-color: #fafafa;
        & div:first-child {width: 773px;}
        & div:nth-child(2) {width: 174px;}
        & div:nth-child(3) {width: 148px;}
    }
    .shop-name {
        margin-bottom: 13px;
        span {margin-right: 10px;font-size: 14px;}
        img {width: 22px;height: 22px;}
    }
    .list-item {
        width: 100%;
        height: 144px;
        margin-bottom: 10px;
        padding: 22px 24px;
        border: 1px solid rgba(230, 230, 230, 1);
        .info {
            width: 785px;
            img {
                width: 100px;
                height: 100px;
                margin-right: 12px;
            }
            h4, div{
                font-size: 14px;
                font-weight: 400;
            }
            h4 {margin-bottom: 11px; color: rgba(0, 0, 0, 1);}
            div {margin-bottom: 4px; color: rgba(102, 102, 102, 1);}
        }
        .price {
            width: 138px;
            & div:first-child {margin-bottom: 4px;}
            & div:last-child {}
        }
        .num {
            width: 101px;
            height: 26px;
            margin-right: 84px;
            text-align: center;
            line-height: 26px;
            border: 1px solid rgba(204, 204, 204, 1);
            div {
                width: 100%;
                height: 26px;
                cursor: pointer;
                user-select: none;
                &:first-child {border-right: 1px solid rgba(204, 204, 204, 1);}
                &:last-child {border-left: 1px solid rgba(204, 204, 204, 1);}
            }
            input {
                width: 49px;
                text-align: center;
            }
        }
        .total {
            font-size: 14px;
            font-weight: 700;
            color: rgba(51, 51, 51, 1);
        }
    }
    .select-box {
        &>div {margin-bottom: 30px;}
        span {margin-right: 20px;}
        /deep/ .el-select {
            width: 300px;
            height: 30px;
            .el-input__inner {
                height: 30px;
                padding: 0 10px;
                color: rgba(51, 51, 51, 1);
            }
        }
        input {
            width: 600px;
            height: 30px;
            padding: 0 10px;
            border: 1px solid rgba(217, 217, 217, 1);
            &::-webkit-input-placeholder {
                color: rgba(51, 51, 51, 1);
            }
        }
    }
    .sum {text-align: right;}
}
.addr-info {
    width: 1326px;
    background-color: #fff;
    .list-title {
        padding-left: 33px;
        position: relative;
        & span:last-child {
            color: rgba(33, 110, 198, 1);
            cursor: pointer;
        }
        &::before {
            position: absolute;
            left: 20px;
        }
    }
    .addr-content {
        padding: 30px 20px;
        p {
            font-size: 14px;
            color: rgba(51, 51, 51, 1);
        }
    }
}
.submit {
    height: 60px;
    background-color: #fff;
    // justify-content: flex-end;
    position: relative;
    .submitBox {
        position: absolute;
        right: 0;
        div {
            color: rgba(51, 51, 51, 1);
            line-height: 60px;
            span {
                font-size: 18px;
                color: rgba(212, 48, 48, 1);
            }
        }
        button {
            width: 146px;
            margin-left: 30px;
            font-size: 24px;
            font-weight: 700;
            color: #fff;
            background-color: rgba(212, 48, 48, 1);
        }
    }
}
/deep/ .el-dialog {
    width: 1120px;
    min-height: 326px;
    .el-table {
        margin-top: 20px;
        border: 1px solid rgba(230, 230, 230, 1);
        // border-bottom: 0;
        font-size: 14px;
        .edit-btn {
            color: rgba(34, 111, 199, 1);
            cursor: pointer;
        }
    }
    .add {
        width: 80px;
        height: 30px;
        margin: 18px 0 40px 0;
        line-height: 30px;
        text-align: center;
        color: rgba(33, 110, 198, 1);
        border: 1px solid rgba(33, 110, 198, 1);
    }
    .el-table__header {
        .cell{
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
        }
        .el-checkbox {display: none;}
    }
    .el-dialog__footer {
        border: 0;
        text-align: center;
        button {
            width: 80px;
            height: 40px;
        }
        & button:first-child {
            margin-right: 20px;
            color: rgba(128, 128, 128, 1);
            background-color: rgba(230, 230, 230, 1);
        }
        & button:last-child {
            color: #fff;
            background-color: rgba(33, 110, 198, 1);
        }
    }
}
.addr-list {
    &>div {
        height: 44px;
        border: 1px solid rgba(230, 230, 230, 1);
    }
    .list-top {
        background-color: rgba(250, 250, 250, 1);
    }
}
</style>