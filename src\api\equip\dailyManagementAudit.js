import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service
//=============================================日常管理审核===============================================================
//审核
const audit = params =>{
    return httpPost({
        url: '/facilitymanagement/flowEngin/audit',
        params
    })
}
//提交
const commit = params =>{
    return httpPost({
        url: '/facilitymanagement/flowEngin/commit',
        params
    })
}
//获取审核历史
const history = params =>{
    return httpPost({
        url: '/facilitymanagement/flowEngin/get/audit/history',
        params
    })
}
//获取流程按钮显示状态
const getCurrentUnitInfo = params =>{
    return httpPost({
        url: '/facilitymanagement/flowEngin/getCurrentUnitInfo',
        params
    })
}
//撤回
const undoworkt = params =>{
    return httpPost({
        url: '/facilitymanagement/flowEngin/undowork',
        params
    })
}
//作废
const nullify = params =>{
    return httpPost({
        url: '/facilitymanagement/common/nullify',
        params
    })
}
const request  = {
    //审核
    audit  (params) {
        return httpPost({
            url: '/facilitymanagement/flowEngin/audit',
            params
        })
    },
    //提交
    commit  (params) {
        return httpPost({
            url: '/facilitymanagement/flowEngin/commit',
            params
        })
    },
    //获取审核历史
    history  (params) {
        return httpPost({
            url: '/facilitymanagement/flowEngin/get/audit/history',
            params
        })
    },
    //获取流程按钮显示状态
    getCurrentUnitInfo  (params) {
        return httpPost({
            url: '/facilitymanagement/flowEngin/getCurrentUnitInfo',
            params
        })
    },
    //撤回
    undoworkt  (params) {
        return httpPost({
            url: '/facilitymanagement/flowEngin/undowork',
            params
        })
    },
    //作废
    nullify  (params) {
        return httpPost({
            url: '/facilitymanagement/common/nullify',
            params
        })
    }
}
export default request
export {
    audit,
    commit,
    history,
    undoworkt,
    nullify,
    getCurrentUnitInfo,
}
