$font_r: 'SourceHanSansCN-Regular';
$font_m: 'SourceHanSansCN-Medium';
nav,
main,
footer,
.content-box {
    min-width: 1354px;
}
nav {
    .content-box {
        & > div:last-child {
            padding: 32px 0;
        }
        .btns-user {
            height: 45px;
            font-family: $font_r;
            font-size: 14px;
            background-color: #f7f7f7;
            img,
            span {cursor: pointer;}
            .user-left {
                height: 14px;
                display: flex;
                align-items: center;
                & > div:first-child {
                    height: 16px;
                    display: flex;
                    align-items: center;
                    img {
                        margin-right: 9px;
                    }
                    span {
                        height: 16px;
                        margin-right: 40px;
                        color: #333333;
                        letter-spacing: 0;
                        font-weight: 400;
                    }
                }
                .login-btns {
                    width: 322px;
                    height: 14px;
                    line-height: 16px;
                    font-size: 14px;
                    color: #216ec6;
                    letter-spacing: 0;
                    font-weight: 400;
                }
            }
            .user-right {
                height: 14px;
                color: #216ec6;
                img {
                    margin-right: 6px;
                }
            }
        }
        .logo img {
            width: 366px;
        }
        .search-box {
            // width: 580px;
            width: unset;
            height: 40px;
            display: flex;
            .input-box {
                padding: 0 18px;
                border: 1px solid #dedede;
                flex-grow: 1;
                display: flex;
                align-items: center;
                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 18px;
                }
                input {
                    height: 36px;
                    font-size: 18px;
                    flex-grow: 1;
                }
            }
            button {
                width: 80px;
                color: #fff;
                background-color: #216ec6;
            }
        }
    }
}
// 页脚
footer {
    height: 316px;
    background-color: #0e1926;
    .content-box {
        height: inherit;
        color: #fff;
        display: flex;
        justify-content: space-between;
        .contact-info {
            width: 480px;
            height: 200px;
            margin-top: 54px;
            .title {
                height: 56px;
                padding-left: 46px;
                font-size: 40px;
                font-family: 'PingFangSC-Semibold', serif;
                line-height: 56px;
                position: relative;
                img {
                    width: 40px;
                    height: 40px;
                    position: absolute;
                    top: 6px;
                    left: 0;
                }
            }
            .tel {
                height: 82px;
                font-size: 70px;
                line-height: 82px;
            }
            .active-time,
            .record {
                height: 30px;
                font-size: 22px;
                line-height: 30px;
                font-weight: lighter;
                font-family: 'PingFang-SC-Regular', serif;
            }
        }
        .navigation {
            width: 660px;
            font-family: 'PingFangSC-Semibold', serif;
            display: flex;
            justify-content: space-between;
            align-items: center;
            a {
                font-size: 28px;
                font-family: 'PingFangSC-Semibold', serif;
            }
            a:hover {
                color: #216ec6;
            }
        }
    }
}
.content-box {
    width: 1354px;
    margin: 0 auto;
}

/deep/ .el-dialog {
    width: 800px;
    //margin-top: 261px !important;
    .el-dialog__body {
        padding: 0;
    }
    .el-dialog__header,
    .el-dialog__close.el-icon.el-icon-close {
        display: none;
    }
}

.dialog-header {
    height: 41px;
    font-family: $font_m;
    font-size: 20px;
    font-weight: 500;
    color: #333333;
    .dialog-header-top {
        height: 20px;
        margin-bottom: 20px;
        font-weight: 500;
        .dialog-title > div:first-child {
            height: 20px;
            width: 3px;
            margin-right: 9px;
            background: #216ec6;
        }
        .dialog-close {
            cursor: pointer;
        }
    }
    & > div:last-child {
        width: inherit;
        height: 1px;
        background: #d8d8d8;
    }
}

.dialog-login {
    .login-box {
        width: 400px;
        margin-top: 78px;
        /deep/ .el-tabs.el-tabs--top {
            width: 400px !important;
            margin: 0 auto;
            font-size: 20px !important;
            .el-tabs__header {
                width: 270px !important;
                margin: 0 65px 30px 65px;
                border: none !important;
            }
            .el-tabs__item.is-top {
                padding-top: 0;
                font-size: 20px;
                font-family: $font_r;
                color: #333333;
            }
            .el-tabs__nav.is-top {
                padding-bottom: 8px !important;
                border: none !important;
            }
            .el-tabs__active-bar.is-top {
                height: 3px;
                margin: 0 auto !important;
                background: #226fc7;
            }
            #tab-first.el-tabs__item.is-top:active .el-tabs__active-bar.is-top {
                transform: translateX(8px) !important;
            }
            #tab-second.el-tabs__item.is-top:active .el-tabs__active-bar {
                transform: translateX(166px) !important;
            }
        }
        .input-box {
            width: 400px;
            height: 55px;
            margin-bottom: 30px;
            padding: 0 12px;
            border: 1px solid #d9d9d9;
            display: flex;
            align-items: center;
            img {
                width: 15px;
                height: 20px;
                margin-right: 10px;
                &:last-child {
                    width: unset;
                    height: unset;
                    margin: 0 0 0 10px;
                }
            }
            input {
                height: 53px;
                font-size: 20px;
                flex-grow: 1;
            }
            ::-webkit-input-placeholder {
                font-size: 20px;
                color: #d9d9d9;
            }
        }
        .verify-box {
            height: 55px;
            margin-bottom: 30px;
            input {
                width: 219px !important;
            }
            .input-box {
                width: 270px;
                margin-bottom: unset;
            }
            button {
                width: 120px;
                font-size: 16px;
                border: 1px solid #216ec6;
                background: #ffffff;
                color: #216ec6;
                border-radius: 5px;
                &:active {
                    color: #fff;
                    background-color: #216ec6;
                }
            }
        }
        button {
            width: 400px;
            height: 55px;
            font-size: 20px;
            font-family: $font_r;
            color: #fff;
            background-color: #216ec6;
        }
    }
}

@media screen and (max-width: 1354px) {
    .content-box {
        width: 100%;
    }
}