<template>
    <div class="root">
        <div class="box center p20">
            <div class="title center">
                <h2>{{ content.title }}</h2>
                <div class="stat">
                    <span>时间：{{ this.content.gmtRelease|formatDate }}</span>&nbsp;&nbsp;&nbsp;<span></span>
                </div>
            </div>
            <div class="content center">
                <p v-html="content.content"></p>
                <div class="file-list mb20" v-if="fileList.length > 0">
                    <span>附件：</span>
                    <div class="file dfa pointer" v-for="file in fileList" :key="file.url">
                        <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                    </div>
                </div>
                <div class="btn">
                    <button @click="handleClose">关闭</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>

import { getWebInfoDetail } from '@/api/frontStage/webInfo'
import { previewFile } from '@/api/platform/common/file'
export default {
    data () {
        return {
            beforePath: '',
            content: {},
            fileList: [],
        }
    },
    filters: {
        formatDate (time) {
            if (!time) return
            let t = new Date(time)//row 表示一行数据, updateTime 表示要格式化的字段名称
            return t.getFullYear() + '-' + (t.getMonth() + 1) + '-' + t.getDate()
        }
    },
    async created () {
        let res = await getWebInfoDetail({ id: this.$route.query.id })
        this.content = res
        this.fileList = res.files
    },
    methods: {
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        //返回
        handleClose () {
            // /mFront/newsList
            // window.close()
            this.$router.push({ path: '/mFront/mallIndex', query: { type: 'noticeType' } })
        },
    },
}
</script>
<style scoped lang="scss">
.root {
  padding: 20px 0;
  background-color: #f5f5f5;

  .box {
    width: 1326px;
    height: 100%;
    background-color: #fff;

    & > div {
      width: 1210px;
    }
  }
}

.title {
  text-align: center;
  padding-bottom: 14px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);

  h2 {
    margin-bottom: 15px;
    font-size: 30px;
    font-weight: 500;
  }

  .stat {
    font-size: 20px;
    color: rgba(153, 153, 153, 1);
  }
}

.content {
  p {
    margin: 40px 0;
    font-size: 24px;
  }
  .file {
    margin-top: 10px;
    span:hover {
      color: rgb(33, 110, 198);
    }
  }

  .btn {
    text-align: center;
  }

  button {
    width: 140px;
    height: 50px;
    font-size: 18px;
    color: rgba(102, 102, 102, 1);
    border: 1px solid rgba(230, 230, 230, 1);
    background-color: #fff;
  }
}
</style>