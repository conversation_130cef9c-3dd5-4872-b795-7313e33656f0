
<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="订单信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="发货单信息" name="orderShip" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="发货单项信息" name="productInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 订单 信息-->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">订单信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单号：">
                                            <span>{{ shipformData.orderSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col v-if="formData.orderSourceType==3" :span="12" >
                                        <el-form-item label="订单来源：">
                                            <el-tag v-show="formData.orderSourceType==1 ">零星采购</el-tag>
                                            <el-tag v-show="formData.orderSourceType==2 ">竞价</el-tag>
                                            <el-tag v-show="formData.orderSourceType=3 ">大宗材料</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col v-else :span="12" >
                                        <el-form-item label="店铺名称：">
                                            <span>{{ formData.shopName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="收件人手机号：">
                                            <span>{{ formData.receiverMobile }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收件地址：">
                                            <span>{{ formData.receiverAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额：">
                                            <span>{{ formData.actualAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="订单状态：">-->
                                    <!--                                            <el-tag v-if="formData.state == 0">草稿</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 1">已提交</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 2">待确认</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 3">已确认</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 4">待签订合</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 5">已签合同</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 6">待发货</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 7">已关闭</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 8">发货中</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 9">待收货</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 10">已完成</el-tag>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                    <!--                                </el-row>-->
                                    <!--                                <el-row>-->
                                    <el-col :span="12">
                                        <el-form-item label="不含税总金额：">
                                            <span>{{ formData.noRateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单备注：">
                                            <span>{{ formData.orderRemark }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div id="orderShipCon" class="con">
                        <div class="tabs-title" id="orderShip">发货单信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="shipformData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发货单号：">
                                            <span>{{ shipformData.billSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收料公司：">
                                            <span>{{ shipformData.receiveOrgName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row >
                                    <el-col :span="12">
                                        <el-form-item label="发货单来源：">
                                            <span  v-show="shipformData.sourceType==2">零星采购</span>
                                            <span  v-show="shipformData.sourceType==1">大宗月供</span>
                                            <span  v-show="shipformData.sourceType==6">大宗临购</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item  v-if="shipformData.sourceType==2"  label="合同编号：">
                                            <span>{{ shipformData.sourceNo }}</span>
                                        </el-form-item>
                                        <el-form-item  v-else  label="计划编号：">
                                            <span>{{ shipformData.sourceNo }}</span>
                                        </el-form-item>

                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发货单状态：">
                                            <el-tag v-if="shipformData.type == '0'">未发货</el-tag>
                                            <el-tag v-if="shipformData.type == '1'" type="success">发货中</el-tag>
                                            <el-tag v-if="shipformData.type == '2'" type="success">已收货</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="发货时间：">
                                            <span>{{ shipformData.shipData }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="收料人：">
                                            <span>{{ shipformData.receiveName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收货时间：">
                                            <span>{{ shipformData.confirmTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="物流公司：">
                                            <span>{{ shipformData.logisticsCompany }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="物流单号：">
                                            <span>{{ shipformData.deliveryFlowId }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!--                                <el-row>-->
                                <!--                                    <el-col :span="12">-->
                                <!--                                        <el-form-item label="收件人手机号：">-->
                                <!--                                            <span>{{ formData.receiverMobile }}</span>-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                    <el-col :span="12">-->
                                <!--                                        <el-form-item label="收件地址：">-->
                                <!--                                            <span>{{ formData.receiverAddress }}</span>-->
                                <!--                                        </el-form-item>-->
                                <!--                                    </el-col>-->
                                <!--                                </el-row>-->
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额：">
                                            <span>{{ shipformData.rateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="订单状态：">-->
                                    <!--                                            <el-tag v-if="formData.state == 0">草稿</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 1">已提交</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 2">待确认</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 3">已确认</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 4">待签订合</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 5">已签合同</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 6">待发货</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 7">已关闭</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 8">发货中</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 9">待收货</el-tag>-->
                                    <!--                                            <el-tag v-if="formData.state == 10">已完成</el-tag>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                  <el-col :span="12">
                                    <el-form-item label="PCWP收料状态：">
                                      <el-tag v-if="shipformData.receiveStatus == '0'">待收货</el-tag>
                                      <el-tag v-if="shipformData.receiveStatus == '1'" type="success" >已收货</el-tag>
                                      <el-tag v-else type="info">未知</el-tag>
                                    </el-form-item>
                                  </el-col>
                                </el-row>
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="收料审核人：">
                                    <span>{{ shipformData.receiveAuditName }}</span>
                                  </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="审核状态：">
                                    <el-tag v-if="shipformData.receiveAuditIs == '0'" type="success">待收货</el-tag>
                                    <el-tag v-if="shipformData.receiveAuditIs == '1'" type="error" >已收货</el-tag>
                                    <el-tag v-else type="info">未知</el-tag>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="收料编号：">
                                    <span>{{ shipformData.receiveNo }}</span>
                                  </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="签收人：">
                                    <span>{{ shipformData.recipient }}</span>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="运输联系人：">
                                    <span>{{ shipformData.contactUser }}</span>
                                  </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="联系电话：">
                                    <span>{{ shipformData.contactPhone }}</span>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 发货单项-->
                    <div id="productInfo" class="con" v-loading="tableLoading">
                        <div class="tabs-title" id="contractList">发货单项</div>
                        <div class="e-table" style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-input type="text" @blur="getTableData" placeholder="输入搜索关键字"
                                              v-model="keywords">
                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData"/>
                                    </el-input>
<!--                                    <el-button  style="margin-left: 12px" type="primary" @click="changShipVlaue"-->
<!--                                               class="btn-greenYellow">批量修改发货数量-->
<!--                                    </el-button>-->
                                    <div class="left-btn" style="margin-left: 20px">
                                        <!--                                        <el-button type="primary" class="btn-greenYellow" @click = "changePrice">批量改价</el-button>-->
                                    </div>
                                </div>
                            </div>
                            <el-table ref="tableRef"
                                      border
                                      style="width: 100%"
                                      :data="tableData"
                                      tableLoading
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <!--<el-table-column prop="orderItemId" label="订单项" width="200px"></el-table-column>-->
                                <!--<el-table-column prop="dtlId" label="发货单项" width="200px"></el-table-column>-->
                                <el-table-column prop="productSn" label="商品编码" width="160"></el-table-column>
                                <el-table-column prop="productName" label="商品名称" width=""></el-table-column>
                                <el-table-column prop="relevanceName" label="物资名称" width="200px"></el-table-column>
                                <el-table-column prop="skuName" label="规格" width=""></el-table-column>
                                <el-table-column prop="texture" label="材质" width=""></el-table-column>
                                <el-table-column prop="unit" label="单位" width=""></el-table-column>
                                <el-table-column prop="shipCounts" label="发货数量" width="180px">
<!--                                    <template slot-scope="scope">-->
<!--                                            <el-input-number-->
<!--                                                v-if="shipformData.type===1||shipformData.type===0"-->
<!--                                                precision="4"-->
<!--                                                size="mini"-->
<!--                                                v-model="scope.row.shipCounts"-->
<!--                                                :min="0"-->
<!--                                                :step="0.01"-->
<!--                                                :max="scope.row.totalSum"-->
<!--                                                @change="getChangShipCounts(scope.row)"-->
<!--                                            >-->
<!--                                            </el-input-number>-->
<!--                                        <el-input v-if="shipformData.type===2" type="number" v-model="scope.row.shipCounts"-->
<!--                                                  :disabled="true"></el-input>-->
<!--                                    </template>-->
                                </el-table-column>
                                <el-table-column prop="productPrice" label="含税单价" width=""></el-table-column>
                                <!--<el-table-column prop="unShipCounts" label="剩余数量" width="">-->
                                <!--</el-table-column>-->
                                <!--<el-table-column prop="totalSum" label="总数量" width=""></el-table-column>-->

                                <el-table-column prop="totalAmount" label="含税金额" width=""></el-table-column>
                                <el-table-column prop="noRateAmount" label="不含税金额" width=""></el-table-column>
                            </el-table>
                        </div>

                        <!--            分页-->
                        <Pagination
                            v-show="tableData != null || tableData.length != 0"
                            :total="paginationInfo.total"
                            :pageSize.sync="paginationInfo.pageSize"
                            :currentPage.sync="paginationInfo.currentPage"
                            @currentChange="getTableData"
                            @sizeChange="getTableData"
                        />
                    </div>

                </div>
            </el-tabs>
            <el-dialog title="扫码二维码" class="qrcodeDialog" :visible.sync="qrcodeVisible" width="420px"
                       :before-close="beforeQrcodeClose">
                <div ref="qrcode" id="qrcode" class="center" style="width: 200px"></div>
                <span slot="footer" class="dialog-footer">
                <el-button @click="() => { beforeQrcodeClose(); qrcodeVisible = false }">取 消</el-button>
                <el-button type="primary"
                           @click="() => { beforeQrcodeClose(); qrcodeVisible = false }">确 定</el-button>
            </span>
            </el-dialog>
        </div>
        <div class="buttons">
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
// import { orderShipList } from '@/api/platform/order/orders'
import { findByOrderSn, orderShipDtlList, updateOrderPrice, updateShipCountsValue } from '@/api/shopManage/order/order'
import QRCode from 'qrcodejs2'

export default {

    data () {
        return {
            shipformData: '',
            qrcodeVisible: false,
            queryVisible: false,
            orderShip: {
                orderId: '',
                deliveryFlowId: '',
                logisticsCompany: '',
                orderSn: '',
                shipAddress: '',
                dtl: []
            },
            formLoading: false,
            tableStateTitle: null, // 状态标题
            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedRow: [],
            changedRowNum: [],
            tableLoading: false,
        }
    },
    components: {
        Pagination
    },
    created () {
        this.shipformData = this.$route.params.row
        this.findByOrderSnM()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        // 获取修改过的表格行数据
        getChangShipCounts (row) {
            if (!this.changedRow[0]) {
                return this.changedRow.push(
                    {
                        dtlId: row.dtlId,
                        shipCounts: row.shipCounts,
                        billId: row.billId

                    })
            }
            let index = null
            this.changedRow.forEach((item, i) => {
                if (item.dtlId === row.dtlId) {
                    index = i
                }
            })
            if (index != null) {
                return this.changedRow[index].shipCounts = row.shipCounts
            }
            this.changedRow.push({  dtlId: row.dtlId,
                shipCounts: row.shipCounts,
                billId: row.billId })
            console.log(this.changedRow.length)
        },
        changShipVlaue () {
            if (!this.changedRow[0]) {
                this.$message.info('当前没有发货单项数量被修改')
                return
            }
            let warnMsg = '您确定要修改‘这些’的发货单项数量吗？'
            this.clientPop('info', warnMsg, async () => {
                updateShipCountsValue(this.changedRow).then(res => {
                    if (res.message === '操作成功') {
                        this.$message.success('修改成功')
                        this.getTableData()
                    }
                })
            })
        },
        beforeQrcodeClose () {
            this.$refs.qrcode.innerHTML = ''
            this.qrcodeVisible = false
        },
        createQrcode ({ billId }) {
            this.qrcodeVisible = true
            setTimeout(() => {
                let div = this.$refs.qrcode
                return new QRCode(div, {
                    text: billId, //二维码内容字符串
                    width: 180, //图像宽度
                    height: 180, //图像高度
                    colorDark: '#000000', //二维码前景色
                    colorLight: '#ffffff', //二维码背景色
                    correctLevel: QRCode.CorrectLevel.H, //容错级别
                })
            }, 100)
        },
        // 详情
        handleView (row) {
            //利用$router.push进行跳转
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/order/shipedDtl/searchOrderDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shipedDtlShopManageSearchOrderDetail',
                params: {
                    row: row
                }
            })
        },
        findByOrderSnM () {
            this.formLoading = true
            findByOrderSn({ orderSn: this.$route.params.row.otherOrderSn }).then(res => {
                this.formData = res
                this.getTableData()
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 批量修改排序
        changePrice () {
            if (this.changedRow.length == 0) {
                return this.$message('未修改数据！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                updateOrderPrice(this.changedRow).then(res => {
                    if (res.code == 200) {
                        this.$message.success('修改成功！')
                        this.findByOrderSnM()
                        this.changedRow = []
                    }
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if (this.changedRow.length == 0) {
                this.changedRow.push({ orderItemId: row.orderItemId, productPrice: row.productPrice })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.orderItemId == row.orderItemId) {
                    t.productPrice = row.productPrice
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push({ orderItemId: row.orderItemId, productPrice: row.productPrice })
            }
            flag = true
        },
        // 获取表格数据
        getTableData () {
            let params = {
                billId: this.shipformData.billId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            orderShipDtlList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        addData () {
        },
        deleteData () {
        },
        //取消
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}

/deep/ .qrcodeDialog .el-dialog__body {
    height: 240px;
}
</style>
