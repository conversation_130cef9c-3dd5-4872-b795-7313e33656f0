export default [
    {
        path: '/platform',
        component: () => import('@/pages/platform/index'),
        redirect: '/platform/mail/outBox',
        children: [
            {
                path: '/platform/bidding/submitAudit',
                component: () => import('@/pages/platform/bidding/submitAudit/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-竞价提交审核'
                }
            },
            {
                path: '/platform/bidding/biddingList',
                component: () => import('@/pages/platform/bidding/biddingList/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-竞价列表'
                }
            },
            {
                path: '/platform/bidding/submitAuditDetail',
                name: 'submitAuditDetail',
                component: () => import('@/pages/platform/bidding/submitAudit/detail.vue'),
                meta: {
                    title: '物资采购管理-竞价详情'
                }
            },
            //   内容管理
            {
                path: '/platform/content/richContent',
                component: () => import('@/pages/platform/content/richContent/index'),
                meta: {
                    title: '物资采购管理-其他内容'
                }
            },
            {
                path: '/platform/content/news',
                component: () => import('@/pages/platform/content/news/index'),
                meta: {
                    title: '物资采购管理-新闻'
                }
            },
            {
                path: '/platform/content/notification',
                component: () => import('@/pages/platform/content/notification/index'),
                meta: {
                    title: '物资采购管理-公告'
                }
            },
            {
                path: '/platform/content/registerAgreement',
                component: () => import('@/pages/platform/content/registerAgreement/index'),
                meta: {
                    title: '物资采购管理-用户注册协议'
                }
            },
            {
                path: '/platform/content/adPicture',
                component: () => import('@/pages/platform/content/adPicture/index'),
                meta: {
                    title: '物资采购管理-广告图'
                }
            },
            {
                path: '/platform/content/richContentDetail',
                component: () => import('@/pages/platform/content/richContent/detail'),
                meta: {
                    title: '物资采购管理-内容发布'
                }
            },
            {
                path: '/platform/content/links',
                component: () => import('@/pages/platform/content/links/index'),
                meta: {
                    title: '物资采购管理-友情链接'
                }
            },
            //商品内容管理
            {
                path: '/platform/floor/floorColumn',
                component: () => import('@/pages/platform/floor/floorColumnManager/index'),
                meta: {
                    title: '物资采购管理-楼层栏目管理'
                }
            },
            {
                path: '/platform/floor/floorColumnDetail',
                name: 'floorColumnDetail',
                component: () => import('@/pages/platform/floor/floorColumnManager/detail'),
                meta: {
                    title: '物资采购管理-楼层栏目详情'
                }
            },
            {
                path: '/platform/floor/floorManager',
                component: () => import('@/pages/platform/floor/floorManager/index'),
                meta: {
                    title: '物资采购管理-楼层管理'
                }
            },
            {
                path: '/platform/floor/floorManagerDetail',
                name: 'floorManagerDetail',
                component: () => import('@/pages/platform/floor/floorManager/detail'),
                meta: {
                    title: '物资采购管理-楼层管理详情'
                }
            },
            //商品管理
            {
                path: '/platform/product/productCategory',
                component: () => import('@/pages/platform/product/productCategory/index'),
                meta: {
                    title: '物资采购管理-商品分类管理'
                }
            },
            {
                path: '/platform/product/productZone',
                component: () => import('@/pages/platform/product/productZone/index'),
                meta: {
                    title: '物资采购管理-商品区域管理'
                }
            },

            // {
            //     path: '/platform/product/productBasics',
            //     component: () => import('@/pages/platform/product/productBasics/index'),
            //     meta: {
            //         title: '物资采购管理-商品基础库管理'
            //     }
            // },
            {
                path: '/platform/product/productStorage',
                component: () => import('@/pages/platform/product/productStorage/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-商品基础库管理'
                }
            },
            {
                path: '/platform/product/productDatabase',
                name: 'productDatabase',
                component: () => import('@/pages/platform/product/productDatabase/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-商品信息数据库',
                }
            },
            {
                path: '/platform/product/productDatabaseDetail',
                name: 'productDatabaseDetail',
                component: () => import('@/pages/platform/product/productDatabase/detail'),
                meta: {
                    title: '物资采购管理-商品信息数据库',
                },
            },
            // 废弃
            // {
            //     path: '/platform/product/shopProduct',
            //     name: 'shopProduct',
            //     component: () => import('@/pages/platform/product/shopProduct/index'),
            //     meta: {
            //         keepAlive: true,
            //         title: '物资采购管理-店铺商品管理',
            //     }
            // },
            // {
            //     path: '/platform/product/shopProductDetail',
            //     name: 'shopProductDetail',
            //     component: () => import('@/pages/platform/product/shopProduct/detail'),
            //     meta: {
            //         title: '物资采购管理-店铺商品详情',
            //     },
            // },
            // 临购
            {
                path: '/platform/product/shopLcMaterialManage',
                name: 'shopLcMaterialManage',
                component: () => import('@/pages/platform/lcProduct/shopMaterialManage/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-店铺商品管理',
                }
            },
            {
                path: '/platform/product/shopLcMaterialManageDetail',
                name: 'shopLcMaterialManageDetail',
                component: () => import('@/pages/platform/lcProduct/shopMaterialManage/detail'),
                meta: {
                    title: '物资采购管理-店铺商品详情',
                },
            },
            {
                path: '/platform/product/shopCheckLcMaterial',
                name: 'shopCheckLcMaterial',
                component: () => import('@/pages/platform/lcProduct/shopCheckMaterial/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-店铺审核临购商品管理',
                }
            },
            {
                path: '/platform/product/shopCheckLcMaterialDetail',
                name: 'shopCheckLcMaterialDetail',
                component: () => import('@/pages/platform/lcProduct/shopCheckMaterial/detail'),
                meta: {
                    title: '物资采购管理-店铺审核临购商品详情',
                }
            },
            // 临购结束
            {
                path: '/platform/product/shopMaterialManage',
                name: 'shopMaterialManage',
                component: () => import('@/pages/platform/product/shopMaterialManage/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-店铺商品管理',
                }
            },
            {
                path: '/platform/product/shopMaterialManageDetail',
                name: 'shopMaterialManageDetail',
                component: () => import('@/pages/platform/product/shopMaterialManage/detail'),
                meta: {
                    title: '物资采购管理-店铺商品详情',
                },
            },
            {
                path: '/platform/product/shopCheckMaterial',
                name: 'shopCheckMaterial',
                component: () => import('@/pages/platform/product/shopCheckMaterial/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-店铺审核商品管理',
                }
            },
            {
                path: '/platform/product/shopCheckMaterialDetail',
                name: 'shopCheckMaterialDetail',
                component: () => import('@/pages/platform/product/shopCheckMaterial/detail'),
                meta: {
                    title: '物资采购管理-店铺审核商品详情',
                }
            },
            //商铺管理
            {
                path: '/platform/shop/shopAudit',
                component: () => import('@/pages/platform/shop/shopAudit/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-商铺审核'
                }
            },
            {
                path: '/platform/shop/shopAuditDetail',
                name: 'shopAuditDetail',
                component: () => import('@/pages/platform/shop/shopAudit/detail'),
                meta: {
                    title: '物资采购管理-商铺审核详情'
                }
            },
            {
                path: '/platform/shop/shopManage',
                component: () => import('@/pages/platform/shop/shopManage/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-商铺管理'
                }
            },
            {
                path: '/platform/shop/shopBusiness',
                component: () => import('@/pages/platform/shop/shopBusiness/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-自营店管理'
                }
            },
            {
                path: '/platform/shop/shopManageDetail',
                name: 'shopManageDetail',
                component: () => import('@/pages/platform/shop/shopManage/detail'),
                meta: {
                    title: '物资采购管理-商铺管理详情'
                }
            },
            //供应商
            {
                path: '/platform/supplier/supplierAudit',
                component: () => import('@/pages/platform/supplier/supplierAudit/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-供应商账号审核'
                }
            },
            {
                path: '/platform/supplier/supplierAuditDetail',
                name: 'supplierAuditDetail',
                component: () => import('@/pages/platform/supplier/supplierAudit/detail'),
                meta: {
                    title: '物资采购管理-供应商账号审核详情'
                }
            },
            {
                path: '/platform/supplier/supplierInquire',
                component: () => import('@/pages/platform/supplier/supplierInquire/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-供应商查询'
                }
            },
            {
                path: '/platform/supplier/supplierInquireDetail',
                name: 'supplierInquireDetail',
                component: () => import('@/pages/platform/supplier/supplierInquire/detail'),
                meta: {
                    title: '物资采购管理-供应商查询详情'
                }
            },
            {
                path: '/platform/supplier/ledger',
                name: 'ledger',
                component: () => import('@/pages/platform/supplier/ledger/index'),
                meta: {
                    title: '物资采购管理-供应商台账'
                }
            },
            // 采购方管理
            {
                path: '/platform/purchaser/ledger',
                name: 'ledger',
                component: () => import('@/pages/platform/purchaser/ledger/index'),
                meta: {
                    title: '物资采购管理-采购员台账'
                }
            },
            //订单管理
            {
                path: '/platform/order/searchOrder',
                component: () => import('@/pages/platform/order/searchOrder/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-订单查询'
                },
            },
            //平台交易缴费
            {
                path: '/platform/platformDealfee',
                component: () => import('@/pages/platform/fee/platformDealfee/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-服务交易费记录'
                },
            },
            {
                path: '/platform/platformDealfeeDetail',
                name: 'platformDealfeeDetail',
                component: () => import('@/pages/platform/fee/platformDealfee/detail.vue'),
                meta: {
                    title: '物资采购管理-服务交易费记录详情'
                },
            },
            {
                path: '/platform/fee/year',
                component: () => import('@/pages/platform/fee/year/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-服务年费记录'
                },
            },
            {
                path: '/platform/fee/yearDetail',
                name: 'platformYearDetail',
                component: () => import('@/pages/platform/fee/year/detail.vue'),
                meta: {
                    title: '物资采购管理-服务年费记录详情'
                },
            },

            {
                path: '/platform/order/orderDetail',
                name: 'orderDetail',
                component: () => import('@/pages/platform/order/searchOrder/detail'),
                meta: {
                    title: '物资采购管理-订单详情'
                }
            },
            {
                path: '/platform/order/towOrderDetail',
                name: 'towOrderDetail',
                component: () => import('@/pages/platform/order/searchOrder/towOrderDetail'),
                meta: {
                    title: '订单详情'
                }
            },
            {
                path: '/platform/order/refund',
                component: () => import('@/pages/platform/order/refund/index'),
                meta: {
                    keepAlive: true,
                    title: '订单退货管理-零星采购退货查询'
                },
            },
            {
                path: '/platform/order/tallRefund',
                component: () => import('@/pages/platform/order/refund/tallIndex.vue'),
                meta: {
                    keepAlive: true,
                    title: '订单退货管理-大宗月供退货查询'
                },
            },
            {
                path: '/platform/order/blockIndex',
                component: () => import('@/pages/platform/order/refund/blockIndex.vue'),
                meta: {
                    keepAlive: true,
                    title: '订单退货管理-大宗临购退货查询'
                },
            },
            {
                path: '/platform/order/refundDetail',
                name: 'refundDetail1',
                component: () => import('@/pages/platform/order/refund/detail'),
                meta: {
                    title: '物资采购管理-退货详情'
                }
            },
            {
                path: '/platform/order/tallDetail',
                name: 'tallDetail',
                component: () => import('@/pages/platform/order/refund/tallDetail.vue'),
                meta: {
                    title: '物资采购管理-退货详情'
                }
            },
            //客户查询
            {
                path: '/platform/user/userManager',
                component: () => import('@/pages/platform/user/userManager/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-账号查询'
                }
            },

            //物资订单管理-发票
            {
                path: '/platform/order/invoice',
                component: () => import('@/pages/platform/order/invoice/index'),
                meta: {
                    keepAlive: true,
                    title: '物资发票管理-发票查询'
                },
            },
            //物资订单管理-发票

            {
                path: '/platform/order/invoice/invoiceDetail',
                component: () => import('@/pages/platform/order/invoice/detail'),
                name: 'invoiceDetail',
                meta: {
                    title: '物资订单管理-发票详情'
                }
            },
            {
                path: '/platform/user/userInquireDetail',
                name: 'userInquireDetail',
                component: () => import('@/pages/platform/user/userManager/detail'),
                meta: {
                    title: '物资采购管理-客户管理'
                }
            },
            {
                path: '/platform/enterprise/inEnterpriseManager',
                component: () => import('@/pages/platform/enterprise/inEnterpriseManager/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-内部企业查询'
                }
            },
            {
                path: '/platform/enterprise/outEnterpriseManagerDetail',
                name: 'enterpriseManagerDetail',
                component: () => import('@/pages/platform/enterprise/outEnterpriseManager/detail'),
                meta: {
                    title: '物资采购管理-外部企业管理详情'
                }
            }, {
                path: '/platform/enterprise/inEnterpriseManagerDetail',
                name: 'enterpriseManagerDetail',
                component: () => import('@/pages/platform/enterprise/inEnterpriseManager/detail'),
                meta: {
                    title: '物资采购管理-内部企业管理详情'
                }
            },
            {
                path: '/platform/enterprise/outEnterpriseManager',
                component: () => import('@/pages/platform/enterprise/outEnterpriseManager/index'),
                meta: {
                    keepAlive: true,
                    title: '物资采购管理-外部企业查询'
                }
            },
            // {
            //     path: '/platform/enterprise/outEnterpriseManager',
            //     component: () => import('@/pages/platform/enterprise/outEnterpriseManager/index'),
            //     meta: {
            //         title: '物资采购管理-外部企业管理'
            //     }
            // },
            {
                path: '/platform/enterprise/inEnterpriseManager',
                component: () => import('@/pages/platform/enterprise/inEnterpriseManager/index'),
                meta: {
                    title: '物资采购管理-内部企业管理'
                }
            },

            {
                path: '/platform/enterprise/enterpriseManagerDetail',
                name: 'enterpriseManagerDetail',
                component: () => import('@/pages/platform/enterprise/enterpriseManager/detail'),
                meta: {
                    title: '物资采购管理-企业详情'
                }
            },
            //统计分析
            {
                path: '/platform/analysis/order',
                component: () => import('@/pages/platform/analysis/order/index'),
                meta: {
                    title: '物资采购管理-订单统计'
                }
            },
            {
                path: '/platform/analysis/shop',
                component: () => import('@/pages/platform/analysis/shop/index.vue'),
                meta: {
                    title: '物资采购管理-店铺统计'
                }
            },
            {
                path: '/platform/analysis/product',
                component: () => import('@/pages/platform/analysis/product/index'),
                meta: {
                    title: '物资采购管理-商品统计'
                }
            },
            {
                path: '/platform/analysis/customer',
                component: () => import('@/pages/platform/analysis/customer/index'),
                meta: {
                    title: '物资采购管理-用户统计'
                }
            },
            {
                path: '/platform/analysis/operand',
                component: () => import('@/pages/platform/analysis/operand/index'),
                meta: {
                    title: '物资采购管理-商品操作数统计'
                }
            },
            {
                path: '/platform/demo/full',
                component: () => import('@/pages/platform/demo/full/index'),
                meta: {
                    title: 'demo'
                }
            },
            {
                path: '/platform/analysis/finance',
                component: () => import('@/pages/platform/analysis/finance/index'),
                meta: {
                    title: '物资采购管理-金融产品'
                }
            },
            //信息发送，接收
            {
                path: '/platform/mail/inBox',
                component: () => import('@/pages/platform/mail/inBox/index'),
                meta: {
                    title: '我的消息-收件箱'
                }
            },
            {
                path: '/platform/mail/OutBoxDetail',
                name: 'OutBoxDetail',
                component: () => import('@/pages/platform/mail/outBox/detail.vue'),
                meta: {
                    title: '我的消息-消息详情'
                }
            },
            //信息发送，发送
            {
                path: '/platform/mail/outBox',
                component: () => import('@/pages/platform/mail/outBox/index'),
                meta: {
                    title: '我的消息-发件箱'
                }
            },
            //反馈中心
            {
                path: '/platform/mail/message',
                component: () => import('@/pages/platform/mail/message/index'),
                meta: {
                    title: '反馈中心'
                }
            },
            {
                path: '/platform/mail/messageDetail',
                name: 'messageDetail',
                component: () => import('@/pages/platform/mail/message/detail.vue'),
                meta: {
                    title: '反馈详情'
                }
            },
            //品牌管理
            {
                path: '/platform/brand/brandLogo',
                component: () => import('@/pages/platform/brand/brandLogo/index'),
                meta: {
                    title: '商品管理-品牌管理'
                }
            },
            //报表管理
            {
                path: '/platform/analysis/productFrom',
                component: () => import('@/pages/platform/analysis/reportForms/index.vue'),
                meta: {
                    title: '报表管理-上架商品报表'
                }
            },
            {
                path: '/platform/analysis/orderStatement',
                component: () => import('@/pages/platform/analysis/orderStatement/index.vue'),
                meta: {
                    title: '报表管理-物资结算报表'
                }
            },
            {
                path: '/platform/analysis/supplierFree',
                component: () => import('@/pages/platform/analysis/supplierFree/index.vue'),
                meta: {
                    title: '报表管理-供应商费用报表'
                }
            },
            {
                path: '/platform/analysis/sendProductStatement',
                component: () => import('@/pages/platform/analysis/sendProductStatement/index.vue'),
                meta: {
                    title: '报表管理-物资交易量报表'
                }
            },
            {
                path: '/platform/analysis/reconciliationLedger',
                component: () => import('@/pages/platform/analysis/reconciliationLedger/index.vue'),
                meta: {
                    title: '报表管理-物资对账统计台账单'
                }
            },
            {
                path: '/platform/system/menuManage',
                component: () => import('@/pages/platform/system/menuManage/index'),
                meta: {
                    title: '菜单管理'
                }
            },
            {
                path: '/platform/system/userManage',
                component: () => import('@/pages/platform/system/userManage/index'),
                meta: {
                    title: '用户管理'
                }
            },
            {
                path: '/platform/system/roleManage',
                component: () => import('@/pages/platform/system/roleManage/index'),
                meta: {
                    title: '角色管理'
                }
            },
            //系统参数表
            {
                path: '/platform/system/systemParam',
                component: () => import('@/pages/platform/system/systemParam/index'),
                meta: {
                    title: '系统参数表-参数管理'
                }
            },
            //字典参数表
            {
                path: '/platform/system/dictionaries',
                component: () => import('@/pages/platform/system/dictionaries/index'),
                meta: {
                    title: '字典参数表-字典管理'
                }
            },
            //蜀道企业库管理
            {
                path: '/platform/system/shudaoEnterprise',
                component: () => import('@/pages/platform/system/shudaoEnterprise/index'),
                meta: {
                    title: '系统参数表-蜀道企业库管理'
                }
            },
            {
                path: '/platform/process/review',
                component: () => import('@/pages/platform/process/review/index'),
                meta: {
                    title: '流程管理-审核场景审核流程'
                }
            },
            {
                path: '/platform/process/review/reviewDetail',
                name: 'reviewDetail',
                component: () => import('@/pages/platform/process/review/detail'),
                meta: {
                    title: '流程管理-审核场景审核流程',
                },
            },

            {
                path: '/platform/analysis/system',
                component: () => import('@/pages/platform/analysis/system/index'),
                meta: {
                    title: '物资采购平台-系统运营统计'
                }
            },
            {
                path: '/platform/fee/yearPayRecord',
                name: 'platformFeeYearPayRecord',
                component: () => import('@/pages/platform/fee/yearPayRecord/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '年度服务费审核'
                }
            },
            {
                path: '/platform/fee/yearPayRecordDtl',
                name: 'platformFeeYearPayRecordDtl',
                component: () => import('@/pages/platform/fee/yearPayRecord/detail.vue'),
                meta: {
                    title: '年度服务费缴费详情'
                }
            },
            {
                path: '/platform/fee/freeDtlAll',
                name: 'platformFreeDtlAll',
                component: () => import('@/pages/platform/fee/freeDtlAll/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '费用明细'
                }
            },
            {
                path: '/platform/fee/payRecordManage',
                name: 'platformFeePayRecordManage',
                component: () => import('@/pages/platform/fee/payRecordManage/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '缴费审核'
                }
            },
            {
                path: '/platform/fee/payRecordManageDtl',
                name: 'platformFeePayRecordManageDtl',
                component: () => import('@/pages/platform/fee/payRecordManage/detail.vue'),
                meta: {
                    title: '年度服务费缴费详情'
                }
            },
            {
                path: '/platform/fee/payRecordManageDtl2',
                name: 'platformFeePayRecordManageDtl2',
                component: () => import('@/pages/platform/fee/payRecordManage/dealDetail.vue'),
                meta: {
                    title: '交易服务费缴费详情'
                }
            },
            {
                path: '/platform/fee/dealPayRecord',
                name: 'platformFeeDealPayRecord',
                component: () => import('@/pages/platform/fee/dealPayRecord/index.vue'),
                meta: {
                    keepAlive: true,
                    title: '交易服务费审核'
                }
            },
            {
                path: '/platform/fee/dealPayRecordDetail',
                name: 'platformFeeDealPayRecordDtl',
                component: () => import('@/pages/platform/fee/dealPayRecord/detail.vue'),
                meta: {
                    title: '交易服务费缴纳记录'
                }
            },
        ]
    },

    // {
    //     path: '/platform/mail/outBox',
    //     component: () => import('@/pages/platform/mail/outBox/index'),
    //     meta: {
    //         title: '物资采购管理-商品统计'
    //     }
    // },
]
