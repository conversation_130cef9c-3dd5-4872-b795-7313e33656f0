<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
<!--                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>-->
<!--                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">批量启用</el-button>-->
<!--                            <el-button type="primary" @click="changePublishState(2)" class="btn-greenYellow">批量停用</el-button>-->
<!--                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>-->
<!--                            <el-button type="primary" @click="changeSortValue" class="btn-greenYellow">批量修改排序值</el-button>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
<!--                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>-->
                        <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" v-loading="isLoading" :style="{ width: '100%' }">
                <el-table class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                          @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
<!--                    <el-table-column type="selection" width="40"></el-table-column>-->
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
<!--                    <el-table-column label="操作" width="80">-->
<!--                        <template slot-scope="scope">-->
<!--                            <span class="action" @click="onDel(scope)"><img src="../../../../assets/btn/delete.png" alt=""></span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column label="企业名称" width="">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">{{scope.row.enterpriseName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业类型" width="">
                        <template slot-scope="scope">
                            <span v-if="scope.row.enterpriseType == '0'">个体户</span>
                            <span v-else-if="scope.row.enterpriseType == '1'">企业</span>
                            <span v-else-if="scope.row.enterpriseType == '2'">个人</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="企业营业状态" width="">-->
<!--                        <template slot-scope="scope">-->
<!--                            <el-tag v-if="scope.row.enterpriseBusinessType==1" type="success">营业</el-tag>-->
<!--                            <el-tag v-if="scope.row.enterpriseBusinessType==0" type="danger">停业</el-tag>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column label="注册资金(万元)" width="">
                        <template slot-scope="scope">
                            {{ scope.row.registeredCapital }}
                        </template>
                    </el-table-column>
                    <el-table-column label="注册时间" width="">
                        <template slot-scope="scope">
                            {{ scope.row.creationTime}}
                        </template>
                    </el-table-column>
                    <el-table-column label="营业执照有效期" width="">
                        <template slot-scope="scope">
                            <span v-if="scope.row.licenseTerm != null">{{ scope.row.licenseTerm }}</span>
                            <span v-else-if="scope.row.licenseTerm == null">长期</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业所在城市" width="">
                        <template slot-scope="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.city }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="企业注册地址" width="">
                        <template slot-scope="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.province }}{{ scope.row.county }}{{ scope.row.detailedAddress }}
                            </span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="供应商" width="">-->
<!--                        <template slot-scope="scope">-->
<!--                            <span v-if="scope.row.isSupplier == '0'">否</span>-->
<!--                            <span v-else-if="scope.row.isSupplier == '1'">待审核</span>-->
<!--                            <span v-else-if="scope.row.isSupplier == '2'">是</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
<!--                    <el-table-column label="路桥结算" width="">-->
<!--                        <template slot-scope="scope">-->
<!--                            <span v-if="scope.row.isInternalSettlement == '1'">支持</span>-->
<!--                            <span v-else-if="scope.row.isInternalSettlement == '0'">不支持</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
<!--                    <el-table-column label="店铺审核状态" width="">-->
<!--                        <template slot-scope="scope">-->
<!--                            <span v-if="scope.row.auditStatus == '1'">审核通过</span>-->
<!--                            <span v-else-if="scope.row.auditStatus == '2'">未审核</span>-->
<!--                            <span v-else-if="scope.row.auditStatus == '3'">审核未通过</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
<!--&lt;!&ndash;                     排序值 &ndash;&gt;-->
<!--                    <el-table-column label="排序值" width="120" prop="sort">-->
<!--                        <template v-slot="scope">-->
<!--                            <el-input v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                           @currentChange="currentChange" @sizeChange="sizeChange" />
        </div>
        <div class="right" v-show="viewList !== true">
            <!-- ---------------------新增/编辑窗口--------------------- -->
            <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
                <div class="tabs-title">基本信息</div>
                <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item width="150px" label="店铺名称：" prop="name">
                                <el-input v-model="formData.shopName" placeholder="请输入链接名称" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item width="150px" label="店铺类型：" prop="url">
                                <el-input v-model="formData.shopType" placeholder="" clearable></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="店铺所在省份：" prop="sort">
                                <el-input v-model="formData.province" placeholder="请选择店铺所在省份" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="店铺所在市：" prop="sort">
                                <el-input v-model="formData.province" placeholder="请选择店铺所在省份" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="店铺所在县、区：" prop="sort">
                                <el-input v-model="formData.city" placeholder="请选择店铺所在县、区" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="店铺详细地址：" prop="sort">
                                <el-input v-model="formData.detailedAddress" placeholder="请输入店铺详细地址" clearable></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="店铺状态：" prop="sort">
                                <el-input v-model="formData.state" placeholder="请输入店铺详细地址" clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input v-model="formData.sort" :min="0"  type="number" placeholder="填写排序值">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input type="textarea" v-model="formData.remarks" autocomplete="off"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="32%" :close-on-click-modal="true" :before-close="closeDialog">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="24">
                        <el-form-item width="150px" label="企业名称：" prop="shopName">
                            <el-input v-model="filterData.enterpriseName" placeholder="请输入企业名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="企业所在城市：" prop="sort">
                            <el-input v-model="filterData.city" placeholder="企业所在城市" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="企业类型：">
                            <el-select v-model="filterData.enterpriseType"  placeholder="企业类型">
                                <el-option v-for="item in enterpriseTypeFilter" :key="item.value" :label="item.label"
                                           :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
<!--                    <el-col :span="12">-->
<!--                        <el-form-item label="企业状态：">-->
<!--                            <el-select v-model="filterData.enterpriseBusinessType"  placeholder="企业状态">-->
<!--                                <el-option v-for="item in enterpriseStateFilter" :key="item.value" :label="item.label"-->
<!--                                           :value="item.value">-->
<!--                                </el-option>-->
<!--                            </el-select>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="advancedQuery">确定</el-button>
                <el-button @click="hideDialog">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getList, getListAll, edit, create, del, batchPublish, batchNotPublish, batchDelete } from '@/api/platform/supplier/supplierAudit'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
import { mapActions } from 'vuex'
export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getListAll(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertName: '店铺',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            selectedRows: [],
            changedRow: [],
            isLoading: false,
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                enterpriseName: '',
                city: '',
                enterpriseType: null,
                state: 1,
                isSupplier: null,
                orderBy: 2,
                mallType: 0,
            },
            tableData: [],
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入链接名', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
                name: '',
                url: '',
                remarks: '',
            },
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            enterpriseTypeFilter: [
                { value: null, label: '全部' },
                { value: 1, label: '企业' },
                { value: 0, label: '个体户' },
            ],
            enterpriseStateFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '停业' },
                { value: 1, label: '营业' },
            ],
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        this.isLoading = true
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            mallType: this.filterData.mallType,
            orderBy: this.filterData.orderBy,
            state: this.filterData.state,
            isSupplier: this.filterData.isSupplier,
            auditStatus: this.filterData.auditStatus,
        }
        getListAll(params).then(res => {
            this.pages = res
            this.isLoading = false
            this.tableData = res.list
        })
        this.getParams()
    },
    methods: {
        // 关闭对话框（取消）
        closeDialog (done) {
            this.filterData = {
                enterpriseName: '',
                city: '',
                enterpriseType: null,
                state: 1,
                isSupplier: 2,
                orderBy: 2,
                mallType: 0,
            },
            done()
        },
        //高级查询返回
        hideDialog () {
            this.filterData = {
                enterpriseName: '',
                city: '',
                enterpriseType: null,
                state: 1,
                isSupplier: 2,
                orderBy: 2,
                mallType: 0,
            },
            this.queryVisible = false
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 高级查询
        advancedQuery () {
            for (let key in this.filterData) {
                if (this.filterData[key] === '') {
                    this.filterData[key] = null
                }
            }
            this.getParams()
            // 查询请求传参
            getListAll(this.requestParams).then(res => {
                this.isLoading = true
                if (res.list) {
                    this.queryVisible = false
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                this.isLoading = false
                this.pages = res
                // this.viewList = true
            })
        },
        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.enterpriseId
            })
            if (!this.selectedRows[0]) {
                let msg = num == 1 ? `请选择要启用的${this.alertName}` : `请选择要停用的${this.alertName}`
                return this.clientPop('warn', msg, () => { })
            }
            let warnMsg = num === 1 ? `您确定要启用选中的${this.alertName}吗？` : `您确定要停用选中的${this.alertName}吗？停用将下架当前店铺所有商品！`
            this.clientPop('info', warnMsg, async () => {
                switch (num)
                {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '启用成功', () => {
                                getListAll(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => { })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '停用成功', () => {
                                getListAll(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {})
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${this.alertName}吗？`, async () => {
                showLoading()
                del({ id: scope.row.enterpriseId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    } else {
                        this.clientPop('warn', res.message, () => { })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if(!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {})
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.enterpriseId
                })
                batchDelete(arr).then(res => {
                    if(res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () { },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = JSON.parse(JSON.stringify(scope.row))
            this.action = '编辑'
            this.$router.push({
                path: '/platform/enterprise/enterpriseManagerDetail',
                name: 'enterpriseManagerDetail',
                params: {
                    row: {
                        ent: scope.row
                    }
                }
            })
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取修改过的表格行数据
        getChangedRow (row) {
            if(!this.changedRow[0]) {
                return this.changedRow.push({ enterpriseId: row.enterpriseId, sort: parseInt(row.sort) })
            }
            let arr = this.changedRow.map((item, i) => {
                if(item.enterpriseId === row.enterpriseId) {
                    return i
                }
            })
            if(arr[0]) {
                return this.changedRow[arr[0]].sort = row.sort
            }
            this.changedRow.push({ enterpriseId: row.enterpriseId, sort: parseInt(row.sort) })
        },
        // 修改排序值
        // changeSortValue () {
        //     // changeSortValue(this.changedRow).then(res => {
        //     //     this.getTableData()
        //     // })
        //     if (!this.changedRow[0]) {
        //         let msg = '当前没有排序值被修改！'
        //         return this.clientPop('warn', msg, () => { })
        //     }
        //     let warnMsg = '您确定要修改‘这些’的排序值吗？'
        //     this.clientPop('info', warnMsg, async () => {
        //         changeSortValue(this.changedRow).then(res => {
        //             if (res.message === '操作成功') {
        //                 this.clientPop('suc', '修改成功', () => {
        //                     this.getTableData()
        //                 })
        //             } else {
        //                 this.clientPop('warn', '操作失败请稍后再试', () => {})
        //             }
        //         })
        //     })
        // },
        // 获取列表数据
        async getTableData () {
            this.isLoading = true
            this.getParams()
            getListAll(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                this.isLoading = false
                this.pages = res
            })
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getParams()
            getListAll(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {})
                }
            })
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.action === '编辑') {
                        return this.handleEditData()
                    }
                    this.handleCreateData()
                }
            })
        },
        // 修改数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 保存数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}
.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}
</style>
