<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="基础信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="对公账户" name="corporateAccount" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="服务费对账单确认" name="serviceFeeStatement" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="缴费信息" name="payInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="审核记录" name="auditRecords" :disabled="clickTabFlag" />
                <el-tab-pane label="本期结算对账单" name="baseInfoDtl" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="本期冲销的已作废对账单" name="invalidStatement" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                  <el-form :model="dfr" label-width="260px" :disabled="false" class="demo-ruleForm">
                    <div id="baseInfoCon" class="con">
                        <div class="tabs-title" id="baseInfo">基础信息</div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="店铺名称：" prop="shopName">
                                        <span>{{dfr.shopName}}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="供应商名称：" prop="enterpriseName">
                                        <span>{{dfr.enterpriseName}}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="本次结算交易额：" prop="periodTransactionAmount">
                                        <span>{{dfr.periodTransactionAmount}}元</span>
                                    </el-form-item>
                                </el-col>
                              <el-col :span="12">
                                <el-form-item label="结算日期：" prop="settleDate">
                                  <span>{{dfr.settleDate}}</span>
                                </el-form-item>
                              </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="交易服务费：" prop="payAmount">
                                        <span>{{dfr.payAmount}}元</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="累计结算交易额：" prop="totalTransactionAmount">
                                        <span>{{dfr.totalTransactionAmount}}元</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="当期交易覆盖交易时间段：">
                                      <span >{{ dfr.periodStartDate }}至{{ dfr.periodEndDate }}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                    </div>
                    <div id="corporateAccountCon" class="con">
                        <div class="tabs-title" id="corporateAccount">对公账户</div>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="开户银行：" prop="bankName">
                            <span>{{dfr.platformFreeyhAddress}}</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="银行户名：" prop="accountName">
                            <span>{{dfr.platformFreeyhOrgName}}</span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="银行帐号：" prop="bankAccount">
                            <span>{{dfr.platformFreeyhAccount}}</span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                    <div id="serviceFeeStatementCon" class="con">
                        <div class="tabs-title" id="serviceFeeStatement">服务费对账单确认</div>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="确认状态：" prop="state">
                            <el-tag  type="info" v-if="dfr.state === 0">待确认</el-tag>
                            <el-tag  v-else-if="dfr.state === 1">确认中</el-tag>
                            <el-tag  type="danger"  v-else-if="dfr.state === 3">确认失败</el-tag>
                            <el-tag  type="success" v-else>已确认</el-tag>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="对账单确认文件：" prop="accountName" class="action">
                            <span @click="handleDownload()">{{reconsLoading ? "加载中" : (recons.length>0? recons[0].name : "")}}</span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                    <div id="payInfoCon" class="con" v-if="dfr.state ===2 || dfr.state > 3">
                        <div class="tabs-title" id="payInfo">缴费信息</div>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="缴费金额：" prop="payAmount">
                            {{dfr.payAmount}}元
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="提交时间：" prop="gmtModified">
                            {{dfr.gmtCreate}}
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item  label="缴费凭证：" prop="file">
                            <el-upload
                              class="hide_box_min"
                              action="fakeaction"
                              :disabled="pmtSlipsLoading"
                              :limit="1"
                              :file-list="pmtSlips.length>0?pmtSlips.slice(0,1):[]"
                              list-type="picture-card">
                              <i slot="default" class="el-icon-plus"></i>
                              <div slot="file" slot-scope="{file}">
                                <img
                                    class="el-upload-list__item-thumbnail"
                                    :src="file.url"
                                    alt=""
                                    />
                                <span class="el-upload-list__item-actions">
                                    <span
                                      class="el-upload-list__item-preview"
                                      @click="$emit('pmt-slips-preview', file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                      class="el-upload-list__item-delete"
                                      @click="handleDownload(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                  </span>
                              </div>
                            </el-upload>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="缴费状态：" prop="state">
                            <el-tag  v-if="dfr.state === 2">未缴费</el-tag>
                            <el-tag  v-else-if="dfr.state === 4">审核中</el-tag>
                            <el-tag  type="danger"  v-else-if="dfr.state === 6">审核失败</el-tag>
                            <el-tag  type="success"  v-else-if="dfr.state === 5">审核成功</el-tag>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                    <div id="auditRecordsCon" class="con">
                        <div class="tabs-title" id="auditRecords">审核记录</div>
                        <div class="e-table"  style="background-color: #fff">
                            <el-table
                                border
                                style="width: 100%"
                                :data="auditRecords"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="120"></el-table-column>
                                <el-table-column prop="auditResult" label="审核结果" width="" show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="400">
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="审核时间" width="300">
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                  <div id="baseInfoDtlCon" class="con">
                    <div class="tabs-title" id="baseInfoDtl">本期结算对账单</div>
                    <div class="e-table"  style="background-color: #fff" v-loading="dealReconsLoading">
                      <el-table
                        border
                        style="width: 100%"
                        max-height="342px"
                        :data="dealRecons.filter(d=>d.state==3)"
                        :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                        :row-style="{ fontSize: '14px', height: '48px' }"
                      >
                        <el-table-column label="序号" type="index" width="120"></el-table-column>
                        <el-table-column prop="reconciliationNo" label="对账单编号"/>
                        <el-table-column prop="reconciliationAmount" label="对账金额"/>
                        <el-table-column label="审核通过时间" width="300">
                            <template v-slot="{ row: { auditRecords }}">
                                <span >{{ auditRecords.length>0 ? auditRecords[0].gmtCreate : ''}}</span>
                            </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                  <div id="invalidStatementCon" class="con">
                    <div class="tabs-title" id="invalidStatement">本期冲销的已作废对账单</div>
                    <div class="e-table"  style="background-color: #fff" v-loading="dealReconsLoading">
                      <el-table
                        border
                        style="width: 100%"
                        max-height="342px"
                        :data="dealRecons.filter(d=>d.state==7)"
                        :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                        :row-style="{ fontSize: '14px', height: '48px' }"
                      >
                        <el-table-column label="序号" type="index" width="120"></el-table-column>
                        <el-table-column prop="reconciliationNo" label="对账单编号"/>
                        <el-table-column prop="reconciliationAmount" label="已作废对账金额"/>
                        <el-table-column prop="nullifyCreated" label="作废时间" width="300"/>
                      </el-table>
                    </div>
                  </div>
                  </el-form>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button type="primary"
                       class="btn-greenYellow"
                       v-if="dfr.state === 0 || dfr.state === 3"
                       @click="goBill">上传对账单</el-button>
            <el-button type="primary"
                       class="btn-greenYellow"
                       v-else-if="dfr.state === 2 || dfr.state === 6"
                       @click="goPay">缴费</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
import { throttle } from '@/utils/common'
import { previewFile } from '@/api/platform/common/file'
export default {
    data () {
        return {
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    props: {
        dfr: { // 交易服务费
            type: Object,
            required: true
        },
    },
    computed: {
        recons () { // 对账单
            return this.dfr.recons
        },
        reconsLoading () { // 对账单加载中
            return this.dfr.reconsLoading
        },
        pmtSlips () { // 缴费凭证
            return this.dfr.pmtSlips
        },
        pmtSlipsLoading () { // 缴费凭证加载中
            return this.dfr.pmtSlipsLoading
        },
        dealRecons () { // 交易对账单
            return this.dfr.dealRecons
        },
        dealReconsLoading () { // 交易对账单加载中
            return this.dfr.dealReconsLoading
        },
        auditRecords () { // 审核记录
            return this.dfr.auditRecords
        },
        auditRecordsLoading () { // 审核记录加载中
            return this.dfr.rauditRecordsLoading
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        handleDownload () {
            let image = this.pmtSlips[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            })
        },
        goPay () {
            this.$emit('pay', this.dfr)
        },
        goBill () {
            this.$emit('bill', this.dfr)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$emit('close')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
/deep/ .showImage {
    .el-dialog__header {
        //display: none;
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        padding: 0px;
        margin: 0px;
    }
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

/deep/ .selectDealDia {
    .el-dialog__body {
        margin-top: 0px;
    }
}

</style>