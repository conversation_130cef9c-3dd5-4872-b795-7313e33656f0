<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-show="viewList === true">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
<!--                        <div class="left-btn">
                            <el-select v-model="filterData.isRead" @change="getReceiveList">
                                <el-option v-for="item in isReads" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>

                        </div>
                        <div style="margin-left: 10px">
                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>
                        </div>-->
                    </div>

                    <div class="search_box">
                        <el-checkbox v-model="isComment" @change="handleClick">
                            <span>综合评价</span><img :src="ComprehensiveEvaluation" alt="">
                        </el-checkbox>
                        <el-input type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table
                    class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
                >
<!--                    <el-table-column type="selection" width="40"></el-table-column>-->
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="80">
                        <template slot-scope="scope">
                          <el-button
                            style="padding:0 8px;"
                            size="mini"
                            type="primary"
                            @click="evaluate(scope.row)"
                          >评价
                          </el-button>
                        </template>
                    </el-table-column>
                    <!-- 发件人名称 -->
                    <el-table-column label="供应商名称" width="" prop="shopName">
<!--                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.sendName }}</span>
                        </template>-->
                    </el-table-column>
                    <el-table-column label="商品品质" width="100" prop="commentLevel">
                    </el-table-column>
                    <el-table-column label="保供能力" width="100" prop="commentSupply">
                    </el-table-column>
                    <el-table-column label="诚信履约" width="100" prop="commentIntegrity">
                    </el-table-column>
                    <el-table-column label="服务水平" width="100" prop="commentService">
                    </el-table-column>
                    <el-table-column label="供货类型" width="200" prop="mainBusiness">
                    </el-table-column>
                    <el-table-column label="汇总日期" width="200" prop="sumDate">
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="供应商名称：">
                    <el-input v-model="filterData.shopName" placeholder="请输入供应商名称" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品品质：">
                            <el-select v-model="filterData.commentLevel" style="width: 100%">
                                <el-option v-for="item in rateOptions" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                  <el-col :span="12">
                    <el-form-item label="保供能力：">
                      <el-select v-model="filterData.commentSupply" style="width: 100%">
                        <el-option v-for="item in rateOptions" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="诚信履约：">
                    <el-select v-model="filterData.commentIntegrity" style="width: 100%">
                      <el-option v-for="item in rateOptions" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="服务水平：">
                    <el-select v-model="filterData.commentService" style="width: 100%">
                      <el-option v-for="item in rateOptions" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="发布日期：">
                            <el-date-picker
                                style="width: 100%"
                                value-format="yyyy-MM-dd HH:mm:ss" v-model="filterData.dateValue" type="datetimerange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
<!--                <el-button type="primary" @click="onSave">确定</el-button>-->
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { debounce, hideLoading, showLoading, stripHtmlTags } from '@/utils/common'
import arrow from '@/assets/images/arrow.png'
import arrow2 from '@/assets/images/arrow2.png'
import { mapActions } from 'vuex'
import { previewFile } from '@/api/platform/common/file'
import { selectFileList } from '@/api/base/file'
import { getShopComment } from '@/api/shopManage/content/supplierComment'

export default {
    components: {
        ComPagination,
        // editor
    },
    watch: {
        'isComment': {
            handler () {
                this.getReceiveList()
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            fileList: [],
            alertName: '消息',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            isComment: false,
            ComprehensiveEvaluation: arrow,
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                shopName: '',
                commentLevel: '',
                commentSupply: '',
                commentIntegrity: '',
                commentService: '',
                dateValue: [], // 开始时间和结束时间
            },
            tableData: [],
            formData: {
                content: '',
            },
            rateOptions: [
                {
                    value: 1,
                    label: '1星'
                },
                {
                    value: 2,
                    label: '2星'
                },
                {
                    value: 3,
                    label: '3星'
                },
                {
                    value: 4,
                    label: '4星'
                },
                {
                    value: 5,
                    label: '5星'
                }],
            mapObj: null,
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {}
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        this.getReceiveList()
        this.getParams()
    },
    methods: {
        stripHtmlTags,
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        resetSearchConditions () {
            this.filterData.shopName = ''
            this.filterData.commentLevel = ''
            this.filterData.commentSupply = ''
            this.filterData.commentIntegrity = ''
            this.filterData.commentService = ''
            this.filterData.dateValue = [] // 开始时间和结束时间
        },
        handleClick (value) {
            if(value) {
                this.isComment = value
                this.ComprehensiveEvaluation = arrow2
            }else{
                this.isComment = ''
                this.ComprehensiveEvaluation = arrow
            }
            this.handleInputSearch()
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getReceiveList()
        },
        confirmSearch () {
            this.getReceiveList()
            this.queryVisible = false
        },
        getReceiveList () {
            this.getParams()
            this.showLoading = true
            getShopComment(this.requestParams).then(res => {
                this.tableData = []
                res.list.forEach(t => {
                    this.tableData.push({
                        shopId: t.shopId,
                        shopName: t.shopName,
                        commentLevel: t.commentLevel,
                        commentSupply: t.commentSupply,
                        commentIntegrity: t.commentIntegrity,
                        commentService: t.commentService,
                        mainBusiness: t.mainBusiness,
                        remarks: t.remarks,
                        sumDate: t.commentStart != null && t.commentEnd != null ? (t.commentStart.substring(0, 10) + '至' + t.commentEnd.substring(0, 10)) : null,
                        commentDate: t.gmtCreate != null ? t.gmtCreate.substring(0, 10) : null,
                    })
                })
                this.pages.totalPage = res.totalPage
                this.pages.totalCount = res.totalCount
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        stateOptionsClick (value) {
            this.filterData.isRead = value
        },
        // 获取页面参数
        getParams () {
            this.requestParams.limit = this.pages.pageSize
            this.requestParams.page = this.pages.currPage
            this.requestParams.keywords = this.keywords
            this.requestParams.isComment = this.isComment
            this.requestParams.shopName = this.filterData.shopName,
            this.requestParams.commentLevel = this.filterData.commentLevel,
            this.requestParams.commentSupply = this.filterData.commentSupply,
            this.requestParams.commentIntegrity = this.filterData.commentIntegrity,
            this.requestParams.commentService = this.filterData.commentStart,
            this.requestParams.commentStart = this.filterData.dateValue[0]
            this.requestParams.commentEnd = this.filterData.dateValue[1]
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${scope.row.title}吗？`, async () => {
                showLoading()
                // eslint-disable-next-line no-undef
                del({ id: scope.row.stationMessageReceiveId }).then(res => {
                    if (res.message === '操作成功') {
                        this.$message({
                            message: '删除成功',
                            type: 'error'
                        })
                        this.getReceiveList()
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () {
        },
        getFileInfos (relevanceId) {
            let params = {
                relevanceId: relevanceId,
                relevanceType: 7,
            }
            selectFileList(params).then(res => {
                this.fileList = res.list
            })
        },
        // 获取列表数据
        async getTableData () {
            this.getReceiveList()
            this.viewList = true
        },
        evaluate (row) {
            console.log(row)
            this.$router.push({
            //path后面跟跳转的路由地址
                path: '/supplierSys/comment/supplierCommentDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'supplierCommentDetail',
                params: {
                    row: row
                }
            })
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        handleCurrentChange (val) {
            this.currentRow = val
        },
        //查看消息的接口
        onSave () {
            this.getReceiveList()
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

/deep/ .el-col.editorCol {
    .el-form-item__content {
        height: unset !important;

        .content {
            width: 100%;
            height: 300px;
            overflow: auto;
            border: 1px solid lightgray
        }
    }
}

.receiveText {

    font-size: 15px;
    width: 1115px;
    height: 304px;
    border: solid 1px #cbc3c3;
    margin-right: 30px;
    padding: 20px
}
/deep/ .el-dialog__body {
  margin-top: 0;
}
.search_box .el-checkbox {
    margin-right: 10px;
}
.search_box /deep/ .el-checkbox__input .el-checkbox__inner {
    border-radius: 50%;
    width: 18px;
    height: 18px;
}
</style>
