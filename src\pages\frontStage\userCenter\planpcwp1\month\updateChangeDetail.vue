<template>
    <div class="root" v-loading="showLoading">
        <div class="order-info box center">
            <div class="dfa mb20">
                <el-button type="primary" v-if="addPlanForm.state == 0 || addPlanForm.state == 3 || addPlanForm.state == 1" class="btn-greenYellow" @click="savePlanM">保存</el-button>
                <el-button type="primary" v-if="addPlanForm.state == 0 || addPlanForm.state == 3 || addPlanForm.state == 1" class="btn-greenYellow" @click="savePlanM(1)">保存并提交</el-button>
                <el-button type="primary" class="btn-greenYellow" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="auditPlanM(1, '通过')">通过</el-button>
                <el-button type="primary" class="btn-delete" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="auditPlanM(0, '未通过')">未通过</el-button>
                <el-button type="warning" class="" v-if="addPlanForm.state == 1 && this.userInfo.isMonthPlanAudit == 1" @click="cancellationPlanM()">作废</el-button>
            </div>
            <el-form  :inline="true" ref="addPlanFormRoteRef" :model="addPlanForm" :data="addPlanForm" :rules="addPlanFormRote">
                <el-row style="">
                    <el-col :span="8">
                        <el-form-item label="变更计划编号：" prop="planChangeNo">
                            <el-input style="width: 300px" disabled v-model="addPlanForm.planChangeNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="当前计划月份：" prop="thisTruePlanDate">
                            <el-input style="width: 300px" disabled v-model="addPlanForm.thisTruePlanDate"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="">
                    <el-col :span="8">
                        <el-form-item label="计划编号：" prop="planNo">
                            <el-input style="width: 300px" clearable disabled v-model="addPlanForm.planNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="变更单据状态：" prop="demandType">
                            <el-tag type="info" v-if="addPlanForm.state == 0">草稿</el-tag>
                            <el-tag type="" v-if="addPlanForm.state == 1">已提交</el-tag>
                            <el-tag type="success" v-if="addPlanForm.state == 2">通过</el-tag>
                            <el-tag type="danger" v-if="addPlanForm.state == 3">未通过</el-tag>
                            <el-tag type="warning" v-if="addPlanForm.state == 4">已作废</el-tag>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9" :offset="0">
                        <el-form-item label="单据机构：" prop="orgName">
                            <el-input disabled style="width: 300px" v-model="addPlanForm.orgName" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="合同编号：" prop="contractNo">
                            <el-input disabled style="width: 300px" v-model="addPlanForm.contractNo" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="变更计划月份：" prop="planDate">
                            <el-date-picker
                                value-format="yyyy-MM"
                                v-model="addPlanForm.planDate"
                                type="month"
                                align="right"
                                :picker-options="pickerOptions"
                                placeholder="选择月">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-form-item label="供应商：" prop="supplierName">
                            <el-input disabled style="width: 300px" v-model="addPlanForm.supplierName" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input style="width: 1100px;" type="textarea" :auto-resize="false" v-model="addPlanForm.remarks"
                                      placeholder="请输入备注" maxlength="1000" show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-tabs v-model="activeName"  type="card" @tab-click="handleClick">
                <el-tab-pane label="明细列表" name="dtl">
                    <el-table
                        max-height="372px"
                        border
                        :data="addPlanForm.dtls"
                        style="min-height: 372px"
                        :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                        :row-style="{ fontSize: '14px', height: '48px' }"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="materialName" label="物资名称" width="">
                        </el-table-column>
                        <el-table-column prop="spec" label="规格型号" width="">
                        </el-table-column>
                        <el-table-column prop="unit" label="计量单位" width="100">
                        </el-table-column>
                        <el-table-column prop="sourceQty" label="总数量" width="100">
                        </el-table-column>
                        <el-table-column prop="maxQty" label="剩余数量" width="100">
                        </el-table-column>
                        <el-table-column prop="thisTrueQty" label="当前数量" width="100">
                        </el-table-column>
                        <el-table-column prop="oldThisPlanQty" label="变更数量" width="100">
                        </el-table-column>
                        <el-table-column prop="isUpdate" label="是否变更" width="100">
                            <template slot-scope="scope">
                                <el-tag type="info" v-if="scope.row.oldThisPlanQty == scope.row.thisTrueQty">否</el-tag>
                                <el-tag type="success" v-else>是</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderQty" label="已下单数量" width="120">
                        </el-table-column>
                        <el-table-column v-if="addPlanForm.state != 2 && addPlanForm.state != 4" prop="thisPlanQty" label="更改变更数量" width="160">
                            <template slot-scope="scope">
                                <el-input-number size="mini" v-model="scope.row.thisPlanQty"
                                                 :min="scope.row.orderQty == 0?1:scope.row.orderQty" :precision="4" :step="0.1" :max="scope.row.thisTrueQty + scope.row.maxQty" @change="changeSelectQtyM(scope.row)">
                                </el-input-number>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane><el-tab-pane label="审核历史" name="auditInfo">
                <el-table
                    max-height="372px"
                    border
                    :data="addPlanForm.auditList"
                    style="min-height: 372px"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="auditType" label="审核类型" width="160">
                        <template slot-scope="scope">
                            <span v-if="scope.row.auditType == 1">录入审核</span>
                            <span v-if="scope.row.auditType == 2">变更审核</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="founderName" label="审核人" width="200">
                    </el-table-column>
                    <el-table-column prop="gmtCreate" label="审核时间" width="160">
                    </el-table-column>
                    <el-table-column prop="auditResult" label="审核意见" width="">
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            </el-tabs>
            <el-button style="margin-left: 90%;margin-top: 20px" @click="$router.push({path: '/user/monthPlan'})">返回</el-button>
        </div>
    </div>
</template>
<script>
import {
    updatePlanChangeDtlByPlanId,
    getPlanChangeDtlInfoByPlanNo,
    auditChangePlan,
    cancellationChangePlan
} from '@/api/plan/plan'
import { mapState } from 'vuex'
export default {
    data () {
        return {
            changePlanDtlRowDate: [],
            activeName: 'dtl',
            addPlanFormRote: {
                planNo: [
                    { required: true, message: '请输入计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                contractNo: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                planDate: [
                    { required: true, message: '请选择计划日期', trigger: 'blur' },
                ],
                supplierName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            showLoading: false,
            addPlanForm: {},
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
        }
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        cancellationPlanM () {
            this.clientPop('info', '您确定要废进行作废操作吗？', async () => {
                this.showLoading = true
                cancellationChangePlan([this.addPlanForm.planChangeId]).then(res => {
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getPlanDtlM()
                    }
                    this.showLoading = false
                }).catch(() => {
                    this.showLoading = false
                })
            })
        },
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗！', async () => {
                if(state == 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            planChangeId: this.addPlanForm.planChangeId,
                            isOpen: 0,
                            auditResult: value,
                        }
                        auditChangePlan(params).then(res => {
                            if(res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.getPlanDtlM()
                            }
                        })
                    }).catch(() => {
                    })
                }else {
                    let params = {
                        planChangeId: this.addPlanForm.planChangeId,
                        isOpen: 1,
                    }
                    auditChangePlan(params).then(res => {
                        if(res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.getPlanDtlM()
                        }
                    })
                }
            })
        },
        // 变更数量
        changeSelectQtyM (row) {
            if(row.thisPlanQty == null) {
                row.thisPlanQty = row.orderQty
            }
            if (this.changePlanDtlRowDate.length == 0) {
                this.changePlanDtlRowDate.push({
                    planDtlChangeId: row.planDtlChangeId,
                    thisPlanQty: row.thisPlanQty,
                })
                return
            }
            let flag = false
            this.changePlanDtlRowDate.forEach(t => {
                if (t.planDtlChangeId == row.planDtlChangeId) {
                    t.thisPlanQty = row.thisPlanQty
                    flag = true
                }
            })
            if (!flag) {
                this.changePlanDtlRowDate.push({
                    planDtlChangeId: row.planDtlChangeId,
                    thisPlanQty: row.thisPlanQty
                })
            }
        },
        getPlanDtlM () {
            this.showLoading = true
            // 如果是计划编号
            getPlanChangeDtlInfoByPlanNo({ planChangeNo: this.$route.query.planChangeNo }).then(res => {
                this.addPlanForm = res
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },

        // 标签点击暂时无用
        // eslint-disable-next-line no-unused-vars
        handleClick (tab, event) {
        },
        savePlanM (isSubmit) {
            this.$refs.addPlanFormRoteRef.validate(valid => {
                if(valid) {
                    // 保存并提交
                    let params = {
                        planChangeId: this.addPlanForm.planChangeId,
                        planDate: this.addPlanForm.planDate,
                        remarks: this.addPlanForm.remarks,
                        dtls: this.changePlanDtlRowDate,
                    }
                    if(isSubmit != null && isSubmit == 1) {
                        params.isSubmit = 1
                    }else {
                        params.isSubmit = 0
                    }
                    this.showLoading = true
                    updatePlanChangeDtlByPlanId(params).then(res => {
                        if(res.code != 200) {
                            this.clientPop('error', res.message, () => {
                                // 查询计划
                                this.getPlanDtlM()
                            })
                        }else {
                            this.clientPop('suc', '操作成功', () => {
                                // 查询计划
                                this.getPlanDtlM()
                            })
                        }
                        this.showLoading = false
                    }).catch(() => {
                        this.showLoading = false
                    })
                }else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
    },
    created () {
        this.getPlanDtlM()
    }
}
</script>
<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);

.root {
    height: 100%;
    padding-top: 20px;
    background-color: #f5f5f5;

    .box {
        padding: 20px;
        width: 1326px;
        background-color: #fff;
    }
}
.list-title {
    padding: 0;
    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}
.list {
    .item {
        height: 52px;
        padding: 0 20px;
        font-size: 14px;
        border: $border;
        color: rgba(102, 102, 102, 1);
        position: relative;

        & > div:not(.pointer) {
            margin-right: 60px;
        }

        .pointer {
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 20px;
        }

        span {
            color: rgba(51, 51, 51, 1);
        }
    }
}/deep/ .el-dialog {
     .el-dialog__header {
         height: 10px;
         padding: 0px;
     }
     .el-dialog__body {
         overflow-y: hidden !important;
     }
 }

.el-form-item {
    display: flex;
    align-items: flex-start;
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}

.addr-info {
    //width: 1226px;
    width: 100%;
    background-color: #fff;
    .list-title {
        margin-top: 20px;
        padding-left: 33px;
        position: relative;
        & span:last-child {
            padding-left: 123px;
            color: rgba(33, 110, 198, 1);
            cursor: pointer;
        }
        &::before {
            position: absolute;
            left: 20px;
        }
    }
    .addr-content {
        padding-top: 20px;
        p {
            //margin-left: 20px;
            font-size: 14px;
            color: rgba(51, 51, 51, 1);
        }
    }
}
.add {
    width: 80px;
    height: 30px;
    margin: 18px 0 40px 0;
    line-height: 30px;
    text-align: center;
    color: rgba(33, 110, 198, 1);
    border: 1px solid rgba(33, 110, 198, 1);
}
.butSub {
    width: 80px;
    height: 40px;
    font-size: 16px;
    color: #fff;
    background-color: #216EC6;
    margin-left: 100px;
}
</style>