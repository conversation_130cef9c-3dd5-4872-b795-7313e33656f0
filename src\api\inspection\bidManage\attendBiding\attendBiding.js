import service from '@/utils/request'

const { httpPost, httpGet } = service
// const invoiceList = params => {
//     return httpPost({
//         url: '/materialMall/supplierSys/invoice/listByEntity',
//         params
//     })
// }
// const changInvoiceState = params => {
//     return httpPost({
//         url: '/materialMall/supplierSys/invoice/updateInvoiceState',
//         params
//     })
// }
// const getTenderNoticeList = params => {
//     return httpPost({
//         url: '/tender/tender/listByEntity',
//         params,
//     })
// }
const attendTenderList = params => {
    return httpPost({
        url: '/tender/inspection/tender/attendBidList',
        params,
    })
}

// const getTenderEnrollList = params => {
//     return httpPost({
//         url: '/tender/tender/listByEntity',
//         params,
//     })
// }
const getPackageListByBillId = params => {
    return httpPost({
        url: '/tender/inspection/tenderPackage/listByEntity',
        params,
    })
}
const getAttendPackageList = params => {
    return httpPost({
        url: '/tender/inspection/tenderEnrollPackage/packageList',
        params,
    })
}
const getTenderPackageSubcontractorList = params => {
    return httpGet({
        url: '/tender/inspection/tenderPackage/TeBerSuByBillId',
        params,
    })
}
const getTenderPackagedtlList = params => {
    return httpPost({
        url: '/tender/inspection/tenderDtl/getBathByPackageIds',
        params,
    })
}
// const getTenderNotis = params => {
//     return httpPost({
//         url: '/tender/tenderDtl/getBathByPackageIds',
//         params,
//     })
// }

export {
    // getTenderNoticeList,
    getPackageListByBillId,
    getTenderPackageSubcontractorList,
    getTenderPackagedtlList,
    // getTenderEnrollList,
    attendTenderList,
    getAttendPackageList,

}