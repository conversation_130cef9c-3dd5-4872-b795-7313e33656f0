module.exports = {
    root: true,
    env: {
        node: true
    },
    extends: ['plugin:vue/essential', 'eslint:recommended', '@vue/prettier'],
    parserOptions: {
        parser: 'babel-eslint'
    },
    globals: {
        CefSharp: true,
        callbackObj: true,
        Vue: true,
        Vuex: true,
        VueRouter: true,
        ELEMENT: true,
        $: true
    },
    rules: {
        "no-debugger": 2,
        'template-curly-spacing': 'off',
        'prettier/prettier': 0,
        'no-trailing-spaces': 2,
        'space-before-function-paren': [2, 'always'],
        'space-before-blocks': 2,
        'space-unary-ops': [2, { words: true, nonwords: false }],
        'key-spacing': [2, { beforeColon: false, afterColon: true }],
        'object-curly-spacing': [2, 'always'],
        'comma-spacing': 2,
        'generator-star-spacing': 2,
        'space-infix-ops': 2,
        indent: ['error', 4,
            {
                ignoredNodes: ['TemplateLiteral']
            }
        ],
        quotes: [2, 'single', 'avoid-escape'],
        semi: ['error', 'never'],
        'no-multiple-empty-lines': [1, { max: 1 }],
        'arrow-parens': ['error', 'as-needed']
    }
}
