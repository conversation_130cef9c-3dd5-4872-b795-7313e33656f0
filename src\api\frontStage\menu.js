import service from '@/utils/request'
import qs from 'qs'

const { httpPost, httpGet } = service

const login = params => {
    return httpPost({
        url: '/identity/auth/signin',
        params: qs.stringify(params),
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}

const getUserMenu = params => {
    return httpGet({
        url: '/permission/menu/getSpeedDial',
        params
    })
}

export {
    login,
    getUserMenu
}