<template>
    <div class="e-form">
        <BillTop @cancel="handleClose" />
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="showLoading">
            <el-tabs :style="{ height: tabsContentHeight + 'px' }" tab-position="left" v-model="tabsName"
                @tab-click="onChangeTab">
                <el-tab-pane label="订单信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="订单商品" name="productInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfo" class="con">
                        <div class="tabs-title" id="baseInfo">订单信息</div>
                        <div class="form">
                            <el-form :model="orderInfo" label-width="200px" ref="rulesBase">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单编号：">
                                            <span>{{ orderInfo.orderSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商信息：">
                                            <span>{{ orderInfo.supplierName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="商品信息：">
                                            <span>{{ orderInfo.untitled }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收货单位名称：">
                                            <span>{{ orderInfo.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单状态：">
                                            <el-tag v-if="orderInfo.state == 0">草稿</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 1">已提交</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 2">待确认</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 3">已确认</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 4">待签订合</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 5">已签合同</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 6">待发货</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 7">已关闭</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 8">发货中</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 9">待收货</el-tag>
                                            <el-tag v-else-if="orderInfo.state == 10">已完成</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="商品类型：">
                                            <el-tag>大宗材料</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="合同编号：">
                                            <span>{{ orderInfo.billNo }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收货人：">
                                            <span>{{ orderInfo.receiverName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="收货人手机号：">
                                            <span>{{ orderInfo.receiverMobile }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收货人地址：">
                                            <span>{{ orderInfo.receiverAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发货时间：">
                                            <span>{{ orderInfo.deliveryTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="订单完成时间：">
                                            <span>{{ orderInfo.successDate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="确认时间：">
                                            <span>{{ orderInfo.flishTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="创建时间：">
                                            <span>{{ orderInfo.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="备注：">
                                            <span>{{ orderInfo.orderRemark }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <div class="df" style="justify-content: flex-end;">
                                    <el-button type="primary" class="btn-greenYellow" v-if="orderInfo.state == 0"
                                        @click="checkedAddressM">
                                        切换收货地址
                                    </el-button>
                                </div>
                            </el-form>
                        </div>
                    </div>
                    <!-- 订单商品-->
                    <div id="productInfo" class="con">
                        <div class="tabs-title" id="contractList">订单商品</div>
                        <div class="e-table" style="background-color: #fff">
                            <!--                            <div class="top" style="height: 50px; padding-left: 10px">-->
                            <!--                                <div class="left">-->
                            <!--                                    <el-input type="text" @blur="getTableData" placeholder="输入搜索关键字"-->
                            <!--                                              v-model="keywords">-->
                            <!--                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData"/>-->
                            <!--                                    </el-input>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <el-table ref="tableRef" border :data="orderInfo.orderProduct" class="table">
                                <el-table-column label="商品名称" label-width="560" prop="productName" />
                                <el-table-column label="规格" label-width="152" prop="skuName"/>
                                <el-table-column label="计量单位" label-width="110" prop="unit"/>
                                <el-table-column label="数量" prop="buyCounts">
                                    <template v-slot="scope">
                                        <el-input-number v-if="orderInfo.state == 6" size="mini" :disabled="disabledElement"
                                            v-model="scope.row.buyCounts" :min="0" :precision="4" :step="0.1" max="999999"
                                            @change="changeSelectQtyM(scope.row)"></el-input-number>
                                        <span v-else>{{ scope.row.buyCounts }}</span>
                                    </template>
                                </el-table-column>
                                <!--                                <el-table-column prop="remarks" label="备注" width="200"></el-table-column>-->
                            </el-table>
                        </div>
                        <!--            分页-->
                        <!--                        <Pagination-->
                        <!--                            v-show="tableData != null || tableData.length != 0"-->
                        <!--                            :total="paginationInfo.total"-->
                        <!--                            :pageSize.sync="paginationInfo.pageSize"-->
                        <!--                            :currentPage.sync="paginationInfo.currentPage"-->
                        <!--                            @currentChange="getTableData"-->
                        <!--                            @sizeChange="getTableData"-->
                        <!--                        />-->
                    </div>

                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button v-if="orderInfo.state >= 8 && orderInfo.state < 10" :disabled="disabledElement" class="btn-blue" @click="orderCloseClick">完结订单</el-button>
            <el-button v-if="orderInfo.state == 6" :disabled="disabledElement" type="primary" size="mini" class="btn-delete"
                @click="deleteOrderInfo">删除订单</el-button>
            <el-button type="primary" @click="saveQty" v-if="orderInfo.state == 6" :disabled="disabledElement">保存</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import {
    orderCloseClick
} from '@/api/platform/order/orders'
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import { deleteYGOrderByOrderId, getOrderDetail, updateDZYGOrderItemQty, updateOrderInfo } from '@/api/frontStage/order'
import { create, getList } from '@/api/frontStage/shippingAddr'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import { UserPermission } from '@/utils/permissions'

export default {
    data () {
        return {
            clickTabFlag: false,
            tabsName: 'baseInfo',
            addressLoading: false,
            userAddressFormRules: {
                receiverName: [
                    { required: true, message: '请输入收件人', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                receiverMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
            },
            // 地址
            addressData: regionData, // 地址数据
            addDetailDialog: false,
            selectAddressOptions: [],
            userAddressForm: { // 新增编辑地址表单
                detailAddress: null,
            },
            receiver: {},
            submitOrderLoading: false,
            addrList: [],
            changeSelectQtyRowDate: [],
            addrDialogVisible: false,
            showLoading: false,
            orderInfo: {},
            spanArr: [],
            pos: null,
            winEvent: {},
            topHeight: 70,
            screenHeight: 0,
            // 数据权限
            userPermission: new UserPermission,
            disabledElement: false
        }
    },
    computed: {
        tabsContentHeight () {
            return this.screenHeight - 128
        },
    },
    mounted () {
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) return
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    return $item ? $item.offsetTop : null
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => document.getElementById(item).offsetTop)
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                let name = this.$route.query.name
                this.onChangeTab({ name })
                this.tabsName = name
            }, 200)
        }
    },
    methods: {
        saveQty () {
            let newDtlArr = this.changeSelectQtyRowDate.filter(t => {
                return t.qty != 0
            })
            if (newDtlArr.length === 0) {
                return this.$message.error('未修改订单数量！')
            }
            this.clientPop('info', '您确认要修改订单数量吗？', () => {
                this.showLoading = true
                updateDZYGOrderItemQty(newDtlArr).then(res => {
                    if (res.code === 200) {
                        this.clientPop('suc', res.message, () => {
                            this.changeSelectQtyRowDate = []
                            this.getOrderDetailM()
                        })
                    }else if (res.code != null && res.code != 200) {
                        this.clientPop('warn', res.message, () => {})
                    }
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        changeSelectQtyM (row) {
            if (row.buyCounts == null) {
                row.buyCounts = 0
            }
            let order = { orderItemId: row.orderItemId, qty: row.buyCounts }
            if (this.changeSelectQtyRowDate.length === 0) {
                this.changeSelectQtyRowDate.push(order)
                return
            }
            let flag = false
            this.changeSelectQtyRowDate.forEach(t => {
                if (t.orderItemId === row.orderItemId) {
                    t.qty = row.buyCounts
                    flag = true
                }
            })
            if (!flag) {
                this.changeSelectQtyRowDate.push(order)
            }
        },
        orderCloseClick () {
            this.clientPop('info', '您确定要完结订单吗？未执行数量将还回计划，不能再发货操作！', async () => {
                this.showLoading = true
                orderCloseClick({ orderId: this.orderInfo.orderId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getOrderDetailM()
                    }
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder')
        },
        // 地址选择
        handleAddressChange () {
            let province = CodeToText[this.selectAddressOptions[0]]
            let city = CodeToText[this.selectAddressOptions[1]]
            let county = CodeToText[this.selectAddressOptions[2]]
            this.userAddressForm.province = province
            this.userAddressForm.city = city
            this.userAddressForm.county = county
            this.userAddressForm.detailAddress = province + city + county
        },
        // 创建编辑地址统一接口
        createAddressM () {
            this.$refs.addAddressRef.validate(valid => {
                if (!valid) return
                create(this.userAddressForm).then(res => {
                    if (res.code === 200) {
                        this.$message.success(res.message)
                        this.getAddress()
                        this.addDetailDialog = false
                    }
                })
            })
        },
        deleteOrderInfo () {
            this.clientPop('info', '您确认要删除订单吗？', async () => {
                this.showLoading = true
                deleteYGOrderByOrderId({ orderId: this.orderInfo.orderId }).then(res => {
                    if (res.code === 200) {
                        this.clientPop('suc', '操作成功！', () => {
                            this.$router.go(-1)
                        })
                    }
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        // 创建
        createAddress () {
            this.userAddressForm = { detailAddress: null }
            this.selectAddressOptions = []
            this.addDetailDialog = true
        },
        // 编辑地址
        handleEditAddr (row) {
            this.userAddressForm = {
                addressId: row.addressId,
                detailAddress: row.addr,
                receiverName: row.name,
                receiverMobile: row.tel,
            }
            //地址选择器回显
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
            this.addDetailDialog = true
        },
        // 地址表格点击
        handleCurrentInventoryClick (row) {
            let params = {
                orderId: this.orderInfo.orderId,
                receiverName: row.name,
                receiverMobile: row.tel,
                receiverAddress: row.addr,
            }
            this.addressLoading = true
            updateOrderInfo(params).then(res => {
                if (res.code === 200) {
                    this.$message.success('修改成功！')
                    this.addrDialogVisible = false
                    this.getOrderDetailM()
                }
            }).finally(() => {
                this.addressLoading = false
            })
        },
        // 获取地址
        getAddress () {
            // 获取收货地址
            this.addressLoading = true
            getList({ page: 1, limit: 30 }).then(res => {
                if (!res.list[0]) return
                let address = []
                // 显示默认地址
                res.list.forEach(item => {
                    let obj = {
                        addressId: item.addressId,
                        checked: false,
                        addr: item.detailAddress,
                        name: item.receiverName,
                        tel: item.receiverMobile,
                        province: item.province,
                        city: item.city,
                        county: item.county,
                    }
                    address.push(obj)
                })
                this.addrList = address
            }).finally(() => {
                this.addressLoading = false
            })
        },
        // 切换地址
        checkedAddressM () {
            this.getAddress()
            this.addrDialogVisible = true

        },
        getOrderDetailM () {
            this.showLoading = true
            getOrderDetail({ orderSn: this.$route.query.orderSn }).then(res => {
                // 数据权限控制
                if (!this.userPermission.isSameOrgByEnterpriseId(res.enterpriseId) && this.showDevFunc) {
                    this.disabledElement = true
                }
                this.orderInfo = res
            }).finally(() => {
                this.showLoading = false
            })
        },
        handleConfirm () {
            this.$router.go(-1)
        },
    },
    created () {
        this.getOrderDetailM()
    }
}
</script>

<style lang="scss" scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    overflow: auto !important;

    &::-webkit-scrollbar {
        width: 0;
    }
}
</style>
