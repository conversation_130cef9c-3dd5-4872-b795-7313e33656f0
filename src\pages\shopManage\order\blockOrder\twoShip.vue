<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-select @change="stateTopOptionsClick" v-model="selectOptionValue"
                                       placeholder="请选择状态">
                                <el-option v-for="item in stateOptions" :key="item.value" :label="item.label"
                                           :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按发货时间排序</el-radio>
                        <el-input clearable style="width: 300px" type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table
                    ref="tableRef"
                    border
                    style="width: 100%"
                    :data="tableData"
                    class="table"
                    v-loading="tableLoading"
                    :height="rightTableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="billSn" label="操作" width="350px">
                        <template slot-scope="scope">
                            <span class="action" @click="createQrcode(scope.row)">生成二维码</span>|
                            <span class="action" @click="outputAll(scope.row.billId)">发货单导出</span>
                            <span v-show="scope.row.type==2">|  </span>
                            <span v-show="scope.row.type===2" class="action"  @click="materialShipExportM(scope.row.billId)">实物收料单导出</span>

                        </template>
                    </el-table-column>
                    <el-table-column prop="billSn" label="发货单编号" width="250px">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.billSn }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderSn" label="订单号" width="250px"></el-table-column>
                    <el-table-column prop="deliveryFlowId" label="物流单号" width="150px">
<!--                        <template slot-scope="scope">-->
<!--                            <el-input v-model="scope.row.deliveryFlowId"></el-input>-->
<!--                        </template>-->
                    </el-table-column>
                    <el-table-column prop="logisticsCompany" label="物流公司" width="150px" p>
<!--                        <template slot-scope="scope">-->
<!--                            <el-input v-model="scope.row.logisticsCompany"></el-input>-->
<!--                        </template>-->
                    </el-table-column>
                    <el-table-column prop="contactUser" label="运输联系人" width="150px"></el-table-column>
                    <el-table-column prop="contactPhone" label="联系电话" width="150px"></el-table-column>
                    <el-table-column prop="rateAmount" label="总价格" width=""></el-table-column>
                    <el-table-column prop="type" label="发货单状态" width="">
                        <template slot-scope="scope">
                           <el-tag v-if="scope.row.type == '0'" type="info">未发货</el-tag>
                            <el-tag v-if="scope.row.type == '1'" >发货中</el-tag>
                            <el-tag v-if="scope.row.type == '2'" type="success">已收货</el-tag>
                        </template>
                    </el-table-column>
                  <el-table-column prop="receiveStatus" label="PCWP现场收料状态" width="150px">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.receiveStatus == '0'">待收货</el-tag>
                      <el-tag v-if="scope.row.receiveStatus == '1'" type="success" >已收货</el-tag>
                      <el-tag v-else type="info">未知</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="receiveNo" label="收料编号" width="150px"></el-table-column>
                  <el-table-column prop="receiveAuditName" label="收料审核人" width="150px"></el-table-column>
                  <el-table-column prop="receiveAuditIs" label="审核状态" width="150px">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.receiveAuditIs == '0'" type="success">已审核</el-tag>
                      <el-tag v-if="scope.row.receiveAuditIs == '1'" type="error" >未审核</el-tag>
                      <el-tag v-else type="info">未知</el-tag>
                    </template>
                  </el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间" width="180px"></el-table-column>
                    <el-table-column prop="shipData" label="发货时间" width="180px"></el-table-column>
                    <el-table-column prop="confirmTime" label="收货时间" width="180px"></el-table-column>
                </el-table>
            </div>
            <!--分页-->
            <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--高级查询-->
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%" :close-on-click-modal="false">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="发货单状态：">
                            <el-select v-model="filterData.type" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.shipType"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单号：">
                            <el-input clearable maxlength="100" placeholder="请输入订单号" v-model="filterData.orderSn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="采购公司：">
                            <el-input clearable maxlength="100" placeholder="采购公司名称" v-model="filterData.receiveOrgName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单提交时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.okDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="发货时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.deliverGoodsDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="总金额以上：">
                            <el-input type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间"
                                      style="width: 200px"></el-input>
                        </el-form-item>
                        <el-form-item label="总金额以下：">
                            <el-input type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间"
                                      style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">确定</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog title="扫码二维码" class="qrcodeDialog" :visible.sync="qrcodeVisible" width="420px"
                   :before-close="beforeQrcodeClose">
            <div ref="qrcode" id="qrcode" class="center" style="width: 200px"></div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="() => { beforeQrcodeClose(); qrcodeVisible = false }">取 消</el-button>
                <el-button type="primary"
                           @click="() => { beforeQrcodeClose(); qrcodeVisible = false }">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapState } from 'vuex'
import { orderTwoShipList } from '@/api/platform/order/orders'
import { updateShipType, del, exportDataTwoById, materialShipExport } from '@/api/platform/order/ship'
import QRCode from 'qrcodejs2'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292 + 'px'
        },
    },
    data () {
        return {
            stateOptions: [
                { label: '全部', value: null },
                { label: '未发货', value: 0 },
                { label: '发货中', value: 1 },
                { label: '已完成', value: 2 }
            ],
            qrcodeVisible: false,
            changedRow: [],
            tableLoading: false,
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            stateOptionTitle: '', // 选中的状态标题
            // 表格数据
            tableStateTitle: null, // 表格的状态
            dataListSelections: [], //选中的数据
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                billId: '',
                billSn: '',
                orderSn: '',
                receiveOrgName: '',
                type: null,
                belowPrice: '', // 总价格
                abovePrice: '', // 总价格
                deliverGoodsDate: [], // 下单时间
                dateValue: [], // 下单时间
                okDate: [], // 发货单创建时间
                selectSateValue: null,  // 选中的值
                shipType: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: '0',
                        label: '未发货'
                    }
                    , {
                        value: '1',
                        label: '发货中'
                    }
                    , {
                        value: '2',
                        label: '已确认'
                    }
                    , {
                        value: '3',
                        label: '退货'
                    }, {
                        value: '4',
                        label: '已完成'
                    }],
                productTypeSelect: [
                    {
                        value: null,
                        label: '全部'
                    },
                ],
                orderBy: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    methods: {
        materialShipExportM (billId) {
            this.tableLoading = true
            materialShipExport({ id: billId }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '实物收料单.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                this.currentQuery.ids = []
                this.dataListSelections = []
                this.$message.success('操作成功')
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        outputAll (billId) {
            exportDataTwoById({ id: billId }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '发货单.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                this.currentQuery.ids = []
                this.dataListSelections = []
                this.$message.success('操作成功')
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        del (row) {
            console.log(row.type === 0, row.type, 22221)
            if (row.type === 0) {
                del({ id: row.billId }).then(res => {
                    if (res.code == 200) {
                        this.$message({
                            type: 'success',
                            message: '删除' + row.billId + '发货单成功'
                        })
                        this.getTableData()
                    } else {
                        this.$message({
                            type: 'error',
                            message: '删除' + row.billId + '发货单失败'
                        })
                    }
                })
            } else {
                this.$message({
                    type: 'error',
                    message: row.billId + '发货单已发货或者确认，不能删除'
                })
            }
        },
        beforeQrcodeClose () {
            this.$refs.qrcode.innerHTML = ''
            this.qrcodeVisible = false
        },
        createQrcode ({ billId }) {
            this.qrcodeVisible = true
            setTimeout(() => {
                let div = this.$refs.qrcode
                return new QRCode(div, {
                    text: billId, //二维码内容字符串
                    width: 180, //图像宽度
                    height: 180, //图像高度
                    colorDark: '#000000', //二维码前景色
                    colorLight: '#ffffff', //二维码背景色
                    correctLevel: QRCode.CorrectLevel.H, //容错级别
                })
            }, 100)
        },
        updateShipTypeM (row) {
            if (row.type == '0') {
                let params = {
                    billId: row.billId,
                    type: '1',
                    deliveryFlowId: row.deliveryFlowId

                }
                if((row.deliveryFlowId == null || row.deliveryFlowId == '') && (row.logisticsCompany == null || row.logisticsCompany == '')) {
                    return this.$message({
                        type: 'warning',
                        message: '请输入物流单号和物流公司'
                    })
                }else {
                    params.deliveryFlowId = row.deliveryFlowId
                    params.logisticsCompany = row.logisticsCompany
                }
                if (row.deliveryFlowId == null || row.deliveryFlowId == '') {
                    this.$message({
                        type: 'warning',
                        message: '请输入物流单号'
                    })
                    return
                } else {
                    params.deliveryFlowId = row.deliveryFlowId
                }
                if (row.logisticsCompany == null || row.logisticsCompany == '') {
                    this.$message({
                        type: 'warning',
                        message: '请输入物流公司'
                    })
                    return
                } else {
                    params.logisticsCompany = row.logisticsCompany
                }
                updateShipType(params).then(res => {
                    if (res.code == 200) {
                        this.$message({
                            type: 'success',
                            message: '编号为' + row.billId + '发货成功'
                        })
                        this.getTableData()
                    }

                })

            } else {
                this.$message({
                    type: 'warning',
                    message: '该发货单已发货，不能重复发货'
                })
                return
            }
        },
        getChangedRow (row) {
            if (this.changedRow.length == 0) {
                this.changedRow.push({
                    orderId: row.orderId,
                    deliveryFlowId: row.deliveryFlowId,
                    logisticsCompany: row.logisticsCompany
                })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.orderId == row.orderId) {
                    t.deliveryFlowId = row.deliveryFlowId
                    t.logisticsCompany = row.logisticsCompany
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push({
                    orderId: row.orderId,
                    deliveryFlowId: row.deliveryFlowId,
                    logisticsCompany: row.logisticsCompany
                })
            }
        },
        // 选中状态进行查询
        stateTopOptionsClick (value) {
            this.expertFIfter()
            this.selectOptionValue = value
            this.getTableData()
        },
        // 详情
        handleView (row) {
            //利用$router.push进行跳转
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '//supplierSys/order/shipped/blockOrderDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shippedShopManageBlockOrderDetail',
                params: {
                    row: row
                }
            })
        },
        resetSearchConditions () {
            this.filterData.belowPrice = '' // 以下价格
            this.filterData.abovePrice = '' // 以上价格
            this.filterData.type = null //发货单类型
            this.filterData.receiveOrgName = '' //公司名称
            this.filterData.orderSn = ''  //订单编号
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.okDate = []  //下单时间
            this.filterData.deliverGoodsDate = []  //发货时间
            this.filterData.selectSateValue = null // 选中的值
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.selectOptionValue = null
            this.stateOptionTitle = ''
            this.getTableData()
            this.queryVisible = false
        },
        expertFIfter () {
            //重置数据
            this.filterData.belowPrice = null // 以下价格
            this.filterData.abovePrice = null // 以上价格
            this.filterData.type = null //发货单类型
            this.filterData.receiveOrgName = null //公司名称
            this.filterData.orderSn = null  //订单编号
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.okDate = []  //下单时间
            this.filterData.deliverGoodsDate = []  //发货时间
            this.filterData.selectSateValue = null // 选中的值
            this.queryVisible = false

        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: 13,
                orderClass: 3
            }
            if (this.selectOptionValue != null) {
                params.type = this.selectOptionValue
            }

            if (this.filterData.type != null) {
                params.type = this.filterData.type
            }
            if (this.filterData.orderId != null) {
                params.orderId = this.filterData.orderId
            }
            if (this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if (this.filterData.receiveOrgName != null) {
                params.receiveOrgName = this.filterData.receiveOrgName
            }
            if (this.filterData.dateValue != null) {

                params.startGmtCreate = this.filterData.dateValue[0]
                params.endGmtCreate = this.filterData.dateValue[1]
            }
            if (this.filterData.okDate != null) {

                params.staConfirmTime = this.filterData.okDate[0]
                params.endConfirmTime = this.filterData.okDate[1]
            }
            if (this.filterData.deliverGoodsDate != null) {

                params.staShipTime = this.filterData.deliverGoodsDate[0]
                params.endShipTime = this.filterData.deliverGoodsDate[1]
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }

            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            orderTwoShipList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            if (res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            } else {
                this.$message({
                    message: res.message,
                    type: 'error'
                })
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}
</style>
