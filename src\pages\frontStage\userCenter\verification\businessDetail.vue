<template>
    <main>
        <div class="list-title mb20 df">{{ title }}
            <div class="edit" @click="handleEdit"><img src="@/assets/images/userCenter/ico_edit.png" alt="">修改</div>
        </div>
        <div class="content p20" v-loading="formLoading">
            <el-form :model="businessForm" label-width="144px" :inline="false">
              <div class="admin">企业信息</div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="企业名称：">
                            <span>{{ businessForm.enterpriseName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="统一社会信用代码：">
                            <span>{{ businessForm.socialCreditCode }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="法定代表人：">
                            <span>{{ businessForm.legalPersonName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="注册资本：">
                            <span>{{ businessForm.registeredCapital }}(万元)</span>
                        </el-form-item>
                    </el-col>
                </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="供方类型：">
                    <span>{{ businessForm.supplierType === '1' ? '生产商' : '贸易商' }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="纳税人类别：">
                    <span>{{ businessForm.taxpayerType === '1' ? '一般纳税人' : '小规模纳税人' }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="注册地址：">
                    <span>{{ businessForm.detailedAddress }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="注册日期：">
                    <span>{{ businessForm.creationTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="固定地址：">
                            <span>{{ businessForm.workXxdz }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="主营业务：">
                            <span>{{ businessForm.mainBusiness }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item class="license" label="营业执照：">
                            <img
                                :src="businessForm.businessLicense ? businessForm.businessLicense : require('@/assets/images/userCenter/yyzz_demo.png')"
                                alt="">
                        </el-form-item>
                    </el-col>
                </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="主要业绩：">
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <div class="custom-table">
                  <el-table
                    :data="businessForm.epLists"
                    stripe
                    border
                    style="width: 100%">
                    <!-- 项目名称列 -->
                    <el-table-column prop="projectName" label="项目名称" width="200px" align="center" show-overflow-tooltip>
                    </el-table-column>
                    <!-- 供应物资品类 -->
                    <el-table-column prop="supplyCategory" label="供应物资品类" width="200px" align="center" show-overflow-tooltip>
                    </el-table-column>
                    <!-- 合同金额（万元） -->
                    <el-table-column prop="contractAmount" label="合同金额（万元）" width="120px" align="center" show-overflow-tooltip>
                    </el-table-column>
                    <!-- 供货起止时间 -->
                    <el-table-column prop="ghdate" label="供货起止时间" width="400px" align="center" show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-date-picker
                          v-model="scope.row.ghdate"
                          type="daterange"
                          :value-format="dateFormat"
                          range-separator="至"
                          :disabled="true"
                          start-placeholder="请选择"
                          end-placeholder="请选择"  style="width: 100%;">
                        </el-date-picker>
                      </template>
                    </el-table-column>
                    <!-- 业绩证明人 -->
                    <el-table-column prop="proofPerson" label="业绩证明人" width="100px" align="center" scope.row.ghdate>
                    </el-table-column>
                    <!-- 证明人联系电话 -->
                    <el-table-column prop="proofPhone" label="证明人联系电话" width="200px" align="center" scope.row.ghdate>
                    </el-table-column>
                  </el-table>
                </div>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="质量认证：">
                    <div >
                      <el-checkbox-group v-model="businessForm.certificate" style="display: flex">
                        <el-checkbox label="ISO9001质量体系认证" value="1" :disabled="true"></el-checkbox>
                        <el-checkbox label="铁路产品CRCC认证" value="2" :disabled="true"></el-checkbox>
                        <el-checkbox label="交通产品CCPC认证" value="3" :disabled="true"></el-checkbox>
                        <el-checkbox label="CCC认证" value="4" :disabled="true"></el-checkbox>
                        <el-checkbox label="其他质量认证" value="5" :disabled="true"></el-checkbox>
                      </el-checkbox-group>
                      <textarea :disabled="true" v-model="businessForm.certificateOther" v-show="businessForm.certificate.includes('其他质量认证')" placeholder="请填写其他质量认证" style="width: 900px;height: 100px;resize:none;"></textarea>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--企业基本情况-->
              <el-row>
                <el-col :span="24">
                  <el-form-item label="企业基本情况：">
                    <div>
                      <div style="font-size: 20px;">企业概况</div>
                      <textarea :disabled="true" style="width: 900px;height: 70px;resize:none;" v-model="businessForm.companyProfile"></textarea>
                    </div>
                    <div>
                      <div style="font-size: 20px">财务情况</div>
                      <textarea :disabled="true" style="width: 900px;height: 70px;resize:none;" v-model="businessForm.financialSituation"></textarea>
                    </div>
                    <div>
                      <div style="font-size: 20px">诉讼情况</div>
                      <textarea :disabled="true" style="width: 900px;height: 70px;resize:none;" v-model="businessForm.litigationSituation"></textarea>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="separ"></div>
              <div class="admin">对公账户信息</div>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="开户银行：">
                    <span>{{ businessForm.bankName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="银行户名称：">
                    <span>{{ businessForm.accountName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="银行账号：">
                    <span>{{ businessForm.bankAccount }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="开票备注：">
                    <span>{{ businessForm.invoiceRemark }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="separ"></div>
              <div class="admin">法人信息</div>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="姓名：">
                    <span>{{ businessForm.legalPersonName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证号码：">
                    <span>{{ businessForm.legalPersonNum }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item class="license" label="身份证人像面：">
                    <img
                      :src="businessForm.legalPersonFace ? businessForm.legalPersonFace : require('@/assets/images/userCenter/yyzz_demo.png')"
                      alt="">
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item class="license" label="身份证国徽面：">
                    <img
                      :src="businessForm.legalPersonNational ? businessForm.legalPersonNational : require('@/assets/images/userCenter/yyzz_demo.png')"
                      alt="">
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="有效期开始日期：">
                    <span>{{ businessForm.lpStartTime }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="有效期结束日期：">
                    <span>{{ businessForm.lpEndTime === null ? '长期' : businessForm.lpEndTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
                <div class="separ"></div>
                <div class="admin">管理员信息</div>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="姓名：">
                    <span>{{ businessForm.adminName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证号码：">
                    <span>{{ businessForm.adminNumber }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="手机号码：">
                            <span>{{ businessForm.adminPhone }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item class="license" label="身份证人像面：">
                    <img
                      :src="businessForm.cardPortraitFace ? businessForm.cardPortraitFace : require('@/assets/images/userCenter/yyzz_demo.png')"
                      alt="">
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item class="license" label="身份证国徽面：">
                    <img
                      :src="businessForm.cardPortraitNationalEmblem ? businessForm.cardPortraitNationalEmblem : require('@/assets/images/userCenter/yyzz_demo.png')"
                      alt="">
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="有效期开始日期：">
                    <span>{{ businessForm.adminPeriodStart }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="有效期结束日期：">
                    <span>{{ businessForm.adminPeriodEnd }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
                <div class="separ"></div>
                <div class="admin">附件资料</div>
                <el-table
                    v-loading="openShopLoading"
                    ref="msgTable"
                    :data="businessForm.files"
                    style="min-height: 472px"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="name" label="附件名称" width="" show-overflow-tooltip>
                      <template v-slot="scope">
                        <span class="action" @click="handleImgPreview(scope.row)" v-if="scope.row.name.endsWith('png') || scope.row.name.endsWith('jpg') || scope.row.name.endsWith('gif') || scope.row.name.endsWith('bmp')">{{ scope.row.name }}</span>
                        <span v-else>{{ scope.row.name }}</span>
                      </template>
                    </el-table-column>
                  <el-table-column prop="category" label="类型" width="" show-overflow-tooltip>
                  </el-table-column>
                  <el-table-column prop="startTime" label="有效期开始日期" width="" show-overflow-tooltip>
                  </el-table-column>
                  <el-table-column prop="endTime" label="有效期结束日期" width="" show-overflow-tooltip>
                    <template slot-scope="scope">
                      {{ scope.row.endTime === null ? '长期' : scope.row.endTime }}
                    </template>
                  </el-table-column>
                    <el-table-column prop="" label="操作" width="90">
                        <template slot-scope="scope">
                            <el-button size="small" type="primary" @click="handleDownload(scope.row)">下载</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
          <el-dialog :visible.sync="imgPreviewDialog" title="图片预览">
            <img class="center mb20" style="display: block;height: 460px;" :src="previewImg" alt="">
          </el-dialog>
        </div>
    </main>
</template>
<script>
import { getEnterpriseAuthInfo } from '@/api/frontStage/verification'
import { previewFile } from '@/api/platform/common/file'

export default {
    data () {
        return {
            openShopLoading: false,
            formLoading: false,
            title: '查看个体户详情',
            businessForm: {},
            imgPreviewDialog: false,
            previewImg: '',
            dateFormat: 'yyyy-MM-dd HH:mm:ss'
        }
    },
    created () {
        this.formLoading = true
        this.getEnterpriseAuthInfoM2()
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            let newDateSr = dateStr.split('-')
            let day = newDateSr[2].split(' ')[0]
            return newDateSr[0] + '年' + newDateSr[1] + '月' + day + '日'
        }
    },
    mounted () {
    },
    methods: {
        async handleDownload (file) {
            this.openShopLoading = true
            previewFile({ recordId: file.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = file.name
                a.click()
                window.URL.revokeObjectURL(url)
                this.openShopLoading = false
            }).catch(() => {
                this.openShopLoading = false
            })
        },
        handleImgPreview (row) {
            this.previewImg = row.url
            this.imgPreviewDialog = true
        },
        getEnterpriseAuthInfoM2 () {
            getEnterpriseAuthInfo({}).then(res => {
                this.businessForm = res
                previewFile({ recordId: this.businessForm.businessLicenseId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.businessForm.businessLicense = url
                })
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 跳转修改页面
        handleEdit () {
            return this.$router.push('/user/reverify/business')
        },
    },
}
</script>
<style scoped lang="scss">
main > div {
    height: 100%;
    border: 1px solid rgba(230, 230, 230, 1);
}

.content {
    min-height: 634px;
}

.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;
    position: relative;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }

    .edit {
        font-size: 16px;
        color: #216EC6;
        position: absolute;
        right: 30px;
        cursor: pointer;

        img {
            width: 14px;
            height: 14px;
            margin-right: 6px;
        }
    }
}

// 个人详情
.individual {
    width: 512px;

    .row {
        margin-bottom: 30px;
        font-size: 16px;
        color: #333;
    }

    .rowLabel {
        width: 147px;
        margin-right: 10px;
        text-align: right;
    }

    .rowContent {
        img {
            width: 160px;
            height: 100px;
            object-fit: cover;
        }

        img:first-of-type {
            margin-right: 30px;
        }
    }
}

// 表单
/deep/ .el-form-item__label {
    margin-right: 24px;
    // margin-bottom: 30px;
    padding-right: 0;
    font-size: 16px;
    color: #333;

    &.license {
        margin-bottom: 38px;
    }
}

/deep/ .el-form-item__content {
    font-size: 16px;
    color: #333;

    img {
        width: 120px;
        height: 90px;
        object-fit: cover;
    }
}

.separ {
    width: 1066px;
    height: 1px;
    margin: 18px 0 13px;
    border-top: 1px dashed rgba(204, 204, 204, 1);
}

.admin {
    margin-left: 40px;
    margin-bottom: 30px;
    font-size: 20px;
    color: #216EC6;
}
//表格
.custom-table {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  margin-bottom: 20px;
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 500px;
    margin-top: 0;
  }
}
</style>