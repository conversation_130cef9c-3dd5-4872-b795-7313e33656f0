<template>
    <div>
        <main>
            <div class="shopBanner mb20 dfa">
                <div class="contentBox center dfa">
                    <img class="logo" src="../../../../assets/images/shop/logo.png" alt="">
                    <div class="shopDetail">
                        <div class="title mb10">业广建筑科技有限公司</div>
                        <div class="info">地址：陕西省西安市雁塔区丈八沟街道锦...</div>
                        <div class="info mb10">主营：商品供应,租赁服务,工程装备,安防...</div>
                        <div class="tags dfa">
                            <div>认证企业</div>
                            <div>信用：132</div>
                            <div>入驻：712天</div>
                        </div>
                    </div>
                    <img class="banner" src="../../../../assets/images/shop/banner.png" alt="">
                </div>
            </div>
            <div class="shopSection center">
                <div class="tabs dfa">
                    <div :style="{ background: currentSection == 0 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 0">商品</div>
                    <!-- <div :style="{ background: currentSection == 1 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 1">需求公告</div> -->
                    <div :style="{ background: currentSection == 1 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 1">企业简介</div>
                    <!-- <div :style="{ background: currentSection == 3 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 3">经营业绩</div>
                    <div :style="{ background: currentSection == 4 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 4">专项资质</div>
                    <div :style="{ background: currentSection == 5 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 5">经营动态</div>
                    <div :style="{ background: currentSection == 6 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 6">信用荣誉</div>
                    <div :style="{ background: currentSection == 7 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 7">知识产权</div>
                    <div :style="{ background: currentSection == 8 ? 'rgba(33, 110, 198, 1)' : '' }" @click="currentSection = 8">宣传展示</div> -->
                </div>
            </div>
            <div class="productInfo" v-show="currentSection === 0">
                <div class="contentBox center">
                    <div class="typeBtns dfa">
                        <div>物资销售</div>
                        <div>集采经营</div>
                        <div>物流经营</div>
                    </div>
                    <searchBox :form="filterArr" :list.sync="checkedList"></searchBox>
                    <div class="searchBar dfa mb10">
                        <span>装货地：</span>
                        <el-select v-model="startPlace" value-key="" placeholder="">
                            <el-option v-for="item in places" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                        <div class="bar"></div>
                        <span>目的地：</span>
                        <el-select v-model="destination" value-key="" placeholder="">
                            <el-option v-for="item in places" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                        <button>搜索</button>
                    </div>
                </div>
                <div class="productList center">
                    <div class="item mb20" v-for="(item, i) in productList" :key="i">
                        <div class="image">
                            <img :src="item.img" alt="">
                            <div class="addr dfa">
                                <i class="el-icon-location-outline"></i>
                                <div>晋中市</div>
                                <div>（345.08km）</div>
                            </div>
                        </div>
                        <div class="info dfb">
                            <span>新BZH198</span>
                            <span>6.2米/低栏火车/0.39吨</span>
                        </div>
                        <div class="company dfa">
                            <img src="" alt=""><span>{{ item.company }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="shopInfo" v-show="currentSection === 1">
                <div class="baseInfo contentBox center mb20">
                    <el-form :model="shopBaseInfo" label-width="120px" :inline="false" size="normal">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="法定代表人：">{{shopBaseInfo.legalRepresentative}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="成立日期：">{{shopBaseInfo.fundDate}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="注册资本：">{{shopBaseInfo.fundMoney}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="联系人：">{{shopBaseInfo.contactPerson}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="手机号码：">{{shopBaseInfo.tel}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="固定电话：">{{shopBaseInfo.landLine}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="行业分类：">{{shopBaseInfo.industryCategory}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="企业性质：">{{shopBaseInfo.enterpriseType}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="企业邮箱：">{{shopBaseInfo.enterpreiseEmail}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="经营地址：">{{shopBaseInfo.businessSite}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="备案单位：">{{shopBaseInfo.recordCompany}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="主营业务：">{{shopBaseInfo.businesses}}</el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>

                </div>
                <div class="intro contentBox center mb20">
                    <div class="title dfa">企业简介</div>
                    <div class="text">{{shopIntro}}</div>
                </div>
                <div class="businessInfo contentBox center">
                    <div class="title dfa">业务信息</div>
                    <div class="text">
                        <el-form :model="businessInfo" label-width="120px" :inline="false" size="normal">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="业务类型：">{{businessInfo.businessType}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="年营业额：">{{businessInfo.yearlyBusinessVolume}}</el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="研发人数：">{{businessInfo.staffs}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="研发范围：">{{businessInfo.developRange}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="研发产品：">{{businessInfo.product}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="客户类型：">{{businessInfo.customerType}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="服务地区：">{{businessInfo.serveArea}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="经营模式：">{{businessInfo.managementModel}}</el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="备案单位：">{{businessInfo.recordCompany}}</el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</template>

<script>
import searchBox from '../../components/searchBox'

export default {
    components: {
        searchBox
    },
    data () {
        return {
            currentSection: 1,
            startPlace: 1,
            destination: 2,
            places: [
                { label: '北京', value: 1 },
                { label: '上海', value: 2 },
            ],
            filterArr: [
                { name: '要求车型', options: ['不限', '平板货车', '高栏货车', '厢式货车', '自卸货车', '集装箱货车', '高地板货车', '低栏货车', '冷藏车', '爬梯车', '微卡', '轻卡'] },
                { name: '要求车长', options: ['不限', '1.8米', '2.5米', '2.7米', '3米', '3.1米', '3.2米', '3.3米', '3.4米', '3.6米', '3.7米', '3.8米', '4.1米', '4.2米', '4.3米', '4.4米'] },
            ],
            checkedList: [],
            productList: [
                { name: '', addr: '', distance: '', license: '', company: '中钧科技有限公司', icon: '', img: require('../../../../assets/images/img/1668320745440.jpg') },
                { name: '', addr: '', distance: '', license: '', company: '中钧科技有限公司', icon: '', img: require('../../../../assets/images/img/1668320745440.jpg') },
                { name: '', addr: '', distance: '', license: '', company: '中钧科技有限公司', icon: '', img: require('../../../../assets/images/img/1668320745440.jpg') },
                { name: '', addr: '', distance: '', license: '', company: '中钧科技有限公司', icon: '', img: require('../../../../assets/images/img/1668320745440.jpg') },
                { name: '', addr: '', distance: '', license: '', company: '中钧科技有限公司', icon: '', img: require('../../../../assets/images/img/1668320745440.jpg') },
                { name: '', addr: '', distance: '', license: '', company: '中钧科技有限公司', icon: '', img: require('../../../../assets/images/img/1668320745440.jpg') },
                { name: '', addr: '', distance: '', license: '', company: '中钧科技有限公司', icon: '', img: require('../../../../assets/images/img/1668320745440.jpg') },
                { name: '', addr: '', distance: '', license: '', company: '中钧科技有限公司', icon: '', img: require('../../../../assets/images/img/1668320745440.jpg') },
            ],
            shopBaseInfo: {
                legalRepresentative: '李素裳',
                fundDate: '2017-10-17',
                fundMoney: '20000万元整',
                contactPerson: '刘强',
                tel: '***********',
                landLine: '************',
                industryCategory: '科学研究和技术服务',
                enterpriseType: '股份制',
                enterpreiseEmail: '<EMAIL>',
                businessSite: '山西西安',
                recordCompany: '',
                businesses: '商品供应',
            },
            shopIntro: '中科技有照公丽注本之元、主经资收件得发，产业五联风、大数现、爱计道、人工都懂、都能执备研发与场售、新村制、城知，商品得费。公用股力子成为国内产业万联网龙头企业，力争通边互取网技术、智能硬件，人工智能等技心技术等工程、能源、制流、房地产、化工、物流、制流业、服务业等诸多传统得近植入技字化曾理，养局传统行业经营管理实现科接转型，让企业陶越式发用。日前，中的科技已经在会国设有6个研发中心18个运营中心，分别在 北点、广州、四、陕西、山西、河南、河北、新疆等地，仅技术研发人员就期过2100人，市场词警销售纪展11000人，90以上为大学幕1学历 35%为21985名校带业。至公司运营团队已检开全国20省的主费城市以及提区典是业务，对产业五联同的代的大前，中购开技力争成为产业亚联网行业的领先者。中物科技有限公司成立于2017年11月10日，注册地位于新疆乌鲁木齐市新市北京南路439号中核发属大厦14层05-47，法人代表为李学。经范围包括:一粒项目:可安戴智能装备销售;件开发;互联网散据服务;云计算装备技术服务:大数据服务;信息系使集成服务:数据处理和存储支持服务:工程和技术研究和试治发展;广告设计、代理;建筑工程机材与装备相:信息技术咨询服务:工程管理服务:招投标代理服务:资物进出口:技术进出口:汽车等配件等售:五金产品零信:家具切售:建筑材料销售:化工产品销售(不含许司类化工产品);机械装备销售;电子产品销售。 (除依法须经批准的项日外，凭营业扶朋依法自主开属经营活动)',
            businessInfo: {
                businessType: '研发型',
                yearlyBusinessVolume: '205660万',
                staffs: '2000人',
                developRange: '互联网技术',
                product: '企业帮平台',
                customerType: '/',
                serveArea: '乌鲁木齐',
                managementModel: '/',
                recordCompany: '/',
            },
        }
    },
    methods: {
    },
}
</script>

<style scoped lang="scss">
main {
    width: 100%;
    padding-bottom: 105px;
    background-color: #f5f5f5;
}
.shopInfo {
    &>div {background-color: #fff;}
}
.typeBtns, .searchBar, .productList {background-color: #fff;}
.contentBox {
    width: 1326px;
    min-width: 1326px;
    height: 100%;
}
.shopBanner {
    width: 100%;
    height: 170px;
    background-color: #e0e9f3;
    .logo {
        width: 118px;
        height: 120px;
        margin-right: 18px;
    }
    .shopDetail {
        width: 410px;
        .title {font-size: 20px;}
        .info {
            font-size: 16px;
            color: rgba(102, 102, 102, 1);
        }
        .info:first-child {margin-bottom: 4px;}
        .tags div {
            padding: 2.5px 12px;
            font-size: 14px;
            border: 1px solid;
            border-radius: 2px;
            margin-right: 10px;
            &:first-child {
                color: rgba(255, 141, 26, 1);
                border-color: rgba(255, 141, 26, 1);
            }

            &:nth-child(2) {
                color: rgba(42, 130, 228, 1);
                border-color: rgba(42, 130, 228, 1);
            }
            &:last-child {
                color: rgba(153, 153, 153, 1);
                border-color: rgba(153, 153, 153, 1);
            }
        }
    }
    .banner {width: 780px;height: 150px;}
}
.shopSection {
    width: 1326px;
    color: #fff;
    .tabs {
        width: 100%;
        height: 52px;
        background-color: #333333;
        user-select: none;
        div {
            width: 147px;
            height: 52px;
            line-height: 52px;
            text-align: center;
            font-size: 20px;
            font-weight: 400;
            cursor: pointer;
        }
    }
}

.typeBtns {
    height: 64px;
    margin-bottom: 10px;
    padding-left: 20px;
    div {
        width: 86px;
        height: 32px;
        margin-right: 10px;
        font-size: 14px;
        font-weight: 400;
        line-height: 32px;
        text-align: center;
        color: rgba(51, 51, 51, 1);
        border-radius: 2px;
        border: 1px solid rgba(230, 230, 230, 1);
        cursor: pointer;
        &:first-child {
            border: none;
            color: #fff;
            background: rgba(33, 110, 198, 1);
        }
    }
}
.searchBar {
    height: 52px;
    padding-left: 26px;
    border-top: 1px solid rgba(230, 230, 230, 1);
    z-index: 1;
    .bar {
        width: 10px;
        height: 1px;
        margin: 0 8px;
        background: rgba(204, 204, 204, 1);
    }
    /deep/ .el-select.el-select--small {
        // margin-right: 26px;
        .el-input__inner {
            height: 28px;
        }
    }

    button {
        width: 52px;
        height: 26px;
        margin-left: 20px;
        color: rgba(255, 255, 255, 1);
        background: rgba(33, 110, 198, 1);
    }
}
.productList {
    width: 1326px;
    height: 800px;
    padding: 20px 21px 39px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .item {
        width: 306px;
        height: 335px;
        border: 1px solid rgba(229, 229, 229, 1);
        .image {
            width: 100%;
            height: 245px;
            position: relative;
            img {width: 100%;height: 100%;}
            .addr {
                width: 100%;
                height: 36px;
                padding-left: 10px;
                color: rgba(255, 255, 255, 1);
                background: rgba(0, 0, 0, 0.5);
                position: absolute;
                left: 0;
                bottom: 0;
                .el-icon-location-outline::before {
                    width: 16px;
                    height: 16px;
                }
                &>div:nth-child(2) {
                    width: 178px;
                    margin-left: 10px;
                }
            }
        }
        .info {
            height: 28px;
            margin: 8px 10px 10px;
            &>span:first-child {
                font-size: 16px;
                color: rgba(33, 110, 198, 1);
            }
        }
        .company {
            padding: 0 10px;
            color: rgba(51, 51, 51, 1);
            img {
                width: 24px;
                height: 24px;
                margin-right: 10px;
                border-radius: 50%;
            }
        }
    }
}
/deep/ .el-form-item__label {
    font-weight: bold;
    color: #333;
}
/deep/ .el-row {
    .el-col {height: 40px;}
}
.baseInfo {
    // height: 440px;
    padding: 20px 0;
}
.intro, .businessInfo {
    padding: 16px;
    .title {
        padding-left: 14px;
        font-size: 18px;
        position: relative;
        &::before {
            content: '';
            display: block;
            width: 4px;
            height: 80%;
            border-radius: 2px;
            background-color: #5791d3;
            position: absolute;
            left: 0;
        }
    }
    .text {
        padding: 20px 0;
    }
}
.intro {
}
</style>