<template>
    <!-- <micro-app name="manage" url="http://************:5173/"></micro-app> -->
    <WuJie width="100%" height="100%" name="hepaas" :url="url"></WuJie>
</template>
<script>
import Wu<PERSON><PERSON> from 'wujie-vue2'

export default {
    name: 'microApp',
    components: { <PERSON><PERSON><PERSON> },
    data () {
        return {
            url: 'http://************:5173'
        }
    },
    methods: {},
    created () {
    },
}
</script>