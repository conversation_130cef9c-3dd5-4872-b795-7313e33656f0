<template>
    <div class="base-page">
        <!-- 列表 -->
        <div v-show="viewList === true" class="right">
            <div class="e-table">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="top">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div class="left-btn">
                            <!--                            <el-button type="primary" @click="handleNew" class="btn-greenYellow">新增</el-button>-->
                            <!--                            <el-button type="primary" @click="changePublishState(1)" class="btn-greenYellow">启用</el-button>-->
                            <!--                            <el-button type="primary" @click="changePublishState(2)" class="btn-greenYellow">停用</el-button>-->
                            <!--                            <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按排序值排序</el-radio>
                        <el-input v-model="keywords" placeholder="输入搜索关键字" type="text" @keyup.enter.native="handleInputSearch">
                            <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table">
                <el-table
                    ref="mainTable2" :data="tableData" :height="rightTableHeight" border class="table" highlight-current-row @row-click="handleCurrentInventoryClick2"
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="80">
                        <template v-slot="scope">
                            <span class="action" @click="onDel(scope)"><img alt="" src="../../../../assets/btn/delete.png"></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺名称" width="260">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.shopName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺地址" width="300">
                        <template v-slot="scope">
                            <span @click="handleView(scope)">
                                {{ scope.row.province }}{{ scope.row.county }}{{ scope.row.detailedAddress }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺类型">
                        <template v-slot="scope">
                            <span v-if="scope.row.shopType == 2">个人</span>
                            <span v-if="scope.row.shopType == 1">企业</span>
                            <span v-if="scope.row.shopType == 0">个体户</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺审核状态">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.auditStatus==2">未审核</el-tag>
                            <el-tag v-if="scope.row.auditStatus==1" type="success">审核通过</el-tag>
                            <el-tag v-if="scope.row.auditStatus==3" type="danger">审核未通过</el-tag>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="店铺状态">-->
<!--                        <template v-slot="scope">-->
<!--                            <el-tag v-if="scope.row.state==1" type="success">启用</el-tag>-->
<!--                            <el-tag v-if="scope.row.state==0" type="danger">停用</el-tag>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column label="自营店铺">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.isBusiness==1" type="success">是</el-tag>
                            <el-tag v-else type="danger">否</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="内部店铺">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.isInternalShop==1" type="success">是</el-tag>
                            <el-tag v-else type="danger">否</el-tag>
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column label="路桥结算">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <el-tag v-if="scope.row.isInternalSettlement==1" type="success">支持</el-tag>-->
                    <!--                            <el-tag v-else type="danger">不支持</el-tag>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->

                    <!-- 图片排序值 -->
                    <!--                    <el-table-column label="排序值" width="120" prop="sort" />-->

                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :currentPage.sync="pages.currPage" :limit="50"
                :pageSize.sync="pages.pageSize"
                :total="pages.totalCount"
                @currentChange="currentChange"
                @sizeChange="sizeChange"
            />
        </div>
        <div v-show="viewList !== true" class="right">
            <!-- ---------------------新增/编辑窗口--------------------- -->
            <div v-show="viewList === 'class'" class="e-form" style="padding: 0 10px 10px;">
                <div class="tabs-title">基本信息</div>
                <el-form ref="formEdit" :model="formData" :rules="formRules" label-width="150px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="店铺名称：" prop="name" width="150px">
                                <el-input v-model="formData.shopName" clearable placeholder="请输入链接名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="店铺类型：" prop="url" width="150px">
                                <el-input v-model="formData.shopType" clearable placeholder=""></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="店铺所在省份：" prop="sort">
                                <el-input v-model="formData.province" clearable placeholder="请选择店铺所在省份"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="店铺所在市：" prop="sort">
                                <el-input v-model="formData.province" clearable placeholder="请选择店铺所在省份"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="店铺所在县、区：" prop="sort">
                                <el-input v-model="formData.city" clearable placeholder="请选择店铺所在县、区"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="店铺详细地址：" prop="sort">
                                <el-input v-model="formData.detailedAddress" clearable placeholder="请输入店铺详细地址"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="店铺状态：" prop="sort">
                                <el-input v-model="formData.state" clearable placeholder="请输入店铺详细地址"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="排序值：" prop="sort">
                                <el-input v-model="formData.sort" :min="0" placeholder="填写排序值" type="number">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="备注信息：">
                                <el-input v-model="formData.remarks" autocomplete="off" type="textarea"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="footer">
                <div class="right-btn">
                    <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
                    <el-button @click="onCancel">取消</el-button>
                </div>
            </div>
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog v-dialogDrag :visible.sync="queryVisible" title="高级查询" width="50%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺名称：" prop="shopName" width="150px">
                            <el-input v-model="filterData.shopName" clearable placeholder="请输入店铺名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="店铺地址：" prop="detailedAddress">
                            <el-input v-model="filterData.detailedAddress" clearable placeholder="请输入店铺地址"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺类型：">
                            <el-select v-model="filterData.shopType" placeholder="店铺类型">
                                <el-option
                                    v-for="item in shopTypeFilter" :key="item.value" :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="店铺状态：">
                            <el-select v-model="filterData.state" placeholder="店铺状态">
                                <el-option
                                    v-for="item in shopStateFilter" :key="item.value" :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="自营店铺：">
                            <el-select v-model="filterData.isBusiness" placeholder="自营店铺">
                                <el-option
                                    v-for="item in shopBusinessFilter" :key="item.value" :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <!--            <el-col :span="12">-->
                    <!--              <el-form-item label="审核状态：">-->
                    <!--                <el-select v-model="filterData.auditStatus"  placeholder="店铺审核状态">-->
                    <!--                  <el-option v-for="item in shopAuditStatusFilter" :key="item.value" :label="item.label"-->
                    <!--                             :value="item.value">-->
                    <!--                  </el-option>-->
                    <!--                </el-select>-->
                    <!--              </el-form-item>-->
                    <!--            </el-col>-->
                    <el-col :span="12">
                        <el-form-item label="内部店铺：">
                            <el-select v-model="filterData.isInternalShop" placeholder="是否为内部店铺">
                                <el-option
                                    v-for="item in shopInernalShopFilter" :key="item.value" :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--                <el-row>-->
                <!--                    <el-col :span="12">-->
                <!--                        <el-form-item label="供应商：">-->
                <!--                            <el-select v-model="filterData.isSupplier"  placeholder="是否为供应商">-->
                <!--                                <el-option v-for="item in shopSupplierFilter" :key="item.value" :label="item.label"-->
                <!--                                           :value="item.value">-->
                <!--                                </el-option>-->
                <!--                            </el-select>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { batchDelete, batchNotPublish, batchPublish, create, del, edit, getList } from '@/api/platform/shop/shopAudit'
// eslint-disable-next-line no-unused-vars
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapActions } from 'vuex'

export default {
    components: {
        ComPagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                getList(this.requestParams).then(res => {
                    this.tableData = res.list
                })
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertName: '店铺',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                shopName: '',
                city: '',
                shopType: null,
                state: null,
                isBusiness: null,
                isSupplier: null,
                isInternalShop: null,
                auditStatus: null,
                detailedAddress: '',
                orderBy: 2,
            },
            tableData: [],
            shopTypeFilter: [
                { value: null, label: '全部' },
                { value: 2, label: '个人' },
                { value: 0, label: '个体户' },
                { value: 1, label: '企业' },
            ],
            shopStateFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '停用' },
                { value: 1, label: '启用' },
            ],
            shopBusinessFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '非自营' },
                { value: 1, label: '自营' },
            ],
            shopSupplierFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '供应商' },
                { value: 1, label: '非供应商' },
            ],
            shopInernalShopFilter: [
                { value: null, label: '全部' },
                { value: 0, label: '非内部店铺' },
                { value: 1, label: '内部店铺' },
            ],
            shopAuditStatusFilter: [
                { value: null, label: '全部' },
                { value: 2, label: '未审核' },
                { value: 3, label: '审核未通过' },
            ],
            // 表单校验规则
            formRules: {
                name: [{ required: true, message: '请输入链接名', trigger: 'blur' }],
                url: [{ required: true, message: '请输入正确的链接地址', type: 'url', trigger: 'blur' }],
                sort: [{ required: true, message: '请输入正确的排序值', trigger: 'blue' }]
            },
            mapObj: null,
            // 新增编辑 表单数据
            formData: {
                shopName: '',
                shopType: '',
                province: '',
                city: '',
                district: '',
                detailedAddress: '',
            },
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        // const obj = {}
        // localStorage.setITem('queryObj', JOSN.stringify(obj))
        // let params = {
        //     limit: this.pages.pageSize,
        //     page: this.pages.currPage,
        //     orderBy: this.filterData.orderBy,
        //     auditStatus: this.filterData.auditStatus
        // }
        // getList(params).then(res => {
        //     this.pages = res
        //     this.tableData = res.list
        // })
        // this.getParams()
        // this.getTableData()
    },
    methods: {
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        resetSearchConditions () {
            this.filterData.shopName = ''
            this.filterData.city = ''
            this.filterData.detailedAddress = ''
            this.filterData.shopType = null
            this.filterData.state = null
            this.filterData.isBusiness = null
            this.filterData.isSupplier = null
            this.filterData.isInternalShop = null
            this.filterData.auditStatus = null
        },
        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 发布/取消
        changePublishState (num) {
            let arr = this.selectedRows.map(item => {
                return item.shopId
            })
            if (!this.selectedRows[0]) {
                let msg = num == 1 ? `请选择要启用的${this.alertName}` : `请选择要停用的${this.alertName}`
                return this.clientPop('warn', msg, () => {
                })
            }
            let warnMsg = num === 1 ? `您确定要启用选中的${this.alertName}吗？` : `您确定要停用选中的${this.alertName}吗？停用将下架当前店铺所有商品！`
            this.clientPop('info', warnMsg, async () => {
                switch (num) {
                case 1:
                    batchPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '启用成功', () => {
                                getList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                    break
                case 2:
                    batchNotPublish(arr).then(res => {
                        if (res.message === '操作成功') {
                            this.clientPop('suc', '停用成功', () => {
                                getList(this.requestParams).then(res => {
                                    if (res.list) {
                                        this.tableData = res.list
                                    } else {
                                        this.clientPop('warn', res.message, () => {
                                        })
                                    }
                                    this.pages = res
                                })
                            })
                        }
                    })
                }
            })
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', `您确定要删除该${this.alertName}吗？`, async () => {
                showLoading()
                del({ id: scope.row.shopId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.shopId
                })
                batchDelete(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () {
        },
        handleView (scope) {
            this.viewList = 'class'
            this.formData = scope.row
            this.action = '编辑'
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/shop/shopAuditDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shopAuditDetail',
                params: {
                    row: scope.row
                }
            })
        },
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.pages = res
            })

            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.mainTable2.toggleRowSelection(row, row.flag)
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            for (let key in this.formData) {
                this.formData[key] = null
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.action === '编辑') {
                        return this.handleEditData()
                    }
                    this.handleCreateData()
                }
            })
        },
        // 修改数据
        handleEditData () {
            edit(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 保存数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}
</style>
