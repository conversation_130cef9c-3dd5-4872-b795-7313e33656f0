<template>
  <!-- 新 企业注册开店完成 -->
  <div class="df">
    <div class="boxTop">
      <div class="title center">企业用户注册</div>
    </div>
    <!--步骤条-->
    <div class="steps">
      <el-steps :active="5" align-center>
        <el-step title="注册"></el-step>
        <el-step title="平台初审"></el-step>
        <el-step title="申请开店"></el-step>
        <el-step title="合同签约及缴费"></el-step>
        <el-step title="平台复审"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div class="successtxt">
      <h2>开店完成</h2>
      <el-button @click="rebackindex" class="rebackindexbtn">返回首页</el-button>
    </div>
  </div>
</template>
<script>

export default {
    data () {
        return {}
    },
    computed: {
    },
    methods: {
        directTo (url) {
            this.openWindowTab(url)
        },
        openShop () {
            this.$router.push('/mFront/openShop')
        },
        rebackindex () {
            this.$router.push('/index')
        }
    },
    // mounted () {
    //     setTimeout(() => {
    //         this.$router.push('/mFront/openShop')
    //     }, 3000)
    // }
}
</script>
<style scoped lang="scss">
.df {
  flex-direction: column;
}
.boxTop {
  height: 87px;
  border-bottom: 1px solid #D9D9D9;
  .title {
    width: 200px;
    height: 100%;
    font-size: 26px;
    font-weight: 500;
    line-height: 87px;
    text-align: center;
    border-bottom: 4px solid #216EC6;
    color: #333;
    user-select: none;
  }
}

.steps{
  //border-style: solid;
  margin: 0 auto;
  height: 10%;
  padding:50px 0;
  width: 800px;
  margin-bottom: -80px;
}
.successtxt{
  padding-left: 350px;
  padding-right: 350px;
  font-size: 20px;
  margin-top: 100px;
  width: 100%;
  text-align: center;
  //border: 1px solid red;
  color: black;
}
.rebackindexbtn{
  margin-top: 50px;
  font-size: 20px;
  width: 150px;
  height: 50px;
  color: white;
  background-color: rgb(16,110,212);
}
</style>
