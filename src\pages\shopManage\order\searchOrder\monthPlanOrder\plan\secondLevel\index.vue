<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right" v-loading="planListLoading">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                    </div>
                    <div class="search_box">
                        <el-input @keyup.enter.native="handleInputSearch" placeholder="计划编号" v-model="keyword">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch"  alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible=true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="e-table">
                <el-table
                    v-loading="isLoading"
                    class="table"
                    :height="rightTableHeight"
                    ref="tableCurrentRow"
                    @row-click="handleCurrentInventoryClick"
                    @selection-change="selectionChangeHandle"
                    border
                    :data="list"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="100">
                        <template v-slot="scope">
                            <div class="pointer" style="color: rgba(33, 110, 198, 1);" @click="planSecondLevelExportM(scope.row)" >导出计划</div>
                        </template>
                    </el-table-column>
                   <el-table-column prop="planNo" label="计划编号" width="300px">
                        <template v-slot="scope">
                            <span class="action" @click="monthPlanDtl(scope.row)">{{scope.row.planNo}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="planDate" label="计划日期" width="100">
                        <template v-slot="scope">
                            {{ scope.row.planDate | dateStr }}
                        </template>
                    </el-table-column>
                  <el-table-column prop="contractNo" label="合同编号" width="200px"/>
                   <el-table-column prop="supplierName" label="采购机构名称" width="300px"/>
                    <el-table-column prop="state" label="单据状态" >
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                            <el-tag type="" v-if="scope.row.state == 1">已提交</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 2">通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 3">未通过</el-tag>
                            <el-tag type="warning" v-if="scope.row.state == 4">已作废</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间"  width="160"/>
<!--                    <el-table-column label="操作" width="60">
                        <template v-slot="scope">
                        </template>
                    </el-table-column>-->
                </el-table>
            </div>
            <pagination
                :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"
                :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange"
            >
            </pagination>
        </div>

        <!--        高级查询-->
        <el-dialog title="高级查询" v-dialogDrag :visible.sync="queryVisible" width="45%">
            <el-form style="margin-top: 50px" :model="filterData" label-width="160px" ref="form" :inline="true">
                <el-row>
                    <el-form-item label="计划编号：">
                        <el-input clearable maxlength="100" placeholder="请输入计划编号" v-model="filterData.planNo"></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="合同编号：">
                        <el-input clearable maxlength="100" placeholder="请输入合同编号" v-model="filterData.contractNo"></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="采购机构名称：">
                        <el-input clearable maxlength="100" placeholder="请输入采购机构名称" v-model="filterData.orgName"></el-input>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="计划时间：">
                        <el-date-picker
                            v-model="filterData.planDate"
                            type="monthrange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            align="center"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始月份"
                            end-placeholder="结束月份"
                            :picker-options="pickerOptionsQuery"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>

import pagination from '@/components/pagination/pagination.vue'

import { mapState } from 'vuex'
import {
    batchDeletePlan,
    batchSubmitPlan,
    cancellationPlan,
    checkTotalNum,
    createChangePlanAndPlanDtl,
    createPlanAndPlanDtl,
    getPCWP2BuyContract,
    getPCWP2BuyContractDtl,
    getPlanDtlInfoByPlanNo, planSecondLevelExport,
    secondLevelPlanMonthList,
} from '@/api/plan/plan'
import { getUuid } from '@/utils/common'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            let newDateSr = dateStr.split('-')
            // let day = newDateSr[2].split(' ')[0]
            return newDateSr[0] + '年' + newDateSr[1] + '月'
        },
        dateStr2 (dateStr) {
            if (dateStr == null) {
                return
            }
            return dateStr.split('T')[0]
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    components: { pagination },
    name: 'index',
    data () {
        return {
            pcwpVersion: null,
            screenHeight: 0,
            isLoading: false,
            // 变更数据
            alterationInfoDtlLoading: false,
            showAlterationPlanDialog: false,
            updatePlanLoading: false,
            pickerOptionsQuery: {
                shortcuts: [{
                    text: '本月',
                    onClick (picker) {
                        picker.$emit('pick', [new Date(), new Date()])
                    }
                }, {
                    text: '今年至今',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date(new Date().getFullYear(), 0)
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近六个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setMonth(start.getMonth() - 6)
                        picker.$emit('pick', [start, end])
                    }
                }]
            },
            queryVisible: false,
            filterData: {
                supplierName: null,
                planNo: null,
                orgName: null,
                contractNo: null,
                planDate: [], // 修改时间
                stateCheckAll: false, // 选择全局
                state: [],
                stateOptions: [{
                    value: 0,
                    label: '草稿'
                }, {
                    value: 1,
                    label: '已提交'
                }, {
                    value: 2,
                    label: '通过'
                }, {
                    value: 3,
                    label: '未通过'
                }, {
                    value: 4,
                    label: '已作废'
                }],
            },
            // 选中的数据
            selectPlanListData: [],
            currentBillId: null, // 合同id
            changePlanDtlRowDate: [],
            addPlanLoading: false,
            addChangePlanFormRote: {
                planChangeNo: [
                    { required: true, message: '请输入变更计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                planNo: [
                    { required: true, message: '请输入计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                contractNo: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                planDate: [
                    { required: true, message: '请选择计划日期', trigger: 'blur' },
                ],
                supplierName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            addPlanFormRote: {
                planNo: [
                    { required: true, message: '请输入计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                contractNo: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                planDate: [
                    { required: true, message: '请选择计划日期', trigger: 'blur' },
                ],
                supplierName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            showSelectContractDialog: false,
            activeName: 'dtl',
            pickerOptions: {
                // disabledDate (time) {
                //     return time.getTime() < Date.now()
                // },
            },
            addPlanForm: {
                planNo: null,
                planDate: null,
                businessType: 0,
                contractId: null,
                contractNo: null,
                supplierId: null,
                supplierName: null,
                orgId: null,
                orgName: null,
                state: 0,
                remarks: null,
                isSubmit: 0,
                dtls: [],
            },
            showAddPlanDialog: false,
            contractObj: {
                contractLoading: false,
                contractList: [], // 合同列表
                pagination2: {
                    currPage: 1,
                    destination: null,
                    pageSize: 10,
                    totalNum: 10,
                    totalPage: 1,
                },
                keyword: null,
            },
            contractDtlObj: {
                contractLoading: false,
                contractDtlList: [], // 合同明细
                pagination2: {
                    currPage: 1,
                    destination: null,
                    pageSize: 10,
                    totalNum: 10,
                    totalPage: 1,
                },
            },
            planListLoading: false,
            keyword: null,
            pagination: {
                currPage: 1,
                destination: null,
                pageSize: 20,
                totalNum: 10,
                totalPage: 1,
            },
            list: [],
            changeQtyRowDate: [],
            updateChangePlanForm: {},
        }
    },
    created () {
        this.getPlanListM()
    },
    mounted () {
        this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
    },
    methods: {
        saveChangePlanM (isSubmit = null) {
            this.$refs.addChangePlanFormRoteRef.validate(valid => {
                if (valid) {
                    // 去除审核信息
                    this.updateChangePlanForm.auditList = []
                    if (isSubmit != null && isSubmit === 1) {
                        this.updateChangePlanForm.isSubmit = 1
                    } else {
                        this.updateChangePlanForm.isSubmit = 0
                    }
                    for (let i = 0; i < this.updateChangePlanForm.dtls.length; i++) {
                        let t = this.updateChangePlanForm.dtls[i]
                        if(t.thisPlanQty == null) {
                            return this.$message.error(t.materialName + '变更数量不能为空！')
                        }
                    }
                    this.updatePlanLoading = true
                    createChangePlanAndPlanDtl(this.updateChangePlanForm).then(res => {
                        if (res.code !== 200) {
                            this.clientPop('error', res.message, () => {
                            })
                        } else {
                            this.clientPop('suc', '操作成功', () => {
                                this.getPlanListM()
                                this.showAlterationPlanDialog = false
                            })
                        }
                    }).finally(() => {
                        this.updatePlanLoading = false
                    })
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
        // 变更数量
        changeSelectQtyM (row) {
            if (row.changeQty == null) {
                row.changeQty = row.orderQty
            }
        },
        // 变更方法
        alterationClickM (row) {
            // 如果变更状态为null，说明是新增变更
            if (row.changeState == null) {
                this.showAlterationPlanDialog = true
                this.updatePlanLoading = true
                // 如果是计划编号
                getPlanDtlInfoByPlanNo({ planNo: row.planNo }).then(res => {
                    this.updateChangePlanForm = res
                    this.updateChangePlanForm.state = 0
                    this.updateChangePlanForm.planChangeNo = getUuid().replaceAll('-', '')
                    this.updatePlanLoading = false
                }).catch(() => {
                    this.updatePlanLoading = false
                })
            } else {
                // 前往变更页面
                this.$router.push({
                    path: '/performanceManage/updateChangeDetail',
                    name: 'performanceManageUpdateChangeDetail',
                    query: {
                        planChangeNo: row.planChangeNo
                    }
                })
            }
        },
        resetSearchConditions () {
            this.filterData.planNo = null
            this.filterData.contractNo = null
            this.filterData.supplierName = null
            this.filterData.state = []
            this.filterData.planDate = []
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getPlanListM()
        },
        planSecondLevelExportM (row) {
            this.clientPop('info', '您确定要导出计划数据？', async () => {
                this.tableLoading = true
                planSecondLevelExport({ id: row.planId } ).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '物资月度供应计划表.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.currentQuery.ids = []
                    this.dataListSelections = []
                    this.$message.success('物资月度供应计划导出操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        // 高级搜索确认
        confirmSearch () {
            this.keyword = ''
            this.getPlanListM()
            this.queryVisible = false
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateOptions.length
        },
        stateAllSelect (value) {
            if (value) {
                this.filterData.state = this.filterData.stateOptions.map(t => {
                    return t.value
                })
            } else {
                this.filterData.state = []
            }
        },
        // 月供计划明细
        monthPlanDtl (row) {
            this.$router.push({
                path: '/supplierSys/order/searchOrder/secondLevelMonthPlanDetail',
                name: 'secondLevelMonthPlanDetail',
                query: {
                    planNo: row.planNo
                }
            })
        },
        // 批量作废
        cancellationPlanM () {
            if (this.selectPlanListData.length == 0) {
                return this.clientPop('warning', '请勾选已提交数据', () => {
                })
            }
            let ids = this.selectPlanListData.filter(t => {
                return t.state == 1
            }).map(item => {
                return item.planId
            })
            if (ids.length == 0) {
                return this.clientPop('warning', '请勾选已提交数据', () => {
                })
            }
            this.clientPop('info', '您确定要批量作废这些数据吗？', async () => {
                this.planListLoading = true
                cancellationPlan(ids).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getPlanListM()
                    }
                    this.planListLoading = false
                }).catch(() => {
                    this.planListLoading = false
                })
            })
        },
        batchDeletePlanM () {
            if (this.selectPlanListData.length == 0) {
                return this.clientPop('warning', '请勾选草稿状态的数据', () => {
                })
            }
            let ids = this.selectPlanListData.filter(t => {
                return t.state == 0
            }).map(item => {
                return item.planId
            })
            if (ids.length == 0) {
                return this.clientPop('warning', '请勾选草稿状态的数据', () => {
                })
            }
            this.clientPop('info', '您确定要批量删除这些数据吗？', async () => {
                this.planListLoading = true
                batchDeletePlan(ids).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getPlanListM()
                    }
                    this.planListLoading = false
                }).catch(() => {
                    this.planListLoading = false
                })
            })
        },
        // 批量提交
        batchSubmitPlanM () {
            if (this.selectPlanListData.length === 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let ids = this.selectPlanListData.filter(t => {
                return t.state == 0 || t.state == 3
            }).map(item => {
                return item.planId
            })
            if (ids.length === 0) {
                return this.$message.warning('请勾选有效的数据！')
            }
            this.clientPop('info', '您确定要批量提交审核这些数据吗？', async () => {
                this.planListLoading = true
                batchSubmitPlan(ids).then(res => {
                    if (res.code != null && res.code === 200) {
                        this.$message.success('操作成功')
                        this.getPlanListM()
                    }
                }).finally(() => {
                    this.planListLoading = false
                })
            })
        },
        changePlanDtlRowM (row) {
            if (row.thisPlanQty == null) {
                row.thisPlanQty = 0
            }
            if (this.changePlanDtlRowDate.length === 0) {
                this.changePlanDtlRowDate.push({
                    contractDtlId: row.DtlId,
                    materialName: row.ItemName,
                    materialId: row.ItemID,
                    classPathId: row.MaterialClassld,
                    classPathName: row.MaterialClassName,
                    spec: row.Model,
                    unit: row.Unit,
                    texture: row.Spec,
                    sourceQty: row.Qty,
                    thisPlanQty: row.thisPlanQty
                })
                return
            }
            let flag = false
            this.changePlanDtlRowDate.forEach(t => {
                if (t.contractDtlId == row.DtlId) {
                    t.thisPlanQty = row.thisPlanQty
                    flag = true
                }
            })
            if (!flag) {
                this.changePlanDtlRowDate.push({
                    contractDtlId: row.DtlId,
                    materialName: row.ItemName,
                    materialId: row.ItemID,
                    classPathId: row.MaterialClassld,
                    classPathName: row.MaterialClassName,
                    spec: row.Model,
                    texture: row.Spec,
                    unit: row.Unit,
                    sourceQty: row.Qty,
                    thisPlanQty: row.thisPlanQty
                })
            }
        },
        // 保存计划
        savePlanM (isSubmit) {
            let newDtlArr = this.changePlanDtlRowDate.filter(t => {
                return t.thisPlanQty != 0
            })
            this.$refs.addPlanFormRoteRef.validate(valid => {
                if (valid) {
                    if (newDtlArr.length == 0) {
                        return this.$message.error('未选择明细数量！')
                    }
                    this.addPlanForm.dtls = newDtlArr
                    if (isSubmit != null && isSubmit == 1) {
                        this.addPlanForm.isSubmit = 1
                    } else {
                        this.addPlanForm.isSubmit = 0
                    }
                    // 保存并提交
                    this.addPlanLoading = true
                    createPlanAndPlanDtl(this.addPlanForm).then(res => {
                        if (res.code != 200) {
                            this.clientPop('error', res.message, () => {
                            })
                        } else {
                            this.clientPop('suc', '操作成功', () => {
                                // 重置
                                this.restartPlanFormM()
                                this.addPlanForm.planNo = getUuid().replaceAll('-', '')
                                this.contractDtlObj.contractDtlList = []
                                this.showAddPlanDialog = false
                                // 查询计划列表
                                this.getPlanListM()
                            })
                        }
                        this.addPlanLoading = false
                    }).catch(() => {
                        this.addPlanLoading = false
                    })
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.selectPlanListData = val
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.tableCurrentRow.toggleRowSelection(row, row.flag)
        },
        // 合同点击
        contractRowClick (row) {
            this.pcwpVersion = row.version
            this.addPlanForm.contractId = row.BillId
            this.addPlanForm.contractNo = row.BillNo
            this.addPlanForm.supplierId = row.BId
            this.addPlanForm.supplierName = row.BName
            this.addPlanForm.orgId = row.OrgId
            this.addPlanForm.orgName = row.OrgName
            this.addPlanForm.supplierName = row.BName
            this.showSelectContractDialog = false
            this.addPlanForm.planNo = getUuid().replaceAll('-', '')
            // 清空之前选择的明细
            this.changePlanDtlRowDate = []
            this.currentBillId = row.BillId
            this.getPCWP1BuyContractDtlM()
        },
        // 选择合同
        selectContractM () {
            this.showSelectContractDialog = true
            this.getPCWP1BuyContractM()
        },
        // 标签点击暂时无用
        handleClick (tab, event) {
            console.log(tab, event)
        },
        restartPlanFormM () {
            this.addPlanForm = {
                planNo: null,
                planDate: null,
                businessType: 0,
                contractId: null,
                contractNo: null,
                supplierId: null,
                supplierName: null,
                orgId: null,
                orgName: null,
                state: 0,
                remarks: null,
                isSubmit: 0,
                dtls: [],
            }
            this.addPlanForm.planNo = getUuid().replaceAll('-', '')
        },
        addPlanM () {
            // this.restartPlanFormM()
            this.addPlanForm.planNo = getUuid().replaceAll('-', '')
            this.showAddPlanDialog = true
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getPlanListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getPlanListM()
        },
        currentChange2 (index) {
            this.contractObj.pagination2.currPage = index
            this.getPCWP1BuyContractM()
        },
        sizeChange2 (size) {
            this.contractObj.pagination2.pageSize = size
            this.getPCWP1BuyContractM()
        },
        currentChange3 (index) {
            this.contractDtlObj.pagination2.currPage = index
            this.getPCWP1BuyContractDtlM()
        },
        sizeChange3 (size) {
            this.contractDtlObj.pagination2.pageSize = size
            this.getPCWP1BuyContractDtlM()
        },
        getPlanListM () {
            let params = {
                page: this.pagination.currPage,
                limit: this.pagination.pageSize,
                state: 2
            }
            if (this.keyword != null) {
                params.keywords = this.keyword
            }
            if (this.filterData.planDate != null) {
                params.startPlanDate = this.filterData.planDate[0]
                params.endPlanDate = this.filterData.planDate[1]
            }
            if (this.filterData.orgName != null) {
                params.orgName = this.filterData.orgName
            }
            if (this.filterData.planNo != null) {
                params.planNo = this.filterData.planNo
            }
            if (this.filterData.contractNo != null) {
                params.contractNo = this.filterData.contractNo
            }
            // if (this.filterData.state.length !== 0) {
            //     params.states = this.filterData.state
            // }
            this.planListLoading = true
            secondLevelPlanMonthList(params).then(res => {
                this.list = res.list
                this.pagination.currPage = res.currPage
                this.pagination.pageSize = res.pageSize
                this.pagination.totalNum = res.totalCount
                this.pagination.totalPage = res.totalPage
                this.planListLoading = false
            }).catch(() => {
                this.planListLoading = false
            })
        },
        getPCWP1BuyContractDtlM () {
            // let params = {
            // //     'jsonrpc': '2.0',
            // //     'method': 'CBM.ContractMaterial.GetMaterialPurchaseListByBillID',
            //     'params': {
            //         'billid': this.currentBillId  /* 物资采购合同的BillId*/
            //     },
            // //     'id': 1,
            // //     'tags': {
            // //         'userid': this.userInfo.farUserId,
            // //         'username': this.userInfo.originalUserName,
            //     orgid: this.userInfo.orgId,
            // //         'orgname': this.userInfo.orgName,
            // //         'companycode': '1000',
            // //         'auth_client_id': 'test',
            // //         'auth_token': 'test',
            // //         'platformid': '1'
            // //     }
            // }

            let params = {
                params: {
                    billid: this.currentBillId,
                    version: this.pcwpVersion
                }
            }

            this.contractDtlObj.contractLoading = true
            getPCWP2BuyContractDtl(params).then(res => {
                let resultList = res
                if (resultList.length != 0) {
                    checkTotalNum(resultList).then(res => {
                        this.contractDtlObj.contractDtlList = res
                        this.contractDtlObj.contractLoading = false
                    }).catch(() => {
                        this.contractDtlObj.contractLoading = false
                    })
                } else {
                    this.contractDtlObj.contractLoading = false
                }
                // this.contractDtlObj.pagination2.totalPage = res.recordsTotal % res.PageSize == 0 ? res.recordsTotal / res.PageSize : Math.floor(res.result.recordsTotal / res.PageSize) + 1
                // this.contractDtlObj.pagination2.totalNum = res.recordsTotal
                // this.contractDtlObj.pagination2.currPage = res.PageIndex
                // this.contractDtlObj.pagination2.pageSize = res.PageSize
            }).catch(() => {
                this.contractDtlObj.contractLoading = false
            })
        },
        // 根据关机字查询
        getPCWP1BuyContractByKeyWord () {
            let key = this.contractObj.keyword
            if (key != null) {
                this.contractObj.contractList = this.contractObj.contractList.filter(t => {
                    if (t.BillNo.indexOf(key) != -1 || t.Name.indexOf(key) != -1 || t.BName.indexOf(key) != -1) {
                        return true
                    } else {
                        return false
                    }
                })
                this.contractObj.keyword = null
            } else {
                this.getPCWP1BuyContractM()
            }
        },
        getPCWP1BuyContractM () {
            let params = {
                //     'jsonrpc': '2.0',
                //     'method': 'CBM.CommonContract.GetContractMaterialListByType',
                //     'params': {
                //         'type': 4 /* [ContractType]   4是物资采购合同 */
                //     },
                //     'id': 1,
                //     'tags': {
                //         'userid': this.userInfo.farUserId,
                //         'username': this.userInfo.originalUserName,
                orgid: this.userInfo.orgId,
                //         'orgname': this.userInfo.orgName,
                //         'companycode': '1000',
                //         'auth_client_id': 'test',
                //         'auth_token': 'test',
                //         'platformid': '1'
                //     }
                // }
                // 他们是前端查询数据
                // if(this.contractObj.keyword != null) {
                //     params.params.filter.KeyWord = this.contractObj.keyword
            }
            this.contractObj.contractLoading = true
            getPCWP2BuyContract(params).then(res => {
                this.contractObj.contractList = res
            }).finally(() => {
                this.contractObj.contractLoading = false
            })
        },
        // 跳转详情
        handleViewDetail (billId) {
            this.$router.push({ path: '/planDetail', query: { billId: billId } })
        },
    },
}
</script>

<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);
main {
    padding: 0 20px;
    border: $border;
}

/deep/ .el-form-item__content {
    //width: 100%;
    .el-range-separator {
        width: unset;
    }
}

/deep/ .el-table__body-wrapper {
    height: 100%;
}

.list-title {
    padding: 0;

    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}

.search {
    .box {
        width: 268px;
        height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);
        border-right: 0;

        img {
            width: 16px;
            height: 16px;
            margin: 0 4px 0 10px;
        }

        input {
            width: 230px;
        }
    }

    button {
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
    }
}

.list {
    .item {
        height: 52px;
        padding: 0 20px;
        font-size: 14px;
        border: $border;
        color: rgba(102, 102, 102, 1);
        position: relative;

        & > div:not(.pointer) {
            margin-right: 60px;
        }

        .pointer {
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 20px;
        }

        span {
            color: rgba(51, 51, 51, 1);
        }
    }
}

/deep/ .el-dialog {
    .el-dialog__header {
        height: 50px;
        line-height: 50px;
        margin-top: 0;
        padding: 0;
    }

    .el-dialog__body {
        height: 450px;
        margin-top: 0;
    }
}

.action {
    color: #2e61d7;
    cursor: pointer;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>