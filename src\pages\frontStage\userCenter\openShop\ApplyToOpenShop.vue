<template>
  <!-- 新 申请开店 -->
  <div class="openshop">
    <div class="boxTop">
      <div class="title center">企业用户</div>
    </div>
    <div class="steps">
      <el-steps :active="2" align-center>
        <el-step title="注册"></el-step>
        <el-step title="平台初审"></el-step>
        <el-step title="申请开店"></el-step>
        <el-step title="合同签约及缴费"></el-step>
        <el-step title="平台复审"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div class="txtbox">
      <div class="txt">平台初审已通过，填写店铺信息点击申请开店进入开店流程</div>
    </div>
    <div class="form">
      <el-form ref="form" :model="OpenShopForm" label-width="100px" size="mini">
        <div class="namebox">
          <el-form-item label="店铺名称："  class="namebox1">
            <div>{{OpenShopForm.shopName}}</div>
          </el-form-item>
<!--          <div class="namebox2">店铺名称默认为企业名称，不可修改</div>-->
        </div>
        <div class="namebox">
          <el-form-item label="店铺地址：">
            <div class="addressbtn">
              <el-select
                  ref="selectLabel1"
                  class="province"
                  v-model="OpenShopForm.province"
                  placeholder="省份"
                  @change="(code) => getSubDistrict(code, 1)"
              >
                <el-option
                    v-for="item in addressOptions.province"
                    :key="item.value"
                    :label="item.districtName"
                    :value="item.districtCode"
                />
              </el-select>
              <el-select ref="selectLabel2" class="city" v-model="OpenShopForm.city" value-key="" placeholder="地级市"
                         @change="(code) => getSubDistrict(code, 2)">
                <el-option
                    v-for="item in addressOptions.city"
                    :key="item.value"
                    :label="item.districtName"
                    :value="item.districtCode"
                />
              </el-select>
              <el-select ref="selectLabel3" @visible-change="addressChange"  class="county" v-model="OpenShopForm.county" value-key="" placeholder="区、县">
                <el-option
                    v-for="item in addressOptions.district"
                    :key="item.value"
                    :label="item.districtName"
                    :value="item.districtCode"
                />
              </el-select>
            </div>
          </el-form-item>
        </div>
        <div class="namebox">
        <el-form-item label="详细地址：" prop="address" :span="12" :offset="1">
          <el-input v-model="OpenShopForm.detailedAddress" class="elinput" type="textarea" :rows="2"></el-input>
        </el-form-item>
        </div>
        <div class="namebox">
        <el-form-item label="主营业务：">
          <el-input v-model="OpenShopForm.mainBusiness" class="elinput" type="textarea" :rows="2"></el-input>
        </el-form-item>
        </div>
        <div class="read">
          <el-checkbox v-model="OpenShopForm.agreeTerm"  :indeterminate="false" class="read">
            您确认阅读并接受<span @click="showTerm = true" style="color: #5fbd61">《慧采商城开店协议》</span>
          </el-checkbox>
        </div>
        <div  class="applybtnbox" style="margin-left: 80px">
          <el-form-item>
            <el-button type="primary" @click="onSubmit" class="applybtn">申请开店</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <el-dialog title="慧采商城开店协议" :visible.sync="showTerm">
            <span v-html="content"></span>
            <span slot="footer">
                        <el-button @click="showTerm = false">取消</el-button>
                        <el-button type="primary" @click="showTerm = false">确定</el-button>
                    </span>
        </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { getCascaderOptions } from '@/api/platform/common/components'
import { createShopExternal } from '@/api/frontStage/becomeSeller'
export default {
    data  () {
        return {
            showTerm: false,
            OpenShopForm: {
                shopName: '',
                province: '',
                city: '',
                county: '',
                detailedAddress: '',
                mainBusiness: '',
                agreeTerm: false,
            },
            // enterpriseForm: {
            //     businessLicense: '',
            //     enterpriseName: '',
            //     socialCreditCode: '',
            //     enterpriseType: '',
            //     legalRepresentative: '',
            //     creationTime: '',
            //     licenseTerm: '',
            //     registeredCapital: '',
            //     provinces: '',
            //     city: '',
            //     county: '',
            //     detailedAddress: '',
            //     mainBusiness: '',
            //     taxRate: null,
            //     cardPortraitFace: '',
            //     cardPortraitNationalEmblem: '',
            //     adminPhone: '',
            //     verificationCode: '',
            //     adminName: '',
            //     adminPassword: '',
            //     adminNumber: '',
            //     agreeTerm: false,
            //     files: [],
            //     mallType: 0,
            //     verifyImg: '',
            //     verifyId: '',
            // },
            addressOptions: {
                province: [],
                city: [],
                district: []
            },
        }
    },
    computed: {
        ...mapState(['userInfo'])
    },
    methods: {
        // 获取地址选择器数据
        async getAddressPickerOptions () {
            let res = await getCascaderOptions({ distCode: '100000' })
            this.addressOptions.province = res
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            if (layer === 1) {
                this.addressOptions.city = []
                this.addressOptions.district = []
                this.OpenShopForm.city = ''
                this.OpenShopForm.county = ''
                this.OpenShopForm.detailedAddress = ''
                getCascaderOptions({ distCode: code }).then(res => {
                    this.addressOptions.city = res
                })
            } else if (layer === 2) {
                this.addressOptions.district = []
                this.OpenShopForm.county = ''
                this.OpenShopForm.detailedAddress = ''
                getCascaderOptions({ distCode: code }).then(res => {
                    this.addressOptions.district = res
                })
            }
        },
        // 校验地址信息
        validateAddress (rule, value, callback) {
            if ( this.enterpriseForm.province == null || this.enterpriseForm.province == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.enterpriseForm.city == null || this.enterpriseForm.city == '' ) {
                return callback(new Error('请选择市级！'))
            }
            // if ( this.enterpriseForm.county == null || this.enterpriseForm.county == '' ) {
            //     return callback(new Error('请选择县、区！'))
            // }
            callback()
        },
        addressChange (val) {
            if(!val) {
                this.OpenShopForm.province = this.$refs.selectLabel1.selectedLabel
                this.OpenShopForm.city = this.$refs.selectLabel2.selectedLabel
                this.OpenShopForm.county = this.$refs.selectLabel3.selectedLabel
                let newAddress = this.OpenShopForm.province + this.OpenShopForm.city + this.OpenShopForm.county
                this.OpenShopForm.detailedAddress = newAddress
            }
        },
        // 提交
        onSubmit () {
            this.$refs['form'].validate(valid => {
                if(valid) {
                    if (this.OpenShopForm.agreeTerm) {
                        this.createShopExternalM()
                    }else {
                        this.$message({
                            message: '请查看协议后勾选',
                            type: 'error'
                        })
                    }
                }
            })
        },
        // 开店
        async createShopExternalM () {
            let res = await createShopExternal(this.OpenShopForm)
            if(res.code == 200) {
                this.$router.push('/mFront/contractSigning')
            }
        },
    },
    created () {
        this.getAddressPickerOptions()
        //this.getRegisterAgreeUser('userRegistration')
        this.OpenShopForm.shopName = this.userInfo.enterpriseName
    },
}
</script>
<style scoped lang="scss">
.openshop {
  flex-direction: column;
  //border-style: dashed;
  height: 100%;
}
.boxTop {
  //border-style: dot-dash;
  height: 87px;
  border-bottom: 1px solid #D9D9D9;
  .title {
    width: 200px;
    height: 100%;
    font-size: 26px;
    font-weight: 500;
    line-height: 87px;
    text-align: center;
    border-bottom: 4px solid #216EC6;
    color: #333;
    user-select: none;
  }
}
.steps{
  //border-style: solid;
  margin: 0 auto;
  height: 10%;
  padding:50px 0;
  width: 800px;
}
.txtbox{
  margin: 0 auto;
  height: 150px;
  padding:50px 0;
  width: 1000px;
  text-align: center;
  .txt{
    font-weight: bold;
    font-size: 35px;
  }
}
.form{
  //border-style: solid;
  margin: 0 auto;
  height: 500px;
  padding:50px 0;
  width: 900px;
}
.addressbtn{
  display: flex;
  .province{
    //border-style: solid;
    margin-right: 20px;
    width: 100px;
    height: 50px;
  }
  .city{
    margin-right: 20px;
    width: 100px;
    height: 50px;
  }
  .county{
    margin-right: 20px;
    width: 100px;
    height: 50px;
  }
}
.elinput {
  width: 800px;
  :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
  }
  :deep(.el-input) {
    height: 60px;
  }
}
.namebox{
  //border-style: dashed;
  display: flex;
  justify-content: space-between;
  height: 80px;
  width: 100%;
  .namebox1{
    //border-style: solid;
    width: 200px;
    height: 40px;
  }
  .namebox2{
    height: 80px;
    border-style: dashed;
    background-color: rgb(255,235,229);
    border-color: rgb(255,51,11);
    color: rgb(255,51,113);
    width: 300px;
    text-align: center;
    line-height: 80px;
  }

}
.read{
  width: 300px;
  height: 30px;
  padding-right: 100px;
  margin-left: 50px;
}
/deep/ .el-button {
  width: 150px;
  height: 50px;
  padding: 0 0;
  text-align: center;
  font-size: 20px;
  border: 1px solid rgba(33, 110, 198, 1);
  border-radius: 0;
  color: white;
  background-color: rgba(33, 110, 198, 1);
  margin-top: 20px;
  margin-left: 200px;
}
</style>
