<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" v-loading="showLoading">
            <el-tabs :style="{ height: tabsContentHeight + 'px' }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="基本信息" name="baseInfo" :disabled="clickTabFlag"/>
                <el-tab-pane label="明细列表" name="dtl" :disabled="clickTabFlag"/>
                <el-tab-pane label="审核历史" name="auditInfo" :disabled="clickTabFlag"/>
                <el-tab-pane label="变更历史" name="planChanges" :disabled="clickTabFlag"/>
                <div id="tabs-content" ref="container" :style="{ height: tabsContentHeight - 40 + 'px' }">
                    <!-- 基本信息 -->
                    <div id="baseInfo" class="con">
                        <div class="tabs-title" id="baseInfoCon">基本信息</div>
                        <div class="form">
                            <el-form :model="addPlanForm" ref="addPlanFormRoteRef" :data="addPlanForm" label-width="200px" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="计划编号：" prop="planNo">
                                            <el-input disabled v-model="addPlanForm.planNo"/>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="单据状态：" prop="demandType">
                                            <el-tag type="info" v-if="addPlanForm.state === 0">草稿</el-tag>
                                            <el-tag type="" v-if="addPlanForm.state === 1">已提交</el-tag>
                                            <el-tag type="success" v-if="addPlanForm.state === 2">通过</el-tag>
                                            <el-tag type="danger" v-if="addPlanForm.state === 3">未通过</el-tag>
                                            <el-tag type="warning" v-if="addPlanForm.state === 4">已作废</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="单据机构：" prop="orgName">
                                            <el-input disabled v-model="addPlanForm.orgName"/>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="合同编号：" prop="contractNo">
                                            <el-input disabled v-model="addPlanForm.contractNo"/>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="计划月份：" prop="planDate">
                                            <el-date-picker
                                                value-format="yyyy-MM"
                                                v-model="addPlanForm.planDate"
                                                type="month"
                                                align="right"
                                                :picker-options="pickerOptions"
                                                placeholder="选择月"
                                                :disabled="![0, 1, 3].includes(addPlanForm.state)"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供应商：" prop="supplierName">
                                            <el-input disabled v-model="addPlanForm.supplierName"/>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="变更通过次数：" prop="planNo">
                                            {{ addPlanForm.alterationCount }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注：" prop="remarks">
                                            <el-input
                                                type="textarea"
                                                :auto-resize="false"
                                                v-model="addPlanForm.remarks"
                                                placeholder="请输入备注"
                                                maxlength="1000"
                                                show-word-limit
                                                :disabled="![0, 1, 3].includes(addPlanForm.state)"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <div class="dfa" style="justify-content: flex-end">
                                    <el-button
                                        type="primary"
                                        class="btn-greenYellow"
                                        v-if="[0, 1, 3].includes(addPlanForm.state)"
                                        @click="savePlanM"
                                    >
                                        保存
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        class="btn-greenYellow"
                                        v-if="[0, 1, 3].includes(addPlanForm.state)"
                                        @click="savePlanM(1)"
                                    >
                                        保存并提交
                                    </el-button>
                                </div>
                            </el-form>
                        </div>
                    </div>
                    <!-- 明细列表-->
                    <div id="dtl" class="con">
                        <div class="tabs-title">明细列表</div>
                        <div class="e-table">
                            <div class="top" style="height: 40px; padding-left: 5px">
                                <div class="left">
                                    <div class="left-btn" style="margin-left: 20px">
                                        <el-button v-if=" addPlanForm.state==2&&addPlanForm.supplierName=='四川路桥建设集团股份有限公司物资分公司'"  class="btn-blue" @click="selectSupplierM">推送供方</el-button>
                                    </div>
                                </div>
                            </div>
                            <el-table
                                border
                                :data="addPlanForm.dtls"
                                class="table"
                                @row-click="masterOrderItemRowClick"
                                @selection-change="masterOrderItemSelectRow"
                                :max-height="$store.state.tableHeight"
                            >
                                <el-table-column type="selection" width="40"></el-table-column>
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="" label="操作">
                                    <template v-slot="scope">
                                        <div v-if="scope.row.twoSupplierId!=null"
                                            class="pointer" style="color: rgba(33, 110, 198, 1);"
                                            @click="closePushDtlM(scope.row)"
                                        >取消推送
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="twoSupplierName" label="供应商名称">
                                </el-table-column>
                                <el-table-column prop="materialName" label="物资名称"/>
                                <el-table-column prop="spec" label="规格型号"/>
                                <el-table-column prop="unit" label="计量单位" width="100"/>
                                <el-table-column prop="texture" label="材质" width=""></el-table-column>
                                <el-table-column prop="sourceQty" label="合同总数量" width="100"/>
                                <el-table-column prop="maxQty" label="合同剩余数量" width="130"/>
                                <el-table-column prop="thisPlanQty" label="本期计划数量" width="160">
                                    <template v-slot="scope">
                                        <el-input-number
                                            v-if="scope.row.state != 2 && scope.row.state != 4"
                                            size="mini" v-model="scope.row.thisPlanQty"
                                            :min="0" :precision="4" :step="0.1" :max="scope.row.oldThisPlanQty + scope.row.maxQty"
                                            @change="changePlanDtlRowM(scope.row)"
                                        />
                                        <span v-if="scope.row.state == 2 || scope.row.state == 4"> {{ scope.row.thisPlanQty }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column v-if="addPlanForm.state == 2" prop="orderQty" label="已下单数量" width="120"/>
                            </el-table>
                        </div>
                    </div>
                    <!--审核历史-->
                    <div id="auditInfo" class="con">
                        <div class="tabs-title" id="">审核历史</div>
                        <div class="e-table">
                            <el-table
                                border
                                :data="addPlanForm.auditList"
                                class="table"
                                :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"/>
                                <el-table-column prop="auditType" label="审核类型" width="160">
                                    <template v-slot="scope">
                                        <span v-if="scope.row.auditType == 1">录入审核</span>
                                        <span v-if="scope.row.auditType == 2">变更审核</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="founderName" label="审核人" width="200"/>
                                <el-table-column prop="gmtCreate" label="审核时间" width="160"/>
                                <el-table-column prop="auditResult" label="审核意见"/>
                            </el-table>
                        </div>
                    </div>
                    <!--变更审核-->
                    <div id="planChanges" class="con">
                        <div class="tabs-title" id="">变更审核</div>
                        <div class="e-table">
                            <el-table
                                border
                                :data="addPlanForm.planChanges"
                                class="table"
                                :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"/>
                                <el-table-column prop="planChangeNo" label="变更计划编号" width="200"/>
                                <el-table-column prop="planNo" label="计划编号" width="200"/>
                                <el-table-column prop="planDate" label="计划日期" width="100">
                                    <template v-slot="scope">
                                        {{ scope.row.planDate | dateStr }}
                                    </template>
                                </el-table-column>
                              <el-table-column prop="contractNo" label="合同编号" width="200px"/>
                                <el-table-column prop="supplierName" label="供应商名称"/>
                                <el-table-column prop="gmtCreate" label="创建时间"/>
                                <el-table-column prop="state" label="变更状态" width="90">
                                    <template v-slot="scope">
                                        <el-tag type="info" v-if="scope.row.state === 0">草稿</el-tag>
                                        <el-tag type="" v-if="scope.row.state === 1">已提交</el-tag>
                                        <el-tag type="success" v-if="scope.row.state === 2">通过</el-tag>
                                        <el-tag type="danger" v-if="scope.row.state === 3">未通过</el-tag>
                                        <el-tag type="warning" v-if="scope.row.state === 4">已作废</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="80">
                                    <template v-slot="scope">
                                        <div
                                            class="pointer" style="color: rgba(33, 110, 198, 1);"
                                            @click="monthPlanDtl(scope.row)"
                                        >详情
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <el-dialog v-dialogDrag title="生成订单" :visible.sync="showOrderDialog" width="80%">
            <div class="e-table" style="margin-left: 20%;">
                <el-table
                    highlight-current-row
                    border
                    :data="addPlanForm.dtls"
                    class="table"
                    :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="materialName" label="物资名称">
                    </el-table-column>
                    <el-table-column prop="spec" label="规格型号">
                    </el-table-column>
                    <el-table-column prop="unit" label="计量单位" width="100">
                    </el-table-column>
                    <!--                    <el-table-column prop="sourceQty" label="数量" width="100">-->
                    <!--                    </el-table-column>-->
                    <!--                        <el-table-column prop="useQty" label="已消耗数量" width="130">-->
                    <!--                        </el-table-column>-->
                    <!--                    <el-table-column prop="maxQty" label="剩余数量" width="130"></el-table-column>-->
                    <el-table-column prop="thisPlanQty" label="本期数量" width="100">
                        <template v-slot="scope">
                            <span> {{ scope.row.thisPlanQty }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderQty" label="已下单数量" width="120">
                        <template v-slot="scope">
                            <span> {{ scope.row.orderQty }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="selectQty" label="选择数量" width="160">
                        <template v-slot="scope">
                            <el-input-number
                                size="mini"
                                v-model="scope.row.selectQty"
                                :min="0"
                                :precision="4"
                                :step="0.1"
                                :max="scope.row.thisPlanQty - scope.row.orderQty"
                                @change="changeSelectQtyM(scope.row)"
                            >
                            </el-input-number>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div class="select-box mt30">
                <div>
                    <!--                        <span>结算方式</span>-->
                    <!--                        <el-select v-model="payMethod" value-key="" placeholder="请选择支付方式">-->
                    <!--                            <el-option v-for="item in payMethods" :key="item.value" :label="item.label" :value="item.value">-->
                    <!--                            </el-option>-->
                    <!--                        </el-select>-->
                </div>
                <div>
                    <span>其他要求</span><input v-model="remarks" type="text" placeholder="填写要求"/>
                </div>
            </div>
            <div class="addr-info center mb20">
                <div class="list-title">
                    <span>收货信息</span>
                    <span style="margin-left: 800px" @click="checkedAddressM">切换收货地址</span>
                </div>
                <div class="addr-content">
                    <p class="mb20">收货地址：{{ receiver.addr }}（{{ receiver.name }}收） </p>
                    <p>收货人：{{ receiver.name }}（{{ receiver.tel }}）</p>
                </div>
            </div>
            <div class="dfa" style="margin-left: 80%">
                <el-button type="primary" @click="submitOrderM">提交订单</el-button>
                <el-button @click="showOrderDialog = false">取消</el-button>
            </div>
        </el-dialog>
        <el-dialog
            v-dialogDrag title="选择收货地址" :visible.sync="addrDialogVisible"
            width="80%" :close-on-click-modal="true"
        >
            <div class="e-table" style="background-color: #ffffff">
                <div class="top" style="height: 50px; padding-left: 10px; ">
                    <div class="left-btn" style="margin-top: 10px">
                        <el-button type="primary" class="btn-greenYellow" @click="createAddress">新增</el-button>
                    </div>
                </div>
                <el-table border :data="addrList" height="520">
                    <el-table-column label="收货地址" label-width="560" prop="addr"></el-table-column>
                    <el-table-column label="联系人" label-width="152" prop="name"></el-table-column>
                    <el-table-column label="联系电话" label-width="110" prop="tel"></el-table-column>
                    <el-table-column label="操作" label-width="">
                        <template v-slot="scope">
                            <span
                                @click="handleEditAddr(scope.row)"
                                style="color: rgba(34, 111, 199, 1);cursor: pointer;"
                            >编辑</span>
                            <span
                                @click="handleCurrentInventoryClick(scope.row)"
                                style="color: rgba(34, 111, 199, 1);cursor: pointer;margin-left: 20px"
                            >选择</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="buttons">
                <el-button @click="addrDialogVisible = false">取消</el-button>
            </div>
        </el-dialog>
        <el-dialog
            v-dialogDrag :title='action' :visible.sync="addDetailDialog"
            width="80%" :close-on-click-modal="true"
        >
            <el-form
                :model="userAddressForm" ref="addAddressRef" :rules="userAddressFormRules"
                :inline="false" label-width="120px" v-loading="shipLoading"
            >
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="收货人：" prop="receiverName">
                            <el-input v-model="userAddressForm.receiverName" placeholder="请输入收货人姓名"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item class="tel" label="手机号码：+86" prop="receiverMobile">
                            <el-input v-model="userAddressForm.receiverMobile" placeholder="请输入手机号码"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="选择地址：" prop="detailAddress">
                            <el-cascader
                                size="large"
                                :options="addressData"
                                v-model="selectAddressOptions"
                                @change="handleAddressChange"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item class="address" label="详细地址：" prop="detailAddress">
                            <el-input v-model="userAddressForm.detailAddress" placeholder="请输入详细收货地址"/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="createAddressM">保存</el-button>
            </span>
        </el-dialog>
        <el-dialog
            v-dialogDrag id="createOrderDialog" title="生成订单" :visible.sync="showOrderDialog" v-loading="submitMonthPlanOrderLoading"
            width="80%" :close-on-click-modal="false"
        >
            <div class="tabs-title" id="">订单信息</div>
            <div class="e-table" style="background-color: #ffffff">
                <el-table
                    border class="table"
                    :data="addPlanForm.dtls"
                    :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="materialName" label="物资名称">
                    </el-table-column>
                    <el-table-column prop="spec" label="规格型号">
                    </el-table-column>
                    <el-table-column prop="unit" label="计量单位" width="100">
                    </el-table-column>
                    <!--<el-table-column prop="sourceQty" label="数量" width="100">-->
                    <!--</el-table-column>-->
                    <!--    <el-table-column prop="useQty" label="已消耗数量" width="130">-->
                    <!--    </el-table-column>-->
                    <!--<el-table-column prop="maxQty" label="剩余数量" width="130"></el-table-column>-->
                    <el-table-column prop="thisPlanQty" label="本期数量" width="100">
                        <template v-slot="scope">
                            <span> {{ scope.row.thisPlanQty }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderQty" label="已下单数量" width="120">
                        <template v-slot="scope">
                            <span> {{ scope.row.orderQty }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="selectQty" label="选择数量" width="160">
                        <template v-slot="scope">
                            <el-input-number
                                size="mini"
                                v-model="scope.row.selectQty"
                                :min="0"
                                :precision="4"
                                :step="0.1"
                                :max="scope.row.thisPlanQty - scope.row.orderQty"
                                @change="changeSelectQtyM(scope.row)"
                            >
                            </el-input-number>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <el-form ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="其他要求：">
                            <el-input clearable maxlength="100" placeholder="填写要求" v-model="remarks"/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <!--            <div class="select-box mt30">-->
            <!--                <div>-->
            <!--                    &lt;!&ndash;                        <span>结算方式</span>&ndash;&gt;-->
            <!--                    &lt;!&ndash;                        <el-select v-model="payMethod" value-key="" placeholder="请选择支付方式">&ndash;&gt;-->
            <!--                    &lt;!&ndash;                            <el-option v-for="item in payMethods" :key="item.value" :label="item.label" :value="item.value">&ndash;&gt;-->
            <!--                    &lt;!&ndash;                            </el-option>&ndash;&gt;-->
            <!--                    &lt;!&ndash;                        </el-select>&ndash;&gt;-->
            <!--                </div>-->
            <!--            </div>-->
            <div class="tabs-title" id="">收货信息</div>
            <div style="height: 20px"></div>
            <el-form ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收货地址：">
                            <span>{{ receiver.addr }}（{{ receiver.name }}收）</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收货人：">
                            <span>{{ receiver.name }}（{{ receiver.tel }}）</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="buttons">
                <el-button type="success" @click="checkedAddressM">切换收货地址</el-button>
                <el-button type="primary" @click="submitOrderM">提交订单</el-button>
                <el-button @click="showOrderDialog = false">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog
            v-loading="changePlanInfoLoading"
            title="变更审核详情"
            v-dialogDrag
            :visible.sync="showChangePlanInfo"
            width="80%"
            :close-on-click-modal="false"
        >
            <el-form :inline="true" ref="addPlanFormRoteRef" :data="changePlanFormDate">
                <el-row>
                    <el-col :span="11" :offset="1">
                        <el-form-item label="变更计划编号：" prop="planChangeNo">
                            {{ changePlanFormDate.planChangeNo }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="合同编号：" prop="contractNo">
                            {{ changePlanFormDate.contractNo }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11" :offset="1">
                        <el-form-item label="当前计划月份：" prop="thisTruePlanDate">
                            {{ changePlanFormDate.thisTruePlanDate }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="变更计划月份：" prop="planDate">
                            {{ changePlanFormDate.planDate }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11" :offset="1">
                        <el-form-item label="计划编号：" prop="planNo">
                            {{ changePlanFormDate.planNo }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="变更单据状态：" prop="demandType">
                            <el-tag type="info" v-if="changePlanFormDate.state === 0">草稿</el-tag>
                            <el-tag type="" v-if="changePlanFormDate.state === 1">已提交</el-tag>
                            <el-tag type="success" v-if="changePlanFormDate.state === 2">通过</el-tag>
                            <el-tag type="danger" v-if="changePlanFormDate.state === 3">未通过</el-tag>
                            <el-tag type="warning" v-if="changePlanFormDate.state === 4">已作废</el-tag>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11" :offset="1">
                        <el-form-item label="单据机构：" prop="orgName">
                            {{ changePlanFormDate.orgName }}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="供应商：" prop="supplierName">
                            {{ changePlanFormDate.supplierName }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="23" :offset="1">
                        <el-form-item label="备注：" prop="remarks">
                            {{ changePlanFormDate.remarks }}
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-tabs v-model="activeName" type="card">
                <el-tab-pane label="明细列表" name="dtl">
                    <div class="e-table" style="background-color: #ffffff">
                        <el-table
                            max-height="372px"
                            border
                            :data="changePlanFormDate.dtls"
                        >
                            <el-table-column prop="materialName" label="物资名称">
                            </el-table-column>
                            <el-table-column prop="spec" label="规格型号">
                            </el-table-column>
                            <el-table-column prop="unit" label="计量单位">

                            </el-table-column>
                            <el-table-column prop="texture" label="材质" width=""></el-table-column>
                            <el-table-column prop="sourceQty" label="总数量">
                            </el-table-column>
                            <el-table-column prop="maxQty" label="剩余数量">
                            </el-table-column>
                            <el-table-column prop="thisTrueQty" label="当前数量">
                            </el-table-column>
                            <el-table-column prop="oldThisPlanQty" label="变更数量">
                            </el-table-column>
                            <el-table-column prop="orderQty" label="已下单数量">
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="审核历史" name="auditInfo">
                    <div class="e-table" style="background-color: #ffffff">
                        <el-table
                            max-height="372px"
                            border
                            :data="changePlanFormDate.auditList"
                            style="min-height: 372px"
                            :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                            :row-style="{ fontSize: '14px', height: '48px' }"
                        >
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column prop="auditType" label="审核类型" width="160">
                                <template v-slot="scope">
                                    <span v-if="scope.row.auditType == 1">录入审核</span>
                                    <span v-if="scope.row.auditType == 2">变更审核</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="founderName" label="审核人" width="200">
                            </el-table-column>
                            <el-table-column prop="gmtCreate" label="审核时间" width="160">
                            </el-table-column>
                            <el-table-column prop="auditResult" label="审核意见">
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div class="buttons">
                <el-button @click="showChangePlanInfo = false">取消</el-button>
            </div>
        </el-dialog>
        <el-dialog v-loading="createOrderLoading" v-dialogDrag id="supplierDialog"  title="推送供方" :visible.sync="showSelectSupplierOrder"  width="80%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div class="tabs-title" id="contractList">推送供方</div>
            <div class="con" v-loading="tableLoading5">
                <div class="e-table"  style="background-color: #fff">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div class="left">
                            <el-input clearable type="text" @blur="getTableData5" placeholder="输入搜索关键字" v-model="keywords5">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData5" />
                            </el-input>
                        </div>
                    </div>
                    <el-table
                        ref="eltableCurrentRow"
                        border
                        style="width: 100%"
                        :data="tableData5"
                        class="table"
                        :max-height="$store.state.tableHeight"
                        @row-click="handleClick"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column label="操作">
                            <template v-slot="scope">
                                <div
                                    class="pointer" style="color: rgba(33, 110, 198, 1);"
                                    @click="pushTwoSupplierRowClick(scope.row)"
                                >推送供应商
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="supplierName" label="供应商名称" width=""></el-table-column>
                        <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <Pagination
                    v-show="tableData5 != null || tableData5.length != 0"
                    :total="paginationInfo5.total"
                    :pageSize.sync="paginationInfo5.pageSize"
                    :currentPage.sync="paginationInfo5.currentPage"
                    @currentChange="getTableData5"
                    @sizeChange="getTableData5"
                />
            </div>
            <div class="buttons">
                <el-button @click="showSelectSupplierOrder = false">返回</el-button>
            </div>
        </el-dialog>
        <div class="buttons">
            <el-button type="success"  @click="planExportM()" >导出计划</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import { create, getDefaultAddress, getList } from '@/api/frontStage/shippingAddr'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import {
    auditPlan,
    cancellationPlan,
    getPlanChangeDtlInfoByPlanNo,
    getPlanDtlInfoByPlanNo,
    submitMonthPlanOrder,
    pushPlanToSupplier,
    closePushDtl,
    updatePlanDtlByPlanId, planExport
} from '@/api/plan/plan'
import { listSupplierByShopId } from '@/api/platform/order/orders'

export default {
    data () {
        return {
            tableData5: [],
            tableLoading5: [],
            createOrderLoading: false,
            screenHeight: 0,
            shipLoading: false,
            changePlanFormDate: {},
            changePlanInfoLoading: false,
            showChangePlanInfo: false,
            remarks: null, // 订单备注
            payMethod: 2,
            payMethods: [
                { label: '路桥结算', value: 2 }
            ],
            userAddressForm: { // 新增编辑地址表单
                detailAddress: null,
            },
            tabsName: 'baseInfo',
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            // 地址
            addressData: regionData, // 地址数据
            selectAddressOptions: [], // 地址选择
            addDetailDialog: false, // 地址编辑新增
            action: '新增',
            userAddressFormRules: {
                receiverName: [
                    { required: true, message: '请输入收件人', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                receiverMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
            },
            addrList: [],
            submitMonthPlanOrderLoading: false,
            addressListLoading: false,
            receiver: {
                name: null,
                tel: null,
                addr: null,
            },
            paginationInfo5: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            masterOrderItemSelectRowData: [],
            showSelectSupplierOrder: false,
            keywords5: null,
            addrDialogVisible: false,
            showOrderDialog: false,
            changePlanDtlRowDate: [],
            changeSelectQtyRowDate: [],
            activeName: 'dtl',
            addPlanFormRote: {
                planNo: [
                    { required: true, message: '请输入计划编号', trigger: 'blur' },
                    { min: 1, max: 36, message: '超过限制', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                contractNo: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                planDate: [
                    { required: true, message: '请选择计划日期', trigger: 'blur' },
                ],
                supplierName: [
                    { required: true, message: '请选择合同', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
            },
            showLoading: false,
            addPlanForm: {},
            pickerOptions: {
                // disabledDate (time) {
                //     return time.getTime() < Date.now()
                // },
            },
            winEvent: {},
            topHeight: 70,
            lastConHeight: 0,
        }
    },
    created () {
        this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        this.getPlanDtlM()
    },
    mounted () {
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'dtl', 'auditInfo', 'planChanges']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) return
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    return $item ? $item.offsetTop : null
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => document.getElementById(item).offsetTop)
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        let { name } = this.$route.query
        if (name) {
            setTimeout(() => {
                this.onChangeTab({ name })
                this.tabsName = name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) return
            let newDateSr = dateStr.split('-')
            // let day = newDateSr[2].split(' ')[0]
            return newDateSr[0] + '年' + newDateSr[1] + '月'
        },
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 150
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        planExportM () {
            console.log('qqq', this.addPlanForm.planId)
            this.clientPop('info', '您确定要导出计划数据？', async () => {
                planExport({ id: this.addPlanForm.planId } ).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '物资月度供应计划表.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.$message.success('商品导出操作成功')
                }).catch(() => {

                })
            })
        },
        masterOrderItemSelectRow (value) {
            this.masterOrderItemSelectRowData = value
        },

        closePushDtlM (row) {
            this.clientPop('info', '您确定要取消推送供应商吗？', async () => {
                closePushDtl({ id: row.planDtlId }).then(res=>{
                    if (res.code == 200) {
                        this.$message({
                            message: '取消成功',
                            type: 'warning',
                        })
                    }
                    this.getPlanDtlM()
                })

            })

        },
        pushTwoSupplierRowClick (row) {
            const newArray = this.createTwoOrderRowData.map(item => item.planDtlId)
            let params = {
                planDtlIds: newArray,
                twoSupplierId: row.supplierId
            }

            pushPlanToSupplier(params).then(res=>{
                if (res.code == 200) {
                    this.$message({
                        message: '推送供应商成功',
                        type: 'success'
                    })
                    this.getPlanDtlM()
                }else {
                    this.$message({
                        message: '推送失败',
                        type: 'warning'
                    })
                }
            })
            this.showSelectSupplierOrder = false

        },
        masterOrderItemRowClick (row) {
            row.flag = !row.flag
            this.$refs.masterOrderItemRef.toggleRowSelection(row, row.flag)
        },
        selectSupplierM () {
            //去掉已经生成订单或者已经选择供应商的明细
            let data =  this.masterOrderItemSelectRowData.filter(t => {
                if( t.twoSupplierId == null) {
                    return true
                }else {
                    return false
                }
            })
            if(data.length == 0) {
                return this.$message.error('请选择未生成二级订单或者未分配的明细')
            }
            this.createTwoOrderRowData = data
            this.getTableData5()
            this.showSelectSupplierOrder = true
        },
        getTableData5 () {
            let params = {
                page: this.paginationInfo5.currentPage,
                limit: this.paginationInfo5.pageSize,
            }
            if(this.keywords5 != null) {
                params.keywords = this.keywords5
            }
            this.tableLoading5 = true
            listSupplierByShopId(params).then(res => {
                this.tableData5 = res.list
                this.paginationInfo5.total = res.totalCount
                this.paginationInfo5.pageSize = res.pageSize
                this.paginationInfo5.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading5 = false
            })
        },
        // 获取地址
        getAddress () {
            // 获取收货地址
            this.addressListLoading = true
            getList({ page: 1, limit: 30 }).then(res => {
                if (!res.list[0]) return
                let address = []
                // 显示默认地址
                res.list.forEach(item => {
                    let obj = {
                        addressId: item.addressId,
                        checked: false,
                        addr: item.detailAddress,
                        name: item.receiverName,
                        tel: item.receiverMobile,
                        province: item.province,
                        city: item.city,
                        county: item.county,
                    }
                    address.push(obj)
                })
                this.addrList = address
                this.addressListLoading = false
            }).catch(() => {
                this.addressListLoading = false
            })
        },
        // 获取默认地址
        getDefaultAddressM () {
            this.submitMonthPlanOrderLoading = true
            getDefaultAddress().then(res => {
                if (res != null) {
                    this.receiver.name = res.receiverName
                    this.receiver.tel = res.receiverMobile
                    this.receiver.addr = res.detailAddress
                }
                this.submitMonthPlanOrderLoading = false
            }).catch(() => {
                this.submitMonthPlanOrderLoading = false
            })
        },
        // 创建编辑地址统一接口
        createAddressM () {
            this.$refs.addAddressRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要新增吗？', async () => {
                        let res = await create(this.userAddressForm)
                        if (res.code !== 200) return
                        this.$message({ message: res.message, })
                        this.getAddress()
                        this.addDetailDialog = false
                        this.shipLoading = false
                    })
                }
            })
        },
        // 地址表格点击
        handleCurrentInventoryClick (row) {
            this.receiver = row
            this.addrDialogVisible = false
        },
        // 地址选择
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.userAddressForm.province = province
            this.userAddressForm.city = city
            this.userAddressForm.county = county
            this.userAddressForm.detailAddress = province + city + county
        },
        // 编辑地址
        handleEditAddr (row) {
            this.action = '编辑收货地址'
            this.userAddressForm = {
                addressId: row.addressId,
                detailAddress: row.addr,
                receiverName: row.name,
                receiverMobile: row.tel,
            }
            //地址选择器回显
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
            this.addDetailDialog = true
        },
        // 创建
        createAddress () {
            this.action = '新增收货地址'
            this.userAddressForm = { detailAddress: null, }
            this.selectAddressOptions = []
            this.addDetailDialog = true
        },
        // 切换地址
        checkedAddressM () {
            this.getAddress()
            this.addrDialogVisible = true
        },
        // 选择数量变化
        changeSelectQtyM (row) {
            if (row.selectQty == null) {
                row.selectQty = 0
            }
            if (this.changeSelectQtyRowDate.length === 0) {
                this.changeSelectQtyRowDate.push({
                    planDtlId: row.planDtlId,
                    materialName: row.materialName,
                    texture: row.texture,
                    materialId: row.materialId,
                    classPathId: row.classPathId,
                    classPathName: row.classPathName,
                    planNo: row.planNo,
                    spec: row.spec,
                    contractDtlId: row.contractDtlId,
                    unit: row.unit,
                    selectQty: row.selectQty
                })
                return
            }
            let flag = false
            this.changeSelectQtyRowDate.forEach(t => {
                if (t.planDtlId === row.planDtlId) {
                    t.selectQty = row.selectQty
                    flag = true
                }
            })
            if (!flag) {
                this.changeSelectQtyRowDate.push({
                    planDtlId: row.planDtlId,
                    materialName: row.materialName,
                    texture: row.texture,
                    materialId: row.materialId,
                    classPathId: row.classPathId,
                    classPathName: row.classPathName,
                    planNo: row.planNo,
                    spec: row.spec,
                    contractDtlId: row.contractDtlId,
                    unit: row.unit,
                    selectQty: row.selectQty
                })
            }
        },
        // 生成订单
        createMonthOrderM () {
            let isSubmitFlag = this.userInfo.isSubmitOrder
            let isInterior = this.userInfo.isInterior
            if (isInterior === 1) {
                if (isSubmitFlag == null || isSubmitFlag === 0) {
                    return this.$message.error('没有下单权限，请联系管理员！')
                }
            }
            this.getDefaultAddressM()
            this.showOrderDialog = true
        },
        savePlanM (isSubmit) {
            this.$refs.addPlanFormRoteRef.validate(valid => {
                if (!valid) this.$message({ message: '请检查非空输入框', type: 'error' })
                let params = {
                    planId: this.addPlanForm.planId,
                    planDate: this.addPlanForm.planDate,
                    remarks: this.addPlanForm.remarks,
                    dtls: this.changePlanDtlRowDate,
                    isSubmit: isSubmit != null && isSubmit == 1 ? 1 : 0
                }
                this.showLoading = true
                updatePlanDtlByPlanId(params).then(res => {
                    let msg = res.code === 200 ? '操作成功' : res.message
                    let msgType = res.code === 200 ? 'suc' : 'error'
                    this.clientPop(msgType, msg, () => this.getPlanDtlM())
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        submitOrderM () {
            let newDtlArr = this.changeSelectQtyRowDate.filter(t => {
                return t.selectQty != 0
            })
            if (newDtlArr.length === 0) {
                return this.$message.error('未选择数量！')
            }
            if (this.receiver.tel == null) {
                return this.$message.error('请选择收货地址！')
            }
            newDtlArr.forEach(t => {
                t.receiverName = this.receiver.name
                t.receiverMobile = this.receiver.tel
                t.receiverAddress = this.receiver.addr
                t.orderRemark = this.remarks
                t.payWay = this.payMethod
                t.planId = this.addPlanForm.planId
                t.contractNo = this.addPlanForm.contractNo
                t.contractId = this.addPlanForm.contractId
            })
            this.clientPop('info', '您确认要提交订单吗？', async () => {
                this.submitMonthPlanOrderLoading = true
                submitMonthPlanOrder({ dtos: newDtlArr }).then(res => {
                    if (res.code === 200) {
                        this.clientPop('suc', '提交成功！', () => {
                            this.getPlanDtlM()
                            this.showOrderDialog = false
                        })
                    }
                }).finally(() => {
                    this.submitMonthPlanOrderLoading = false
                })
            })
        },
        // 月供计划明细
        monthPlanDtl (row) {
            this.showChangePlanInfo = true
            this.changePlanInfoLoading = true
            getPlanChangeDtlInfoByPlanNo({ planChangeNo: row.planChangeNo }).then(res => {
                this.changePlanFormDate = res
            }).finally(() => {
                this.changePlanInfoLoading = false
            })
        },
        cancellationPlanM () {
            this.clientPop('info', '您确定要废进行作废操作吗？', async () => {
                this.showLoading = true
                let res = await cancellationPlan([this.addPlanForm.planId])
                if (res.code === 200) {
                    this.$message.success('操作成功')
                    this.getPlanDtlM()
                }
                this.showLoading = false
            })
        },
        auditPlanM (state, title) {
            this.clientPop('info', '您确定进行【' + title + '】操作吗！', async () => {
                if (state === 0) {
                    this.$prompt('未通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            planId: this.addPlanForm.planId,
                            isOpen: 0,
                            auditResult: value,
                        }
                        auditPlan(params).then(res => {
                            if (res.code === 200) {
                                this.$message.success('操作成功')
                                this.getPlanDtlM()
                            }
                        })
                    })
                } else {
                    let params = {
                        planId: this.addPlanForm.planId,
                        isOpen: 1,
                    }
                    auditPlan(params).then(res => {
                        if (res.code === 200) {
                            this.$message.success('操作成功')
                            this.getPlanDtlM()
                        }
                    })
                }
            })
        },
        getPlanDtlM () {
            this.showLoading = true
            getPlanDtlInfoByPlanNo({ planNo: this.$route.query.planNo }).then(res => {
                this.addPlanForm = res
            }).finally(() => {
                this.showLoading = false
            })
        },
        changePlanDtlRowM (row) {
            // 如果把数量增大了
            // if(row.thisPlanQty > row.oldThisPlanQty) {
            //     // 判断大于的数量是否超过剩余数量
            //     let num = row.thisPlanQty - row.oldThisPlanQty
            //     if(num > row.maxQty) {
            //         row.thisPlanQty = row.oldThisPlanQty + row.maxQty
            //     }
            // }
            let { thisPlanQty, planDtlId } = row
            if (thisPlanQty == null) {
                row.thisPlanQty = 1
            }
            if (this.changePlanDtlRowDate.length === 0) {
                this.changePlanDtlRowDate.push({
                    planDtlId,
                    thisPlanQty: row.thisPlanQty,
                })
                return
            }
            let flag = false
            this.changePlanDtlRowDate.forEach(t => {
                if (t.planDtlId === planDtlId) {
                    t.thisPlanQty = row.thisPlanQty
                    flag = true
                }
            })
            if (!flag) {
                this.changePlanDtlRowDate.push({
                    planDtlId: planDtlId,
                    thisPlanQty: row.thisPlanQty
                })
            }
        },
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder/twoOrder')
        },
        onChangeTab (e) {
            // const height = $('#' + e.name).offset().top
            const height = document.getElementById(e.name).offsetTop
            try {
                // $('#tabs-content')[0].scrollTo(height < 0 ? 0 : height, 500)
                this.$refs['container'].scrollTop = height
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
    }
}
</script>

<style lang='scss' scoped>
.warningTabs {
    padding-top: 70px;
}

#tabs-content {
    scroll-behavior: smooth;
}

/deep/ .el-tabs {
    height: 100%;

    .el-tabs__content {
        padding-bottom: 70px;
    }
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}

/deep/ .el-dialog {
    .el-dialog__body {
        margin-left: 20px;
        height: 580px;
        margin-top: 0px;
    }
}
</style>
