<template>
    <main :style="{ padding: isUserCenterPage ? 0 : '' }">
        <div class="compare-box center p20" :style="{ width: isUserCenterPage ? '100%' : ''}">
            <div class="top-row dfb">
                <div class="left">
                    <span>商品对比</span>
                    <el-checkbox v-model="highlightDifference">高亮显示不同项</el-checkbox>
                </div>
                <span>比价时间：{{compareTime | dateStr}}</span>
                <div class="right">
<!--                    <el-button  class="pointer mt10" @click="$router.go(-1)">返回</el-button>-->
                    <span class="pointer" @click="exportComparisonData">
                        <i class="el-icon el-icon-download"/>导出数据
                    </span>
                    <span class="ml10 pointer" @click="$router.push('/user/productCompare')" v-if="!isUserCenterPage">查看对比历史</span>
                    <span class="ml10 pointer" @click="generateHistory" v-if="isUserCenterPage">重新比价</span>
                </div>
            </div>
            <el-table border :show-header="false" :data="transData" :cell-style="tableRowStyle" v-loading="loadingState">
                <el-table-column
                    v-for="(item, index) in transTitle" :label="item"
                    :key="index" align="center" :width="index === 0 ? '198px' : ''"
                >
                    <template v-slot="scope">
                        <img
                            class="product-img pointer"
                            @click="openWindowTab({path:'/mFront/productDetail',query:{productId: productIds[index - 1]}})"
                            :src="scope.row[index] ? imgUrlPrefixAdd + scope.row[index] : require('@/assets/images/img/queshen5.png')"
                            v-if="index > 0 && scope.row[0] === '商品'"
                            alt=""
                        >
<!--                        <div>-->
                            <div class="pointer"
                                v-else-if="index > 0 && scope.row[0] === '名称'"
                                @click="openWindowTab({path:'/mFront/productDetail',query:{productId: productIds[index - 1]}})"
                            >{{ scope.row[index] }}
                            </div>
                            <div v-else>{{ scope.row[index] }}</div>
<!--                        </div>-->
                    </template>
                </el-table-column>
            </el-table>

        </div>
    </main>
</template>

<script>
// eslint-disable-next-line
import { generateComparisonHistory, getComparisonDetail, exportPdf, reCompare } from '@/api/frontStage/productCompare'
import { hideLoading, showLoading } from '@/utils/common'

export default {
    name: 'index',
    data () {
        return {
            compareId: '',
            highlightDifference: false,
            loadingState: false,
            originData: [],
            originTitle: ['商品', '名称', '价格', '单位', '规格型号', '品牌', '服务支持', '供应商'], // originTitle 该标题为 正常显示的标题, 数组中的顺序就是上面数据源对象中的字段标题对应的顺序
            transTitle: [''], // transTitle 该标题为转化后的标题, 注意多一列,  因为原来的标题变成了竖着显示了, 所以多一列标题, 第一个为空即可
            transData: [],
            productIds: [],
            compareTime: null

        }
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return dateStr.split(' ')[0]
        },
    },
    computed: {
        isUserCenterPage () {
            return this.$route.path === '/user/productCompareDetail'
        },
    },
    methods: {
        handleReCompare () {
            showLoading()
            reCompare(this.productIds, this.compareId).then(res => {
                if (res) this.getHistoryDetail()
                this.loadingState = false
            }).finally(()=>{
                hideLoading()
            })
        },
        highlightRow (row) {
            if (row[0] === '商品' || !this.highlightDifference) return false
            let highlightState = false
            let valueArr = row.slice(1)
            valueArr.forEach(item => {
                valueArr.forEach(subItem => {
                    if (item !== subItem) highlightState = true
                })
            })
            return highlightState
        },
        // eslint-disable-next-line
        tableRowStyle ({ row, column, rowIndex, columnIndex }) {
            let style = { height: '60px', fontSize: '14px', color: '#333' }
            if (columnIndex === 0) {
                style.backgroundColor = '#f7f7f7'
            }
            if (this.highlightRow(row)) style.color = '#D43030'
            if (rowIndex === 0) return { ...style, height: '250px' }
            if (rowIndex === 2 && columnIndex > 0) return { ...style, fontSize: '18px' }
            if (rowIndex === 6) return { ...style, height: '100px' }
            return style
        },
        exportComparisonData () {
            showLoading()
            exportPdf({ id: this.compareId }).then(res => {
                let blob = new Blob([res], { type: 'application/pdf' })
                let link = document.createElement('a')
                link.href = URL.createObjectURL(blob)
                link.download = '比价.pdf'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            }).finally(()=>{
                hideLoading()
            })
        },
        generateHistory () {
            this.loadingState = true
            let list
            if (this.isUserCenterPage) {
                list = this.productIds
            }else {
                list = JSON.parse(this.$route.query.list)
            }
            generateComparisonHistory(list).then(res => {
                this.compareId = res[0].compareId
                this.compareTime = res[0].gmtCreate
                this.setOriginData(res)
                // 先清空productList
                this.productIds = []
                res.map(item=>{
                    this.productIds.push(item.productId)
                })
                this.convertData()
                this.loadingState = false
            })
        },
        getHistoryDetail () {
            this.loadingState = true
            let { id } = this.$route.query
            getComparisonDetail({ id }).then(res => {
                this.compareId = res.productCompare.compareId
                this.compareTime = res.productCompare.compareTime
                this.setOriginData(res.productList)
                // 先清空productList
                this.productIds = []
                res.productList.map(item=>{
                    this.productIds.push(item.productId)
                })
                this.convertData()
                this.loadingState = false
            })
        },
        setOriginData (list) {
            this.originData = list.map(item => {
                console.log(item, 11)
                return {
                    productMinImg: item.productMinImg,
                    productName: item.productName,
                    sellPrice: item.sellPrice,
                    unit: item.unit,
                    skuName: item.skuName,
                    brandName: item.brandName,
                    service: item.isInternalSettlement ? '可路桥结算' : '不可路桥结算',
                    supplierName: item.isBusiness ? (item.shopId == '1663065245837488130' ? '四川路桥建设集团股份有限公司物资分公司' : '四川路桥建设集团物资有限责任公司') : item.supplierName
                }
            })
        },
        convertData () {
            this.transTitle = ['']
            this.transTitle = this.transTitle.concat(this.originData.map(() => ''))
            // 数组按矩阵思路, 变成转置矩阵
            let matrixData = this.originData.map(row => {
                row.sellPrice = '￥' + row.sellPrice.toFixed(2) + ' / ' + row.unit
                let arr = []
                for (let key in row) {
                    arr.push(row[key])
                }
                return arr
            })
            // 加入标题拼接最终的数据
            this.transData = matrixData[0].map((col, i) => {
                return [this.originTitle[i], ...matrixData.map(row => row[i])]
            })
        },
    },
    created () {
        this.isUserCenterPage ? this.getHistoryDetail() : this.generateHistory()
    },
}
</script>

<style scoped lang="scss">
main {
    height: 100% !important;
    padding: 20px 0 150px;
    background-color: rgba(245, 245, 245, 1);
}

.compare-box {
    width: 1326px;
    background-color: #fff;
}

.top-row {
    margin-bottom: 17px;

    .left {
        span:first-child {
            margin-right: 30px;
            font-size: 24px;
            color: black;
        }

        .el-checkbox {
            font-size: 14px;
            color: rgba(51, 51, 51, 1);
        }
    }

    .right {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
    }
}

.product-img {
    width: 160px;
    height: 160px;
}

</style>