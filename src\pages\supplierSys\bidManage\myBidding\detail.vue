<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div v-loading='formLoading' class="tabs warningTabs" style="padding: 70px 0 0;">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="基本信息" name="baseInfo" :disabled="clickTabFlag"></el-tab-pane>
                <el-tab-pane label="竞价说明" name="bidNotice" :disabled="clickTabFlag"></el-tab-pane>
                <el-tab-pane label="报价" name="raisePrice" :disabled="clickTabFlag"></el-tab-pane>
                <div id="tabs-content">
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">基本信息</div>
                        <el-form :model="rowData" :data="rowData" ref="form" label-width="200px">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="编号：" prop="biddingSn">
                                        {{ rowData.biddingSn }}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="竞价标题：" prop="title">
                                        {{ rowData.title }}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="发起单位：" prop="createOrgName">
                                        {{ rowData.createOrgName }}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
<!--                                <el-col :span="12">-->
<!--                                    <el-form-item label="开始时间：" prop="startTime">-->
<!--                                        {{ rowData.startTime }}-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
                                <el-col :span="12">
                                    <el-form-item label="商品类型：" prop="startTime">
                                        <span v-if="rowData.productType === 0"><el-tag type="success">低值易耗品</el-tag></span>
                                        <span v-if="rowData.productType === 1" > <el-tag >大宗临购</el-tag></span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="结束时间：" prop="endTime">
                                        {{ rowData.endTime }}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="含税总金额：" prop="bidRateAmount">
                                        {{ rowData.bidRateAmount }}
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="不含税总金额：" prop="bidAmount">
                                        {{ rowData.bidAmount }}
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="增值税税率（%）：" prop="taxRate">
                                        <template>
                                            <el-input
                                                type="number" :disabled="rowData.editAble === 0" @change="calChangeTaxRate" :controls="false"
                                                :max="100" v-model="rowData.taxRate"
                                            />
                                        </template>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <div class="btns dfc">
                            <el-tooltip placement="top" content="暂存报价数据至系统">
                            <el-button type="primary" :disabled="rowData.editAble === 0"
                                @click="handleUpdate">确认修改</el-button>
                            </el-tooltip>
                            <el-tooltip placement="top" content="下载系统生成的报价文件">

                            <el-button type="success" style="margin: 0 100px;" :disabled="rowData.editAble === 0"
                                @click="downlad">下载报价文件</el-button>
                            </el-tooltip>
                            <el-tooltip placement="top" content="下载已经提交的报价函">

                            <el-button type="primary" v-if="this.fileStatus"
                                @click="handleDownloadBidFile(rowData.bidFile)">下载报价函</el-button>
                            </el-tooltip>

                            <el-upload :disabled="rowData.editAble === 0" style="display: inline-block;margin-left: 10px"
                                action="fakeaction" multiple :limit="1" :show-file-list="true" :file-list="fileList"
                                :before-upload="beforeOneOfFilesUpload" :http-request="uploadOneOfFiles"
                                :on-remove="handleRemove">
                                <el-tooltip placement="top">
                                    <el-button :disabled="rowData.editAble === 0" type="success">上传报价函</el-button>
                                </el-tooltip>
                                <!--                <div slot="tip" class="el-upload__tip">只能上传excel文件，且不超过5M</div>-->
                            </el-upload>
                        </div>
                    </div>

                    <el-dialog title="商品明细" :visible.sync="showInfo" width="50%">
                        <!--商品明细查看-->
                        <div id="baseInfCon" class="con">
                            <div class="tabs-title" id="baseInfo">物资信息</div>
                            <div style="width: 100%" class="form">
                                <el-form :model="rowInfo" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                    <el-row>
                                        <!--                    <el-col :span="12">-->
                                        <!--                      <el-form-item label="物资编号：">-->
                                        <!--                        <span>{{ rowInfo.biddingProductId }}</span>-->
                                        <!--                      </el-form-item>-->
                                        <!--                    </el-col>-->
                                        <el-col :span="12">
                                            <el-form-item label="物资名称：">
                                                <span>{{ rowInfo.productName }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="规格型号：">
                                                <span>{{ rowInfo.spec }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="商品材质：">
                                                <span>{{ rowInfo.productTexture }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="计量单位：">
                                                <span>{{ rowInfo.unit }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="数量：">
                                                <span>{{ rowInfo.num }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="供货时间：">
                                                <span>{{ rowInfo.deliveryDate }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="供货地址：">
                                                <span>{{ rowInfo.deliveryAddress }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col>
                                            <el-form-item label="最高单价：">
                                                <span>{{ rowInfo.referencePrice }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="24">
                                            <el-form-item label="备注：">
                                                <span>{{ rowInfo.remarks || '' }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </div>

                            <!--              <span slot="footer" class="dialog-footer">-->
                            <!--&lt;!&ndash;    <el-button @click="showInfo = false">取 消</el-button>&ndash;&gt;-->
                            <!--    <el-button type="primary" @click="showInfo = false">确 定</el-button>-->
                            <!--  </span>-->
                        </div>

                        <!--            <el-descriptions border title="商品明细">-->

                        <!--              <el-descriptions-item label="商品名称">-->
                        <!--                  商品名称-->
                        <!--                <el-tag type="success">{{ rowInfo.productName }}</el-tag>-->
                        <!--              </el-descriptions-item>-->
                        <!--              <el-descriptions-item label="品牌名称">{{ rowInfo.brand }}</el-descriptions-item>-->

                        <!--              <el-descriptions-item label="规格型号">{{ rowInfo.spec }}</el-descriptions-item>-->
                        <!--              <el-descriptions-item label="商品材质">{{ rowInfo.productTexture }}</el-descriptions-item>-->

                        <!--              <el-descriptions-item label="计量单位">{{ rowInfo.unit }}</el-descriptions-item>-->
                        <!--              <el-descriptions-item label="数量">{{ rowInfo.num }}</el-descriptions-item>-->
                        <!--              <el-descriptions-item label="参考单价">{{ rowInfo.referencePrice }}</el-descriptions-item>-->
                        <!--              <el-descriptions-item label="供货地点">{{ rowInfo.deliveryAddress }}</el-descriptions-item>-->
                        <!--              <el-descriptions-item label="供货时间">{{ rowInfo.deliveryDate }}</el-descriptions-item>-->
                        <!--              <el-descriptions-item label="备注">{{ rowInfo.remarks }}</el-descriptions-item>-->

                        <!--            </el-descriptions>-->
                        <div class="buttons">
                            <el-button @click="showInfo = false">关闭</el-button>
                        </div>
                    </el-dialog>
                    <!-- 竞价说明 -->
                    <div id="bidNotice" class ="con" style="margin-bottom: 10px">
                        <div class="tabs-title" id="bidNotice">竞价说明</div>
                        <div v-html="rowData.biddingNotice" class="formatted-text"></div>
                    </div>
                    <div id="raisePriceCon" class="con">
                        <div class="tabs-title" id="raisePrice">报价</div>
                        <div class="e-table">
                            <el-table class="table" :data="tableData" :height="300" border highlight-current-row>
                                <el-table-column label="操作" width="100">
                                    <template v-slot="scope">
                                        <span class="action" @click="handleViewRowDetail(scope)">查看明细</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="序号" type="index" width="" />
                                <el-table-column label="物资名称" prop="productName" width="" />
                                <el-table-column label="商品材质" prop="productTexture" width="100" />
                                <el-table-column label="规格型号" prop="spec" width="" />
                                <el-table-column label="预估数量" prop="num" width="100" />
                                <el-table-column label="含税到场单价（元/计量单位）" prop="bidRatePrice" width="">
                                    <template v-slot="scope">
                                        <el-input :disabled="rowData.editAble === 0" :controls="false" type="number"
                                            v-model="scope.row.bidRatePrice" />
                                    </template>
                                </el-table-column>
<!--                                <el-table-column label="增值税税率（%）" prop="taxRate" width="160">-->
<!--                                    <template v-slot="scope">-->
<!--                                        <el-input type="number" :disabled="rowData.editAble === 0" :controls="false"-->
<!--                                            :max="100" v-model="scope.row.taxRate" />-->
<!--                                    </template>-->
<!--                                </el-table-column>-->
                                <el-table-column label="不含税到场单价（元/计量单位）" prop="bidPrice" width="" />
                                <el-table-column label="含税总金额" prop="bidRateAmount" width="100" />
                                <el-table-column label="不含税总金额" prop="bidAmount" width="100" />
                                <el-table-column label="最高单价" prop="referencePrice" width="" />
                                <el-table-column label="备注" prop="remarks" width="">
                                    <template v-slot="scope">
                                        <el-input type="text" :disabled="rowData.editAble === 0"
                                            v-model="scope.row.remarks" />
                                    </template>
                                </el-table-column>
                            </el-table>
<!--                            <div class="dfc mt20">-->
<!--                                <el-button :disabled="rowData.editAble === 0" type="primary"-->
<!--                                    @click="submitBid">提交报价</el-button>-->
<!--                                <el-button @click="handleClose">返回</el-button>-->
<!--                            </div>-->
                        </div>
                    </div>
                </div>
            </el-tabs>
            <div class="dfc mt20 fixed-bottom" >
                <el-button
                    :disabled="rowData.editAble === 0" type="primary"
                    @click="submitBid"
                >提交报价
                </el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
        <!--        <div class="buttons">-->
        <!--            <el-button type="success" @click="onSave">保存</el-button>-->
        <!--            <el-button @click="handleClose">返回</el-button>-->
        <!--        </div>-->
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
// import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle, toFixed } from '@/utils/common'
import {
    downloadFile,
    getBidInfoList,
    udateOfferBid,
    createFileRecordDelete,
    saveBid, getProductInfo
} from '@/api/supplierSys/bidManage/myBidding/myBidManage'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { hideLoading, showLoading } from '@/utils/common'
export default {
    data () {
        return {
            // 限价提示
            showLimitedPrice: false,
            shopDialogLoading: false,
            // 数据加载
            formLoading: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            rowData: {
                bidRecordId: 0,
                bidRateAmount: 0,
                bidAmount: 0,
                startTime: '',
                endTime: '',
                createOrgName: '',
                publicityState: 0,
                title: '',
                biddingSn: '',
                status: 0
            }, // 跳转过来的数据
            tableData: [],
            showForm: false,
            // 商品库
            showDeviceDialog: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            showShopDialog: false, // 店铺
            //文件上传表单数据
            formData: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            biddingSn: null,
            fileLoading: false,
            // 文件上传
            fileList: [],
            showFileList: false, //文件列表是否显示,默认不显示
            fileUrl: '', //上传文件的域名地址
            limitNum: 1, //文件上传个数限制
            // 明细查看弹窗
            showInfo: false,
            rowInfo: {},
            // 控制文件下载
            fileStatus: false

        }
    },
    components: {
        // Pagination,
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // 计算属性配合watch监听tableData，否则无法获取旧值
        newTableData () {
            return JSON.parse(JSON.stringify(this.tableData))
        },
        // 使用计算属性处理文本，替换换行符为 <br> 标签
        formattedText () {
            return this.rowData.biddingNotice.replace(/\n/g, '<br>')
        },
        // 含税总金额
        totalRateAmountWithTax () {
            return this.rowData.bidRateAmount
        },
        totalAmountWithTax () {
            return this.rowData.bidAmount
        },
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight (newVal) {
            $('#tabs-content').height(newVal - 71)
        },
        // 计算属性配合watch监听tableData，否则无法获取旧值
        newTableData: {
            handler (newVal, oldVal) {
                // this.rowData.bidRateAmount = 0
                // this.rowData.bidAmount = 0
                let rate = this.rowData.taxRate
                let len = newVal.length
                if (len !== oldVal.length) return
                for (let i = 0; i < len; i++) {
                    // if (newVal[i].bidRatePrice != oldVal[i].bidRatePrice) {
                    if(newVal[i].bidRatePrice < 0) {
                        newVal[i].bidRatePrice = 0
                        this.tableData[i].bidRatePrice = 0
                        return
                    }
                    // 限价
                    // 需要限价
                    this.showAlert = false
                    if (this.tableData[i].referencePrice != null && this.tableData[i].referencePrice != '') {
                        // 参考单价变化
                        let referencePrice = this.tableData[i].referencePrice
                        if (Number(referencePrice) < this.tableData[i].bidRatePrice && Number(referencePrice) != 0) {
                            // 限价提示
                            this.showAlert = true
                            this.$message.error('含税单价不能大于最高单价')
                            return
                        }
                    }else {
                        this.tableData[i].referencePrice = Number(0.00)
                    }
                    //计算含税总金额
                    let bidRatePrice = newVal[i].bidRatePrice
                    this.tableData[i].bidRatePrice = bidRatePrice
                    this.tableData[i].bidRateAmount = Number(bidRatePrice) * newVal[i].num
                    // 计算不含税单价
                    let bidPrice = this.calNotRate(rate, bidRatePrice)
                    this.tableData[i].bidPrice = bidPrice
                    // 计算不含税总金额
                    this.tableData[i].bidAmount = Number(bidPrice) * newVal[i].num

                    // }
                }
                // 计算总金额
                this.calcAllBidRatePriceAmount()

            },
            deep: true
        },
    },

    async activated () {
        this.biddingSn = this.$route.query.id
        await this.getDataInfo()
    },
    created () {

    },
    mounted () {
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'raisePrice']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) return
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    methods: {
        // 计算不含税单价
        calNotRate (taxRate, ratePrice) {
            return toFixed((ratePrice) / (1 + ( taxRate / 100)), 2)
        },
        calChangeTaxRate () {
            if (this.rowData.taxRate != null) {
                if (this.rowData.taxRate < 0 || this.rowData.taxRate > 100) {
                    this.rowData.taxRate = 0
                    this.$message.error('税率不能小于0或大于100')
                    return
                }
                this.rowData.taxRate = this.formatNumber(this.rowData.taxRate)
                let taxRate = this.rowData.taxRate
                this.tableData.forEach(item => {
                    // 计算含税单价
                    // item.bidRatePrice = toFixed(Number(item.netPrice) + Number(item.fixationPrice), 2)
                    // 计算含税总金额
                    item.bidRateAmount = toFixed(Number(item.bidRatePrice) * Number(item.num), 2)
                    // 计算不含税单价
                    item.bidPrice = toFixed(Number(item.bidRatePrice) / (1 + Number(taxRate) / 100), 2)
                    // 计算不含税总金额（含税总金额和税率计算）
                    item.bidAmount = toFixed(Number(item.bidRateAmount) / (1 + Number(taxRate) / 100), 2)
                    // item.bidAmount = toFixed(Number(item.bidPrice) * Number(item.num), 2)
                })
                this.calcAllBidRatePriceAmount()
            }else {
                this.rowData.taxRate = 0
            }
        },
        /**
         * 直接截取不四舍五入
         * @param input
         * @returns {string}
         */
        formatNumber (input) {
            // 将输入转换为数字
            let number = parseFloat(input)
            // 如果转换成功
            if (!isNaN(number)) {
                // 转换为字符串
                let numberString = number.toString()

                // 找到小数点的位置
                let decimalIndex = numberString.indexOf('.')

                // 如果存在小数点
                if (decimalIndex !== -1) {
                    // 截取小数点后两位
                    numberString = numberString.substring(0, decimalIndex + 3)
                }

                // 返回字符串格式的数字
                return numberString
            } else {
                // 转换失败，输入不是有效的数字
                return 'Invalid Input'
            }
        },
        // 保存报价单数据并状态
        async submitBid () {
            if (this.formData == null || this.formData.length == 0) {
                this.$message.error('请上传报价函')
                return
            }
            if (!this.rowData.bidRecordId || this.rowData.bidRecordId === 'null') {
                this.$message.error('请先确认修改信息')
                return
            }
            showLoading()
            await saveBid(this.formData[0], this.rowData.bidRecordId).then(res => {
                if (res.code === 200) {
                    this.$message.success('报价提交成功')
                    this.getDataInfo()
                }
            }).finally(() => {
                this.tableLoading = false
                hideLoading()
            }
            )

        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if (size > 50) {
                this.$message.error('文件大小不能超过50M')
                return false
            }
            return true
        },

        // 多文件上传
        async uploadOneOfFiles (data) {
            let file = data.file
            showLoading()
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if (uploadRes.code != null && uploadRes.code != 200) {
                this.fileList.push(file)
                this.fileList.pop()
                hideLoading()
            } else {
                this.$message.success('上传成功')
                this.formData.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 15,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId
                })
                // console.log(this.formData[0])
                hideLoading()
            }
        },
        handleExceed () {
            this.$message.error('文件个数不能超出1个')
            return false
        },
        handleRemove (file) {
            let files = this.formData.files
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name === t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('删除成功！')
                    this.formData.files = newFiles
                }
            })
        },
        async downlad () {
            this.fileLoading = true
            showLoading()

            downloadFile({ biddingSn: this.biddingSn }).then(res => {
                if (res.size !== 0) {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                    const url = window.URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = '竞价函.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url)
                }
                if (res.code === 500) {
                    this.$message.error('竞价函下载错误')
                }
            }).finally(() => {this.fileLoading = false
                hideLoading()
            })
        },
        // 修改事件
        handleUpdate () {
            if (this.showLimitedPrice) {
                this.$message.error('含税单价大于最高参考单价，请修改')
                return
            }
            showLoading()
            udateOfferBid({ taxRate: this.rowData.taxRate, tableData: this.tableData }, this.biddingSn).then(res => {
                if (res.code === 200) {
                    this.$message.success('报价修改成功')
                    this.getDataInfo()
                }
            }).finally(() => {
                this.tableLoading = false
                hideLoading()
            })
        },
        async handleDownloadBidFile (file) {
            console.log(file)
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        getDataInfo () {
            if (this.biddingSn == null) {
                return
            }
            showLoading()
            getBidInfoList({ biddingSn: this.biddingSn }).then(res => {
                // console.log(res)
                if (res) {
                    // console.log(res)
                    this.rowData = res
                    // console.log(res.bidAmount)
                    this.tableData = res.productList
                    if (this.rowData.editAble === 0) {
                        this.$alert('当前竞价已结束，不允许修改！', '提示', {
                            confirmButtonText: '确定'
                        })
                    }
                    if (this.rowData.bidFile) {
                        // 文件存在允许下载
                        // console.log(this.rowData.bidFile)
                        this.fileStatus = true
                    }
                }
            }).finally(() => {
                this.tableLoading = false
                hideLoading()
            })
        },
        onSave () {
        },
        // 查看明细
        async handleViewRowDetail ({ row }) {
            showLoading()
            this.showInfo = true
            await getProductInfo({ biddingProductId: row.biddingProductId, biddingSn: this.biddingSn }).then(res => {
                if (res) {
                    this.rowInfo = res

                }
            }).finally(()=>{
                hideLoading()
            })

        },
        calcPriceWithTax ({ taxRate, bidPrice }) {
            return bidPrice * (1 + taxRate / 100)
        },
        calcAllBidRatePriceAmount () {
            let   bidRateAmount  = toFixed(this.tableData.reduce((total, item) => {
                total = Number(total) + Number(item.bidRateAmount)
                return total
            }, 0), 2)
            this.rowData.bidRateAmount = bidRateAmount
            console.log('====')
            console.log(this.rowData.bidRateAmount)
            console.log('====')
            this.rowData.bidAmount = toFixed(bidRateAmount / (1 + (this.rowData.taxRate / 100)), 2)
        },
        // 限制小数位数
        limitFloatDigits (number) {
            let digits = String(number).split('.')[1]
            let processedNum = typeof number === 'number' ? number : Number(number)
            if (digits && digits.length - 2 > 0) {
                processedNum = parseFloat(String(processedNum.toFixed(2)))
            }
            return processedNum
        },
        //取消
        handleClose () {
            this.$router.push({
                path: '/supplierSys/bidManage/myBidding',
                name: 'myBidingList'
            })
            // this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
    }
}
</script>

<style lang='scss' scoped>
/* 隐藏数字控制按钮 */
/deep/input[type="number"].el-input__inner {
    -moz-appearance: textfield !important;
    appearance: textfield;
}

/deep/input[type="number"].el-input::-webkit-inner-spin-button,
/deep/input[type="number"].el-input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 500px;
        margin-top: 0;
    }
}

.action {
    color: #2e61d7;
    cursor: pointer;
}

.btns {
    margin: 20px auto;
    align-items: flex-start;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ .el-button {
    width: 150px;
    height: 40px;
    line-height: 40px;
}
.formatted-text {
    white-space: pre-line; /* 处理换行符显示 */
}
</style>
