import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service
//删除菜单
const deleteById = params => {
    return httpGet({
        url: '/cloudCenter/sysMenuRole/delete',
        params,
    })
}

//修改菜单状态
const updateState = params => {
    return httpPost({
        url: '/cloudCenter/sysMenuRole/updateState',
        params,
    })
}
//修改菜单
const update = params => {
    return httpPost({
        url: '/cloudCenter/menu/update',
        params
    })
}

//登录时查看本机构招标信息
const getDateList = params => {
    return httpPost({
        url: '/cloudCenter/sysMenuRole/listByEntity',
        params,
    })
}

//根据招标id查询分包信息
const saveMenuAndSysRole = params => {
    return httpPost({
        url: '/cloudCenter/sysMenuRole/create',
        params
    })
}
//查询系统当前节点所有父级订单
const selectParentMenus = params => {
    return httpPost({
        url: '/cloudCenter/sysMenuRole/selectParentMenus',
        params
    })
}
export {
    update,
    saveMenuAndSysRole,
    getDateList,
    updateState,
    deleteById,
    selectParentMenus,

}