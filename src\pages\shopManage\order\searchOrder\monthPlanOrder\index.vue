<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <!--                            <el-select @change="stateTopOptionsClick" v-model="selectOptionValue"-->
                            <!--                                       placeholder="状态">-->
                            <!--                                <el-option v-for="item in stateOptions" :key="item.value" :label="item.label"-->
                            <!--                                           :value="item.value">-->
                            <!--                                </el-option>-->
                            <!--                            </el-select>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <!--                        <el-radio v-model="filterData.orderBy" :label="2">按订单提交时间排序</el-radio>-->
                        <el-radio v-model="filterData.orderBy" :label="3">按发货时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="4">按完成时间排序</el-radio>
                        <el-input clearable style="width: 300px" type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table">
                <el-table
                    class="table" :height="rightTableHeight" v-loading="tableLoading" :data="tableData" border
                    @selection-change="selectionChangeHandle"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="订单号" width="240" prop="orderSn">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.orderSn }}</span>
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column label="店铺名称" width="200" prop="shopName"></el-table-column>-->
                    <el-table-column label="商品名称" width="200" prop="untitled"></el-table-column>
                    <el-table-column label="状态" width="" prop="state">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                            <span v-if="scope.row.state == 1">已提交</span>
                            <span v-if="scope.row.state == 2">待确认</span>
                            <span v-if="scope.row.state == 3">已确认</span>
                            <span v-if="scope.row.state == 4">待签订合</span>
                            <span v-if="scope.row.state == 5">已签合同</span>
                            <span v-if="scope.row.state == 6">待发货</span>
                            <span v-if="scope.row.state == 7">已关闭</span>
                            <span v-if="scope.row.state == 8">发货中</span>
                            <span v-if="scope.row.state == 9">待收货</span>
                            <span v-if="scope.row.state == 10">已完成</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单类型" width="100" prop="orderClass">
                        <template v-slot="scope">
                            <span v-if="scope.row.orderClass == 1">普通订单</span>
                            <span v-if="scope.row.orderClass == 2">多供方订单</span>
                            <span v-if="scope.row.orderClass == 3">已拆分订单</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单类别" width="100" prop="productType">
                        <template v-slot="scope">
                            <span v-if="scope.row.productType == 0">物资</span>
                            <span v-if="scope.row.productType == 10">低值易耗品</span>
                            <span v-if="scope.row.productType == 11">办公用品</span>
                            <span v-if="scope.row.productType == 12">大宗材料</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="收件人" width="" prop="receiverName"></el-table-column>
                    <el-table-column label="收件人手机号" width="150" prop="receiverMobile"/>
                    <el-table-column label="收件地址" width="200" prop="receiverAddress"/>
                    <!--                    <el-table-column label="总金额" width="120" prop="actualAmount" />-->
                    <!--                    <el-table-column label="物流单号" width="200" prop="deliveryFlowId" >-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <el-input clearable v-model="scope.row.deliveryFlowId" @change="getChangedRow(scope.row)"></el-input>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <!--                    <el-table-column label="物流公司" width="200" prop="logisticsCompany" >-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <el-input clearable v-model="scope.row.logisticsCompany" @change="getChangedRow(scope.row)"></el-input>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column label="创建时间" width="160" prop="gmtCreate"/>
                    <!--                    <el-table-column label="订单提交时间" width="160" prop="flishTime" />-->
                    <el-table-column label="发货时间" width="160" prop="deliveryTime"/>
                    <el-table-column label="完成时间" width="160" prop="successDate"/>
                    <el-table-column label="订单备注" width="300" prop="orderRemark"></el-table-column>
                </el-table>
            </div>
            <!--分页-->
            <Pagination
                v-show="tableData || tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <!--                <el-row>-->
                <!--                    <el-col :span="12">-->
                <!--                        <el-form-item label="订单类别：">-->
                <!--                            <el-select v-model="filterData.productType" placeholder="请选择状态">-->
                <!--                                <el-option-->
                <!--                                    v-for="item in filterData.productTypeSelect"-->
                <!--                                    :key="item.value"-->
                <!--                                    :label="item.label"-->
                <!--                                    :value="item.value">-->
                <!--                                </el-option>-->
                <!--                            </el-select>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
                <!--                <el-row>-->
                <!--                    <el-col :span="12">-->
                <!--                        <el-form-item label="订单类型：">-->
                <!--                            <el-select v-model="filterData.orderClass" placeholder="请选择订单类型">-->
                <!--                                <el-option-->
                <!--                                    v-for="item in filterData.orderClasss"-->
                <!--                                    :key="item.value"-->
                <!--                                    :label="item.label"-->
                <!--                                    :value="item.value">-->
                <!--                                </el-option>-->
                <!--                            </el-select>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单状态：">
                            <el-select v-model="filterData.selectSateValue" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单号：">
                            <el-input clearable maxlength="100" placeholder="请输入订单号" v-model="filterData.orderSn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品名称：">
                            <el-input clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.untitled"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单提交时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.okDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="发货时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.deliverGoodsDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--                <el-row>-->
                <!--                    <el-col :span="12">-->
                <!--                        <el-form-item label="总金额以上：">-->
                <!--                            <el-input type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 200px"></el-input>-->
                <!--                        </el-form-item>-->
                <!--                        <el-form-item label="总金额以下：">-->
                <!--                            <el-input type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapState } from 'vuex'
import { shopManageOrderList } from '@/api/platform/order/orders'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            stateOptions: [
                {
                    value: null,
                    label: '全部'
                },
                { label: '普通订单', value: 1 },
                { label: '多供方订单', value: 2 },
                { label: '拆分子订单', value: 3 }

            ],
            changedRow: [],
            tableLoading: false,
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            stateOptionTitle: '', // 选中的状态标题
            // 表格数据
            tableStateTitle: null, // 表格的状态
            dataListSelections: [], //选中的数据
            className: null,
            classId: null, // 分类id
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                untitled: null,
                orderSn: null,
                belowPrice: null,
                abovePrice: null,
                dateValue: [], // 开始时间和结束时间
                okDate: [], // 订单提交时间
                deliverGoodsDate: [], // 发货时间
                selectSateValue: null,  // 选中的值
                productType: null,
                orderClass: null,
                orderClasss: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: 1,
                        label: '普通订单'
                    }
                    , {
                        value: 2,
                        label: '多供方订单'
                    }
                ],
                stateOptions: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: 0,
                        label: '草稿'
                    }
                    , {
                        value: 6,
                        label: '待发货'
                    }
                    , {
                        value: 7,
                        label: '已关闭'
                    }
                    , {
                        value: 8,
                        label: '已发货'
                    }, {
                        value: 9,
                        label: '待收货'
                    }, {
                        value: 10,
                        label: '已完成'
                    }],
                productTypeSelect: [
                    {
                        value: null,
                        label: '全部'
                    },
                    {
                        value: 0,
                        label: '物资'
                    },
                    {
                        value: 10,
                        label: '低值易耗品'
                    },
                    {
                        value: 11,
                        label: '办公用品'
                    },
                ],
                orderBy: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        getChangedRow (row) {
            if (this.changedRow.length == 0) {
                this.changedRow.push({
                    orderId: row.orderId,
                    deliveryFlowId: row.deliveryFlowId,
                    logisticsCompany: row.logisticsCompany
                })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.orderId == row.orderId) {
                    t.deliveryFlowId = row.deliveryFlowId
                    t.logisticsCompany = row.logisticsCompany
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push({
                    orderId: row.orderId,
                    deliveryFlowId: row.deliveryFlowId,
                    logisticsCompany: row.logisticsCompany
                })
            }
        },
        // 选中状态进行查询
        stateTopOptionsClick (value) {
            this.selectOptionValue = value
            this.getTableData()
        },
        // 详情
        handleView (row) {
            this.$router.push({
                path: '/supplierSys/order/searchOrder/monthPlanOrderDetail',
                name: 'searchOrderMonthPlanOrderDetail',
                query: {
                    orderSn: row.orderSn
                }
            })
        },
        resetSearchConditions () {
            this.filterData.belowPrice = null // 以下价格
            this.filterData.abovePrice = null // 以上价格
            this.filterData.untitled = null
            this.filterData.orderSn = null
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.okDate = []
            this.filterData.deliverGoodsDate = []
            this.filterData.selectSateValue = null
            this.filterData.productType = null
            this.filterData.orderClass = null
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = null
            this.selectOptionValue = null
            this.stateOptionTitle = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 获取表格数据
        getTableData () {
            let params = {
                classId: this.classId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: 12,
                orderClass: 1,
            }
            if (this.selectOptionValue != null) {
                params.state = this.selectOptionValue
            }
            if (this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if (this.filterData.productType != null && this.filterData.productType != '') {
                params.productType = this.filterData.productType
            }
            if (this.filterData.orderClass != null) {
                params.orderClass = this.filterData.orderClass
            }
            if (this.filterData.selectSateValue != null) {
                params.state = this.filterData.selectSateValue
            }
            if (this.filterData.okDate != null) {
                params.okStartDate = this.filterData.okDate[0]
                params.okEndDate = this.filterData.okDate[1]
            }
            if (this.filterData.deliverGoodsDate != null) {
                params.deliverGoodsStartDate = this.filterData.deliverGoodsDate[0]
                params.deliverGoodsEndDate = this.filterData.deliverGoodsDate[1]
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if (this.filterData.untitled != null) {
                params.untitled = this.filterData.untitled
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            this.tableLoading = true
            shopManageOrderList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list || []
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {
    min-height: auto;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
    -moz-appearance: textfield !important;
}
</style>
