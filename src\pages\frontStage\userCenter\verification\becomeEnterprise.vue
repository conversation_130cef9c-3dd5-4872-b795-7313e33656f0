<template>
    <main v-loading="formLoading">
        <div class="list-title df mb20">成为企业</div>
        <div class="content" v-if="!(succeedPage || failPage)">
            <el-form :model="enterpriseForm" :rules="enterpriseFormRules" ref="enterpriseFormV" label-width="168px" class="enterpriseForm"
                     :inline="false">
                <div class="df">
                    <el-form-item class="licenseUploader" label="营业执照图片：" prop="businessLicense">
                        <el-upload class="avatar-uploader" action="fakeaction" :before-upload="handleBeforeUpload" name="img" :auto-upload="true"
                                   :show-file-list="false" :on-change="handleUploadChange" :http-request="uploadLicenseEnterprise">
                            <img v-if="enterpriseForm.businessLicense" :src="enterpriseForm.businessLicense" class="avatar">
                            <div v-else class="licenseUploader">
                                <img src="@/assets/images/userCenter/upload_yyzz.png" />
                            </div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item class="licenseUploader">
                        <div class="uploadDemo dfa">
                            <img src="@/assets/images/userCenter/yyzz_demo.png" alt="">
                            <div><span>示例图</span><i class="el-icon-zoom-in"></i></div>
                        </div>
                    </el-form-item>
                    <div class="uploadTip">请上传10MB以内的PNG，JPG，GIF，BMP格式图片</div>
                </div>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="企业名称：" prop="enterpriseName">
                            <el-input clearable v-model="enterpriseForm.enterpriseName" placeholder="请填写50字以内的企业名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                            <el-input clearable v-model="enterpriseForm.socialCreditCode"
                                      v-on:blur="selectIsPcwpUser(enterpriseForm.socialCreditCode,'companyRegistration')"
                                      placeholder="请输入统一社会信用代码"  ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="注册资本(万元)：" prop="registeredCapital">
                            <el-input clearable v-model="enterpriseForm.registeredCapital" placeholder="请填写企业注册资本"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="0">
                        <el-form-item class="licenseValidTime" label="法定代表人：" prop="legalRepresentative">
                            <el-input clearable v-model="enterpriseForm.legalRepresentative" placeholder="请填写50字以内的法定代表人姓名"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="注册时间：" prop="creationTime">
                            <el-date-picker v-model="enterpriseForm.creationTime" align="right" type="date" value-format="yyyy-MM-dd HH:mm:ss"
                                            placeholder="请选择企业注册日期" :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item class="licenseValidTime" label="营业执照有效期：" prop="licenseTerm">
                            <el-date-picker v-model="enterpriseForm.licenseTerm" align="right" type="date" :value-format="dateFormat"
                                            placeholder="请选择营业执照有效期" :picker-options="pickerAfterOptions">
                            </el-date-picker>
                            <el-checkbox label="长期" :indeterminate="false" v-model="longTerm"></el-checkbox>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" :offset="0">
                        <el-form-item class="registerAddress" label="企业注册地址：" prop="address">
                            <div>
                                <el-select ref="selectLabel1" class="province" v-model="enterpriseForm.provinces" value-key="" placeholder="省份"
                                           @change="(code) => getSubDistrict(code, 1)">
                                    <el-option v-for="item in addressOptions.province" :key="item.value" :label="item.districtName"
                                               :value="item.districtCode">
                                    </el-option>
                                </el-select>
                                <el-select ref="selectLabel2" class="city" v-model="enterpriseForm.city" value-key="" placeholder="地级市"
                                           @change="(code) => getSubDistrict(code, 2)">
                                    <el-option v-for="item in addressOptions.city" :key="item.value" :label="item.districtName"
                                               :value="item.districtCode">
                                    </el-option>
                                </el-select>
                                <el-select ref="selectLabel3"  @visible-change="addressChange"   class="county" v-model="enterpriseForm.county" value-key="" placeholder="区、县">
                                    <el-option v-for="item in addressOptions.district" :key="item.value" :label="item.districtName"
                                               :value="item.districtCode">
                                    </el-option>
                                </el-select>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="详细地址：" prop="detailedAddress">
                            <el-input clearable v-model="enterpriseForm.detailedAddress" placeholder="请填写营业执照注册详细地址"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="税率%：" prop="taxRate">
                                <el-input v-model="enterpriseForm.taxRate" style="width: 110px;" :step="0.01" type="number" placeholder="填写税率" @change="writeTaxRate"/>%
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="主营业务：" prop="mainBusiness">
                            <el-input clearable v-model="enterpriseForm.mainBusiness" placeholder="请填写与营业执照相同的主营业务"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="separ center"></div>
                <div class="subtitle">设置管理员</div>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="身份证人像面照：" prop="cardPortraitFace">
                            <el-upload class="identityUpload face" action="fakeaction" :http-request="(res) => uploadIdentity(res, 1,1)"
                                       :show-file-list="false">
                                <img class="identityUpload" v-if="enterpriseForm.cardPortraitFace" :src="enterpriseForm.cardPortraitFace"
                                     alt="">
                                <img class="identityUpload" v-else src="@/assets/images/userCenter/sfz_renmian.png" alt="">
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="身份证国徽面照：" prop="cardPortraitNationalEmblem">
                            <el-upload class="identityUpload badge" action="fakeaction" :http-request="(res) => uploadIdentity(res, 2,1)"
                                       :show-file-list="false">
                                <img class="identityUpload" v-if="enterpriseForm.cardPortraitNationalEmblem" :src="enterpriseForm.cardPortraitNationalEmblem"
                                     alt="">
                                <img class="identityUpload" v-else src="@/assets/images/userCenter/upload_sfz.png" alt="">
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="手机号码：" prop="adminPhone">
                            <el-input clearable v-model="enterpriseForm.adminPhone" disabled placeholder="请输入手机号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="姓名：" prop="adminName">
                            <el-input clearable v-model="enterpriseForm.adminName" placeholder="请输入姓名"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11" :offset="0">
                        <el-form-item label="身份证号码：" prop="adminNumber">
                            <el-input clearable v-model="enterpriseForm.adminNumber" placeholder="请输入身份证号码"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--                            附件资料-->
<!--                <el-row>-->
<!--                    <el-col :span="24" style="height: unset;" v-loading="fileLoading">-->
<!--                        <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">-->
<!--                            <el-upload-->
<!--                                ref="multi-upload"-->
<!--                                class="multi-file-uploader"-->
<!--                                action="fakeaction"-->
<!--                                accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"-->
<!--                                :on-remove="handleRemove"-->
<!--                                multiple-->
<!--                                :limit="10"-->
<!--                                :before-upload="beforeOneOfFilesUpload"-->
<!--                                :http-request="uploadOneOfFilesC"-->
<!--                                :on-exceed="handleExceed"-->
<!--                                :file-list="enterpriseFormFileList"-->
<!--                            >-->
<!--                                <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>-->
<!--                                <div slot="tip" class="el-upload__tip"><span>请上传</span>-->
<!--                                    <div class="file dfa pointer" v-for="file in fileList" :key="file.url">-->
<!--                                        <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>-->
<!--                            </el-upload>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                </el-row>-->
                <el-row v-show="registerPcwpFiles.isPcwoSupplier =='unPcwpSupplier'">
                    <el-col :span="24" style="height: unset;" v-loading="fileLoading">
                        <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                            <el-upload
                                ref="multi-upload"
                                class="multi-file-uploader"
                                action="fakeaction"
                                accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                :on-remove="handleRemoveIsPcwp"
                                multiple
                                :limit="10"
                                :before-upload="beforeOneOfFilesUpload"
                                :http-request="uploadOneOfFilesC"
                                :on-exceed="handleExceed"
                                :file-list="enterpriseForm.files"
                            >
                                <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip"><span>请上传以下资料</span>
                                    <div class="file dfa pointer" v-for="file in registerPcwpFiles.files" :key="file.url">
                                        <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                    </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-show="registerPcwpFiles.isPcwoSupplier=='isPcwpSupplier'">
                    <el-col :span="24" style="height: unset;" v-loading="fileLoading">
                        <el-form-item class="upload-item" label="附件资料：" prop="openShopFile">
                            <el-upload
                                ref="multi-upload"
                                class="multi-file-uploader"
                                action="fakeaction"
                                accept=".pdf, .doc, .docx, .xlsx, .zip, .rar, image/*"
                                :on-remove="handleRemoveNoPcwp"
                                multiple
                                :limit="10"
                                :before-upload="beforeOneOfFilesUpload"
                                :http-request="uploadOneOfFilesC"
                                :on-exceed="handleExceed"
                                :file-list="enterpriseForm.files"
                            >
                                <el-button class="upload-btn" size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip"><span>请上传以下资料</span>
                                    <div class="file dfa pointer" v-for="file in registerPcwpFiles.files" :key="file.url">
                                        <span @click="handleDownload(file)"><i class="el-icon el-icon-download"></i>&nbsp;<span>{{ file.name }}</span></span>
                                    </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">支持上传pdf、doc、docx、xlsx、zip、rar、图片文件</div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="agreeTerm" >
                            <el-checkbox v-model="agreeTerm" :indeterminate="false">
                                您确认阅读并接受<span @click="openAgreemen">《物资采购平台企业认证协议》</span>
                            </el-checkbox>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="btns center dfb">
                <button @click="$router.go(-1)">返回</button>
                <button @click="onSubmit">提交</button>
            </div>
            <!-- 平台协议 -->
            <el-dialog title="物资采购平台企业认证协议" :visible.sync="showTerm">
                <span v-html="content"></span>
                <span slot="footer">
                        <el-button @click="showTerm = false">Cancel</el-button>
                        <el-button type="primary" @click="showTerm = false">OK</el-button>
                    </span>
            </el-dialog>
        </div>
        <div class="tabBox" v-if="succeedPage">
            <enterpriseReg :title="'企业'"></enterpriseReg>
        </div>
        <div class="tabBox" v-if="failPage">
            <failReg></failReg>
        </div>
    </main>
</template>
<script>
import { previewFile, uploadFile } from '@/api/platform/common/file'
import { getCascaderOptions } from '@/api/platform/common/components'
import { becomeEnterprise, getEnterpriseAuthInfo } from '@/api/frontStage/verification'
import enterpriseReg from './enterpriseReg.vue'
import failReg from './fail.vue'
import { findByProgramaKey } from '@/api/w/richContent'
import { createWFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { selectIsPcwpUserByCode } from '@/api/frontStage/register'
export default {
    components: { enterpriseReg, failReg },
    data () {
        return {
            fileList: [],
            fileLoading: false,
            enterpriseFormFileList: [],
            registerPcwpFiles: {
                isPcwoSupplier: '',
                files: []
            },
            formLoading: false,
            content: '',
            succeedPage: false,
            failPage: false,
            uploadImgSize: 4,
            agreeTerm: false,
            enterpriseForm: {},
            //企业表单验证
            enterpriseFormRules: {
                businessLicense: { required: true, message: '请上传营业执照!', trigger: 'blur' },
                enterpriseName: [
                    { required: true, message: '请填写企业名称', trigger: 'blur' },
                    { min: 1, max: 50, message: '请填写50字以内的企业名称', trigger: 'blur' }
                ],
                socialCreditCode: [
                    { required: true, message: '请输入统一信用代码', trigger: 'blur' },
                    { min: 18, max: 18, message: '请输入18位统一信用代码', trigger: 'blur' }
                ],
                legalRepresentative: [
                    { required: true, message: '请填写企业法定代表人', trigger: 'blur' },
                    { min: 2, max: 10, message: '请填写企业法定代表人', trigger: 'blur' }
                ],
                registeredCapital: [
                    { required: true, message: '请输入注册资本',  trigger: 'blur' },
                    { min: 1, max: 18, message: '超过限制', trigger: 'blur' },
                    // { validator: this.checkInt, trigger: 'blur' }
                ],
                creationTime: { required: true, validator: this.validateDate, trigger: 'blur' },
                // licenseTerm: { required: true, validator: this.validateDate, trigger: 'blur' },
                address: { required: true, validator: this.validateAddress, trigger: 'blur' },
                detailedAddress: [
                    { required: true, message: '请填写详细地址', trigger: 'blur' },
                    { min: 1, max: 100, message: '超过限制', trigger: 'blur' }
                ],
                mainBusiness: [
                    { required: true, message: '请填写与营业执照相同的主营业务', trigger: 'blur' },
                    { min: 1, max: 1000, message: '超过限制', trigger: 'blur' }
                ],
                taxRate: [
                    { required: true, message: '请输入税率', trigger: 'blur' },
                    { min: 0, max: 3, message: '超过限制', trigger: 'blur' }
                ],
                cardPortraitFace: { required: true, message: '请上传身份证人像面照!', trigger: 'blur' },
                cardPortraitNationalEmblem: { required: true, message: '请上传身份证国徽面照!', trigger: 'blur' },
                adminName: [
                    { required: true, message: '请输入真实姓名', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                adminNumber: [
                    { required: true, message: '请输入18位身份证号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入18位身份证号码', trigger: 'blur' },
                ],
                adminPhone: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
            },
            // 获取地址选择器数据
            async getAddressPickerOptions () {
                let res = await getCascaderOptions({ distCode: '100000' })
                this.addressOptions.province = res
            },
            pickerAfterOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            // 日期选择器选项
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() > Date.now()
                },
                shortcuts: [{
                    text: '今天',
                    onClick (picker) {
                        picker.$emit('pick', new Date())
                    }
                }, {
                    text: '昨天',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24)
                        picker.$emit('pick', date)
                    }
                }, {
                    text: '一周前',
                    onClick (picker) {
                        const date = new Date()
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
                        picker.$emit('pick', date)
                    }
                }]
            },
            // 地址选择器选项
            addressOptions: {
                province: [],
                city: [],
                district: []
            },
            dateFormat: 'yyyy-MM-dd HH:mm:ss',
            showTerm: false,
            longTerm: false,
        }
    },
    created () {
        this.getEnterpriseAuthInfoM()
        this.getAddressPickerOptions()
        this.getRegisterAgreeUser('companyRegistration')
    },
    watch: {
        longTerm (val) {
            if(val) {
                this.enterpriseForm.licenseTerm = null
            }
        },
    },
    mounted () {},
    methods: {
        writeTaxRate () {
            console.log(this.enterpriseForm.taxRate, 0 <= this.enterpriseForm.taxRate && this.enterpriseForm.taxRate <= 100)
            if (this.enterpriseForm.taxRate != null) {
                if (!(0 <= this.enterpriseForm.taxRate && this.enterpriseForm.taxRate <= 100)) {
                    this.$message.error('税率不能小于0或大于100')
                    this.enterpriseForm.taxRate = 0
                }
            }else {
                this.$message.error('税率不能为空')
                this.enterpriseForm.taxRate = 0
            }
        },
        selectIsPcwpUser (socialCreditCode, programaKey) {
            if (socialCreditCode.length == 18) {
                selectIsPcwpUserByCode({ socialCreditCode: socialCreditCode, programaKey: programaKey }).then(res=>{
                    this.registerPcwpFiles = res
                })
            }

        },
        async uploadOneOfFilesC (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.enterpriseForm.files.push(file)
                this.enterpriseForm.files.pop()
            }else {
                this.$message.success('上传成功')
                this.enterpriseForm.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    programaKeyTwo: this.registerPcwpFiles.isPcwoSupplier,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,

                })
            }
            console.log()
        },

        handleRemoveIsPcwp (file) {
            this.fileLoading = true
            let files = this.enterpriseForm.files
            let recordId = null
            let newFiles = files.filter(t =>{
                if(file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                }else {
                    return true
                }
            })
            createWFileRecordDelete({ recordId: recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.enterpriseForm.files = newFiles
                }
                this.fileLoading = false
            }).catch(() => {
                this.fileLoading = false
            })
        },
        handleRemoveNoPcwp (file) {
            this.fileLoading = true
            let files = this.enterpriseForm.files
            let recordId = null
            let newFiles = files.filter(t =>{
                if(file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                }else {
                    return true
                }
            })
            createWFileRecordDelete({ recordId: recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.enterpriseForm.files = newFiles
                }
                this.fileLoading = false
            }).catch(() => {
                this.fileLoading = false
            })
        },

        // 正整数
        checkInt (rule, value, callback) {
            if (Number(value) && value % 1 === 0 && value >= 0) {
                callback()
            } else {
                return callback(new Error('请输入正整数！'))
            }
        },
        getRegisterAgreeUser (programaKey) {
            findByProgramaKey({ programaKey: programaKey }).then(res => {
                this.fileList = res.files
                this.content = res.content

            })
        },
        openAgreemen () {
            this.showTerm = true
        },
        async handleDownload (file) {
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
        },
        handleExceed () {
            this.$message.error('文件个数不能超出10个')
            return false
        },
        // 多文件上传
        async uploadOneOfFiles (data) {
            let file = data.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private')
            form.append('directory', 'material')
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.fileLoading = true
            let uploadRes = await uploadFile(form)
            this.fileLoading = false
            if(uploadRes.code != null && uploadRes.code != 200) {
                this.enterpriseFormFileList.push(file)
                this.enterpriseFormFileList.pop()
            }else {
                this.$message.success('上传成功')
                console.log(this.enterpriseForm, 'this.enterpriseForm')
                this.enterpriseForm.files.push({
                    name: uploadRes[0].objectName,
                    relevanceType: 6,
                    url: uploadRes[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: uploadRes[0].recordId,

                })
            }
        },
        handleRemove (file) {
            this.fileLoading = true
            let files = this.enterpriseForm.files
            let recordId = null
            let newFiles = files.filter(t =>{
                if(file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                }else {
                    return true
                }
            })
            createWFileRecordDelete({ recordId: recordId }).then(res => {
                if(res.code == 200) {
                    this.$message.success('删除成功！')
                    this.enterpriseForm.files = newFiles
                }
                this.fileLoading = false
            }).catch(() => {
                this.fileLoading = false
            })
        },
        beforeOneOfFilesUpload (file) {
            let size = file.size / 1024 / 1024
            if(size > 100) {
                this.$message.error('文件大小不能超过100M')
                return false
            }
            return true
        },
        addressChange (val) {
            if(!val) {
                this.enterpriseForm.provinces = this.$refs.selectLabel1.selectedLabel
                this.enterpriseForm.city = this.$refs.selectLabel2.selectedLabel
                this.enterpriseForm.county = this.$refs.selectLabel3.selectedLabel
                let newAddress = this.enterpriseForm.provinces + this.enterpriseForm.city + this.enterpriseForm.county
                this.enterpriseForm.detailedAddress = newAddress
            }
        },
        getRegisterAgree () {
            findByProgramaKey({ programaKey: 'userRegistration' }).then(res => {
                this.content = res.content
                this.showTerm = true
            })
        },
        async uploadIdentity (params, num, type) {
            if(num === 1 ) {
                let file = params.file
                const form = new FormData()
                form.append('files', file)
                form.append('bucketName', 'mall-private') //存储桶名称
                form.append('directory', 'material') // 商城类型
                form.append('isChangeObjectName', true) // 是否修改文件名称
                form.append('isTemplate', false)  //是否是模板
                form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
                form.append('relationId', '990116') // 关联ID
                uploadFile(form).then(res => {
                    if(type == 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.enterpriseForm.cardPortraitFace = url
                        })
                        this.enterpriseForm.cardPortraitFace = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.enterpriseForm.cardPortraitFaceId = res[0].recordId
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                        let fileSuffix = res[0].objectName.substr(res[0].objectName.lastIndexOf('.'))
                        this.enterpriseForm.files.push({
                            name: '身份证正面.' + fileSuffix,
                            relevanceType: 8,
                            url: res[0].nonIpObjectPath,
                            fileType: 3,
                            fileFarId: res[0].recordId
                        })
                    }
                })
            }
            if(num === 2 ) {
                let file = params.file
                const form = new FormData()
                form.append('files', file)
                form.append('bucketName', 'mall-private') //存储桶名称
                form.append('directory', 'material') // 商城类型
                form.append('isChangeObjectName', true) // 是否修改文件名称
                form.append('isTemplate', false)  //是否是模板
                form.append('orgCode', 'SRBC') // 登![](C:/Users/<USER>/Documents/ShareX/Screenshots/2023-02/webstorm64_P8AGsBXk91.png)录获取（组织机构简称）
                form.append('relationId', '990116') // 关联ID
                uploadFile(form).then(res => {
                    if(type === 1) {
                        previewFile({ recordId: res[0].recordId }).then(res => {
                            let url = window.URL.createObjectURL(res)
                            this.enterpriseForm.cardPortraitNationalEmblem = url
                        })
                        this.enterpriseForm.cardPortraitNationalEmblem = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                        this.enterpriseForm.cardPortraitNationalEmblemId = res[0].recordId
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        })
                        let fileSuffix = res[0].objectName.substr(res[0].objectName.lastIndexOf('.'))
                        this.enterpriseForm.files.push({
                            name: '身份证反面.' + fileSuffix,
                            relevanceType: 8,
                            url: res[0].nonIpObjectPath,
                            fileType: 3,
                            fileFarId: res[0].recordId,

                        })
                    }
                })
            }
        },

        // 获取企业信息
        getEnterpriseAuthInfoM () {
            this.formLoading = true
            getEnterpriseAuthInfo({}).then(res => {
                this.enterpriseForm = res
                this.enterpriseForm.files = []
                if(this.enterpriseForm.cardPortraitFaceId != null) {
                    previewFile({ recordId: this.enterpriseForm.cardPortraitFaceId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.cardPortraitFace = url
                    })
                }
                if(this.enterpriseForm.cardPortraitNationalEmblemId != null) {
                    previewFile({ recordId: this.enterpriseForm.cardPortraitNationalEmblemId }).then(res => {
                        let url = window.URL.createObjectURL(res)
                        this.enterpriseForm.cardPortraitNationalEmblem = url
                    })
                }
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 消息提示
        message (res) {
            if(res.code == 200) {
                this.succeedPage = true
                setTimeout(() => {
                    localStorage.removeItem('token')
                    this.$store.commit('setUserInfo', {})
                    window.location.href = '/login'
                }, 1000)
            } else {
                this.failPage = true
            }
        },
        // 提交
        onSubmit () {
            this.$refs['enterpriseFormV'].validate(valid => {
                if(valid) {
                    if(this.agreeTerm) {
                        this.enterpriseForm.enterpriseType = 1
                        this.enterpriseForm.mallType = 0
                        this.formLoading = true
                        this.enterpriseForm.files = [...this.enterpriseForm.files, ...this.enterpriseFormFileList]
                        this.clientPop('info', '提交后请加QQ：840469283，便于审查部分未上传的资料并邮寄原件。', async () => {
                            becomeEnterprise(this.enterpriseForm).then(res => {
                                this.message(res)
                                this.formLoading = false
                            })
                        }).catch(() => {
                            this.formLoading = false
                        })
                    }else {
                        this.$message({
                            message: '请查看协议后勾选',
                            type: 'error'
                        })
                    }
                }
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        handleUploadChange (file) {
            if (file.status == 'ready') {
                this.uploadInProgress = true
                this.uploadPercentage = 0
                const interval = setInterval(() => {
                    if (this.uploadPercentage >= 99) {
                        clearInterval(interval)
                        return
                    }
                    this.uploadPercentage += 1
                }, 20)
            }
            if (file.status == 'success') {
                this.uploadPercentage = 100
                setTimeout(() => {
                    this.uploadInProgress = false
                }, 300)
            }
        },
        async uploadLicenseEnterprise (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true) // 是否修改文件名称
            form.append('isTemplate', false)  //是否是模板
            form.append('orgCode', 'SRBC') // 登录获取（组织机构简称）
            form.append('relationId', '990116') // 关联ID
            uploadFile(form).then(res => {
                previewFile({ recordId: res[0].recordId }).then(res => {
                    let url = window.URL.createObjectURL(res)
                    this.enterpriseForm.businessLicense = url
                })
                this.enterpriseForm.businessLicense = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                this.enterpriseForm.businessLicenseId = res[0].recordId
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                let fileSuffix = res[0].objectName.substr(res[0].objectName.lastIndexOf('.'))
                this.enterpriseForm.files.push({
                    name: '营业执照.' + fileSuffix,
                    relevanceType: 8,
                    url: res[0].nonIpObjectPath,
                    fileType: 3,
                    fileFarId: res[0].recordId
                })
            })
        },
        // 获取子级地区
        getSubDistrict (code, layer) {
            this.enterpriseForm.district = ''
            if(layer === 1) {
                this.enterpriseForm.city = ''
            }
            getCascaderOptions({ distCode: code }).then(res => {
                if(layer === 1) {
                    return this.addressOptions.city = res
                }
                this.addressOptions.district = res
            })
        },
        //时间验证
        validateDate (rule, value, callback) {
            if (value == null || value == '') {
                return callback(new Error('请选择时间！'))
            }
            callback()
        },
        validateAddress (rule, value, callback) {
            if ( this.enterpriseForm.provinces == null || this.enterpriseForm.provinces == '' ) {
                return callback(new Error('请选择省份！'))
            }
            if ( this.enterpriseForm.city == null || this.enterpriseForm.city == '' ) {
                return callback(new Error('请选择市级！'))
            }
            callback()
        },
    },
}
</script>
<style scoped lang="scss">
$font: 'Source Han Sans CN';
main>div {
    height: 100%;
    border: 1px solid rgba(230, 230, 230, 1);
}
.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
.content {padding-bottom: 30px;}
/deep/ .el-form-item {
    margin-bottom: 25px;

    .el-form-item__label {
        height: 100%;
        padding-right: 10px;
        line-height: 50px;
        font-size: 16px;
        color: #333;
    }

    .el-input__inner {
        height: 50px;
        font-size: 16px;
        border-radius: 0;
        border: 1px solid rgba(204, 204, 204, 1);
    }
}

/deep/ .el-checkbox {
    display: flex;
    align-items: center;

    .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(33, 110, 198, 1);
    }

    .el-checkbox__label {
        font-size: 16px;
        color: #333;

        span {
            color: #216EC6;
        }
    }
}
/deep/.avatar-uploader .el-upload {
    width: 138px;
    height: 138px;
    border: 1px dashed rgba(217, 217, 217, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
/deep/ .enterpriseForm {
    width: 1066px;
    margin-top: 30px;
    .el-col:not(.el-col-24) {
        width: 48.4%;
        .el-input, .el-input__inner {width: 350px;}
        &.el-col-offset-1 {margin-left: 20px;}
    }
    .el-col-24 .el-input__inner, .el-input {width: 893px;}
    .el-input.is-disabled {background-color: #e6e6e6;color: #999;}
    .el-form-item__error {
        width: 80%;
        margin-top: -10px;
    }
    .licenseUploader {
        font-size: 40px;
        color: #8c939d;
        width: 138px;
        height: 138px;
        //margin-right: 20px;
        line-height: 140px;
        display: inline;
        .el-form-item__error {
            width: 500px;
        }
    }

    .avatar {
        width: 140px;
        height: 140px;
        display: block;
    }

    .uploadDemo {
        width: 138px;
        height: 138px;
        padding: 5px;
        margin-left: 40px;
        font-size: 16px;
        color: #999;
        border: 1px dashed rgba(217, 217, 217, 1);
        flex-direction: column;

        img {
            width: 130px;
            height: 95px;
        }

        span {
            margin-right: 5px;
        }
    }

    .uploadTip {
        font-size: 14px;
        margin-top: 155px;
        margin-bottom: 20px;
        margin-left: -108px;
        width: 380px;
        color: #808080;
    }

    .el-select {
        width: 100%;
    }
    .licenseValidTime {
        .el-form-item__content {display: flex;}
        .el-date-editor {
            width: 260px;
            margin-right: 32px;
            .el-input__inner {width: 260px;}
        }
        .el-checkbox {height: 50px; margin-bottom: 0;}
        .el-checkbox__inner {border: 1px solid rgba(217,217,217,1);}
    }
    .registerAddress {
        .el-select {
            width: 100px;
            margin-right: 10px;
            &:last-child{margin-right: 0;}
            .el-input, .el-input__inner {width: 100px;}
        }
        .el-form-item__error {
            margin-left: 0px;
            //margin-top: -50px;
        }
    }
    .separ {
        width: 1066px;
        height: 1px;
        margin-left: 20px;
        margin-bottom: 30px;
        border-top: 1px dashed rgba(204, 204, 204, 1);
    }

    .subtitle {
        margin-left: 60px;
        margin-bottom: 30px;
        font-size: 20px;
        font-weight: 500;
        color: #216EC6;
    }

    .identityUpload {
        width: 160px;
        height: 100px;
    }
    .verifyBox .el-button {
        width: 140px;
        height: 50px;
        padding: 0;
        font-size: 16px;
        line-height: 50px;
        color: #216EC6;
        background: #FFFFFF;
        border: 1px solid rgba(33, 110, 198, 1);
        border-radius: 0;
    }
    .verifyBox {
        .el-input, .el-input__inner{
            width: 195px !important;
            margin-right: 15px;
        }
    }

    .el-checkbox {
        margin-top: -3px;
        margin-bottom: 50px;
        .is-checked .el-checkbox__inner {background-color: #216ec6;}
    }
}
.tabBox {
    height: 100%;
    min-height: 622px;
    font-family: $font;
    position: relative;
}
.btns {
    width: 350px;
    margin-top: -25px;
    button {
        width: 160px;
        height: 50px;
        font-size: 22px;
    }
    button:first-child {
        color: rgba(33,110,198,1);;
        border: 1px solid rgba(33,110,198,1);
        background-color: #fff;
    }
    button:last-child {
        color: #fff;
        background-color: rgba(33,110,198,1);
    }
}

</style>