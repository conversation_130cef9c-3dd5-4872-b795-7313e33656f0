import service from '@/utils/request'

const { httpPost, httpGet } = service
/* pcwp1物资采购合同*/
const contractPcwp1List = params => {
    return httpPost({
        url: '/supplierSys/outApi/pcwp1/contractList',
        params
    })
}
const wzContractById = params => {
    return httpGet({
        url: '/supplierSys/outApi/pcwp1/contractById',
        params
    })
}

export {
    contractPcwp1List,
    wzContractById

}