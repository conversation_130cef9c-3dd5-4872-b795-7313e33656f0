import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

// 新增库房信息
const warehouseAdd = params => {
    return httpPost({
        url: '/material/warehouseManagement/create',
        params
    })
}
// 修改库房信息
const warehouseModify = params => {
    return httpPost({
        url: '/material/warehouseManagement/update',
        params
    })
}
// 新增库房明细
const warehouseListAdd = params => {
    return httpPost({
        url: '/material/warehouseManagement/createLsit',
        params
    })
}
// 根据条件分页查询库房信息
const warehouseLs = params => {
    return httpPost({
        url: '/material/warehouseManagement/listByEntity',
        params
    })
}
// 根据库房id查询查询库房信息
const warehouseGetById = params => {
    return httpGet({
        url: '/material/warehouseManagement/findById',
        params
    })
}
// 根据库房id获取库房明细信息
const warehouseGetLs = params => {
    return httpGet({
        url: '/material/warehouseManagement/getListById',
        params
    })
}
// 修改仓库明细
const warehouseModifyList = params => {
    return httpPost({
        url: '/material/warehouseManagement/updateList',
        params
    })
}
// 根据乙方人员id删除乙方人员
const warehouseDel = params => {
    return httpGet({
        url: '/material/warehouseManagement/delete',
        params
    })
}
export {
    warehouseAdd,
    warehouseModify,
    warehouseListAdd,
    warehouseLs,
    warehouseGetById,
    warehouseGetLs,
    warehouseModifyList,
    warehouseDel
}