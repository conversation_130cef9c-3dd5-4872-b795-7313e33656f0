import service from '@/utils/request'

const { httpPost, httpGet } = service
const sendMessageApi = params => {
    return httpPost({
        url: '/materialMall/stationMessage/createBatch',
        params
    })
}
const getList = params => {
    return httpPost({
        url: '/materialMall/stationMessageReceive/listByEntity',
        params
    })
}
const del = params => {
    return httpGet({
        url: '/materialMall/stationMessage/delete',
        params
    })
}
const getMessageList = params => {
    return httpPost({
        url: '/materialMall/platform/stationMessage/listByEntity',
        params
    })
}
const receivePageList = params => {
    return httpPost({
        url: '/materialMall/stationMessageReceive/listReceiveByMgId',
        params
    })
}

const queryAllPage = params => {
    return httpPost({
        url: '/materialMall/platform/messageInformations/listByEntit',
        params
    })
}
const sendRespond = params => {
    return httpPost({
        url: '/materialMall/platform/messageInformations/createRespond',
        params
    })
}
const queryPage = params => {
    return httpPost({
        url: '/materialMall/platform/messageInformations/listByEntity',
        params
    })
}
const createMessage = params => {
    return httpPost({
        url: '/materialMall/platform/messageInformations/create',
        params
    })
}
const publicDisplay = params => {
    return httpPost({
        url: '/materialMall/platform/messageInformations/listByShowEntity',
        params
    })
}
const deleteMessage = params => {
    return httpGet({
        url: '/materialMall/platform/messageInformations/delete',
        params
    })
}

const updateNotDisplay = params => {
    return httpPost({
        url: '/materialMall/platform/messageInformations/updateNotPublish',
        params
    })
}
const updateDisplay = params => {
    return httpPost({
        url: '/materialMall/platform/messageInformations/updateByPublish',
        params
    })
}
export {
    sendMessageApi,
    getList,
    del,
    getMessageList,
    receivePageList,
    queryAllPage,
    sendRespond,
    queryPage,
    createMessage,
    publicDisplay,
    deleteMessage,
    updateDisplay,
    updateNotDisplay

}