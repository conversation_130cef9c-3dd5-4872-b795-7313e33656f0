import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

// 删除附件
const deleterRecordIdAttach = params => {
    return httpPost({
        url: '/oss/deleterRecordId',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        params: qs.stringify(params)
    })
}

// 获取用户所在机构
const getOrgByUserId = params => {
    return httpGet({
        url: '/hr/org/getOrgByUserId',
        params
    })
}
// 通过机构Id查找机构
const getOrgById = params => {
    return httpGet({
        url: '/hr/org/getOrgById',
        params
    })
}
// 获取机构的上级机构
const getOrgByParentId = params => {
    return httpGet({
        url: '/hr/org/getSingleParentOrg',
        params
    })
}
// 获取本机构及其以下机构
const getChildrenOrg = params => {
    return httpGet({
        url: '/hr/org/getChildrenOrg',
        params
    })
}
// 获取币种字典
const getCurrencyApi = params => {
    return httpGet({
        url: '/config/kv/getDicValue1',
        params
    })
}
// 获取本位币设置列表（分页）
const getBaseCyApi = params => {
    return httpPost({
        url: '/config/cy/getBaseCyPage',
        params
    })
}
// 批量新增本位币信息
const addBatchBaseCyApi = params => {
    return httpPost({
        url: '/config/cy/batchSaveBaseCy',
        params
    })
}
// 批量修改本位币信息
const batchUpdateBaseCyApi = params => {
    return httpPost({
        url: '/config/cy/batchUpdateBaseCy',
        params
    })
}
// 批量创建机构下人民币汇率
const batchSaveRmbRateApi = params => {
    return httpPost({
        url: '/config/cy/batchSaveRmbRate',
        params
    })
}
// 单条修改本位币
const updateBaseCyApi = params => {
    return httpPost({
        url: '/config/cy/updateBaseCy',
        params
    })
}
// 单条新增本位币信息
const addBaseCyApi = params => {
    return httpPost({
        url: '/config/cy/saveBaseCy',
        params
    })
}
// 删除本位币信息
const deleteBaseCyApi = params => {
    return httpPost({
        url: '/config/cy/deleteBaseCy',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        params: qs.stringify(params)
    })
}
// 获取本位币信息
const getBaseCyInfoApi = params => {
    return httpGet({
        url: '/config/cy/getBaseCy',
        params
    })
}
// 获取机构下的人民币汇率
const getRmbRateApi = params => {
    return httpGet({
        url: '/config/cy/getRmbRateList',
        params
    })
}

//获取机构下某币种的人民币汇率
const getRmbRateApi2 = params => {
    return httpGet({
        url: '/config/cy/getRmbRate',
        params
    })
}

//获取机构设置的本位币
const getbaseCurrency = params => {
    return httpGet({
        url: '/config/cy/getBaseCyByOrgId',
        params
    })
}

// 获取机构下所有角色(根据用户查询)
const getRoleByOrg = params => {
    return httpGet({
        url: '/hr/role/getOrgRole',
        params
    })
}
// 获取机构的所有用户(分页)
const getUserUnderOrgApi = params => {
    return httpPost({
        url: '/hr/user/getUserUnderOrgPage',
        params
    })
}
// 通过关键字查找用户
const getUsersByKeyWordApi = params => {
    return httpGet({
        url: '/hr/user/getUsersByKeyWord',
        params
    })
}
// 保存用户分配的角色数据
const saveOrgRoleApi = ({ params, url }) => {
    return httpPost({
        url: `/hr/role/saveOrgRole?${qs.stringify(url)}`,
        params
    })
}
export {
    deleterRecordIdAttach,
    getOrgByUserId,
    getChildrenOrg,
    getCurrencyApi,
    getBaseCyApi,
    addBaseCyApi,
    batchSaveRmbRateApi,
    updateBaseCyApi,
    addBatchBaseCyApi,
    batchUpdateBaseCyApi,
    deleteBaseCyApi,
    getBaseCyInfoApi,
    getRmbRateApi,
    getRoleByOrg,
    getUserUnderOrgApi,
    getUsersByKeyWordApi,
    saveOrgRoleApi,
    getOrgById,
    getOrgByParentId,
    getRmbRateApi2,
    getbaseCurrency
}
