<template>
    <el-dialog :visible.sync="isVisible" :before-close="closeDialog">
        <div slot="title" class="header">
            <div class="header-left">
                <img src="@/assets/dg_left.png" alt="">
            </div>
            {{ setDialogParams.title }}
            <div class="header-right">
                <img src="@/assets/dg_right.png" alt="">
            </div>
        </div>
        <iframe v-if="!isSelect" class="iframe" :src="setDialogParams.url" :style="{'width':'100%','height':clientHeight*3/4 + 'px'}" frameborder="0"></iframe>
        <ChildComponent v-else :style="{'width':'100%','height':clientHeight*3/4 + 'px'}"></childComponent>
        <div slot="footer" class="footer">
            <div class="button">
                <el-button
                    type="primary"
                    class="btn-blue"
                    @click="save"
                    size="small"
                    v-debounce='3000'
                    >确定</el-button
                >
                <el-button
                    @click="cacel"
                    size="small"
                    v-debounce='3000'
                    >取消</el-button
                >
            </div>
        </div>
    </el-dialog>
</template>

<script>
import Vue from 'vue'
import { mapState, mapMutations } from 'vuex'
export default {
    data () {
        return {
            gridData: [],
            src: '/subcontractList',
            clientHeight: 0,
            isSelect: false
        }
    },
    computed: {
        ...mapState(['dialogVisible', 'setDialogParams', 'getDialogData']),
        isVisible () {
            return this.dialogVisible && this.$route.path !== this.setDialogParams.url
        },
    },
    mounted () {
        this.clientHeight = document.documentElement.clientHeight
        window.addEventListener('resize', this.changScreenWidth)
        const url = this.setDialogParams.url.split('?')[0]
        const urls = this.setDialogParams.url.split('?')
        console.log('🚀 ~ urls', urls)
        let com =  this.$router.options.routes.find(x=>x.path === url)
        if(com && urls.length <= 1) {
            this.isSelect = true
            Vue.component('ChildComponent', com.component)
        }else{
            this.isSelect = false
        }
        console.log('🚀 ~ this.isSelect', this.isSelect)
    },
    methods: {
        ...mapMutations(['setSelectedInfo']),
        //屏幕变化处理事件
        changScreenWidth () {
            this.clientHeight = document.documentElement.clientHeight
        },
        closeDialog () {
            this.$store.commit('setDialogVisible', false)
        },
        //保存
        save () {
            this.closeDialog()
            window.getPopData(this.getDialogData)
        },
        //取消
        cacel () {
            this.closeDialog()
            window.cancelPop()
        },
    },
}
</script>

<style lang="scss" scoped>
    /deep/ .el-dialog{
        box-shadow: 0 0 0;
        width: 67%;
        border-radius: 10px;
        margin-top: 8vh!important;
        .el-dialog__header{
            border-radius: 10px 10px 0 0;
            text-align: center;
            padding: 0px;
            .header{
                height: 30px;
                display: flex;
                background-color: #131532;
                justify-content: space-between;
                align-items: center;
                color: #f4f4f5;
                border-radius: 10px 10px 0 0 ;
                .header-left{
                    border-radius: 10px 0 0 0 ;
                    height: 30px;
                    img{
                        border-radius: 10px 0 0 0 ;
                        height: 30px;
                    }
                }
                .header-right{
                    border-radius: 0 10px 0 0 ;
                    height: 30px;
                    img{
                        border-radius: 0 10px 0 0 ;
                        height: 30px;
                    }
                }
            }
            .el-dialog__headerbtn{
                top: 6px;
            }
        }
        .el-dialog__body{
            padding: 0 10px !important;
        }
        .el-dialog__footer{
            border-radius: 0 0 10px 10px;
            background-color: #eff2f6;
        }
    }
</style>