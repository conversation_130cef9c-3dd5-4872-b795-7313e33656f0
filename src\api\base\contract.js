import '@/utils/request'
import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import qs from 'qs'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

// 获取所有合同类别
const getEquipmentCategory = params => {
    return httpPost({
        url: '/facilitybase/equipment/getEquipmentCategoryAll',
        params
    })
}
//分页查询装备
const getEquipmentList = params => {
    return httpPost({
        url: '/facilitybase/equipment/list/Category',
        params
    })
}
//新增装备
const addEquip = params => {
    return httpPost({
        url: '/facilitybase/equipment/create',
        params
    })
}
//修改装备
const updateEquip = params => {
    return httpPost({
        url: '/facilitybase/equipment/update',
        params
    })
}
//删除装备
const deleteEquip = params => {
    return httpGet({
        url: '/facilitybase/equipment/delete',
        params
    })
}
//修改装备类别
const editCategory = params => {
    return httpPost({
        url: '/facilitybase/equipment/update/category',
        params
    })
}
//新增装备类别
const addCategory = params => {
    return httpPost({
        url: '/facilitybase/equipment/create/category',
        params
    })
}
//删除装备类别
const deleteCategory = params => {
    return httpPostForm({
        url: '/facilitybase/equipment/delete/category',
        params
    })
}
//新增租赁限额
const addEquipTemporary = params => {
    return httpPost({
        url: '/facilitybase/leaseLimit/add',
        params
    })
}
//删除租赁限额
const deleteEquipTemporary = params => {
    return httpPostForm({
        url: '/facilitybase/leaseLimit/delete',
        params
    })
}
//根据id获取租赁限额
const getEquipTemporary = params => {
    return httpGet({
        url: '/facilitybase/leaseLimit/findById',
        params
    })
}
//租赁限额分页查询
const equipTemporaryList = params => {
    return httpPost({
        url: '/facilitybase/leaseLimit/listByEntity',
        params
    })
}
//租赁限额修改
const updataEquipTemporary = params => {
    return httpPost({
        url: '/facilitybase/leaseLimit/update',
        params
    })
}
//新增台班转换设置
const addShiftConversion = params => {
    return httpPost({
        url: '/facilitybase/workShift/create',
        params
    })
}
//修改台班转换设置
const updataShiftConversion = params => {
    return httpPost({
        url: '/facilitybase/workShift/update',
        params
    })
}
//台班转换设置列表
const ShiftConversionList = params => {
    return httpPost({
        url: '/facilitybase/workShift/AdvancedQurey',
        params
    })
}
//新增装备价格设置
const addEquipPrice = params => {
    return httpPost({
        url: '/facilitybase/priceSetting/create',
        params
    })
}
//价格设置查询(申请时间未知未排序)
const equipPriceList = params => {
    return httpPost({
        url: '/facilitybase/priceSetting/find/advanceQuery',
        params
    })
}
//删除装备价格设置
const deleteEquipPrice = params => {
    return httpPostForm({
        url: '/facilitybase/priceSetting/delete',
        params
    })
}
//修改装备价格设置
const updataEquipPrice = params => {
    return httpPost({
        url: '/facilitybase/priceSetting/update',
        params
    })
}
//根据id获取价格设置信息
const getEquipPriceDetail = params => {
    return httpGet({
        url: '/facilitybase/priceSetting/getDetails',
        params
    })
}
//模糊查询管理单位
const managementUnitList = params => {
    return httpPost({
        url: '/facilitybase/managementUnit/queryList',
        params
    })
}
//保存管理单位
const updateManagementUnit = params => {
    return httpPost({
        url: '/facilitybase/managementUnit/update',
        params
    })
}
//设置管理单位（多个）
const setManagementUnit = params => {
    return httpPost({
        url: '/facilitybase/managementUnit/updateuUnit',
        params
    })
}

//*****************合同类别维护*************** */
//创建合同类别
const setContractList = params => {
    return httpPost({
        url: '/subcontract/basicSettingsContractCategory/create',
        params
    })
}
//获取合同类别
const getContractList = params => {
    return httpGet({
        url: '/subcontract/basicSettingsContractCategory/get',
        params
    })
}
//添加修改合同类别
const upContractList = params => {
    return httpPost({
        url: '/subcontract/basicSettingsContractCategory/update',
        params
    })
}
// ***************************保证金******

//添加修改保证金数据
const addMarginData = params => {
    return httpPost({
        url: '/subcontract/basicSettingsBond/create',
        params
    })
}
//根据合同类别查询保证金
const getContractData = params => {
    return httpGet({
        url: '/subcontract/basicSettingsBond/get',
        params
    })
}
//获取保证金
const getMarginData = params => {
    return httpGet({
        url: '/subcontract/basicSettingsBond/getBond',
        params
    })
}
//获取保证金查询合同列表
const getContractListData = params => {
    return httpGet({
        url: '/subcontract/basicSettingsContractCategory/getForBond',
        params
    })
}
// ***************************合同限额配置******
//获取合同限额配置列表
const getBasicSettingsData = params => {
    return httpPost({
        url: '/subcontract/basicSettingsContractQuota/queryList',
        params
    })
}
//保存合同限额配置列表
const saveBasicSettingsData = params => {
    return httpPost({
        url: '/subcontract/basicSettingsContractQuota/create',
        params
    })
}
//删除合同限额配置列表
const deleteBasicSettingsData = params => {
    return httpPost({
        url: '/subcontract/basicSettingsContractQuota/delete',
        params
    })
}
//修改合同限额配置列表
const updateBasicSettingsData = params => {
    return httpPost({
        url: '/subcontract/basicSettingsContractQuota/update',
        params
    })
}
//id查询合同限额配置列表
const getByIdBasicSettingsData = params => {
    return httpGet({
        url: '/subcontract/basicSettingsContractQuota/getById/{billId}',
        params
    })
}
export {
    getEquipmentCategory,
    getEquipmentList,
    addEquip,
    updateEquip,
    addCategory,
    deleteEquip,
    editCategory,
    deleteCategory,
    addEquipTemporary,
    deleteEquipTemporary,
    getEquipTemporary,
    equipTemporaryList,
    updataEquipTemporary,
    addShiftConversion,
    updataShiftConversion,
    ShiftConversionList,
    addEquipPrice,
    equipPriceList,
    deleteEquipPrice,
    updataEquipPrice,
    managementUnitList,
    updateManagementUnit,
    setManagementUnit,
    getEquipPriceDetail,
    //以上为待修改数据
    setContractList,
    getContractList,
    upContractList,
    addMarginData,
    getContractData,
    getMarginData,
    getContractListData,
    getBasicSettingsData,
    saveBasicSettingsData,
    deleteBasicSettingsData,
    updateBasicSettingsData,
    getByIdBasicSettingsData
}
