import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

//装备管理 - 装备初始化 接口
const request = {

    //高级查询装备初始化
    equipInitList (params) {
        return httpPost({
            url: '/facilitymanagement/initialize/list/advanced',
            params
        })
    },
    //添加装备初始化
    equipInitAdd (params) {
        return httpPost({
            url: '/facilitymanagement/initialize/add',
            params
        })
    },
    //提交装备初始化
    equipInitCommit (params) {
        return httpPostForm({
            url: '/facilitymanagement/initialize/commit',
            params
        })
    },
    //删除装备初始化
    equipInitDelete (params) {
        return httpPostForm({
            url: '/facilitymanagement/initialize/delete',
            params
        })
    },
    //修改装备初始化
    equipInitUpdate (params) {
        return httpPost({
            url: '/facilitymanagement/initialize/update',
            params
        })
    },
    //获取装备初始化基础信息
    equipInitBaseInfo (params) {
        return httpPostForm({
            url: '/facilitymanagement/initialize/get',
            params
        })
    },
    //获取装备参数
    equipInitparam (params) {
        return httpGet({
            url: '/facilitymanagement/initialize/particulars/get/parameter',
            params
        })
    },
    //获取技改登记
    transformationRegist (params) {
        return httpGet({
            url: '/facilitymanagement/initialize/particulars/get/technically/registration',
            params
        })
    },

    //获取运行记录
    operationRecord (params) {
        return httpGet({
            url: '/facilitymanagement/initialize/particulars/get/operation/Record',
            params
        })
    },

    //获取检测记录
    testRecord (params) {
        return httpGet({
            url: '/facilitymanagement/initialize/particulars/get/detection/record',
            params
        })
    },

    //获取维修保养记录
    maintenanceRecords (params) {
        return httpGet({
            url: '/facilitymanagement/initialize/particulars/get/maintenance/log',
            params
        })
    },
    //获取事故记录
    accidentRecord (params) {
        return httpGet({
            url: '/facilitymanagement/initialize/particulars/get/accident/record',
            params
        })
    },

}

export default request
