<template>
    <div class="page">
        <div class="full" style="padding-right: 11px;">
            <div class="menu full" v-show="showMenu">
                <div class="title">
                    <img class="logo" src="../../assets/images/wuzi_shop.png" alt="">
                </div>
                <el-menu class="el-menu-vertical-demo" mode="vertical" background-color="#ffffff00"
                    text-color="#fff" active-text-color="#FFD41C" :default-active="defaultActive" :unique-opened="true" :router="true" @open="handleSubOpen">
                    <template v-for="item in visibleMenu">
                        <!-- 包含子1菜单 -->
                        <template v-if="item.children">
                            <el-submenu :key="item.menuId" :index="item.menuId.toString()" @click="changePath(1, item.menuName)" v-show="!item.hide">
                                <template slot="title">
                                    <div class="bar"></div>
                                    <img :src="images.gear2" style="margin-right: 10px"  alt=""/>
                                    <span>{{ item.menuName }}</span>
                                </template>
                                <div class="menu-item-box">
                                    <el-menu-item v-for="subitem in item.children" @click="changePath(2, subitem.menuName)" :key="subitem.menuId"
                                        :index="subitem.menuId" :route="subitem.route">
                                        <template slot="title">
                                            <img :src="images.dot" alt="" />
                                            <span>{{ subitem.menuName }}</span>
                                        </template>
                                    </el-menu-item>
                                </div>
                            </el-submenu>
                        </template>
                        <!-- 不包含子菜单 -->
                        <template v-else>
                            <el-menu-item
                                :key="item.menuId"
                                :index="item.menuId"
                                :route="item.route"
                                @click="changePath(0, item.menuName)"
                            >
                                <div class="bar"></div>
                                <img :src="images.gear2" style="margin-right: 10px" alt="" />
                                <span slot="title">{{ item.menuName }}</span>
                            </el-menu-item>
                        </template>
                    </template>
                </el-menu>
            </div>
            <div id="fold-btn" @click="showMenu = !showMenu"></div>
        </div>
        <div class="table-box">
            <div class="history">
                <top-btn-bar></top-btn-bar>
            </div>
            <div style="height: 100%; background-color: #fff;">
                <div class="router-box">
                    <top-step :stepInfo="steps" v-show="showSteps" />
                    <keep-alive>
                        <router-view v-if="$route.meta.keepAlive"></router-view>
                    </keep-alive>
                    <router-view v-if="!$route.meta.keepAlive"></router-view>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import axios from '../../axios/index'
import topStep from '../../components/topstep/topstep'
import topBtnBar from '../../components/topButtonBar'
import foldBtn from '../../assets/menu_close.png'
import openBtn from '../../assets/menu_open.png'
import { getParamsByCode } from '@/api/platform/system/systemParam'
import {
    gearActive,
    gear,
    gear2,
    gear2Active,
    rental,
    rentalActive,
    repair,
    repairActive,
    order,
    orderActive,
    dot,
    arrow,
} from '../../assets/images'
import { getMenu } from '@/api/platform/common/menu'
import { mapActions, mapState } from 'vuex'
export default {
    components: { topStep, topBtnBar },
    watch: {
        showMenu: {
            handler (newVal) {
                let btnImg = newVal ? `url(${foldBtn})` : `url(${openBtn})`
                $('#fold-btn').css('background-image', btnImg)
            }
        },
        $route: {
            handler () {
                this.setDefaultActive()
            },
            deep: true,
            immediate: true
        },
    },
    computed: {
        ...mapState(['userInfo', 'steps']),
        showSteps () {
            return !this.$route.path.includes('etail')
        },
        // 最终渲染的菜单
        visibleMenu () {
            let visibleMenu = this.menu.filter(item => (item.show && this.hasPermission(item.role)))
            visibleMenu.forEach(item => {
                if(item?.children.length > 0) {
                    item.children = item.children.filter(child => (child.show && this.hasPermission(child.role)))
                }
            })
            return visibleMenu
        },
    },
    data () {
        return {
            showMenu: true,
            useDynamicMenu: false,
            defaultActive: '',
            images: { gearActive, gear, gear2, gear2Active, rental, rentalActive, repair, repairActive, order, orderActive, dot, arrow, openBtn, foldBtn },
            tempList: [],
            steps: [
                { description: '内容管理', },
                { description: '新闻', }
            ],
            submenuName: '',
            // 导航菜单数据
            menu: [
                {
                    show: true,
                    menuName: '消息管理',
                    menuId: '1',
                    children: [
                        // { show: true, menuName: '发件箱', menuId: '1-1', route: { path: '/shopManage/mail/outBox', } },
                        { show: true, menuName: '收件箱', menuId: '1-2', route: { path: '/shopManage/product/inBox', } },
                    ],
                },
                /*{
                    show: true,
                    menuName: '商品管理',
                    menuId: '2',
                    children: [
                        { menuName: '出售中商品', menuId: '2-1', route: { path: '/shopManage/product/materialManage', } },
                        { menuName: '仓库中的商品', menuId: '2-2', route: { path: '/shopManage/product/materialWarehouse', } },
                        { menuName: '审核的商品', menuId: '2-3', route: { path: '/shopManage/product/materialCheck', } },
                    ],
                },*/
                {
                    show: true,
                    menuName: '订单管理',
                    menuId: '3',
                    children: [
                        { show: true, menuName: '全部订单', menuId: '3-1', route: { path: '/shopManage/order/searchOrder', } },
                        { show: true, menuName: '零星采购订单', menuId: '3-2', route: { path: '/shopManage/order/smallShipTwo/searchOrder', } },
                        { show: true, menuName: '零星采购发货单', menuId: '3-3', route: { path: '/shopManage/order/shiped/searchOrder', } },
                        // { show: true, menuName: '路桥结算订单', menuId: '3-4', route: { path: '/shopManage/rentService', } },
                        // { show: true, menuName: '未完成订单', menuId: '3-5', route: { path: '/shopManage/rentService', } },
                    ],
                },
                {
                    show: true,
                    menuName: '退货管理',
                    menuId: '4',
                    children: [
                        { show: true, menuName: '退货申请', menuId: '4-1', route: { path: '/shopManage/returnGoods/invoice', } },
                        { show: true, menuName: '退货记录', menuId: '4-2', route: { path: '/shopManage/returnGoods/record', } }
                    ],
                },
                {
                    show: true,
                    menuName: '发票管理',
                    menuId: '5',
                    children: [
                        { show: true, menuName: '发票申请', menuId: '5-1', route: { path: '/shopManage/invoice/invoice', } },
                        { show: true, menuName: '发票记录', menuId: '5-2', route: { path: '/shopManage/invoice/record', } }
                    ],
                },
                {
                    show: true,
                    menuName: '店铺管理',
                    menuId: '6',
                    children: [
                        { show: true, menuName: '店铺信息', menuId: '6-1', route: { path: '/shopManage/shop/shopInfoManage', } },
                    ],
                },
                {
                    show: true,
                    menuName: '统计分析',
                    menuId: '7',
                    children: [
                        { show: true, menuName: '订单统计', menuId: '7-1', route: { path: '/shopManage/analysis/order', } },
                        { show: true, menuName: '商品统计', menuId: '7-2', route: { path: '/shopManage/analysis/product', } }
                    ],
                },
            ],
            idList: [],
        }
    },
    methods: {
        ...mapActions(['changeSteps']),
        // 设置默认高亮的菜单项
        setDefaultActive () {
            let path = this.$route.path
            this.visibleMenu.forEach(item => {
                if(item.route && item.route.path === path) {
                    this.currentSteps = [{ description: item.menuName }]
                    this.defaultActive = item.menuId
                }
                if(!item.children) return
                item.children.forEach(subItem => {
                    if(subItem.route.path !== path) return
                    this.defaultActive = subItem.menuId
                    this.submenuName = item.menuName
                    this.currentSteps[0].description = item.menuName
                    this.currentSteps[1].description = subItem.menuName
                })
            })
        },
        // 传入权限字段，判断用户是否有此权限
        hasPermission (permission) {
            if(!permission || permission.length === 0) return true
            let { roles } = this.userInfo
            let hasPermission = true
            permission.forEach(item => {
                if(!hasPermission) return
                hasPermission = roles.includes(item) || this.userInfo[item] === 1
            })
            return hasPermission
        },
        getParamsByCodeM () {
            getParamsByCode({ code: 'materialUnit', size: 100 }).then(res => {
                if(res.length != 0) {
                    let materialUnit = []
                    res.forEach(t => {
                        let obj = {}
                        obj.label = t.keyValue
                        obj.value = t.keyValue2
                        materialUnit.push(obj)
                    })
                    this.$store.commit('setMaterialUnit', materialUnit)
                }
            })
        },
        // 获取菜单数据
        async getMenuData () {
            if (!this.useDynamicMenu) return
            this.menu = await getMenu({ type: 0 })
        },
        // 展开子菜单
        handleSubOpen (index) {
            this.menu.forEach(item => {
                if (item.menuId === index) {
                    this.submenuName = item.menuName
                }
            })
        },
        // 修改路径显示
        changePath (num, name) {
            if (this.currentSteps.length === 1) {
                num === 0 ? this.currentSteps[0].description = name : this.currentSteps.push({ description: name })
            } else {
                if (num === 0) {
                    this.currentSteps.pop()
                    this.currentSteps[0].description = name
                } else {
                    this.currentSteps[0].description = this.submenuName
                    this.currentSteps[1].description = name
                }
            }
            this.changeSteps(this.currentSteps)
        },
        getMenuItem (menuId, i) {
            axios.get('/permission/menu/getClassicMenu', { params: { isFull: true, menuId } }).then(res => {
                if (res.data.code === 200) {
                    this.tempList[i].children = res.data.data
                }
            })
        },
        getMenu () {
            axios
                .get('/permission/menu/getSpeedDial')
                .then(async res => {
                    await res.data.data.forEach(item => {
                        this.tempList.push(item)
                        this.idList.push(item.menuId)
                    })
                    this.idList.forEach((item, i) => {
                        this.getMenuItem(item, i)
                    })
                    setTimeout(() => {
                        this.menu = this.tempList
                    }, 700)
                })
        },
    },
    created () {
        this.steps = this.$store.state.steps
        this.getMenuData()
        this.getParamsByCodeM()
    },
}
</script>
<style scoped lang="scss">
@import '../../assets/css/backStage.css';

/deep/ .el-dialog__header {
    background: url(../../assets/test.png);
}

.page {
    display: flex;
    font-family: 'SourceHanSansCN-Regular';
    height: 100%;
}

.table-box {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    box-sizing: border-box;
    background-color: #eff2f6;

    &::-webkit-scrollbar {
        display: none;
    }

    .history {
        font-weight: bold;
        font-size: 17px;

        &>div {
            line-height: 84px;

            span {
                color: gray;
            }
        }
    }

    .router-box {
        height: 100%;
        display: flex;
        flex-direction: column;

        &>*:last-child {
            flex-grow: 1;
        }
    }
}
</style>
