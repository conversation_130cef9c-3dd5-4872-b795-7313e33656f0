<template>
  <div class="detailBox center">
    <div class="tabBox dfa mb20">
      <div @click="Changetabcurrent(0)" :class="[tabcurrent1 == 0 ? 'tabab' : '']">
        招标详情
      </div>
      <div v-show="login" @click="Changetabcurrent(1)" :class="[tabcurrent1 == 1 ? 'tabab' : '']">
        投标概况
      </div>
      <div v-show="login" @click="Changetabcurrent(2)" :class="[tabcurrent1 == 2 ? 'tabab' : '']">
        澄清提问
      </div>
    </div>
    <!--  招标详情     -->
    <div v-show="tabcurrent1 == 0" style="padding-bottom: 20px;">
      <div class="description center p20" >
        <el-button class="status" @click="showDetail(1)">招标公告</el-button>
        <el-button v-show="login" class="status" @click="showDetail(2)">包件信息</el-button>
        <el-button v-show="login" class="status" @click="showDetail(3)">招标清单</el-button>
      </div>
      <!-- 招标公告-->
      <div v-show="proclamation">
        <div  class ="description center p20" style="overflow: auto; height: 500px">
          <span v-html="form.tenderNotice"></span>
        </div>
      </div>
      <!--//包件信息-->
      <div v-show="balse" style="overflow: auto; height: 500px">
        <div class="lineBox">
          <div class="leftDiv">招标包件信息</div>
        </div>
        <el-table border :data="babalTable" style="width: 1288px;" :header-cell-style="{ background: '#f7f7f7' }">
          <el-table-column type="selection" width="54px">
          </el-table-column>
          <el-table-column prop="index" label="序号" width="80" align="center">
            <template v-slot="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column prop="itemNo" label="包件" align="center">
          </el-table-column>
          <el-table-column prop="name" label="包件名称" width="110" align="center">
          </el-table-column>
          <el-table-column prop="priceLimit" label="最高限价" width="110" align="center">
          </el-table-column>
          <el-table-column  label="操作" width="110" align="center">
            <template slot-scope="scope">
              <div class="pointer" style="color: rgba(33, 110, 198, 1);" @click="applyPackage(scope.row.dtlId)">报名</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--招标清单-->
      <div v-show="tenderList" style="padding-top: 20px;overflow: auto; height: 500px">
        <div v-for="item in tenderdtlTable " :key="item.id">
          <div class="lineBox">
            <div class="leftDiv">包件编号: <span>{{ item.itemNo }}</span></div>
            <div class="rightDiv">包件名称: <span>{{ item.name }}</span></div>
          </div>
          <el-table border :data="item.tenderDtlList" style="width: 800px;" :header-cell-style="{ background: '#f7f7f7' }" height="200">
            <el-table-column prop="type" label="物资类别" align="center">
            </el-table-column>
            <el-table-column prop="name" label="物资名称" width="110" align="center">
            </el-table-column>
            <el-table-column prop="spec" label="规格型号" width="110" align="center">
            </el-table-column>
            <el-table-column prop="unit" label="计量单位" width="110" align="center">
            </el-table-column>
            <el-table-column prop="texture" label="材质" width="110" align="center">
            </el-table-column>
            <el-table-column prop="num" label="数量" width="110" align="center">
            </el-table-column>
          </el-table>
        </div>
      </div>

    </div>
    <!--     投标概况      -->
    <div v-show="tabcurrent1 == 1" class="overflow">
      <div v-for="item in packageTable " :key="item.id">
        <div class="lineBox">
          <div class="leftDiv">包件编号: <span>{{ item.packageNo }}</span></div>
          <div class="rightDiv">包件名称: <span>{{ item.packageName }}</span></div>
        </div>
        <el-table border :data="item.tenderPackages" style="width: 1288px;"
                  :header-cell-style="{ background: '#f7f7f7' }">
          <el-table-column prop="supplierName" label="投标单位" align="center">
          </el-table-column>
          <el-table-column prop="contact" label="联系人" width="130" align="center">
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" width="160" align="center">
          </el-table-column>
          <el-table-column label="相应" width="110" align="center">
            <template>
              <img src="../assets/images/userCenter/md-check.png" width="20px" height="20px"/>
            </template>
          </el-table-column>
          <el-table-column label="标书购买" width="80" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.tenderPayment==null">未支付</div>
              <div v-else-if="scope.row.tenderPayment.mode==0"><img class="big-icon"
                                                                    src="../assets/images/userCenter/md-check.png"
                                                                    width="20px" height="20px"/></div>
              <div v-else-if="scope.row.tenderPayment.mode==1">
                <div v-if="scope.row.tenderPayment.status==1"><img class="big-icon"
                                                                   src="../assets/images/userCenter/md-check.png"
                                                                   width="20px" height="20px"/></div>
                <div v-if="scope.row.tenderPayment.status==2"><img class="big-icon"
                                                                   src="../assets/images/userCenter/md-check.png"
                                                                   width="20px" height="20px"/></div>
                <div v-if="scope.row.tenderPayment.status==3">审核不通过</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="标书下载" width="80" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isPackageAttachDown==1"><img class="big-icon"
                                                                src="../assets/images/userCenter/md-check.png"
                                                                width="20px" height="20px"/></div>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <el-table-column label="保证金" width="80" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.bondPayment==null">--</div>
              <div v-else>
                <div v-if="scope.row.bondPayment.type==2">
                  <div v-if="scope.row.bondPayment.status==1">转为履约保证金</div>
                  <div v-else-if="scope.row.bondPayment.status==2"><img class="big-icon"
                                                                        src="../assets/images/userCenter/md-check.png"
                                                                        width="20px" height="20px"/></div>
                </div>
                <div v-else-if="scope.row.bondPayment.type==1">
                  <div v-if="scope.row.bondPayment.mode==0"><img class="big-icon"
                                                                 src="../assets/images/userCenter/md-check.png"
                                                                 width="20px" height="20px"/></div>
                  <div v-if="scope.row.bondPayment.mode==1">
                    <div v-if="scope.row.bondPayment.status==1"><img class="big-icon"
                                                                     src="../assets/images/userCenter/md-check.png"
                                                                     width="20px" height="20px"/></div>
                    <div v-else-if="scope.row.bondPayment.status==2"><img class="big-icon"
                                                                          src="../assets/images/userCenter/md-check.png"
                                                                          width="20px" height="20px"/></div>
                    <div v-else-if="scope.row.bondPayment.status==3">审核不通过</div>
                  </div>
                </div>

              </div>
            </template>
          </el-table-column>
          <el-table-column  label="澄清提问" width="110" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.subStep>=5">
                <div v-if="scope.row.notReplyQuestionCount<=0">查看回答</div>
                <div v-else>提问回答</div>
              </div>
              <div v-if="scope.row.isApplyComplete==1">查看报价文件</div>
            </template>
          </el-table-column>
          <el-table-column prop="addendum" label="补遗确认" width="110" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isPackageAddendumConfirm=1"><img class="big-icon"
                                                                    src="../assets/images/userCenter/md-check.png"
                                                                    width="20px" height="20px"/></div>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <el-table-column  label="报价" width="110" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isApplyComplete=1"><img class="big-icon"
                                                           src="../assets/images/userCenter/md-check.png"
                                                           width="20px" height="20px"/></div>
              <div v-else>--</div>
            </template>
          </el-table-column>
        </el-table>

      </div>

      <!--                        <div v-for="item in tenderPurchasePlatform.packages " :key="item" >-->
      <!--                          <div class="lineBox">-->
      <!--                            <div class="leftDiv">包件编号: <span>{{item.packageNo}}</span></div>-->
      <!--                            <div class="rightDiv">包件名称: <span>{{item.packageName}}</span></div>-->
      <!--                          </div>-->

      <!--                        </div>-->
    </div>
    <!--          澄清提问-->
    <div v-show="tabcurrent1 == 2">
        <el-table border :data="tenderPackages" style="width: 1288px;min-height: 400px;"
                  :header-cell-style="{ background: '#f7f7f7' }">
          <el-table-column prop="index" label="序号" width="80" align="center">
            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column prop="title" label="标题" align="center">
          </el-table-column>
          <el-table-column prop="createTime" label="提问时间" width="110" align="center">
          </el-table-column>
          <el-table-column  label="附件" width="110" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.fileUrl.IsNullOrWhiteSpace()==false">{{scope.row.fileName}}</div>
              <div v-else>--</div>
            </template>
          </el-table-column>
        </el-table>

    </div>
  </div>
</template>
<script>
import {
    getbalseList,
    getTenderdtlList,
    getTenderPackageSubcontractorList,
    getQuesionList,
    getPurchasePlatformDate
} from '@/api/frontStage/bidding'
import { addtenderApply } from '@/api/frontStage/tender/tenderEnroll.js'
import { mapState } from 'vuex'
export default {
    created () {
        this.isLogin()
    },
    props: ['tenderInfo'],
    data () {
        return {
            login: true,
            tenderdtlTable: [],
            tenderPackages: [],
            packageTable: [],
            tenderDtls: [],
            tenderPurchasePlatform: {},
            tenderNotice: '', //招标公告
            TenderCommentList: [],
            tenderTable: [],
            babalTable: [],
            TenderPackageVo: [],
            num: 1,
            tabcurrent1: 0,
            form: this.tenderInfo,
            proclamation: true,
            balse: false,
            tenderList: false,
            dataListSelections: [], //表格选中的数据
        }
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        isLogin () {
            if (!(localStorage.getItem('token'))) {
                this.login = false
            }
        },
        balseList () {
            getbalseList({ id: this.form.billId }).then(res=>{
                this.babalTable = res
            })
        },
        //外部接口，获得采购平台数据
        // eslint-disable-next-line
        async purchasePlatform (billNo) {
            let params = { Key: this.form.billNo }
            getPurchasePlatformDate(params).then(res => {
                this.tenderPurchasePlatform = res
                this.packageTable = this.tenderPurchasePlatform.packages
                this.packageTable.forEach(item => item.tenderPackages = [])
            })
        },
        async Changetabcurrent (i) {
            this.tabcurrent1 = i
            if (i == 0) {
                this.showDetail(1)
            } else if (i == 1) {
                await this.purchasePlatform(this.form.billNo)
                this.tenderPackages = this.tenderPurchasePlatform.tenderPackages
                if (this.packageTable.length > 0) {
                    for (let i = 0; i < this.packageTable.length; i++) {
                        let tenderPackagesList = new Set([])
                        if (this.tenderPackages.length > 0) {
                            console.log('this.tenderPackages', this.tenderPackages)
                            if (this.tenderPackages.length > 0) {
                                for (let j = 0; j < this.tenderPackages.length; j++) {
                                    if (this.packageTable[i].id == this.tenderPackages[j].bidPackageId) {
                                        tenderPackagesList.add(this.tenderPackages[j])
                                    }
                                }
                            }
                        }
                        this.packageTable[i].tenderPackages = [...tenderPackagesList]
                    }
                }

            } else if (i == 2) {
                this.quesionList()
            }
        },
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        //招标报名
        applyPackage (packageId) {
            addtenderApply({ id: packageId }).then(res=>{
                if (res.code == 200) {
                    this.$message({
                        type: 'success',
                        message: res.message
                    })
                }else {
                    this.$message({
                        message: res.message,
                        type: 'warning'
                    })
                }
            })
        },
        showDetail (i) {
            if (i == 1) {
                this.proclamation = true
                this.balse = false
                this.tenderList = false
            }else  if (i == 2) {
                this.proclamation = false
                this.balse = true
                this.tenderList = false
                this.balseList()
            }else  if (i == 3) {
                this.proclamation = false
                this.balse = false
                this.tenderList = true
                this.getTenderdtlLists()
            }
        },
        getTenderdtlLists () {
            getTenderdtlList({ billId: this.form.billId }).then(res=>{
                this.tenderdtlTable = res
            })
        },
        getTenderPackageSubcontractor () {
            getTenderPackageSubcontractorList({ id: this.form.billId }).then(res=>{
                this.TenderPackageVo = res
            })
        },
        quesionList () {
            getQuesionList({ id: this.form.billId }).then(res=>{
                this.TenderCommentList = res
            })
        }
    }
}
</script>
<style scoped lang="scss">
.root {
    padding-bottom: 50px;
    background-color: #f5f5f5;
}

span {
  line-height: 1;
}

.row {
  margin-bottom: 20px;
}

.main {
  width: 1326px;
  margin: 0 auto;
  margin-top: 10px;

  .history {
    width: 100%;
    height: 40px;
    margin-top: 10px;
    padding-left: 20px;
    font-size: 12px;
    line-height: 40px;
    background-color: #fff;
    position: relative;

    div {
      width: 64px;
      height: 28px;
      text-align: center;
      line-height: 28px;
      border: 1px solid rgba(230, 230, 230, 1);
      color: rgba(153, 153, 153, 1);
      position: absolute;
      top: 6px;
      right: 10px;
      cursor: pointer;
      user-select: none;
    }
  }
}

.overflow {
  height: 500px;
  overflow: hidden;
  overflow-y: scroll;
}

.status {
  width: 142px;
  height: 40px;
  opacity: 1;
  border-radius: 1px;
  font-size: 16px;
  font-weight: 400;
  text-align: center;
  line-height: 40px;
  margin-top: 15px;
}

.topBox {
  width: 1326px;
  padding: 20px 21px 20px 19px;
  background-color: #fff;
  padding-bottom: 30px;
  position: relative;
}

.title {
  text-align: center;
  font-size: 20px;
  font-weight: 500;
  padding-top: 20px;
  padding-bottom: 54px;
  border-bottom: 1px dashed rgba(229, 229, 229, 1);;
}

.el-table {
  margin: auto;
}

.infoBox {

  padding-top: 32px;

}

.lineBox {

  height: 30px;
  padding-left: 205px;
  margin-top: 21px;
  font-size: 14px;
  overflow: hidden;
  display: flex;

  span {
    margin-left: 10px;
  }

  .leftDiv, .rightDiv {
    width: 20%;
    display: flex;
    align-items: flex-start;

    .tit-img {
      width: 20px;
      height: 20px;
      margin-right: 11px;
    }
  }

}

.button_bule {
  width: 142px;
  height: 40px;
  opacity: 1;
  background: #226FC7;
  font-size: 16px;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  text-align: center;
  line-height: 40px;
  margin-top: 15px;
  position: relative;
  left: 40%;
}

.logImg {
  z-index: 9999;
  width: 136px;
  height: 136px;
  border: 1px dashed rgba(229, 229, 229, 1);
  position: absolute;
  right: 110px;
  top: 0;
}

.detailBox {
  width: 1326px;
  min-height: 605px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(229, 229, 229, 1);
  margin-top: 60px;

  .tabBox {
    width: 1324px;
    height: 60px;
    border-bottom: 1px solid rgba(229, 229, 229, 1);
    background: rgba(250, 250, 250, 1);

    div {
      width: 160px;
      height: 60px;
      text-align: center;
      line-height: 60px;
      font-size: 18px;
      cursor: pointer;
    }

    .tabab {
      background: #fff;
      border-top: solid 2px rgba(34, 111, 199, 1);
    }
  }

  .description {
    width: 1286px;
    min-height: 108px;
    border: 1px solid rgba(230, 230, 230, 1);
  }

  .row {
    padding-left: 27px;
    padding-right: 240px;

    .item {
      span {
        font-size: 16px;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
      }

      div {
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 1);
      }
    }
  }

  .han {
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    padding-left: 30px;
    margin-top: 30px;
  }

  .descriptionsBox {
    padding: 20px;
  }
}
</style>