import service from '@/utils/request'

const { httpPost, httpGet } = service

const getList = params => {
    return httpPost({
        url: '/materialMall/platform/processConfig/page',
        params
    })
}

const getProcessConfigDtlById = params => {
    return httpGet({
        url: '/materialMall/platform/processConfig/getProcessConfigDtlById',
        params
    })
}

const update = params => {
    return httpPost({
        url: '/materialMall/platform/processConfig/update',
        params
    })
}

const create = params => {
    return httpPost({
        url: '/materialMall/platform/processConfig/create',
        params
    })
}

const del = params => {
    return httpGet({
        url: '/materialMall/platform/processConfig/delete',
        params
    })
}
/**
 *  获取提交用户角色
 * @param params 流程定义id
 * @returns {*}
 */
const getSubmitUser = params => {
    return httpGet({
        url: '/materialMall/platform/processConfig/getSubmitUser',
        params
    })
}
/**
 *  获取审核用户角色
 * @param params 业务id
 * @returns {*}
 */
const getAuditUser = params => {
    return httpGet({
        url: '/materialMall/platform/processConfig/getAuditUser',
        params
    })
}
/**
 *  获取审核日志
 * @param params 业务id
 * @returns {*}
 */
const getAuditLog = params => {
    return httpGet({
        url: '/materialMall/platform/processConfig/getAuditLog',
        params
    })
}
export {
    getList,
    getProcessConfigDtlById,
    update,
    create,
    del,
    getSubmitUser,
    getAuditUser,
    getAuditLog
}