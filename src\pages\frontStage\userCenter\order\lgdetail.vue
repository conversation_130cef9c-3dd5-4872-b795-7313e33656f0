<template>
    <div class="root" v-loading="showLoading">
<!--        <div class="progress box center mb20">-->
<!--            <div class="list-title dfa">订单详情</div>-->
<!--            <div class="content">-->
<!--                <div class="progress-box df">-->
<!--                    <div class="step dfa">-->
<!--                        <img src="../../../../assets/images/userCenter/确认订单.png" alt="">-->
<!--                        <span>确认订单信息</span>-->
<!--                    </div>-->
<!--                    <img src="../../../../assets/images/userCenter/进度.png" alt="">-->
<!--                    <div class="step dfa">-->
<!--                        <img src="../../../../assets/images/userCenter/关联任务.png" alt="">-->
<!--                        <span>关联计划单</span>-->
<!--                    </div>-->
<!--                    <img src="../../../../assets/images/userCenter/进度.png" alt="">-->
<!--                    <div class="step dfa">-->
<!--                        <img src="../../../../assets/images/userCenter/发货.png" alt="">-->
<!--                        <span>通知供方发货</span>-->
<!--                    </div>-->
<!--                    <img src="../../../../assets/images/userCenter/进度.png" alt="">-->
<!--                    <div class="step dfa">-->
<!--                        <img src="../../../../assets/images/userCenter/收货成功.png" alt="">-->
<!--                        <span>确认收货</span>-->
<!--                    </div>-->
<!--                    <img src="../../../../assets/images/userCenter/进度.png" alt="">-->
<!--                    <div class="step dfa">-->
<!--                        <img src="../../../../assets/images/userCenter/财务结算.png" alt="">-->
<!--                        <span>系统结算</span>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
        <div class="order-info box center">
            <div class="list-title dfa">订单信息</div>
            <div class="content p20">
                <h3>卖家信息</h3>
                <div class="info">
                    <el-row>
                        <el-col :span="6" :offset="0">店铺：{{orderInfo.shopName}}</el-col>
                        <el-col :span="6" :offset="0">联系人：{{orderInfo.linkMan}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" :offset="0">联系电话：{{orderInfo.contactNumber}}</el-col>
                    </el-row>
                </div>
            </div>
            <div class="content order p20">
                <h3>订单信息</h3>
                <div class="info">
                    <el-row>
                        <el-col :span="6" :offset="0">订单编号：{{orderInfo.orderSn}}</el-col>
                        <el-col :span="6" :offset="0">计划编号：{{orderInfo.billNo}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" :offset="0">确认时间：{{orderInfo.flishTime}}</el-col>
                        <el-col :span="6" :offset="0">创建时间：{{orderInfo.gmtCreate}}</el-col>
                    </el-row>
                </div>
            </div>
            <div class="p20 order-list">
                <el-table :data="orderInfo.orderProduct" class="vertical-center-table">
                    <el-table-column label="商品清单" width="500">
                        <template v-slot="scope">
                            <div class="info df" @click="$router.push({path: '/mFront/productDetail', query: {productId: scope.row.productId}})">
                                <img :src="scope.row.productImg ? imgUrlPrefixAdd + scope.row.productImg : require('@/assets/images/img/queshen5.png')" alt="">
                                <div class="detail">
                                    <h4>{{ scope.row.productName }}</h4>
                                    <div>品牌：{{ scope.row.brandName }}</div>
                                    <div>规格：{{ scope.row.skuName }}</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="税率" width="149">
                        <template v-slot="scope">
                            <div class="num">
                                {{scope.row.taxRate}}%
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="税额" width="149">
                        <template v-slot="scope">
                            <div class="num">
                                {{fixed2((scope.row.productPrice - scope.row.noTaxUnitPrice),2) }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="单价" width="194">
                        <template v-slot="scope">
                            <div class="price">
                                <!-- <div>￥{{ scope.row.productPrice }}</div>
                                <div>单位：{{ scope.row.unit }}</div> -->
                                <div>不含税：￥{{fixed2(scope.row.noTaxUnitPrice)}}</div>
                                <div>含税：￥{{fixed2(scope.row.productPrice)}}</div>
                                <div>单位：{{ scope.row.unit }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="数量" width="149" align="center">
                        <!-- <template v-slot="scope">
                            <div class="num">
                                <el-input-number
                                    v-if="orderInfo.state == 6"
                                    size="mini" v-model="scope.row.buyCounts"
                                    :min="0" :precision="4" :step="0.1" max="999999"
                                    @change="changeSelectQtyM(scope.row)" ></el-input-number>
                                <span v-else>{{scope.row.buyCounts}}</span>
                            </div>
                        </template> -->
                        <template v-slot="scope">
                            <div class="num">
                                x{{scope.row.buyCounts}}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="总价" align="center">
                        <template v-slot="scope">
                            <!-- <div class="total">￥{{scope.row.totalAmount}}</div> -->
                            <div>不含税：￥{{fixed2(scope.row.numTotalNoRatePrice)}}</div>
                            <div>含税：￥{{fixed2(scope.row.totalAmount)}}</div>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="sum mt20">商品合计：¥{{orderInfo.actualAmount}}</div>
            </div>
            <div class="logistic p20">
                <div style="display: flex;justify-content: space-between">
                    <h3>订单信息</h3>
                    <h3 @click="checkedAddressM" v-if="orderInfo.state == 6">切换收货地址</h3>
                </div>
                <div class="info">
                    <el-row>
                        <el-col :span="24" :offset="0">收货地址：{{orderInfo.receiverName}}  {{orderInfo.receiverMobile}}  {{orderInfo.receiverAddress}}</el-col>
                    </el-row>
<!--                    <el-row>-->
<!--                        <el-col :span="24" :offset="0">运送方式：{{orderInfo.deliveryType}}</el-col>-->
<!--                    </el-row>-->
                    <el-row v-if="orderInfo.deliveryFlowId != null">
                        <el-col :span="24" :offset="0">物流单号：{{orderInfo.deliveryFlowId}}</el-col>
                    </el-row>
                    <el-row v-if="orderInfo.logisticsCompany != null">
                        <el-col :span="24" :offset="0">物流公司：{{orderInfo.logisticsCompany}}</el-col>
                    </el-row>
                    <el-row v-if="orderInfo.deliveryTime != null">
                        <el-col :span="24" :offset="0">发货时间：{{orderInfo.deliveryTime}}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" :offset="0">备注：{{orderInfo.orderRemark}}</el-col>
                    </el-row>
                </div>
            </div>
        </div>
            <div class="confirm-btn">
                <button @click="saveQty" v-if="orderInfo.state == 0" style="margin-right: 30px">保存</button>
                <button @click="handleConfirm">返回</button>
            </div>
        <publicity></publicity>
        <el-dialog class="front" title="" :visible.sync="addrDialogVisible">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>选择收货地址</div>
                    </div>
                    <div class="dialog-close" @click="addrDialogVisible = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <el-table :data="addrList" @row-click="handleCurrentInventoryClick" v-loading="addressLoading">
                <el-table-column label="收货地址" label-width="560" prop="addr"></el-table-column>
                <el-table-column label="联系人" label-width="152" prop="name"></el-table-column>
                <el-table-column label="联系电话" label-width="110" prop="tel"></el-table-column>
                <el-table-column label="操作" label-width="">
                    <template v-slot="scope">
                        <span @click.stop="handleEditAddr(scope.row)" class="edit-btn">编辑</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="add pointer" @click="createAddress">+ 新增</div>
        </el-dialog>
        <el-dialog class="front" :visible.sync="addDetailDialog" top="8vh">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>选择收货地址</div>
                    </div>
                    <div class="dialog-close" @click="addDetailDialog = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <!-- 弹框内容 -->
            <div class="dialog-body center">
                <el-form :model="userAddressForm" ref="addAddressRef" :rules="userAddressFormRules" label-width="80px" :inline="false" label-position="top">
                    <el-form-item label="收货人：" prop="receiverName">
                        <el-input v-model="userAddressForm.receiverName" placeholder="请输入收货人姓名"></el-input>
                    </el-form-item>
                    <el-form-item class="tel" label="手机号码：" prop="receiverMobile">
                        <span>+86</span><el-input v-model="userAddressForm.receiverMobile" placeholder="请输入手机号码"></el-input>
                    </el-form-item>
                    <el-form-item label="选择地址：" prop="detailAddress">
                        <el-cascader
                            size="large"
                            :options="addressData"
                            v-model="selectAddressOptions"
                            @change="handleAddressChange">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item class="address" label="详细地址：" prop="detailAddress">
                        <el-input v-model="userAddressForm.detailAddress" placeholder="请输入详细收货地址"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer">
                <button class="butSub" @click="createAddressM">保存</button>
            </span>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import publicity from '../../components/publicity.vue'
import { toFixed } from '@/utils/common'
import { updateDZLGOrderItemQty, getOrderDetail, updateOrderInfo } from '@/api/frontStage/order'
import { create, getList } from '@/api/frontStage/shippingAddr'
export default {
    components: { publicity },
    data () {
        return {
            addressLoading: false,
            changeSelectQtyRowDate: [],
            userAddressFormRules: {
                receiverName: [
                    { required: true, message: '请输入收件人', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                receiverMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
            },
            // 地址
            addressData: regionData, // 地址数据
            addDetailDialog: false,
            selectAddressOptions: [],
            userAddressForm: { // 新增编辑地址表单
                detailAddress: null,
            },
            receiver: {},
            submitOrderLoading: false,
            addrList: [],
            addrDialogVisible: false,
            showLoading: false,
            orderInfo: {
            },
            totalPrice: '130000.00',
            spanArr: [],
            pos: null
        }
    },
    methods: {
        // 地址选择
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.userAddressForm.province = province
            this.userAddressForm.city = city
            this.userAddressForm.county = county
            this.userAddressForm.detailAddress = province + city + county
        },
        // 改变数量
        changeSelectQtyM (row) {
            if (row.buyCounts == null) {
                row.buyCounts = 0
            }
            if (this.changeSelectQtyRowDate.length === 0) {
                this.changeSelectQtyRowDate.push({
                    orderItemId: row.orderItemId,
                    qty: row.buyCounts,
                })
                return
            }
            let flag = false
            this.changeSelectQtyRowDate.forEach(t => {
                if (t.orderItemId === row.orderItemId) {
                    t.qty = row.buyCounts
                    flag = true
                }
            })
            if (!flag) {
                this.changeSelectQtyRowDate.push({
                    orderItemId: row.orderItemId,
                    qty: row.buyCounts,
                })
            }
        },
        // 创建编辑地址统一接口
        createAddressM () {
            this.$refs.addAddressRef.validate(valid => {
                if (valid) {
                    create(this.userAddressForm).then(res => {
                        if(res.code == 200) {
                            this.$message({
                                message: res.message,
                                type: 'success'
                            })
                            this.getAddRess()
                            this.addDetailDialog = false
                        }
                    })
                }
            })
        },
        // 创建
        createAddress () {
            this.userAddressForm = {
                detailAddress: null,
            },
            this.selectAddressOptions = []
            this.addDetailDialog = true
        },
        // 编辑地址
        handleEditAddr (row) {
            let obj = {
                addressId: row.addressId,
                detailAddress: row.addr,
                receiverName: row.name,
                receiverMobile: row.tel,
            }
            this.userAddressForm = obj
            //地址选择器回显
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
            this.addDetailDialog = true
        },
        // 地址表格点击
        handleCurrentInventoryClick (row) {
            let params = {
                orderId: this.orderInfo.orderId,
                receiverName: row.name,
                receiverMobile: row.tel,
                receiverAddress: row.addr,
            }
            this.addressLoading = true
            updateOrderInfo(params).then(res => {
                if(res.code != null && res.code == 200) {
                    this.$message.success('修改成功！')
                    this.addrDialogVisible = false
                    this.getOrderDetailM()
                }
                this.addressLoading = false
            }).catch(() => {
                this.addressLoading = false
            })
        },
        // 获取地址
        getAddRess () {
            // 获取收货地址
            this.addressLoading = true
            getList({ page: 1, limit: 30 }).then(res => {
                if(!res.list[0]) return
                let address = []
                // 显示默认地址
                res.list.forEach(item => {
                    let obj = {
                        addressId: item.addressId,
                        checked: false,
                        addr: item.detailAddress,
                        name: item.receiverName,
                        tel: item.receiverMobile,
                        province: item.province,
                        city: item.city,
                        county: item.county,
                    }
                    address.push(obj)
                })
                this.addrList = address
            }).finally(() => {
                this.addressLoading = false
            })
        },
        // 切换地址
        checkedAddressM () {
            this.getAddRess()
            this.addrDialogVisible = true
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        getOrderDetailM () {
            this.showLoading = true
            getOrderDetail({ orderSn: this.$route.query.orderSn }).then(res=>{
                this.orderInfo = res
                this.orderInfo.orderProduct.forEach(subItem =>{
                    //遍历订单商品，计算税额、含税单价、不含税单价、含税总价、不含税总价
                    const taxRate = subItem.taxRate / 100 || 0 // 处理未设置税率的情况
                    const total = Number(subItem.productPrice) * subItem.buyCounts
                    if (taxRate > 0) {
                        // 不含税单价（保留2位小数）
                        const noTaxUnitPrice = this.fixed2(Number(subItem.productPrice) / (1 + taxRate))
                        subItem.noTaxUnitPrice = noTaxUnitPrice
                        // 不含税总价
                        subItem.numTotalNoRatePrice = this.fixed2(noTaxUnitPrice * subItem.buyCounts)
                    } else {
                        subItem.numTotalNoRatePrice = this.fixed2(total)
                        subItem.noTaxUnitPrice = subItem.productPrice
                    }
                })
            }).finally(() => {
                this.showLoading = false
            })
        },
        saveQty () {
            let newDtlArr = this.changeSelectQtyRowDate.filter(t => t.qty != 0)
            if (newDtlArr.length === 0) {
                return this.$message.error('未修改订单数量！')
            }
            this.clientPop('info', '您确认要修改订单数量吗？', async () => {
                this.showLoading = true
                updateDZLGOrderItemQty( newDtlArr).then(res => {
                    if (res.code === 200) {
                        this.clientPop('suc', res.message, () => {
                            this.changeSelectQtyRowDate = []
                            this.getOrderDetailM()
                        })
                    }
                    if(res.code != null && res.code != 200) {
                        this.clientPop('warn', res.message)
                    }
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        handleConfirm () {
            this.$router.go(-1)
        },
    },
    created () {
        this.getOrderDetailM()
    }
}
</script>
<style scoped lang="scss">
.root {
    height: 100%;
    padding-top: 20px;
    background-color: #f5f5f5;

    .box {
        width: 1326px;
        background-color: #fff;
    }
}

.list-title {
    height: 50px;
    padding: 15px 20px;
    font-size: 18px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.progress {
    .content {
        padding: 40px 20px;
    }

    .progress-box {
        padding: 0 40px;
        align-items: flex-start;
        justify-content: center;

        &>img {
            margin-top: 6px;
        }

        .step {
            min-width: 68px;
            flex-direction: column;

            img {
                width: 28px;
                height: 28px;
                margin-bottom: 20px;
            }
        }
    }
}

.sheet {
    /deep/ .el-table {
        border: 1px solid rgba(230, 230, 230, 1);

        thead th {
            height: 45px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            background-color: rgba(247, 247, 247, 1);
        }

        .el-table__body {
            .el-table__row {
                height: 46px;
            }
        }

        .el-table-cell {
            color: rgba(51, 51, 51, 1);
        }
    }
}

.order-info {
    margin-bottom: 20px;

    h3 {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 500;
    }

    .el-row {
        margin-bottom: 16px;
        font-size: 14px;
        color: rgba(51, 51, 51, 1);
    }

    .content:not(.order) {
        border-bottom: 1px solid rgba(230, 230, 230, 1);
    }
    .sum {
        font-size: 14px;
        text-align: right;
        color: rgba(51, 51, 51, 1);
    }
    .order-list {
        border-bottom: 1px solid rgba(230, 230, 230, 1);
    }
}
.confirm-btn {
    padding-bottom: 40px;
    text-align: center;
    button {
        width: 150px;
        height: 40px;
        color: #fff;
        font-size: 18px;
        background-color: rgb(48, 100, 212);
    }
}

/deep/ .el-dialog {
    width: 1000px;
    //width: 1120px;
    min-height: 326px;
    .dialog-body {
        width: 500px;
        padding-top: 30px;

        .el-form-item {
            margin-bottom: 14px;
            &:last-of-type {margin-bottom: 20px;}
        }

        .el-form-item__label {
            // height: 14px;
            // margin-bottom: 20px;
            padding-bottom: 0;
            color: #999;
        }

        .el-input__inner {
            width: 300px;
            height: 35px;
            border: 1px solid rgba(217, 217, 217, 1);
            border-radius: 0;
        }
        .address .el-input__inner {width: 500px;}
        .tel {
            .el-form-item__content {
                display: flex;
                span {
                    margin-right: 10px;
                    color: #333;
                }
            }
            .el-input, .el-input__inner {width: 266px;}
        }
    }
    .butSub {
        width: 80px;
        height: 40px;
        font-size: 16px;
        color: #fff;
        background-color: #216EC6;
        margin-left: 100px;
    }
    .el-table {
        margin-top: 20px;
        border: 1px solid rgba(230, 230, 230, 1);
        // border-bottom: 0;
        font-size: 14px;
        .edit-btn {
            color: rgba(34, 111, 199, 1);
            cursor: pointer;
        }
    }
    .add {
        width: 80px;
        height: 30px;
        margin: 18px 0 40px 0;
        line-height: 30px;
        text-align: center;
        color: rgba(33, 110, 198, 1);
        border: 1px solid rgba(33, 110, 198, 1);
    }
    .el-table__header {
        .cell{
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
        }
        .el-checkbox {display: none;}
    }
    .el-dialog__footer {
        border: 0;
        text-align: center;
        button {
            width: 80px;
            height: 40px;
        }
        & button:first-child {
            margin-right: 20px;
            color: rgba(128, 128, 128, 1);
            background-color: rgba(230, 230, 230, 1);
        }
        & button:last-child {
            color: #fff;
            background-color: rgba(33, 110, 198, 1);
        }
    }
}
</style>