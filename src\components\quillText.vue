<template>
  <quill-editor
      ref="QuillEditor"
      class='editor'
      :content="form"
      :options="editorOption"
      @blur="onEditorBlur($event)"
      @focus="onEditorFocus($event)"
      @ready="onEditorReady($event)"
      @change="onEditorChange($event)"
  />
</template>

<script>
import { uploadFile } from '../api/platform/common/file'
import { Quill, quillEditor } from 'vue-quill-editor'
// eslint-disable-next-line no-unused-vars
import { container, ImageExtend, QuillWatch } from 'quill-image-extend-module'
// import resizeImage from 'quill-image-resize-module'
// import { ImageDrop } from 'quill-image-drop-module'

import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
// toolbar标题
const titleConfig = [
    { Choice: '.ql-insertMetric', title: '跳转配置' },
    { Choice: '.ql-bold', title: '加粗' },
    { Choice: '.ql-italic', title: '斜体' },
    { Choice: '.ql-underline', title: '下划线' },
    { Choice: '.ql-header', title: '段落格式' },
    { Choice: '.ql-strike', title: '删除线' },
    { Choice: '.ql-blockquote', title: '块引用' },
    { Choice: '.ql-code', title: '插入代码' },
    { Choice: '.ql-code-block', title: '插入代码段' },
    { Choice: '.ql-font', title: '字体' },
    { Choice: '.ql-size', title: '字体大小' },
    { Choice: '.ql-list[value="ordered"]', title: '编号列表' },
    { Choice: '.ql-list[value="bullet"]', title: '项目列表' },
    { Choice: '.ql-direction', title: '文本方向' },
    { Choice: '.ql-header[value="1"]', title: 'h1' },
    { Choice: '.ql-header[value="2"]', title: 'h2' },
    { Choice: '.ql-align', title: '对齐方式' },
    { Choice: '.ql-color', title: '字体颜色' },
    { Choice: '.ql-background', title: '背景颜色' },
    { Choice: '.ql-image', title: '图像' },
    { Choice: '.ql-video', title: '视频' },
    { Choice: '.ql-link', title: '添加链接' },
    { Choice: '.ql-formula', title: '插入公式' },
    { Choice: '.ql-clean', title: '清除字体格式' },
    { Choice: '.ql-script[value="sub"]', title: '下标' },
    { Choice: '.ql-script[value="super"]', title: '上标' },
    { Choice: '.ql-indent[value="-1"]', title: '向左缩进' },
    { Choice: '.ql-indent[value="+1"]', title: '向右缩进' },
    { Choice: '.ql-header .ql-picker-label', title: '标题大小' },
    { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: '标题一' },
    { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: '标题二' },
    { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: '标题三' },
    { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: '标题四' },
    { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: '标题五' },
    { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: '标题六' },
    { Choice: '.ql-header .ql-picker-item:last-child', title: '标准' },
    { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: '小号' },
    { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: '大号' },
    { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: '超大号' },
    { Choice: '.ql-size .ql-picker-item:nth-child(2)', title: '标准' },
    { Choice: '.ql-align .ql-picker-item:first-child', title: '居左对齐' },
    { Choice: '.ql-align .ql-picker-item[data-value="center"]', title: '居中对齐' },
    { Choice: '.ql-align .ql-picker-item[data-value="right"]', title: '居右对齐' },
    { Choice: '.ql-align .ql-picker-item[data-value="justify"]', title: '两端对齐' }
]

export default {
    name: 'Vue2CliQuillEditor',
    data () {
        const imgPrefix = this.imgUrlPrefixAdd
        return {
            editorOption: {
                modules: {
                    imageDrop: true,
                    imageResize: {
                        displayStyles: {
                            backgroundColor: 'black',
                            border: 'none',
                            color: 'white'
                        },
                        modules: ['Resize', 'DisplaySize', 'Toolbar']
                    },
                    ImageExtend: {
                        name: 'files',
                        size: 10,
                        action: '/api/oss/uploader1',
                        response: res => {
                            let url = res.data[0].objectPath
                            return imgPrefix + url.replace(/^http:\/\/[^/]+/, '')
                        },
                        headers: xhr => {
                            let token = 'UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA'
                            xhr.setRequestHeader('token', token)
                        },
                        sizeError: () => {
                            this.$message('大小不能超过10M')
                        },
                        error: () => {
                        },
                        success: () => {
                        },
                        // eslint-disable-next-line
            change: (xhr, formData) => {
                            formData.append('bucketName', 'mall')
                            formData.append('directory', 'material')
                            formData.append('isChangeObjectName', true)
                            formData.append('isTemplate', false)
                            formData.append('orgCode', 'SRBC')
                            formData.append('relationId', '990116')
                        },
                    },
                    toolbar: {
                        container: container,
                        handlers: {
                            image: function () {
                                QuillWatch.emit(this.quill.id)
                            },
                            video: function () {
                                const quill = this.quill
                                quill.getModule('toolbar').addHandler('video', () => {
                                    let uploader = document.createElement('input')
                                    uploader.setAttribute('type', 'file')
                                    document.body.appendChild(uploader)
                                    uploader.click()
                                    uploader.addEventListener('change', e => {
                                        const file = e.target.files[0]
                                        let form = new FormData()
                                        form.append('files', file)
                                        form.append('bucketName', 'mall')
                                        form.append('directory', 'device')
                                        form.append('isChangeObjectName', true)
                                        form.append('isTemplate', false)
                                        form.append('orgCode', 'SRBC')
                                        form.append('relationId', '990116')
                                        form.append('fileType', '2')
                                        uploadFile(form).then(res => {
                                            let url = res[0].objectPath
                                            document.body.removeChild(uploader)
                                            if (url) {
                                                let processedUrl = imgPrefix + url.replace(/^http:\/\/[^/]+/, '')
                                                quill.insertEmbed(quill.getSelection(true).index, 'video', processedUrl)
                                            }
                                        })
                                    })
                                })
                            }
                        },
                    }
                },
                theme: 'snow',
                placeholder: '请输入正文'
            },
        }
    },
    model: {
        prop: 'form',
        event: 'change'
    },
    props: ['form', 'disabled'],
    components: {
        quillEditor
    },
    created () {
        // const fonts = ['SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong', 'Arial', 'sans-serif']
        // let Font = Quill.import('formats/font')
        // Font.whitelist = fonts
        const BaseVideo = Quill.import('blots/block/embed')
        class Video extends BaseVideo {
            static create (value) {
                const node = super.create(value)
                node.setAttribute('controls', 'true')
                node.setAttribute('src', value)
                return node
            }

            static value (node) {
                return node.getAttribute('src')
            }
        }

        Video.blotName = 'video'
        Video.tagName = 'video'

        // Quill.register(Video)
        // Quill.register(Font, true)
        // Quill.register('modules/ImageExtend', ImageExtend)
        // Quill.register('modules/imageDrop', ImageDrop)
        // Quill.register('modules/imageResize', resizeImage)
    },
    mounted () {
        this.initTitle()
    },
    methods: {
        initTitle () {
            for (let item of titleConfig) {
                let tip = document.querySelector('.quill-editor ' + item.Choice)
                if (!tip) continue
                tip.setAttribute('title', item.title)
            }
        },
        //失去焦点事件
        onEditorBlur (quill) {
            this.$emit('blur', quill)
        },
        //获得焦点事件
        onEditorFocus (quill) {
            this.$emit('focus', quill)
            if(this.disabled === true) {
                quill.enable(false)
            }
        },
        onEditorReady (quill) {
            this.$emit('ready', quill)
        },
        //内容改变事件
        onEditorChange ({ html }) {
            this.$emit('change', html)
        },
    },
}
</script>

<style lang='scss' >
.editor {
  line-height: normal !important;
  height: 300px;
  margin-bottom: 42px;
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=SimSun]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=SimSun]::before {
  content: "宋体" !important;
  font-family: "SimSun";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=SimHei]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=SimHei]::before {
  content: "黑体" !important;
  font-family: "SimHei";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Microsoft-YaHei]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Microsoft-YaHei]::before {
  content: "微软雅黑" !important;
  font-family: "Microsoft YaHei";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=KaiTi]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=KaiTi]::before {
  content: "楷体" !important;
  font-family: "KaiTi";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=FangSong]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=FangSong]::before {
  content: "仿宋" !important;
  font-family: "FangSong";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Arial]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Arial]::before {
  content: "Arial" !important;
  font-family: "Arial";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=sans-serif]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=sans-serif]::before {
  content: "sans-serif" !important;
  font-family: "sans-serif";
}

.ql-font-SimSun {
  font-family: "SimSun";
}

.ql-font-SimHei {
  font-family: "SimHei";
}

.ql-font-Microsoft-YaHei {
  font-family: "Microsoft YaHei";
}

.ql-font-KaiTi {
  font-family: "KaiTi";
}

.ql-font-FangSong {
  font-family: "FangSong";
}

.ql-font-Arial {
  font-family: "Arial";
}

.ql-font-sans-serif {
  font-family: "sans-serif";
}
</style>