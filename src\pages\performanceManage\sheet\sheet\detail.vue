<template>
    <div class="base-page">
        <div class="e-form">
            <BillTop @cancel="handleClose"></BillTop>
            <div class="tabs warningTabs" style="padding-top: 70px;">
                <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                    <el-tab-pane label="对账单详情" name="sheetDetail" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="物资清单" name="materialList" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <div id="tabs-content">
                        <!-- 基本信息 -->
                        <div id="columnInfoCon" class="con">
                            <div class="tabs-title" id="sheetDetail">对账单详情</div>
                            <div class="e-form" style="padding: 0 10px 10px;">
                            </div>
                        </div>
                        <!--计划清单-->
                        <div id="floorInfoCon" class="con">
                            <div class="tabs-title" id="materialList">物资清单</div>
                            <div class="e-table"  style="background-color: #fff" >
                                <el-table
                                    border
                                    style="width: 100%"
                                    :data="tableData"
                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                    :row-style="{ fontSize: '14px', height: '48px' }"
                                >
                                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                                    <el-table-column label="操作" width="160">
                                        <template slot-scope="scope">
                                            <span class="pointer" style="color: rgba(33, 110, 198, 1); margin-left: 20px" @click="dismantleM(scope.row)">拆单</span>
                                            <span class="pointer" style="color: rgb(176,5,5); margin-left: 20px" @click="deleteM(scope.row)">删除</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="materialName" label="物资名称" width="200"/>
                                    <el-table-column prop="spec" label="规格型号" width=""/>
                                    <el-table-column prop="unit" label="计量单位" width=""/>
                                    <el-table-column prop="texture" label="材质" width=""/>
                                    <el-table-column prop="maxQuantity" label="单据数量" width=""/>
                                    <el-table-column prop="quantity" label="已选数量" width="160">
                                        <template v-slot="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.quantity"
                                                @change="getChangedRow(scope.row)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="freightPrice" label="到货网价" width="140">
                                        <template v-slot="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.freightPrice"
                                                @change="getChangedRow(scope.row)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="fixationPrice" label="固定费用" width="">
                                        <template v-slot="scope">
                                            <el-input
                                                type="number"
                                                v-model="scope.row.fixationPrice"
                                                @change="getChangedRow(scope.row)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="price" label="含税单价" width=""/>
                                    <el-table-column prop="noRatePrice" label="不含税单价" width=""/>
                                    <el-table-column prop="acceptanceAmount" label="总金额" width=""/>
                                    <el-table-column prop="remarks" label="备注" width="">
                                        <template v-slot="scope">
                                            <el-input
                                                v-model="scope.row.remarks">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </el-tabs>
            </div>
            <div class="buttons">
<!--                <el-button type="primary" @click="updateSheet">确定</el-button>-->
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
    </div>
</template>
<script>
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
import { getUuid, throttle } from '@/utils/common'

export default {
    components: {
    },
    data () {
        return {
            maxNum: 999999999,
            tableData: [
                {
                    materialName: '扳手',
                    spec: 'SLDOD-001',
                    unit: '把',
                    texture: '304不锈钢',
                    quantity: 12,
                    freightPrice: 20.11,
                    fixationPrice: 0,
                    price: 20.11,
                    acceptanceAmount: 241.32,
                    maxQuantity: 0,
                    uuid: null,
                },
                {
                    materialName: '螺丝',
                    spec: 'LSDFA-001',
                    unit: '个',
                    texture: '不锈钢',
                    quantity: 44,
                    freightPrice: 2.12,
                    fixationPrice: 0,
                    price: 2.12,
                    acceptanceAmount: 93.28,
                    maxQuantity: 0,
                    uuid: null,
                }
            ],
            winEvent: {},
            queryVisible: false,
            //选中数据
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
        }
    },
    created () {
        for (let i = 0; i < this.tableData.length; i++) {
            this.tableData[i].maxQuantity = this.tableData[i].quantity
            this.tableData[i].uuid = getUuid()
        }
    },
    computed: {
    },
    watch: {
    },
    methods: {
        // 删除单
        deleteM (row) {
            this.tableData = this.tableData.filter(t => {
                if(t.uuid != row.uuid) {
                    return true
                }else{
                    return  false
                }
            })
        },
        // 拆单
        dismantleM (row) {
            // 插入到当前点击的下一个节点
            let insertIndex = this.tableData.length
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if(t.uuid == row.uuid) {
                    insertIndex = i + 1
                }
            }
            let newRow = {
                ...row,
                acceptanceAmount: this.fixed2(0),
                quantity: 0,
                uuid: getUuid()
            }
            this.tableData.splice(insertIndex, 0, newRow)
        },

        // 表单变化
        getChangedRow (row) {
            // 处理固定费用
            this.disposeFixationPriceM(row)
            // 处理到店网价
            this.disposeFreightPriceM(row)
            // 处理数量
            this.disposeQuantityM(row)
            // 最终计算
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                t.price = this.fixed2(Number(t.freightPrice) + Number(t.fixationPrice))
                t.acceptanceAmount = t.quantity * t.price
            }
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 处理固定费用
        disposeFixationPriceM (row) {
            if(row.fixationPrice <= 0 || row.freightPrice >= this.maxNum) {
                return row.fixationPrice = this.fixed2(0)
            }
        },
        // 处理到货网价
        disposeFreightPriceM (row) {
            if(row.freightPrice <= 0 || row.freightPrice >= this.maxNum) {
                return row.freightPrice = this.fixed2(0)
            }
        },
        // 处理数量
        disposeQuantityM (row) {
            if(row.quantity <= 0) {
                return row.quantity = this.fixed4(0)
            }
            // 计算最大值
            let maxNum = this.fixed4(row.maxQuantity)
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if(t.materialName === row.materialName && t.spec === row.spec && t.unit == row.unit && t.uuid !== row.uuid) {
                    if(maxNum <= 0) {
                        maxNum = 0
                        continue
                    }
                    maxNum = maxNum - t.quantity
                }
            }
            if(row.quantity >= maxNum) {
                row.quantity = this.fixed4(maxNum)
            }else {
                row.quantity = this.fixed4(row.quantity)
            }
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        async mounted () {
            this.getLastConHeight()
            // 保存所有tabName
            const arr = ['baseInfo', 'receive']
            this.tabArr = arr
            let $idsTop = []
            this.winEvent.onScroll = () => {
                // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
                if (this.clickTabFlag) {
                    return
                }
                if (!$idsTop[$idsTop.length - 1]) {
                    $idsTop = arr.map(item => {
                        const $item = document.getElementById(item)
                        let itemTop = null
                        if ($item) {
                            itemTop = $item.offsetTop
                        }
                        return itemTop
                    })
                }
                const scrollTop = $('#tabs-content')[0].scrollTop
                // 倒序查找
                let curLocal = 0
                for (let i = $idsTop.length - 1; i >= 0; i--) {
                    let item = $idsTop[i]
                    if (scrollTop + 1 >= item) {
                        curLocal = i
                        break
                    }
                }
                // 设置对应tabName
                this.tabsName = arr[curLocal]
            }
            $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            const onResize = () => {
                this.screenWidth = document.documentElement.clientWidth - this.topHeight
                this.screenHeight = document.documentElement.clientHeight - this.topHeight
                $idsTop = arr.map(item => {
                    return document.getElementById(item).offsetTop
                })
            }
            this.winEvent.onResize = throttle(onResize, 500)
            window.addEventListener('resize', this.winEvent.onResize)
            if (this.$route.query.name) {
                setTimeout(() => {
                    this.onChangeTab({
                        name: this.$route.query.name
                    })
                    this.tabsName = this.$route.query.name
                }, 200)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>