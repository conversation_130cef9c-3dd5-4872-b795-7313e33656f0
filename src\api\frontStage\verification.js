import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

// 根据当前的登陆的用户id获取企业信息
const getEnterpriseAuthInfo = params => {
    params.mallType = 0
    return httpGet({
        url: '/materialMall/userCenter/user/getEnterpriseAuthInfo',
        params,
    })
}
// 修改企业
const updateEnterprise = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/updateEnterprise',
        params,
    })
}
// 成为企业
const becomeEnterprise = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/userCenter/user/becomeEnterprise',
        params,
    })
}
export {
    getEnterpriseAuthInfo,
    updateEnterprise,
    becomeEnterprise,
}