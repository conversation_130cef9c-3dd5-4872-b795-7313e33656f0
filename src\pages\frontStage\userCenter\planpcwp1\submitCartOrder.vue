<template>
    <div>
    <div class="root" v-loading="submitCartOrderLoading" v-if="!showPage">
        <div class="order-box center p20">
            <el-page-header class="mb10 ml20" @back="goBack" content="">
            </el-page-header>
            <div class="list-title df mt10">购买清单</div>
            <div class="order-list p20">
                <div class="name-bar center dfa">
                    <div>商品清单</div><div>单价</div><div>数量</div><div>小计（元）</div>
                </div>
                <div v-for="(item, i) in productList" :key="i">
                    <div class="shop-name dfa">
                        <span>{{item.shopName}}</span>
                    </div>
                    <div class="list-item df" v-for="(item2, i2) in item.productInfo" :key="i2">
                        <div class="info df">
                            <img @click="$router.push({ path: '/mFront/productDetail', query: { productId: item2.productId } })" :src="item2.productMinImg ? imgUrlPrefixAdd + item2.productMinImg : require('@/assets/images/img/queshen5.png')" alt="">
                            <div class="detail">
                                <h4 @click="$router.push({ path: '/mFront/productDetail', query: { productId: item2.productId } })">{{item2.productName}}</h4>
                                <div>规格：{{item2.skuName}}</div>
                                <div>剩余库存：{{item2.stock}}</div>
                                <div>计量单位：{{item2.unit}}</div>
                            </div>
                        </div>
                        <div class="price">
                            <div>￥{{item2.sellPrice}}</div>
                            <div>单位：{{item2.unit}}</div>
                        </div>
                        <div class="num dfa">
                            <div></div>
                            {{item2.cartNum}}
                            <div></div>
                        </div>
                        <div class="total">￥{{item2.numTotalPrice}}</div>
                    </div>
                </div>
                <div class="select-box mt30">
                    <div>
                        <span>结算方式</span>
                        <el-select v-model="payMethod" value-key="" placeholder="请选择支付方式">
                            <el-option v-for="item in payMethods" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <span>其他要求</span><input v-model="remarks" type="text" placeholder="填写要求"/>
                    </div>
                </div>
                <div class="sum">商品合计：<span style="color: rgba(212, 48, 48, 1);">￥{{totalPrice}}</span></div>
            </div>
        </div>
        <div class="addr-info center mb20">
            <div class="list-title">
                <span>收货信息</span>
                <span style="margin-left: 800px" @click="checkedAddressM">切换收货地址</span>
            </div>
            <div class="addr-content">
                <p class="mb20">收货地址：{{receiver.addr}}（{{receiver.name}}收） </p>
                <p>收货人：{{receiver.name}}（{{receiver.tel}}）</p>
            </div>
        </div>
        <div class="submit df">
            <div class="submitBox df">
                <div>总计：<span>￥{{totalPrice}}</span></div>
                <button @click="submitOrder">提交订单</button>
            </div>
        </div>
        <el-dialog class="front" title="" :visible.sync="addrDialogVisible">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>{{ dialogTitle }}</div>
                    </div>
                    <div class="dialog-close" @click="addrDialogVisible = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <el-table :data="addrList" @row-click="handleCurrentInventoryClick">
                <el-table-column label="收货地址" label-width="560" prop="addr"></el-table-column>
                <el-table-column label="联系人" label-width="152" prop="name"></el-table-column>
                <el-table-column label="联系电话" label-width="110" prop="tel"></el-table-column>
                <el-table-column label="操作" label-width="">
                    <template v-slot="scope">
                        <span @click="handleEditAddr(scope.row)" class="edit-btn">编辑</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="add pointer" @click="createAddress">+ 新增</div>
        </el-dialog>
        <el-dialog class="front" :visible.sync="addDetailDialog" top="8vh">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>{{ dialogTitle }}</div>
                    </div>
                    <div class="dialog-close" @click="addDetailDialog = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <!-- 弹框内容 -->
            <div class="dialog-body center">
                <el-form :model="userAddressForm" ref="addAddressRef" :rules="userAddressFormRules" label-width="80px" :inline="false" label-position="top">
                    <el-form-item label="收货人：" prop="receiverName">
                        <el-input v-model="userAddressForm.receiverName" placeholder="请输入收货人姓名"></el-input>
                    </el-form-item>
                    <el-form-item class="tel" label="手机号码：" prop="receiverMobile">
                        <span>+86</span><el-input v-model="userAddressForm.receiverMobile" placeholder="请输入手机号码"></el-input>
                    </el-form-item>
                    <el-form-item label="选择地址：" prop="detailAddress">
                        <el-cascader
                            size="large"
                            :options="addressData"
                            v-model="selectAddressOptions"
                            @change="handleAddressChange">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item class="address" label="详细地址：" prop="detailAddress">
                        <el-input v-model="userAddressForm.detailAddress" placeholder="请输入详细收货地址"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer">
                <button class="butSub" @click="createAddressM">保存</button>
            </span>
            </div>
        </el-dialog>
    </div>
        <div class="tabBox" v-if="showPage">
            <closeReg :errorOrderVO="orderVOS" :shopDialog="shopDialog"></closeReg>
        </div>
    </div>
</template>
<script>
import closeReg  from './closeReg'
// eslint-disable-next-line
import { regionData, CodeToText, TextToCode } from 'element-china-area-data'
import { create, getDefaultAddress, getList } from '@/api/frontStage/shippingAddr'
import { mapState } from 'vuex'
import { submitPlanAndOrder } from '@/api/plan/plan'
export default {
    components: { closeReg },
    data () {
        return {
            orderVOS: [],
            showOrderVOSDialog: false,
            shopDialog: false,
            showPage: false,
            // 地址
            addressData: regionData, // 地址数据
            selectAddressOptions: [], // 地址选择
            addDetailDialog: false, // 地址编辑新增
            userAddressFormRules: {
                receiverName: [
                    { required: true, message: '请输入收件人', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                receiverMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
            },
            userAddressForm: { // 新增编辑地址表单
                detailAddress: null,
            },
            submitCartOrderLoading: false,
            cartIds: [],
            shopName: null, // 店铺名称
            dialogTitle: '选择收货地址',
            rowData: {
                productId: null,
                buyNum: null,
            },
            orderList: [],
            productList: [],
            payMethods: [
                { label: '路桥结算', value: 2 }
            ],
            payMethod: 2,
            remarks: null, // 订单备注
            totalPrice: null, // 总金额
            receiver: {
                name: '蛮蛮',
                tel: '18548759621',
                addr: '四川省成都市青羊区沧浪胡同918号',
            },
            addrDialogVisible: false,
            addrList: [
                // { checked: true, addr: '江西省南昌市进贤县黄山街345号', name: '蛮蛮', tel: '18548759621', },
            ]
        }
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        // 返回上一页
        goBack () {
            window.history.back()
        },
        // 获取默认地址
        getDefaultAddressM () {
            this.submitCartOrderLoading = true
            getDefaultAddress().then(res => {
                if(res != null) {
                    this.receiver.name = res.receiverName
                    this.receiver.tel = res.receiverMobile
                    this.receiver.addr = res.detailAddress
                }
                this.submitCartOrderLoading = false
            }).catch(() => {
                this.submitCartOrderLoading = false
            })
        },
        // 地址表格点击
        handleCurrentInventoryClick (row) {
            this.receiver = row
            this.addrDialogVisible = false
        },
        // 切换地址
        checkedAddressM () {
            this.getAddRess()
            this.addrDialogVisible = true
        },
        // 获取地址
        getAddRess () {
            // 获取收货地址
            this.submitCartOrderLoading = true
            getList({ page: 1, limit: 30 }).then(res => {
                this.submitCartOrderLoading = false
                if(!res.list[0]) return
                let address = []
                // 显示默认地址
                res.list.forEach(item => {
                    let obj = {
                        addressId: item.addressId,
                        checked: false,
                        addr: item.detailAddress,
                        name: item.receiverName,
                        tel: item.receiverMobile,
                        province: item.province,
                        city: item.city,
                        county: item.county,
                    }
                    address.push(obj)
                })
                this.addrList = address
            }).catch(() => {
                this.submitCartOrderLoading = false
            })
        },
        // 创建编辑地址统一接口
        createAddressM () {
            this.$refs.addAddressRef.validate(valid => {
                if (valid) {
                    create(this.userAddressForm).then(res => {
                        if(res.code == 200) {
                            this.$message({
                                message: res.message,
                                type: 'success'
                            })
                            this.getAddRess()
                            this.addDetailDialog = false
                        }
                    })
                }
            })
        },
        // 编辑地址
        handleEditAddr (row) {
            let obj = {
                addressId: row.addressId,
                detailAddress: row.addr,
                receiverName: row.name,
                receiverMobile: row.tel,
            }
            this.userAddressForm = obj
            //地址选择器回显
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
            this.addDetailDialog = true
        },
        // 地址选择
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.userAddressForm.province = province
            this.userAddressForm.city = city
            this.userAddressForm.county = county
            this.userAddressForm.detailAddress = province + city + county
        },
        // 创建
        createAddress () {
            this.userAddressForm = {
                detailAddress: null,
            },
            this.selectAddressOptions = []
            this.addDetailDialog = true
        },
        // 提交订单
        submitOrder () {
            let isSubmitFlag = this.userInfo.isSubmitOrder
            let isInterior = this.userInfo.isInterior
            if(isInterior == 1) {
                if(isSubmitFlag == null || isSubmitFlag == 0) {
                    this.$message.error('没有下单权限，请联系管理员！')
                    return
                }
            }
            if(this.receiver.tel == null) {
                this.$message({
                    message: '请选择收货地址！',
                    type: 'error'
                })
                return
            }
            let submitPro = this.$route.params.row.productSubmitList
            submitPro.forEach(t => {
                t.receiverName = this.receiver.name
                t.receiverMobile = this.receiver.tel
                t.receiverAddress = this.receiver.addr
                t.orderRemark = this.remarks
                t.payWay = this.payMethod
            })
            this.submitCartOrderLoading = true
            submitPlanAndOrder(submitPro).then(res => {
                this.submitCartOrderLoading = false
                if(res.code != null && res.code == 200) {
                    this.showPage = true
                    return
                }
                if(res != null && res.length != 0) {
                    this.shopDialog = true
                    this.orderVOS = res
                    this.showPage = true
                }
            }).catch(() => {
                this.submitCartOrderLoading = false
            })
        },
    },
    created () {
        this.productList = this.$route.params.row.productList
        this.totalPrice = this.$route.params.row.totalPrice
        this.getDefaultAddressM()
    },

}
</script>
<style scoped lang="scss">
$font: 'Source Han Sans CN';
.root {
    height: 100%;
    padding-top: 20px;
    background-color: #f5f5f5;

    .order-box {
        width: 1326px;
        background-color: #fff;
    }
}
.list-title {
    height: 50px;
    padding: 15px 20px;
    font-size: 18px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);
    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
.order-list {
    .name-bar {
        width: 1286px;
        height: 52px;
        padding-left: 40px;
        margin-bottom: 20px;
        font-size: 14px;
        border: 1px solid rgba(230, 230, 230, 1);
        color: rgba(51, 51, 51, 1);
        background-color: #fafafa;
        & div:first-child {width: 773px;}
        & div:nth-child(2) {width: 174px;}
        & div:nth-child(3) {width: 148px;}
    }
    .shop-name {
        margin-bottom: 13px;
        span {margin-right: 10px;font-size: 14px;}
        img {width: 22px;height: 22px;}
    }
    .list-item {
        width: 100%;
        height: 144px;
        margin-bottom: 10px;
        padding: 22px 24px;
        border: 1px solid rgba(230, 230, 230, 1);
        .info {
            width: 785px;
            img {
                width: 100px;
                height: 100px;
                margin-right: 12px;
            }
            h4, div{
                font-size: 14px;
                font-weight: 400;
            }
            h4 {margin-bottom: 11px; color: rgba(0, 0, 0, 1);}
            div {margin-bottom: 4px; color: rgba(102, 102, 102, 1);}
        }
        .price {
            width: 138px;
            & div:first-child {margin-bottom: 4px;}
            & div:last-child {}
        }
        .num {
            width: 101px;
            height: 26px;
            margin-right: 84px;
            text-align: center;
            line-height: 26px;
            div {
                width: 100%;
                height: 26px;
                cursor: pointer;
                user-select: none;
            }
        }
        .total {
            font-size: 14px;
            font-weight: 700;
            color: rgba(212, 48, 48, 1);
        }
    }
    .select-box {
        &>div {margin-bottom: 30px;}
        span {margin-right: 20px;}
        /deep/ .el-select {
            width: 300px;
            height: 30px;
            .el-input__inner {
                height: 30px;
                padding: 0 10px;
                color: rgba(51, 51, 51, 1);
            }
        }
        input {
            width: 600px;
            height: 30px;
            padding: 0 10px;
            border: 1px solid rgba(217, 217, 217, 1);
            &::-webkit-input-placeholder {
                color: rgba(51, 51, 51, 1);
            }
        }
    }
    .sum {text-align: right;}
}
.addr-info {
    width: 1326px;
    background-color: #fff;
    .list-title {
        padding-left: 33px;
        position: relative;
        & span:last-child {
            color: rgba(33, 110, 198, 1);
            cursor: pointer;
        }
        &::before {
            position: absolute;
            left: 20px;
        }
    }
    .addr-content {
        padding: 30px 20px;
        p {
            font-size: 14px;
            color: rgba(51, 51, 51, 1);
        }
    }
}
.submit {
    height: 60px;
    background-color: #fff;
    // justify-content: flex-end;
    position: relative;
    .submitBox {
        position: absolute;
        right: 0;
        div {
            color: rgba(51, 51, 51, 1);
            line-height: 60px;
            span {
                font-size: 18px;
                color: rgba(212, 48, 48, 1);
            }
        }
        button {
            width: 146px;
            margin-left: 30px;
            font-size: 24px;
            font-weight: 700;
            color: #fff;
            background-color: rgba(212, 48, 48, 1);
        }
    }
}
/deep/ .el-dialog {
    width: 1000px;
    //width: 1120px;
    min-height: 326px;
    .dialog-body {
        width: 500px;
        padding-top: 30px;

        .el-form-item {
            margin-bottom: 14px;
            &:last-of-type {margin-bottom: 20px;}
        }

        .el-form-item__label {
            // height: 14px;
            // margin-bottom: 20px;
            padding-bottom: 0;
            color: #999;
        }

        .el-input__inner {
            width: 300px;
            height: 35px;
            border: 1px solid rgba(217, 217, 217, 1);
            border-radius: 0;
        }
        .address .el-input__inner {width: 500px;}
        .tel {
            .el-form-item__content {
                display: flex;
                span {
                    margin-right: 10px;
                    color: #333;
                }
            }
            .el-input, .el-input__inner {width: 266px;}
        }
    }
    .butSub {
        width: 80px;
        height: 40px;
        font-size: 16px;
        color: #fff;
        background-color: #216EC6;
        margin-left: 100px;
    }
    .el-table {
        margin-top: 20px;
        border: 1px solid rgba(230, 230, 230, 1);
        // border-bottom: 0;
        font-size: 14px;
        .edit-btn {
            color: rgba(34, 111, 199, 1);
            cursor: pointer;
        }
    }
    .add {
        width: 80px;
        height: 30px;
        margin: 18px 0 40px 0;
        line-height: 30px;
        text-align: center;
        color: rgba(33, 110, 198, 1);
        border: 1px solid rgba(33, 110, 198, 1);
    }
    .el-table__header {
        .cell{
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
        }
        .el-checkbox {display: none;}
    }
    .el-dialog__footer {
        border: 0;
        text-align: center;
        button {
            width: 80px;
            height: 40px;
        }
        & button:first-child {
            margin-right: 20px;
            color: rgba(128, 128, 128, 1);
            background-color: rgba(230, 230, 230, 1);
        }
        & button:last-child {
            color: #fff;
            background-color: rgba(33, 110, 198, 1);
        }
    }
}
.addr-list {
    &>div {
        height: 44px;
        border: 1px solid rgba(230, 230, 230, 1);
    }
    .list-top {
        background-color: rgba(250, 250, 250, 1);
    }
}
.tabBox {
    height: 100%;
    min-height: 622px;
    font-family: $font;
    position: relative;
}
.returnButt {
    margin-left: 40%;
    width: 120px;
    height: 40px;
    font-size: 22px;
    color: #fff;
    background-color: #216EC6;
}
</style>