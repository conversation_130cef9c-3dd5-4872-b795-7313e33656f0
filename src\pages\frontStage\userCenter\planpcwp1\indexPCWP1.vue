
<template>
    <main class="userCenter" v-loading="planListLoading">
        <div class="list-title dfa mb20">
            零星采购计划列表
            <div class="search df" style="margin-left: 50%">
                <div class="box dfa">
                    <img src="@/assets/images/ico_search.png" alt="" />
                    <input v-model="keyword" type="text" placeholder="计划编号" />
                </div>
                <button @click="getPlanListM">搜索</button>
            </div>
        </div>
        <el-table border ref="msgTable" :data="list" style="min-height: 472px" :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
            :row-style="{ fontSize: '14px', height: '48px' }">
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column prop="BillNo" label="计划编号" width="300"> </el-table-column>
            <el-table-column prop="Year" label="计划年度" width=""> </el-table-column>
            <el-table-column prop="Month" label="计划月份" width=""> </el-table-column>
            <el-table-column prop="BillDate" label="计划日期" width="">
                <template slot-scope="scope">
                    {{ scope.row.BillDate | dateStr }}
                </template>
            </el-table-column>
            <el-table-column prop="PlanAmount" label="计划金额" width=""> </el-table-column>
            <el-table-column prop="" label="操作" width="">
                <template slot-scope="scope">
                    <div class="pointer" style="color: rgba(33, 110, 198, 1)" @click="handleViewDetail(scope.row.BillId)">查看详情</div>
                </template>
            </el-table-column>
        </el-table>
        <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"
            :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange">
        </pagination>
    </main>
</template>

<script>
import pagination from '@/pages/frontStage/components/pagination.vue'

import { mapState } from 'vuex'
import { getPCWP1Interface } from '@/api/plan/plan'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return dateStr.split('T')[0]
        },
    },
    computed: {
        ...mapState(['userInfo']),
    },
    components: { pagination },
    name: 'index',
    data () {
        return {
            planListLoading: false,
            keyword: null,
            pagination: {
                currPage: 1,
                destination: null,
                pageSize: 10,
                totalNum: 10,
                totalPage: 1,
            },
            list: [],
        }
    },
    created () {
        this.getPlanListM()
    },
    mounted () { },
    methods: {
        currentChange (index) {
            this.pagination.currPage = index
            this.getPlanListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getPlanListM()
        },
        getPlanListM () {
            // let params = {
            //     jsonrpc: '2.0',
            //     method: 'Material.SporadicPlan.QueryList',
            //     params: {
            //         filter: {
            //             MatterName: '' /*材料名称*/,
            //             Spec: '' /*材料规格*/,
            //             BillNo: '' /*编号*/,
            //             IsSign: false /*是否合同*/,
            //             PurchaseType: 1 /*采购方式*/,
            //             // 'ConditionString': 'IsOut = 1'  /*查询条件(Where 之后的SQL)*/,
            //             IsOut: true /*查询条件(Where 之后的SQL)*/,
            //             KeyWord: '' /*关键字*/,
            //             OrgId: this.userInfo.orgId /*机构Id*/,
            //             // 9.到达财务共享，2,完全已审核
            //             State: [2, 9] /*单据状态*/,
            //             PageIndex: this.pagination.currPage /*分页返回结果，指定页号，以1为起始数字，表示第1页*/,
            //             PageSize: this.pagination.pageSize /*分页返回结果，每页记录数*/,
            //             SortFields: '' /*排序方式，格式如： InDate DESC，可不填，保持默认排序即可*/,
            //         } /*查询参数类 [SporadicPlanQueryFilter]*/,
            //     },
            //     id: 1,
            //     tags: {
            //         userid: this.userInfo.farUserId,
            //         username: this.userInfo.originalUserName,
            //         orgid: this.userInfo.orgId,
            //         orgname: this.userInfo.orgName,
            //         companycode: '1000',
            //         auth_client_id: 'test',
            //         auth_token: 'test',
            //         platformid: '1',
            //     },
            // }

            let params = {
                beginPlanDate:,
                endPlanDate:,
                keyword:,
                limit:,
                month:,
                orgId:,
                page:,
                states:,
                year:,
            }
            if (this.keyword != null) {
                params.params.filter.KeyWord = this.keyword
            }
            this.planListLoading = true
            getPCWP1Interface(params)
                .then(res => {
                    this.list = res.result.data
                    this.pagination.totalPage =
                        res.result.recordsTotal % res.result.PageSize == 0
                            ? res.result.recordsTotal / res.result.PageSize
                            : Math.floor(res.result.recordsTotal / res.result.PageSize) + 1
                    this.pagination.totalNum = res.result.recordsTotal
                    this.pagination.currPage = res.result.PageIndex
                    this.pagination.pageSize = res.result.PageSize
                    this.planListLoading = false
                })
                .catch(() => {
                    this.planListLoading = false
                })
        },
        // 跳转详情
        handleViewDetail (billId) {
            this.$router.push({ path: '/user/planDetail', query: { billId: billId } })
        },
    },
}
</script>

<style scoped lang="scss">
$border: 1px solid rgba(229, 229, 229, 1);

main {
    padding: 0 20px;
    border: $border;
}

.list-title {
    padding: 0;

    .pointer {
        font-size: 14px;
        color: rgba(33, 110, 198, 1);
        position: absolute;
        right: 20px;
    }
}

.search {
    .box {
        width: 268px;
        height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);
        border-right: 0;

        img {
            width: 16px;
            height: 16px;
            margin: 0 4px 0 10px;
        }

        input {
            width: 230px;
        }
    }

    button {
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
    }
}

.list {
    .item {
        height: 52px;
        padding: 0 20px;
        font-size: 14px;
        border: $border;
        color: rgba(102, 102, 102, 1);
        position: relative;

        &>div:not(.pointer) {
            margin-right: 60px;
        }

        .pointer {
            color: rgba(33, 110, 198, 1);
            position: absolute;
            right: 20px;
        }

        span {
            color: rgba(51, 51, 51, 1);
        }
    }
}
</style>
