<template>
  <main>
    <div class="list-title dfa mb20">发票详情</div>
    <div class="box">
      <section>
        <div class="title mb20">订单信息</div>
        <div class="con df">
          <div class="item">
            <span>订单状态：</span>
            <span v-show='orderForm.state === 3'>已确认</span>
            <span v-show='orderForm.state === 4'>待签订合同</span>
            <span v-show='orderForm.state === 5'>已签合同</span>
            <span v-show='orderForm.state === 6'>已完成</span>
            <span v-show='orderForm.state === 7'>已关闭</span>
          </div>
          <div class="item">
            <span>订单编号：</span>
            <span>{{ orderForm.orderSn }}</span>
          </div>
          <div class="item">
            <span>创建时间：</span>
            <span>{{ orderForm.gmtCreate }}</span>
          </div>
          <div class="item">
            <span>发票类型：</span>
            <span>{{ invoiceForm.invoiceType===0? '增值税发票':'普通发票' }}</span>
          </div>
        </div>
      </section>
      <section>
        <div class="title mb20">发票信息</div>
        <div class="con df">
          <div class="item">
            <span>发票状态：</span>
            <span :class="invoiceForm.invoiceState === 1 ? 'green' : ''">{{
                invoiceForm.invoiceState === 1 ? '已申请' : '已开票'
              }}</span>
          </div>
          <div class="item">
            <span>发票内容：</span>
            <span>{{ invoiceForm.remarks }}</span>
          </div>
          <div class="item">
            <span>抬头类型：</span>
            <span v-show="invoiceForm.riseType==1">单位</span>
            <span v-show="invoiceForm.riseType==2">个人</span>
          </div>
        </div>
      </section>
      <section>
        <div class="con df">
          <div class="item">
            <span>发票代码：</span>
            <span>{{ invoiceForm.invoiceNo }}</span>
          </div>
          <div class="item">
            <span>单位税号：</span>
            <span>{{ invoiceForm.dutyParagraph }}</span>
          </div>
          <!--                    <div class="item">-->
          <!--                        <span>下载电子增值税普通发票：</span>-->
          <!--                        <el-icon class="el-icon el-icon-download"></el-icon>-->
          <!--                        <span class="pointer" @click="handleDownload">查看发票</span>-->
          <!--                    </div>-->
        </div>
      </section>
      <!--            <button @click="handleSendMail">发送邮箱</button>-->
      <button @click="$router.go(-1)">返回</button>
    </div>
  </main>
</template>

<script>
import  { getOrderById } from '@/api/frontStage/order'
export default {
    data () {
        return {
            orderForm: {},
            invoiceForm: {}
        }
    },
    created () {
        this.invoiceForm = this.$route.params.row
        this.getOrderData(this.invoiceForm.orderId)
    },
    mounted () {
    },
    methods: {
        // 获取订单数据
        getOrderData (orderId) {
            getOrderById({ id: orderId }).then(res=>{
                this.orderForm = res
            })
        },
        // 下载发票
        handleDownload () {
        },
        // 发送邮箱
        handleSendMail () {
        },
    },
}
</script>

<style lang="scss" scoped>
main > div {
    border: 1px solid rgba(229, 229, 229, 1);
}

.list-title {
    height: 50px;
    padding: 15px 19px 15px 21px;
    font-size: 20px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);
    position: relative;

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}

.box {
    height: 785px;
    padding: 0 20px;

    section {
        padding: 30px 0;

        &:not(:last-of-type) {
            border-bottom: 1px dashed rgba(204, 204, 204, 1);
        }

        .title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }

        .con {
            flex-wrap: wrap;
        }

        .item {
            width: 350px;

            span:first-of-type {
                color: #999;
            }

            span:last-of-type {
                color: #333;
            }

            span.green {
                color: #71B247;
            }

            span.pointer {
                color: #216EC6;
            }

            .el-icon {
                margin-right: 5px;
                color: #999;
            }
        }

        .item:not(:last-child) {
            margin-bottom: 15px;
        }
    }

    button {
        width: 100px;
        height: 40px;
        margin: 70px auto 0;
        border-radius: 0;
        color: #fff;
        background-color: #216EC6;
        display: block;
    }
}
</style>