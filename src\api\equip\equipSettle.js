import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

//结算管理 - 装备结算模块 接口
const request = {

    //===============================================================================================采购结算接口

    //高级分页查询添加采购结算
    purchaseSettleList (params) {
        return httpPost({
            url: '/facilitysettle/buy/advanced/query',
            params
        })
    },
    //添加采购结算
    purchaseSettleAdd (params) {
        return httpPost({
            url: '/facilitysettle/buy/add',
            params
        })
    },
    //获取采购结算基础信息
    purchaseSettleBaseInfo (params) {
        return httpGet({
            url: '/facilitysettle/buy/get',
            params
        })
    },
    //获取采购结算明细
    purchaseSettlePlanInfo (params) {
        return httpGet({
            url: '/facilitysettle/del/buy/list',
            params
        })
    },
    //获取采购运费明细
    purchasefreightPlanInfo (params) {
        return httpGet({
            url: '/facilitysettle/freight/buy/list',
            params
        })
    },
    //修改采购结算
    updatePurchaseSettle (params) {
        return httpPost({
            url: '/facilitysettle/buy/update',
            params
        })
    },
    //提交采购结算
    purchaseSettleCommit (params) {
        return httpPostForm({
            url: '/facilitysettle/buy/commit',
            params
        })
    },
    //删除采购结算
    purchaseSettleDelete (params) {
        return httpPostForm({
            url: '/facilitysettle/buy/delete',
            params
        })
    },
    //保证金
    settleBond (params) {
        return httpGet({
            url: '/facilitysettle/bond/buy/list',
            params
        })
    },

    //===============================================================================================租赁结算接口

    //高级分页查询添加租赁结算
    leaseSettleList (params) {
        return httpPost({
            url: '/facilitysettle/lease/advanced/query',
            params
        })
    },
    //添加租赁结算
    leaseSettleAdd (params) {
        return httpPost({
            url: '/facilitysettle/lease/add',
            params
        })
    },
    //获取租赁结算基础信息
    leaseSettleBaseInfo (params) {
        return httpGet({
            url: '/facilitysettle/lease/get',
            params
        })
    },
    //获取租赁结算明细
    leaseSettlePlanInfo (params) {
        return httpGet({
            url: '/facilitysettle/del/lease/list',
            params
        })
    },
    //获取租赁运费明细
    leasefreightPlanInfo (params) {
        return httpGet({
            url: '/facilitysettle/freight/lease/list',
            params
        })
    },
    //修改租赁结算
    updateLeaseSettle (params) {
        return httpPost({
            url: '/facilitysettle/lease/update',
            params
        })
    },
    //提交租赁结算
    leaseSettleCommit (params) {
        return httpPostForm({
            url: '/facilitysettle/lease/commit',
            params
        })
    },
    //删除租赁结算
    leaseSettleDelete (params) {
        return httpPostForm({
            url: '/facilitysettle/lease/delete',
            params
        })
    },

    //===============================================================================================维修结算接口

    //高级分页查询添加维修结算
    maintenanceSettleList (params) {
        return httpPost({
            url: '/facilitysettle/repair/advanced/query',
            params
        })
    },
    //添加维修结算
    maintenanceSettleAdd (params) {
        return httpPost({
            url: '/facilitysettle/repair/add',
            params
        })
    },
    //获取维修结算基础信息
    maintenanceSettleBaseInfo (params) {
        return httpGet({
            url: '/facilitysettle/repair/get',
            params
        })
    },
    //获取维修结算明细
    maintenanceSettlePlanInfo (params) {
        return httpGet({
            url: '/facilitysettle/del/repair/list',
            params
        })
    },
    //修改维修结算
    updateMaintenanceSettle (params) {
        return httpPost({
            url: '/facilitysettle/repair/update',
            params
        })
    },
    //提交维修结算
    maintenanceSettleCommit (params) {
        return httpPostForm({
            url: '/facilitysettle/repair/commit',
            params
        })
    },
    //删除维修结算
    maintenanceSettleDelete (params) {
        return httpPostForm({
            url: '/facilitysettle/repair/delete',
            params
        })
    },

    //===============================================================================================运输合同结算接口

    //高级分页查询添加运输合同结算
    contractSettleList (params) {
        return httpPost({
            url: '/facilitysettle/transportContract/advancedQuery',
            params
        })
    },
    //添加运输合同结算
    contractSettleAdd (params) {
        return httpPost({
            url: '/facilitysettle/transportContract/insert',
            params
        })
    },
    //获取运输合同结算基础信息
    contractSettleBaseInfo (params) {
        return httpGet({
            url: '/facilitysettle/transportContract/get',
            params
        })
    },
    //获取运输合同结算明细
    contractSettlePlanInfo (params) {
        return httpGet({
            url: '/facilitysettle/del/transportContract/list',
            params
        })
    },
    //修改运输合同结算
    updateContractSettle (params) {
        return httpPost({
            url: '/facilitysettle/transportContract/update',
            params
        })
    },
    //提交运输合同结算
    contractSettleCommit (params) {
        return httpPostForm({
            url: '/facilitysettle/transportContract/commit',
            params
        })
    },
    //删除运输合同结算
    contractSettleDelete (params) {
        return httpPostForm({
            url: '/facilitysettle/transportContract/delete',
            params
        })
    },
}

export default request
