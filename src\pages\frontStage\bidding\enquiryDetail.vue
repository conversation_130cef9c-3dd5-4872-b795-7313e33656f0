<template>
    <div class="root">
        <div class="main">
            <div class="history mb10 center"><span class="router-usher">
            <a @click="$router.push('/mFront/biddingIndex')">招标</a> >
            <a @click="$router.push({path:'/mFront/biddingIndex',query:{tenderForm: 2}})">询价</a>
            >招标详情<!--<div>返回</div>--></span></div>
        </div>
        <div class="topBox center">
            <div class="title">{{ form.tenderName }}</div>
            <DIV class="logImg">
                <img v-if="form.tenderState<3" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState==3" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState==4" src="@/assets/images/img/<EMAIL>" alt="" width="135px">
                <img v-if="form.tenderState==5" src="@/assets/images/img/biddingsignup.png" alt="" width="135px">
                <img v-if="form.tenderState>=6" src="@/assets/images/img/<EMAIL>" alt="" width="135px"></DIV>
            <div class="center infoxBox">
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>招标名称:
                        <span>{{ form.tenderName }}</span></div>
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>招标编号: <span>
                       {{ form.billNo }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>采购类型:
                        <span>{{ ['', '装备', '装备租赁', '周材', '物资', '机材'][form.tenderType] }}</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>采购企业:
                        <span>{{ form.applyOrgName }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>预算金额:
                        <span>{{ form.tenderAdress }}</span>
                    </div>
                    <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>联系人:
                        <span>{{ form.tenderUser }}</span>
                    </div>
                </div>
                <!--                <div class="lineBox">-->
                <!--                    <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png" />询价人限定: <span>{{form.tenderAmount}}</span></div>-->
                <!--                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png" />询价家数: <span>{{form.tenderBail}}</span>-->
                <!--                    </div>-->
                <!--                </div>-->
                <div class="lineBox">
                    <div class="leftDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>联系电话:
                        <span>{{ form.telephone }}</span>
                    </div>
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>发布时间:
                        <span>{{ form.releaseDate }}</span>
                    </div>
                </div>
                <div class="lineBox">
                    <div class="rightDiv"><img class="tit-img" src="../../../assets/images/zao1.png"/>投标截止时间:
                        <span>{{ form.tenderEndTime }}</span>
                    </div>
                </div>
                <!--                <div class="button_bule">收藏</div>-->
            </div>

        </div>

        <!-- =========== -->
        <!--        <div class="detailBox center">-->
        <!--            <div class="tabBox dfa mb20">-->
        <!--                <div @click="Changetabcurrent(0)" :class="[tabCurrent1 == 0 ? 'tabab' : '']">-->
        <!--                    招标详情-->
        <!--                </div>-->
        <!--                <div @click="Changetabcurrent(1)" :class="[tabCurrent1 == 1 ? 'tabab' : '']">-->
        <!--                   投标概况-->
        <!--                </div>-->
        <!--                <div @click="Changetabcurrent(2)" :class="[tabCurrent1 == 2 ? 'tabab' : '']">-->
        <!--                    澄清提问-->
        <!--                </div>-->
        <!--            </div>-->
        <!--           &lt;!&ndash;  招标详情     &ndash;&gt;-->
        <!--          <div v-show="tabCurrent1 == 0">-->
        <!--            <div class="description center p20">-->
        <!--                <el-button class="status" @click="showDetail(1)">招标公告</el-button>-->
        <!--                <el-button class="status"  @click="showDetail(2)">包件信息</el-button>-->
        <!--                <el-button class="status"  @click="showDetail(3)">招标清单</el-button>-->
        <!--            </div>-->
        <!--            &lt;!&ndash; 招标公告&ndash;&gt;-->
        <!--            <div v-show="proclamation">-->
        <!--              <div class="description center p20">-->
        <!--                {{form.tenderNotice}}-->
        <!--              </div>-->
        <!--           </div>-->
        <!--            &lt;!&ndash;//包件信息&ndash;&gt;-->
        <!--            <div  class="" v-show=" balse">-->
        <!--              <div class="lineBox">-->
        <!--                <div class="leftDiv">招标包件信息</div>-->
        <!--              </div>-->
        <!--              <el-table border :data="babalTable" style="width: 1288px;" :header-cell-style="{ background: '#f7f7f7' }">-->
        <!--                <el-table-column type="selection" width="54px">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="index" label="序号" width="80" align="center">-->
        <!--                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="itemNo" label="包件" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="name" label="包件名称" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="priceLimit" label="最高限价" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--              </el-table>-->
        <!--            </div>-->
        <!--            &lt;!&ndash;招标清单&ndash;&gt;-->
        <!--            <div v-show="tenderList" style="padding-top: 20px;">-->
        <!--              <span  style="margin-left: 19px;">{{form.tenderName}}</span>-->
        <!--              <el-table border :data="tenderTable" style="width: 1288px;" :header-cell-style="{ background: '#f7f7f7' }">-->
        <!--                <el-table-column prop="type" label="物资类别" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="name" label="物资名称" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="specs" label="规格型号" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="unit" label="计量单位" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="texture" label="材质" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="num" label="数量" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--              </el-table>-->

        <!--            </div>-->
        <!--          </div>-->
        <!--&lt;!&ndash;     投标概况      &ndash;&gt;-->
        <!--          <div v-show="tabCurrent1 == 1" class="overflow">-->
        <!--            <div v-for="item in TenderPackageVo " :key="item" >-->
        <!--              <div class="lineBox">-->
        <!--                <div class="leftDiv">包件编号: <span>{{item.itemNo}}</span></div>-->
        <!--                <div class="rightDiv">包件名称: <span>{{item.name}}</span></div>-->
        <!--              </div>-->
        <!--              <el-table border :data="item.list" style="width: 1288px;" :header-cell-style="{ background: '#f7f7f7' }">-->
        <!--                <el-table-column prop="subcontractorName" label="投标单位" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="relationUser" label="联系人" width="130" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="relationPhone" label="联系电话" width="160" align="center">-->
        <!--                </el-table-column>-->
        <!--                &lt;!&ndash;                <el-table-column prop="unit" label="相应" width="110" align="center">&ndash;&gt;-->
        <!--                &lt;!&ndash;                </el-table-column>&ndash;&gt;-->
        <!--                <el-table-column  label="标书购买" width="80" align="center">-->
        <!--                  <template slot-scope="scope">-->
        <!--                    <div v-if="scope.row.isPurchase=='1'">11<img class="big-icon" src="../../../assets/images/userCenter/md-check.png" width="20px" height="20px"/>   </div>-->
        <!--                    <div  v-show="scope.row.isPurchase=='0'">审核不通过</div>-->
        <!--                  </template>-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="isDownLoad" label="标书下载" width="80" align="center">-->
        <!--                  <template slot-scope="scope">-->
        <!--                    <div v-show="scope.row.isDownLoad=='1'">11<img class="big-icon" src="../../../assets/images/userCenter/md-check.png" width="20px" height="20px"/>   </div>-->
        <!--                    <div v-show="scope.row.isDownLoad=='0'">未下载</div>-->
        <!--                  </template>-->
        <!--                </el-table-column>-->
        <!--                <el-table-column  label="保证金" width="80" align="center">-->
        <!--                  <template slot-scope="scope">-->
        <!--                    <div v-if="scope.row.isPay=='1'"><img class="big-icon" src="../../../assets/images/userCenter/md-check.png" width="20px" height="20px"/>   </div>-->
        <!--                    <div  v-show="scope.row.isPay=='0'">未缴纳</div>-->
        <!--                  </template>-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="question" label="澄清提问" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="addendum" label="补遗确认" width="110" align="center">-->
        <!--                </el-table-column>-->
        <!--                <el-table-column prop="price" label="报价" width="110" align="center">-->
        <!--                </el-table-column>-->

        <!--              </el-table>-->

        <!--            </div>-->
        <!--         </div>-->
        <!--&lt;!&ndash;          澄清提问&ndash;&gt;-->
        <!--          <div v-show="tabCurrent1 == 2">-->
        <!--            <el-table border :data="TenderCommentList" style="width: 1288px;min-height: 400px;" :header-cell-style="{ background: '#f7f7f7' }">-->
        <!--              <el-table-column prop="index" label="序号" width="80" align="center">-->
        <!--                <template slot-scope="scope">{{ scope.$index + 1 }}</template>-->
        <!--              </el-table-column>-->
        <!--              <el-table-column prop="title" label="标题" align="center">-->
        <!--              </el-table-column>-->
        <!--              <el-table-column prop="createTime" label="提问时间" width="110" align="center">-->
        <!--              </el-table-column>-->
        <!--              <el-table-column prop="accessory" label="附件" width="110" align="center">-->
        <!--              </el-table-column>-->

        <!--            </el-table>-->
        <!--          </div>-->
        <!--        </div>-->
        <tenderBar :tenderInfo="form"></tenderBar>
    </div>
</template>
<script>
// import { getbalseList, getTenderList, getTenderPackageSubcontractorList, getQuesionList, getTenderNotice } from '@/api/frontStage/bidding'
// import { getbalseList, getTenderdtlList, getTenderPackageSubcontractorList, getQuesionList } from '@/api/frontStage/bidding'
export default {
    name: 'biddingDetail',
    components: { tenderBar: () => import ( '@/components/tenderBar.vue') },
    data () {
        return {
            tenderNotice: '', //招标公告
            TenderCommentList: [],
            tenderTable: [],
            babalTable: [],
            TenderPackageVo: [],
            num: 1,
            tabCurrent1: 0,
            form: {},
            proclamation: true,
            balse: false,
            tenderList: false,
            dataListSelections: [], //表格选中的数据
        }
    },
    created () {
        this.form = this.$route.params.row
        // this.balseList()
    },
    // methods: {
    //     Changetabcurrent (i) {
    //         this.tabCurrent1 = i
    //         if (i == 0) {
    //             this.showDetail(1)
    //         }else if (i == 1) {
    //             this.getTenderPackageSubcontractor()
    //         }else  if (i == 2) {
    //             this.quesionList()
    //         }
    //     },
    //     // tenderNoticea () {
    //     //     getTenderNotice({ id: this.form.billId }).then(res=>{
    //     //         console.log(res)
    //     //     })
    //     // },
    //     selectionChangeHandle (val) {
    //         this.dataListSelections = val
    //     },
    //     handleCurrentInventoryClick (row) {
    //         row.flag = !row.flag
    //         this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
    //     },
    //     showDetail (i) {
    //         if (i == 1) {
    //             this.proclamation = true
    //             this.balse = false
    //             this.tenderList = false
    //         }else  if (i == 2) {
    //             this.proclamation = false
    //             this.balse = true
    //             this.tenderList = false
    //         }else  if (i == 3) {
    //             this.proclamation = false
    //             this.balse = false
    //             this.tenderList = true
    //             this.getTenderdtlLists()
    //         }
    //     },
    //     balseList () {
    //         getbalseList({ id: this.form.billId }).then(res=>{
    //             this.babalTable = res
    //         })
    //     },
    //     getTenderdtlLists () {
    //         getTenderdtlList({ id: this.form.billId }).then(res=>{
    //             this.tenderTable = res
    //         })
    //     },
    //     getTenderPackageSubcontractor () {
    //         getTenderPackageSubcontractorList({ id: this.form.billId }).then(res=>{
    //             this.TenderPackageVo = res
    //         })
    //     },
    //     quesionList () {
    //         getQuesionList({ id: this.form.billId }).then(res=>{
    //             this.TenderCommentList = res
    //         })
    //     }
    // }
}
</script>
<style scoped lang="scss">
.root {
    background-color: #f5f5f5;
    padding-bottom: 50px;
}

span {
    line-height: 1;
}

.router-usher {
    cursor: pointer;

    a:hover {
        color: rgba(34, 111, 199, 1);
    }
}

.row {
    margin-bottom: 20px;
}

.main {
    width: 1326px;
    margin: 0 auto;
    margin-top: 10px;

    .history {
        width: 100%;
        height: 40px;
        margin-top: 10px;
        padding-left: 20px;
        font-size: 12px;
        line-height: 40px;
        background-color: #fff;
        position: relative;

        div {
            width: 64px;
            height: 28px;
            text-align: center;
            line-height: 28px;
            border: 1px solid rgba(230, 230, 230, 1);
            color: rgba(153, 153, 153, 1);
            position: absolute;
            top: 6px;
            right: 10px;
            cursor: pointer;
            user-select: none;
        }
    }
}

.overflow {
    height: 500px;
    overflow: hidden;
    overflow-y: scroll;
}

.status {
    width: 142px;
    height: 40px;
    opacity: 1;
    border-radius: 1px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    line-height: 40px;
    margin-top: 15px;
}

.topBox {
    width: 1326px;
    padding: 20px 21px 20px 19px;
    background-color: #fff;
    padding-bottom: 30px;
    position: relative;
}

.title {
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    padding-top: 20px;
    padding-bottom: 54px;
    border-bottom: 1px dashed rgba(229, 229, 229, 1);;
}

.el-table {
    margin: auto;
}

.infoBox {

    padding-top: 32px;

}

.lineBox {

    height: 30px;
    padding-left: 205px;
    margin-top: 21px;
    font-size: 14px;
    overflow: hidden;
    display: flex;

    span {
        margin-left: 10px;
    }

    .leftDiv, .rightDiv {
        width: 50%;
        display: flex;
        align-items: center;

        .tit-img {
            width: 20px;
            height: 20px;
            margin-right: 11px;
        }
    }

}

.button_bule {
    width: 142px;
    height: 40px;
    opacity: 1;
    background: #226FC7;
    font-size: 16px;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    line-height: 40px;
    margin-top: 15px;
    position: relative;
    left: 40%;
}

.logImg {
    z-index: 9999;
    width: 136px;
    height: 136px;
    border: 1px dashed rgba(229, 229, 229, 1);
    position: absolute;
    right: 110px;
    top: 0;
}

.detailBox {
    width: 1326px;
    height: 605px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(229, 229, 229, 1);
    margin-top: 60px;

    .tabBox {
        width: 1324px;
        height: 60px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        background: rgba(250, 250, 250, 1);

        div {
            width: 160px;
            height: 60px;
            text-align: center;
            line-height: 60px;
            font-size: 18px;
            cursor: pointer;
        }

        .tabab {
            background: #fff;
            border-top: solid 2px rgba(34, 111, 199, 1);
        }
    }

    .description {
        width: 1286px;
        height: 108px;
        border: 1px solid rgba(230, 230, 230, 1);
    }

    .row {
        padding-left: 27px;
        padding-right: 240px;

        .item {
            span {
                font-size: 16px;
                font-weight: 400;
                color: rgba(102, 102, 102, 1);
            }

            div {
                font-size: 16px;
                font-weight: 400;
                color: rgba(0, 0, 0, 1);
            }
        }
    }

    .han {
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 1);
        padding-left: 30px;
        margin-top: 30px;
    }

    .descriptionsBox {
        padding: 20px;
    }
}
</style>
