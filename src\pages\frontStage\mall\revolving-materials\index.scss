.root {
  height: 100%;
  padding: 20px 0;
  background-color: #f5f5f5;

  .box {
    width: 1326px;
    background-color: #fff;
  }
}
/deep/ .el-checkbox {
  color: rgba(51, 51, 51, 1);
  .el-checkbox__inner {
    border: 1px solid rgba(204, 204, 204, 1);
  }
}
.addr-bar {
  height: 30px;
  & > div:first-child {
    font-size: 20px;
    font-weight: 500;
    color: rgba(212, 48, 48, 1);
  }
  & > div:last-child {
    color: rgba(51, 51, 51, 1);
  }
  /deep/ .el-input__inner {
    width: 216px;
    height: 26px;
  }
}

.title {
  height: 52px;
  margin-bottom: 12px;
  padding-left: 20px;
  color: rgba(51, 51, 51, 1);
  background-color: rgba(250, 250, 250, 1);
  /deep/ .el-checkbox {
    width: 210px;
  }
  & > div:nth-child(2) {
    width: 270px;
  }
  & > div:nth-child(3) {
    width: 140px;
  }
  & > div:nth-child(4) {
    width: 129px;
  }
  & > div:nth-child(5) {
    width: 129px;
  }
  & > div:nth-child(6) {
    width: 158px;
  }
  & > div:nth-child(7) {
    width: 158px;
  }
  & > div:nth-child(8) {
    width: 158px;
  }
}
.product {
  min-height: 600px;
  margin-bottom: 30px;
  .shop-name {
    padding: 22px 0 14px 20px;
    img {
      width: 22px;
      height: 22px;
      margin-left: 3px;
    }
  }
  & .product-item:not(:last-of-type) {
    margin-bottom: 10px;
  }
  .product-item {
    position: relative;
    border: 1px solid rgba(230, 230, 230, 1);
    padding-top: 40px; /* 为右上角配送区域腾出空间 */
    /* 配送区域选择器，放在右上角 */
    .delivery-area-row {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1;
      .delivery-area-selector {
        display: flex;
        align-items: center;
        .delivery-area-label {
          font-size: 14px;
          color: #606266;
          margin-right: 5px;
        }
        /deep/ .el-select {
          width: 140px;
        }
      }
    }
    /* 商品信息行样式 */
    .product-info-row {
      display: flex;
      height: 120px;
      img {
        width: 100px;
        height: 100px;
      }
      .checkbox {
        padding: 0 30px 0 20px;
      }
      .title-box {
        width: 330px;
        padding-left: 12px;
        .tag {
          width: 60px;
          height: 20px;
          margin: 0 10px 7px 0;
          font-size: 12px;
          line-height: 20px;
          text-align: center;
          color: #fff;
          background-color: rgba(255, 195, 0, 1);
        }
      }
      .price {
        width: 157px;
        text-align: center;
      }
      .operate {
        width: 158px;
        font-size: 10px;
        color: rgba(102, 102, 102, 1);
        margin-left: 80px;
        div {
          margin-bottom: 10px;
          cursor: pointer;
        }
      }
    }
  }
}
.bottom-bar {
  height: 60px;
  padding-left: 20px;
  border: 1px solid rgba(230, 230, 230, 1);
  & > div {
    height: 100%;
  }
  /deep/ .el-checkbox {
    margin-right: 30px;
  }
  .bar-left {
    color: rgba(102, 102, 102, 1);
    span {
      margin-right: 20px;
      cursor: pointer;
    }
  }
  .bar-right {
    & > div:first-child {
      margin: 7px 9px 0 0;
      color: rgba(153, 153, 153, 1);
      cursor: pointer;
      span {
        margin-right: 9px;
      }
    }
    .bar-right-price {
      height: 27px;
      margin: 4px 22px 0 0;
      & span:first-child {
        color: rgba(153, 153, 153, 1);
      }
      & span:nth-child(2) {
        margin-right: 12px;
        font-size: 18px;
        font-weight: 700;
        color: rgba(212, 48, 48, 1);
      }
      img {
        width: 16px;
        height: 16px;
      }
    }
    button {
      padding: 0 14px;
      height: 60px;
      font-size: 24px;
      font-weight: 400;
      text-align: center;
      line-height: 60px;
      color: rgba(255, 255, 255, 1);
      background-color: rgba(212, 48, 48, 1);
    }
  }
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  appearance: none !important;
}
::v-deep input[type='number'] {
  -moz-appearance: textfield !important;
  appearance: textfield !important;
}
//该商品选择框
.checkbox {
  position: relative;
  top: -70px;
  left: 20px;
}
//商品图片
.goodsImg {
  cursor: pointer;
  border: 1px solid red;
  width: 130px;
  height: 130px;
  margin-left: 50px;
  margin-right: 20px;
}
//商品详情
.title-box {
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  margin-right: 30px;
  width: 216px;
}
//该商品单价
.price-container {
  margin-left: 10px;
}
.price-value {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}
.price-label {
  margin-bottom: 10px;
  font-weight: 200;
}
//该商品税率
.price {
  margin-left: -50px;
  margin-right: 40px;
  width: 80px;
  text-align: center;
  margin-top: -80px;
}
//该商品账期
.payment-period-select {
  font-weight: 600;
  width: 80px;
  text-align: center;
  margin-right: 40px;
  margin-top: -80px;
}
//该商品数量
.num-box {
  width: 100px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  border: 1px solid black;
  margin-right: 40px;
  margin-top: -80px;
}
.num-box-btn {
  cursor: pointer;
  width: 34px;
  height: 34px;
  background-color: rgb(229, 229, 227);
  border-bottom: 1px solid black;
}
.num-box-input {
  text-align: center;
  width: 60px;
  height: 34px;
  border-left: 1px solid black;
  border-right: 1px solid black;
  border-top: none;
  border-bottom: 1px solid black;
}
//该商品小计
.price-group {
  margin-top: -60px;
  margin-left: 20px;
  margin-right: 100px;
}
//该商品操作
.operate {
  margin-top: -60px;
}
.operate-btn {
  font-weight: 546;
  margin-bottom: 20px;
  cursor: pointer;
}
.operate-btn:hover {
  color: red;
}
.allPrice {
  //border: 3px solid red;
  width: 800px;
  display: flex;
  justify-content: space-between;
}
.allPrice span {
  color: red;
}
.allPriceBtn1 {
  background-color: rgb(255, 243, 238);
  border: 1px solid #fbd4cd;
  color: rgb(255, 50, 17);
  font-size: 25px;
  width: 200px;
  height: 60px;
  margin-left: 20px;
}
.allPriceBtn2 {
  background-color: rgb(255, 50, 17);
  color: white;
  font-size: 25px;
  width: 220px;
  height: 60px;
  margin-left: 20px;
}
.product-name {
  max-width: 216px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sku-name {
  margin-top: 4px;
  max-width: 215px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.custom-d {
  /deep/ .el-dialog {
    padding: 0;

    .el-dialog__body {
      height: unset;
      margin-top: 0;
    }

    .el-dialog__header {
      padding: 20px 20px 10px;
      margin-bottom: unset;
      text-align: unset;
      font-weight: unset;

      .el-dialog__title {
        color: #303133;
      }
    }
  }
}