import service from '@/utils/request'

const { httpPost, httpGet } = service
const getSupplierList = params => {
    return httpPost({
        url: '/materialMall/shopManage/invoice/listByEntity',
        params
    })
}
const getInvoiceList = params => {
    return httpPost({
        url: '/materialMall/platform/invoice/listByEntity',
        params
    })
}
const del = params => {
    return httpGet({
        url: '/materialMall/invoice/delete',
        params
    })
}
const changInvoiceState = params => {
    return httpPost({
        url: '/materialMall/shopManage/invoice/updateInvoiceState',
        params
    })
}
const saveFiles = params => {
    return httpPost({
        url: '/materialMall/shopManage/invoice/saveFiles',
        params
    })
}
const updateBathIds = params => {
    return httpPost({
        url: '/materialMall/shopManage/invoice/updateBathIds',
        params
    })
}
const updateAllState = params => {
    return httpPost({
        url: '/materialMall/shopManage/invoice/updateAllState',
        params
    })
}
const changStateBath = params => {
    return httpPost({
        url: '/materialMall/shopManage/invoice/changStateBath',
        params
    })
}

const findById = params => {
    return httpGet({
        url: '/materialMall/invoice/findById',
        params
    })
}

// 批量删除
const batchDelete = params => {
    return httpPost({
        url: '/materialMall/invoice/deleteBatch',
        params
    })
}

export {
    findById,
    getSupplierList,
    changInvoiceState,
    saveFiles,
    updateBathIds,
    batchDelete,
    updateAllState,
    changStateBath,
    getInvoiceList,
    del
}