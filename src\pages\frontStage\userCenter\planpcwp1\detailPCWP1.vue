<template>
    <main>
        <div v-loading="contractDetailLoading">
            <div class="list-title dfa mb20">计划信息</div>
            <div class="detailBox">
                <div class="row">
                    <div class="col">
                        <div class="name">
                            计划编号：
                        </div>
                        {{ resultDetail.BillNo }}
                    </div>
                    <div class="col">
                        <div class="name">
                            计划日期：
                        </div>
                        {{ resultDetail.BillDate | dateStr }}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            计划金额：
                        </div>
                        {{ resultDetail.PlanAmount }}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="name">
                            备注：
                        </div>
                        {{ resultDetail.Remark }}
                    </div>
                </div>
            </div>
            <div class="list-title dfa mb20">计划明细<el-button v-show="true" type="primary" style="margin-left: 800px" @click="submitOrderByPlan">生成订单</el-button></div>
            <el-table
                ref="msgTable"
                :data="resultDetail.Details"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="MatterName" label="物资名称" width="200"/>
                <el-table-column prop="Spec" label="规格型号" width=""/>
                <el-table-column prop="ClassName" label="类别名称" width=""/>
                <el-table-column prop="Price" label="单价" width=""/>
                <el-table-column prop="Number" label="数量" width=""/>
                <el-table-column prop="Amount" label="总金额" width=""/>
                <el-table-column prop="ConsumeAmount" label="已下单金额" width=""/>
                <el-table-column prop="ConsumeNumber" label="已下单数量" width=""/>
                <el-table-column label="操作" width="">
                    <template slot-scope="scope">
                        <div class="pointer" v-show="scope.row.Number != scope.row.ConsumeNumber" style="color: rgba(33, 110, 198, 1);" @click="submitOrder(scope.row)">去下单</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

    </main>
</template>

<script>

import { mapState } from 'vuex'
import { getPCWP1Interface } from '@/api/plan/plan'

export default {
    filters: {
        dateStr (dateStr) {
            if(dateStr == null) {
                return
            }
            return dateStr.split('T')[0]
        }
    },
    name: 'detail',
    data () {
        return {
            contractDetailLoading: false,
            resultDetail: {}
        }
    },
    created () {
        this.getPlantDetailM()
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        submitOrderByPlan () {
            this.$router.push({
                path: '/user/submitOrderByPlan',
                query: { billId: this.$route.query.billId }
            })
        },
        getPlantDetailM () {
            let params = {
                'jsonrpc': '2.0',
                'method': 'Material.SporadicPlan.GetById',
                'params': {
                    'Id': this.$route.query.billId /*单据ID [String]*/
                },
                'id': 1,
                'tags': {
                    'userid': this.userInfo.farUserId,
                    'username': this.userInfo.originalUserName,
                    'orgid': this.userInfo.orgId,
                    'orgname': this.userInfo.orgName,
                    'companycode': '1000',
                    'auth_client_id': 'test',
                    'auth_token': 'test',
                    'platformid': '1'
                }
            }
            this.contractDetailLoading = true
            getPCWP1Interface(params).then(res => {
                this.contractDetailLoading = false
                this.resultDetail = res.result
            }).catch(() => {
                this.contractDetailLoading = false
            })
        },
        // 去下单
        // eslint-disable-next-line no-unused-vars
        submitOrder (row) {
            this.$router.push({
                path: '/user/submitOrderByPlan',
                query: { billId: this.$route.query.billId, DtlId: row.DtlId }
            })
        }
    }
}
</script>

<style scoped lang="scss">
main {
    min-height: 894px;
    padding: 0 20px;
    border: 1px solid rgba(229, 229, 229, 1);
}

.list-title {
    padding: 0;
}

.reviewList {
    min-height: 400px;
}

.reviewItem {
    &:not(:last-of-type) {
        margin-bottom: 24px;
        padding-bottom: 20px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
    }

}

.avatar {
    width: 44px;
    height: 44px;
    margin-right: 10px;
    border-radius: 50%;
}

.reviewItem {
    &:not(:last-of-type) {
        margin-bottom: 24px;
        padding-bottom: 20px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
    }

    .reviewTop {

        .user {
            div:first-child {
                font-size: 18px;
                font-weight: 500;
            }

            div:last-child {
                color: rgba(102, 102, 102, 1);
            }
        }
    }

    p {
        font-size: 16px;
        color: rgba(51, 51, 51, 1);
    }

    .imgs {
        margin-top: 10px;

        img {
            width: 120px;
            height: 120px;
            margin-right: 10px;
            object-fit: cover;
        }
    }
}

.historyItem {
    height: 108px;
    padding-right: 26px;

    &:not(:last-of-type) {
        border-bottom: 1px solid rgba(229, 229, 229, 1);
    }

    .product, .numbers, .finishTime {
        text-align: center;
        margin-left: 200px;
    }
}

.detailBox {
    margin: 70px;

    .row {
        margin-bottom: 22px;
        color: rgba(51, 51, 51, 1);

        &, .col {
            display: flex;
        }

        .col {
            width: 50%;
        }

        .name {
            width: 90px;
            text-align: right;

            span {
                color: rgba(255, 95, 95, 1);
            }
        }
    }
}
</style>