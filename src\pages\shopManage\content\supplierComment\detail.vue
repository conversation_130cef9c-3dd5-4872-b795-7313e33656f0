<template>    <!-- 二级供应商评价 -->
  <div class="e-form">
    <BillTop @cancel="handleClose"></BillTop>
    <div v-loading='formLoading' class="tabs warningTabs" style="padding-top: 70px;">
      <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
        <el-tab-pane label="基本信息" name="baseInfo" :disabled="clickTabFlag">
        </el-tab-pane>
        <el-tab-pane label="自营店评价评分" name="commentInfo" :disabled="clickTabFlag">
        </el-tab-pane>
        <div id="tabs-content">
          <div style="width: 100%" class="form">
            <el-form
              :model="addForm.formData" :rules="formRules" label-width="200px" ref="formComment" class="demo-ruleForm"
            >
              <div id="baseInfCon" class="con">
                <div class="tabs-title" id="baseInfo">基本信息</div>
                <div>
                  <el-row>
                    <el-col :span="10">
                      <el-form-item label="汇总日期：">
                        <el-input type="input" :rows="4" disabled v-model="rowData.sumDate" show-word-limit></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="10">
                      <el-form-item label="评价日期：">
                        <el-input type="input" :rows="4" disabled v-model="rowData.commentDate"  show-word-limit></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="10">
                      <el-form-item label="商品品质：">
                        <el-rate v-model="rowData.commentLevel" disabled :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
<!--                        <span>{{ addForm.formData.commentLevel }}分</span>-->
                      </el-form-item>
                    </el-col>
                    <el-col :span="10">
                      <el-form-item label="保供能力：">
                        <el-rate v-model="rowData.commentSupply" disabled :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="10">
                      <el-form-item label="诚信履约：">
                        <el-rate v-model="rowData.commentIntegrity" disabled :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
                      </el-form-item>
                    </el-col>
                    <el-col :span="10">
                      <el-form-item label="服务水平：">
                        <el-rate v-model="rowData.commentService" disabled :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="备注：">
                        <el-input type="textarea" :rows="4" disabled v-model="rowData.remarks" maxlength="200" show-word-limit></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <div id="commentInfoCon" class="con">
                <div class="tabs-title" id="commentInfo">自营店评价评分</div>
                <div>
                  <el-row>
                    <el-col :span="10">
                      <el-form-item label="商品品质：" prop="commentLevel">
                        <el-rate v-model="addForm.formData.commentLevel" :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
                        <!--                        <span>{{ addForm.formData.commentLevel }}分</span>-->
                      </el-form-item>
                    </el-col>
                    <el-col :span="10">
                      <el-form-item label="保供能力：" prop="commentSupply">
                        <el-rate v-model="addForm.formData.commentSupply" :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="10">
                      <el-form-item label="诚信履约：" prop="commentIntegrity">
                        <el-rate v-model="addForm.formData.commentIntegrity" :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
                      </el-form-item>
                    </el-col>
                    <el-col :span="10">
                      <el-form-item label="服务水平：" prop="commentService">
                        <el-rate v-model="addForm.formData.commentService" :colors="['#FF9900','#FF9900','#FF9900']"></el-rate>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="备注：">
                        <el-input type="textarea" :rows="4" v-model="addForm.formData.remarks" placeholder="请输入备注" maxlength="200" show-word-limit></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-form>
          </div>
        </div>
      </el-tabs>
    </div>
    <div class="buttons">
      <el-button type="success" @click="submit">保存</el-button>
      <el-button @click="handleClose">返回</el-button>
    </div>
  </div>
</template>

<script>
import { throttle } from '@/utils/common'
import { updateShopComment } from '@/api/shopManage/content/supplierComment'
export default {
    data () {
        return {
            // 数据加载
            formLoading: false,
            formRules: {
                commentLevel: [
                    { required: true, message: '商品品质', trigger: 'blur' },
                ],
                commentSupply: [
                    { required: true, message: '保供能力', trigger: 'blur' },
                ],
                commentIntegrity: [
                    { required: true, message: '诚信履约', trigger: 'blur' },
                ],
                commentService: [
                    { required: true, message: '服务水平', trigger: 'blur' },
                ],
            },
            addForm: {
                formData: {
                    commentService: null,
                    commentIntegrity: null,
                    commentSupply: null,
                    commentLevel: null,
                    shopId: null,
                    shopName: null,
                    mainBusiness: null,
                    commentStart: null,
                    commentEnd: null,
                    remarks: null,
                }
            },
            rowData: {
                commentService: null,
                commentIntegrity: null,
                commentSupply: null,
                commentLevel: null,
                sumDate: null,
                commentDate: null,
                shopId: null,
                shopName: null,
                mainBusiness: null,
                remarks: null,
            }, // 跳转过来的数据
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    components: {},
    created () {
        this.rowData = this.$route.params.row
    },
    mounted () {
    // 获取数据
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'commentInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        submit () {
            this.$refs.formComment.validate(async valid => {
                if (valid) {
                    this.showLoading = true
                    this.addForm.formData.shopId = this.rowData.shopId
                    this.addForm.formData.shopName = this.rowData.shopName
                    this.addForm.formData.mainBusiness = this.rowData.mainBusiness
                    this.addForm.formData.commentStart = this.rowData.sumDate ? this.rowData.sumDate.split('至')[0] + ' 00:00:00' : null
                    this.addForm.formData.commentEnd = this.rowData.sumDate ? this.rowData.sumDate.split('至')[1] + ' 23:59:59' : null
                    updateShopComment(this.addForm.formData).then(res => {
                        if(res.code == 200) {
                            this.$message.success('修改成功')
                            this.addForm.formData = {
                                remarks: null,
                                commentService: null,
                                commentIntegrity: null,
                                commentSupply: null,
                                commentLevel: null,
                            }
                            this.rowData = {
                                remarks: null,
                                commentService: null,
                                commentIntegrity: null,
                                commentSupply: null,
                                commentLevel: null,
                                sumDate: null,
                                commentDate: null,
                                shopId: null,
                            }, // 跳转过来的数据
                            this.$router.go(-1)
                        }
                        this.showLoading = false
                    }).catch(() => {
                        this.showLoading = false
                    })
                }
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
            document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 重置数据
        resetFormData () {
            this.addForm.formData = {
                remarks: null,
                commentService: null,
                commentIntegrity: null,
                commentSupply: null,
                commentLevel: null,
                shopId: null,
                shopName: null,
                mainBusiness: null,
            }
        }
    }
}
</script>

<style lang='scss' scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
  -moz-appearance: textfield !important;
}

.e-table {
  min-height: auto;
  background: #fff;
}

.upload {
  margin: 20px auto;
  display: flex;
  justify-content: center;
  text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-tabs__content {
  // overflow: hidden;
  &::-webkit-scrollbar {
    width: 0;
  }
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
  display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
  display: none;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 600px;
    margin-top: 0px;
  }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}
</style>