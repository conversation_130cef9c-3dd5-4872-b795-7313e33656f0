import service from '@/utils/request'
// eslint-disable-next-line no-unused-vars
const { httpPostForm, httpPost, httpGet } = service

//装备管理 - 装备交接 接口
const request = {

    //===============================================================================================采购验收接口

    //高级查询采购验收
    purchaseAcceptanceList (params) {
        return httpPost({
            url: '/facilityconnect/list/advanced/receive/buy',
            params
        })
    },
    //添加采购验收
    purchaseAcceptanceAdd (params) {
        return httpPost({
            url: '/facilityconnect/add/buy/receive',
            params
        })
    },
    //删除采购验收
    purchaseAcceptanceDelete (params) {
        return httpPostForm({
            url: '/facilityconnect/delete/buy/receive',
            params
        })
    },
    //修改采购验收
    purchaseAcceptanceUpdate (params) {
        return httpPost({
            url: '/facilityconnect/update/buy/receive',
            params
        })
    },
    //获取采购验收基础信息
    purchaseAcceptanceBaseInfo (params) {
        return httpPost({
            url: '/facilityconnect/get/receive/buy',
            params
        })
    },
    //获取采购验收明细
    purchaseAcceptancePlanInfo (params) {
        return httpPost({
            url: '/facilityconnect/list/buy/receive/dtl',
            params
        })
    },
    //采购验收明细新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updatePurchaseAcceptanceDel (params) {
        return httpPost({
            url: '/facilityconnect/update/buy/receive/del',
            params
        })
    },
    //获取采购验收技术参数
    getTechnical (billid) {
        return httpPostForm({
            url: '/facilityconnect/list/technical/specification',
            params: {
                id: billid,
            }
        })
    },
    //采购验收技术参数新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateTechnical (params) {
        return httpPost({
            url: '/facilityconnect/update/buy/receive/del/technical',
            params
        })
    },

    //===============================================================================================装备调动接口

    //高级查询装备调动
    equipMoveList (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/advanced/query',
            params
        })
    },
    //装备调动新增
    equipMoveAdd (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/add',
            params
        })
    },
    //装备调动删除
    equipMoveDelete (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/delete',
            params: params
        })
    },
    //装备调动基础信息修改
    equipMoveUpdate (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/update',
            params
        })
    },
    //获取装备调动基础信息
    equipMoveBaseInfo (params) {
        return httpGet({
            url: '/facilityconnect/equipment/transfer/get',
            params,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //获取初始化装备调动基础信息
    equipMoveInitBaseInfo (params) {
        return httpGet({
            url: '/facilityconnect/equipment/transfer/init',
            params,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //获取装备调动明细
    equipMovePlanInfo (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/dtl/list',
            params
        })
    },
    //获取随机工具及技术资料
    equipMovePlanTechnical (params) {
        return httpGet({
            url: '/facilityconnect/equipment/transfer/get/transfer/tools',
            params,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //获取变更历史
    getMoveChangeHistory (params) {
        return httpPostForm({
            url: '/facilityconnect/equipment/transfer/list/transfer/change/history',
            params,
        })
    },
    //装备调动明细新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateEquipMoveDtl (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/dtl/update/transfer/dtl',
            params
        })
    },
    //随机工具及技术资料新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateEquipToolsDtl (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/dtl/update/transfer/dtl/tools',
            params
        })
    },
    //装备调动变更删除
    equipMoveDeleteChange (params) {
        return httpGet({
            url: '/facilityconnect/equipment/transfer/change/delete',
            params: {
                id: params
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },

    //装备调动变更新增基本信息
    equipMoveChangeAdd (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/add/change/transfer',
            params
        })
    },
    //获取装备调动变更基础信息
    getEquipMoveBaseInfoChange (params) {
        return httpPostForm({
            url: '/facilityconnect/equipment/transfer/get/change/transfer',
            params: {
                id: params
            }
        })
    },
    //获取装备调动明细变更
    getEquipMovePlanInfoChange (params) {
        return httpPostForm({
            url: '/facilityconnect/equipment/transfer/list',
            params: {
                id: params
            }
        })
    },
    //获取随机工具及技术资料
    getEquipMoveTechnicalChange (params) {
        return httpPostForm({
            url: '/facilityconnect/equipment/transfer/get/transfer/dtl/tools',
            params: {
                id: params
            }
        })
    },
    //装备调动变更修改基本信息
    equipMoveChangeUpdate (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/update/change/transfer',
            params
        })
    },
    //装备调动明细变更新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateEquipMoveDtlChange (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/update/change/transfer/dtl',
            params
        })
    },
    //随机工具及技术资料变更新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateEquipToolsDtlChange (params) {
        return httpPost({
            url: '/facilityconnect/equipment/transfer/update/change/transfer/dtl/tools',
            params
        })
    },

    //===============================================================================================临租转长租接口

    //高级查询临租转长租
    longLeaseList (params) {
        return httpPost({
            url: '/facilityconnect/handover/change/lease/advanced/query',
            params
        })
    },
    //临租转长租新增
    longLeaseAdd (params) {
        return httpPost({
            url: '/facilityconnect/handover/change/lease/add',
            params
        })
    },
    //临租转长租删除
    longLeaseDelete (params) {
        return httpPostForm({
            url: '/facilityconnect/handover/change/lease/delete',
            params
        })
    },
    //临租转长租修改
    longLeaseUpdate (params) {
        return httpPost({
            url: '/facilityconnect/handover/change/lease/update',
            params
        })
    },
    //临租转长租提交
    longLeaseCommit (params) {
        return httpPostForm({
            url: '/facilityconnect/handover/change/lease/commit',
            params
        })
    },
    //作废临租转长租
    longLeaseNullify (params) {
        return httpPostForm({
            url: '/facilityconnect/handover/change/lease/cancel',
            params
        })
    },
    //临租转长租基础信息
    longLeaseBaseInfo (params) {
        return httpGet({
            url: '/facilityconnect/handover/change/lease/get',
            params
        })
    },
    //获取临租转长租明细
    longLeasePlanInfo (params) {
        return httpPost({
            url: '/facilityconnect/handover/change/lease/dtl/list',
            params
        })
    },
    //临租转长租明细新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updatelongLeaseDtl (params) {
        return httpPost({
            url: '/facilityconnect/handover/change/lease/dtl/update',
            params
        })
    },

    //===============================================================================================租赁退场租接口

    //高级查询租赁退场
    leaseExitList (params) {
        return httpPost({
            url: '/facilityconnect/lease/exit/advanced/query',
            params
        })
    },
    //租赁退场新增
    leaseExitAdd (params) {
        return httpPost({
            url: '/facilityconnect/lease/exit/add',
            params
        })
    },
    //租赁退场删除
    leaseExitDelete (params) {
        return httpPostForm({
            url: '/facilityconnect/lease/exit/delete',
            params
        })
    },
    //租赁退场修改
    leaseExitUpdate (params) {
        return httpPost({
            url: '/facilityconnect/lease/exit/update',
            params
        })
    },
    //租赁退场提交
    leaseExitCommit (params) {
        return httpPostForm({
            url: '/facilityconnect/lease/exit/commit',
            params
        })
    },
    //作废租赁退场
    leaseExitNullify (params) {
        return httpPostForm({
            url: '/facilityconnect/lease/exit/cancel',
            params
        })
    },
    //租赁退场基础信息
    leaseExitBaseInfo (params) {
        return httpGet({
            url: '/facilityconnect/lease/exit/get',
            params
        })
    },
    //获取租赁退场明细
    leaseExitPlanInfo (params) {
        return httpPost({
            url: '/facilityconnect/lease/exit/dtl/list',
            params
        })
    },
    //租赁退场明细新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateLeaseExitDtl (params) {
        return httpPost({
            url: '/facilityconnect/lease/exit/dtl/update',
            params
        })
    },

    //===============================================================================================租赁验收接口

    //高级查询租赁验收
    leaseAcceptanceList (params) {
        return httpPost({
            url: '/facilityconnect/lease/receive/list/advanced',
            params
        })
    },
    //添加租赁验收
    leaseAcceptanceAdd (params) {
        return httpPost({
            url: '/facilityconnect/lease/receive/add/receive',
            params
        })
    },
    //提交租赁验收
    leaseAcceptanceCommit (params) {
        return httpPostForm({
            url: '/facilityconnect/lease/receive/commit/receive',
            params
        })
    },
    //删除租赁验收
    leaseAcceptanceDelete (params) {
        return httpPostForm({
            url: '/facilityconnect/lease/receive/delete',
            params
        })
    },
    //作废租赁验收
    leaseAcceptanceNullify (params) {
        return httpPostForm({
            url: '/facilityconnect/lease/receive/cancel',
            params
        })
    },
    //修改租赁验收
    leaseAcceptanceUpdate (params) {
        return httpPost({
            url: '/facilityconnect/lease/receive/update',
            params
        })
    },
    //获取租赁验收基础信息
    leaseAcceptanceBaseInfo (params) {
        return httpPostForm({
            url: '/facilityconnect/lease/receive/get',
            params
        })
    },
    //获取租赁验收明细
    leaseAcceptancePlanInfo (params) {
        return httpPost({
            url: '/facilityconnect/lease/receive/dtl/list/buy/receive/dtl',
            params
        })
    },
    //租赁验证明细新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateLeaseAcceptanceDtl (params) {
        return httpPost({
            url: '/facilityconnect/lease/receive/dtl/update/dtl',
            params
        })
    },
    //获取租赁验收技术参数
    getLeaseTechnical (billid) {
        return httpGet({
            url: '/facilityconnect/lease/receive/dtl/get/technical',
            params: {
                id: billid,
            },
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
    },
    //租赁验收技术参数新增  编辑  删除 操作状态(未变更0 新增 1 编辑 2 删除 -1)
    updateLeaseTechnical (params) {
        return httpPost({
            url: '/facilityconnect/lease/receive/dtl/update/technical',
            params
        })
    },
}

export default request
