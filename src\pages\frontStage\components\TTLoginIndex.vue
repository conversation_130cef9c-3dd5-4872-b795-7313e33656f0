<template>
    <main>
        <div class="text">请稍后...</div>
    </main>
</template>
<script>
import { Loading } from 'element-ui'
import { ttLogin } from '@/api/w/tokenLogin'

export default {
    data () {
        return {
            a: null,
            p: null,
        }
    },
    created () {
        this.a = this.$route.query.a
        this.p = this.$route.query.p
        this.ttLoginM()
    },
    methods: {
        ttLoginM () {
            let loading = Loading.service({
                lock: true,
                background: 'rgba(0, 0, 0, 0.4)'
            })
            let param = {
                a: this.a,
                p: this.p,
                mallType: 0,
            }
            if(this.a == null || this.p == null) {
                return
            }
            ttLogin(param).then(res => {
                localStorage.setItem('token', res.token)
                localStorage.setItem('lastTime', new Date().getTime())
                this.$store.commit('setUserInfo', {
                    ...res
                })
                loading.close()
                this.$router.push('/index')
            }).catch(() => {
                loading.close()
            })
        }
    },
}
</script>
<style scoped>
main {
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}
.text {
    font-size: 22px;
    color: #333;
}
</style>